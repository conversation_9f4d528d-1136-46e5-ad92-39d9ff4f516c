# Personal Services - 个人服务集群

这是一个基于微服务架构的个人服务集群，主要用于收藏和下载网络资源（如B站视频）。系统采用Go语言开发，使用Thrift RPC框架进行服务间通信，通过RabbitMQ实现异步任务处理。

## 系统架构

### 服务组件

1. **Gateway (网关服务)**
   - 端口: 16135
   - 功能: HTTP API网关，处理外部请求并转发到后端服务
   - 技术栈: Go + Gin + Thrift Client

2. **Favorites Service (收藏服务)**
   - 端口: 18081 (Thrift RPC)
   - 功能: 处理收藏业务逻辑，管理收藏记录，发送下载任务
   - 技术栈: Go + Thrift Server + GORM + RabbitMQ

3. **Task Service (任务管理服务)**
   - 端口: 18082 (Thrift RPC)
   - 功能: 处理任务管理业务逻辑，支持层级任务、时间线、标记等
   - 技术栈: Go + Thrift Server + GORM

4. **Notes Service (笔记管理服务)**
   - 端口: 18083 (Thrift RPC)
   - 功能: 处理笔记管理业务逻辑，支持笔记CRUD、分类、标签、搜索等
   - 技术栈: Go + Thrift Server + GORM

5. **Bilibili Crawler (B站爬虫服务)**
   - 功能: 消费MQ消息，执行实际的视频下载任务
   - 技术栈: Python + Bilix + RabbitMQ

6. **Thrift Common (公共库)**
   - 功能: 提供Thrift接口定义、客户端和服务端代码
   - 包含: IDL定义、生成的Go代码、客户端/服务端封装

### 架构图

```
┌─────────────────┐    HTTP     ┌─────────────────┐
│                 │   Request   │                 │
│   Client/Web    │ ──────────► │    Gateway      │
│                 │             │   (Port 16135)  │
└─────────────────┘             └─────────────────┘
                                          │
                                          │ Thrift RPC
                                          ▼
                        ┌─────────────────┐         ┌─────────────────┐         ┌─────────────────┐
                        │ Favorites       │         │ Task Service    │         │ Notes Service   │
                        │ Service         │         │ (Port 18082)    │         │ (Port 18083)    │
                        │ (Port 18081)    │         └─────────────────┘         └─────────────────┘
                        └─────────────────┘                   │                           │
                                  │                           │ Database
                                  │ MQ Message                ▼
                                  ▼                 ┌─────────────────┐
                        ┌─────────────────┐         │   MySQL         │
                        │   RabbitMQ      │         │   Database      │
                        │                 │         │                 │
                        └─────────────────┘         └─────────────────┘
                                  │                           ▲
                                  │ Consume                   │ Database
                                  ▼                           │
                        ┌─────────────────┐                   │
                        │   Bilibili      │───────────────────┘
                        │   Crawler       │
                        └─────────────────┘
                                  │
                                  │ Download Files
                                  ▼
                        ┌─────────────────┐
                        │   File Storage  │
                        │                 │
                        └─────────────────┘
```

## 业务流程

### 1. 收藏管理流程

#### 添加收藏
1. **客户端请求**: `POST /api/favorites/add`
2. **Gateway处理**: 验证权限，获取Thrift客户端
3. **RPC调用**: Gateway → Favorites Service
4. **业务处理**:
   - 验证URL格式
   - 转换短链接为长链接
   - 创建收藏记录到数据库
   - 发送MQ消息到下载队列
5. **异步下载**: Bilibili Crawler消费MQ消息，执行下载
6. **状态回调**: 下载完成后通过数据库更新状态

#### 获取收藏列表
1. **客户端请求**: `GET /api/favorites/list?offset=0&size=10`
2. **Gateway处理**: 解析分页参数
3. **RPC调用**: Gateway → Favorites Service
4. **数据查询**: 从数据库分页查询收藏记录
5. **返回结果**: 包含列表数据、总数、是否有更多数据

### 2. 任务管理流程

#### 添加任务
1. **客户端请求**: `POST /api/task/add`
2. **Gateway处理**: 验证权限，获取Thrift客户端
3. **RPC调用**: Gateway → Task Service
4. **业务处理**:
   - 验证任务参数
   - 生成任务ID
   - 创建任务记录到数据库
5. **返回结果**: 任务创建成功

#### 任务操作
1. **客户端请求**: `POST /api/task/action` (开始/完成/暂停等)
2. **Gateway处理**: 解析操作参数
3. **RPC调用**: Gateway → Task Service
4. **业务处理**:
   - 更新任务状态
   - 记录时间线
   - 更新数据库
5. **返回结果**: 操作执行结果

## API 接口文档

### Favorites API

#### 1. 添加收藏
```http
POST /api/favorites/add
Content-Type: application/json
Headers: Bullet: 36fdf066-9e42-11ec-b41e-525400043ced

{
  "collect_type": "1001",
  "origin": "https://www.bilibili.com/video/BV1234567890",
  "title": "视频标题",
  "save_path": "/videos/",
  "remark": "备注信息"
}
```

**响应:**
```json
{
  "message": "create task success"
}
```

#### 2. 获取收藏列表 (支持分页)
```http
GET /api/favorites/list?offset=0&size=10
Headers: Bullet: 36fdf066-9e42-11ec-b41e-525400043ced
```

**参数说明:**
- `offset`: 偏移量，从第几条记录开始，默认为0
- `size`: 每页记录数，默认为10，最大100

**响应:**
```json
{
  "data": [
    {
      "origin": "https://www.bilibili.com/video/BV1234567890",
      "title": "视频标题",
      "author": "66",
      "state": "success",
      "create_time": "2024-01-01 12:00:00"
    }
  ],
  "has_more": true,
  "total": 25
}
```

**字段说明:**
- `data`: 当前页的收藏记录列表
- `has_more`: 是否还有更多数据
- `total`: 总记录数

#### 3. 获取磁盘信息
```http
GET /api/favorites/disk/info
Headers: Bullet: 36fdf066-9e42-11ec-b41e-525400043ced
```

### Task API

#### 1. 添加任务
```http
POST /api/task/add
Content-Type: application/json
Headers: Bullet: 36fdf066-9e42-11ec-b41e-525400043ced

{
  "title": "任务标题",
  "parent": "5",
  "priority": "2",
  "description": "任务描述",
  "ddl": "2024-12-31 23:59:59"
}
```

#### 2. 获取任务列表
```http
GET /api/task/list
Headers: Bullet: 36fdf066-9e42-11ec-b41e-525400043ced
```

#### 3. 任务操作 (开始/完成/暂停等)
```http
POST /api/task/action
Content-Type: application/json
Headers: Bullet: 36fdf066-9e42-11ec-b41e-525400043ced

{
  "taskIDLabel": "991104",
  "action": "start",
  "mustLeaf": "true"
}
```

#### 4. 搜索任务
```http
GET /api/task/search?q=关键词
Headers: Bullet: 36fdf066-9e42-11ec-b41e-525400043ced
```

### Notes API

#### 1. 添加笔记
```http
POST /api/notes/add
Content-Type: application/json
Headers: Bullet: 36fdf066-9e42-11ec-b41e-525400043ced

{
  "title": "笔记标题",
  "content": "笔记内容",
  "category": "分类名称",
  "tags": "标签1,标签2"
}
```

#### 2. 获取笔记列表
```http
GET /api/notes/list?offset=0&size=10&keyword=搜索关键词&category=分类名称
Headers: Bullet: 36fdf066-9e42-11ec-b41e-525400043ced
```

#### 3. 获取单个笔记
```http
GET /api/notes/{id}
Headers: Bullet: 36fdf066-9e42-11ec-b41e-525400043ced
```

#### 4. 更新笔记
```http
PUT /api/notes/{id}
Content-Type: application/json
Headers: Bullet: 36fdf066-9e42-11ec-b41e-525400043ced

{
  "title": "更新的标题",
  "content": "更新的内容",
  "category": "新分类",
  "tags": "新标签"
}
```

#### 5. 删除笔记
```http
DELETE /api/notes/{id}
Headers: Bullet: 36fdf066-9e42-11ec-b41e-525400043ced
```

## 消息队列

### 队列定义

1. **favorites_pull_resource_bilibili_start**: B站视频下载任务队列
2. **favorites_pull_resource_wx_start**: 微信文章下载任务队列  
3. **favorites_pull_resource_status**: 下载状态回传队列

### 消息格式

#### 下载任务消息 (PullResourceStartMsg)
```json
{
  "CollectRecordID": 123,
  "Url": "https://www.bilibili.com/video/BV1234567890",
  "SavePath": "/videos/",
  "TaskSource": 1,
  "IsMultiple": true
}
```

#### 状态回传消息 (PullResourceStatusMsg)
```json
{
  "CollectRecordID": 123,
  "StatusCode": 200,
  "StatusMessage": "success",
  "Duration": 5000,
  "TaskSource": 1,
  "Title": "视频标题",
  "FilesNum": 1,
  "TotalSize": 1048576
}
```

## 数据库设计

### collect_record 表 (Favorites Service)
```sql
CREATE TABLE collect_record (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  collect_type BIGINT NOT NULL,
  origin VARCHAR(500) NOT NULL,
  save_path VARCHAR(200),
  remark TEXT,
  status_code BIGINT DEFAULT 0,
  status_message VARCHAR(100),
  duration BIGINT,
  duration_pull_meta BIGINT,
  duration_details TEXT,
  title VARCHAR(200),
  files_num BIGINT,
  files_status TEXT,
  files_missing_ids TEXT,
  total_size VARCHAR(50),
  env VARCHAR(20),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL
);
```

### task 表 (Task Service)
```sql
CREATE TABLE task (
  level BIGINT NOT NULL,
  id VARCHAR(50) PRIMARY KEY,
  parent VARCHAR(50) NOT NULL,
  title VARCHAR(200) NOT NULL,
  label VARCHAR(100),
  categoryID BIGINT,
  isMeta BOOLEAN DEFAULT FALSE,
  status VARCHAR(20) DEFAULT 'unstarted',
  description TEXT,
  priority BIGINT DEFAULT 2,
  quadrant BIGINT DEFAULT 0,
  ddl TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL
);
```

### timelines 表 (Task Service)
```sql
CREATE TABLE timelines (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  task_id VARCHAR(50) NOT NULL,
  start_time TIMESTAMP NOT NULL,
  end_time TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 部署配置

### 环境变量
- `ENV`: 环境标识 (dev/deploy)
- `TREETHEME_INSTALL_MODE`: 安装模式

### Docker 网络
- 网络名称: `admin-net`
- 服务间通过容器名称通信

### 存储路径
- 开发环境: `/data/docker-volumes/public_root/file_resources`
- 生产环境: `/store_root`

## 分页功能详解

### 参数说明
- **offset**: 偏移量，从第几条记录开始，默认为0
- **size**: 每页记录数，默认为10，最大100

### 安全限制
- 最大每页记录数: 100条
- 最小每页记录数: 1条 (0或负数会被重置为10)

### 响应字段
- **data**: 当前页的记录列表
- **total**: 总记录数
- **has_more**: 是否还有更多数据 (offset + size < total)

### 使用示例
```bash
# 获取第一页，每页10条
curl -H "Bullet: 36fdf066-9e42-11ec-b41e-525400043ced" \
  "http://localhost:16135/api/favorites/list?offset=0&size=10"

# 获取第二页，每页20条
curl -H "Bullet: 36fdf066-9e42-11ec-b41e-525400043ced" \
  "http://localhost:16135/api/favorites/list?offset=20&size=20"
```

## 开发指南

### 本地开发

1. **启动Gateway**:
```bash
cd personal_service_biz/gateway
ENV=dev go run main.go
```

2. **启动Favorites Service**:
```bash
cd personal_service_biz/favorites-service
ENV=dev go run main.go
```

3. **启动Task Service**:
```bash
cd personal_service_biz/task-service
ENV=dev go run main.go
```

4. **启动Bilibili Crawler**:
```bash
cd personal_crawler/bilibili-crawler
python main.py
```

### 测试

#### 单元测试
```bash
# Gateway测试
cd personal_service_biz/gateway
make TestFavoritesList

# Favorites Service测试
cd personal_service_biz/favorites-service
make test
```

#### 集成测试

集成测试用于验证完整的功能链路，包括API接口、数据库操作、业务逻辑等。

##### 1. API集成测试脚本

创建集成测试脚本来验证API功能：

```javascript
#!/usr/bin/env node
const axios = require('axios');

// API配置
const API_BASE_URL = 'https://local.luzeshu.cn/api';
const API_HEADERS = {
  'Content-Type': 'application/json',
  'bullet': '36fdf066-9e42-11ec-b41e-525400043ced',
};

const axiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: API_HEADERS,
});

// 测试结果跟踪
let testResults = { passed: 0, failed: 0, total: 0, details: [] };

function logTest(testName, passed, message = '') {
  testResults.total++;
  if (passed) {
    testResults.passed++;
    console.log(`✅ ${testName}: PASSED ${message}`);
  } else {
    testResults.failed++;
    console.log(`❌ ${testName}: FAILED ${message}`);
  }
  testResults.details.push({ testName, passed, message });
}

async function testAPI() {
  console.log('🧪 API集成测试开始\n');

  try {
    // 测试添加功能
    const addResponse = await axiosInstance.post('/favorites/add', {
      collect_type: '1001',
      origin: 'https://www.bilibili.com/video/BV1234567890',
      title: '测试视频',
      save_path: '/test/',
      remark: '集成测试'
    });
    logTest('添加收藏', addResponse.data?.message === 'create task success');

    // 测试列表功能
    const listResponse = await axiosInstance.get('/favorites/list', {
      params: { offset: 0, size: 10 }
    });
    const hasData = listResponse.data?.data && Array.isArray(listResponse.data.data);
    logTest('获取列表', hasData, `数据条数: ${listResponse.data?.data?.length}`);

    // 测试搜索功能
    const searchResponse = await axiosInstance.get('/favorites/list', {
      params: { offset: 0, size: 10, keyword: '测试' }
    });
    logTest('搜索功能', searchResponse.data?.data !== undefined);

  } catch (error) {
    logTest('API连接', false, error.message);
  }
}

function printSummary() {
  console.log('\n📊 测试结果汇总');
  console.log(`总测试数: ${testResults.total}`);
  console.log(`通过: ${testResults.passed} ✅`);
  console.log(`失败: ${testResults.failed} ❌`);
  console.log(`成功率: ${((testResults.passed / testResults.total) * 100).toFixed(1)}%`);
}

testAPI().then(printSummary);
```

##### 2. 使用方法

```bash
# 1. 创建测试脚本
cat > integration_test.js << 'EOF'
# [将上面的脚本内容粘贴到这里]
EOF

# 2. 安装依赖
npm install axios

# 3. 运行测试
node integration_test.js
```

##### 3. 测试覆盖范围

集成测试应该覆盖以下方面：

- **API接口测试**: 验证所有HTTP接口的正确性
- **数据库操作**: 验证CRUD操作的完整性
- **业务逻辑**: 验证复杂业务流程
- **错误处理**: 验证异常情况的处理
- **性能测试**: 验证接口响应时间
- **并发测试**: 验证高并发场景

##### 4. 最佳实践

1. **测试环境隔离**: 使用独立的测试数据库
2. **数据清理**: 测试前后清理测试数据
3. **断言完整**: 验证返回数据的结构和内容
4. **错误捕获**: 记录详细的错误信息
5. **结果报告**: 生成清晰的测试报告
6. **持续集成**: 集成到CI/CD流程中

##### 5. 客户端应用测试

对于React Native应用，可以进行以下测试：

```bash
# 编译检查
cd personal_clients/thinking-react-native
npx tsc --noEmit --skipLibCheck

# 构建测试
npm run android

# 功能验证
node integration_test.js
```

## 技术特性

1. **微服务架构**: 服务间解耦，独立部署
2. **Thrift RPC**: 高性能跨语言RPC通信
3. **异步处理**: 基于RabbitMQ的异步任务处理
4. **容错机制**: MQ重试机制，服务重连
5. **分页查询**: 支持offset/size分页
6. **权限验证**: 基于Header的简单权限验证
7. **参数验证**: 自动限制分页大小，防止过大查询
8. **准确计数**: 正确计算总记录数和是否有更多数据

## 收藏类型

- `1001`: B站视频
- `2001`: 微信文章

## 任务来源类型

- `1`: 用户手动添加
- `2`: 后台任务
- `3`: 延迟获取任务

## 客户端应用

### 1. Thinking React Native
- 路径: `personal_clients/thinking-react-native/`
- 功能: 移动端任务管理和收藏管理应用
- 技术栈: React Native + TypeScript

### 2. Favorites Android
- 路径: `personal_clients/favorites-android/`
- 功能: Android原生收藏管理应用
- 技术栈: Kotlin + Android SDK

### 3. Task Assistant
- 路径: `personal_clients/task-assistant/`
- 功能: 命令行任务管理工具
- 技术栈: Rust

## AI Agent 上下文文档

为了帮助Cursor、Augment等AI代码助手更好地理解复杂的开发环境，项目提供了专门的上下文文档：

### 📋 [README-for-AI.md](./README-for-AI.md)
**完整的开发环境规范文档**
- 详细的约束条件和常见错误
- 完整的微服务架构说明
- 部署流程和调试指南
- 网络配置和权限管理
- 适合深入了解项目架构

### 🚀 [AI-CONTEXT-QUICK-REF.md](./AI-CONTEXT-QUICK-REF.md)
**快速参考卡片**
- 关键约束的速查表
- 常用命令和配置
- 故障排除步骤
- 适合快速查阅和问题解决

### 使用建议
1. **新AI Agent首次接触项目**：先阅读完整的README-for-AI.md
2. **日常开发任务**：使用AI-CONTEXT-QUICK-REF.md快速查阅
3. **遇到问题时**：参考故障排除章节

这些文档总结了开发过程中AI Agent经常犯的错误，如：
- 使用`sudo`命令时丢失`.bashrc`配置
- 错误使用`127.0.0.1`而非`https://local.luzeshu.cn`
- 端口动态分配导致SSH隧道失效
- 使用`docker logs`而非文件路径查看日志

## 更新日志

### 2024-06-29
1. **新增AI Agent上下文文档**:
   - 创建README-for-AI.md完整规范文档
   - 创建AI-CONTEXT-QUICK-REF.md快速参考卡片
   - 总结常见错误和解决方案
   - 提供标准化的开发流程指导

### 2024-06-22
1. **新增Notes Service**:
   - 实现完整的笔记管理功能，支持CRUD、分类、标签、搜索
   - 端口18083，完整的Thrift RPC接口
   - 参考favorites-service的项目结构和部署方式
   - 集成到gateway，提供HTTP API接口
2. **完善README文档**: 添加了Task Service的详细说明，包括API接口、数据库设计等
3. **简化分页参数**:
   - 移除了count参数，统一使用size参数
   - 更新了Gateway、Favorites Service、客户端代码
   - 保持向后兼容性，默认size=10，最大100
4. **优化分页逻辑**:
   - 正确计算total总记录数
   - 准确判断has_more字段
   - 添加参数验证和安全限制
