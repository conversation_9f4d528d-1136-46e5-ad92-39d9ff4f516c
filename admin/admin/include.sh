#!/bin/bash
# set -x
filename=$(readlink -f $0)
cur_dir=$(dirname $filename)

# 用户git clone时指定的用户
main_user=tic

project_dir=/data/projects

admin_dir=${project_dir}/admin
personal_common_dir=${project_dir}/personal_common
service_biz_dir=${project_dir}/personal_service_biz
crawler_dir=${project_dir}/personal_crawler

admin_git_prefix="ssh://*****************/data/gitsrv/admin"
common_git_prefix="ssh://*****************/data/gitsrv"
service_biz_git_prefix="ssh://*****************/data/gitsrv/personal_service_biz"
# service_biz_git_prefix="ssh://*****************/data/gitsrv"
crawler_git_prefix="ssh://*****************/data/gitsrv/personal_crawler"
# crawler_git_prefix="ssh://*****************/data/gitsrv"

mkdir -p ${admin_dir}
mkdir -p ${personal_common_dir}
mkdir -p ${service_biz_dir}
mkdir -p ${crawler_dir}

output_dir=${cur_dir}/output
stdout_file=${output_dir}/stdout
stderr_file=${output_dir}/stderr
rm -rf ${output_dir}
mkdir -p ${output_dir}

chown -R ${main_user}:${main_user} ${project_dir}


install_mode_conf_file="${cur_dir}/install_mode_configs/${TREE_INSTALL_MODE}.conf"
scripts_dir=${cur_dir}/scripts
source ${scripts_dir}/common.sh
source ${scripts_dir}/step_prepare_public_root.sh
source ${scripts_dir}/step_prepare_docker_image.sh
source ${scripts_dir}/step_deploy_dbmanager.sh
source ${scripts_dir}/step_deploy_rabbit_mq.sh
source ${scripts_dir}/step_deploy_es.sh
source ${scripts_dir}/step_deploy_services.sh
source ${scripts_dir}/step_deploy_jump_server.sh

if [[ $(docker network ls | grep admin-net) == "" ]]; then
    docker network create -d bridge admin-net
fi
