#!/bin/bash
# set -x

CUR_DIR=$(dirname $(readlink -f $0))
source ${CUR_DIR}/include.sh

CallStep StepPreparePublicRoot
CallStep StepPrepareDockerImage

CallStep StepDeployDbManager
CallStep StepDeployDbGitea
CallStep StepDeployDbAccounts
CallStep StepDeployDbMindmap
CallStep StepDeployDbVideoParser
CallStep StepDeployDbFavorites
CallStep StepDeployDbFinancial
CallStep StepDeployDbBlog
CallStep StepDeployDbTask
CallStep StepDeployDbNotes
CallStep StepDeployDbSynchronizer


CallStep StepDeployRabbitMq

CallStep StepDeployEs


# 下面所有依赖thrift的服务的通用依赖项
CallStep StepDeployThriftCommon

CallStep StepDeployFavoritesService
CallStep StepDeployBilibiliCrawler
CallStep StepDeployBlogService
CallStep StepDeployWebProxy
# gateway放最后，在go build是依赖其他服务的client: /usr/local/go/src/thrift-common/client/task,favorites
CallStep StepDeployGateway

# gateway/blog-service等要在nginx-admin之前，否则nginx配置启动时报错upstream host not found
CallStep StepDeployNginxAdmin

CallStep StepDeployJumpServer

# 部署gitea
# docker rm -f gitea gitea-mysql nginx
# rm -rf /data/docker-volumes/{gitea,gitea-mysql,nginx}
# ${admin_dir}/dbmanager/scripts/create_dbs.sh gitea-mysql
# ${admin_dir}/git-admin/gitea/install.sh
# # nginx有配置，需要现有其他web容器，否则配置启动出错
# ${admin_dir}/nginx-admin/install.sh
