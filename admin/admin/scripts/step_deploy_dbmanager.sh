#!/bin/bash

function StepDeployDbManager {
    local repo_dir=${admin_dir}/dbmanager
    local git_url=${admin_git_prefix}/dbmanager.git

    PullRepo ${repo_dir} ${git_url}

    # 部署dbmanager
    # ${admin_dir}/jump-server/install.sh
    # 通过DoCmd最后不进入交互式container bash
    # DoCmd "${admin_dir}/jump-server/install.sh"
}

function stepDeployDb {
    local db_name=$1
    local container_path="/data/docker-volumes/${db_name}"
    if [[ -d ${container_path} ]]; then
        PrintWarn "Already Exists ${container_path}, if rm path, using FORCE_DB=1(recreate all db) or FORCE_DB=${db_name}(recreate only ${db_name})"
        if [[ ${FORCE_DB} == 1 ]] || [[ ${FORCE_DB} == ${db_name} ]]; then
            DoCmd "docker rm -f ${db_name}"
            DoCmd "rm -rf /data/docker-volumes/${db_name}"
        else
            if docker ps -a --format '{{.Names}}' | grep -q "^${db_name}$"; then
                # https://stackoverflow.com/questions/48767760/how-to-make-docker-container-ls-f-name-filter-by-exact-name
                local status=$(docker container inspect ${db_name} -f '{{.State.Status}}')
                if [[ ${status} == 'exited' ]]; then
                    PrintRaw "Container ${db_name} has exited"
                    PrintRaw "Restart... " && docker container restart ${db_name}
                else
                    PrintRaw "Do Nothing."
                fi
            fi
        fi
    fi
    DoCmd "${admin_dir}/dbmanager/scripts/create_dbs.sh ${db_name}"
}

# 部署db:gitea-mysql
function StepDeployDbGitea {
    stepDeployDb gitea-mysql
}

# 部署db:accounts-mysql
function StepDeployDbAccounts {
    stepDeployDb accounts-mysql
}

# 部署db:mindmap-mysql
function StepDeployDbMindmap {
    stepDeployDb mindmap-mysql
}

# 部署db:videoparser-mysql
function StepDeployDbVideoParser {
    stepDeployDb videoparser-mysql
}

# 部署db:favorites-mysql
function StepDeployDbFavorites {
    stepDeployDb favorites-mysql
}

# 部署db:financial-mysql
function StepDeployDbFinancial {
    stepDeployDb financial-mysql
}

# 部署db:task-mysql
function StepDeployDbTask {
    stepDeployDb task-mysql
}

# 部署db:blog-mysql
function StepDeployDbBlog {
    stepDeployDb blog-mysql
}

# 部署db:synchronizer-mysql
function StepDeployDbSynchronizer {
    stepDeployDb synchronizer-mysql
}

# 部署db:notes-mysql
function StepDeployDbNotes {
    stepDeployDb notes-mysql
}
