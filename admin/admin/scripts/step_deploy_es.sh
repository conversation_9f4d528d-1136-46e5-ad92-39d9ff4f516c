#!/bin/bash

cur_dir=$(dirname $(readlink -f $0))

function deployEs {
    local es_name=$1
    local container_path=/data/docker-volumes/${es_name}
    local network="admin-net"
    local image_name=elasticsearch:8.2.3
    # image_name=elasticsearch:8.12.2
    
    # 检查镜像是否存在
    if docker image inspect "$image_name" &>/dev/null; then
        echo "镜像 ${image_name} 已存在，跳过拉取。"
    else
        echo "镜像 ${image_name} 不存在，正在拉取..."
        docker pull "$image_name"
    fi
    
    if docker ps -a --format '{{.Names}}' | grep -q "^${es_name}$"; then
        docker container rm -f ${es_name}
    fi
    rm -rf ${container_path}
    
    mkdir -p ${container_path}/{config,logs,plugins,data}
    
    if ! docker network inspect "$network" &>/dev/null; then
        docker network create -d bridge "$network"
    fi
    
    # echo -e 'cluster.name: "docker-cluster"\nnetwork.host: 0.0.0.0' > ${container_path}/config/elasticsearch.yml
    cp ${cur_dir}/configs/elasticsearch.yml ${container_path}/config/elasticsearch.yml
    
    chmod -R 777 ${container_path}/*
    
    docker run --add-host=host.docker.internal:host-gateway  --net ${network} --name ${es_name} \
           --restart unless-stopped \
           -e "discovery.type=single-node" \
           -e ES_JAVA_OPTS="-Xms84m -Xmx512m" \
           -v ${container_path}/config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml \
           -v ${container_path}/logs:/usr/share/elasticsearch/logs \
           -v ${container_path}/data:/usr/share/elasticsearch/data \
           -v ${container_path}/plugins:/usr/share/elasticsearch/plugins \
           -d ${image_name}
    
    # export ELASTIC_PASSWORD="${KAE_ES_CLUSTER_CACHE_QA_AI_PASSWD}"
    # docker exec -it ${es_name} /usr/share/elasticsearch/bin/elasticsearch-reset-password -u elastic
    # 安装插件
    # docker exec -it tree-file-parser-es /bin/bash
}


function deployKibana {
    local image_name=kibana:8.2.3
    # local image_name=kibana:8.12.2
    local container_name=ai-kibana
    local network="admin-net"
    local container_path=/data/docker-volumes/${container_name}
    # 检查镜像是否存在
    if docker image inspect "$image_name" &>/dev/null; then
        echo "镜像 ${image_name} 已存在，跳过拉取。"
    else
        echo "镜像 ${image_name} 不存在，正在拉取..."
        docker pull "$image_name"
    fi
    

    # local es_hosts=""
    # for host in "$@"; do
    #     es_hosts+="\"http://${host}:9200\","
    # done
    # es_hosts="[${es_hosts%,}]"  # 移除末尾多余的逗号
    local es_hosts="http://ai-es:9200"

    
    if docker ps -a --format '{{.Names}}' | grep -q "^${container_name}$"; then
        docker container rm -f ${container_name}
    fi
    rm -rf ${container_path}
    
    mkdir -p ${container_path}/config
    chmod -R 777 ${container_path}/*
    cp ${cur_dir}/configs/kibana.yml ${container_path}/config/kibana.yml
    
    set -x
    docker run --net ${network} --name ${container_name} \
           --restart unless-stopped \
           -e "ELASTICSEARCH_HOSTS=${es_hosts}" \
           -e "ELASTICSEARCH_USERNAME=" \
           -e "ELASTICSEARCH_PASSWORD=" \
           -v ${container_path}/config/kibana.yml:/usr/share/kibana/config/kibana.yml \
           -d ${image_name}
    set +x
    # docker logs -f ${container_name}
}

function StepDeployEs {
    # deployEs ai-es
    # deployEs tree-es
    deployKibana ai-es tree-es
}

