#!/bin/bash

function StepDeployJumpServer {
    local repo_dir=${admin_dir}/jump-server
    local git_url=${admin_git_prefix}/jump-server.git

    PullRepo ${repo_dir} ${git_url}

    # 部署jump-server
    # 如果format 'table {{.ID}}'则会加上头部
    if [[ $(docker container ls -a --filter "name=^jump-server$" --format '{{.ID}}') ]]; then
      PrintWarn "Already Exist Container jump-server. Please using FORCE_STEP=jump-server"
        if [[ ${FORCE_STEP} == "jump-server" ]]; then
            DoCmd "oproxy"
            DoCmd "${admin_dir}/jump-server/install.sh"
            DoCmd "cproxy"
        else
            if docker ps -a --format '{{.Names}}' | grep -q "^jump-server$"; then
                local status=$(docker container inspect jump-server -f '{{.State.Status}}')
                if [[ ${status} == 'exited' ]]; then
                    PrintRaw "Container jump-server has exited, Restart..."
                    docker container restart jump-server
                else
                    PrintRaw "Do Nothing."
                fi
            fi
        fi
    else
      # ${admin_dir}/jump-server/install.sh
      # 通过DoCmd最后不进入交互式container bash
      DoCmd "oproxy"
      DoCmd "${admin_dir}/jump-server/install.sh"
      DoCmd "cproxy"
    fi
}
