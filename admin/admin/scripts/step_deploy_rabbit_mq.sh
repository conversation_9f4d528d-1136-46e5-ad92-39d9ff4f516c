#!/bin/bash

function StepDeployRabbitMq {
    local db_name="rabbit-mq"
    local container_path="/data/docker-volumes/${db_name}"
    if [[ -d ${container_path} ]]; then
        PrintWarn "Already Exists ${container_path}, if rm path, using FORCE_MQ=1"
        if [[ ${FORCE_MQ} == 1 ]]; then
            DoCmd "docker rm -f ${db_name}"
            DoCmd "rm -rf /data/docker-volumes/${db_name}"
        else
            if docker ps -a --format '{{.Names}}' | grep -q "^${db_name}$"; then
                # https://stackoverflow.com/questions/48767760/how-to-make-docker-container-ls-f-name-filter-by-exact-name
                local status=$(docker container inspect ${db_name} -f '{{.State.Status}}')
                if [[ ${status} == 'exited' ]]; then
                    PrintRaw "Container ${db_name} has exited, Restart..."
                    docker container restart ${db_name}
                else
                    PrintRaw "Do Nothing."
                fi
            fi
        fi
    fi
    DoCmd "${admin_dir}/dbmanager/scripts/create_mq.sh"
}
