#!/bin/bash

function checkDeployService {
    local srv_name=$1
    local container_path="/data/docker-volumes/${srv_name}"
    if [[ -d ${container_path} ]]; then
        PrintWarn "Already Exists ${container_path}, if rm path, using FORCE_SVR=1(recreate all services) or FORCE_SVR=${srv_name}(recreate only ${srv_name})"
        if [[ ${FORCE_SVR} == 1 ]] || [[ ${FORCE_SVR} == ${srv_name} ]]; then
            if docker ps -a --format '{{.Names}}' | grep -q "^${srv_name}$"; then
                # 容器存在时再rm，避免报错: Error response from daemon: No such container: gitea-mysql
            	DoCmd "docker rm -f ${srv_name}"
            fi
            DoCmd "rm -rf /data/docker-volumes/${srv_name}"
            return 1
        else
            if docker ps -a --format '{{.Names}}' | grep -q "^${srv_name}$"; then
                # https://stackoverflow.com/questions/48767760/how-to-make-docker-container-ls-f-name-filter-by-exact-name
                local status=$(docker container inspect ${srv_name} -f '{{.State.Status}}')
                if [[ ${status} == 'exited' ]]; then
                    PrintRaw "Container ${srv_name} has exited, Restart..."
                    DoCmd "docker container restart ${srv_name}"
                else
                    PrintRaw "Do Nothing."
                fi
            fi
            return 0
        fi
    else
        # 不存在${container_path}，表示第一次部署
        return 1
    fi
}

function StepDeployThriftCommon {
    local repo_dir=${personal_common_dir}/thrift-common
    local git_url=${common_git_prefix}/thrift-common.git
    PullRepo ${repo_dir} ${git_url}

    DoCmd "make -C ${repo_dir} init"
    DoCmd "make -C ${repo_dir} gen"
}

function StepDeployNginxAdmin {
    local repo_dir=${admin_dir}/nginx-admin
    local git_url=${admin_git_prefix}/nginx-admin.git
    PullRepo ${repo_dir} ${git_url}

    checkDeployService "nginx-admin"
    local code=$?
    if [[ ${code} == 1 ]]; then
        DoCmd "oproxy"
        DoCmd "make -C ${repo_dir} deploy"
        DoCmd "cproxy"
    fi
}

function StepDeployGateway {
    local repo_dir=${service_biz_dir}/gateway
    local git_url=${service_biz_git_prefix}/gateway.git
    PullRepo ${repo_dir} ${git_url}

    checkDeployService "gateway"
    local code=$?
    if [[ ${code} == 1 ]]; then
        DoCmd "oproxy"
        DoCmd "make -C ${repo_dir} build"
        DoCmd "chown -R ${main_user} ${repo_dir}"
        DoCmd "make -C ${repo_dir} deploy"
        DoCmd "cproxy"
    fi
}

function StepDeployFavoritesService {
    local repo_dir=${service_biz_dir}/favorites-service
    local git_url=${service_biz_git_prefix}/favorites-service.git
    PullRepo ${repo_dir} ${git_url}

    checkDeployService "favorites-service"
    local code=$?
    if [[ ${code} == 1 ]]; then
        DoCmd "oproxy"
        DoCmd "make -C ${repo_dir} build"
        DoCmd "chown -R ${main_user} ${repo_dir}"
        DoCmd "make -C ${repo_dir} deploy"
        DoCmd "cproxy"
    fi
}

function StepDeployBilibiliCrawler {
    local repo_dir=${crawler_dir}/bilibili-crawler
    local git_url=${crawler_git_prefix}/bilibili-crawler.git
    PullRepo ${repo_dir} ${git_url}

    checkDeployService "bilibili-crawler"
    local code=$?
    if [[ ${code} == 1 ]]; then
        DoCmd "make -C ${repo_dir} deploy"
    fi
}

function StepDeployBlogService {
    local repo_dir=${service_biz_dir}/tree-blog
    local git_url=${service_biz_git_prefix}/tree-blog.git
    PullRepo ${repo_dir} ${git_url}

    checkDeployService "blog-service"
    local code=$?
    if [[ ${code} == 1 ]]; then
        DoCmd "oproxy"
        DoCmd "make -C ${repo_dir} build"
        DoCmd "chown -R ${main_user} ${repo_dir}"
        DoCmd "make -C ${repo_dir} deploy"
        DoCmd "cproxy"
    fi
}

function StepDeployWebProxy {
    local repo_dir=${service_biz_dir}/web-proxy
    local git_url=${service_biz_git_prefix}/web-proxy.git
    PullRepo ${repo_dir} ${git_url}

    checkDeployService "web-proxy"
    local code=$?
    if [[ ${code} == 1 ]]; then
        DoCmd "oproxy"
        DoCmd "make -C ${repo_dir} deploy"
        DoCmd "cproxy"
    fi

}
