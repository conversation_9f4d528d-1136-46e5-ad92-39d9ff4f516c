#!/bin/bash

function StepPrepareDockerImage {
    # oproxy_docker/cproxy_docker频繁重启docker.service导致异常退出，需要sleep，另外重启docker.service会导致其他容器全部exited。应在外层onekey.sh只执行一次。或者分离docker pull xxx和实际启动容器的步骤，避免启动容器后再开proxy导致重启
    DoCmd "oproxy_docker"
    # DoCmd "docker pull mysql:5.7"
    # DoCmd "docker pull rabbitmq:3.10.7-management"
    # DoCmd "docker pull nginx:latest"
    # DoCmd "docker pull golang:1.23"
    # DoCmd "docker pull python:3.10"
    # DoCmd "docker pull node:latest"
    # DoCmd "docker pull debian:bullseye"
    DoCmd "cproxy_docker"
    exit
}
