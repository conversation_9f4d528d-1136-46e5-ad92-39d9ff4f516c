#!/bin/bash

function StepPreparePublicRoot {
    local vol_root=/data/docker-volumes
    if [[ ! -d ${vol_root} ]]; then
        mkdir ${vol_root}
    fi
    local resource_path=${vol_root}/public_root/file_resources
    local resource_image=${vol_root}/fs_image_file_resources
    # cloud_server才用xfs限制容量
    if [[ ${TREETHEME_INSTALL_MODE} == "cloud_server" ]]; then
        if [[ ! -f ${resource_image} ]] || [[ ! -s ${resource_image} ]] ; then
            PrintRaw "Initial fs_image_file_resources."
            # 8G的镜像文件
            DoCmd "dd if=/dev/zero of=${resource_image} bs=512M count=16"
            DoCmd "/sbin/mkfs -t xfs -q ${resource_image}"
            if [[ -d ${resource_path} ]]; then
                DoCmd "umount ${resource_path}"
                DoCmd "rmdir ${resource_path}"
            fi
            DoCmd "mkdir -p ${resource_path}"
            DoCmd "mount -o 'loop,rw,usrquota,pquota' ${resource_image} ${resource_path}"
            DoCmd "xfs_quota -x -c 'project -s -p ${resource_path} 100' ${resource_path}"
            DoCmd "xfs_quota -x -c 'limit -p bsoft=8g bhard=8g 100' ${resource_path}"
            DoCmd "df -Th ${resource_path}"
            DoCmd "xfs_quota -x -c state"
        fi
    else
        mkdir -p ${resource_path}
    fi
}
