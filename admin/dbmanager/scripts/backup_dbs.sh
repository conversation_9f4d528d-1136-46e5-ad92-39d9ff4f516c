#!/bin/bash
CUR_DIR=$(dirname $(readlink -f $0))
BACKUP_PATH=/data/db-backups
mkdir -p ${BACKUP_PATH}

function backup_db {
	backup_container_name=dbbackup
	container_name=$1
	source ${CUR_DIR}/dbs/${container_name}/.env
	backup_path=${BACKUP_PATH}/${container_name}
	mkdir -p ${backup_path}

	cp ${CUR_DIR}/dbs/${container_name}/.env ${backup_path}/.env
	cp ${CUR_DIR}/run-in-containers/backup.sh ${backup_path}/backup.sh

	# 启动一个mysql容器，为了使用mysql客户端来执行命令，否则无法确定宿主机有安装mysqldump，或者版本一致
	docker run --name ${backup_container_name} --restart unless-stopped -v ${backup_path}:/backups --network="host" -e MYSQL_ROOT_PASSWORD=$(uuidgen) -d mysql:5.7
	docker container exec ${backup_container_name} bash /backups/backup.sh
	docker container rm -f ${backup_container_name}
}

backup_db accounts-mysql
