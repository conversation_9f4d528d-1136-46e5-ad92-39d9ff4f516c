#!/bin/bash
CUR_DIR=$(dirname $(readlink -f $0))
DB_PATH=/data/docker-volumes
mkdir -p ${DB_PATH}

# set -x

function create_db {
    if [[ $(docker network ls | grep admin-net) == "" ]]; then
        docker network create -d bridge admin-net
    fi

	container_name=$1
	source ${CUR_DIR}/dbs/${container_name}/.env

    if [[ ${FORCE_DB} == 1 ]] || [[ ${FORCE_DB} == ${container_name} ]]; then
        if docker ps -a --format '{{.Names}}' | grep -q "^${container_name}$"; then
            # 容器存在时再rm，避免报错: Error response from daemon: No such container: gitea-mysql
            docker rm -f ${container_name}
        fi
        rm -rf /data/docker-volumes/${container_name}
    fi

	db_path=${DB_PATH}/${container_name}/var/lib/mysql
	if [[ -d ${db_path} ]] && [[ "$(ls -A ${db_path})" ]] ; then
		PrintError "Already Exist Path: ${db_path}"
        return
	fi
	mkdir -p ${db_path}

	init_path=${DB_PATH}/${container_name}/init
	mkdir -p ${init_path}
	cp ${CUR_DIR}/run-in-containers/init.sh ${init_path}/init.sh
	cp ${CUR_DIR}/dbs/${container_name}/init.sql ${init_path}/init.sql
	cp ${CUR_DIR}/dbs/${container_name}/.env ${init_path}/.env

    PrintInfo "TREETHEME_DEPLOY: ${TREETHEME_DEPLOY}"
    # oproxy_docker/cproxy_docker频繁重启docker.service导致异常退出，需要sleep，另外重启docker.service会导致其他容器全部exited。应在外层onekey.sh只执行一次。或者分离docker pull xxx和实际启动容器的步骤，避免启动容器后再开proxy导致重启
    # sleep 30 && oproxy_docker
	docker run -d --net admin-net --name ${container_name} \
        --restart unless-stopped \
        -v ${db_path}:/var/lib/mysql \
        -v ${init_path}:/init \
        -e MYSQL_ROOT_PASSWORD=${db_root_pass} \
        -e MYSQL_CHARSET=utf8mb4 \
        -e MYSQL_COLLATION=utf8mb4_unicode_ci \
        mysql:5.7
    local exit_code=$?

    # sleep 3 && cproxy_docker

    echo "exit_code: ${exit_code}"
    if [[ ${exit_code} != 0 ]]; then
        echo "Failed! exit..."
        exit 1
    fi

    # 参考【项目】favorites里面adb获取进程状态的自旋锁，取代盲目的sleep 20s等待
	echo "waiting for mysqld start..."
    check_tag="Version:.*port: 3306"
    check_log=$(docker logs ${container_name} 2>&1 | grep "${check_tag}")
    while [[ ${check_log} == "" ]];do
      sleep 0.5
      check_log=$(docker logs ${container_name} 2>&1 | grep "${check_tag}")
    done
	echo "mysqld start success"
	docker container exec ${container_name} bash /init/init.sh
}

db_container_name=$1
if [[ ! -d ${CUR_DIR}/dbs/${db_container_name} ]]; then
  echo "Not Exist DB=${db_container_name} Info In Path ${CUR_DIR}/dbs/${db_container_name}"
  exit 2
fi

# 用法举例: create_db accounts-mysql
create_db ${db_container_name}
echo "Create DB ${db_container_name} Finish"
docker container ls
