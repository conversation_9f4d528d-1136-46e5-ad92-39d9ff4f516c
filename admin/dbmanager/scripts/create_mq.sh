#!/bin/bash

CUR_DIR=$(dirname $(readlink -f $0))
DB_PATH=/data/docker-volumes
mkdir -p ${DB_PATH}

function create_rabbitmq {
	local container_name=$1
	# source ${CUR_DIR}/dbs/${container_name}/.env

    local container_path="/data/docker-volumes/${container_name}"
    if [[ -d ${container_path} ]]; then
        PrintWarn "Already Exists ${container_path}, if rm path, using FORCE_MQ=1 or FORCE_RABBIT_MQ=1"
        if [[ ${FORCE_MQ} == 1 ]] || [[ ${FORCE_RABBIT_MQ} == 1 ]]; then
            DoCmd "docker rm -f ${container_name}"
            DoCmd "rm -rf /data/docker-volumes/${container_name}"
        else
            # https://stackoverflow.com/questions/48767760/how-to-make-docker-container-ls-f-name-filter-by-exact-name
            local status=$(docker container inspect ${container_name} -f '{{.State.Status}}')
            if [[ ${status} == 'exited' ]]; then
                PrintRaw "Container ${container_name} has exited, Restart..."
                docker container restart ${container_name}
            else
                PrintRaw "Do Nothing."
            fi
        fi
    fi


	local db_path=${DB_PATH}/${container_name}/var/lib/rabbitmq
	local conf_path=${DB_PATH}/${container_name}/etc/rabbitmq
	local log_path=${DB_PATH}/${container_name}/var/log/rabbitmq
	if [[ -d ${db_path} ]] && [[ "$(ls -A ${db_path})" ]] ; then
		PrintError "Already Exist Path: ${db_path}"
        return 
	fi
	mkdir -p ${db_path}
	mkdir -p ${conf_path}
    echo 'consumer_timeout = 30' > ${conf_path}/rabbitmq.conf
    # echo 'management.listener.port = 15672' >> ${conf_path}/rabbitmq.conf
	mkdir -p ${log_path}
    chmod -R 777 ${log_path}

	# init_path=${DB_PATH}/${container_name}/init
	# mkdir -p ${init_path}
	# cp ${CUR_DIR}/run-in-containers/init.sh ${init_path}/init.sh
	# cp ${CUR_DIR}/dbs/${container_name}/init.sql ${init_path}/init.sql
	# cp ${CUR_DIR}/dbs/${container_name}/.env ${init_path}/.env

	docker run -d --net admin-net --name ${container_name} \
        --restart unless-stopped \
        -v ${db_path}:/var/lib/rabbitmq \
        -v ${log_path}:/var/log/rabbitmq \
        -v ${conf_path}/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf \
        -e RABBITMQ_DEFAULT_USER=admin \
        -e RABBITMQ_DEFAULT_PASS=adminuser \
        rabbitmq:3.10.7-management
    # 参考【项目】favorites里面adb获取进程状态的自旋锁，取代盲目的sleep 20s等待
	echo "waiting for rabbitmq start..."
    check_tag="started TCP listener on"
    # check_log改写到日志文件了
    check_log=$(docker logs ${container_name} 2>&1 | grep "${check_tag}")
    # shopt -s extglob # 不打开的话，正则匹配的括号会报错
    # check_log=$(cat ${log_path}/rabbit@+([0-9a-z]).log | grep "${check_tag}")
    while [[ ${check_log} == "" ]];do
      sleep 0.5
      check_log=$(docker logs ${container_name} 2>&1 | grep "${check_tag}")
      # check_log=$(cat ${log_path}/rabbit@+([0-9a-z]).log | grep "${check_tag}")
    done
	echo "rabbitmq start success"
    # docker exec -u 0:0 ${container_name} chown -R rabbitmq:rabbitmq /var/log
	# docker container exec ${container_name} bash /init/init.sh
}


function create_rocketmq {
    local container_name=$1
	# source ${CUR_DIR}/dbs/${container_name}/.env

    local container_path="/data/docker-volumes/${container_name}"
    if [[ -d ${container_path} ]]; then
        PrintWarn "Already Exists ${container_path}, if rm path, using FORCE_MQ=1 or FORCE_ROCKET_MQ=1"
        if [[ ${FORCE_MQ} == 1 ]] || [[ ${FORCE_ROCKET_MQ} == 1 ]]; then
            DoCmd "docker rm -f ${container_name}"
            DoCmd "rm -rf /data/docker-volumes/${container_name}"
        else
            # https://stackoverflow.com/questions/48767760/how-to-make-docker-container-ls-f-name-filter-by-exact-name
            local status=$(docker container inspect ${container_name} -f '{{.State.Status}}')
            if [[ ${status} == 'exited' ]]; then
                PrintRaw "Container ${container_name} has exited, Restart..."
                docker container restart ${container_name}
            else
                PrintRaw "Do Nothing."
            fi
        fi
    fi

    if [ "$(docker ps -a -q -f name=${container_name})" ]; then
        # 存在就返回
        return
    fi


	local db_path=${DB_PATH}/${container_name}/home/<USER>/store
	local log_path=${DB_PATH}/${container_name}/home/<USER>/logs
	local conf_path=${DB_PATH}/${container_name}/home/<USER>/rocketmq-4.9.7/conf
	if [[ -d ${db_path} ]] && [[ "$(ls -A ${db_path})" ]] ; then
		PrintError "Already Exist Path: ${db_path}"
        return 
	fi
	mkdir -p ${db_path}
    chown -R 3000:3000 ${db_path}
    # chmod -R 777 ${db_path}
	mkdir -p ${log_path}
    chown -R 3000:3000 ${log_path}
    # chmod -R 777 ${log_path}
	mkdir -p ${conf_path}

    echo '
# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.

brokerClusterName = DefaultCluster
brokerName = broker-a
brokerId = 0
deleteWhen = 04
fileReservedTime = 48
brokerRole = ASYNC_MASTER
flushDiskType = ASYNC_FLUSH
' > ${conf_path}/broker.conf

    if [[ $1 == "rocket-mq-namesrv" ]]; then
        local init_cmd="sh mqnamesrv"
    else
        local init_cmd="sh mqbroker -n rocket-mq-namesrv:9876"
    fi
	docker run -d --net admin-net --name ${container_name} \
        --restart unless-stopped \
        --expose 9876 \
        -v ${db_path}:/home/<USER>/store \
        -v ${log_path}:/home/<USER>/logs \
        -v ${conf_path}/broker.conf:/home/<USER>/rocketmq-4.9.7/conf/broker.conf \
        apache/rocketmq:4.9.7 ${init_cmd}
    # 参考【项目】favorites里面adb获取进程状态的自旋锁，取代盲目的sleep 20s等待

	# echo "waiting for rabbitmq start..."
    # check_tag="started TCP listener on"
    # # check_log改写到日志文件了
    # check_log=$(docker logs ${container_name} 2>&1 | grep "${check_tag}")
    # # shopt -s extglob # 不打开的话，正则匹配的括号会报错
    # # check_log=$(cat ${log_path}/rabbit@+([0-9a-z]).log | grep "${check_tag}")
    # while [[ ${check_log} == "" ]];do
    #   sleep 0.5
    #   check_log=$(docker logs ${container_name} 2>&1 | grep "${check_tag}")
    #   # check_log=$(cat ${log_path}/rabbit@+([0-9a-z]).log | grep "${check_tag}")
    # done
	echo "${container_name} start success"
}

function deploy_rocketmq_dashboard {
    local container_name="rocketmq-dashboard"

    # https://stackoverflow.com/questions/38576337/how-to-execute-a-bash-command-only-if-a-docker-container-with-a-given-name-does
    if [ ! "$(docker ps -a -q -f name=${container_name})" ]; then
        # 不存在
	    docker run -d --net admin-net --name ${container_name} \
            --restart unless-stopped \
            -e "JAVA_OPTS=-Drocketmq.namesrv.addr=rocket-mq-namesrv:9876" \
            -p 18080:8080 -t apacherocketmq/rocketmq-dashboard:latest
	    echo "rocketmq-dashboard start success"

    else
        # 存在

        # https://stackoverflow.com/questions/48767760/how-to-make-docker-container-ls-f-name-filter-by-exact-name
        local status=$(docker container inspect ${container_name} -f '{{.State.Status}}')
        if [[ ${status} == 'exited' ]]; then
            PrintRaw "Container ${container_name} has exited, Restart..."
            docker container restart ${container_name}
        else
            PrintRaw "Do Nothing."
        fi
    fi

}

create_rabbitmq rabbit-mq
# create_rocketmq rocket-mq-namesrv
# create_rocketmq rocket-mq-broker1
# create_rocketmq rocket-mq-broker2
# create_rocketmq rocket-mq-broker3
# deploy_rocketmq_dashboard
