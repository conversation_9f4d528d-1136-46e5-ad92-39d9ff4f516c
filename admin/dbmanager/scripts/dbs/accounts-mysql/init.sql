CREATE TABLE `Accounts` (
	  `id` int(11) NOT NULL AUTO_INCREMENT,
	  `accountId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `accountName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `accountType` enum('xjzh','xnzh','jrzh','xyzh','tzzh','bxzh','zqzh','fzzh') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `typeName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `createdAt` datetime NOT NULL,
	  `updatedAt` datetime NOT NULL,
	  PRIMARY KEY (`id`),
	  UNIQUE KEY `accounts_unique_index_accountid` (`accountId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `Categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `categoryId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `level` enum('1','2') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `parentId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `categoryName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `comment` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `categorys_unique_index_categoryid` (`categoryId`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `RecordAlis` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tradeNo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `orderNo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `date` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payTime` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `lastModifyTime` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `origin` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `tradeType` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `partner` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `commodity` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `amount` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `incomeType` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `payStatus` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `serviceCharge` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `refund` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `fundStatus` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `createdAt` datetime NOT NULL,
  `updatedAt` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `RecordSsjBaks` (
	  `id` int(11) NOT NULL AUTO_INCREMENT,
	  `account` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `buyerAccount` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `buyerAccountId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `categoryIcon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `categoryId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `categoryName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `content` varchar(2048) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `currencyAmount` int(11) DEFAULT NULL,
	  `imgId` bigint(20) DEFAULT NULL,
	  `itemAmount` int(11) DEFAULT NULL,
	  `memberId` bigint(20) DEFAULT NULL,
	  `memberName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `memo` varchar(2048) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `projectId` int(11) DEFAULT NULL,
	  `projectName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `relation` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `sellerAccount` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `sellerAccountId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `tranId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `tranName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `tranType` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `transferStoreId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `date` int(11) DEFAULT NULL,
	  `day` int(11) DEFAULT NULL,
	  `hours` int(11) DEFAULT NULL,
	  `minutes` int(11) DEFAULT NULL,
	  `month` int(11) DEFAULT NULL,
	  `seconds` int(11) DEFAULT NULL,
	  `time` int(11) DEFAULT NULL,
	  `timezoneOffset` int(11) DEFAULT NULL,
	  `year` int(11) DEFAULT NULL,
	  `createdAt` datetime NOT NULL,
	  `updatedAt` datetime NOT NULL,
	  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `RecordSsjs` (
	  `id` int(11) NOT NULL AUTO_INCREMENT,
	  `account` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `buyerAccount` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `buyerAccountId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `categoryIcon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `categoryId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `categoryName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `content` varchar(2048) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `currencyAmount` int(11) DEFAULT NULL,
	  `imgId` bigint(20) DEFAULT NULL,
	  `itemAmount` int(11) DEFAULT NULL,
	  `memberId` bigint(20) DEFAULT NULL,
	  `memberName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `memo` varchar(2048) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `projectId` int(11) DEFAULT NULL,
	  `projectName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `relation` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `sellerAccount` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `sellerAccountId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `tranId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `tranName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `tranType` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `transferStoreId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `date` int(11) DEFAULT NULL,
	  `day` int(11) DEFAULT NULL,
	  `hours` int(11) DEFAULT NULL,
	  `minutes` int(11) DEFAULT NULL,
	  `month` int(11) DEFAULT NULL,
	  `seconds` int(11) DEFAULT NULL,
	  `time` int(11) DEFAULT NULL,
	  `timezoneOffset` int(11) DEFAULT NULL,
	  `year` int(11) DEFAULT NULL,
	  `createdAt` datetime NOT NULL,
	  `updatedAt` datetime NOT NULL,
	  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `RecordWxes` (
	  `id` int(11) NOT NULL AUTO_INCREMENT,
	  `tradeTime` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `tradeType` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `partner` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `commodity` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `incomeType` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `amount` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `itemAmount` int(11) DEFAULT NULL,
	  `payType` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `payStatus` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `tradeOrderNo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `mchOrderNo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `createdAt` datetime NOT NULL,
	  `updatedAt` datetime NOT NULL,
	  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `Records` (
	  `id` int(11) NOT NULL AUTO_INCREMENT,
	  `billType` tinyint(4) DEFAULT NULL,
	  `parentId` int(11) DEFAULT NULL,
	  `group` char(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
	  `accountId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `accountName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `tranAccountId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `tranAccountName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `itemAmount` int(11) DEFAULT NULL,
	  `tranName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `tranType` tinyint(4) DEFAULT NULL,
	  `categoryId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `categoryName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `categoryIcon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `time` datetime DEFAULT NULL,
	  `year` int(11) DEFAULT NULL,
	  `month` int(11) DEFAULT NULL,
	  `date` int(11) DEFAULT NULL,
	  `hours` int(11) DEFAULT NULL,
	  `minutes` int(11) DEFAULT NULL,
	  `seconds` int(11) DEFAULT NULL,
	  `day` int(11) DEFAULT NULL,
	  `imgId` bigint(20) DEFAULT NULL,
	  `memberId` bigint(20) DEFAULT NULL,
	  `memberName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `memo` varchar(2048) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `projectId` int(11) DEFAULT NULL,
	  `projectName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `relation` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `tranId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `transferStoreId` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `url` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `createdAt` datetime NOT NULL,
	  `updatedAt` datetime NOT NULL,
	  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
