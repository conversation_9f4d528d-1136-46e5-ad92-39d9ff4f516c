CREATE TABLE Users (
  id int(11) NOT NULL AUTO_INCREMENT,
  user varchar(50) NOT NULL,
  password varchar(255) NOT NULL,
  createTime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updateTime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleteTime TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (id)
) ENGINE=InnoDB AUTO_INCREMENT=489 DEFAULT CHARSET=utf8;

CREATE TABLE blogs (
  id char(36) CHARACTER SET utf8 COLLATE utf8_bin NOT NULL DEFAULT '',
  url varchar(255) NOT NULL,
  title varchar(255) DEFAULT NULL,
  content text DEFAULT NULL,
  readNum int(11) NOT NULL,
  status enum('POST','UNPOST') DEFAULT NULL,
  commentNum int(11) DEFAULT NULL,
  abstract text DEFAULT NULL,
  cnodeId varchar(50) DEFAULT NULL,
  cnblogId int(11) DEFAULT NULL,
  postTime datetime DEFAULT NULL,
  createTime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updateTime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleteTime TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE Comments (
  id char(36) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  blogId char(36) COLLATE utf8mb4_unicode_ci NOT NULL,
  comment mediumtext COLLATE utf8mb4_unicode_ci NOT NULL,
  user varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  email varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  link varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  reply char(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  fromCnblog tinyint(1) DEFAULT NULL,
  fromCnode tinyint(1) DEFAULT NULL,
  headpicurl varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  postTime datetime NOT NULL,
  status enum('READ','UNREAD') COLLATE utf8mb4_unicode_ci NOT NULL,
  cnodeCommentId char(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  cnblogCommentId char(36) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  createTime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updateTime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleteTime TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
