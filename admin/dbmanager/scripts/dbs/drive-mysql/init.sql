-- 云盘文件表
CREATE TABLE drive_files (
  id int(11) NOT NULL AUTO_INCREMENT,
  name varchar(255) NOT NULL COMMENT '文件名',
  path varchar(2048) NOT NULL COMMENT '文件路径',
  parent_id int(11) DEFAULT NULL COMMENT '父文件夹ID，NULL表示根目录',
  file_type enum('file', 'folder') NOT NULL DEFAULT 'file' COMMENT '文件类型：file-文件，folder-文件夹',
  mime_type varchar(128) DEFAULT NULL COMMENT 'MIME类型',
  size bigint DEFAULT 0 COMMENT '文件大小（字节）',
  hash varchar(64) DEFAULT NULL COMMENT '文件哈希值（SHA256）',
  storage_path varchar(2048) DEFAULT NULL COMMENT '实际存储路径',
  thumbnail_path varchar(2048) DEFAULT NULL COMMENT '缩略图存储路径',
  is_public tinyint(1) DEFAULT 0 COMMENT '是否公开：0-私有，1-公开',
  download_count int(11) DEFAULT 0 COMMENT '下载次数',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY(id),
  KEY idx_parent_id (parent_id),
  KEY idx_path (path(255)),
  KEY idx_file_type (file_type),
  KEY idx_hash (hash),
  CONSTRAINT fk_drive_files_parent FOREIGN KEY (parent_id) REFERENCES drive_files(id) ON DELETE CASCADE
)
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT "云盘文件表";

-- 文件分享表
CREATE TABLE drive_shares (
  id int(11) NOT NULL AUTO_INCREMENT,
  file_id int(11) NOT NULL COMMENT '文件ID',
  share_code varchar(32) NOT NULL COMMENT '分享码',
  password varchar(32) DEFAULT NULL COMMENT '提取密码',
  expire_time TIMESTAMP NULL DEFAULT NULL COMMENT '过期时间，NULL表示永不过期',
  download_limit int(11) DEFAULT 0 COMMENT '下载次数限制，0表示无限制',
  download_count int(11) DEFAULT 0 COMMENT '已下载次数',
  is_active tinyint(1) DEFAULT 1 COMMENT '是否激活：0-禁用，1-激活',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY(id),
  UNIQUE KEY uk_share_code (share_code),
  KEY idx_file_id (file_id),
  KEY idx_expire_time (expire_time),
  CONSTRAINT fk_drive_shares_file FOREIGN KEY (file_id) REFERENCES drive_files(id) ON DELETE CASCADE
)
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT "文件分享表";

-- 上传任务表
CREATE TABLE drive_upload_tasks (
  id int(11) NOT NULL AUTO_INCREMENT,
  file_name varchar(255) NOT NULL COMMENT '文件名',
  file_size bigint NOT NULL COMMENT '文件大小',
  chunk_size int(11) DEFAULT 1048576 COMMENT '分片大小（字节），默认1MB',
  total_chunks int(11) NOT NULL COMMENT '总分片数',
  uploaded_chunks int(11) DEFAULT 0 COMMENT '已上传分片数',
  upload_path varchar(2048) NOT NULL COMMENT '上传路径',
  temp_dir varchar(2048) NOT NULL COMMENT '临时目录',
  status enum('pending', 'uploading', 'completed', 'failed') DEFAULT 'pending' COMMENT '上传状态',
  error_message text DEFAULT NULL COMMENT '错误信息',
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY(id),
  KEY idx_status (status),
  KEY idx_created_at (created_at)
)
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT "上传任务表";

-- 创建根目录
INSERT INTO drive_files (name, path, parent_id, file_type, created_at, updated_at) 
VALUES ('/', '/', NULL, 'folder', NOW(), NOW());
