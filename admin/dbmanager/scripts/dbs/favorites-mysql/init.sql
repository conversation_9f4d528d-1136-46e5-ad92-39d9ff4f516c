CREATE TABLE collect_record (
  id int(11) NOT NULL AUTO_INCREMENT,
  collect_type smallint NOT NULL COMMENT '收藏类型, 1xxx表示视频, 2xxx表示网页',
  origin text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始链接',
  save_path varchar(2048) NOT NULL COMMENT '保存路径，目前存储采用原生系统文夹的方式',
  remark text COLLATE utf8mb4_unicode_ci COMMENT '备注',
  status_code smallint NOT NULL COMMENT '异步收藏状态, 0表示创建了记录待拉取， 2xx表示成功, 5xx表示失败',
  status_message varchar(256) NOT NULL COMMENT '异步收藏状态的详细描述',
  duration_pull_meta int(11) NULL COMMENT '拉meta时长，单位ms',
  duration int(11) NOT NULL COMMENT '任务处理时长,单位ms',
  duration_details text COMMENT '分步骤耗时详情',
  title varchar(256) COMMENT '标题',
  files_num varchar(256) COMMENT '文件数量',
  total_size varchar(32) DEFAULT NULL COMMENT '系列文件总size',
  files_status text COMMENT '文件列表信息，json序列化字符串',
  files_missing_ids varchar(1024) COMMENT '缺少的文件ids，json序列化字符串',
  env varchar(32) COMMENT '收藏的环境变量ENV来源，dev、deploy等等',
  -- treetheme_mode varchar(32) COMMENT '服务器的环境变量TREETHEME_INSTALL_MODE来源，local_server、cloud_server等等', 不能存该字段，db数据本身也要不同server间同步，必须对这个信息无感知
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY(id)
)
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT "收藏记录";

CREATE TABLE video_record (
  id int(11) NOT NULL AUTO_INCREMENT,
  -- 业务自身字段
  collect_record_id int(11) NOT NULL,
  file_id int(11) NOT NULL COMMENT '系列视频中的序号id',
  total_file int(11) NOT NULL COMMENT '系列视频总数',
  title varchar(256) NOT NULL COMMENT '标题',
  filename varchar(256) DEFAULT NULL COMMENT '文件名',
  format varchar(64) NOT NULL COMMENT '比如dash-flv，来自you-get -i获取的信息',
  filetype varchar(64) DEFAULT NULL COMMENT 'file -b --mime-type命令的校验结果',
  container varchar(16) NOT NULL COMMENT '比如mp4',
  quality varchar(64) NOT NULL COMMENT '视频质量',
  size int(11) NOT NULL COMMENT '文件大小，单位Byte',
  realsize int(11) DEFAULT NULL COMMENT '真实文件大小，单位Byte',
  checksum varchar(64) DEFAULT NULL COMMENT 'sha256摘要',
  -- duration_skip int(11) NULL COMMENT 'skip时长，单位ms',
  -- duration_waiting int(11) NULL COMMENT '时长，单位ms',
  -- duration_finish int(11) NULL COMMENT '总时长，单位ms',
  -- duration int(11) NULL COMMENT '总时长，单位ms',
  -- 转储同步方案协议字段
  sync_status int(11) NOT NULL DEFAULT 0 COMMENT '维护转储状态的字段，0：未转储；1：已转储到localserver；2：转储后数据有更新(暂无场景)；3：cloudserver已清除（表示业务上还正常存在，只是数据转储后在cloudserver进行物理删除节省空间）；4：清除后有更新(非法逻辑，暂无场景)；5：业务上逻辑删除；6：数据冲突；7：延迟到localserver进行拉取',
  sync_unit_type int(11) NOT NULL DEFAULT 0 COMMENT '维护转储数据单位的类型，0：未知/默认值；1：二进制数据；2：表数据',
  sync_path varchar(2048) DEFAULT NULL COMMENT '表示元记录寻找对应的转储数据单位的路径，对于sync_unit_type=1，格式表示二进制数据的路径；对于sync_unit_type=1，格式表示二进制数据的路径。',
  -- 全局规范字段
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY(id),
  UNIQUE KEY collect_file_id (collect_record_id, file_id),
  CONSTRAINT fk_tb_video_record FOREIGN KEY (collect_record_id) REFERENCES collect_record(id)
)
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT "拉取视频记录";
