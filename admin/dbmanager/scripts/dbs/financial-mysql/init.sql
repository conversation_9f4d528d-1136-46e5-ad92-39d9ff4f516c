CREATE TABLE `Funds` (
	  `id` int(11) NOT NULL AUTO_INCREMENT,
	  `fundCode` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `fundName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `fundAbbrName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `fundFullName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `fundType` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `fundSeries` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '级数',
      `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `FundKLineDailies` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `fundCode` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `date` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `openingPrice` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `closingPrice` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `highestPrice` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `lowestPrice` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `tradingVolume` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `turnover` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `amplitude` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `changePercent` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `change` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `turnoverRate` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      UNIQUE KEY `funds_unique_index_fundcode_date` (`fundCode`,`date`) USING BTREE,
      KEY `funds_index_date` (`date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `Stocks` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `stockCode` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `stockName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `cmd` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `stockType` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `stockCity` enum('sh','sz','bj') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `isNew` tinyint(1) DEFAULT NULL,
      `isSme` tinyint(1) DEFAULT NULL,
      `isGem` tinyint(1) DEFAULT NULL,
      `isKcb` tinyint(1) DEFAULT NULL,
      `isDanger` tinyint(1) DEFAULT NULL,
      `isInnovate` tinyint(1) DEFAULT NULL,
      `isBasic` tinyint(1) DEFAULT NULL,
      `isAgreement` tinyint(1) DEFAULT NULL,
      `isMarketMaking` tinyint(1) DEFAULT NULL,
      `isBidding` tinyint(1) DEFAULT NULL,
      `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      UNIQUE KEY `stocks_unique_index_stockcode` (`stockCode`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `StockKLineDailies` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `stockCode` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `date` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `openingPrice` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `closingPrice` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `highestPrice` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `lowestPrice` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `tradingVolume` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `turnover` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `amplitude` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `changePercent` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `change` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `turnoverRate` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      UNIQUE KEY `stocks_unique_index_stockcode_date` (`stockCode`,`date`) USING BTREE,
      KEY `stocks_index_date` (`date`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `NetValues` (
	  `id` int(11) NOT NULL AUTO_INCREMENT,
	  `fundCode` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `date` datetime DEFAULT NULL COMMENT '净值日期',
	  `DGR` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '基金日增长率/daily growth rate',
	  `NAVPS` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '基金单位净值/net asset value per share',
	  `ACCNAV` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '基金累计净值/accumulative net asset value',
	  `pStatus` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '申购状态/purchasing status',
	  `rStatus` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '赎回状态/redeming status',
	  `dividend` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分红配送',
      `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	  PRIMARY KEY (`id`),
	  UNIQUE KEY `netvalue_unique_index_fundcode_date` (`fundCode`,`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `AssetSizes` (
	  `id` int(11) NOT NULL AUTO_INCREMENT,
	  `fundCode` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `date` datetime DEFAULT NULL COMMENT '净值日期',
	  `purchaseShares` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '期间申购（亿份）',
	  `redemptionShares` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '期间赎回（亿份）',
	  `totalShares` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '期末总份额（亿份）',
	  `NAV` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '期末净资产（亿元）/net asset value',
	  `NAVROC` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '净资产变动率/net asset value rate of change',
      `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	  PRIMARY KEY (`id`),
	  UNIQUE KEY `assetsize_unique_index_fundcode_date` (`fundCode`,`date`),
	  UNIQUE KEY `idx_fundcode` (`fundCode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `DealRecords` (
	  `id` int(11) NOT NULL AUTO_INCREMENT,
	  `fundCode` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
	  `dealDate` datetime DEFAULT NULL COMMENT '交易日期',
	  `dealType` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易类型, 1:买入;2:卖出',
	  `dealAmount` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '交易金额，单位（元）',
	  -- `createdAt` datetime NOT NULL,
	  -- `updatedAt` datetime NOT NULL,
      `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
	  PRIMARY KEY (`id`),
	  KEY `idx_fundcode` (`fundCode`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `Indices` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `indexCity` enum('sh','sz') COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `isImportant` tinyint(1) DEFAULT '0',
      `indexCode` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `indexName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      UNIQUE KEY `indices_unique_index_indexcode` (`indexCode`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `IndexValueDailies` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `indexCode` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `indexName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `date` datetime DEFAULT NULL,
      `openingPrice` int(11) DEFAULT NULL,
      `closingPrice` int(11) DEFAULT NULL,
      `maximumPrice` int(11) DEFAULT NULL,
      `minimumPrice` int(11) DEFAULT NULL,
      `volume` int(11) DEFAULT NULL,
      `percentChange` int(11) DEFAULT NULL,
      `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `indicesvaluedaily_index_indexcode` (`indexCode`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `IndexValueDetails` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `indexCode` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `indexName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `time` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `price` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `avgPrice` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `highestPrice` int(11) DEFAULT NULL,
      `lowestPrice` int(11) DEFAULT NULL,
      `volume` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `percentChange` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `indicesvaluedetail_index_indexcode` (`indexCode`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE `IndexValueMinutelies` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `indexCode` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `indexName` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `time` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `price` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `avgPrice` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `highestPrice` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `lowestPrice` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `totalPrice` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `volume` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `totalVolume` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `percentChange` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
      `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      PRIMARY KEY (`id`),
      KEY `indicesvalueminutely_index_indexcode` (`indexCode`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


CREATE TABLE IF NOT EXISTS `financial_pbc_balance_sheet` (
      `year` SMALLINT UNSIGNED NOT NULL COMMENT '年份',
      `month` TINYINT UNSIGNED NOT NULL COMMENT '月份',
      `subject_type` TINYINT UNSIGNED NOT NULL COMMENT '科目类型; 1: asset/资产, 2: liability/负债',
      `subject_name` VARCHAR(100) NOT NULL COMMENT '科目名称',
      `value` INT NOT NULL COMMENT '数值大小, 单位(百万元)',
      `created_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `updated_at` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  UNIQUE KEY unique_year_month_subject(`year`, `month`, `subject_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT '央行资产负债表';


CREATE TABLE `MarketValuations` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `changeRate` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
      `closePrice` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
      `tradeMarketCode` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
      `freeShares` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
      `freeMarketCap` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
      `totalShares` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
      `totalMarketCap` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
      `listingOrgNum` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
      `peTtmAvg` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
      `tradeDate` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
      `created_at` datetime NOT NULL,
      `updated_at` datetime NOT NULL,
      PRIMARY KEY (`id`),
      UNIQUE KEY `market_valuation_unique_index_code_date` (`tradeMarketCode`,`tradeDate`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1613 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


-- PankouDatRecords 盘口数据记录表
CREATE TABLE IF NOT EXISTS PankouDatRecords (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    stockCode CHAR(6) NOT NULL COMMENT '股票代码（6位数字）',
    `date` CHAR(8) NOT NULL COMMENT '日期（8位数字YYYYMMDD）',
    downloadPath VARCHAR(512) NOT NULL COMMENT 'dat文件存储路径',
    downloadStatus VARCHAR(255) NOT NULL COMMENT '下载状态: SUCCESS/错误信息',
    datSize INT UNSIGNED DEFAULT 0 COMMENT '下载文件大小（字节）',
    parsedSize INT UNSIGNED DEFAULT NULL COMMENT '从header解析的预期大小',
    datEntropy FLOAT DEFAULT NULL COMMENT '文件熵值（0-8范围）',
    decompressStatus VARCHAR(255) DEFAULT NULL COMMENT '解压状态: SUCCESS/错误信息',
    decompressPath VARCHAR(512) DEFAULT NULL COMMENT '解压后文件路径',
    decompressSize INT UNSIGNED DEFAULT NULL COMMENT '解压后文件大小',
    decompressAlgo ENUM('ZLIB','LZMA_RAW','Unknown') DEFAULT NULL COMMENT '解压算法类型',
    decompressEntropy FLOAT DEFAULT NULL COMMENT '解压后文件熵值',
    MD5 CHAR(32) COMMENT 'MD5校验和',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',  -- 注意这里没有逗号
    UNIQUE KEY uniq_stock_date (stockCode, `date`),
    INDEX idx_date (`date`),
    INDEX idx_status (downloadStatus(20))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='盘口数据下载记录表';

-- FenshiDatRecords 分时数据记录表
CREATE TABLE IF NOT EXISTS FenshiDatRecords (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    stockCode CHAR(6) NOT NULL COMMENT '股票代码（6位数字）',
    `date` CHAR(8) NOT NULL COMMENT '日期（8位数字YYYYMMDD）',
    downloadPath VARCHAR(512) NOT NULL COMMENT 'dat文件存储路径',
    downloadStatus VARCHAR(255) NOT NULL COMMENT '下载状态: SUCCESS/错误信息',
    datSize INT UNSIGNED DEFAULT 0 COMMENT '下载文件大小（字节）',
    parsedSize INT UNSIGNED DEFAULT NULL COMMENT '从header解析的预期大小',
    datEntropy FLOAT DEFAULT NULL COMMENT '文件熵值（0-8范围）',
    decompressStatus VARCHAR(255) DEFAULT NULL COMMENT '解压状态: SUCCESS/错误信息',
    decompressPath VARCHAR(512) DEFAULT NULL COMMENT '解压后文件路径',
    decompressSize INT UNSIGNED DEFAULT NULL COMMENT '解压后文件大小',
    decompressAlgo ENUM('ZLIB','LZMA_RAW','Unknown') DEFAULT NULL COMMENT '解压算法类型',
    decompressEntropy FLOAT DEFAULT NULL COMMENT '解压后文件熵值',
    MD5 CHAR(32) COMMENT 'MD5校验和',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',  -- 注意这里没有逗号
    UNIQUE KEY uniq_stock_date (stockCode, `date`),
    INDEX idx_date (`date`),
    INDEX idx_status (downloadStatus(20))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分时数据下载记录表';
