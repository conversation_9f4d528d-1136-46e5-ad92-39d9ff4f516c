CREATE TABLE field (
  id int(11) NOT NULL AUTO_INCREMENT,
  name varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  description text COLLATE utf8mb4_unicode_ci,
  depends varchar(1024) COMMENT '依赖的领域id列表',
  parent_id int(11) COMMENT '允许大领域划分成小领域组成',
  key_parts text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '每个领域聚焦的核心问题列表',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  PRIMARY KEY(id),
  UNIQUE KEY field_unique_name (name) USING BTREE
)
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT "领域表";


CREATE TABLE knowledge (
  id int(11) NOT NULL AUTO_INCREMENT,
  field_id int(11) NOT NULL COMMENT '一个知识点有且仅有属于一个领域',
  content text COLLATE utf8mb4_unicode_ci NOT NULL,
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  PRIMARY KEY(id),
  FOREIGN KEY(field_id) REFERENCES field(id)
)
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT "知识点表";


CREATE TABLE course (
  id int(11) NOT NULL AUTO_INCREMENT,
  name varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  source varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '大学院系、讲座、或者其他网络来源',
  description text COLLATE utf8mb4_unicode_ci,
  key_parts text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '每个课程聚焦的核心问题列表',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  PRIMARY KEY(id)
)
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT "课程表";


CREATE TABLE course_fragment (
  id int(11) NOT NULL AUTO_INCREMENT,
  course_id int(11) NOT NULL COMMENT '多个课程片段组合成一个课程',
  knowledge_id int(11) NOT NULL COMMENT '一个课程片段应该就是套着一个知识点的外壳',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  PRIMARY KEY(id),
  FOREIGN KEY(course_id) REFERENCES course(id),
  FOREIGN KEY(knowledge_id) REFERENCES knowledge(id)
)
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT "课程片段表";


