CREATE TABLE notes (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  title VARCHAR(200) NOT NULL COMMENT '笔记标题',
  content TEXT NOT NULL COMMENT '笔记内容',
  note_type ENUM('normal', 'timeline') DEFAULT 'normal' COMMENT '笔记类型：normal-普通笔记，timeline-时间线笔记',
  category VARCHAR(100) COMMENT '笔记分类',
  tags VARCHAR(500) COMMENT '笔记标签，多个标签用逗号分隔',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleted_at TIMESTAMP NULL,
  INDEX idx_category (category),
  INDEX idx_created_at (created_at),
  INDEX idx_title (title),
  INDEX idx_note_type (note_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='笔记表';
