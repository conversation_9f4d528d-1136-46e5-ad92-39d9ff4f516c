CREATE TABLE sync_record_deprecated (
  -- id int(11) NOT NULL AUTO_INCREMENT,
  id VARCHAR(36) NOT NULL,
  sync_mode enum('full', 'update') NOT NULL COMMENT '同步记录的模式',
  db_name varchar(64) NOT NULL COMMENT '同步的db名',
  src_host varchar(64) NOT NULL COMMENT '同步的db名',
  src_port varchar(64) NOT NULL COMMENT '同步的db名',
  target_host varchar(64) NOT NULL COMMENT '同步的db名',
  target_port varchar(64) NOT NULL COMMENT '同步的db名',
  src_install_mode varchar(64) NOT NULL COMMENT 'src的TREETHEME_INSTALL_MODE',
  target_install_mode varchar(64) NOT NULL COMMENT 'target的TREETHEME_INSTALL_MODE',
  status varchar(64) DEFAULT NULL COMMENT '最终状态, success/confict，为空表示还未更新结果或者异常中断了',
  sync_num int(11) DEFAULT NULL COMMENT '同步了多少条记录',
  sync_num_detail text DEFAULT NULL COMMENT '每个表分别同步了多少条记录',
  base_time DATETIME DEFAULT NULL COMMENT '基于前面的那个记录时间作为基点进行同步, full mode时为NULL',
  base_id VARCHAR(36) DEFAULT NULL COMMENT '基于前面的那个记录id作为基点进行同步, full mode时为NULL',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  PRIMARY KEY(id)
)
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT "数据同步记录表";

CREATE TABLE sync_record (
  -- id int(11) NOT NULL AUTO_INCREMENT,
  id VARCHAR(36) NOT NULL,
  sync_mode enum('full', 'update') NOT NULL COMMENT '同步记录的模式',
  db_name varchar(64) NOT NULL COMMENT '同步的db名',
  src_install_mode varchar(64) NOT NULL COMMENT 'src的TREETHEME_INSTALL_MODE',
  target_install_mode varchar(64) NOT NULL COMMENT 'target的TREETHEME_INSTALL_MODE',
  status varchar(64) DEFAULT NULL COMMENT '最终状态, success/confict，为空表示还未更新结果或者异常中断了',
  sync_num int(11) DEFAULT NULL COMMENT '同步了多少条记录',
  sync_num_detail text DEFAULT NULL COMMENT '每个表分别同步了多少条记录',
  base_time DATETIME DEFAULT NULL COMMENT '基于前面的那个记录时间作为基点进行同步, full mode时为NULL',
  base_id VARCHAR(36) DEFAULT NULL COMMENT '基于前面的那个记录id作为基点进行同步, full mode时为NULL',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  PRIMARY KEY(id)
)
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT "数据同步记录表";

DELIMITER ;;
CREATE TRIGGER `sync_record_before_insert`
BEFORE INSERT ON `sync_record` FOR EACH ROW
BEGIN
  IF new.id IS NULL THEN
    SET new.id = uuid();
  END IF;
END;;
DELIMITER ;
