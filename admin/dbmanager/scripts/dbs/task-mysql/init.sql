CREATE TABLE categories (
  id int(11) NOT NULL,
  name varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL,
  createTime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updateTime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleteTime TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE tasks (
  id varchar(64) NOT NULL,
  parent varchar(64),
  title text COLLATE utf8mb4_unicode_ci NOT NULL,
  label varchar(64) NULL DEFAULT NULL,
  isMeta tinyint,
  level int(11) NOT NULL,
  categoryID int(11) NULL,
  # status enum('待启动', '进行中', '阻塞中', '挂起中', '已完成', '已删除', '未知'),
  status varchar(50),
  description text COLLATE utf8mb4_unicode_ci,
  # priority enum('0', '1', '2'),
  priority tinyint,
  quadrant int(11),
  ddl TIMESTAMP NULL DEFAULT NULL,
  createTime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updateTime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleteTime TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (id),
  UNIQUE (label),
  CONSTRAINT fk_tb_task1 FOREIGN KEY (categoryID) REFERENCES categories (id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE pickups (
  id int(11) NOT NULL AUTO_INCREMENT,
  taskID varchar(64) NOT NULL,
  position int(11) NOT NULL,
  createTime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updateTime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleteTime TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (id),
  # UNIQUE (position), 加了唯一键，会导致重排序更新position时有冲突情况需要额外解决
  CONSTRAINT fk_tb_pickup1 FOREIGN KEY (taskID) REFERENCES tasks (id) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE marks (
  id int(11) NOT NULL AUTO_INCREMENT,
  taskID varchar(64) NOT NULL,
  # type enum('心情随想', '感悟心得', '评论备注', '流水记事', '学习笔记'),
  type varchar(32),
  docLink varchar(1024) COMMENT '关联富文本文档链接',
  content longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  createTime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updateTime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleteTime TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fk_tb_mark1 FOREIGN KEY (taskID) REFERENCES tasks (id) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

CREATE TABLE timelines (
  id int(11) NOT NULL AUTO_INCREMENT,
  taskID varchar(64) NOT NULL,
  startTime TIMESTAMP NULL DEFAULT NULL,
  endTime TIMESTAMP NULL DEFAULT NULL,
  coverTaskID varchar(64) NULL DEFAULT NULL,
  createTime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updateTime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleteTime TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (id),
  CONSTRAINT fk_tb_timeline1 FOREIGN KEY (taskID) REFERENCES tasks (id) ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
# 不知道为什么，下面会报错关于约束的错误
# ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

# 不知道为什么在dbeaver不加反引号，会报语法错
CREATE TABLE `references` (
  id int(11) NOT NULL AUTO_INCREMENT,
  refType varchar(64) NOT NULL,
  srcID varchar(64) NOT NULL,
  destID varchar(64) NOT NULL,
  createTime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updateTime TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  deleteTime TIMESTAMP NULL DEFAULT NULL,
  PRIMARY KEY (id),
  UNIQUE KEY type_src_dest (refType, srcID, destID)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
