CREATE TABLE tencent_asr_task (
  id int(11) NOT NULL AUTO_INCREMENT,
  video varchar(255) NOT NULL COMMENT '标识原始视频id、信息的字段，后续跟其他服务打通统一规范后，换成其他video_id字段，目前格式比较灵活',
  task_id int(11) COMMENT '腾讯任务id',
  result longtext COLLATE utf8mb4_unicode_ci COMMENT '翻译结果',
  result_detail longtext COLLATE utf8mb4_unicode_ci COMMENT '接口返回字段',
  audio_duration int(11) COMMENT '接口返回字段',
  request_id varchar(128) COLLATE utf8mb4_unicode_ci COMMENT '接口返回字段',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  PRIMARY KEY(id),
  UNIQUE KEY field_unique_video (video) USING BTREE
)
ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
COMMENT "腾讯asr服务的任务记录";
