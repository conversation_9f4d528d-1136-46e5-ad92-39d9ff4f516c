#!/bin/bash

# docker安装移到项目treetheme里面初始化了。并且不把普通用户权限加到docker group
## 安装docker，直接参照https://docs.docker.com/engine/install/ubuntu/
#apt-get remove docker docker-engine docker.io containerd runc
#apt-get update
#apt-get install -y \
#	apt-transport-https \
#	ca-certificates \
#	curl \
#	gnupg \
#	lsb-release
#curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
#echo \
#	"deb [arch=amd64 signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu \
#	$(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
#
#apt-get update
#apt-get install -y docker-ce docker-ce-cli containerd.io
#
#sudo usermod -aG docker $USER && newgrp docker
