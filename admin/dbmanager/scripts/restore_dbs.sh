#!/bin/bash
CUR_DIR=$(dirname $(readlink -f $0))
BACKUP_PATH=/data/db-backups

function restore_db {
	restore_container_name=dbrestore
	container_name=$1
	source ${CUR_DIR}/dbs/${container_name}/.env
	backup_path=${BACKUP_PATH}/${container_name}

	restore_sql_file=$2

	cp ${CUR_DIR}/dbs/${container_name}/.env ${backup_path}/.env
	cp ${CUR_DIR}/run-in-containers/restore.sh ${backup_path}/restore.sh

	# 启动一个mysql容器，为了使用mysql客户端来执行命令，否则无法确定宿主机有安装mysqldump，或者版本一致
	docker run --name ${restore_container_name} --restart unless-stopped -v ${backup_path}:/backups --network="host" -e MYSQL_ROOT_PASSWORD=$(uuidgen) -d mysql:5.7
	docker container exec ${restore_container_name} bash /backups/restore.sh ${restore_sql_file}
	docker container rm -f ${restore_container_name}
}

# TODO restore具体文件参数应该有一个选择来源
restore_db accounts-mysql "db_accountbook_backup_2021_11_27_08_58_59_UTC.sql"
