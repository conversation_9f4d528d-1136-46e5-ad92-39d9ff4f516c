#!/bin/bash
CUR_DIR=$(dirname $(readlink -f $0))

echo "USE mysql;" > /docker-entrypoint-initdb.d/timezones.sql &&  mysql_tzinfo_to_sql /usr/share/zoneinfo >> /docker-entrypoint-initdb.d/timezones.sql
echo "Asia/Hong_Kong" > /etc/timezone
ln -s /usr/share/zoneinfo/Asia/Hong_Kong /etc/localtime

source ${CUR_DIR}/.env
echo "Install database and user"
# 参考：https://www.cnblogs.com/bruce1992/p/13945087.html
# 新版的mysql，这个语句会报错：GRANT ALL ON ${db_name}.* TO '${db_user}'@'%' IDENTIFIED BY '${db_pass}';
# 改成: GRANT ALL ON ${db_name}.* TO '${db_user}'@'%' WITH GRANT OPTION;
MYSQL_PWD=${db_root_pass} mysql --host=${db_host} --port=${db_port} -uroot -e "
  USE mysql;
  SET GLOBAL time_zone = '+8:00';
  CREATE USER '${db_user}' IDENTIFIED BY '${db_pass}';
  CREATE DATABASE IF NOT EXISTS ${db_name} default charset utf8 COLLATE utf8_unicode_ci;
  GRANT ALL ON ${db_name}.* TO '${db_user}'@'%' WITH GRANT OPTION;
  FLUSH PRIVILEGES;
"

MYSQL_PWD=${db_pass} mysql -h ${db_host} --port=${db_port} -u${db_user} -D${db_name} < ${CUR_DIR}/init.sql
