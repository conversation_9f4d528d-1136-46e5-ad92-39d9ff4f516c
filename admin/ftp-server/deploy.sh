#!/bin/bash

cur_dir=$(dirname $(readlink -f $0))
container_name=sony-ftpd
ftpuser=ticftp
ftppass=fTp86642!au?K
path=/data/docker-volumes/${container_name}

# 创建必要的目录结构
mkdir -p ${path}/home/<USER>
mkdir -p ${path}/etc/vsftpd
mkdir -p ${path}/var/log/vsftpd

# 创建自定义的 vsftpd.conf 配置文件
cp ${cur_dir}/vsftpd.conf ${path}/etc/vsftpd/

# 清理旧容器
docker container rm -f ${container_name}

# 启动新容器
docker run -d --net admin-net \
  --name=${container_name} \
  -p 52020:20 \
  -p 52021:21 \
  -p 52098-52099:52098-52099 \
  -e FTP_USER=$ftpuser \
  -e FTP_PASS=$ftppass \
  -e PASV_MIN_PORT=52098 \
  -e PASV_MAX_PORT=52099 \
  -v ${path}/home/<USER>/home/<USER>
  -v ${path}/etc/vsftpd/vsftpd.conf:/etc/vsftpd/vsftpd.conf \
  -v ${path}/var/log/vsftpd:/var/log/vsftpd \
  --restart unless-stopped \
  fauria/vsftpd
docker logs -f ${container_name}
