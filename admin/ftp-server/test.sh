#!/bin/bash
set -x

# FTP服务器配置
# FTP_SERVER="127.0.0.1"
FTP_SERVER="luzeshu.cn"
FTP_PORT="52021"
FTP_USER="ticuser"
FTP_PASS="ticpass"
REMOTE_DIR="/"
LOCAL_FILE_LIST="ftp_files.txt"

# 清理旧文件
rm -f "$LOCAL_FILE_LIST"

# 修复点1：使用正确的端口连接语法
{
ftp -n -v <<EOF
open $FTP_SERVER $FTP_PORT
user $FTP_USER $FTP_PASS
binary
cd "$REMOTE_DIR"
ls
quit
EOF
} > "$LOCAL_FILE_LIST" 2>&1

# 修复点2：优化日志过滤（合并sed命令）
sed -i -e '/^ftp>/d' \
       -e '/^[0-9][0-9][0-9]/d' \
       -e '/^Remote system/d' \
       -e '/^Using binary/d' \
       -e '/^Connection established/d' \
       "$LOCAL_FILE_LIST"

# 检查结果
if [ -s "$LOCAL_FILE_LIST" ]; then
    echo "✅ 文件列表获取成功！"
    echo "📋 文件列表（共 $(wc -l < "$LOCAL_FILE_LIST") 个文件）："
    cat "$LOCAL_FILE_LIST"
else
    echo "❌ 获取失败！可能原因："
    echo "1. 端口 $FTP_PORT 未开放"
    echo "2. 认证失败（用户名/密码错误）"
    echo "3. 目录 $REMOTE_DIR 不存在"
    rm -f "$LOCAL_FILE_LIST"
    exit 1
fi
