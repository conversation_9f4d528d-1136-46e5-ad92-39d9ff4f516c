FROM debian:bullseye
COPY sources.list /etc/apt/sources.list

RUN cat /etc/resolv.conf && apt-get update && apt-get install -y git procps curl openssh-server default-mysql-client iputils-ping nmap rsync net-tools telnet

COPY jumpserver.pub /root/.ssh/id_rsa.pub
COPY jumpserver /root/.ssh/id_rsa
COPY jumpserver.authorized_keys /root/.ssh/authorized_keys

COPY ssh_host_dsa_key /etc/ssh/ssh_host_dsa_key
COPY ssh_host_dsa_key.pub /etc/ssh/ssh_host_dsa_key.pub
COPY ssh_host_rsa_key /etc/ssh/ssh_host_rsa_key
COPY ssh_host_rsa_key.pub /etc/ssh/ssh_host_rsa_key.pub
COPY ssh_host_ecdsa_key /etc/ssh/ssh_host_ecdsa_key
COPY ssh_host_ecdsa_key.pub /etc/ssh/ssh_host_ecdsa_key.pub
COPY ssh_host_ed25519_key /etc/ssh/ssh_host_ed25519_key
COPY ssh_host_ed25519_key.pub /etc/ssh/ssh_host_ed25519_key.pub
# 用&& 避免多个RUN导致无效的层次太多

RUN chmod 600 /etc/ssh/ssh_host_rsa_key
RUN chmod 600 /etc/ssh/ssh_host_ecdsa_key
RUN chmod 600 /etc/ssh/ssh_host_ed25519_key
RUN mkdir /run/sshd
# CMD [ "ssh-keyscan *********** >> /root/.ssh/known_hosts" ]
CMD [ "/usr/sbin/sshd", "-D" ]
# CMD [ "sleep", "500000" ]
