#!/bin/bash
# set -x
cur_dir=$(dirname $(readlink -f $0))
image_version_file="${cur_dir}/.version"
if [[ ! -f ${image_version_file} ]]; then
  echo "Not Found .version File"
  exit
fi
image_version=$(UpdateVersionPatch ${image_version_file})
echo "image_version: ${image_version}"

chmod 600 ${cur_dir}/jumpserver_client

image_name=jump-server:${image_version}
container_name=jump-server

# docker build -t ${image_name} -f ${cur_dir}/image/Dockerfile ${cur_dir}/image/
docker build --no-cache -t ${image_name} -f ${cur_dir}/image/Dockerfile ${cur_dir}/image/
docker image ls -a

path=/data/docker-volumes/${container_name}
if [[ -d ${path} ]]; then
  echo "Already Exist Path: ${path}"
else
  mkdir -p ${path}/workdir
fi

if [[ $(docker ps -a | grep ${container_name} | wc -l) -gt 0 ]]; then
    # rm -rf ${path}
    docker container rm -f ${container_name}
fi

docker run -d --restart unless-stopped --net admin-net \
  -e LANG="C.UTF-8" \
  -e LANGUAGE="C.UTF-8" \
  -e LC_ALL="C.UTF-8" \
  --name=${container_name} \
  -p 127.0.0.1:2222:22 \
  -v /data/docker-volumes/public_root:/public_root \
  -v ${path}/workdir:/workdir ${image_name}
docker container ls -a

if [[ $(docker ps -a | grep ${container_name} | wc -l) -gt 0 ]]; then
  echo "Create Container Success/TREETHEME_DO_CMD=${TREETHEME_DO_CMD}"
  if [[ ${TREETHEME_DO_CMD} == "" ]]; then
    # 通过DoCmd的时候，不进入interactive shell
    docker exec -it ${container_name} bash
  fi
else
  echo "Create Container Failed"
fi
