server {
    listen 443 ssl;
    proxy_set_header X-Real-IP $remote_addr;

    ssl_certificate /etc/nginx/cert/luzeshu.cn/fullchain.pem;
    ssl_certificate_key /etc/nginx/cert/luzeshu.cn/privkey.pem;

	server_name luzeshu.cn;
	# root /var/www/luzeshu.cn;
	# # location / {
    # #   proxy_pass http://gitea:3000;
	# # }
    # renew时避免直接location /，会覆盖root，导致生成证书的临时路径无法访问
	# location /api {
    #   proxy_pass http://gateway:16135;
	# }

	location /api/drive/upload {
      proxy_pass http://gateway:16135;
      # 允许上传大文件 (20G)
      client_max_body_size 20G;
      # 增加超时时间
      proxy_read_timeout 300s;
      proxy_send_timeout 300s;
	}

	# 分片上传相关路由
	location /api/drive/init-chunk-upload {
      proxy_pass http://gateway:16135;
      # 允许大文件信息
      client_max_body_size 1m;
      proxy_read_timeout 60s;
      proxy_send_timeout 60s;
	}

	location /api/drive/upload-chunk {
      proxy_pass http://gateway:16135;
      # 允许大分片 (10M)
      client_max_body_size 10M;
      proxy_read_timeout 120s;
      proxy_send_timeout 120s;
	}

	location /api/drive/complete-chunk-upload {
      proxy_pass http://gateway:16135;
      # 完成上传请求
      client_max_body_size 1m;
      proxy_read_timeout 60s;
      proxy_send_timeout 60s;
	}

	# WebSocket上传进度支持
	location /api/drive/upload-progress {
      proxy_pass http://gateway:16135;
      # WebSocket 协议支持
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "upgrade";
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;

      # 延长超时时间
      proxy_read_timeout 3600s;
      proxy_send_timeout 3600s;
	}

    # 还是直接全部交由网关处理根路由，证书临时路径问题等加入cronjob功能后再考虑一下怎么处理。现在一直有人尝试各种访问，避免nginx有已知漏洞被利用
	location /api {
      proxy_pass http://gateway:16135;
    }

    # # open-webui访问deepseek
	# location /apps/ {
    #   proxy_pass http://web-proxy:8080/; # 末尾加上斜杠，在web-proxy里面接收的请求去掉/apps
	# }

	location / {
      proxy_pass http://blog-service:7799;
	}
}

server {
    listen 443 ssl;
    proxy_set_header X-Real-IP $remote_addr;

    ssl_certificate /etc/nginx/cert/local.luzeshu.cn/fullchain.pem;
    ssl_certificate_key /etc/nginx/cert/local.luzeshu.cn/privkey.pem;

	server_name local.luzeshu.cn;


	location /api/drive/upload {
      proxy_pass http://gateway:16135;
      # 允许上传大文件 (20G)
      client_max_body_size 20G;
      # 增加超时时间
      proxy_read_timeout 300s;
      proxy_send_timeout 300s;
	}

	# 分片上传相关路由
	location /api/drive/init-chunk-upload {
      proxy_pass http://gateway:16135;
      # 允许大文件信息
      client_max_body_size 1m;
      proxy_read_timeout 60s;
      proxy_send_timeout 60s;
	}

	location /api/drive/upload-chunk {
      proxy_pass http://gateway:16135;
      # 允许大分片 (10M)
      client_max_body_size 10M;
      proxy_read_timeout 120s;
      proxy_send_timeout 120s;
	}

	location /api/drive/complete-chunk-upload {
      proxy_pass http://gateway:16135;
      # 完成上传请求
      client_max_body_size 1m;
      proxy_read_timeout 60s;
      proxy_send_timeout 60s;
	}

	# WebSocket上传进度支持
	location /api/drive/upload-progress {
      proxy_pass http://gateway:16135;
      # WebSocket 协议支持
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "upgrade";
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Proto $scheme;

      # 延长超时时间
      proxy_read_timeout 3600s;
      proxy_send_timeout 3600s;
	}

    # 还是直接全部交由网关处理根路由，证书临时路径问题等加入cronjob功能后再考虑一下怎么处理。现在一直有人尝试各种访问，避免nginx有已知漏洞被利用
	location /api {
      proxy_pass http://gateway:16135;
    }

	location / {
      proxy_pass http://blog-service:7799;
	}
}

server {
    listen 443 ssl;
    proxy_set_header X-Real-IP $remote_addr;

    ssl_certificate /etc/nginx/cert/ai.luzeshu.cn/fullchain.pem;
    ssl_certificate_key /etc/nginx/cert/ai.luzeshu.cn/privkey.pem;

	server_name ai.luzeshu.cn;
	
    # open-webui访问deepseek
	location / {
      proxy_pass http://web-proxy:8080/; # 末尾加上斜杠，在web-proxy里面接收的请求去掉/apps
      # WebSocket 协议支持
      proxy_http_version 1.1;
      proxy_set_header Upgrade $http_upgrade;
      proxy_set_header Connection "upgrade";
      proxy_set_header Host $host;
      
      # 可选：延长超时时间
      proxy_read_timeout 3600s;
      proxy_send_timeout 3600s;
	}
}



# 80端口页面重定向
server {
    listen 80;
    listen [::]:80;
    proxy_set_header X-Real-IP $remote_addr;

    server_name luzeshu.cn;
    rewrite ^(.*) https://$host$1 permanent;
}

# 80端口页面重定向
server {
    listen 80;
    listen [::]:80;
    proxy_set_header X-Real-IP $remote_addr;

    server_name local.luzeshu.cn;
    rewrite ^(.*) https://$host$1 permanent;
}

# 80端口页面重定向
server {
    listen 80;
    listen [::]:80;
    proxy_set_header X-Real-IP $remote_addr;

    server_name ai.luzeshu.cn;
    rewrite ^(.*) https://$host$1 permanent;
}

# 80端口页面重定向
server {
    listen 80;
    listen [::]:80;
    proxy_set_header X-Real-IP $remote_addr;

    server_name think.luzeshu.cn;
    rewrite ^(.*) https://$host$1 permanent;
}

# 内部容器 -> 内部接口调用
server {
    listen 80;
    listen [::]:80;
    proxy_set_header X-Real-IP $remote_addr;

	fastcgi_read_timeout 365d;
	proxy_read_timeout 365d;
	proxy_connect_timeout 365d;
	proxy_send_timeout 365d;

    server_name nginx-admin;
	location /inner_api {
      proxy_pass http://gateway:16135;
      # 允许上传大文件 (20GB)
      client_max_body_size 20G;
      # 增加超时时间
      proxy_read_timeout 300s;
      proxy_send_timeout 300s;
	}
}
