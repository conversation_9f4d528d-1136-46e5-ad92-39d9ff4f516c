# 80端口页面重定向
server {
    listen 80;
    listen [::]:80;
    proxy_set_header X-Real-IP $remote_addr;

    server_name luzeshu.cn;
	root /var/www/luzeshu.cn;
}

# 80端口页面重定向
server {
    listen 80;
    listen [::]:80;
    proxy_set_header X-Real-IP $remote_addr;

    server_name ai.luzeshu.cn;
	root /var/www/ai.luzeshu.cn;
}

# 80端口页面重定向
server {
    listen 80;
    listen [::]:80;
    proxy_set_header X-Real-IP $remote_addr;

    server_name think.luzeshu.cn;
	root /var/www/think.luzeshu.cn;
}
