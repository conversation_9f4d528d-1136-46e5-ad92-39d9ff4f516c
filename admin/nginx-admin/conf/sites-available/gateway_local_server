server {
    listen 443 ssl;
    proxy_set_header X-Real-IP $remote_addr;

    ssl_certificate /etc/nginx/cert/local.luzeshu.cn/fullchain.pem;
    ssl_certificate_key /etc/nginx/cert/local.luzeshu.cn/privkey.pem;

	server_name local.luzeshu.cn;
    # 还是直接全部交由网关处理根路由，证书临时路径问题等加入cronjob功能后再考虑一下怎么处理。现在一直有人尝试各种访问，避免nginx有已知漏洞被利用
	location /api {
      proxy_pass http://gateway:16135;
	}

	location / {
      proxy_pass http://blog-service:7799;
	}
}

# 80端口页面重定向
server {
    listen 80;
    listen [::]:80;
    proxy_set_header X-Real-IP $remote_addr;

    server_name local.luzeshu.cn;
    rewrite ^(.*) https://$host$1 permanent;
}

# 内部容器 -> 内部接口调用
server {
    listen 80;
    listen [::]:80;
    proxy_set_header X-Real-IP $remote_addr;

	fastcgi_read_timeout 365d;
	proxy_read_timeout 365d;
	proxy_connect_timeout 365d;
	proxy_send_timeout 365d;

    server_name nginx-admin;
	location /inner_api {
      proxy_pass http://gateway:16135;
	}
}
