server {
    listen 443 ssl;
    proxy_set_header X-Real-IP $remote_addr;

    ssl_certificate /etc/nginx/cert/git.luzeshu.cn/fullchain.pem;
    ssl_certificate_key /etc/nginx/cert/git.luzeshu.cn/privkey.pem;

	server_name git.luzeshu.cn;
	# root /var/www/git.luzeshu.cn;
	# # location / {
    # #   proxy_pass http://gitea:3000;
	# # }
    # 避免直接location /，会覆盖root，导致生成证书的临时路径无法访问
	# location /ping {
    #   proxy_pass http://gateway:16135;
	# }

    # 还是直接全部交由网关处理根路由，证书临时路径问题等加入cronjob功能后再考虑一下怎么处理。现在一直有人尝试各种访问，避免nginx有已知漏洞被利用
	location / {
      proxy_pass http://gateway:16135;
	}
}

# 80端口页面重定向
server {
    listen 80;
    listen [::]:80;
    proxy_set_header X-Real-IP $remote_addr;

    server_name git.luzeshu.cn git.luzeshu.cn;
    rewrite ^(.*) https://$host$1 permanent;
}
