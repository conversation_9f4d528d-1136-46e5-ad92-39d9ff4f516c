#!/bin/bash
cur_dir=$(dirname $(readlink -f $0))
root_dir=$(dirname ${cur_dir})

image_version_file="${root_dir}/.version"
if [[ ! -f ${image_version_file} ]]; then
  echo "Not Found .version File"
  exit
fi
image_version=$(UpdateVersionPatch ${image_version_file})

image_name=nginx-admin:${image_version}
container_name=nginx-admin

# docker build -t ${image_name} -f ${root_dir}/image/Dockerfile ${root_dir}/image/
docker build --no-cache -t ${image_name} -f ${root_dir}/image/Dockerfile ${root_dir}/image/
# docker image ls -a

path=/data/docker-volumes/${container_name}
if [[ -d ${path} ]]; then
  echo "Already Exist Path: ${path}"
else
  mkdir -p ${path}
fi

# 清除旧的残留文件
rm -rf ${path}/etc/nginx
mkdir -p ${path}/etc/nginx
cp -R ${root_dir}/conf/* ${path}/etc/nginx/
mkdir -p ${path}/etc/nginx/cert

function copyCert {
  local domain=$1
  cert_path="/data/docker-volumes/nginx-admin/etc/nginx/cert/$domain"
  src_cert_path=/etc/letsencrypt/live/$domain
  if [[ -d ${src_cert_path} ]]; then
    cp -RL ${src_cert_path} $cert_path
    echo "cp -RL ${src_cert_path} $cert_path success"
  else
    echo "Not Found ${src_cert_path}, Using backup copy: ${root_dir}/certs/${domain}"
    # 没有letsencrypt实际签名的证书时，用备用固定文件copy，让nginx先跑起来先。
    cp -RL ${root_dir}/certs/${domain}  $cert_path
    echo "cp -RL ${root_dir}/certs/${domain} $cert_path"
  fi
}


# if [[ ${TREETHEME_INSTALL_MODE} == "cloud_server" ]]; then
  mkdir -p ${path}/var/www/git.luzeshu.cn
  echo "Hello git" > ${path}/var/www/git.luzeshu.cn/index.html

  mkdir -p ${path}/var/www/local.luzeshu.cn
  echo "Hello local" > ${path}/var/www/local.luzeshu.cn/index.html
  copyCert "local.luzeshu.cn"

  mkdir -p ${path}/var/www/luzeshu.cn
  echo "Hello empty" > ${path}/var/www/luzeshu.cn/index.html
  copyCert "luzeshu.cn"

  mkdir -p ${path}/var/www/ai.luzeshu.cn
  echo "Hello ai" > ${path}/var/www/ai.luzeshu.cn/index.html
  copyCert "ai.luzeshu.cn"

  mkdir -p ${path}/var/www/think.luzeshu.cn
  echo "Hello think" > ${path}/var/www/think.luzeshu.cn/index.html
  copyCert "think.luzeshu.cn"

  rm ${path}/etc/nginx/sites-enabled/gateway_local_server
  rm ${path}/etc/nginx/sites-enabled/default_local_server
# elif [[ ${TREETHEME_INSTALL_MODE} == "local_server" ]]; then
#   # mkdir -p ${path}/var/www/git.luzeshu.cn
#   # echo "Hello git" > ${path}/var/www/git.luzeshu.cn/index.html
#   mkdir -p ${path}/var/www/local.luzeshu.cn
#   echo "Hello empty" > ${path}/var/www/local.luzeshu.cn/index.html
#   copyCert "local.luzeshu.cn"
# 
#   rm ${path}/etc/nginx/sites-enabled/gateway
#   rm ${path}/etc/nginx/sites-enabled/default
# fi

if [[ $(docker ps -a | grep ${container_name} | wc -l) -gt 0 ]]; then
    docker container rm -f ${container_name}
fi

# 参考https://stackoverflow.com/questions/38576337/how-to-execute-a-bash-command-only-if-a-docker-container-with-a-given-name-does
if [[ ! "$(docker ps -a -q -f name=${container_name} | grep -w ${container_name})" ]]; then
  docker run -d --restart unless-stopped --net admin-net \
    --name=${container_name} \
    -p 80:80 \
    -p 443:443 \
    -p 2013:2013 \
    -v ${path}/var/www:/var/www \
    -v ${path}/var/log/nginx:/var/log/nginx \
    -v ${path}/etc/nginx/nginx.conf:/etc/nginx/nginx.conf \
    -v ${path}/etc/nginx/cert:/etc/nginx/cert \
    -v ${path}/etc/nginx/sites-enabled:/etc/nginx/sites-enabled \
    -v ${path}/etc/nginx/sites-available:/etc/nginx/sites-available \
    ${image_name}
  echo "waiting for ${container_name} start..."
  check_tag="ready for start up"
  check_log=$(docker logs ${container_name} 2>&1 | grep "${check_tag}")
  while [[ ${check_log} == "" ]];do
    sleep 0.5
    check_log=$(docker logs ${container_name} 2>&1 | grep "${check_tag}")
  done
  echo "${container_name} start success"
fi

# docker container ls -a
