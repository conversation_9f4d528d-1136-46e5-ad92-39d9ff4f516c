#!/bin/bash
cur_dir=$(dirname $(readlink -f $0))
image_name=nginx
container_name=nginx
docker container rm -f nginx

# 参考https://stackoverflow.com/questions/30543409/how-to-check-if-a-docker-image-with-a-specific-tag-exist-locally
if [[ "$(docker images -q ${image_name} 2> /dev/null)" == "" ]]; then
  docker pull ${image_name}
fi
path=/data/docker-volumes/${container_name}
if [[ -d ${path} ]]; then
  echo "Already Exist Path: ${path}"
else
  mkdir -p ${path}
fi
mkdir -p ${path}/etc/nginx
cp -R ${cur_dir}/conf/* ${path}/etc/nginx/
mkdir -p ${path}/etc/nginx/cert
mkdir -p ${path}/var/www/git.luzeshu.cn
echo "Hello git" > ${path}/var/www/git.luzeshu.cn/index.html
mkdir -p ${path}/var/www/luzeshu.cn
echo "Hello empty" > ${path}/var/www/luzeshu.cn/index.html

# 参考https://stackoverflow.com/questions/38576337/how-to-execute-a-bash-command-only-if-a-docker-container-with-a-given-name-does
if [[ ! "$(docker ps -a -q -f name=${container_name} | grep -w ${container_name})" ]]; then
  docker run -d --restart unless-stopped --net admin-net --name=${container_name} \
	-p 80:80 \
	-p 443:443 \
    -p 2013:2013 \
	-v ${path}/var/www:/var/www \
    -v ${path}/var/log/nginx:/var/log/nginx \
	-v ${path}/etc/nginx/nginx.conf:/etc/nginx/nginx.conf \
    -v ${path}/etc/nginx/cert:/etc/nginx/cert \
	-v ${path}/etc/nginx/sites-enabled:/etc/nginx/sites-enabled \
    -v ${path}/etc/nginx/sites-available:/etc/nginx/sites-available \
	${image_name}
  echo "waiting for ${container_name} start..."
  check_tag="ready for start up"
  check_log=$(docker logs ${container_name} 2>&1 | grep "${check_tag}")
  while [[ ${check_log} == "" ]];do
    sleep 0.5
    check_log=$(docker logs ${container_name} 2>&1 | grep "${check_tag}")
  done
  echo "${container_name} start success"
fi

docker container ls -a
