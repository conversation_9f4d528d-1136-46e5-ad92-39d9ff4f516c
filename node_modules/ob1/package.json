{"name": "ob1", "version": "0.82.5", "description": "A small library for working with 0- and 1-based offsets in a type-checked way.", "main": "src/ob1.js", "exports": {".": "./src/ob1.js", "./package.json": "./package.json", "./private/*": "./src/*.js", "./src/*.js": "./src/*.js", "./src/*": "./src/*.js"}, "repository": {"type": "git", "url": "**************:facebook/metro.git"}, "scripts": {"prepare-release": "test -d build && rm -rf src.real && mv src src.real && mv build src", "cleanup-release": "test ! -e build && mv src build && mv src.real src"}, "keywords": ["metro"], "license": "MIT", "dependencies": {"flow-enums-runtime": "^0.0.6"}, "engines": {"node": ">=18.18"}}