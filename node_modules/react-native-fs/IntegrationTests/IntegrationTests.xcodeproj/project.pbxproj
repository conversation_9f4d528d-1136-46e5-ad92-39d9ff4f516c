// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		004D28A31AAF61C70097A701 /* IntegrationTestsTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 004D28A21AAF61C70097A701 /* IntegrationTestsTests.m */; };
		13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.m */; };
		13B07FBD1A68108700A75B9A /* LaunchScreen.xib in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB11A68108700A75B9A /* LaunchScreen.xib */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		F24706C11B1F865C00001A84 /* libRCTTest.a in Frameworks */ = {isa = PBXBuildFile; fileRef = F24706C01B1F863700001A84 /* libRCTTest.a */; };
		F24706C21B1F865C00001A84 /* libReact.a in Frameworks */ = {isa = PBXBuildFile; fileRef = F24706BA1B1F85BD00001A84 /* libReact.a */; };
		F24706CF1B1F898A00001A84 /* libRCTText.a in Frameworks */ = {isa = PBXBuildFile; fileRef = F24706CE1B1F896000001A84 /* libRCTText.a */; };
		F24706D01B1F898A00001A84 /* libRCTWebSocket.a in Frameworks */ = {isa = PBXBuildFile; fileRef = F24706C81B1F87A800001A84 /* libRCTWebSocket.a */; };
		F24706F81B1FC0DB00001A84 /* libRCTNetwork.a in Frameworks */ = {isa = PBXBuildFile; fileRef = F24706F71B1FC0C300001A84 /* libRCTNetwork.a */; };
		F24707431B1FC8E000001A84 /* libRNFS.a in Frameworks */ = {isa = PBXBuildFile; fileRef = F24707421B1FC8D200001A84 /* libRNFS.a */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		58005BCB1ABA44F10062E044 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = IntegrationTests;
		};
		F24706B91B1F85BD00001A84 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F24706B51B1F85BD00001A84 /* React.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 83CBBA2E1A601D0E00E9B192;
			remoteInfo = React;
		};
		F24706BF1B1F863700001A84 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F24706BB1B1F863700001A84 /* RCTTest.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 580C376F1AB104AF0015E709;
			remoteInfo = RCTTest;
		};
		F24706C71B1F87A800001A84 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F24706C31B1F87A800001A84 /* RCTWebSocket.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3C86DF461ADF2C930047B81A;
			remoteInfo = RCTWebSocket;
		};
		F24706CD1B1F896000001A84 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F24706C91B1F895F00001A84 /* RCTText.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 58B5119B1A9E6C1200147676;
			remoteInfo = RCTText;
		};
		F24706F61B1FC0C300001A84 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F24706F21B1FC0C300001A84 /* RCTNetwork.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 58B511DB1A9E6C8500147676;
			remoteInfo = RCTNetwork;
		};
		F24707411B1FC8D200001A84 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F24707381B1FC8D200001A84 /* RNFS.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = F12AFB9B1ADAF8F800E0535D;
			remoteInfo = RNFS;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		004D289E1AAF61C70097A701 /* IntegrationTestsTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = IntegrationTestsTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		004D28A11AAF61C70097A701 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		004D28A21AAF61C70097A701 /* IntegrationTestsTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = IntegrationTestsTests.m; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* IntegrationTests.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = IntegrationTests.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		13B07FB21A68108700A75B9A /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.xib; name = Base; path = Base.lproj/LaunchScreen.xib; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		F24706B51B1F85BD00001A84 /* React.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = React.xcodeproj; path = "../node_modules/react-native/React/React.xcodeproj"; sourceTree = "<group>"; };
		F24706BB1B1F863700001A84 /* RCTTest.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTTest.xcodeproj; path = "../node_modules/react-native/Libraries/RCTTest/RCTTest.xcodeproj"; sourceTree = "<group>"; };
		F24706C31B1F87A800001A84 /* RCTWebSocket.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTWebSocket.xcodeproj; path = "../node_modules/react-native/Libraries/WebSocket/RCTWebSocket.xcodeproj"; sourceTree = "<group>"; };
		F24706C91B1F895F00001A84 /* RCTText.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTText.xcodeproj; path = "../node_modules/react-native/Libraries/Text/RCTText.xcodeproj"; sourceTree = "<group>"; };
		F24706F21B1FC0C300001A84 /* RCTNetwork.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RCTNetwork.xcodeproj; path = "../node_modules/react-native/Libraries/Network/RCTNetwork.xcodeproj"; sourceTree = "<group>"; };
		F24707381B1FC8D200001A84 /* RNFS.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RNFS.xcodeproj; path = ../RNFS.xcodeproj; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		004D289B1AAF61C70097A701 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F24707431B1FC8E000001A84 /* libRNFS.a in Frameworks */,
				F24706F81B1FC0DB00001A84 /* libRCTNetwork.a in Frameworks */,
				F24706CF1B1F898A00001A84 /* libRCTText.a in Frameworks */,
				F24706D01B1F898A00001A84 /* libRCTWebSocket.a in Frameworks */,
				F24706C11B1F865C00001A84 /* libRCTTest.a in Frameworks */,
				F24706C21B1F865C00001A84 /* libReact.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		004D289F1AAF61C70097A701 /* IntegrationTestsTests */ = {
			isa = PBXGroup;
			children = (
				004D28A21AAF61C70097A701 /* IntegrationTestsTests.m */,
				004D28A01AAF61C70097A701 /* Supporting Files */,
			);
			path = IntegrationTestsTests;
			sourceTree = "<group>";
		};
		004D28A01AAF61C70097A701 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				004D28A11AAF61C70097A701 /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		1316A21D1AA397F400C0188E /* Libraries */ = {
			isa = PBXGroup;
			children = (
				F24707381B1FC8D200001A84 /* RNFS.xcodeproj */,
				F24706F21B1FC0C300001A84 /* RCTNetwork.xcodeproj */,
				F24706C91B1F895F00001A84 /* RCTText.xcodeproj */,
				F24706C31B1F87A800001A84 /* RCTWebSocket.xcodeproj */,
				F24706BB1B1F863700001A84 /* RCTTest.xcodeproj */,
				F24706B51B1F85BD00001A84 /* React.xcodeproj */,
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* IntegrationTests */ = {
			isa = PBXGroup;
			children = (
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.m */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				13B07FB11A68108700A75B9A /* LaunchScreen.xib */,
				13B07FB71A68108700A75B9A /* main.m */,
			);
			name = IntegrationTests;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				13B07FAE1A68108700A75B9A /* IntegrationTests */,
				1316A21D1AA397F400C0188E /* Libraries */,
				004D289F1AAF61C70097A701 /* IntegrationTestsTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* IntegrationTests.app */,
				004D289E1AAF61C70097A701 /* IntegrationTestsTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F24706B61B1F85BD00001A84 /* Products */ = {
			isa = PBXGroup;
			children = (
				F24706BA1B1F85BD00001A84 /* libReact.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F24706BC1B1F863700001A84 /* Products */ = {
			isa = PBXGroup;
			children = (
				F24706C01B1F863700001A84 /* libRCTTest.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F24706C41B1F87A800001A84 /* Products */ = {
			isa = PBXGroup;
			children = (
				F24706C81B1F87A800001A84 /* libRCTWebSocket.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F24706CA1B1F895F00001A84 /* Products */ = {
			isa = PBXGroup;
			children = (
				F24706CE1B1F896000001A84 /* libRCTText.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F24706F31B1FC0C300001A84 /* Products */ = {
			isa = PBXGroup;
			children = (
				F24706F71B1FC0C300001A84 /* libRCTNetwork.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F24707391B1FC8D200001A84 /* Products */ = {
			isa = PBXGroup;
			children = (
				F24707421B1FC8D200001A84 /* libRNFS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		004D289D1AAF61C70097A701 /* IntegrationTestsTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 004D28AD1AAF61C70097A701 /* Build configuration list for PBXNativeTarget "IntegrationTestsTests" */;
			buildPhases = (
				004D289A1AAF61C70097A701 /* Sources */,
				004D289B1AAF61C70097A701 /* Frameworks */,
				004D289C1AAF61C70097A701 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				58005BCC1ABA44F10062E044 /* PBXTargetDependency */,
			);
			name = IntegrationTestsTests;
			productName = IntegrationTestsTests;
			productReference = 004D289E1AAF61C70097A701 /* IntegrationTestsTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* IntegrationTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "IntegrationTests" */;
			buildPhases = (
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = IntegrationTests;
			productName = "Hello World";
			productReference = 13B07F961A680F5B00A75B9A /* IntegrationTests.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0610;
				ORGANIZATIONNAME = Facebook;
				TargetAttributes = {
					004D289D1AAF61C70097A701 = {
						CreatedOnToolsVersion = 6.1.1;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "IntegrationTests" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = F24706F31B1FC0C300001A84 /* Products */;
					ProjectRef = F24706F21B1FC0C300001A84 /* RCTNetwork.xcodeproj */;
				},
				{
					ProductGroup = F24706BC1B1F863700001A84 /* Products */;
					ProjectRef = F24706BB1B1F863700001A84 /* RCTTest.xcodeproj */;
				},
				{
					ProductGroup = F24706CA1B1F895F00001A84 /* Products */;
					ProjectRef = F24706C91B1F895F00001A84 /* RCTText.xcodeproj */;
				},
				{
					ProductGroup = F24706C41B1F87A800001A84 /* Products */;
					ProjectRef = F24706C31B1F87A800001A84 /* RCTWebSocket.xcodeproj */;
				},
				{
					ProductGroup = F24706B61B1F85BD00001A84 /* Products */;
					ProjectRef = F24706B51B1F85BD00001A84 /* React.xcodeproj */;
				},
				{
					ProductGroup = F24707391B1FC8D200001A84 /* Products */;
					ProjectRef = F24707381B1FC8D200001A84 /* RNFS.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* IntegrationTests */,
				004D289D1AAF61C70097A701 /* IntegrationTestsTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		F24706BA1B1F85BD00001A84 /* libReact.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libReact.a;
			remoteRef = F24706B91B1F85BD00001A84 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		F24706C01B1F863700001A84 /* libRCTTest.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTTest.a;
			remoteRef = F24706BF1B1F863700001A84 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		F24706C81B1F87A800001A84 /* libRCTWebSocket.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTWebSocket.a;
			remoteRef = F24706C71B1F87A800001A84 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		F24706CE1B1F896000001A84 /* libRCTText.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTText.a;
			remoteRef = F24706CD1B1F896000001A84 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		F24706F71B1FC0C300001A84 /* libRCTNetwork.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRCTNetwork.a;
			remoteRef = F24706F61B1FC0C300001A84 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		F24707421B1FC8D200001A84 /* libRNFS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRNFS.a;
			remoteRef = F24707411B1FC8D200001A84 /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		004D289C1AAF61C70097A701 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
				13B07FBD1A68108700A75B9A /* LaunchScreen.xib in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		004D289A1AAF61C70097A701 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				004D28A31AAF61C70097A701 /* IntegrationTestsTests.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.m in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		58005BCC1ABA44F10062E044 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* IntegrationTests */;
			targetProxy = 58005BCB1ABA44F10062E044 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		13B07FB11A68108700A75B9A /* LaunchScreen.xib */ = {
			isa = PBXVariantGroup;
			children = (
				13B07FB21A68108700A75B9A /* Base */,
			);
			name = LaunchScreen.xib;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		004D28A61AAF61C70097A701 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(SDKROOT)/Developer/Library/Frameworks",
					"$(DEVELOPER_FRAMEWORKS_DIR)",
				);
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"FB_REFERENCE_IMAGE_DIR=\"\\\"$(SOURCE_ROOT)/$(PROJECT_NAME)Tests/ReferenceImages\\\"\"",
					"$(inherited)",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../node_modules/react-native/React/**",
				);
				INFOPLIST_FILE = IntegrationTestsTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 8.1;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/IntegrationTests.app/IntegrationTests";
			};
			name = Debug;
		};
		004D28A71AAF61C70097A701 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(SDKROOT)/Developer/Library/Frameworks",
					"$(DEVELOPER_FRAMEWORKS_DIR)",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../node_modules/react-native/React/**",
				);
				INFOPLIST_FILE = IntegrationTestsTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 8.1;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks @loader_path/Frameworks";
				PRODUCT_NAME = "$(TARGET_NAME)";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/IntegrationTests.app/IntegrationTests";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../node_modules/react-native/React/**",
					"$(SRCROOT)/../node_modules/react-native/Libraries",
				);
				INFOPLIST_FILE = "$(SRCROOT)/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = IntegrationTests;
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../node_modules/react-native/React/**",
					"$(SRCROOT)/../node_modules/react-native/Libraries",
				);
				INFOPLIST_FILE = "$(SRCROOT)/Info.plist";
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = IntegrationTests;
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../React/**",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++0x";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../React/**",
				);
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		004D28AD1AAF61C70097A701 /* Build configuration list for PBXNativeTarget "IntegrationTestsTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				004D28A61AAF61C70097A701 /* Debug */,
				004D28A71AAF61C70097A701 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "IntegrationTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "IntegrationTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
