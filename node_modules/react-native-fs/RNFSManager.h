//
//  RNFSManager.h
//  RNFSManager
//
//  Created by <PERSON> on 08/05/15.
//  Copyright (c) 2015 <PERSON>. All rights reserved.
//

#import <React/RCTBridgeModule.h>
#import <React/RCTEventEmitter.h>
#import <React/RCTLog.h>

typedef void (^CompletionHandler)(void);

@interface RNFSManager : RCTEventEmitter <RCTBridgeModule>

+(void)setCompletionHandlerForIdentifier: (NSString *)identifier completionHandler: (CompletionHandler)completionHandler;

@end
