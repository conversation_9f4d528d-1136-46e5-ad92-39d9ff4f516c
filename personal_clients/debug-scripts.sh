#!/bin/bash

echo "=== React Native 调试脚本 ==="
echo "请选择调试方式："
echo "1. 详细模式运行 Android"
echo "2. 查看 React Native 日志"
echo "3. 查看所有设备日志"
echo "4. 查看 Metro 错误日志"
echo "5. 查看 Gradle 构建日志"
echo "6. 清理并重新构建"
echo "7. 查看连接的设备"

read -p "请输入选项 (1-7): " choice

case $choice in
  1)
    echo "运行详细模式 Android..."
    npx react-native run-android --verbose
    ;;
  2)
    echo "显示 React Native 相关日志..."
    adb logcat | grep -E "(ReactNative|JS|Bundle|Error|Exception|com.thinkingandroid)" --color=always
    ;;
  3)
    echo "显示所有设备日志..."
    adb logcat
    ;;
  4)
    echo "显示 Metro 错误日志..."
    npx react-native start --verbose
    ;;
  5)
    echo "运行 Gradle 构建并显示详细日志..."
    cd android && ./gradlew assembleDebug --info
    ;;
  6)
    echo "清理并重新构建..."
    npx react-native clean
    cd android && ./gradlew clean
    cd ..
    npx react-native run-android --verbose
    ;;
  7)
    echo "连接的设备列表："
    adb devices -l
    ;;
  *)
    echo "无效选项"
    ;;
esac 