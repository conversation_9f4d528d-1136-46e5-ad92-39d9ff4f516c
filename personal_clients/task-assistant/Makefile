
SHELL:=/bin/bash

.PHONY: build

init:
	cargo init commands

build:
	cd commands && cargo build
	ln -sf commands/target/debug/commands ./ktask

run:
	cd commands && cargo run

install: bash_complete
	cp commands/target/debug/commands /data/deploy/treetheme/config_tools/ktask

bash_complete:
	su -m root -s /bin/bash -c "./ktask generate > /usr/share/bash-completion/completions/ktask"
	# 无法对外部environment生效，还是要外部手动执行。
	# source /usr/share/bash-completion/completions/ktask

# all: build install run
all: build install bash_complete
