# KTask CLI - 任务管理命令行工具

KTask CLI是一个用Rust编写的任务管理命令行工具。

## 功能特性

- 任务生命周期管理：创建、编辑、删除、移动任务
- 层级任务结构：支持父子任务关系
- 标记系统：comment、thought、diary、note、gains
- 搜索功能：通过关键字搜索任务和标记
- 时间追踪：记录任务时间投入

## 安装

```bash
cd commands
cargo build --release
sudo cp target/release/ktask /usr/local/bin/
```

## 环境配置

```bash
export KTASK_HOST="http://127.0.0.1:16135"
export KTASK="your-auth-token"
```

## 使用示例

### 任务管理
```bash
# 创建任务
ktask add "学习Rust" -p root

# 查看任务
ktask list
ktask info task-id

# 编辑任务
ktask edit task-id --title "新标题"

# 任务状态
ktask start task-id
ktask finish task-id
```

### 搜索功能
```bash
# 搜索任务
ktask search "关键字"

# 搜索标记
ktask search "关键字" --in-marks
ktask search "关键字" --in-marks --mark-type comment
```

### 标记系统
```bash
# 添加各种标记
ktask mark-comment "评论内容" --task-id task-id
ktask mark-thought "思考内容" --task-id task-id
ktask mark-note "笔记内容" --task-id task-id
ktask mark-gains "收获内容" --task-id task-id
```

## 架构

- `main.rs`: 命令行入口
- `task_cli.rs`: CLI命令定义  
- `http_db.rs`: HTTP客户端
- `data_source.rs`: 数据访问接口
- `terminal_view.rs`: 终端显示

## 开发

```bash
cargo run -- list
cargo test
cargo clippy
```