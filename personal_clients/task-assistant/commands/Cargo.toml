[package]
name = "commands"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
clap = {version = "3.1.18", features = ["derive", "env"]}
clap_complete = "3.1.4"
serde = {version = "1.0.137", features = ["derive", "rc"]}
serde_json = "1.0.81"
serde_with = { version = "1.5.1", features = ["json"]}
colored = "2.0.0"
lazy_static = "1.4.0"
chrono = "0.4.23"
reqwest = { version = "0.11", features = ["json", "blocking"] }
tokio = { version = "1", features = ["full"] }
url = { version = "2", features = ["serde"] }
# derivative = "2.2.0"
nix = "0.17.0"
libc = "0.2"
