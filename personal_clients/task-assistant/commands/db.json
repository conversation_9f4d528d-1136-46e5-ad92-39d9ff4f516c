{"doingTask": "1012", "tasks": [{"tid": "1", "parent": null, "title": "bytedance", "category": "", "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 0, "marks": [], "timeline": []}, {"tid": "11", "parent": "1", "title": "技术需求", "category": "", "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 1, "marks": [], "timeline": []}, {"tid": "1101", "parent": "11", "title": "搜索优化", "category": "OKR", "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "110101", "parent": "1101", "title": "搜索优化-埋点开发联调", "category": null, "status": "阻塞中", "description": "", "priority": 1, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "11010101", "parent": "110101", "title": "代码开发", "category": null, "status": "已完成", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "11010102", "parent": "110101", "title": "前端联调", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "11010103", "parent": "110101", "title": "数仓联调", "category": null, "status": "已完成", "description": "", "priority": 3, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "11010104", "parent": "110101", "title": "网关配置&发布", "category": null, "status": "已完成", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "11010105", "parent": "110101", "title": "补充单测&发起MR", "category": null, "status": "已完成", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "11010106", "parent": "110101", "title": "看板验证", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "11010107", "parent": "110101", "title": "待确认：打点统计跟流量地图数据不一致", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "11010108", "parent": "110101", "title": "补充埋点上线 & 找秉东加上线字段 & 验证tea看板是否ok", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "11010109", "parent": "110101", "title": "埋点数据模板次数有点问题，待定位", "category": null, "status": "挂起中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "110102", "parent": "1101", "title": "输出搜索优化方案", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "11010201", "parent": "110102", "title": "结合现有看板（把握现有数据情况和指标状况） + 迭代埋点需求（整理方案内详细埋点需求）", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [{"time": "2023-01-12 18:00:00", "content": "一下午都在整理埋点需求，详细追溯了数据集相关的dorado任务，确保每个字段含义理解清楚。", "gains": null}], "timeline": [["2023-01-12 14:00:00", "2023-01-12 18:00:00"]]}, {"tid": "11010202", "parent": "110102", "title": "年前先跟产品对齐这个事项，看是不是交为产品需求(陆妍)", "category": null, "status": "已完成", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [{"time": "2023-01-18 17:35:00", "content": "持续对齐&输出会议结论", "gains": null}], "timeline": [["2023-01-18 17:00:00", "2023-01-18 17:20:00"], ["2023-01-18 17:20:00", "2023-01-18 17:35:00"]]}, {"tid": "11010203", "parent": "110102", "title": "完善搜索优化方案，尤其是埋点+还原用户真实意图", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [{"time": "2023-01-29 21:42:20", "content": "明天就要算正式启动120401事项，今晚还是得先把搜索优化的思路弄好", "gains": null}, {"time": "2023-01-29 23:06:16", "content": "刚打算搞没一会，时光叫回去了，然后跟他沟通了一下，发现自己又吭哧吭哧花那么多时间搞偏了。还是得多跟他沟通。切忌排斥沟通。另外方文说高优处理降噪，也是非重点方向。第三点，还是自己的事件重点错了。第四点，自己也要会判断", "gains": true}], "timeline": [["2023-01-28 16:00:54", "2023-01-28 18:00:00"], ["2023-01-29 11:38:44", "2023-01-29 11:38:44"], ["2023-01-29 11:50:58", "2023-01-29 11:50:58"], ["2023-01-29 14:25:44", "2023-01-29 14:25:44"], ["2023-01-29 14:34:45", "2023-01-29 14:55:00"], ["2023-01-29 17:25:34", "2023-01-29 18:00:00"], ["2023-01-29 21:37:03", "2023-01-29 22:10:00"]]}, {"tid": "1102", "parent": "11", "title": "数据工程-评测数据集+离线验证", "category": "OKR", "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "110201", "parent": "1102", "title": "跟方文聊清楚细节", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "110202", "parent": "1102", "title": "整理预期收益&同步到周会文档", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "1103", "parent": "11", "title": "[Q4]一分钟定位搜索推荐路径", "category": "OKR", "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1104", "parent": "11", "title": "[Q4]性能优化专项", "category": "OKR", "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "110401", "parent": "1104", "title": "待回收数据", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "1105", "parent": "11", "title": "[Q4]推荐策略聚合&系统列表重构", "category": "OKR", "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "110501", "parent": "1105", "title": "上线后接口性能待定位(进坤反馈)", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "1106", "parent": "11", "title": "推荐中台:推荐策略接入优化二期", "category": "OKR", "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "110601", "parent": "1106", "title": "整理推荐中台遗留事项", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "1107", "parent": "11", "title": "系统模板接口升级V2版本", "category": "OKR", "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "110701", "parent": "1107", "title": "对齐v2接口切换，前端&移动端人力", "category": null, "status": "挂起中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "11070101", "parent": "110701", "title": "时光安全周会文档：让同步节奏", "category": null, "status": "挂起中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "12", "parent": "1", "title": "产品需求", "category": "", "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 1, "marks": [], "timeline": []}, {"tid": "1201", "parent": "12", "title": "bitable分类tab推荐实验", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "120101", "parent": "1201", "title": "数据回收与分析", "category": null, "status": "已完成", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "120102", "parent": "1201", "title": "待讨论分析方式与下一步是否继续", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "1202", "parent": "12", "title": "doc模板个性化推荐", "category": "OKR", "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1203", "parent": "12", "title": "批量把bitable应用放工作台上(郑雨婷)", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "120301", "parent": "1203", "title": "对齐需求会议", "category": null, "status": "已完成", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "120302", "parent": "1203", "title": "待捋清楚功能细节和链路", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "1204", "parent": "12", "title": "支持指定模板置顶(关安頔)", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1205", "parent": "12", "title": "搜索优化-优先展示主推模板(关安頔)", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1206", "parent": "12", "title": "模板创建后默认开启automation(关安頔)", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1207", "parent": "12", "title": "模板库路径推荐首屏增加bitable模板(关安頔)", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1208", "parent": "12", "title": "bitable模板特征解析:表格数、视图类型、字段等(关安頔)", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "120401", "parent": "1204", "title": "对齐推荐排序规则", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "120402", "parent": "1204", "title": "一起PRD评审(陈泽彬,韦秉东,关安頔)", "category": null, "status": "已完成", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-01-18 16:00:00", "2023-01-18 16:45:00"]]}, {"tid": "120403", "parent": "1204", "title": "确认global模式数据", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-02-07 17:33:07", "2023-02-07 17:33:07"]]}, {"tid": "120404", "parent": "1204", "title": "确认人力&输出排期（该事项比较高优）", "category": null, "status": "已完成", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "1209", "parent": "12", "title": "单模板应用预置到工作台(马瑞雪)", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "120901", "parent": "1209", "title": "初步技术调研会议", "category": null, "status": "已完成", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "120902", "parent": "1209", "title": "待捋清楚功能细节和链路", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "1210", "parent": "12", "title": "【bitable】bitable首页优化，与入口建设工作协同，提升用户创建和规模(马瑞雪)", "category": null, "status": "已删除", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1211", "parent": "12", "title": "【bitable】支持好mkt和商业化团队的模板需求，帮助客户场景落地(陆妍)", "category": null, "status": "已删除", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1212", "parent": "12", "title": "企业模板下线(陆妍)", "category": null, "status": "已删除", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1213", "parent": "12", "title": "[DocX增长实验] Space首页前置新建空白文档和模板入口", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "13", "parent": "1", "title": "交接模板", "category": "", "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 1, "marks": [], "timeline": []}, {"tid": "1301", "parent": "13", "title": "跟进：authz支持模板服务(万军,锦锦)", "category": "", "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1302", "parent": "13", "title": "跟进：模板民生私有化(进坤,高杨)", "category": "", "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1303", "parent": "13", "title": "跟进：企业模板下线(黄万军,陆妍)", "category": "", "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1304", "parent": "13", "title": "跟进：批量群禁言sheet模板(黄万军,阮锦锦)", "category": "", "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1305", "parent": "13", "title": "跟进：日志组件治理和规范(刘进坤)", "category": "", "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1306", "parent": "13", "title": "跟进：[Q4]MG场景下与我共享关系&用户使用历史推荐读本地GA(陈锐)", "category": "", "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1307", "parent": "13", "title": "跟进：支持跨MG使用模板创建副本", "category": "", "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1308", "parent": "13", "title": "画板模板库(葛思磊,王晨,刘思远)", "category": "", "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "130801", "parent": "1308", "title": "对齐是否交接", "category": "", "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "1309", "parent": "13", "title": "飞书和lark模板的批量跟新(阮锦锦,黄万军)", "category": "", "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "130901", "parent": "1309", "title": "对齐是否交接", "category": "", "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "1310", "parent": "13", "title": "模板后台:运营后台数据可筛选导出(阮锦锦,黄万军,王晨)", "category": "", "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1311", "parent": "13", "title": "云文档支持条件访问控制能力(高杨,黄万军,陈锐,王晨)", "category": "", "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1312", "parent": "13", "title": "时光comment: 告警治理", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1313", "parent": "13", "title": "陈锐的权限点位改造，到时对齐 & CR", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "1314", "parent": "13", "title": "颖鑫有个MR待确认", "category": null, "status": "已完成", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [{"time": "2023-01-30 16:20:33", "content": "今天状态真的差，几分钟的东西就忘了。", "gains": null}, {"time": "2023-01-31 11:40:02", "content": "跟颖鑫会议对齐", "gains": null}], "timeline": [["2023-01-30 16:01:58", "2023-01-30 16:01:58"], ["2023-01-30 16:10:59", "2023-01-30 16:29:11"], ["2023-01-31 11:39:49", "2023-01-31 11:51:50"]]}, {"tid": "14", "parent": "1", "title": "问题跟进", "category": "", "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 1, "marks": [], "timeline": []}, {"tid": "120201", "parent": "1202", "title": "(bitable分类tab扩量)分析数据报告问题 + check模型分为0时有没有返回", "category": null, "status": "挂起中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "120202", "parent": "1202", "title": "(bitable分类tab扩量 + doc推荐)分析近段时间的模型参数、数据验证", "category": null, "status": "挂起中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "120203", "parent": "1202", "title": "矩阵模型推荐&策略聚合重构上线", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "12020301", "parent": "120203", "title": "旁路验证看板Check问题 & 需不需要修复", "category": null, "status": "已完成", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "12020302", "parent": "120203", "title": "矩阵模型bitable参数回溯7天uv是否ok", "category": null, "status": "挂起中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "12020303", "parent": "120203", "title": "验证上线后功能", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "12020304", "parent": "120203", "title": "关注开启后实验数据", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "12020305", "parent": "120203", "title": "重新评估下tenant_info来自uv记录的影响（之前以为通过rpc直接调用获取）：doc推荐和搜索埋点上报", "category": null, "status": "挂起中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "12020306", "parent": "120203", "title": "代码下掉v2接口fg", "category": null, "status": "挂起中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "42", "parent": "1", "title": "规划与总结", "category": "regular", "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 1, "marks": [], "timeline": []}, {"tid": "4201", "parent": "42", "title": "盘点2022收益 & Q1规划", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "4202", "parent": "42", "title": "完整盘点人力和排期节奏", "category": null, "status": "挂起中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "4203", "parent": "42", "title": "清理meego（曹沙沙拉了一个群也待反馈）", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [{"time": "2023-01-30 17:04:45", "content": "又是各种群聊。还有人咨询染色分流ipv6的问题", "gains": null}, {"time": "2023-01-30 17:05:24", "content": "还有投票团建时间等", "gains": null}, {"time": "2023-01-30 22:11:17", "content": "mess了一下，回过神来着手处理，又发现今天已经加了一个meego任务，把时间临时排满先了。接下来的高优任务应该是主线任务，120405出方案。", "gains": null}], "timeline": [["2023-01-30 16:46:11", "2023-01-30 17:05:32"], ["2023-01-30 17:15:12", "2023-01-30 18:00:00"], ["2023-01-30 21:46:42", "2023-01-30 21:46:42"], ["2023-01-30 22:02:35", "2023-01-30 22:11:28"]]}, {"tid": "4204", "parent": "42", "title": "完成OKR填写", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-01-26 15:10:27", "2023-01-26 15:19:11"]]}, {"tid": "41", "parent": "1", "title": "工作琐碎事项", "category": "regular", "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 1, "marks": [], "timeline": [["2023-01-11 11:00:00", "2023-01-11 12:00:00"]]}, {"tid": "4101", "parent": "41", "title": "开发机环境不可用，推mesh染色分流oncall解决", "category": null, "status": "已完成", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "4102", "parent": "41", "title": "本周完成安全考试", "category": null, "status": "已完成", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-01-28 14:30:00", "2023-01-28 15:03:33"]]}, {"tid": "4103", "parent": "41", "title": "回收百科项数据(峻榕)", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-01-26 15:19:11", "2023-01-26 15:27:27"], ["2023-01-26 15:27:27", "2023-01-28 11:46:26"], ["2023-01-28 14:30:00", "2023-01-28 14:30:00"]]}, {"tid": "43", "parent": "1", "title": "工作固定事项", "category": "regular", "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 1, "marks": [], "timeline": [["2023-01-11 16:00:00", "2023-01-11 17:00:00"], ["2023-01-18 18:00:00", "2023-01-18 19:00:00"]]}, {"tid": "4302", "parent": "43", "title": "提前更新安全周会文档", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-01-18 17:45:00", "2023-01-18 18:00:00"], ["2023-01-26 15:10:07", "2023-01-26 15:10:27"], ["2023-02-01 13:45:39", "2023-02-01 14:07:00"], ["2023-02-01 17:15:00", "2023-02-01 18:00:00"], ["2023-02-08 17:09:00", "2023-02-08 17:09:00"], ["2023-02-08 17:19:45", "2023-02-08 17:19:45"], ["2023-02-08 17:39:51", "2023-02-08 18:01:33"], ["2023-02-15 17:13:38", "2023-02-15 17:23:21"], ["2023-02-15 17:23:21", "2023-02-15 17:39:15"], ["2023-02-22 17:30:02", "2023-02-22 17:59:36"], ["2023-03-01 17:00:00", "2023-03-01 17:11:19"], ["2023-03-08 17:30:00", "2023-03-08 17:45:44"], ["2023-03-15 15:43:22", "2023-03-15 16:03:29"]]}, {"tid": "5", "parent": null, "title": "personal", "category": "", "status": "进行中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 0, "marks": [], "timeline": []}, {"tid": "52", "parent": "5", "title": "个人技术事项", "category": null, "status": "进行中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 1, "marks": [], "timeline": []}, {"tid": "5203", "parent": "52", "title": "博客部署与备案", "category": null, "status": "已完成", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-01-11 20:20:00", "2023-01-11 23:20:00"]]}, {"tid": "5201", "parent": "52", "title": "思考优化工作模式", "category": null, "status": "进行中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "520101", "parent": "5201", "title": "整理当前手中的任务，以及近段时间能回溯的时间段投入事项，顺便梳理task模型及需要的命令功能", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "520102", "parent": "5201", "title": "继续把task模型和工具的功能，整理到方案文档梳理一下，进行初步开发", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [{"time": "2023-01-13 00:55:00", "content": "好困了，先刷牙、晾衣服、睡觉先", "gains": null}, {"time": "2023-01-13 02:12:00", "content": "又继续调了一晚上rust命令，真睡了。", "gains": null}, {"time": "2023-01-13 22:12:00", "content": "按完摩，回到家，继续。明天下午回老家等过年了，今晚抓紧把工具写完。", "gains": null}], "timeline": []}, {"tid": "520103", "parent": "5201", "title": "持续开发task", "category": null, "status": "进行中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 3, "marks": [{"time": "2023-01-28 21:08:51", "content": "测试mark命令", "gains": null}, {"time": "2023-01-28 21:26:08", "content": "完成mark命令", "gains": null}, {"time": "2023-01-28 21:28:55", "content": "test", "gains": null}, {"time": "2023-01-28 23:30:56", "content": "既然基本功能已经大部分可用了，就先不抠各种美化细节。list展示跨度与投入、timelines和marks等等，添加-v、-vvv参数等。但是优先补充一些日常会用到的功能。比如现在先加一下mark指定到某个task_id去，而不是进行中的任务", "gains": null}, {"time": "2023-01-28 23:40:25", "content": "test mark to tid", "gains": null}, {"time": "2023-01-29 00:10:50", "content": "现在完成info命令才展示timelines", "gains": null}, {"time": "2023-01-29 00:14:34", "content": "接下来优化一下--start-time参数的格式灵活支持多种长短方式；然后再优化一下start是指定--force-multi参数，强制多任务并行", "gains": null}, {"time": "2023-01-29 00:16:59", "content": "还是先添加成待办task好了。现在来不及了，而且不是严重影响。有更高优的事情是公司的任务。明天要跟pm开会对排期。搜索优化的方案思路都没捋完，拖太久了。", "gains": null}, {"time": "2023-01-30 15:58:55", "content": "有时突然就临时遇到一些问题，比如现在遇到grep颜色的问题，就卡了一会整理了一个task出来。所以需要一些临时创建任务的同时、自带start？但想想似乎也没必要，现在的方式很灵活了", "gains": null}], "timeline": [["2023-01-28 11:46:26", "2023-01-28 12:00:00"], ["2023-01-28 14:00:00", "2023-01-28 14:30:00"], ["2023-01-28 15:03:33", "2023-01-28 15:18:34"], ["2023-01-28 19:20:57", "2023-01-28 20:10:26"], ["2023-01-28 20:10:49", "2023-01-28 20:22:38"], ["2023-01-28 20:27:57", "2023-01-28 20:29:34"], ["2023-01-28 20:29:34", "2023-01-28 20:30:15"], ["2023-01-28 20:30:15", "2023-01-28 20:30:42"], ["2023-01-28 20:30:42", "2023-01-28 20:44:25"], ["2023-01-28 21:00:06", "2023-01-28 21:29:23"], ["2023-01-28 23:28:28", "2023-01-29 00:25:38"]]}, {"tid": "91", "parent": "5", "title": "生活固定琐事", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 1, "marks": [], "timeline": []}, {"tid": "9101", "parent": "91", "title": "吃饭休息", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [{"time": "2023-01-30 14:22:35", "content": "中午跟冰女吃饭，喝奈雪，聊到后面合租的事情。还要先问问霞什么情况。", "gains": null}, {"time": "2023-01-31 19:19:59", "content": "吃完饭刷了会手机无聊，又看了半集狂飙。。还是先工作吧。", "gains": null}, {"time": "2023-01-31 19:38:18", "content": "mess了一段时间，又想开始去搞task工具。实际上还是混乱所致。否则就不是任由自己随性产生的想法。而是收一下心，高优处理迫在眉睫堆积的一堆事情。", "gains": true}, {"time": "2023-02-02 18:32:49", "content": "傻了。。。特意越来18:00会议，完了在这里玩手机等吃饭。。18:00可以吃了。。", "gains": false}, {"time": "2023-02-06 22:52:00", "content": "吃完晚饭，跟刘雄他们下去走了一圈，说刘雄被转岗到email团队的事情。", "gains": false}, {"time": "2023-02-09 14:33:36", "content": "睡了一觉起来又开始大涨了。然后继续聊了+不想干活。", "gains": false}, {"time": "2023-02-09 19:16:28", "content": "吃完饭帮爸叫了顺风车，明天从增城回碣石。", "gains": false}, {"time": "2023-02-10 14:15:13", "content": "一个中午又被垃圾琐事影响午休都没。垃圾顺风车临时取消，又重新叫了一辆，现在多少都ptsd一样，又怕临时取消。各种担心的情绪导致mess。中午没午休，现在又mess十分钟。还是要调整心态、看淡小事，专心做事。", "gains": false}, {"time": "2023-02-10 14:15:54", "content": "大不了就明天再回去，小事不应该干扰到自己的正常节奏，最小成本去解决。打杯咖啡继续。", "gains": false}, {"time": "2023-02-10 14:39:15", "content": "又玩了一会ChatGPT，现在继续。", "gains": false}, {"time": "2023-02-10 20:39:04", "content": "晚上吃完饭想工作一会，但没午睡实在太困了，熬了一会，七点就走了。", "gains": false}, {"time": "2023-02-14 19:21:06", "content": "又想做financial，又不能宽心，所以又mess一段时间，坐着浪费时间，还不如gohome", "gains": false}, {"time": "2023-02-16 19:47:09", "content": "太困了。", "gains": false}, {"time": "2023-02-18 22:02:35", "content": "吃饭的时候看了一集科幻大鹿的三体电视剧解说。结果一看七集，看到现在。现在继续工作吧。", "gains": false}, {"time": "2023-02-19 11:51:25", "content": "两天中午都点披萨", "gains": false}, {"time": "2023-03-11 23:10:43", "content": "躺着刷了会视频，又开始hxg，然后跟家里视频听说隔音窗的闹了一会，然后帮大伯母挑手机下单，浪费了一晚上。", "gains": false}, {"time": "2023-03-11 23:11:26", "content": "前面是一直躺着看地图，看小区，看路线。今天师礼去看了人才房，对买房和位置又研究了一下。", "gains": false}], "timeline": [["2023-01-12 18:00:00", "2023-01-12 18:40:00"], ["2023-01-28 12:00:00", "2023-01-28 14:00:00"], ["2023-01-28 18:00:00", "2023-01-28 19:20:57"], ["2023-01-28 20:10:26", "2023-01-28 20:10:49"], ["2023-01-29 12:00:56", "2023-01-29 14:25:44"], ["2023-01-29 18:00:00", "2023-01-29 19:25:24"], ["2023-01-29 22:50:00", "2023-01-29 22:50:00"], ["2023-01-30 11:57:00", "2023-01-30 14:23:43"], ["2023-01-30 18:00:00", "2023-01-30 18:50:00"], ["2023-01-31 11:55:00", "2023-01-31 14:00:00"], ["2023-01-31 18:03:29", "2023-01-31 19:39:12"], ["2023-02-01 12:00:00", "2023-02-01 13:43:35"], ["2023-02-01 19:00:00", "2023-02-01 19:24:00"], ["2023-02-02 12:00:54", "2023-02-02 14:14:51"], ["2023-02-02 18:32:20", "2023-02-02 19:58:13"], ["2023-02-04 17:37:27", "2023-02-04 17:37:27"], ["2023-02-06 12:00:00", "2023-02-06 12:57:31"], ["2023-02-06 18:08:59", "2023-02-06 18:55:00"], ["2023-02-07 12:00:00", "2023-02-07 14:10:00"], ["2023-02-07 18:02:41", "2023-02-07 19:02:23"], ["2023-02-08 12:00:00", "2023-02-08 14:01:06"], ["2023-02-08 19:10:00", "2023-02-08 20:39:39"], ["2023-02-09 12:02:26", "2023-02-09 14:34:21"], ["2023-02-09 18:06:43", "2023-02-09 19:16:50"], ["2023-02-10 11:56:41", "2023-02-10 14:39:17"], ["2023-02-10 18:00:00", "2023-02-10 19:10:00"], ["2023-02-13 12:00:00", "2023-02-13 14:26:00"], ["2023-02-13 14:26:00", "2023-02-13 14:26:00"], ["2023-02-13 17:57:26", "2023-02-13 19:43:44"], ["2023-02-14 12:00:00", "2023-02-14 14:10:00"], ["2023-02-14 18:10:00", "2023-02-14 19:21:13"], ["2023-02-15 11:57:00", "2023-02-15 14:09:19"], ["2023-02-15 19:10:00", "2023-02-15 19:43:00"], ["2023-02-16 12:05:00", "2023-02-16 14:17:55"], ["2023-02-16 18:08:16", "2023-02-16 19:30:00"], ["2023-02-17 12:00:00", "2023-02-17 14:00:00"], ["2023-02-17 18:00:00", "2023-02-17 19:00:00"], ["2023-02-18 18:33:00", "2023-02-18 22:02:48"], ["2023-02-19 11:51:18", "2023-02-19 12:34:08"], ["2023-02-19 18:47:09", "2023-02-19 19:41:06"], ["2023-02-20 11:57:00", "2023-02-20 14:23:04"], ["2023-02-20 18:10:00", "2023-02-20 19:31:00"], ["2023-02-21 12:00:00", "2023-02-21 14:19:33"], ["2023-02-21 18:04:42", "2023-02-21 18:38:22"], ["2023-02-22 12:00:00", "2023-02-22 14:30:35"], ["2023-02-22 18:55:00", "2023-02-22 19:36:00"], ["2023-02-23 12:02:17", "2023-02-23 12:42:42"], ["2023-02-24 12:00:00", "2023-02-24 12:41:35"], ["2023-02-24 18:06:20", "2023-02-24 18:40:00"], ["2023-02-26 13:17:13", "2023-02-26 14:05:34"], ["2023-02-27 12:10:30", "2023-02-27 13:17:02"], ["2023-02-27 18:00:00", "2023-02-27 19:19:05"], ["2023-02-28 11:57:00", "2023-02-28 12:50:00"], ["2023-02-28 13:05:21", "2023-02-28 13:07:07"], ["2023-02-28 13:07:18", "2023-02-28 14:11:43"], ["2023-02-28 18:00:28", "2023-02-28 19:30:03"], ["2023-03-01 12:00:00", "2023-03-01 14:19:20"], ["2023-03-01 18:55:00", "2023-03-01 19:25:00"], ["2023-03-02 12:00:00", "2023-03-02 14:08:13"], ["2023-03-02 16:00:00", "2023-03-02 16:00:00"], ["2023-03-02 18:03:11", "2023-03-02 19:00:41"], ["2023-03-03 11:58:13", "2023-03-03 14:34:23"], ["2023-03-03 18:00:00", "2023-03-03 18:40:00"], ["2023-03-04 18:14:02", "2023-03-04 20:15:57"], ["2023-03-06 12:00:00", "2023-03-06 13:15:00"], ["2023-03-06 18:24:03", "2023-03-06 18:52:23"], ["2023-03-07 12:00:00", "2023-03-07 14:14:00"], ["2023-03-07 18:01:45", "2023-03-07 19:25:00"], ["2023-03-08 12:00:47", "2023-03-08 13:00:39"], ["2023-03-08 19:10:00", "2023-03-08 20:25:01"], ["2023-03-09 12:00:00", "2023-03-09 14:10:00"], ["2023-03-09 18:00:00", "2023-03-09 18:40:00"], ["2023-03-10 12:00:00", "2023-03-10 14:10:00"], ["2023-03-10 18:00:00", "2023-03-10 19:15:00"], ["2023-03-11 18:43:06", "2023-03-11 23:14:31"], ["2023-03-12 11:22:56", "2023-03-12 12:59:21"], ["2023-03-12 18:38:33", "2023-03-12 20:50:00"], ["2023-03-13 12:00:00", "2023-03-13 14:11:34"], ["2023-03-13 17:58:00", "2023-03-13 18:28:39"], ["2023-03-14 11:54:00", "2023-03-14 12:33:23"], ["2023-03-14 13:01:17", "2023-03-14 14:07:41"], ["2023-03-14 16:43:32", "2023-03-14 16:43:32"], ["2023-03-14 18:07:59", "2023-03-14 19:14:26"], ["2023-03-15 11:55:00", "2023-03-15 14:28:30"], ["2023-03-15 18:55:00", "2023-03-15 19:25:00"], ["2023-03-16 12:00:00", "2023-03-16 14:27:22"], ["2023-03-16 18:00:00", "2023-03-16 19:35:00"], ["2023-03-16 20:15:00", "2023-03-16 20:15:00"], ["2023-03-17 12:00:00", "2023-03-17 14:07:08"], ["2023-03-17 18:00:00", "2023-03-17 18:35:00"], ["2023-03-18 11:49:47", "2023-03-18 12:26:42"]]}, {"tid": "9102", "parent": "91", "title": "mess", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 1, "createTime": "", "ddl": "", "level": 2, "marks": [{"time": "2023-01-29 14:35:47", "content": "投票去团建，想起彭婷他们同个时间约欧洲小镇。聊了会", "gains": null}, {"time": "2023-01-31 14:40:10", "content": "发现1月工资没有专项扣除问了会；方文有新的事情来，先记一下；颖鑫问了下MR的事情先不下fg了；听到梦元问oncall值班表，又收录了一下常用文档链接；跟学萱建城沟通了下周日烧烤时间", "gains": null}, {"time": "2023-01-31 20:08:11", "content": "又去分心看了下群消息，又刷起了手机。。", "gains": false}, {"time": "2023-02-02 14:33:09", "content": "还是不断关注股票。", "gains": false}, {"time": "2023-02-07 15:11:07", "content": "QA测试评审会议+进坤交接会议两个时间没对上，空了十分钟", "gains": false}, {"time": "2023-02-13 17:14:11", "content": "犯困", "gains": false}, {"time": "2023-02-23 20:28:58", "content": "低效进行110105，喝完酒状态还是不适合做这种高关注的、比对日志细微格式的东西。", "gains": false}, {"time": "2023-02-24 16:08:27", "content": "被办卡中断了一下有点接不上，还有另外群聊yanxiang说，去香港现场办普卡，内地只能办高级卡", "gains": false}], "timeline": [["2023-01-12 14:00:00", "2023-01-12 14:10:00"], ["2023-01-28 15:18:34", "2023-01-28 16:00:54"], ["2023-01-28 23:16:47", "2023-01-28 23:28:28"], ["2023-01-29 11:50:58", "2023-01-29 12:00:56"], ["2023-01-29 14:25:44", "2023-01-29 14:34:45"], ["2023-01-30 11:28:13", "2023-01-30 11:32:58"], ["2023-01-30 11:40:28", "2023-01-30 11:57:00"], ["2023-01-30 14:23:43", "2023-01-30 14:34:35"], ["2023-01-31 14:00:00", "2023-01-31 14:41:39"], ["2023-01-31 16:40:43", "2023-01-31 16:54:51"], ["2023-01-31 19:50:36", "2023-01-31 20:08:17"], ["2023-02-02 14:14:51", "2023-02-02 14:32:38"], ["2023-02-02 17:06:10", "2023-02-02 17:10:29"], ["2023-02-03 11:24:00", "2023-02-03 11:50:29"], ["2023-02-07 15:10:27", "2023-02-07 15:14:17"], ["2023-02-08 17:09:00", "2023-02-08 17:19:45"], ["2023-02-09 14:53:45", "2023-02-09 15:19:00"], ["2023-02-12 01:12:12", "2023-02-12 01:26:45"], ["2023-02-12 13:32:51", "2023-02-12 13:53:33"], ["2023-02-13 16:01:04", "2023-02-13 16:07:19"], ["2023-02-13 17:06:35", "2023-02-13 17:14:21"], ["2023-02-23 20:09:35", "2023-02-23 20:29:20"], ["2023-02-24 15:55:34", "2023-02-24 16:08:41"], ["2023-02-24 16:08:41", "2023-02-24 16:15:50"], ["2023-03-03 14:34:23", "2023-03-03 14:48:05"], ["2023-03-07 17:00:26", "2023-03-07 17:11:20"]]}, {"tid": "92", "parent": "5", "title": "学习计划", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-28 20:04:09", "ddl": "", "level": 1, "marks": [], "timeline": []}, {"tid": "9103", "parent": "91", "title": "上厕所休息", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-28 20:11:42", "ddl": "", "level": 2, "marks": [{"time": "2023-01-31 15:26:20", "content": "吃了下午茶，竟然还因为胡歌微博的一个将字在无聊地找另一种写法。。。不要逃避拖延了，抓紧正事吧。。", "gains": null}, {"time": "2023-02-01 17:01:12", "content": "被咨询了一会bitable模板导出的问题，又群里争辩了一会手续费。", "gains": false}, {"time": "2023-02-23 15:42:33", "content": "中午没睡成。也没喝咖啡，还是困。", "gains": false}], "timeline": [["2023-01-28 20:22:38", "2023-01-28 20:27:57"], ["2023-01-29 21:14:00", "2023-01-29 21:37:03"], ["2023-01-30 15:08:58", "2023-01-30 15:22:04"], ["2023-01-30 17:05:32", "2023-01-30 17:15:12"], ["2023-01-31 15:01:03", "2023-01-31 15:26:54"], ["2023-01-31 16:32:00", "2023-01-31 16:40:43"], ["2023-02-01 16:38:45", "2023-02-01 17:01:17"], ["2023-02-02 15:12:02", "2023-02-02 15:40:00"], ["2023-02-02 16:55:46", "2023-02-02 17:06:10"], ["2023-02-02 17:48:05", "2023-02-02 17:56:06"], ["2023-02-02 18:20:05", "2023-02-02 18:20:05"], ["2023-02-02 18:25:06", "2023-02-02 18:32:20"], ["2023-02-02 21:33:48", "2023-02-02 21:40:00"], ["2023-02-03 11:50:29", "2023-02-03 11:55:00"], ["2023-02-04 15:30:23", "2023-02-04 15:44:32"], ["2023-02-06 14:38:06", "2023-02-06 14:50:00"], ["2023-02-06 15:57:44", "2023-02-06 16:11:24"], ["2023-02-06 17:43:24", "2023-02-06 17:50:00"], ["2023-02-07 20:23:02", "2023-02-07 20:38:00"], ["2023-02-08 14:30:08", "2023-02-08 14:34:00"], ["2023-02-08 20:54:27", "2023-02-08 21:02:00"], ["2023-02-09 11:49:02", "2023-02-09 11:53:34"], ["2023-02-09 15:33:54", "2023-02-09 15:50:09"], ["2023-02-13 17:00:45", "2023-02-13 17:05:35"], ["2023-02-14 16:49:41", "2023-02-14 17:01:09"], ["2023-02-15 16:59:59", "2023-02-15 17:13:38"], ["2023-02-21 17:41:41", "2023-02-21 17:45:22"], ["2023-02-22 15:21:55", "2023-02-22 15:43:18"], ["2023-02-22 17:22:37", "2023-02-22 17:30:02"], ["2023-02-23 15:16:52", "2023-02-23 15:52:25"], ["2023-02-24 15:08:46", "2023-02-24 15:29:00"], ["2023-02-24 16:43:46", "2023-02-24 16:48:14"], ["2023-02-25 21:21:39", "2023-02-25 21:29:04"], ["2023-02-27 14:37:49", "2023-02-27 14:39:00"], ["2023-02-27 15:50:04", "2023-02-27 16:00:09"], ["2023-02-27 21:07:26", "2023-02-27 21:13:26"], ["2023-02-28 17:39:15", "2023-02-28 18:00:28"], ["2023-03-01 16:41:14", "2023-03-01 16:45:00"], ["2023-03-01 17:47:08", "2023-03-01 17:55:55"], ["2023-03-02 15:13:04", "2023-03-02 15:34:24"], ["2023-03-02 17:56:05", "2023-03-02 18:03:11"], ["2023-03-02 21:18:10", "2023-03-02 21:25:00"], ["2023-03-05 16:18:10", "2023-03-05 16:48:46"], ["2023-03-05 21:23:03", "2023-03-05 21:33:13"], ["2023-03-07 16:14:39", "2023-03-07 16:21:34"], ["2023-03-07 17:11:20", "2023-03-07 17:24:00"], ["2023-03-09 16:30:51", "2023-03-09 16:36:49"], ["2023-03-09 17:21:33", "2023-03-09 17:37:41"], ["2023-03-13 17:47:09", "2023-03-13 17:55:00"], ["2023-03-14 15:22:31", "2023-03-14 15:37:04"], ["2023-03-14 17:41:06", "2023-03-14 17:46:34"], ["2023-03-14 20:09:55", "2023-03-14 20:49:49"], ["2023-03-15 15:25:00", "2023-03-15 15:43:22"], ["2023-03-15 17:28:53", "2023-03-15 17:35:00"], ["2023-03-16 16:05:01", "2023-03-16 16:15:50"], ["2023-03-16 20:15:00", "2023-03-16 20:25:26"], ["2023-03-17 15:17:00", "2023-03-17 15:38:00"]]}, {"tid": "9104", "parent": "91", "title": "回家", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-28 20:12:27", "ddl": "", "level": 2, "marks": [{"time": "2023-01-29 23:08:22", "content": "时光说要去宵夜，不过都关门了，本来打算去他家那边，迟疑了下说改变吧。顺便去买了眼药水。", "gains": null}, {"time": "2023-01-30 22:42:55", "content": "今天状态不好，晚上打车还用了自费的。。垃圾高德体验越来越差。", "gains": null}, {"time": "2023-02-02 22:11:11", "content": "垃圾高德实在不想吐槽了。又是叫个车等了快一个钟。还好没下去。第1位排了都有十几分钟。垃圾中的垃圾，下去坐班车了。", "gains": false}, {"time": "2023-02-06 22:54:08", "content": "帮把叫顺风车去增城。经常为这种小事有负面情绪。虽然最后又便宜了差不多100块钱。一路上刚好到家的时候把事情谈妥了。", "gains": false}, {"time": "2023-03-07 22:46:51", "content": "等车等了二十多分钟。。", "gains": false}, {"time": "2023-03-14 22:14:35", "content": "昨天电子锁换电池没锁紧，今天回来不亮了。。折腾了好一会发现用充电宝插可以先用，跟少优拿了个充电宝进来了。", "gains": false}], "timeline": [["2023-01-28 21:30:00", "2023-01-28 22:08:06"], ["2023-01-29 22:10:00", "2023-01-29 22:50:00"], ["2023-01-30 22:12:00", "2023-01-30 22:32:00"], ["2023-01-31 22:00:52", "2023-01-31 22:35:00"], ["2023-02-01 19:24:00", "2023-02-01 20:16:00"], ["2023-02-02 21:33:48", "2023-02-02 21:33:48"], ["2023-02-02 22:10:28", "2023-02-03 11:13:05"], ["2023-02-03 22:20:00", "2023-02-03 23:01:00"], ["2023-02-06 21:10:00", "2023-02-06 22:20:00"], ["2023-02-07 21:30:00", "2023-02-07 22:05:34"], ["2023-02-08 20:54:19", "2023-02-08 20:54:27"], ["2023-02-08 21:02:00", "2023-02-08 21:40:00"], ["2023-02-09 21:10:00", "2023-02-09 21:46:00"], ["2023-02-10 19:10:00", "2023-02-10 19:55:00"], ["2023-02-13 21:35:00", "2023-02-13 22:15:00"], ["2023-02-14 19:21:13", "2023-02-14 20:15:01"], ["2023-02-15 22:10:00", "2023-02-15 22:50:05"], ["2023-02-16 21:51:44", "2023-02-16 22:34:18"], ["2023-02-17 21:20:00", "2023-02-17 22:05:00"], ["2023-02-20 22:07:40", "2023-02-20 22:52:21"], ["2023-02-21 21:42:14", "2023-02-21 22:58:00"], ["2023-02-22 22:02:26", "2023-02-22 22:34:15"], ["2023-02-23 20:59:25", "2023-02-23 21:38:00"], ["2023-02-24 21:20:00", "2023-02-24 22:09:03"], ["2023-02-27 21:06:20", "2023-02-27 21:07:26"], ["2023-02-27 21:13:26", "2023-02-27 22:08:08"], ["2023-02-28 20:48:15", "2023-02-28 21:25:37"], ["2023-03-02 21:25:00", "2023-03-02 22:08:53"], ["2023-03-03 21:15:00", "2023-03-03 21:58:40"], ["2023-03-06 21:20:17", "2023-03-06 22:05:00"], ["2023-03-07 21:29:18", "2023-03-07 22:10:00"], ["2023-03-08 21:20:00", "2023-03-08 21:53:00"], ["2023-03-09 21:45:00", "2023-03-09 22:15:00"], ["2023-03-10 19:15:00", "2023-03-10 19:50:00"], ["2023-03-13 21:48:40", "2023-03-13 22:31:09"], ["2023-03-14 21:06:18", "2023-03-14 22:14:49"], ["2023-03-16 22:15:00", "2023-03-16 23:29:00"], ["2023-03-17 20:00:00", "2023-03-17 20:40:00"]]}, {"tid": "9105", "parent": "91", "title": "睡觉", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-28 20:12:31", "ddl": "", "level": 2, "marks": [{"time": "2023-02-01 11:22:07", "content": "早上还是十点半班车，一直在聊股票。没状态。现在开始专心工作", "gains": false}], "timeline": [["2023-01-31 02:41:10", "2023-01-31 11:18:20"], ["2023-02-01 02:30:00", "2023-02-01 11:22:22"], ["2023-02-02 03:05:00", "2023-02-02 11:15:00"], ["2023-02-08 02:20:00", "2023-02-08 10:52:40"], ["2023-02-14 01:46:00", "2023-02-14 11:00:00"], ["2023-02-17 01:43:48", "2023-02-17 10:57:26"], ["2023-03-10 23:20:09", "2023-03-10 23:20:42"]]}, {"tid": "9201", "parent": "92", "title": "经济发展规划&城市规划&政策研究", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-28 20:17:50", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "9202", "parent": "92", "title": "外国语言与文化", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-28 20:19:15", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "9203", "parent": "92", "title": "世界地理&历史&政治", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-28 20:19:38", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "9204", "parent": "92", "title": "物理", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-28 20:19:48", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "9205", "parent": "92", "title": "数学", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-28 20:19:52", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "9106", "parent": "91", "title": "洗澡", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-28 20:21:20", "ddl": "", "level": 2, "marks": [{"time": "2023-02-06 23:56:44", "content": "洗完澡剪了个指甲。", "gains": false}, {"time": "2023-02-09 22:50:58", "content": "洗完澡帮爸联系了明天回碣石的顺风车。霞提前一天今晚过生日，没来得及视频一下，外甥应该都睡了。现在继续工作了", "gains": false}, {"time": "2023-02-11 01:07:53", "content": "洗完澡刷了会手机，现在继续。", "gains": false}, {"time": "2023-02-18 00:18:58", "content": "今晚买椰子开的第一个老板娘说坏了。换了一个刚刚拿来喝发现椰子肉淡紫色的，有些香芋味。。查了一下也是坏了。还喝了几口边喝边查。。完了还不小心倒一地。。开瓶椰汁继续吧。", "gains": false}, {"time": "2023-03-02 00:07:57", "content": "只想快速搞钱。。是不是得抓紧投入到量化分析了。。task工具和favorites，耗时有点高了。。", "gains": false}, {"time": "2023-03-02 00:11:22", "content": "但更重要的还是得早点睡觉，难得打完球很累不会强迫症。", "gains": false}, {"time": "2023-03-10 00:33:41", "content": "洗完澡得刷了半个多钟手机。", "gains": false}, {"time": "2023-03-12 00:10:25", "content": "洗完澡又刷了半个钟手机，今天太多事情又很烦躁。或许是霞的指标让我焦虑。或许是今晚hxg。或许是又想到买房跟各种社会问题焦虑。或许是对爸发脾气又很懊悔。总之糟糕极了。活着太痛苦了。刷了半个钟在看习连任和李强的信息。", "gains": false}, {"time": "2023-03-17 23:47:27", "content": "中间一边帮晓威表妹看外包私单跟报价", "gains": false}], "timeline": [["2023-01-12 23:49:00", "2023-01-13 00:18:00"], ["2023-01-28 22:08:06", "2023-01-28 22:08:06"], ["2023-01-28 22:24:11", "2023-01-28 23:16:47"], ["2023-01-29 23:22:44", "2023-01-30 00:12:00"], ["2023-01-30 22:43:14", "2023-01-30 23:11:00"], ["2023-01-31 23:55:00", "2023-02-01 00:24:00"], ["2023-02-02 00:18:00", "2023-02-02 03:05:00"], ["2023-02-04 00:38:00", "2023-02-04 01:19:08"], ["2023-02-04 23:43:30", "2023-02-05 00:54:47"], ["2023-02-05 23:29:00", "2023-02-06 00:01:00"], ["2023-02-06 23:10:52", "2023-02-07 00:02:38"], ["2023-02-07 22:21:07", "2023-02-07 23:04:11"], ["2023-02-08 23:31:39", "2023-02-09 00:21:15"], ["2023-02-09 21:46:00", "2023-02-09 22:53:20"], ["2023-02-11 00:19:14", "2023-02-11 01:09:13"], ["2023-02-11 23:30:00", "2023-02-12 00:20:00"], ["2023-02-13 00:37:15", "2023-02-13 01:06:39"], ["2023-02-13 22:15:00", "2023-02-13 23:02:22"], ["2023-02-14 20:15:01", "2023-02-14 20:46:19"], ["2023-02-15 23:17:19", "2023-02-15 23:48:33"], ["2023-02-16 22:40:55", "2023-02-16 23:22:51"], ["2023-02-17 23:38:46", "2023-02-18 00:20:05"], ["2023-02-19 00:57:05", "2023-02-19 01:34:21"], ["2023-02-20 00:37:07", "2023-02-20 01:41:37"], ["2023-02-20 22:52:21", "2023-02-20 23:19:12"], ["2023-02-21 22:58:00", "2023-02-21 23:40:33"], ["2023-02-22 22:53:44", "2023-02-22 23:41:53"], ["2023-02-23 21:57:00", "2023-02-23 22:41:50"], ["2023-02-24 23:11:26", "2023-02-24 23:41:32"], ["2023-02-25 23:10:59", "2023-02-26 00:10:23"], ["2023-02-26 23:33:03", "2023-02-27 00:09:43"], ["2023-02-27 22:08:08", "2023-02-27 22:08:08"], ["2023-02-27 22:24:09", "2023-02-27 23:27:07"], ["2023-02-28 22:31:41", "2023-02-28 23:39:56"], ["2023-03-01 23:14:00", "2023-03-02 00:11:34"], ["2023-03-02 22:08:53", "2023-03-02 23:10:07"], ["2023-03-03 22:53:23", "2023-03-04 00:04:51"], ["2023-03-05 00:42:47", "2023-03-05 01:16:23"], ["2023-03-05 21:56:29", "2023-03-05 22:52:09"], ["2023-03-06 22:18:23", "2023-03-06 22:56:55"], ["2023-03-07 22:47:30", "2023-03-07 23:32:52"], ["2023-03-08 23:17:00", "2023-03-08 23:50:00"], ["2023-03-09 23:40:43", "2023-03-10 00:44:28"], ["2023-03-10 21:14:00", "2023-03-10 21:48:13"], ["2023-03-11 23:14:31", "2023-03-12 00:10:33"], ["2023-03-12 21:48:00", "2023-03-12 22:18:00"], ["2023-03-13 22:31:09", "2023-03-13 23:23:12"], ["2023-03-14 23:08:34", "2023-03-14 23:52:04"], ["2023-03-16 00:58:24", "2023-03-16 01:58:12"], ["2023-03-16 23:31:21", "2023-03-16 23:59:11"], ["2023-03-17 22:29:00", "2023-03-17 23:47:39"]]}, {"tid": "1401", "parent": "14", "title": "时光反馈局部模板推荐永远固定几个", "category": null, "status": "已完成", "description": "", "priority": 1, "quadrant": 0, "createTime": "2023-01-28 20:43:38", "ddl": "", "level": 2, "marks": [{"time": "2023-01-28 22:03:18", "content": "局部模板推荐规则比较独立，之前pm临时拍的规则", "gains": null}], "timeline": [["2023-01-28 20:44:25", "2023-01-28 21:00:06"], ["2023-01-28 21:29:23", "2023-01-28 21:30:00"]]}, {"tid": "1402", "parent": "14", "title": "把局部模板推荐的最近使用规则从7天改成30天，直接改，改完群里同步。", "category": null, "status": "已完成", "description": "", "priority": 1, "quadrant": 0, "createTime": "2023-01-28 22:05:51", "ddl": "", "level": 2, "marks": [{"time": "2023-02-13 15:36:01", "content": "万军改了", "gains": false}], "timeline": []}, {"tid": "9107", "parent": "91", "title": "休息吃东西刷手机", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-28 22:09:18", "ddl": "", "level": 2, "marks": [{"time": "2023-01-30 00:28:31", "content": "躺床上看狂飙了。最近两天都看到四点，睡五六个钟，着实有点累，想抓紧推task和favorite进度的，以及学习最近的一些高质量发展的文件，干不动了。", "gains": null}, {"time": "2023-01-30 22:43:05", "content": "吃了个苹果。准备洗澡先。", "gains": null}, {"time": "2023-01-31 02:40:49", "content": "去躺着又看了三个钟的狂飙。。本来发誓今晚不熬夜追剧，结果又到现在。。关了剧还hxg，现在刚起来刷完牙、晾完衣服。。明天又要状态差。工作一团乱。一直在堆积、拖延。真的今晚连做私事都不如。。", "gains": null}, {"time": "2023-02-01 11:19:11", "content": "昨晚冰女少优回去后，又开始看起了狂飙", "gains": false}, {"time": "2023-02-01 11:20:41", "content": "洗完澡又继续看起了狂飙一晚上。。", "gains": false}, {"time": "2023-02-02 11:21:54", "content": "活动日早点回家继续看狂飙。", "gains": false}, {"time": "2023-02-04 23:06:42", "content": "玩了半个钟的箫", "gains": false}, {"time": "2023-02-06 00:23:27", "content": "今晚还是早点睡了。还是很困", "gains": false}, {"time": "2023-02-06 22:55:50", "content": "大概歇了快30分钟，主要还在跟家里聊包车，然后练了会箫。", "gains": false}, {"time": "2023-02-07 00:39:48", "content": "吃了个芒果，刷了会脉脉。", "gains": false}, {"time": "2023-02-07 22:05:45", "content": "剪个指甲", "gains": false}, {"time": "2023-02-07 22:20:44", "content": "剪了个脚趾甲，吃了个苹果。洗澡先。", "gains": false}, {"time": "2023-02-08 22:07:17", "content": "安排了好一会周末华为小镇出游的行程安排。现在准备继续工作。", "gains": false}, {"time": "2023-02-16 22:34:23", "content": "吃个芒果", "gains": false}, {"time": "2023-03-09 00:41:36", "content": "出来改了下绩效，吃了东西，又刷起了b站，看起了一个垃圾悬疑电影解说。浪费半个多钟。洗衣机都洗完了。别干活了早点睡了。现在喉咙又隐隐作痛，不知道吃了饼干还是甲流，还是休息不够还硬撑、各种hxg、消耗自己。", "gains": false}, {"time": "2023-03-09 23:04:36", "content": "回到家吃了个根香蕉，喉咙昨晚开始痛，昨天下午就昏昏沉沉，今天更是。也不知道是不是甲流，还好之前出门妈硬塞了几包板蓝根，洗了热水壶冲了一包。", "gains": false}, {"time": "2023-03-10 20:55:23", "content": "躺着刷一晚上手机，吃了个芒果，洗澡先了。", "gains": false}, {"time": "2023-03-16 23:29:55", "content": "吃了个芒果，剪了个指甲", "gains": false}], "timeline": [["2023-01-28 22:08:06", "2023-01-28 22:24:11"], ["2023-01-29 22:50:00", "2023-01-29 23:22:44"], ["2023-01-30 00:23:36", "2023-01-30 00:35:36"], ["2023-01-30 21:15:00", "2023-01-30 21:41:26"], ["2023-01-30 22:32:00", "2023-01-30 22:43:14"], ["2023-01-30 23:11:00", "2023-01-31 02:41:10"], ["2023-01-31 23:27:00", "2023-01-31 23:55:00"], ["2023-02-01 00:24:00", "2023-02-01 02:30:00"], ["2023-02-01 20:16:00", "2023-02-02 00:18:00"], ["2023-02-04 01:40:00", "2023-02-04 02:00:03"], ["2023-02-04 17:37:27", "2023-02-04 20:02:00"], ["2023-02-04 22:14:00", "2023-02-04 22:52:00"], ["2023-02-06 00:01:00", "2023-02-06 00:25:21"], ["2023-02-06 22:20:00", "2023-02-06 23:10:52"], ["2023-02-07 00:14:20", "2023-02-07 00:40:33"], ["2023-02-07 22:05:34", "2023-02-07 22:21:07"], ["2023-02-08 21:40:00", "2023-02-08 22:13:35"], ["2023-02-10 21:37:37", "2023-02-10 21:51:03"], ["2023-02-16 22:34:18", "2023-02-16 22:40:55"], ["2023-02-17 22:05:00", "2023-02-17 22:19:13"], ["2023-02-23 21:38:00", "2023-02-23 21:57:00"], ["2023-03-01 22:45:00", "2023-03-01 23:14:00"], ["2023-03-02 23:10:07", "2023-03-02 23:25:50"], ["2023-03-02 23:25:50", "2023-03-02 23:40:15"], ["2023-03-06 22:05:00", "2023-03-06 22:18:23"], ["2023-03-07 22:10:00", "2023-03-07 22:47:30"], ["2023-03-08 21:53:00", "2023-03-08 22:10:00"], ["2023-03-08 23:50:00", "2023-03-09 00:42:58"], ["2023-03-09 22:15:00", "2023-03-09 23:40:43"], ["2023-03-10 19:50:00", "2023-03-10 21:14:00"], ["2023-03-15 22:51:00", "2023-03-15 23:52:00"], ["2023-03-16 23:29:00", "2023-03-16 23:31:21"], ["2023-03-17 20:40:00", "2023-03-17 21:30:00"]]}, {"tid": "61", "parent": "5", "title": "生活临时琐事", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-28 22:26:40", "ddl": "", "level": 1, "marks": [], "timeline": []}, {"tid": "6101", "parent": "61", "title": "清理生活中可以不要的垃圾，精简生活。做好随时灵活搬家打算", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 0, "createTime": "2023-01-28 22:28:48", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "9108", "parent": "91", "title": "清洁打扫", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-28 22:30:38", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "520104", "parent": "5201", "title": "整理任务(CRUD和安排每日任务)", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-28 23:16:35", "ddl": "", "level": 3, "marks": [{"time": "2023-01-28 23:41:08", "content": "test mark to tid", "gains": null}, {"time": "2023-01-30 16:00:25", "content": "想启动task时用了grep的能力，发现有问题，梳理了task", "gains": null}, {"time": "2023-02-04 01:29:17", "content": "不得不感慨一下搞出task工具，真的很受用。这么多年，用印象笔记管理任务简直烂透了。笔记终究是笔记，任务管理是任务管理，时间管理是时间管理。这些来到大公司才有所感悟，也说明了自己一直是有成长的。\n1. 自从搞了这个命令，现在周末的时间更加平衡了。起码，可以放一些时间到生活。不至于一大堆堆积的任务，做都做不完，又想着赶紧做完。以至于所有周末时间都不得不投入到自己的私人项目。明明是业余的东西，却让自己很有压力。以至于错过一切社交生活。\n2. 另外一点，随着天马行空的想法任务越来越多，之前也会频频岔出去做其他事情。但是就会越来越乱，因为没有一个地方汇总任务，都是零散地分散在各个笔记。以至于失控。不知道这个做完要做什么，不知道堆积了哪些任务。不知道当下更高优做什么，当下做的事情在全部事情中的位置。甚至有时知道卡进来一个问题，并不比当前的任务更高优，但是想尽快close或者收尾，就先分心去攻克一些难题，导致自己的任务模型变成栈模型，而不是优先队列模型。久而久之让自己形成一个坏习惯，就是不是先记录下来、快速绕过、着眼于更高优的任务，等有空再去解决。因为任务管理混乱，跳过了可能就忘记这个事情了。\n3. 之前让自己频频接近崩溃的原因就是任务永远做不完，而且越来越多。当在这里统一管理之后，看着不断膨胀的琐碎任务，意识到注定不可能全部完成的，能做的只是里面极少的一部分。因此，每次都只能一眼在里面挑最重要和有兴趣的事情。因此，优先级的意识就出来了。这样就会发现出去社交也是需要安排时间的一个重要事项，而不是永远被自己的其他任务阻塞，永远想结束后再去社交。\n4. 甚至有很多事情，之前强迫自己先做了，是无用功。因为没想清楚，演变之后就不用做了。像现在对task命令行做一大堆排版、美化的事情，后面发现要做web，那前面这些大概率又是没意义的白费功了，哪有这么多生命浪费呢。。\n4. 因此，现在这个工具就很适用于不断膨胀的任务事项，有个定海神针的作用。\n5. 并且这可以不断优化、沉淀出来自己优秀的任务管理、时间管理模型。包括现在不断新加的需求，比如pick up出来一部分当天需要处理的任务。这些都是自己抽象模型的不断完善功能、提取。", "gains": false}, {"time": "2023-02-10 16:27:49", "content": "应该高优启动主线任务。不应该过多时间分散在零碎小事。但是几个高优事项最好先借空挡优先解决：\n1. 梳理MG+双品牌相关的逻辑，不然每次需求实现都比较mess\n2. 问题meego单先定位一下\n3. 前面遗留的几个实验相关的数据先分析一下\n4. 事情接口性能又变差了，需要重新定位分析", "gains": false}, {"time": "2023-02-17 23:38:29", "content": "有了这个任务工具，才知道自己原来花了那么多个小时在很多没什么意义的事情上，又特别花时间，尤其是踩坑的时候。浪费时间起来真要命。侧面说明这个工具对自己的价值。有个更清晰的把握。不然年复一年很多年，都全身心投入在永不停歇的这些事项中。真的是机缘巧合开始动手搞这个东西。意义重大。", "gains": false}, {"time": "2023-02-18 13:18:37", "content": "其实搞出来这个工具也不单纯是机缘巧合，而是越来越注重优先级，这个工具目前需求最迫切，所以最高优搞出来管理时间。那么接下来，其实最高优的事情，要搞钱。不管是注册企业、写app尝试，还是分析股票数据，还是快速研究国家政策、需要完善的笔记工具来做管理、维护。都应该最高优先支持这些目标。其他的小修小布、优化等等，都是非高优的。", "gains": false}, {"time": "2023-02-18 13:22:08", "content": "所以即使是优化ktask的web界面，也应该高优为了支持做笔记，研究分析国家政策、总结历史政策等目的高优。", "gains": false}, {"time": "2023-02-19 16:00:03", "content": "不用这个工具统计一下时间不知道，统计了真的吓一跳。难怪感觉自己那么拼，结果永远走不上正轨。一直在做各种毫无意义的事情。这么多时间、那么多累计的时间。实际上工作投入度都不高。就一股脑投入到各种踩坑、无止尽的踩坑、各种垃圾技术坑的学习。从来无法停下来总结一下。停下来也会迷茫，因为没有统计数据，不知道时间究竟投进去多少。", "gains": true}], "timeline": [["2023-01-28 23:16:47", "2023-01-28 23:16:47"], ["2023-01-29 11:38:44", "2023-01-29 11:50:58"], ["2023-01-30 00:12:00", "2023-01-30 00:23:36"], ["2023-01-30 11:19:00", "2023-01-30 11:28:13"], ["2023-01-30 15:01:56", "2023-01-30 15:04:43"], ["2023-01-30 15:04:43", "2023-01-30 15:08:58"], ["2023-01-30 15:41:00", "2023-01-30 16:01:58"], ["2023-01-30 16:01:58", "2023-01-30 16:10:59"], ["2023-01-31 11:32:00", "2023-01-31 11:39:13"], ["2023-02-02 11:15:00", "2023-02-02 11:25:48"], ["2023-02-02 18:20:05", "2023-02-02 18:25:06"], ["2023-02-04 01:19:08", "2023-02-04 01:40:00"], ["2023-02-04 02:00:03", "2023-02-04 02:25:25"], ["2023-02-04 14:08:19", "2023-02-04 14:34:49"], ["2023-02-04 22:52:00", "2023-02-04 23:15:50"], ["2023-02-07 20:58:56", "2023-02-07 20:58:56"], ["2023-02-08 10:52:40", "2023-02-08 10:56:24"], ["2023-02-10 16:12:25", "2023-02-10 16:12:42"], ["2023-02-10 16:12:47", "2023-02-10 16:15:00"], ["2023-02-12 12:26:05", "2023-02-12 12:49:29"]]}, {"tid": "4104", "parent": "41", "title": "跟pm对齐搜索优化、指定模板指定等产品需求的人力和排期", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 00:23:36", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "9109", "parent": "91", "title": "刷牙晾衣服忙碌", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 00:25:31", "ddl": "", "level": 2, "marks": [{"time": "2023-02-04 02:25:35", "content": "好困了，搞完早点睡了。", "gains": false}, {"time": "2023-02-12 01:28:38", "content": "今天毕竟走了一整天，还是又累又困，先刷牙吧。", "gains": false}, {"time": "2023-02-14 00:11:44", "content": "今晚早点睡了，难得有些累，不要熬夜了。今天咖啡喝不多。", "gains": false}, {"time": "2023-02-17 01:43:34", "content": "还是注意休息。早点睡。", "gains": false}, {"time": "2023-02-18 01:18:29", "content": "晚上按完头，确实昏昏沉沉不要熬夜了。早点躺着就睡了。\n", "gains": false}, {"time": "2023-02-21 00:50:54", "content": "明天高优实现页面功能，因为最可能有风险。先mock任务数据。然后实现各种功能，比如多种操作按钮，已经展示、跳转笔记文件等等。", "gains": false}, {"time": "2023-02-26 01:27:45", "content": "早点刷牙睡觉了，明天一大早又被死楼上吵醒。", "gains": false}, {"time": "2023-03-10 00:49:42", "content": "还是早点睡。", "gains": false}], "timeline": [["2023-01-29 00:25:38", "2023-01-29 00:45:28"], ["2023-01-30 00:56:01", "2023-01-30 01:07:45"], ["2023-02-04 02:25:25", "2023-02-04 02:43:14"], ["2023-02-05 01:42:46", "2023-02-05 01:51:47"], ["2023-02-06 00:25:21", "2023-02-06 00:31:53"], ["2023-02-07 01:09:28", "2023-02-07 01:21:11"], ["2023-02-08 00:03:23", "2023-02-08 00:06:39"], ["2023-02-08 00:14:17", "2023-02-08 00:33:00"], ["2023-02-09 00:52:56", "2023-02-09 01:08:00"], ["2023-02-10 00:31:31", "2023-02-10 00:50:20"], ["2023-02-11 02:28:18", "2023-02-11 02:40:28"], ["2023-02-12 01:28:16", "2023-02-12 01:43:22"], ["2023-02-13 01:46:40", "2023-02-13 01:58:52"], ["2023-02-14 00:11:26", "2023-02-14 00:24:41"], ["2023-02-15 00:06:43", "2023-02-15 00:23:45"], ["2023-02-16 00:40:42", "2023-02-16 01:02:48"], ["2023-02-17 01:32:29", "2023-02-17 01:43:48"], ["2023-02-18 01:18:07", "2023-02-18 01:29:40"], ["2023-02-19 02:56:04", "2023-02-19 03:07:00"], ["2023-02-20 02:37:31", "2023-02-20 02:46:12"], ["2023-02-21 00:41:16", "2023-02-21 00:51:07"], ["2023-02-22 01:01:41", "2023-02-22 01:15:00"], ["2023-02-23 00:12:57", "2023-02-23 11:25:18"], ["2023-02-24 00:05:22", "2023-02-24 00:20:51"], ["2023-02-25 00:52:34", "2023-02-25 01:07:28"], ["2023-02-26 01:27:27", "2023-02-26 01:40:04"], ["2023-02-27 01:35:00", "2023-02-27 01:48:41"], ["2023-02-28 00:46:10", "2023-02-28 00:55:01"], ["2023-03-01 00:31:46", "2023-03-01 00:48:25"], ["2023-03-02 00:11:34", "2023-03-02 00:21:42"], ["2023-03-02 00:55:48", "2023-03-02 00:59:45"], ["2023-03-03 00:32:27", "2023-03-03 00:48:19"], ["2023-03-04 01:06:34", "2023-03-04 01:20:52"], ["2023-03-05 02:08:48", "2023-03-05 02:18:16"], ["2023-03-06 00:10:10", "2023-03-06 00:25:26"], ["2023-03-06 23:40:29", "2023-03-06 23:52:33"], ["2023-03-08 00:01:00", "2023-03-08 00:14:21"], ["2023-03-09 00:42:58", "2023-03-09 00:58:18"], ["2023-03-10 00:49:34", "2023-03-10 01:02:10"], ["2023-03-10 23:00:59", "2023-03-10 23:20:09"], ["2023-03-12 00:53:28", "2023-03-12 01:15:52"], ["2023-03-13 00:31:18", "2023-03-13 01:03:19"], ["2023-03-15 01:18:09", "2023-03-15 01:29:10"], ["2023-03-16 02:28:00", "2023-03-16 02:36:39"], ["2023-03-17 01:16:29", "2023-03-17 01:25:47"], ["2023-03-18 01:10:52", "2023-03-18 01:24:51"]]}, {"tid": "52010301", "parent": "520103", "title": "start-time参数灵活支持多种便捷短格式", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 00:27:23", "ddl": "", "level": 4, "marks": [{"time": "2023-01-30 11:39:52", "content": "支持%H:%M:%S格式", "gains": null}], "timeline": [["2023-01-29 19:25:24", "2023-01-29 21:14:00"], ["2023-01-30 11:32:58", "2023-01-30 11:32:58"], ["2023-01-30 11:32:58", "2023-01-30 11:40:28"]]}, {"tid": "52010302", "parent": "520103", "title": "start命令指定--force-multi多任务", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 00:27:44", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "52010303", "parent": "520103", "title": "每次start命令，把当前inprogress命令push到一个列表字段，方便取回查看", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 0, "createTime": "2023-01-29 00:28:43", "ddl": "", "level": 4, "marks": [{"time": "2023-01-30 00:55:45", "content": "改起来要记录history，还要记录start时的进行中任务列表。没那么好改，先刷牙睡觉了。这么晚了。。", "gains": null}], "timeline": [["2023-01-30 00:46:55", "2023-01-30 00:56:01"]]}, {"tid": "52010304", "parent": "520103", "title": "把ddl参数加上，现在所以任务都没有ddl，一个是优先级不好确定，另一个是又不知道做哪个最终又临时抱佛脚", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 0, "createTime": "2023-01-29 00:41:14", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "9110", "parent": "91", "title": "躺床", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 00:44:49", "ddl": "", "level": 2, "marks": [{"time": "2023-01-29 00:47:15", "content": "明天一大早对齐会议，还有一堆事情没做。。又想躺床上了。优化方案是搞不完的了，至少模板置顶的排期要给出来。躺着思考吧。", "gains": null}, {"time": "2023-01-30 11:18:03", "content": "最近还是不要看剧了，连续三天都在追狂飙，看到四点，严重影响状态，今天睡到9:45，错过年后第一天开盘的高开了。。", "gains": null}, {"time": "2023-02-04 13:59:58", "content": "昨晚还是差不多三点睡，早上一觉十点多被吵醒，躺床上12点才起来，差点又hxg，终究没有，思绪太多，事情太多。起来点了个披萨，又刷了会手机，现在打完咖啡，继续干事情。", "gains": false}, {"time": "2023-02-06 00:12:32", "content": "还是两点多睡，差不多九点就醒了。", "gains": false}, {"time": "2023-02-09 01:43:28", "content": "早点休息了。尽量把这些ROI不高的事情不要占据全部生活。平时多并行学习、多并行享受生活、多并行休息注意身体，这个最关键。", "gains": false}, {"time": "2023-02-10 20:41:30", "content": "回到差不多八点，太困了又累，想躺床歇会，但是光刷手机了。刷了半个多钟b站，也没得到休息。还不如不浪费时间，抓紧干活。明天霞回来，得抓紧打扫干净，明天又要去欧洲小镇，得抓紧把自己的任务处理完。", "gains": false}, {"time": "2023-02-12 01:43:30", "content": "不熬夜了，早点躺床。", "gains": false}, {"time": "2023-02-12 12:21:35", "content": "09:35醒来，睡了差不多7个钟，然后昨天走了一天全身酸累，刷了差不多一个钟手机，开始hxg", "gains": false}, {"time": "2023-02-12 15:11:54", "content": "坐得腰酸屁股痛，躺会。", "gains": false}, {"time": "2023-02-16 01:03:31", "content": "今晚跑完步，身体还是有点累的。大脑还是很兴奋的。早点睡了。", "gains": false}, {"time": "2023-02-18 13:19:14", "content": "昨晚大概两点睡，早上十一点多醒，躺到十二点多起来。昨晚按完摩全身还是累，睡得比较香。现在继续抓紧干活。", "gains": false}, {"time": "2023-02-18 13:22:34", "content": "而不是仅仅为了美化任务的界面，目前还能用得过去。", "gains": false}, {"time": "2023-02-19 10:48:43", "content": "昨晚差不多四点才睡，早上九点多醒了，迷迷糊糊被楼上吵醒，上个厕所睡不着了，干脆不赖床了。10:22起来洗漱，现在打了咖啡、吃了个面包。继续干活。", "gains": false}, {"time": "2023-02-25 16:56:51", "content": "写到头眩晕", "gains": false}, {"time": "2023-03-04 14:09:05", "content": "晓霞过来，早上十一点多起来拖了个地。吃完披萨又聊了好一会。现在继续。", "gains": false}, {"time": "2023-03-05 15:24:40", "content": "早上九点多被楼上吵醒，十点多起来收拾，十一点多冰女少优过来，喝了咖啡，十二点半出去吃新疆菜、查理一世，刚回到。继续工作。", "gains": false}, {"time": "2023-03-05 21:22:51", "content": "晚上吃饭、刷手机、练会钢琴、躺着看hxg、继续刷b站。。", "gains": false}, {"time": "2023-03-11 16:14:56", "content": "霞发了最新的报告，发现了有核红细胞，内心很不安。躺床又刷了两个钟短视频。还不如继续干活。从理性出发，焦虑也没用，更应该合理安排时间，什么时候干什么事。", "gains": false}, {"time": "2023-03-16 11:39:04", "content": "又在群里跟建城辩了一早上。。", "gains": false}, {"time": "2023-03-18 10:17:23", "content": "昨晚两点睡，早上八点又被楼上傻逼拖椅子吵醒。玩了一个钟手机又眯了一会睡不着但是全身累还头嗡嗡，九点半还是起来了。继续推进。晚上早点睡。", "gains": false}], "timeline": [["2023-01-29 00:45:28", "2023-01-29 10:35:00"], ["2023-01-30 01:07:45", "2023-01-30 11:19:00"], ["2023-02-04 02:43:14", "2023-02-04 14:08:19"], ["2023-02-05 01:51:47", "2023-02-05 10:20:00"], ["2023-02-06 00:31:53", "2023-02-06 10:56:31"], ["2023-02-07 01:21:11", "2023-02-07 10:43:26"], ["2023-02-08 00:33:00", "2023-02-08 01:40:00"], ["2023-02-09 01:42:28", "2023-02-09 10:40:00"], ["2023-02-10 02:10:44", "2023-02-10 10:49:28"], ["2023-02-10 19:55:00", "2023-02-10 20:43:11"], ["2023-02-11 02:40:28", "2023-02-11 11:30:00"], ["2023-02-12 01:43:22", "2023-02-12 11:00:00"], ["2023-02-12 15:11:41", "2023-02-12 16:00:32"], ["2023-02-13 01:58:52", "2023-02-13 10:50:00"], ["2023-02-14 00:24:41", "2023-02-14 01:00:00"], ["2023-02-16 01:02:48", "2023-02-16 11:05:00"], ["2023-02-18 01:29:40", "2023-02-18 13:23:03"], ["2023-02-19 03:19:40", "2023-02-19 10:50:32"], ["2023-02-20 02:46:12", "2023-02-20 11:00:00"], ["2023-02-21 00:51:07", "2023-02-21 11:20:05"], ["2023-02-22 01:50:25", "2023-02-22 10:30:00"], ["2023-02-24 00:20:51", "2023-02-24 11:16:45"], ["2023-02-25 01:07:28", "2023-02-25 12:52:03"], ["2023-02-25 16:56:39", "2023-02-25 20:10:22"], ["2023-02-26 01:40:04", "2023-02-26 13:13:47"], ["2023-02-26 16:21:00", "2023-02-26 17:38:16"], ["2023-02-27 01:48:41", "2023-02-27 10:47:45"], ["2023-02-28 00:55:01", "2023-02-28 10:54:10"], ["2023-03-01 00:48:25", "2023-03-01 11:08:00"], ["2023-03-02 00:59:45", "2023-03-02 10:55:00"], ["2023-03-03 00:48:19", "2023-03-03 11:15:34"], ["2023-03-04 01:20:52", "2023-03-04 14:09:23"], ["2023-03-04 16:24:54", "2023-03-04 18:01:55"], ["2023-03-04 22:10:42", "2023-03-04 23:05:49"], ["2023-03-05 02:25:02", "2023-03-05 15:24:53"], ["2023-03-05 17:16:10", "2023-03-05 17:29:55"], ["2023-03-05 18:22:18", "2023-03-05 21:23:03"], ["2023-03-06 00:25:26", "2023-03-06 11:05:12"], ["2023-03-07 00:19:09", "2023-03-07 10:55:00"], ["2023-03-08 00:41:00", "2023-03-08 10:17:00"], ["2023-03-09 00:58:18", "2023-03-09 10:57:44"], ["2023-03-10 01:02:10", "2023-03-10 11:30:45"], ["2023-03-10 23:20:42", "2023-03-11 12:50:25"], ["2023-03-11 14:00:00", "2023-03-11 16:15:10"], ["2023-03-12 01:15:52", "2023-03-12 11:14:19"], ["2023-03-13 01:03:19", "2023-03-13 10:50:17"], ["2023-03-14 01:28:35", "2023-03-14 10:46:00"], ["2023-03-15 01:46:51", "2023-03-15 10:27:00"], ["2023-03-16 02:36:39", "2023-03-16 11:46:39"], ["2023-03-17 01:25:47", "2023-03-17 11:04:05"], ["2023-03-18 01:24:51", "2023-03-18 10:18:14"], ["2023-03-18 13:16:35", "2023-03-18 13:33:59"]]}, {"tid": "52010305", "parent": "520103", "title": "mv命令添加parent参数", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 10:38:30", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "410401", "parent": "4104", "title": "提前整理现有需求情况，文档收录，明确会议对齐目标", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 10:39:49", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-01-29 10:35:00", "2023-01-29 11:00:00"]]}, {"tid": "410403", "parent": "4104", "title": "会议对齐需求&优先级&部分排期", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 11:27:23", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-01-29 11:00:00", "2023-01-29 11:38:44"]]}, {"tid": "120405", "parent": "1204", "title": "出技术方案", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 11:35:54", "ddl": "", "level": 3, "marks": [{"time": "2023-01-30 16:04:41", "content": "今天这个任务进行得非常低效，时时叉出去想东想西做别的。不够专注集中思路高效推动。跟没有午睡、昨晚睡五个钟也有关系。开了代码就想先解决拖了很久的颖鑫的MR", "gains": null}, {"time": "2023-01-31 17:48:25", "content": "多少有在写，但是还是比较分心。主要也很困。状态不佳。今晚一定不能看剧，克制住两天就适应了。", "gains": null}, {"time": "2023-01-31 20:17:20", "content": "明天周会。今晚最好全力加班一晚，不要看剧。把手头的事情高效处理掉一些。另外累了早点睡，调整一下状态。", "gains": false}, {"time": "2023-01-31 22:00:40", "content": "后面基本没什么状态。。先回家了。。", "gains": false}, {"time": "2023-02-02 16:15:39", "content": "发了一下技术评审会议。给陈锐找了biz_checker打点，顺便收录链接。", "gains": false}], "timeline": [["2023-01-30 11:19:00", "2023-01-30 11:19:00"], ["2023-01-30 11:28:13", "2023-01-30 11:28:13"], ["2023-01-30 11:40:28", "2023-01-30 11:40:28"], ["2023-01-30 14:23:43", "2023-01-30 14:23:43"], ["2023-01-30 14:34:35", "2023-01-30 14:34:35"], ["2023-01-30 14:49:55", "2023-01-30 15:01:56"], ["2023-01-30 15:08:58", "2023-01-30 15:08:58"], ["2023-01-30 15:22:04", "2023-01-30 15:41:00"], ["2023-01-30 16:29:11", "2023-01-30 16:46:11"], ["2023-01-30 22:11:28", "2023-01-30 22:12:00"], ["2023-01-31 11:18:20", "2023-01-31 11:32:00"], ["2023-01-31 11:39:13", "2023-01-31 11:39:49"], ["2023-01-31 11:51:50", "2023-01-31 11:55:00"], ["2023-01-31 14:41:39", "2023-01-31 14:41:39"], ["2023-01-31 15:26:54", "2023-01-31 15:26:54"], ["2023-01-31 15:36:11", "2023-01-31 15:36:11"], ["2023-01-31 15:53:26", "2023-01-31 15:53:26"], ["2023-01-31 16:14:08", "2023-01-31 16:14:08"], ["2023-01-31 16:18:00", "2023-01-31 16:18:00"], ["2023-01-31 16:40:43", "2023-01-31 16:40:43"], ["2023-01-31 16:54:51", "2023-01-31 18:03:29"], ["2023-01-31 19:50:36", "2023-01-31 19:50:36"], ["2023-01-31 20:08:17", "2023-01-31 20:32:00"], ["2023-01-31 21:34:12", "2023-01-31 22:00:52"], ["2023-02-01 11:22:22", "2023-02-01 12:00:00"], ["2023-02-01 13:43:35", "2023-02-01 13:45:39"], ["2023-02-01 14:33:54", "2023-02-01 14:33:54"], ["2023-02-01 14:48:37", "2023-02-01 15:00:00"], ["2023-02-01 17:01:17", "2023-02-01 17:15:00"], ["2023-02-02 15:50:26", "2023-02-02 16:55:46"], ["2023-02-02 17:06:10", "2023-02-02 17:06:10"], ["2023-02-02 17:10:29", "2023-02-02 17:10:57"]]}, {"tid": "120406", "parent": "1204", "title": "开发", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 11:36:07", "ddl": "", "level": 3, "marks": [{"time": "2023-02-06 14:10:46", "content": "完成tcc配置=>获取top列表=>推荐tab置顶的流程，接下来补充category tab的流程", "gains": false}, {"time": "2023-02-06 14:33:49", "content": "category tab添加完成，但目前似乎有跟其他策略有冲突", "gains": false}, {"time": "2023-02-06 15:05:27", "content": "继续搜索接口接入置顶策略", "gains": false}, {"time": "2023-02-06 15:13:31", "content": "category tab添加完成，但目前似乎有跟其他策略有冲突", "gains": false}, {"time": "2023-02-06 15:28:30", "content": "搜索接口也完成置顶", "gains": false}, {"time": "2023-02-06 15:30:46", "content": "还是先完成这个。更加高优的主线任务。", "gains": false}, {"time": "2023-02-06 15:31:03", "content": "目前剩下策略冲突的问题，看看怎么解决。", "gains": false}, {"time": "2023-02-06 15:35:07", "content": "看起来不是策略冲突的问题。而是因为套组模板的关系。在做策略合并时，没有把套组模板归类到某个文档类型中一起排序。", "gains": false}, {"time": "2023-02-06 15:35:41", "content": "这个应该算是策略聚合引擎的小问题。暂时先忽略吧。", "gains": false}, {"time": "2023-02-06 15:36:01", "content": "那接下来先发boe提测。", "gains": false}, {"time": "2023-02-06 15:47:25", "content": "接下来补单测、发MR", "gains": false}], "timeline": [["2023-02-02 17:10:57", "2023-02-02 17:10:57"], ["2023-02-02 17:31:19", "2023-02-02 17:31:19"], ["2023-02-02 21:26:36", "2023-02-02 21:27:05"], ["2023-02-06 11:04:23", "2023-02-06 12:00:00"], ["2023-02-06 12:57:31", "2023-02-06 14:38:06"], ["2023-02-06 14:50:00", "2023-02-06 15:29:37"], ["2023-02-06 15:30:20", "2023-02-06 15:48:13"]]}, {"tid": "120407", "parent": "1204", "title": "自测&改bug", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 11:36:15", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "4303", "parent": "43", "title": "安全周会", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 11:40:25", "ddl": "", "level": 2, "marks": [{"time": "2023-02-01 18:54:54", "content": "活动日下班开周会，公司组织的羽毛球抢到了又取消了。", "gains": false}, {"time": "2023-02-08 18:36:07", "content": "一边建了发布CheckList文档，一边提了发布工单，一边讨论周六欧洲小镇出游的事情。", "gains": false}], "timeline": [["2023-02-01 18:00:00", "2023-02-01 19:00:00"], ["2023-02-08 18:01:33", "2023-02-08 19:10:00"], ["2023-02-15 18:06:43", "2023-02-15 19:10:00"], ["2023-02-22 17:59:36", "2023-02-22 18:55:00"], ["2023-03-01 18:00:00", "2023-03-01 18:55:00"], ["2023-03-08 18:00:00", "2023-03-08 19:10:00"], ["2023-03-15 18:00:00", "2023-03-15 18:55:00"]]}, {"tid": "12020307", "parent": "120203", "title": "推荐模型的打点数据有问题待定位，年前受4101开发机问题影响中断", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 11:49:16", "ddl": "", "level": 4, "marks": [{"time": "2023-02-13 16:29:14", "content": "翻了半天笔记找不到当时是什么问题；找了半天文档找不到之前整理的相关metrics埋点链接收录", "gains": false}, {"time": "2023-02-13 16:29:52", "content": "只能重新梳理一遍埋点、验证下日志、埋点和数据了", "gains": false}, {"time": "2023-02-13 16:35:16", "content": "当时可能要验证模型分数为0的模板，会什么表现。", "gains": false}, {"time": "2023-02-13 16:38:25", "content": "模型构建的节点确实不稳定，好几天只有一天rebuild和dump", "gains": false}, {"time": "2023-02-13 16:48:33", "content": "记得之前是跟request相关的埋点数据有关系", "gains": false}, {"time": "2023-02-13 17:00:41", "content": "先定位到一个问题了。发现很多matrix_model_request埋点上报的日志报错entry的tagV为空", "gains": false}, {"time": "2023-02-13 17:20:25", "content": "懵逼半天愣是找不到之前Entry参数要怎么设计的。先重新捋一遍fix了吧，再完整过一遍其他数据跟有没有问题，之前少了完整自测跟数据验证这个环节。", "gains": false}, {"time": "2023-02-13 17:29:59", "content": "估计之前预留了entry用来标志推荐入口、分类tab入口、新建入口等。但实际上这里无法感知。最多区分新建入口和独立按钮入口。至于推荐tab和分类tab，是在同个接口返回的。", "gains": false}, {"time": "2023-02-13 17:40:24", "content": "发现了这里的matrix_model_request埋点，是整个推荐策略聚合打点的一个，所以有entry，还有其他的zeus_category_request等策略的打点。这样就清晰了", "gains": false}, {"time": "2023-02-13 17:41:06", "content": "entry不是要感知前端用户的entry，而是具体entry下，命中不同推荐策略的次数", "gains": false}, {"time": "2023-02-13 17:45:28", "content": "之前这个埋点应该属于最基础的验证埋点，自己不在实验租户里面，起码有渠道知道现在命中doc推荐实验的租户、人数、请求数", "gains": false}, {"time": "2023-02-13 17:47:53", "content": "其实推荐策略聚合相关的埋点，放到最终聚合后根据algorithmID来统计上报一次，会更高", "gains": false}, {"time": "2023-02-13 17:53:15", "content": "思考一下怎么优化和修复这块的打点统计，可以进行数据验证", "gains": false}, {"time": "2023-02-13 17:57:14", "content": "有些mess犯困", "gains": false}, {"time": "2023-02-13 19:44:09", "content": "继续看看怎么完善修复策略聚合和模型推荐的打点统计", "gains": false}, {"time": "2023-02-13 20:21:54", "content": "好困。。", "gains": false}, {"time": "2023-02-13 21:31:08", "content": "纠结那么多开发机怎么调试推荐模型流程，就为了来验证fix埋点上报是否成功了。还不如直接SCM发ppe验证一下更快。不用思考更多怎么优雅地实现本地调试。还要相应增加很多代码，又要调通。", "gains": false}, {"time": "2023-02-14 11:24:10", "content": "昨晚发了ppe，现在验证一下打点", "gains": false}, {"time": "2023-02-14 11:28:03", "content": "打点验证修复了，可以发hotfixes上线", "gains": false}, {"time": "2023-02-14 11:35:25", "content": "思考一下，除了fix的这个request_counter打点，还需要补充哪些。比如策略聚合下的各个Entry数量", "gains": false}, {"time": "2023-02-14 11:44:17", "content": "目前的打点设计没有统一好方案，写代码的时候临时拍脑袋想的，比较混乱。不要投入太多时间完善，修复的先上。然后优先考虑数据分析目标，才是高ROI主线目标，以此来看看目前的打点支持不支持。", "gains": false}, {"time": "2023-02-14 11:49:39", "content": "至于zeus_category_request_counter为什么没有打点，日志也看不到报错。不好定位就别浪费太多时间，毕竟metrics本身就垃圾很多坑。", "gains": false}, {"time": "2023-02-14 14:41:57", "content": "再三确认代码没问题，而且流程不复杂。那就是metrics本身的问题，结合之前说大数据被封禁之类的。如果终于直接Oncall。但高优分析模型数据为准。先把fix发布上线", "gains": false}, {"time": "2023-02-14 14:48:20", "content": "确认了线上环境commit，提起MR，关联meegoID commit", "gains": false}, {"time": "2023-02-14 15:59:14", "content": "继续发布devops", "gains": false}, {"time": "2023-02-14 17:08:24", "content": "pre发完验证了一下打点好像还没有", "gains": false}, {"time": "2023-02-14 17:15:45", "content": "是metrics部署区域选错了，要用China-North", "gains": false}], "timeline": [["2023-02-13 16:01:04", "2023-02-13 16:01:04"], ["2023-02-13 16:07:19", "2023-02-13 17:00:45"], ["2023-02-13 17:05:35", "2023-02-13 17:06:35"], ["2023-02-13 17:14:21", "2023-02-13 17:57:26"], ["2023-02-13 19:43:44", "2023-02-13 19:43:44"], ["2023-02-13 20:21:34", "2023-02-13 21:35:00"], ["2023-02-14 11:23:51", "2023-02-14 12:00:00"], ["2023-02-14 14:35:20", "2023-02-14 14:49:48"], ["2023-02-14 15:59:04", "2023-02-14 16:01:00"], ["2023-02-14 17:01:09", "2023-02-14 17:08:34"]]}, {"tid": "52010306", "parent": "520103", "title": "比较常见的问题就是start一个任务后，mess了一段时间，又要去查找上一个任务的启动时间来覆盖。最好能够直接reset上一次start到某个任务", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 14:32:55", "ddl": "", "level": 4, "marks": [], "timeline": [["2023-02-02 19:58:13", "2023-02-02 20:00:00"], ["2023-02-02 21:59:01", "2023-02-02 22:10:28"]]}, {"tid": "52010307", "parent": "520103", "title": "info命令支持无tid参数时，默认展示进行中的任务", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 14:34:12", "ddl": "", "level": 4, "marks": [], "timeline": [["2023-01-30 00:35:36", "2023-01-30 00:46:55"]]}, {"tid": "6102", "parent": "61", "title": "跟卫老板去华强北修ipad3逛了逛", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 17:24:55", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-01-29 14:55:00", "2023-01-29 17:25:34"]]}, {"tid": "93", "parent": "5", "title": "技术专题", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 20:13:26", "ddl": "", "level": 1, "marks": [], "timeline": []}, {"tid": "9301", "parent": "93", "title": "Rust", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 20:13:43", "ddl": "", "level": 2, "marks": [{"time": "2023-01-29 20:14:58", "content": "关于Result enum、问号运算符、From<T> trait、Result<T, E>的unwrap/unwrap_err/unwrap_else等用法", "gains": null}, {"time": "2023-01-29 20:38:37", "content": "rust函数中省略分号只是最后一条语句，表示return，并不是Ok或者Err的特殊写法。。函数体中间还是要return而且要分号", "gains": null}], "timeline": []}, {"tid": "52010308", "parent": "520103", "title": "需要完善单测能力了，功能逻辑这么复杂，一有改动很容易导致其他功能受损", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 21:44:12", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "52010309", "parent": "520103", "title": "需要支持mark记录打标为心得的能力。先手动加个字段gains", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 23:11:47", "ddl": "", "level": 4, "marks": [{"time": "2023-01-30 14:45:39", "content": "发现gains手动标志会被覆盖。fix一下", "gains": null}, {"time": "2023-01-31 19:39:26", "content": "但是这个还是比较高优，现在用着不方便", "gains": null}], "timeline": [["2023-01-30 14:34:35", "2023-01-30 14:49:55"], ["2023-01-31 19:39:12", "2023-01-31 19:50:36"]]}, {"tid": "52010310", "parent": "520103", "title": "把gohome、rest这些，支持成link或者label能力。直接在任务中展示，否则记不住", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 23:13:14", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "52010311", "parent": "520103", "title": "把label和task_id等看看也能不能做成tab补全", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-29 23:13:35", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "52010312", "parent": "520103", "title": "通过环境变量指定KTASK_ROOT，固定在mac的zsh配置。否则经常出现ktask list出现其他部分的情况。今晚就被看到了", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-30 00:13:37", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "5202", "parent": "52", "title": "favorites", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-30 00:15:51", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "11010204", "parent": "110102", "title": "昨晚跟时光沟通了一下，目前搜索优化的方向有点偏（不应该优化算法，先把内容搞起来），投入在降噪方案做得很复杂，就尽量降低投入。另外自己讲的可能也不够清晰。得梳理一下，再跟时光、方文、安頔一起对一下。", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-30 11:25:08", "ddl": "", "level": 4, "marks": [{"time": "2023-01-30 11:30:19", "content": "还是那个问题，写方案之前一定要先沟通想法。避免投入太多无效成本。", "gains": true}], "timeline": []}, {"tid": "52010313", "parent": "520103", "title": "任务没有createTime字段，这点问题很大", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 0, "createTime": "2023-01-30 11:27:23", "ddl": "", "level": 4, "marks": [{"time": "2023-01-30 14:48:20", "content": "发现db.json文件里面有些又有createTime字段.", "gains": null}], "timeline": []}, {"tid": "5204", "parent": "52", "title": "financial", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-30 14:23:01", "ddl": "", "level": 2, "marks": [{"time": "2023-02-02 18:24:20", "content": "得益于这次高优先让ktask命令work起来，再来整理优化代码架构、再来改成db+web模式。关键是先让终端（个人）的数据先采集起来。这才是正确的思路。那么实际上关于金融爬虫的数据获取，至今拖了一年都没启动。如果以前也是这种方式，数据早采集了一年了。", "gains": false}], "timeline": []}, {"tid": "9111", "parent": "91", "title": "理疗", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-30 15:02:48", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-01-11 19:10:00", "2023-01-11 20:20:00"], ["2023-01-30 18:50:00", "2023-01-30 21:15:00"], ["2023-02-06 18:55:00", "2023-02-06 21:10:00"], ["2023-02-06 21:10:00", "2023-02-06 21:10:00"], ["2023-02-17 19:00:00", "2023-02-17 21:20:00"], ["2023-02-24 18:40:00", "2023-02-24 21:20:00"], ["2023-03-03 18:40:00", "2023-03-03 21:15:00"], ["2023-03-17 18:35:00", "2023-03-17 20:00:00"]]}, {"tid": "6103", "parent": "61", "title": "今晚跑到深圳大学总医院做CT检查。快十点半才回到，本来打算一回到马上继续任务工具的功能整理、投入盘点。结果买了粒上皇>板栗、椰子，跟1908群聊、跟彭婷他们聊去欧洲小镇。一边剥了快一个钟板栗吃完，现在准备洗澡。又感觉有点困，中午也没午睡。洗完也很晚了。", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-30 15:05:56", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-01-12 18:40:00", "2023-01-12 23:49:00"]]}, {"tid": "4105", "parent": "41", "title": "整理一下模板/局部模板的推荐、搜索规则", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-30 15:38:59", "ddl": "", "level": 2, "marks": [{"time": "2023-02-14 14:21:21", "content": "刚好安頔问推荐规则，顺便梳理了一下所有策略。有空再看一下上次万军写的新上模板的规则", "gains": false}], "timeline": [["2023-02-14 14:10:00", "2023-02-14 14:24:00"]]}, {"tid": "9302", "parent": "93", "title": "技术问题", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-30 15:53:19", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "930201", "parent": "9302", "title": "为什么script -q -c包装命令，跟直接敲命令，进入 | grep的结果保留颜色编码能力不一样", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-30 15:54:38", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "52010314", "parent": "520103", "title": "随着内容越来越多，考虑如何优雅地缩进，也是值得优化的", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-30 15:55:40", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "52010315", "parent": "520103", "title": "随着内容越来越多，排序也是一个问题。现在有些补充的task，理应排在前面，目前根据taskID或者createTime、ddl、优先级的方式，都不太高效，但又都需要考虑。值得思考。", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-30 16:01:14", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "52010316", "parent": "520103", "title": "关于grep的能力，是否要添加成参数", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-30 16:01:40", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "52010317", "parent": "520103", "title": "添加记录心情mood的能力", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-30 16:05:33", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "52010318", "parent": "520103", "title": "（既然要迁移到db，其他任务就先等这个之后再继续了）随着内容越多越复杂。db.json是否支撑得住。纯命令行是否支撑得住。是否需要service+db+ncurse+web。尤其是可以记录详细的log，现在的方式很容易丢信息", "category": null, "status": "进行中", "description": "", "priority": 0, "quadrant": 0, "createTime": "2023-01-30 16:07:22", "ddl": "", "level": 4, "marks": [{"time": "2023-01-30 16:20:09", "content": "另外，也可以支持web、android等更复杂的场景。比如app提供按钮点击实际睡觉与醒来", "gains": null}, {"time": "2023-02-04 14:27:50", "content": "随着task工具时间管理的威力显现，应该先高优把这个进一步完善一下", "gains": false}], "timeline": []}, {"tid": "52010319", "parent": "520103", "title": "start一个任务时，经常会需要自带mark的，是否可以直接加mark参数，不用额外mark一次", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-30 16:10:25", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "52010320", "parent": "520103", "title": "tid高效重整编排能力", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 0, "createTime": "2023-01-30 16:21:15", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "52010321", "parent": "520103", "title": "mark和mood需要更加复杂的编辑能力。至少类似git commit", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 0, "createTime": "2023-01-30 16:21:29", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "52010322", "parent": "520103", "title": "start命令除了start-time还可以加end-time，相当于在当前任务截出来一段", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 0, "createTime": "2023-01-30 16:22:26", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "1403", "parent": "14", "title": "进坤at了一个模板创建wiki文档失败的问题：copy permission error", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-30 17:39:43", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "4106", "parent": "41", "title": "工作负荷调研问卷", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-30 21:41:22", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-01-30 21:41:26", "2023-01-30 21:46:42"]]}, {"tid": "52010323", "parent": "520103", "title": "bug:如果进行中的任务，先执行finish了，timeline没有填上end。", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 0, "createTime": "2023-01-30 21:49:34", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "6104", "parent": "61", "title": "给爸说怎么切换华硕笔记本键盘，wx沟通低效。。烦。", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-30 22:02:12", "ddl": "", "level": 2, "marks": [{"time": "2023-01-30 22:04:19", "content": "态度表现得很差。起码多家人多一些耐心吧。不要把负面情绪传递到家里。还是要多花时间跟心思给家人，像霞这次事情就很多感悟跟遗憾。", "gains": null}], "timeline": [["2023-01-30 21:46:42", "2023-01-30 22:02:35"]]}, {"tid": "920101", "parent": "9201", "title": "经济工作会议:学习&分析&研究", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-31 11:33:40", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "920102", "parent": "9201", "title": "行业&产业链研究", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-31 11:33:56", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "92010201", "parent": "920102", "title": "C919相关产业链上下游调研", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-31 11:34:55", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "9206", "parent": "92", "title": "经济金融会计理论", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-31 11:36:11", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "920103", "parent": "9201", "title": "广东主要城市规划&产业研究", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-31 11:38:01", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "4107", "parent": "41", "title": "方文发了个CCM内部研发安全自查的文档，要这周抽空处理完", "category": null, "status": "已完成", "description": "", "priority": 0, "quadrant": 0, "createTime": "2023-01-31 14:41:01", "ddl": "", "level": 2, "marks": [{"time": "2023-02-02 16:02:09", "content": "跟万军会议对齐了下", "gains": false}], "timeline": [["2023-02-02 14:32:38", "2023-02-02 15:12:02"], ["2023-02-02 15:40:00", "2023-02-02 15:50:26"]]}, {"tid": "9112", "parent": "91", "title": "歇会", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-31 15:00:20", "ddl": "", "level": 2, "marks": [{"time": "2023-01-31 15:00:52", "content": "吃到胡歌生孩子的大瓜，然后水了会群", "gains": null}, {"time": "2023-01-31 15:36:07", "content": "然后继续水群继续分心。", "gains": null}, {"time": "2023-01-31 15:53:15", "content": "不行了。。还在继续水。继续分心。。集中不了精神。", "gains": null}, {"time": "2023-01-31 15:59:12", "content": "不管什么时候高效做完手头的事情。后面的才会顺，做自己的事才安心、得心应手。否则一步慢步步慢，一步拖步步拖。", "gains": true}, {"time": "2023-01-31 16:31:38", "content": "归根结底还是要让自己进入紧张状态，才能高度集中思考。需要一些压力或者自驱。早点干完活早点干自己的兴趣。", "gains": null}, {"time": "2023-02-01 14:33:35", "content": "群里又跟他们杠起了傻逼才买深圳房子", "gains": false}, {"time": "2023-02-01 14:48:30", "content": "群聊股票还在继续", "gains": false}, {"time": "2023-02-07 16:52:35", "content": "1.开完会，回了几句爸到增城的事情。2.然后就是时光反馈方文说我方案经常跑偏的事情，估计是1on1时我一直强调时光的意见，怕受影响。3.然后就是丘翔咨询华为OD的事情，连续3年降薪，30w到22w，顺便聊了一波情况到现在。", "gains": false}, {"time": "2023-02-07 17:32:51", "content": "准备工作了结果又被聊了会，专注了。", "gains": false}, {"time": "2023-02-07 17:58:04", "content": "又跟汉奕在聊租车，周末华为小镇", "gains": false}, {"time": "2023-02-07 20:15:55", "content": "跟万军闲聊了一会", "gains": false}, {"time": "2023-02-07 21:12:39", "content": "今晚很困，一天工作忙碌充实。", "gains": false}, {"time": "2023-02-09 11:08:18", "content": "聊晓霞回深圳上班的事；聊股票银河证券免五；还有家龙咨询pre测试", "gains": false}, {"time": "2023-02-12 01:02:06", "content": "在群里又宣传了一下自己的时间管理工具，跟彭婷介绍了一番。然后开始mess，刷了会手机，又mess一会。现在继续回主线任务。", "gains": false}, {"time": "2023-02-13 20:21:23", "content": "刚打算工作有病友咨询了下现在的指标情况，又顺便了解了一下环孢素药理。现在工作", "gains": false}, {"time": "2023-02-14 11:21:21", "content": "今天214在跟汉奕发消息", "gains": false}, {"time": "2023-02-14 11:21:31", "content": "和群聊", "gains": false}, {"time": "2023-02-14 15:33:51", "content": "向琪发了最近复查的骨穿报告，看了会，吃了个下午茶。", "gains": false}, {"time": "2023-02-16 20:34:25", "content": "好困。", "gains": false}, {"time": "2023-02-16 21:15:13", "content": "好困。准备回去了。。", "gains": false}, {"time": "2023-02-19 18:21:09", "content": "果然一趟床又hxg。本来只是类了歇一会。但是也不困。然后就困了。然后现在也没什么精神，等外卖，继续干活先。", "gains": false}, {"time": "2023-02-19 23:03:41", "content": "一躺又是两个钟。起码一个多钟浪费在刷脉脉。但是也看到一个不错的视频：对话贾康，讲房地产问题和赋税问题。收录下来", "gains": false}, {"time": "2023-02-19 23:15:47", "content": "剖了个青椰，继续。", "gains": false}, {"time": "2023-02-22 16:56:06", "content": "群里说华师90周年，10年前的事，截图了个朋友圈，回去确认半天。。", "gains": false}, {"time": "2023-03-03 11:58:05", "content": "跟锋英杠起了股票跌90%的理论。。。", "gains": false}, {"time": "2023-03-10 16:12:26", "content": "喉咙隐隐不舒服。头昏昏沉沉。", "gains": false}], "timeline": [["2023-01-31 14:41:39", "2023-01-31 15:01:03"], ["2023-01-31 15:26:54", "2023-01-31 15:36:11"], ["2023-01-31 15:36:11", "2023-01-31 15:53:26"], ["2023-01-31 15:53:26", "2023-01-31 16:14:08"], ["2023-01-31 16:14:08", "2023-01-31 16:18:00"], ["2023-01-31 16:18:00", "2023-01-31 16:32:00"], ["2023-02-01 14:07:00", "2023-02-01 14:33:54"], ["2023-02-01 14:33:54", "2023-02-01 14:48:37"], ["2023-02-02 17:31:19", "2023-02-02 17:48:05"], ["2023-02-04 14:34:49", "2023-02-04 15:30:23"], ["2023-02-07 16:00:41", "2023-02-07 17:24:02"], ["2023-02-07 17:24:02", "2023-02-07 17:33:07"], ["2023-02-07 17:33:07", "2023-02-07 18:02:41"], ["2023-02-07 19:55:00", "2023-02-07 20:16:13"], ["2023-02-07 20:58:56", "2023-02-07 21:30:00"], ["2023-02-08 15:12:00", "2023-02-08 15:26:56"], ["2023-02-08 17:19:45", "2023-02-08 17:39:51"], ["2023-02-09 10:40:00", "2023-02-09 11:31:44"], ["2023-02-09 19:24:42", "2023-02-09 19:55:48"], ["2023-02-12 00:31:31", "2023-02-12 01:03:20"], ["2023-02-13 10:50:00", "2023-02-13 11:31:26"], ["2023-02-13 14:26:00", "2023-02-13 14:44:40"], ["2023-02-13 15:39:12", "2023-02-13 16:01:04"], ["2023-02-13 19:43:44", "2023-02-13 20:21:34"], ["2023-02-14 11:00:00", "2023-02-14 11:23:51"], ["2023-02-14 14:57:00", "2023-02-14 15:34:29"], ["2023-02-14 15:34:29", "2023-02-14 15:38:00"], ["2023-02-15 20:03:49", "2023-02-15 20:10:00"], ["2023-02-16 20:34:21", "2023-02-16 20:51:32"], ["2023-02-16 21:13:15", "2023-02-16 21:24:55"], ["2023-02-18 16:45:29", "2023-02-18 18:09:19"], ["2023-02-19 16:00:41", "2023-02-19 16:30:00"], ["2023-02-19 17:10:00", "2023-02-19 18:23:09"], ["2023-02-19 20:52:09", "2023-02-19 23:17:29"], ["2023-02-21 19:37:43", "2023-02-21 19:37:43"], ["2023-02-22 16:20:18", "2023-02-22 16:26:09"], ["2023-02-22 16:26:09", "2023-02-22 16:56:12"], ["2023-02-26 15:16:21", "2023-02-26 15:39:46"], ["2023-02-27 17:19:44", "2023-02-27 17:50:00"], ["2023-03-02 16:00:00", "2023-03-02 16:16:32"], ["2023-03-02 20:34:43", "2023-03-02 20:43:10"], ["2023-03-03 11:35:00", "2023-03-03 11:58:13"], ["2023-03-06 20:39:13", "2023-03-06 21:20:17"], ["2023-03-10 16:12:10", "2023-03-10 16:15:08"], ["2023-03-10 16:15:08", "2023-03-10 16:15:13"], ["2023-03-13 18:42:23", "2023-03-13 19:22:02"], ["2023-03-14 16:43:32", "2023-03-14 16:48:11"], ["2023-03-14 19:44:07", "2023-03-14 19:49:34"]]}, {"tid": "920201", "parent": "9202", "title": "英语口语训练", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-31 16:41:20", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "920301", "parent": "9203", "title": "欧美知名老建筑涉猎", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-31 16:41:58", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "92010202", "parent": "920102", "title": "房地产行业研究（找找论文）", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-31 16:51:19", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "9207", "parent": "92", "title": "经济&政治制度研究", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-31 16:53:00", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "920701", "parent": "9207", "title": "地方政府发债机制与城投的历史了解", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-31 16:53:24", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "920601", "parent": "9206", "title": "世界经济学(关红凌))", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-31 16:54:25", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "6105", "parent": "61", "title": "冰女让帮忙考阿里云证书抵税，通知家里人，然后帮她跟少优考了。自己也考个。中间吃零食。前后花了一个钟。。", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-01-31 21:33:54", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-01-31 20:32:00", "2023-01-31 21:34:12"]]}, {"tid": "6106", "parent": "61", "title": "冰女跟少优买了甘蔗来啃", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-01 11:16:54", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-01-31 22:35:00", "2023-01-31 23:27:00"]]}, {"tid": "5201030401", "parent": "52010304", "title": "除了加上ddl，最好需要一个每日编排的功能，可以展示今日任务，这样比较直观。像以前印象笔记的方式。但是关联绑定到一条任务，不像印象笔记那样copy漏", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-01 11:28:58", "ddl": "", "level": 5, "marks": [{"time": "2023-02-01 13:42:47", "content": "特别是现在任务太多了之后，反倒比之前用印象笔记迷糊了。就是每天一来没有先明确几个高优任务。", "gains": false}], "timeline": []}, {"tid": "92010203", "parent": "920102", "title": "军工相关产业链和企业，对沙特出口的部分", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-01 13:41:28", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "4108", "parent": "41", "title": "Collabration All Hands", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-01 16:25:39", "ddl": "", "level": 2, "marks": [{"time": "2023-02-01 16:38:38", "content": "一边all hands，一边mess水群，一边搞了一点点方案", "gains": false}], "timeline": [["2023-02-01 15:00:00", "2023-02-01 16:38:45"]]}, {"tid": "920602", "parent": "9206", "title": "投资心得", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-02 11:24:09", "ddl": "", "level": 3, "marks": [{"time": "2023-02-02 11:51:22", "content": "发行注册制被套路心得：\n1. 证券板块已经连续涨了很多天。等到注册制消息对公众发布，那么得到信息的就是最广大散户群众。在这种赚信息差的市场，以及收割散户（反着买）的市场，这两个点都应该告诉自己谨慎。\n2. 事实上自己的第六感有时候很敏锐，已经设想了这样一种可能。就是高开之后暴跌，所以应该等等开盘前，看看挑着底部买。但是，最终还是另一种贪婪与恐惧战胜了理智与直觉。恐惧的是一开盘直接暴涨，没机会再买入。但是即使不信直觉，从理智分析看，这种情况比起高开暴跌的概率也是很低的。\n3. 操作上还不够熟练，出现了事故。按照9:25前显示的价格是0.993，挂了1w单。但是25-30分显示的价格是0.995，为了促进马上成交，30分一开始马上购入1w单，实际上潜意识以为是直接一路涨，所以忽视了前面一单。。这简直就是不理智战胜了理智分析。结果暴跌，两单瞬间成交。造成double成交事故。\n4. 开盘一路暴跌到0.960，在0.968的位置补了1w。然后大概在这个位置。如果不断加仓来短期回本、再赶紧降低仓位，还是可以做到的。就在准备继续0.959继续加仓的时候，突然意识到一个问题。今天怎么拉低成本都是卖不出去的。应该尽量减少操作，看看明天的情况，再来操作降仓位。\n5. 不要天天无脑盯盘、跟着价格波动干扰情绪，还是要多些理智分析。\n6. 事实证明，每次听锋英的(说不重仓不足以改变什么，这跟之前自己在高位搞成重仓一样)、看证券app推送新闻的(滞后的公众信息，跟散户同步调)，最终都是巨亏。\n7.仓位策略不要失去理智，谨小慎微。不要因为看好某一只，导致整体仓位脱离自己原策略预订的范围。但是仓位控制应该以区间分段，有一定操作空间，不是平滑的，否则操作很受限。\n", "gains": true}, {"time": "2023-02-02 17:43:34", "content": "假如接下来有重大回调，那么现在最大的问题不是因为大力买入证券，而是因为买入了证券导致仓位变重了。从长期看，不管买入哪个，涨的概率大。但是仓位重，接下来如果有回调，便不好加仓。很被动。", "gains": true}, {"time": "2023-02-02 17:46:04", "content": "被阴了一波，实在有些不爽。不过理智分析，既然属于诱骗，那么说明短期不会因为这个诱骗的利好有所上涨。明天该减仓位还是得抓住机会赶紧让仓位降回正常，即使些许亏损也要及时止损，不要妄想回到今天冲高诱骗的位置。", "gains": true}, {"time": "2023-02-03 11:16:32", "content": "不要太多时间看盘，容易不够理智瞎操作，集中买入或者卖出。除了震荡市做T，大部分情况下一天只需要合适时机操作一次就行了，即时做短线还是要至少跨几天的周期来做交易。既省时间，而且那才是真正能套到差价。", "gains": true}, {"time": "2023-02-03 11:23:31", "content": "每次都在3200+点的时候越加越重，一半是贪婪，一半是不懂得放平心态等机会。总之，人性克服上还不够。一旦设定了策略就要严格遵守，不要任由人性的弱点奴役。", "gains": true}], "timeline": [["2023-02-02 11:25:48", "2023-02-02 12:00:54"], ["2023-02-03 11:13:05", "2023-02-03 11:24:00"]]}, {"tid": "4109", "parent": "41", "title": "后端fg清理", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-02 15:37:37", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "52010324", "parent": "520103", "title": "bug: gains参数只能后置，不能放前面，应该跟min_value=0的用法有关系。看看如何把min改成equal的关系", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-02 17:12:55", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "5205", "parent": "52", "title": "treetheme", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-02 17:15:59", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "520501", "parent": "5205", "title": "上次开发机迁移之后，vim-go的配置似乎有点问题。。这个旧的开发机也没把配置保留下来。得完善一下treetheme的vim-go配置", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-02 17:16:12", "ddl": "", "level": 3, "marks": [{"time": "2023-02-02 17:25:27", "content": "初步定位了一下，估计是goimports这些命令没有按照。vim-go跟vim的脚本导入没有问题", "gains": false}], "timeline": [["2023-02-02 17:10:57", "2023-02-02 17:31:19"]]}, {"tid": "52010325", "parent": "520103", "title": "gains的marks在展示中应该有所标志。老是忘记加上--gains参数。但是最好的解决方式还是把mark跟gain的命令区分开。底层数据可以一致", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-02 17:47:09", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "120408", "parent": "1204", "title": "技术评审", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-02 17:55:10", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-02-02 17:56:06", "2023-02-02 18:20:05"]]}, {"tid": "52010326", "parent": "520103", "title": "finish这种命令支持一次性多个taskid", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-02 18:21:16", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "520401", "parent": "5204", "title": "高优让终端数据采集起来", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 0, "createTime": "2023-02-02 18:24:35", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "52010327", "parent": "520103", "title": "添加一个任务时，如果是叶子节点，要默认增加一个00id的子节点继承父节点内容。", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-02 19:51:26", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "1404", "parent": "14", "title": "颖鑫反馈生态模板变成套组模板，template_type不对", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-02 20:03:48", "ddl": "", "level": 2, "marks": [{"time": "2023-02-02 21:13:02", "content": "定位到又是重构后打包逻辑的bug，上次fix了一个bug后又出现另一个bug", "gains": false}, {"time": "2023-02-02 21:25:26", "content": "修复完。SCM编译中。", "gains": false}, {"time": "2023-02-02 21:33:36", "content": "发完PPE验证成功，明天再上线了。", "gains": false}, {"time": "2023-02-02 21:44:37", "content": "发起MR，等打车中。。", "gains": false}, {"time": "2023-02-02 21:50:08", "content": "发完MR", "gains": false}, {"time": "2023-02-06 10:56:59", "content": "先发个hotfix，赶在班车之前。连同1402的fix一起上了先。", "gains": false}, {"time": "2023-02-06 11:04:14", "content": "并行走流程，并发120406开发任务。", "gains": false}], "timeline": [["2023-02-02 20:00:00", "2023-02-02 21:26:36"], ["2023-02-02 21:27:05", "2023-02-02 21:33:48"], ["2023-02-02 21:40:00", "2023-02-02 21:59:01"], ["2023-02-06 10:56:31", "2023-02-06 11:04:23"]]}, {"tid": "4110", "parent": "41", "title": "辣可可聚餐&汤崎团建", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-03 11:39:10", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-02-03 11:55:00", "2023-02-03 22:20:00"]]}, {"tid": "9113", "parent": "91", "title": "hxg", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-04 01:18:01", "ddl": "", "level": 2, "marks": [{"time": "2023-02-04 02:05:12", "content": "今晚回来终究是hxg。实在难忍，发泄完反而可以更加专心。", "gains": false}, {"time": "2023-02-04 23:03:16", "content": "垃圾腾讯云要开始工作又内存占满卡死。然后刚吃饱，又累不想收拾打扫，就又躺床上去了。", "gains": false}, {"time": "2023-02-14 01:46:41", "content": "1点困到快睡过去，又不知怎么hxg到现在，作践自己。睡吧。", "gains": false}, {"time": "2023-03-09 00:39:10", "content": "明明很困很累。早点洗个澡清醒下，要么写绩效，要么做会的事情，可以早点睡。结果还是一堆乱七八糟无孔不入的东西刺激自己的神经。自制力太差了。真的有毒。克服成瘾心理障碍有多难。", "gains": false}, {"time": "2023-03-17 23:46:24", "content": "难得今晚没有理疗很晚，八点多回来，而且还有很强的状态想要加速推进。但终究还是hxg。主要怕憋着也影响状态。", "gains": false}], "timeline": [["2023-02-03 23:01:00", "2023-02-04 00:38:00"], ["2023-02-04 20:02:00", "2023-02-04 21:07:00"], ["2023-02-08 01:40:00", "2023-02-08 02:20:00"], ["2023-02-12 11:00:00", "2023-02-12 11:45:00"], ["2023-02-14 01:00:00", "2023-02-14 01:46:00"], ["2023-02-19 16:30:00", "2023-02-19 17:10:00"], ["2023-03-08 22:10:00", "2023-03-08 23:17:00"], ["2023-03-17 21:30:00", "2023-03-17 22:29:00"]]}, {"tid": "52010328", "parent": "520103", "title": "需要全面实现一下，gains、marks等功能。包括记录心情/随想、感悟/心得、mark/comment、日记流水账、学习/工作/技术/读书笔记。关联富文本、印象笔记等具体的笔记功能。", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-04 01:40:48", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "520402", "parent": "5204", "title": "高优把交易记录的excel导入到数据库中来使用。快速把分析工具搭建起来。", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 0, "createTime": "2023-02-04 02:11:52", "ddl": "", "level": 3, "marks": [{"time": "2023-02-10 01:03:00", "content": "重温了之前的一些工作。之前搞过stock-crawler工具，往financial-mysql写入日K线数据", "gains": false}, {"time": "2023-02-10 01:05:31", "content": "相关的内容之前在《【项目】stock-analysis》整理，并且交易记录xls也是这里面整理的", "gains": false}, {"time": "2023-02-10 01:15:36", "content": "做了一些economy到stock-crawler仓库的收敛对比。以及review之前的《【项目】stock-analysis》，发现之前对仓库收敛做过以此对比：\neconomy：nodejs，accounts-mysql，爬东方财富网的基金数据。旧的pcb数据。\nweb-crawler：python+scrapy，accounts-mysql，最新的爬全部pcb数据。\nstockmarket：nodejs，accounts-mysql，又爬了东方财富网的基金+股票数据。并且最近升级了v2接口。\n之前economy和stockmarket的爬虫，是在《【项目】economy》里面使用的。关于web-crawler，是在《【项目】python爬虫》里面的。不管后续要迁移到哪个仓库，用什么技术栈。现在主要快速实现爬数据就行了，并且固定这三个仓库的爬虫地位。\n所以接下来要做的事情，是先把股票数据爬进accounts-mysql。\n最快的是直接用stockmarket，回到《【项目】economy》，然后再在本项目进行分析。", "gains": false}, {"time": "2023-02-10 01:20:11", "content": "之前写了用accounts-mysql，实际上这个应该适合日常账单记录的收敛。金融数据和交易记录，最好收敛到financial-mysql", "gains": false}], "timeline": [["2023-02-10 00:50:20", "2023-02-10 01:20:00"]]}, {"tid": "920702", "parent": "9207", "title": "整理一下都有哪些平台是政府发布第一手重要信息、文件的，并结合平时各种吹的自媒体的信息，看看都是哪些信息源头。以后自己关注。", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 0, "createTime": "2023-02-04 02:24:56", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "********", "parent": "520103", "title": "今天团建，终于把最后两级狂飙看完了。最近疯狂投入了十几个小时的时间。以后看电视、看电影、看书等，还是要单独拎出来统计时间。但是，这个也是合并在休闲等事情里面的。所以 >更合适的是以笔记或者特殊tag的方式存在？比如既是在进行某个休息任务，同时又针对某个主题的并行统计。主要统计这个剧、或者这本书花了多少时间。", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-04 02:34:15", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "5206", "parent": "52", "title": "data-synchronizer", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-04 14:25:38", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "520601", "parent": "5206", "title": "随着stx2数据越来越多，系统越来越完善。先演练一遍数据备份。", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-04 14:26:24", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-02-04 23:15:50", "2023-02-04 23:43:30"], ["2023-02-05 00:54:47", "2023-02-05 01:42:46"]]}, {"tid": "5201031801", "parent": "52010318", "title": "先实现golang后端rpc服务", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-04 14:33:26", "ddl": "", "level": 5, "marks": [], "timeline": [["2023-02-04 15:44:32", "2023-02-04 15:59:00"]]}, {"tid": "5201031802", "parent": "52010318", "title": "实现web页面，web有更丰富的各种树状缩进组件，不像android成本那么高。", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-04 14:34:16", "ddl": "", "level": 5, "marks": [{"time": "2023-02-19 14:36:30", "content": "先调研一下用什么组件", "gains": false}, {"time": "2023-02-19 14:37:34", "content": "还需要考虑到四者联动的跳转，还有考虑移动端和web的界面适配。", "gains": false}, {"time": "2023-02-19 14:39:39", "content": "这块上面尽量缩减浪费时间纠结，用最低成本，选择自己熟悉的、可满足的技术栈。目前看直接React+Ant Design", "gains": false}], "timeline": []}, {"tid": "520103180101", "parent": "5201031801", "title": "初始化db建表", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-04 15:59:21", "ddl": "", "level": 6, "marks": [], "timeline": [["2023-02-04 15:59:00", "2023-02-04 17:04:37"]]}, {"tid": "520103180104", "parent": "5201031801", "title": "实现基础接口", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-04 15:59:31", "ddl": "", "level": 6, "marks": [{"time": "2023-02-25 00:13:24", "content": "接下来开始把ktast原本的rust核心业务逻辑，迁移到golang中以api的形式", "gains": false}, {"time": "2023-02-25 13:29:17", "content": "又浪费不少时间在纠结垃圾golang的gorm Error怎么判断。过滤dup prikey的错误。毫无意义。最后还是只能string.Contains判断。而且gorm还会自动打印Error。", "gains": false}, {"time": "2023-02-25 13:29:45", "content": "赶紧快速调试，把功能代码抓紧迁移完。", "gains": false}, {"time": "2023-02-25 14:12:46", "content": "又搞了半天。。调试链路都没搞通。。rpc服务端口都忘记怎么搞了。整理完，还是dev模式的rpc链路不同。", "gains": false}, {"time": "2023-02-25 14:14:24", "content": "gin.Context的Get(rpc_client)一直返回false", "gains": false}, {"time": "2023-02-25 14:18:51", "content": "看起来有可能是thrift服务监听的端口仅在ipv6", "gains": false}, {"time": "2023-02-25 14:20:33", "content": "go生态还是垃圾。一个写法完全搜不到任何资料。页不知道gin.Context的Get rpc_client怎么来的。", "gains": false}, {"time": "2023-02-25 14:23:11", "content": "原来是自己设置的。。gin.Context只是提供了Set和Get的中间件用法。", "gains": false}, {"time": "2023-02-25 14:24:34", "content": "还是自己头脑不清醒、定位效率低。一开始看到Set方法，就可以锁定问题了。\n另外一方面，搜索不到任何东西，也大概率可以锁定了。\n=> 关键问题在于自己情绪控制太差，一有挫折优先从外部找原因。。", "gains": true}, {"time": "2023-02-25 14:44:57", "content": "又20min在纠结怎么完善异常处理逻辑细节。。用最简单的流程处理就行了。", "gains": false}, {"time": "2023-02-25 14:52:35", "content": "实际上是自己处理抽象逻辑思路没那么活跃了，不过静下心思考了一下，也改好了。", "gains": false}, {"time": "2023-02-25 14:57:33", "content": "总算调通dev的debug链路了。。", "gains": false}, {"time": "2023-02-25 15:40:49", "content": "总算完成Add接口。", "gains": false}, {"time": "2023-02-25 15:41:27", "content": "实现起来确实比rust快速很多。。一大堆转换逻辑。而且是操作json", "gains": false}, {"time": "2023-02-25 16:18:19", "content": "又踩gin的垃圾坑。。BindQuery不起作用。", "gains": false}, {"time": "2023-02-25 16:20:51", "content": "原来是idl需要针对gin添加form tag。。。参考: https://github.com/gin-gonic/gin/issues/1513", "gains": false}, {"time": "2023-02-25 16:38:26", "content": "list任务树接口也写好了一部分了。接下来处理一下不指定root参数的全量树。", "gains": false}, {"time": "2023-02-25 16:49:17", "content": "完成root=0的完整接口", "gains": false}, {"time": "2023-02-25 20:10:30", "content": "继续Start、Mark命令", "gains": false}, {"time": "2023-02-25 20:14:30", "content": "很多action操作可以合并为一个接口", "gains": false}, {"time": "2023-02-25 22:58:51", "content": "总算把Start命令的时间段截取和更新也写完了。", "gains": false}, {"time": "2023-02-26 00:14:16", "content": "继续完善start命令的EndTime参数", "gains": false}, {"time": "2023-02-26 14:49:18", "content": "踩了很久坑，发现重新建表后的timelines的startTime出现了自动更新的属性。。on update CURRENT_TIMESTAMP 不知道怎么来的。。dbmanager的建表语句貌似没有。。", "gains": false}, {"time": "2023-02-26 14:52:43", "content": "难道NOT NULL默认自带这个属性。。改成NULL DEFAULT NULL试试。", "gains": false}, {"time": "2023-02-26 14:53:37", "content": "改成NULL DEFAULT NULL倒是不会了。不纠结了，也没时间求证了，能解决就快速推", "gains": false}, {"time": "2023-02-26 14:57:09", "content": "不带endTime的几种情况算是调完成了。", "gains": false}, {"time": "2023-02-26 15:12:33", "content": "关于带endTime的，各种边界和宽度覆盖的情况，十分之复杂。紧靠罗列逻辑、各种分支特殊处理，有些难以维护，理解上也困难。最好在方案梳理一下各种边界case，看看如何抽象成一个模块，把复杂细节封装起来。\n另外，这么复杂的边界case，全部人工来一个个用例验证，也是不合理，把复杂细节封装起来之后，需要用单测来保证逻辑。", "gains": false}, {"time": "2023-02-26 15:13:28", "content": "反正这周末是搞不完了。待会要去大姐家，还有公司的代码没写完。", "gains": false}, {"time": "2023-02-26 15:40:11", "content": "与其又坐着浪费了20min。。还不如能写多少是多少。", "gains": false}, {"time": "2023-02-26 16:19:34", "content": "时间竟然这么快。。👉又过了40min，竟然仅仅是在方案文档整理了各种逻辑case。发现统一梳理，确实清晰很多，case也确实多，另外梳理出来几个前置限制的条件，比如时间连续性，参数区间不支持零宽度等等", "gains": false}, {"time": "2023-02-27 00:05:59", "content": "自己的事情太多了永远做不完，也不要一味地降低工作的优先级。有时候快一步步步得心应手，慢一步步步被动。还是难得抽空去做个总结、复盘，复盘一下现在的数据情况。和后面的计划，否则没有计划、一周又过得乱糟糟的任务安排。", "gains": true}, {"time": "2023-02-28 23:46:53", "content": "现在实现start命令的case通用模块", "gains": false}, {"time": "2023-03-04 00:06:17", "content": "接下来实现logic3分支的Cases", "gains": false}, {"time": "2023-03-04 16:04:37", "content": "又花了两个钟。总算实现完复杂的区间操作。。前后花了15hour。比工作内容还多。。。接下来还需要完整边界case测试。", "gains": false}, {"time": "2023-03-04 16:22:38", "content": "build完成", "gains": false}, {"time": "2023-03-04 18:02:07", "content": "下一步完善各种case单测", "gains": false}, {"time": "2023-03-04 20:26:10", "content": "看来没什么mock db表的东西，合理的方式还是自己新建临时表mock数据", "gains": false}, {"time": "2023-03-04 21:25:07", "content": "又浪费一个钟在mock db表。垃圾gorm又一堆坑。", "gains": false}, {"time": "2023-03-04 21:27:04", "content": "AutoMigrate接口版本已经旧了。直接返回就是err，不需要Error", "gains": false}, {"time": "2023-03-04 21:37:29", "content": "完成mockdb的准备", "gains": false}, {"time": "2023-03-04 21:38:18", "content": "现在开始mock timelines数据", "gains": false}, {"time": "2023-03-04 21:52:27", "content": "又踩了半天垃圾goconvey的坑。不知道公司的框架怎么出发的。。反正直接执行go test，并不会触发Convey的回调。看到原生的go单测，也只是执行func TestXXX的用例", "gains": false}, {"time": "2023-03-04 21:54:47", "content": "傻逼了。。原来是TestMain需要m.Run()。。。参考go test的teardown和setup、TestMain部分", "gains": false}, {"time": "2023-03-04 21:55:47", "content": "参考goconvey基础文档: https://pkg.go.dev/github.com/smartystreets/goconvey/convey#Convey", "gains": false}, {"time": "2023-03-04 22:10:30", "content": "完成单测初始化清timeline表操作。。真是繁琐。。接下来才可以真正实现测试cases", "gains": false}, {"time": "2023-03-04 23:47:50", "content": "继续补复杂case单测", "gains": false}, {"time": "2023-03-05 02:08:38", "content": "补充完4个逻辑流程的各种case的用例。确实测出很多问题。这些问题不通过单测，想要手动触发各种case来验证，是及其耗时间的。", "gains": false}, {"time": "2023-03-05 02:24:56", "content": "果然发现了很多bug。。明天再改了。", "gains": false}, {"time": "2023-03-05 16:09:19", "content": "实际模型搞这么复杂，是不是因为考虑了零宽度区间。这个一开始设计上不保留会合适些。", "gains": false}, {"time": "2023-03-05 17:11:22", "content": "总算完成所有用例的修复。虽然还有隐藏的问题就是这些步骤没有在事务之内。如果中间挂了，会有脏数据。", "gains": false}, {"time": "2023-03-05 17:15:54", "content": "最复杂的start命令截取完成了。。接下来就是可以快速完善接口流程 + rust命令工具改造了。", "gains": false}, {"time": "2023-03-05 17:30:17", "content": "接下来把mark命令拆解成各种类型的命令", "gains": false}, {"time": "2023-03-05 18:20:39", "content": "mark相关接口也完成了", "gains": false}], "timeline": [["2023-02-25 00:12:34", "2023-02-25 00:12:34"], ["2023-02-25 00:45:18", "2023-02-25 00:52:34"], ["2023-02-25 12:52:03", "2023-02-25 16:56:39"], ["2023-02-25 20:10:22", "2023-02-25 21:21:39"], ["2023-02-25 21:29:04", "2023-02-25 22:59:30"], ["2023-02-26 00:10:23", "2023-02-26 01:27:27"], ["2023-02-26 13:13:47", "2023-02-26 13:17:13"], ["2023-02-26 14:05:34", "2023-02-26 15:16:21"], ["2023-02-26 15:39:46", "2023-02-26 16:21:00"], ["2023-02-28 00:16:31", "2023-02-28 00:16:31"], ["2023-02-28 00:42:49", "2023-02-28 00:46:10"], ["2023-02-28 23:39:56", "2023-03-01 00:31:46"], ["2023-03-02 00:21:42", "2023-03-02 00:55:48"], ["2023-03-02 23:10:07", "2023-03-02 23:10:07"], ["2023-03-02 23:25:50", "2023-03-02 23:25:50"], ["2023-03-02 23:40:15", "2023-03-03 00:32:27"], ["2023-03-04 00:04:51", "2023-03-04 01:06:34"], ["2023-03-04 14:09:23", "2023-03-04 16:24:54"], ["2023-03-04 18:01:55", "2023-03-04 18:14:02"], ["2023-03-04 20:15:57", "2023-03-04 22:10:42"], ["2023-03-04 23:47:34", "2023-03-05 00:42:47"], ["2023-03-05 01:16:23", "2023-03-05 02:08:48"], ["2023-03-05 02:18:16", "2023-03-05 02:25:02"], ["2023-03-05 15:24:53", "2023-03-05 16:18:10"], ["2023-03-05 16:48:46", "2023-03-05 17:16:10"], ["2023-03-05 17:29:55", "2023-03-05 18:22:18"], ["2023-03-05 22:52:09", "2023-03-05 23:02:47"]]}, {"tid": "520103180103", "parent": "5201031801", "title": "搭建后端服务和框架代码", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-04 16:26:08", "ddl": "", "level": 6, "marks": [{"time": "2023-02-19 11:15:15", "content": "之前db建表了，服务搭建一半，现在继续，然后再导入数据", "gains": false}, {"time": "2023-02-19 11:32:36", "content": "元模型可以设计成study的Category？但是元模型的预置结构，怎么表现呢。所以不是简单的关联关系，而是类似于模板预置的意思，还是加多一个字段。", "gains": false}, {"time": "2023-02-19 13:24:00", "content": "又开始纠结这些学习提升的任务，哪些划分为框架、模型抽象分类，哪些作为细分任务。实际上这种纠结没有意义。现在的分化、还有细分任务，无非就是自己计划学习的很多事情的分类。他们是一个整体。不管事一级，还是最后一级，都有可能不断调整变化。", "gains": false}, {"time": "2023-02-19 14:26:50", "content": "总算把学习相关的初始任务数据构建完成。", "gains": false}, {"time": "2023-02-19 14:35:12", "content": "接下来先试图实现简单可用的web页面。并且把基础接口调通。再把rust工具切换到http调用来。", "gains": false}], "timeline": [["2023-02-04 17:04:37", "2023-02-04 17:37:27"], ["2023-02-19 11:14:59", "2023-02-19 11:51:18"], ["2023-02-19 12:34:08", "2023-02-19 14:36:18"]]}, {"tid": "910801", "parent": "9108", "title": "收拾衣服", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-04 23:04:20", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-02-04 21:07:00", "2023-02-04 21:07:00"], ["2023-02-04 21:27:00", "2023-02-04 22:14:00"]]}, {"tid": "910802", "parent": "9108", "title": "洗咖啡机碗筷擦桌子", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-04 23:05:09", "ddl": "", "level": 3, "marks": [{"time": "2023-02-13 00:37:02", "content": "上了个厕所、洗了咖啡机、顺便简单拖一下厨房和客厅，现在洗澡", "gains": false}, {"time": "2023-03-04 23:46:53", "content": "顺便拖了地。剖了个青椰。现在继续。", "gains": false}], "timeline": [["2023-02-04 21:07:00", "2023-02-04 21:27:00"], ["2023-02-13 00:05:20", "2023-02-13 00:37:15"], ["2023-02-19 00:45:03", "2023-02-19 00:57:05"], ["2023-02-20 00:25:37", "2023-02-20 00:37:07"], ["2023-02-25 22:59:30", "2023-02-25 23:10:59"], ["2023-02-26 23:17:30", "2023-02-26 23:33:03"], ["2023-03-04 23:05:49", "2023-03-04 23:47:34"], ["2023-03-05 21:33:13", "2023-03-05 21:56:29"]]}, {"tid": "910803", "parent": "9108", "title": "收拾桌面和柜子", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-04 23:05:44", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "94", "parent": "5", "title": "周末生活", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-06 00:13:05", "ddl": "", "level": 1, "marks": [], "timeline": []}, {"tid": "9401", "parent": "94", "title": "剪头发+建城家烤肉+看流浪地球", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-06 00:13:59", "ddl": "", "level": 2, "marks": [{"time": "2023-02-06 00:19:53", "content": "一大早自己醒了，赖到十点多，赶在10:55到理发店剪完差不多11:30出发。中间地铁一波三折，又9号线转7号线跑错了错过一趟，然后7号线又做过了两站，10分钟搞成25分钟。。\n然后13:00刚好到，吃烤肉到16:00，出去公园散步消食，走了很久，快17:30坐地铁回去，18:30到南油。\n19:00电影票看流浪地球2，影院简直垃圾到爆，又热又困。不过今天走了一天，睡得又少，昨晚又hxg，今天又没喝咖啡。看电影一个小时就撑不住了，频频快睡着，错过很多片段，大脑实在困硬撑。挨到22:00看完了。\n回来点了个粥吃，虽然还很饱。\n这个周末，本来还打算补一下公司落下的进度的，结果还是什么都来不及干。\n", "gains": false}], "timeline": [["2023-02-05 10:20:00", "2023-02-05 23:29:00"]]}, {"tid": "4304", "parent": "43", "title": "班车发布", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-06 15:29:29", "ddl": "", "level": 2, "marks": [{"time": "2023-02-06 15:30:04", "content": "遇到一个ftf的问题，看来得花点时间", "gains": false}, {"time": "2023-02-06 15:51:18", "content": "其实完全可以异步问一下，并行进行。因为压根不用占用时间。问一下万军，只要部署一下ppe就行了，也可以跳过。", "gains": false}], "timeline": [["2023-02-06 15:29:37", "2023-02-06 15:30:20"], ["2023-02-06 15:50:38", "2023-02-06 15:57:44"]]}, {"tid": "120409", "parent": "1204", "title": "补单测+发MR", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-06 15:48:07", "ddl": "", "level": 3, "marks": [{"time": "2023-02-06 16:49:02", "content": "垃圾goconvey得又踩了十几分钟坑。。ShouldContainKey不知道为什么一直不按照自己理解的方式成功。。垃圾玩意。", "gains": false}, {"time": "2023-02-07 11:47:00", "content": "修复MR单测失败", "gains": false}], "timeline": [["2023-02-06 15:48:13", "2023-02-06 15:50:38"], ["2023-02-06 16:20:55", "2023-02-06 17:02:23"], ["2023-02-06 17:21:23", "2023-02-06 17:43:24"], ["2023-02-06 17:50:00", "2023-02-06 18:08:59"], ["2023-02-07 00:03:00", "2023-02-07 00:08:32"], ["2023-02-07 11:45:50", "2023-02-07 11:50:58"], ["2023-02-07 11:57:33", "2023-02-07 12:00:00"]]}, {"tid": "120410", "parent": "1204", "title": "部署boe+提测文档", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-06 16:11:18", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-02-06 16:11:24", "2023-02-06 16:20:55"]]}, {"tid": "9303", "parent": "93", "title": "golang", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-06 16:59:37", "ddl": "", "level": 2, "marks": [{"time": "2023-02-06 17:01:39", "content": "垃圾goconvey全部是interface判断，int64和int的判断，因为type不对导致断言失败", "gains": false}], "timeline": []}, {"tid": "1405", "parent": "14", "title": "lark搜索不到头脑风暴", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-06 17:02:15", "ddl": "", "level": 2, "marks": [{"time": "2023-02-06 17:21:08", "content": "对了十几分钟就对个问题的背景。。有空再看了", "gains": false}, {"time": "2023-02-10 17:30:11", "content": "整了半天发现不能复现了", "gains": false}, {"time": "2023-02-10 17:32:59", "content": "docs旧版才有。", "gains": false}, {"time": "2023-02-10 17:36:07", "content": "先跟踪一下代码逻辑", "gains": false}, {"time": "2023-02-13 11:39:56", "content": "这里的代码是旧的推荐策略，比万军还在的那版", "gains": false}, {"time": "2023-02-13 14:45:34", "content": "先确认运营平台配置的模板内容，看看是配置问题还是模板问题。刚刚搞定飞书云的bug", "gains": false}, {"time": "2023-02-13 14:48:46", "content": "底部模板推荐配置能match啥功能doc和docx类型。那估计是搜索的时候，没有把doc的加进去，应该是被过滤掉了，因为doc和docx很多重复模板", "gains": false}, {"time": "2023-02-13 15:13:34", "content": "定位清楚，因为对应模板没有上到分类，所以搜不出来。另外发现一个前端docx_template=1的bug。", "gains": false}, {"time": "2023-02-13 15:33:49", "content": "归因完。走完meego单，沟通了一下前端参数问题的反馈。现在继续。", "gains": false}, {"time": "2023-02-14 14:34:43", "content": "发现QA直接把meego单删了。。跟时光反馈这个问题，又耗了10min", "gains": false}], "timeline": [["2023-02-06 17:02:23", "2023-02-06 17:21:23"], ["2023-02-10 17:22:28", "2023-02-10 18:00:00"], ["2023-02-13 11:31:26", "2023-02-13 12:00:00"], ["2023-02-13 14:26:00", "2023-02-13 14:26:00"], ["2023-02-13 14:44:40", "2023-02-13 15:39:12"], ["2023-02-14 14:24:00", "2023-02-14 14:35:20"]]}, {"tid": "4305", "parent": "43", "title": "1on1前梳理", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 00:02:33", "ddl": "", "level": 2, "marks": [{"time": "2023-02-07 11:38:46", "content": "垃圾苹果又浪费不少时间在搞随航。。usb怎么都不行，升级ipad最新系统也不行。重启也不行。只能用wifi。", "gains": false}], "timeline": [["2023-02-07 00:02:38", "2023-02-07 00:03:00"], ["2023-02-07 00:08:32", "2023-02-07 00:14:20"], ["2023-02-07 00:40:33", "2023-02-07 01:09:28"], ["2023-02-07 10:43:26", "2023-02-07 11:45:50"], ["2023-02-27 23:45:52", "2023-02-28 00:16:31"]]}, {"tid": "120411", "parent": "1204", "title": "提测+沟通", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 11:49:41", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-02-07 11:50:58", "2023-02-07 11:57:33"]]}, {"tid": "4306", "parent": "43", "title": "方文1on1", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 15:09:45", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-02-07 14:10:00", "2023-02-07 15:10:27"]]}, {"tid": "120412", "parent": "1204", "title": "测试用例评审", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 15:14:10", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-02-07 15:14:17", "2023-02-07 15:25:22"]]}, {"tid": "1315", "parent": "13", "title": "模板导入导出(双品牌&KA)", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 15:22:55", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "131501", "parent": "1315", "title": "backup交接会议(进坤)", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 15:25:19", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-02-07 15:25:22", "2023-02-07 16:00:41"]]}, {"tid": "120413", "parent": "1204", "title": "发现模板使用量的fake逻辑漏加了", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 17:23:04", "ddl": "", "level": 3, "marks": [{"time": "2023-02-07 20:16:50", "content": "开始发起CR", "gains": false}], "timeline": [["2023-02-07 17:24:02", "2023-02-07 17:24:02"], ["2023-02-07 17:33:07", "2023-02-07 17:33:07"], ["2023-02-07 19:02:23", "2023-02-07 19:55:00"], ["2023-02-07 20:16:13", "2023-02-07 20:23:02"]]}, {"tid": "120414", "parent": "1204", "title": "关于套组模板不参与文档类型列表排序占坑位的问题", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 17:36:48", "ddl": "", "level": 3, "marks": [{"time": "2023-02-10 14:41:42", "content": "验证后发现线上数据就有几个套组模板占着坑位。。亟待解决了。。", "gains": false}, {"time": "2023-02-10 14:43:36", "content": "看起来推荐位占的不是套组模板，是生态模板。。", "gains": false}, {"time": "2023-02-10 14:44:01", "content": "需要高优fix推荐策略聚合的问题。", "gains": false}, {"time": "2023-02-10 15:21:14", "content": "跟颖鑫会议对了半个钟，之前生态模板的规则。确认应该是可以合并到聚合策略的。至于目前配置模板的优先级，由安頔待会确认回复。", "gains": false}, {"time": "2023-02-10 15:28:22", "content": "确认优先级之后，发现可以把生态模板fg跟逻辑下掉了。后续走新的置顶策略就行了", "gains": false}, {"time": "2023-02-10 16:06:02", "content": "关闭fg之后，验证通过。VA、lang=En验证了也正常。。不知道为什么，fix逻辑都没上。可能模板没有english版本?", "gains": false}, {"time": "2023-02-10 16:07:01", "content": "那现在都不用发热修复了。直接给产品配置fg，就可以开始其他高优任务了。", "gains": false}], "timeline": [["2023-02-10 14:41:20", "2023-02-10 16:12:25"]]}, {"tid": "131502", "parent": "1315", "title": "自己整理一下文档", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 18:45:04", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "131503", "parent": "1315", "title": "找CICD/标品环境实操一下", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 18:45:47", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "120415", "parent": "1204", "title": "发PPE+发布上线", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 20:38:59", "ddl": "", "level": 3, "marks": [{"time": "2023-02-07 20:56:33", "content": "发布tcc配置", "gains": false}, {"time": "2023-02-08 15:32:48", "content": "先把pre发了，后面发hotfix再修MG的问题", "gains": false}, {"time": "2023-02-08 15:33:29", "content": "麻烦的事master又给合入了几个MR。。", "gains": false}, {"time": "2023-02-08 15:40:38", "content": "提工单+周知，等check commit能否合入回复", "gains": false}, {"time": "2023-02-08 16:09:30", "content": "确认revert，准备发布list", "gains": false}, {"time": "2023-02-08 16:59:11", "content": "一边跟家里聊霞减量艾曲波帕的事情", "gains": false}, {"time": "2023-02-08 16:59:26", "content": "工单发布中", "gains": false}, {"time": "2023-02-08 17:08:41", "content": "其他MR有问题，回滚了。等配置完。", "gains": false}], "timeline": [["2023-02-07 20:38:00", "2023-02-07 20:58:56"], ["2023-02-08 15:32:39", "2023-02-08 15:40:59"], ["2023-02-08 16:00:00", "2023-02-08 16:12:00"], ["2023-02-08 16:43:02", "2023-02-08 17:09:00"]]}, {"tid": "120416", "parent": "1204", "title": "确认一下MG+不同品牌+不同语言的限制", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 20:56:01", "ddl": "", "level": 3, "marks": [{"time": "2023-02-08 11:10:30", "content": "最高优事项，不要又一直mess一直拖。", "gains": false}, {"time": "2023-02-08 11:12:07", "content": "还是需要对MG先做一个自己整理的文档，才能思路清晰。", "gains": false}, {"time": "2023-02-08 14:05:27", "content": "纠结了一早上，又是方向跑偏了，实际上解决这个问题，只要限制住CN unit、飞书品牌、中文就可以快速实验了。", "gains": false}, {"time": "2023-02-08 14:11:32", "content": "发现之前矩阵模型也没判断unit，只判断了中文。", "gains": false}, {"time": "2023-02-09 17:16:22", "content": "浪费一个钟在411104，接下来直接用QA提供的测试账号测试就可以了。。", "gains": false}, {"time": "2023-02-09 17:18:29", "content": "另外徐兆楠也帮忙迁移va完成", "gains": false}, {"time": "2023-02-09 17:33:08", "content": "MG验证了一下va用户，发现没问题，但不是代码限制，而是tcc配置没同步到va，所以没生效。也能实现。接下来验证多品牌。", "gains": false}, {"time": "2023-02-09 17:39:00", "content": "目前实际租户应该没有MG的，所以应该问题不大。Lark租户应该没有CN用户的。应该不会调用CN的tcc配置。至于调用关系，最好在MG文档梳理一下", "gains": false}], "timeline": [["2023-02-08 10:52:40", "2023-02-08 10:52:40"], ["2023-02-08 11:10:07", "2023-02-08 11:12:20"], ["2023-02-08 14:04:30", "2023-02-08 14:12:00"], ["2023-02-08 15:02:21", "2023-02-08 15:02:21"], ["2023-02-08 15:26:56", "2023-02-08 15:32:39"], ["2023-02-08 15:40:59", "2023-02-08 15:41:31"], ["2023-02-09 11:53:34", "2023-02-09 12:01:10"], ["2023-02-09 15:19:00", "2023-02-09 15:33:54"], ["2023-02-09 17:15:22", "2023-02-09 17:39:40"]]}, {"tid": "920901", "parent": "9209", "title": "了解社会各行业利益输送规则", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 21:06:02", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "9209", "parent": "92", "title": "社会规则", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 21:11:12", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "920104", "parent": "9201", "title": "全国主要城市的GDP和财政收入来源、支柱产业（结合最新成都、以及大同市长的旅游业背景）", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 21:14:27", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "9114", "parent": "91", "title": "提升娱乐", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 22:03:35", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "911401", "parent": "9114", "title": "吹箫", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 22:03:49", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "911402", "parent": "9114", "title": "唱歌", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 22:03:52", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "911403", "parent": "9114", "title": "弹钢琴", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 22:03:55", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "520602", "parent": "5206", "title": "演练了一下发现，前面的实现方式导致db双写不合适。需要开发成主从模式。", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-07 23:04:07", "ddl": "", "level": 3, "marks": [{"time": "2023-02-08 00:02:05", "content": "整理了快一个小时之前的代码流程画成方案。结果发现之前仅仅实现了隧道的建立、以及往data-admin服务写入sync_record记录。尚未完成真正的db数据迁移备份。", "gains": false}, {"time": "2023-02-08 00:14:08", "content": "先刷牙早点睡吧，这个流程梳理完了，明晚思考一下是否合理，可以基于目前的实现完善，还是需要改动。", "gains": false}, {"time": "2023-02-08 20:40:26", "content": "继续思考一下流程，看看要不要改", "gains": false}, {"time": "2023-02-08 20:54:14", "content": "找半天没找到代码路径。还不如回家搞了。然后趴了会，太困了，回家算了。", "gains": false}, {"time": "2023-02-08 22:48:35", "content": "又整理了半个钟的架构图。画图ROI其实很低，又费时间，又没多少价值产出。", "gains": false}, {"time": "2023-02-08 22:59:57", "content": "发现data-admin服务都没部署。。", "gains": false}, {"time": "2023-02-08 23:00:36", "content": "即之前的data-synchronizer流程其实都跑不通了。现在需要重新设计一把，忘掉之前的了。", "gains": false}, {"time": "2023-02-08 23:27:23", "content": "发现好像之前实现的大部分也能用到。直接找回之前印象笔记的记录过程，差点忘了印象笔记了都。。", "gains": false}, {"time": "2023-02-09 00:25:11", "content": "发现方案越设计越复杂。针对数据同步这件事，逻辑太复杂了，太容易出问题了。花这么多时间搞这件事，ROI到底值不值。但是假如不搞，后续服务器迁移、数据备份等，又频频会用到。每次手动操作成本又更高、且容易遗漏。=>所以，设计得友好一点、降低使用+信息反馈界面的成本，长期来看还是值得的。", "gains": false}, {"time": "2023-02-09 00:39:14", "content": "但是或许可以快速迭代，一步步完善先。当下要同步哪些db数据，就先同步哪些，不要一开始就设计得很完善，后面又变更，又做无用功。现在赶紧把要同步的数据，尽量实现得低成本使用、友好些就行。主要目的是先备份数据而已，然后快速投入股票分析的核心任务。", "gains": false}, {"time": "2023-02-12 01:27:49", "content": "mess了好一阵，有点没接上任务，review了一下Marks，清楚现在不过多考虑方案，以快速支持数据同步备份为主要目的，支持这个目的地做一些完善改动先。不用太过周到", "gains": false}, {"time": "2023-02-12 13:36:03", "content": "是不是要考虑把localserver的、只作为数据读备份的db，改成只读从库模式。否则很容易意外写入、导致数据冲突。", "gains": false}, {"time": "2023-02-12 13:36:31", "content": "虽然说不要一步做到位，但是方向性的考虑和设计要提前做好。", "gains": false}, {"time": "2023-02-12 13:56:46", "content": "但是直接改成mysql原生支持的主从模式来同步数据，并不科学。这种模式确实适合同时提供服务的主从库。但目前这个类似于一个离线库，做备份用。如果表结构有变更，也需要能够在同步之前检测到变化。所以，还是按照自己的需求来实现同步脚本吧。只是需要做好完善的验证。", "gains": false}, {"time": "2023-02-12 14:08:11", "content": "如果datta-admin的记录要通过手机app看状态的话，最好也是提供cloud_server的服务部署。", "gains": false}, {"time": "2023-02-12 15:08:35", "content": "完成了data-sync的核心sync_db_core部分提取到nodejs容器的流程框架，接下来实现核心逻辑", "gains": false}, {"time": "2023-02-12 16:28:27", "content": "发现加多一层sync_db_core容器之后，前面建立的Tunnel又需要多一层localserver的docker到localserver本地端口的访问通道。另外，原本localserver本地到localserver mysql容器的通道变得多余了。那实际上datasync建立的ssh隧道，可以移到容器内部去做，似乎更加合理。但其实还是存在问题:\n1. localserver建立ssh通道，依赖宿主机的配置项否，没有的话还可以\n2. 但是nodejs容器的启动，在容器内部可能无法控制，还是需要外部宿主机来触发才行。或者就直接合并datasync跟nodejs整个容器。", "gains": false}, {"time": "2023-02-12 16:32:25", "content": "但其实还是不妥，这整个datasync流程很多步骤必须在宿主机操作的。比如gitsrv的拉取，以及file_resource迁移，还有前置的admin操作等等部署各个服务跟mysql容器自身流程", "gains": false}, {"time": "2023-02-12 16:36:05", "content": "另外像隧道建立这种安全敏感的操作，最好在宿主机人工介入感知，放到容器内直接打通cloud_server的隧道，安全隐患还是比较大", "gains": false}, {"time": "2023-02-12 16:51:10", "content": "好像没找到容器访问宿主机端口的。难道隧道建立也要挪到容器内部。", "gains": false}, {"time": "2023-02-12 16:53:49", "content": "也行吧，那就把Step3，整个SyncDbs步骤挪到容器内部。至于ssh到cloud安全性，主要还是证书文件映射给容器内部去使用。", "gains": false}, {"time": "2023-02-12 21:42:11", "content": "现在把Step3挪到容器内实现。", "gains": false}, {"time": "2023-02-12 22:17:41", "content": "目录大概设计迁移完，但原先依赖treetheme的一些方法，比如CallStep、PrintError等等，就无法在docker里面使用。", "gains": false}, {"time": "2023-02-12 22:22:08", "content": "把/data/deploy/treetheme/config_tools/tool_common.sh,tool_print.sh映射给容器内就可以了。", "gains": false}, {"time": "2023-02-12 23:36:44", "content": "踩了半天大坑，如果DoCmd跟的是自己的方法，比如这里的BuildTunnel，内部echo和PrintRaw等信息打印不出来，所以最后遇到的问题是Host key verification failed.", "gains": false}, {"time": "2023-02-12 23:37:08", "content": "花了快两个钟，差不多把buildTunnel的逻辑迁移到容器内部了", "gains": false}, {"time": "2023-02-12 23:38:35", "content": "其实关键信息不要用2>&1就好了，改成2>${stderr_file}就能看到了", "gains": false}, {"time": "2023-02-12 23:41:56", "content": "所以就是容器内需要处理known_hosts的问题", "gains": false}, {"time": "2023-02-12 23:58:11", "content": "差最后一步了，现在是容器内既要求镜像有node、又要求mysql命令。但是mysql只是用于最后的连通性检测，其实也可以迁移到nodejs代码里面实现还简洁一些。", "gains": false}, {"time": "2023-02-13 00:05:07", "content": "所以晚上回来也只是完成了sync_dbs迁移到镜像内，还来不及处理nodejs对mysql的双连接", "gains": false}, {"time": "2023-02-13 01:46:31", "content": "开始在实现nodejs流程了。先刷牙睡觉了。", "gains": false}, {"time": "2023-02-13 23:08:51", "content": "现在剩下的问题是，本地代理的20000端口会Connection Refused", "gains": false}, {"time": "2023-02-13 23:16:33", "content": "在node镜像执行apt-get update和install也方便。通过net-tools、telnet、mycli几个install，验证是可以通过localhost:20000连通的。。不知道nodejs项目为啥有问题。。", "gains": false}, {"time": "2023-02-13 23:22:37", "content": "果然是BuildTunnel为非阻塞的问题，等待1s重试就ok了。重试mysql连接逻辑在js里面实现简直简单", "gains": false}, {"time": "2023-02-13 23:29:43", "content": "那么接下来就要开始正式进入同步数据逻辑开发了，起多两个任务吧", "gains": false}], "timeline": [["2023-02-07 23:04:11", "2023-02-08 00:03:23"], ["2023-02-08 00:06:39", "2023-02-08 00:14:17"], ["2023-02-08 20:39:39", "2023-02-08 20:54:19"], ["2023-02-08 22:13:35", "2023-02-08 23:31:39"], ["2023-02-09 00:21:15", "2023-02-09 00:52:56"], ["2023-02-12 01:12:12", "2023-02-12 01:12:12"], ["2023-02-12 01:26:45", "2023-02-12 01:28:16"], ["2023-02-12 12:26:05", "2023-02-12 12:26:05"], ["2023-02-12 12:49:29", "2023-02-12 12:53:44"], ["2023-02-12 13:32:51", "2023-02-12 13:32:51"], ["2023-02-12 13:53:33", "2023-02-12 15:11:41"], ["2023-02-12 16:00:32", "2023-02-12 16:54:29"], ["2023-02-12 21:41:46", "2023-02-13 00:05:20"], ["2023-02-13 01:06:39", "2023-02-13 01:46:40"], ["2023-02-13 23:02:22", "2023-02-13 23:31:34"]]}, {"tid": "920703", "parent": "9207", "title": "数据平台搜集", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-08 10:54:41", "ddl": "", "level": 3, "marks": [{"time": "2023-02-08 10:54:57", "content": "wind数据资讯", "gains": false}], "timeline": []}, {"tid": "4111", "parent": "41", "title": "公司基础学习", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-08 11:06:17", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "411101", "parent": "4111", "title": "了解Lark业务线", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-08 11:06:32", "ddl": "", "level": 3, "marks": [{"time": "2023-02-08 11:07:11", "content": "从春节值班表，看到wiki上有Lark专题。", "gains": false}], "timeline": [["2023-02-08 10:56:24", "2023-02-08 11:10:07"]]}, {"tid": "411102", "parent": "4111", "title": "了解KA客户和双品牌", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-08 11:09:54", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "411103", "parent": "4111", "title": "了解MG与梳理架构图", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-08 11:11:37", "ddl": "", "level": 3, "marks": [{"time": "2023-02-08 11:50:25", "content": "就收录了一堆相关文档的link到目录，纠结了一会记得之前自己的MG笔记好像有两个，只找到一个。", "gains": false}, {"time": "2023-02-08 11:54:05", "content": "要完整梳理MG架构图，需要很费时间，所以目前应该高优思考清楚逻辑，感觉完成主线任务先。", "gains": false}, {"time": "2023-02-09 17:40:32", "content": "梳理一下各个品牌+Unit组合下，请求web页面的域名与unit，还有调用service的unit，以及请求tcc配置的unit。", "gains": false}, {"time": "2023-02-09 17:41:13", "content": "这些梳理，需要自己根据实际账号和logid去梳理一遍，验证后，才印象深刻。", "gains": false}, {"time": "2023-02-09 17:54:04", "content": "维护了一个2*4*4的32列表格，作为维度。后续各种指标列添加就行。比如日志Unit、服务Unit、网关Unit、tccUnit等等", "gains": false}], "timeline": [["2023-02-08 11:12:20", "2023-02-08 11:18:00"], ["2023-02-08 11:33:26", "2023-02-08 12:00:00"], ["2023-02-08 14:01:06", "2023-02-08 14:04:30"], ["2023-02-09 17:39:40", "2023-02-09 18:06:43"], ["2023-02-10 16:15:00", "2023-02-10 17:00:00"]]}, {"tid": "1316", "parent": "13", "title": "模板跨unit同步", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-08 11:31:01", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "131601", "parent": "1316", "title": "交接会议(陈锐)", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-08 11:32:29", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-02-08 11:18:00", "2023-02-08 11:33:26"]]}, {"tid": "110103", "parent": "1101", "title": "跟安頔重新对齐优化事项，调整埋点降噪优化的优先级", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-08 14:26:28", "ddl": "", "level": 3, "marks": [{"time": "2023-02-08 14:29:12", "content": "昨天跟方文1on1之后就要高优跟安頔讨论的，不过对于如何描述，又是一团mess，所以又优先做了其他又费时间又紧急的任务，把不费时间更高优的事拖到现在，也就十分钟组织完的事情:\n哈喽安頔，上次我们对过需求人力和优先级的，里面搜索优化的事项当时是说，想这边先做完埋点降噪优化再开始。\n这个最近我们评估了一下方案，觉得在后端服务做这个会比较复杂，ROI不高，觉得还是以快速迭代、优化+补充模板内容为主要目的，所以做了一些调整建议：\n关于埋点噪声的问题，可以在数据平台那边来处理，这样可能要先给数据同学提需求排期，可能节奏不会那么快，可以并行展开\n我们可以先明确一下搜索优化具体的收益目标和工作内容，重新评估下埋点噪声对我们整体的搜索优化影响面如何，我理解如果整个搜索优化过程是不断迭代进行的，那在埋点噪声优化之前我们也可以先展开、迭代一批内容看看效果\n你这边看看意见如何~", "gains": false}, {"time": "2023-02-08 14:30:05", "content": "另外还没提到催PRD的事情。", "gains": false}, {"time": "2023-02-08 16:08:56", "content": "跟安頔语音对齐了优化事项，本周出简单PRD，尽快小步迭代", "gains": false}, {"time": "2023-02-14 14:50:12", "content": "待会会议对齐PRD，提前看下细节，思考一下捋一捋", "gains": false}, {"time": "2023-02-14 17:23:10", "content": "有些琐事打算，一边看数据和PRD，需要先关注分析搜索数据和流量地图", "gains": false}, {"time": "2023-02-14 17:29:06", "content": "梳理了一下看板链接收录，专心看数据没几分钟，准备搜索PRD会议了", "gains": false}, {"time": "2023-02-14 18:00:56", "content": "过完会议，结论还是搜索打标实验。代码需要加一下搜索标签的ab实验。另外需要思考好现有数据跟如何分析", "gains": false}], "timeline": [["2023-02-08 14:12:00", "2023-02-08 14:30:08"], ["2023-02-08 15:41:31", "2023-02-08 16:00:00"], ["2023-02-14 14:49:48", "2023-02-14 14:57:00"], ["2023-02-14 16:36:53", "2023-02-14 16:38:00"], ["2023-02-14 17:01:09", "2023-02-14 17:01:09"], ["2023-02-14 17:08:34", "2023-02-14 18:10:00"]]}, {"tid": "4112", "parent": "41", "title": "还有个一直催调整告警群的(梁峰)", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-08 14:34:00", "ddl": "", "level": 2, "marks": [{"time": "2023-02-08 14:58:02", "content": "捣鼓了一会怎么群聊拉定位小助手，直接问进坤搞定。", "gains": false}, {"time": "2023-02-08 14:58:23", "content": "然后拉了两个模板专属告警群做后续收敛。", "gains": false}, {"time": "2023-02-08 15:01:54", "content": "有空再收敛告警群。先继续核心任务。", "gains": false}], "timeline": [["2023-02-08 14:34:00", "2023-02-08 15:02:21"]]}, {"tid": "4113", "parent": "41", "title": "画板私有化-模板导入导出拉群建meego", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-08 14:50:01", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-02-08 15:02:21", "2023-02-08 15:12:00"]]}, {"tid": "9210", "parent": "92", "title": "医学", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-08 15:41:26", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "921001", "parent": "9210", "title": "环孢素药理", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-08 15:41:47", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "921002", "parent": "9210", "title": "护肝药药理", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-08 16:03:25", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "4307", "parent": "43", "title": "方案评审", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-08 16:16:12", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "430701", "parent": "4307", "title": "CCM越权治理-旁路校验方案", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-08 16:16:33", "ddl": "", "level": 3, "marks": [{"time": "2023-02-08 16:41:00", "content": "实际上在mess、群聊、休息。", "gains": false}], "timeline": [["2023-02-08 16:12:00", "2023-02-08 16:43:02"]]}, {"tid": "120417", "parent": "1204", "title": "建libra+群里周知+配置tcc", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-08 17:10:24", "ddl": "", "level": 3, "marks": [{"time": "2023-02-09 11:48:58", "content": "建完libra，发起tcc配置工单。等发布完验证一下", "gains": false}], "timeline": [["2023-02-09 11:36:49", "2023-02-09 11:49:02"]]}, {"tid": "921003", "parent": "9210", "title": "再障病理", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-08 17:39:36", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "9402", "parent": "94", "title": "松山湖欧洲小镇游玩", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-08 20:36:30", "ddl": "", "level": 2, "marks": [{"time": "2023-02-12 00:28:27", "content": "11:30来福士越小品，同冰女少优取车回工区拿眼药水点manner，出发东莞14:30同瑞芬湖岸花园豪叔椰冰汇合，晚坐错末班火车到A区，19:10至湖滨万科里椰雨仙踪椰子鸡，21:30顺风车返程桃园地铁，23:10到家。", "gains": false}], "timeline": [["2023-02-11 11:30:00", "2023-02-11 23:30:00"]]}, {"tid": "5207", "parent": "52", "title": "admin", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-09 00:54:08", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "520701", "parent": "5207", "title": "先添加一个一键部署所有service的脚本(用于local_server同步db前，同步更新service)", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-09 00:54:59", "ddl": "", "level": 3, "marks": [{"time": "2023-02-09 01:09:18", "content": "发现之前admin.git/onekey.sh已经有这些操作了。", "gains": false}, {"time": "2023-02-09 01:42:20", "content": "补充了onekey.sh脚本新增部署blog和task的问题。但是目前发现local_server没有thrift-common安装的thrift-go等。最好能再演练一遍treetheme在local_server的install流程。", "gains": false}, {"time": "2023-02-10 00:15:15", "content": "流程也顺利跑完了。", "gains": false}, {"time": "2023-02-10 00:29:15", "content": "发现BlogService没部署起来，顺手修了个bug", "gains": false}, {"time": "2023-02-10 00:29:42", "content": "现在拉nodejs Docker镜像", "gains": false}, {"time": "2023-02-10 00:50:08", "content": "每次docker拉node镜像真的是龟速。。", "gains": false}, {"time": "2023-02-10 01:21:34", "content": "垃圾docker真的无敌龟速。。解决一大堆最后把最耗时的留到现在。。看这龟速得爬几个钟不止。。", "gains": false}, {"time": "2023-02-10 01:22:29", "content": "记得以前好像也是龟速到没法忍，oproxy打开代理试试。", "gains": false}, {"time": "2023-02-10 01:24:52", "content": "oproxy开完代理，拉docker node镜像果然快了。。", "gains": false}, {"time": "2023-02-10 01:31:35", "content": "发现oproxy后压根没走到代理。。心理作用。。而且发现确实还是慢", "gains": false}, {"time": "2023-02-10 01:42:22", "content": "之前已经在《【笔记】docker之坑》记录过方法了。。2021-08-07踩过一样的坑。不得不更改/etc/systemd/system/docker.service.d/http-proxy.conf然后重启服务，这样会导致所有docker容器stop掉。为此，不得不特制一个oproxy_docker。至于如何避开重启，先查查看", "gains": false}, {"time": "2023-02-10 02:07:54", "content": "垃圾Docker，按照之前《【笔记】docker之坑》2021-08-07记录的踩坑步骤全部走过一遍后，又有新的问题，还是一直Forward failure。明天再看什么问题了。", "gains": false}, {"time": "2023-02-10 20:43:28", "content": "继续抓紧解决docker的问题。", "gains": false}, {"time": "2023-02-10 20:48:19", "content": "意识到treetheme可能没有配置privoxy到sslocal的代理路径。。所以直接通过privoxy，访问不通docker.io", "gains": false}, {"time": "2023-02-10 21:54:40", "content": "发现原来是sslocal没启动。。直接tail -f /tmp/sslocal.log容易被误导。。", "gains": false}, {"time": "2023-02-10 21:58:49", "content": "重新开一个pane，oproxy_docker之后就可以了。估计是前面一直source ~/.bashrc把shell环境搞坏了", "gains": false}, {"time": "2023-02-10 21:59:18", "content": "果然快了。", "gains": false}, {"time": "2023-02-10 22:10:23", "content": "blog-service还有问题要修复才能部署。", "gains": false}, {"time": "2023-02-10 22:19:51", "content": "总算搞定blog-service在localserver部署的问题。因为初次拉仓库没有npm i，导致没有node_modules，镜像构建失败", "gains": false}, {"time": "2023-02-10 22:22:44", "content": "目前看，nginx-admin还是无法在localserver部署起来", "gains": false}, {"time": "2023-02-10 22:28:55", "content": "只是因为localserver本地也有nginx在跑端口占用。现在启动了", "gains": false}, {"time": "2023-02-10 22:31:19", "content": "试一下路由器配置局域网域名，指向local_server测试局域网服务功能可用性。", "gains": false}, {"time": "2023-02-10 22:34:32", "content": "好像路由器无法配置，只能mac和手机单独都配置/etc/hosts了", "gains": false}, {"time": "2023-02-10 22:38:21", "content": "发现腾讯云DNS解析直接配置local.luzeshu.site到局域网ip地址就ok了", "gains": false}, {"time": "2023-02-10 22:49:45", "content": "部署到local_server缺少luzeshu.site的ssl证书。那么treetheme也应该给local_server安装CronJob模块，用来定时从cloud_server scp证书下来", "gains": false}, {"time": "2023-02-10 22:57:17", "content": "都忘了之前cronjob运行机制是什么。参考《【项目】cronjob》", "gains": false}, {"time": "2023-02-10 23:05:22", "content": "发现之前的/etc/cron.hourly/入口，是nginx-admin/在make prepare时部署的。CronJob仓库仅仅用来存放各种脚本。总觉得这种方式有点奇怪。或许所有CronJob应该单独统一，treetheme install时处理，不要留到nginx-admin的prepare.sh，触发时机都没有。", "gains": false}, {"time": "2023-02-10 23:28:23", "content": "把nginx-admin里的CronJob逻辑，移交到treetheme触发，并且代码逻辑收敛到Cronjob仓库", "gains": false}, {"time": "2023-02-10 23:28:52", "content": "接下来补充local_server的scp证书cronjob", "gains": false}, {"time": "2023-02-11 00:18:29", "content": "才意识到，全部数据通过datasync来同步，而这都是tic用户授权操作，这安全隐患很大。因为目前遇到的是需要以同样的方式，scp拉取ssl证书。而这都是tic不可读的。", "gains": false}, {"time": "2023-02-11 00:18:50", "content": "一搞又是两个半钟。。先洗澡了。。", "gains": false}, {"time": "2023-02-11 01:09:46", "content": "针对安全问题，记个后续任务去处理好了。目前怎么快速完成主要任务怎么来。", "gains": false}, {"time": "2023-02-11 01:15:38", "content": "或者先不动/etc/letsencrypt/live权限，把要scp_cert的内容先找个路径存放先。", "gains": false}, {"time": "2023-02-11 01:31:24", "content": "把scp_cert的流程搞完了", "gains": false}, {"time": "2023-02-11 01:32:01", "content": "现在继续把nginx-admin部署可用，通过local.luzeshu.site访问。", "gains": false}, {"time": "2023-02-11 01:38:33", "content": "发现luzeshu.site的证书还不能给local.luzeshu.site使用。。。", "gains": false}, {"time": "2023-02-11 01:39:57", "content": "那问题来了，要额外生成local.luzeshu.site的证书，就需要cloud ip，那就dns不能配置成局域网ip，还是只能通过本地修改/etc/hosts。那可以不用local.luzeshu.site了。", "gains": false}, {"time": "2023-02-11 01:42:43", "content": "还是需要local.luzeshu.site，只是只能本地/etc/hosts配置了。否则无法生成证书。", "gains": false}, {"time": "2023-02-11 02:24:40", "content": "千辛万苦把整个local证书流程搞定了。本地可以访问局域网nginx，但是local证书显示有其他问题爆红。。", "gains": false}, {"time": "2023-02-11 02:25:09", "content": "显示组织未包含在证书之类的", "gains": false}, {"time": "2023-02-11 02:28:00", "content": "好像这个证书在luzeshu.site域名也是这个问题。但不会爆红。明天再找原因了。早点睡了，困死。明天还要去欧洲小镇。", "gains": false}, {"time": "2023-02-12 01:03:49", "content": "现在继续解决昨晚localserver的https证书问题", "gains": false}, {"time": "2023-02-12 01:06:35", "content": "今天发现local.luzeshu.site的域名没问题了。。那就算解决了。可以继续返回上一个任务了", "gains": false}, {"time": "2023-02-12 01:11:49", "content": "review了一下marks列表，全部问题跟主要任务都搞完了。主要就是把服务在localserver都跑起来。现在可以回到上个任务进行数据同步了", "gains": false}], "timeline": [["2023-02-09 01:08:00", "2023-02-09 01:42:28"], ["2023-02-09 22:53:20", "2023-02-09 22:55:25"], ["2023-02-10 00:08:56", "2023-02-10 00:31:31"], ["2023-02-10 01:20:00", "2023-02-10 02:10:44"], ["2023-02-10 20:43:11", "2023-02-10 20:49:00"], ["2023-02-10 21:37:37", "2023-02-10 21:37:37"], ["2023-02-10 21:51:03", "2023-02-11 00:19:14"], ["2023-02-11 01:09:13", "2023-02-11 02:28:18"], ["2023-02-12 01:03:20", "2023-02-12 01:12:12"]]}, {"tid": "120418", "parent": "1204", "title": "配置pre置顶模板给QA测试", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-09 11:30:31", "ddl": "", "level": 3, "marks": [{"time": "2023-02-09 12:01:17", "content": "好像有点问题", "gains": false}, {"time": "2023-02-09 12:02:10", "content": "吃完饭再日志排查一下吧", "gains": false}, {"time": "2023-02-09 14:47:26", "content": "日志定位了一下，发现早上配置的两个模板中，一个是矩阵模型推荐返回来的。这个模型之前返回没根据zeus分类获取所有上线模板进行筛选。所以属于之前的遗留bug导致。后续不会配置这类模板", "gains": false}], "timeline": [["2023-02-09 11:31:44", "2023-02-09 11:36:49"], ["2023-02-09 12:01:10", "2023-02-09 12:02:26"], ["2023-02-09 14:34:21", "2023-02-09 14:53:45"]]}, {"tid": "120419", "parent": "1204", "title": "把正式置顶模板token转为id，进行正式配置", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-09 11:30:56", "ddl": "", "level": 3, "marks": [{"time": "2023-02-10 11:56:30", "content": "只有几个模板，手动转了一下，配置完tcc发布中", "gains": false}, {"time": "2023-02-10 14:39:33", "content": "现在验证一下配置正常不。", "gains": false}], "timeline": [["2023-02-10 11:41:03", "2023-02-10 11:56:41"], ["2023-02-10 14:39:17", "2023-02-10 14:41:20"], ["2023-02-10 16:12:42", "2023-02-10 16:12:47"]]}, {"tid": "12020308", "parent": "120203", "title": "转A5-A6规模租户实验id放量", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-09 15:09:18", "ddl": "", "level": 4, "marks": [{"time": "2023-02-09 19:23:04", "content": "还好A5、A6租户只有几个，手动转一下配置完成了。", "gains": false}], "timeline": [["2023-02-09 19:16:50", "2023-02-09 19:24:42"]]}, {"tid": "411104", "parent": "4111", "title": "尝试MG测试租户迁移一下到sg(另外准备4个支持MG的租户，4个unit分别准备个测试用户)", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-09 15:50:02", "ddl": "", "level": 3, "marks": [{"time": "2023-02-09 16:13:22", "content": "了解了一些MG租户和迁移的流程。以及域名差异，larksuite表示品牌，跨unit会有sg.等四级域名", "gains": false}, {"time": "2023-02-09 16:42:47", "content": "一下子就快浪费一个钟了。实践到接口需要填jwt-token，但是又需要登录字节云平台获取。外部租户用户如何获取？卡住了，咨询中。", "gains": false}, {"time": "2023-02-09 16:50:44", "content": "实际上从16:05，QA就提供了一个各种unit的测试用户列表。从ROI来说，这个钟又是在浪费时间。一个对话意识到:\n我: 哈喽兆楠，我想问下这个文档里面提到jwt-token是要登录字节云才能获取DTS 测试接口 Token 权限改造\n那我一个外部租户的用户，想要调用migrate_user接口，进行MG迁移，应该怎样获取jwtToken呢\n对方: jwt用你字节账号的就行，外部租户只有开了mg功能才能迁移\n我: 有开了MG功能的，那也就是说这个jwt的鉴权，实际上跟要迁移的user本身，不是一致的吗，有QA现成用户，不过我自己想体验一下迁移流程、还有给自己的user也迁移下\n这个体验迁移流程，还有维护自己的MG账号，实际上没有任何意义。而且迁移流程也不稳定，后续也不一定维护，不一定需要这个流程。所以ROI考虑的做法是直接一开始用QA的测试账号验证就行了。。", "gains": false}, {"time": "2023-02-09 16:54:00", "content": "另外，对方回复了答案之后，进一步把自己这个钟的时间打成浪费了，因为压根不需要自己这样迁移了。。\n对方: 哦哦，不太一样，因为我们还有自动迁移机器人，jwt 是验证并记录启动迁移的管理员身份\nmg 功能是按租户维度开通的（比如说字节租户），迁移的用户和启动迁移的触发者可能不是一个人\n触发迁移现在大部分都是靠机器人了，因为字节海外员工几乎都迁移完成了，剩下的只有少量员工异动\n想体验迁移话，我找个时间给你迁移走就好[笑哭]\n我: 那这样是不是可能会有安全问题，因为所有字节用户都有迁移他人账号的权限？\n对方: 并不是，只有有管理员权限的才能迁移他人的账号，jwt 就是验证你的管理员身份的。\n我: 这个机器人有没有文档呀\n对方: pm 那边提交的员工是机器人的输入，迁移你还是需要手动触发的[捂脸]", "gains": false}, {"time": "2023-02-09 16:54:45", "content": "这么麻烦，而且对方说了可以帮忙迁移，早这样问就不用自己折腾了。真想维护一个MG用户，就直接让他们搞定就好了，高优搞其他事情。折腾这种流程没有意义", "gains": false}, {"time": "2023-02-09 16:59:53", "content": "搞定。直接让帮忙迁移，而且线上流程封禁掉了。一开始问就省一个小时。\n假如你想体验迁移自己的流程的话，得去boe[捂脸]线上的这个我们已经封禁掉了\n\n回复 徐兆楠: \npm 那边提交的员工是机器人的输入，迁移你还是需要手动触发的[捂脸]（已编辑）\n想问说明文档哈，或者你那边能帮我迁移吗，我把user_id提供给你[思考]\n\n但我这还有迁移字节账号的权限，我可以给你迁移到 va sg 或者 jp\n\n卢泽树\n对 你提供 user_id 就好\n6972344031604457473 这个？\n\n7073012656243785732\n这个，其他租户的\n\n我看看这个租户开没开mg开关。。mg开关是好几个不同业务线的卡点一起控制的，开起来还挺麻烦的[捂脸]\n1 条回复\n\n回复 徐兆楠: \n我看看这个租户开没开mg开关。。mg开关是好几个不同业务线的卡点一起控制的，开起来还挺麻烦的[捂脸]\n这个租户有mG\n\n哦哦 好的\n此消息已撤回\n迁移到哪？\n\n不过不确认有哪些units，这个能查吗\n迁移到VA吧\n", "gains": false}, {"time": "2023-02-09 17:00:55", "content": "既然流程封禁了，那让QA去确认units list也没意义了，自己不打算迁移了后续。", "gains": false}], "timeline": [["2023-02-09 15:50:09", "2023-02-09 17:00:00"]]}, {"tid": "430702", "parent": "4307", "title": "诉讼保留CCM侧后端方案", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-09 17:10:10", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-02-09 17:00:00", "2023-02-09 17:15:22"]]}, {"tid": "9115", "parent": "91", "title": "运动", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-09 19:55:30", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "911501", "parent": "9115", "title": "桌球", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-09 19:55:41", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-02-09 19:55:48", "2023-02-09 21:10:00"], ["2023-03-09 18:40:00", "2023-03-09 21:45:00"]]}, {"tid": "520201", "parent": "5202", "title": "为了节省以后的维护成本，以及android这种各种不兼容更新的垃圾玩意，应该都改成app嵌h5的方式。", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-09 22:52:38", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "520502", "parent": "5205", "title": "local_server演练一遍幂等install", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-09 22:54:19", "ddl": "", "level": 3, "marks": [{"time": "2023-02-09 22:55:08", "content": "520701任务遇到一些问题，需要演练一遍install", "gains": false}, {"time": "2023-02-09 23:53:14", "content": "又完善了一些脚本配置写法(属于跑岔路了)。然后进行到thrift的编译，把binary存进仓库。现在thrift编译中。", "gains": false}, {"time": "2023-02-10 00:00:22", "content": "thrift在make过程中，一直报错。这个问题一直存在。似乎是跟rust模块相关的。估计之前就是因为这里一直报错，才关掉了thrift的安装。以前开发机部署时，也是make成功了一半，后面不影响就不管了。现在看看怎么只make需要的相关模块。rust暂时不需要。而且thrift估计也年久失修了", "gains": false}, {"time": "2023-02-10 00:05:35", "content": "直接cd /tmp/thrift-0.16.0/thrift-0.16.0下执行./configure --help，发现所有语言模块都有--with-rs=no之类的配置，默认都是yes。这样就可以去除不需要的部分了", "gains": false}, {"time": "2023-02-10 00:08:52", "content": "搞定thrift安装，继续回到520701任务", "gains": false}], "timeline": [["2023-02-09 22:55:25", "2023-02-10 00:08:56"]]}, {"tid": "520403", "parent": "5204", "title": "stock-crawler", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-10 01:26:40", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "520404", "parent": "5204", "title": "web-crawler(爬pcb)", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-10 01:28:00", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "520405", "parent": "5204", "title": "phone-screenshot(原本是想通过截屏提取交易记录的)", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-10 01:29:26", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "9116", "parent": "91", "title": "整理心情", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-10 10:49:11", "ddl": "", "level": 2, "marks": [{"time": "2023-02-10 11:22:19", "content": "1. 近期有太多负面情绪。不要让自己的时间充满仇恨，天天怨天尤人埋怨社会。其实以前很满足。最大的导火索就是各种社会不公，自己利益受到不断侵蚀。薪资倒挂严重，年龄渐长，成果一般，尤其脉脉各种看多了。第三就是深圳房租一年占据大头收入，心态失衡。\n2. 从理性来看，自己目前的时间肯定花在最有价值的事情上，才是合理的，天天负面情绪并不能改变什么，只会让自己不断滑入失败深渊。另外，对自己的形象塑造也不好，这也会影响未来人脉和成就。\n3. 从以前的心态来看，还是最注重核心事情，比如身体一定要搞好，打持久战；一定要不断学习，才能提升；要多花时间在家人身上，用现在赚的钱多孝敬父母，一辈子被这个社会压榨，该安享晚年。", "gains": false}, {"time": "2023-02-10 11:33:32", "content": "中间被万军来问最近做什么、打断了一会，然后帮爸取消顺风车订单，又在锋英群聊了会股票后市，现在继续。", "gains": false}], "timeline": [["2023-02-10 10:49:28", "2023-02-10 11:41:03"]]}, {"tid": "430703", "parent": "4307", "title": "旁路校验系统支持聚合校验方案", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-10 17:08:10", "ddl": "", "level": 3, "marks": [{"time": "2023-02-10 17:22:07", "content": "有点mess", "gains": false}], "timeline": [["2023-02-10 17:00:00", "2023-02-10 17:22:28"]]}, {"tid": "9117", "parent": "91", "title": "家庭", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-10 21:36:05", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "911701", "parent": "9117", "title": "家人视频", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-10 21:36:16", "ddl": "", "level": 3, "marks": [{"time": "2023-02-10 21:37:15", "content": "跟家人视频，明天晓霞回深圳，问了后续打算，先去大姐住。还有后续续租房子的事情，有空再打算吧。", "gains": false}], "timeline": [["2023-02-10 20:49:00", "2023-02-10 21:37:37"], ["2023-02-28 21:53:00", "2023-02-28 22:31:41"]]}, {"tid": "920105", "parent": "9201", "title": "各地政策研究", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-11 01:08:39", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "92010501", "parent": "920105", "title": "深圳如何吸引那么多互联网企业", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-11 01:08:53", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "5208", "parent": "52", "title": "安全问题整改", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-11 01:10:20", "ddl": "", "level": 2, "marks": [{"time": "2023-02-11 01:11:45", "content": "主要考虑smysql/jump-server的安全隐患，以及把/etc/letsencrypt改成tic可访问的隐患", "gains": false}], "timeline": []}, {"tid": "520801", "parent": "5208", "title": "关于datasync和cronjob/scp_cert直接通过tic获取敏感数据的安全隐患", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-11 01:11:11", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "9118", "parent": "91", "title": "日记", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-12 00:30:37", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-02-12 00:20:00", "2023-02-12 00:31:31"]]}, {"tid": "9403", "parent": "94", "title": "去彭婷家吃烤肉", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-12 00:36:06", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "9404", "parent": "94", "title": "去彭婷家玩狼人杀", "category": null, "status": "待启动", "description": "", "priority": 0, "quadrant": 0, "createTime": "2023-02-12 00:36:34", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "95", "parent": "5", "title": "周末固定生活", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-12 12:23:13", "ddl": "", "level": 1, "marks": [], "timeline": []}, {"tid": "9501", "parent": "95", "title": "起床清理点外卖", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-12 12:23:53", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-02-12 11:45:00", "2023-02-12 12:26:05"]]}, {"tid": "9211", "parent": "92", "title": "社交能力提升", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-12 12:28:34", "ddl": "", "level": 2, "marks": [{"time": "2023-02-12 12:32:08", "content": "踏入社会这么多年，没有刻意训练过。外在形象、语言表达、沟通能力、以及情商。昨天欧洲小游，很多时候回话很低情商，很多行为举止不够注意外在形象，后面跟冰女辩论副业的时候甚至表达不太清楚观点、而且有些话题不够适可而止导致她看着有些生气。", "gains": false}], "timeline": []}, {"tid": "921101", "parent": "9211", "title": "语言表达能力与沟通训练(精确、简明且适可而止)", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-12 12:30:53", "ddl": "", "level": 3, "marks": [{"time": "2023-02-12 12:45:22", "content": "一直来习惯了在网上油嘴滑舌，应对得当。逻辑清晰，组织语言犀利。看看如何把这部分能力迁移到面对面沟通中。这跟自我形象意识和管理、沟通中的气场把握和控场，也是分不开的。", "gains": false}], "timeline": []}, {"tid": "921102", "parent": "9211", "title": "形象管理", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-12 12:31:23", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "9502", "parent": "95", "title": "吃午餐、刷手机、看电视", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-12 12:53:34", "ddl": "", "level": 2, "marks": [{"time": "2023-02-12 12:53:52", "content": "尊宝披萨", "gains": false}, {"time": "2023-02-12 13:32:41", "content": "打完咖啡继续干活", "gains": false}], "timeline": [["2023-02-12 12:53:44", "2023-02-12 13:32:51"]]}, {"tid": "9212", "parent": "92", "title": "未来热点研究", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-12 13:30:41", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "921201", "parent": "9212", "title": "盈创建筑科技和3d建筑打印", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-12 13:31:23", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "9405", "parent": "94", "title": "去大姐家", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-12 16:54:15", "ddl": "", "level": 2, "marks": [{"time": "2023-02-12 16:54:44", "content": "收拾一下、洗一下咖啡机准备去大姐家吃晚饭了。", "gains": false}, {"time": "2023-02-12 17:10:39", "content": "关电脑出发", "gains": false}, {"time": "2023-02-12 21:32:29", "content": "前后四个半小时。17:20出发，差不多18:05到，隔了一会吃完休息没一会，20:30回来，21:15到家，剖个美团买菜的垃圾青椰剖了十几分钟。现在继续干活。", "gains": false}, {"time": "2023-02-12 21:40:54", "content": "又浪费十分钟剖了一个。", "gains": false}, {"time": "2023-02-26 23:12:18", "content": "今晚霞生日，等吃完蛋糕九点半回来，路过顺便做了个采耳。", "gains": false}], "timeline": [["2023-02-12 16:54:29", "2023-02-12 21:41:46"], ["2023-02-26 17:38:16", "2023-02-26 23:17:30"]]}, {"tid": "96", "parent": "5", "title": "其他尝试", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-12 17:11:08", "ddl": "", "level": 1, "marks": [], "timeline": []}, {"tid": "9601", "parent": "96", "title": "先搞一个过年的小程序，支付系统构建起来", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-12 17:11:25", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "9602", "parent": "96", "title": "搞一个电商平台，可以快速搭建，paypal系统构建起来", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-12 17:11:55", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "960201", "parent": "9602", "title": "调研一下国外的一些用户习惯，以及支付习惯", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-12 17:12:17", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "520503", "parent": "5205", "title": "踩了半天大坑，如果DoCmd跟的是自己的方法，比如这里的BuildTunnel，内部echo和PrintRaw等信息打印不出来", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-12 23:35:20", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "520603", "parent": "5206", "title": "db备份同步的内容冲突检测与备份同步逻辑", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-13 23:31:02", "ddl": "", "level": 3, "marks": [{"time": "2023-02-14 20:47:04", "content": "现在高优以favorites数据同步为例，先进行逻辑判断与数据同步", "gains": false}, {"time": "2023-02-14 21:15:28", "content": "写到show tables查询时，不知道多个table的情况怎么样，于是想顺便把args指定db的传参配置，现在优先写完", "gains": false}, {"time": "2023-02-14 21:58:24", "content": "完成：把sync指定db改成可配置参数指定", "gains": false}, {"time": "2023-02-14 22:05:08", "content": "现在试一下配置其他库看看", "gains": false}, {"time": "2023-02-14 22:06:56", "content": "成功搞定db配置指定", "gains": false}, {"time": "2023-02-14 22:07:10", "content": "现在继续多table scheme检测", "gains": false}, {"time": "2023-02-14 23:38:27", "content": "完成表scheme和列检测比较", "gains": false}, {"time": "2023-02-14 23:45:19", "content": "现在暂时把存在表变更的，检测出来抛Error，后续遇到具体case再优化逻辑。现在优先完成整个检测流程之后，先进行实际数据的同步", "gains": false}, {"time": "2023-02-14 23:57:16", "content": "又补充了field检测。接下来开始数据记录检测", "gains": false}, {"time": "2023-02-15 00:01:29", "content": "通过nodejs来实现，还有一个好处，就是插入synchronizer-mysql同步记录，都不用通过data-admin发送http请求。直接在sync逻辑里面就能插入", "gains": false}, {"time": "2023-02-15 00:25:06", "content": "现在实现对synchronizer-mysql的操作，以及上一次同步的时间戳，以此判断增量更新数据量。但是这么复杂的逻辑，万一真的出现bug，会不会导致数据永久性丢失？", "gains": false}, {"time": "2023-02-15 00:47:31", "content": "但是如果要把同步操作的信息本身放到cloud_server通过服务管理，还是需要通过data-admin接口", "gains": false}, {"time": "2023-02-15 00:49:10", "content": "既然data-admin要操作synchronizer-mysql，那么就收归到一个入口，尽量每个服务有且仅映射一个db", "gains": false}, {"time": "2023-02-15 00:50:58", "content": "还是早点睡先。", "gains": false}, {"time": "2023-02-15 22:53:17", "content": "昨晚最后进行到data-admin这步。今晚先开始data-admin收敛synchronizer-mysql的操作。另外之前data-admin提供http接口，不走网关+rpc了", "gains": false}], "timeline": [["2023-02-13 23:31:34", "2023-02-14 00:11:26"], ["2023-02-14 20:46:19", "2023-02-15 00:06:43"], ["2023-02-15 00:23:45", "2023-02-15 10:52:00"], ["2023-02-15 22:50:05", "2023-02-15 22:53:37"]]}, {"tid": "520604", "parent": "5206", "title": "完善多个db进行备份同步的配置传参流程", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-13 23:31:29", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "1406", "parent": "14", "title": "万军at了一个boe获取系统模板接口报错问题", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-14 15:34:21", "ddl": "", "level": 2, "marks": [{"time": "2023-02-14 15:53:54", "content": "孟运卓反馈是系统接口报错，看了日志没问题。。", "gains": false}, {"time": "2023-02-14 16:26:30", "content": "了解了一通，浪费20min才了解清楚情况，低效沟通：\n1. 先说是接口报错，然后问清楚说是数据不对，要返回历史实验模板；而且上周没问题，突然不行了。只上了boe；\n2. 问了一遍发现没有后端参与，只有前端进行的需求，让加个fg体验一下，磨磨唧唧大半天，又给url，又给curl，就是不加fg。\n3. 加完fg，准备来验证，不过大致清楚了，本来就不是推历史模板，但是mindnote类型不符合预期", "gains": false}, {"time": "2023-02-14 16:26:44", "content": "定位下mindnote和sheet", "gains": false}, {"time": "2023-02-14 16:27:50", "content": "加了前端fg还是看不到", "gains": false}, {"time": "2023-02-14 16:28:11", "content": "不过不加fg，自己开模板库看接口也可以验证。。", "gains": false}, {"time": "2023-02-14 16:33:34", "content": "结论：@冯雪梅 boe应该是@黄万军 发了其他分支在验证功能，不过@陈毅茵 那个接口确实不能保证历史使用模板记录在最前面的，可能有推荐需求会覆盖掉", "gains": false}, {"time": "2023-02-14 16:48:16", "content": "又被拉上问了半天，QA一直在纠结推荐策略。。", "gains": false}], "timeline": [["2023-02-14 15:34:29", "2023-02-14 15:34:29"], ["2023-02-14 15:46:57", "2023-02-14 15:59:04"], ["2023-02-14 16:01:00", "2023-02-14 16:36:53"], ["2023-02-14 16:38:00", "2023-02-14 16:49:41"]]}, {"tid": "120420", "parent": "1204", "title": "更新tcc配置", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-14 15:43:54", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-02-14 15:38:00", "2023-02-14 15:46:57"]]}, {"tid": "1407", "parent": "14", "title": "万军at了一个drive满了，创建错误码返回1的问题，预期是要透传下游code", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-14 18:04:10", "ddl": "", "level": 2, "marks": [{"time": "2023-02-15 11:43:00", "content": "定位了一下，发现日志报错错误码8位数:90008072，代码实现透传范围是900011000到900099999，万军写的估计写多一位数了。先确认之前对齐问题，还是写错了。看看fix不。", "gains": false}, {"time": "2023-02-15 15:23:26", "content": "断断续续在回复，一边跟安頔说先push泽彬加埋点，另外锦锦咨询6号推荐创建下跌问题，另外咖啡机没牛奶，下午茶。。等杂事", "gains": false}, {"time": "2023-02-15 18:04:45", "content": "插入咨询了一下，跟万军确认了下情况，还是这边兼容drive旧错误码。让提meego先", "gains": false}, {"time": "2023-02-16 14:18:23", "content": "今天得优先fix上线。。", "gains": false}, {"time": "2023-02-16 14:52:31", "content": "加了几行代码透传，push上分支，现在发scm、ppe验证，提MR，随后没问题再发布上线工单", "gains": false}, {"time": "2023-02-16 15:35:49", "content": "一会万军质疑MR要完善处理，一会前端没处理code。。", "gains": false}, {"time": "2023-02-16 15:36:03", "content": "破事。先处理数据问题分析。", "gains": false}, {"time": "2023-02-16 15:49:34", "content": " 先解决万军在抠的点。。还有对齐一下drive和前端怎么处理code先。", "gains": false}, {"time": "2023-02-16 16:07:15", "content": "语音对齐还是比较高效，觉得也还合适。就加了一行提示。", "gains": false}, {"time": "2023-02-16 16:07:23", "content": "发scm、ppe中", "gains": false}, {"time": "2023-02-21 11:29:36", "content": "关闭meego issue。前端待转需求实现", "gains": false}], "timeline": [["2023-02-15 11:29:03", "2023-02-15 11:49:58"], ["2023-02-15 14:51:00", "2023-02-15 15:23:37"], ["2023-02-15 17:39:00", "2023-02-15 17:53:00"], ["2023-02-16 14:17:55", "2023-02-16 14:17:55"], ["2023-02-16 14:39:46", "2023-02-16 15:02:58"], ["2023-02-16 15:25:00", "2023-02-16 15:36:09"], ["2023-02-16 15:49:32", "2023-02-16 16:07:54"], ["2023-02-21 11:29:11", "2023-02-21 11:30:29"]]}, {"tid": "52010330", "parent": "520103", "title": "命令行功能，能否对接成vim插件，方便折叠、跳转、搜索等等", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-14 19:06:58", "ddl": "", "level": 4, "marks": [], "timeline": []}, {"tid": "4310", "parent": "43", "title": "整理工作任务(CRUD和安排每日任务) & 消息清理 & 文档整理", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-14 19:14:18", "ddl": "", "level": 2, "marks": [{"time": "2023-03-07 16:01:34", "content": "清完消息，有个敏感日志同步给思磊，还有meego机器人，又更新起了meego", "gains": false}, {"time": "2023-03-16 15:00:33", "content": "整理一下现有技术文档的管理。", "gains": false}, {"time": "2023-03-16 15:21:03", "content": "整理了云空间的目录，以A-Z开头标记分类和顺序。有空再整理一份相关的工作方法论", "gains": false}, {"time": "2023-03-16 15:23:16", "content": "值得先mark的是，X笔记&思路梳理部分，在完善之后，要迁移进 => U沉淀输出", "gains": false}, {"time": "2023-03-16 15:36:04", "content": "把技术文档收录到wiki", "gains": false}, {"time": "2023-03-16 15:50:13", "content": "把文档移到wiki，发现会自动创建软连接了。", "gains": false}, {"time": "2023-03-16 15:51:08", "content": "在U沉淀输出预先创建了文档: 【WIP】双品牌&跨unit链路梳理。今天整理的目的也为了这，把待办的事项文档有地方收录，避免管理混乱遗忘", "gains": false}], "timeline": [["2023-02-20 11:13:09", "2023-02-20 11:26:54"], ["2023-02-23 14:15:00", "2023-02-23 14:31:24"], ["2023-03-02 10:55:00", "2023-03-02 11:18:12"], ["2023-03-07 15:51:22", "2023-03-07 16:14:39"], ["2023-03-07 21:13:55", "2023-03-07 21:29:18"], ["2023-03-16 15:00:14", "2023-03-16 15:23:32"], ["2023-03-16 15:35:51", "2023-03-16 16:05:01"]]}, {"tid": "4308", "parent": "43", "title": "各种垃圾踩坑", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-15 11:08:36", "ddl": "", "level": 2, "marks": [{"time": "2023-02-15 11:09:27", "content": "刚打算工作，垃圾mac又无法连上ipad随航。各种重试，插拔usb，重启mac，重启ipad都不行。垃圾玩意。", "gains": false}, {"time": "2023-02-15 11:25:47", "content": "参考这里提供了很多解决方法，都是遇到这个问题，解决方法各异，说明苹果真垃圾。。https://www.zhihu.com/question/385028253 挨个试了一些，有些今日视图都是旧版功能了也搜了一会。最后真正解决的是wifi连到自己手机热点，就可以了。说明是公司网络的问题。这网络真垃圾动不动出问题。。", "gains": false}, {"time": "2023-02-15 11:25:59", "content": "浪费半个钟搞这种玩意。", "gains": false}, {"time": "2023-02-16 14:39:41", "content": "又是垃圾电脑卡成翔，重启后ipad又连不上，又要搞一波热点链接。。", "gains": false}, {"time": "2023-02-16 14:42:03", "content": "一开始卡还是先关了seal，现在重启后不开seal开发机都连不上。。", "gains": false}, {"time": "2023-02-20 11:12:24", "content": "又是垃圾随航时不时卡住的问题。切换usb还是没一会卡住。参考: https://forums.macrumors.com/threads/ipad-sidecar-freezing-temporarily-fix.2331204/ 关闭了密码，待会再看看。\n结果一句话甚至没打完，又卡死了。。密码重新打开", "gains": false}, {"time": "2023-02-20 11:18:27", "content": "垃圾苹果真的没一会卡一下。", "gains": false}, {"time": "2023-02-20 11:38:40", "content": "垃圾苹果，还不如三个屏，本来随航多一个屏为了更高效，现在在拖后腿。", "gains": false}, {"time": "2023-02-23 16:54:27", "content": "垃圾mac是不是花屏，出现在chrome上，感觉不是飞书问题。发现是macos bug。。升级提示几分钟。等了20min。", "gains": false}, {"time": "2023-02-23 17:05:00", "content": "刚重装完mac准备继续任务垃圾kms下游又接口超时了。。", "gains": false}], "timeline": [["2023-02-15 10:52:00", "2023-02-15 11:29:03"], ["2023-02-16 14:17:55", "2023-02-16 14:39:46"], ["2023-02-20 11:00:00", "2023-02-20 11:13:09"], ["2023-02-23 16:26:00", "2023-02-23 16:55:18"]]}, {"tid": "110104", "parent": "1101", "title": "重新出搜索打标优化AB实验方案", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-15 11:49:50", "ddl": "", "level": 3, "marks": [{"time": "2023-02-15 15:51:54", "content": "方案简单写完，主要就是时序图稍微加两步，tcc配置需要说明。其他好像没什么。回头思考一下数据验证、埋点、正确性保证这些。", "gains": false}], "timeline": [["2023-02-15 11:49:58", "2023-02-15 11:57:00"], ["2023-02-15 14:09:19", "2023-02-15 14:51:00"], ["2023-02-15 15:23:37", "2023-02-15 15:52:12"]]}, {"tid": "1408", "parent": "14", "title": "锦锦问模板推荐创建率下降的问题", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-15 15:28:41", "ddl": "", "level": 2, "marks": [{"time": "2023-02-15 16:23:54", "content": "对了半天服务发布、tcc发布、fg发布、数据看板，没发现什么原因导致。。", "gains": false}, {"time": "2023-02-15 16:24:06", "content": "花了半个钟了，需要咨询一下其他人", "gains": false}, {"time": "2023-02-15 16:25:16", "content": "但是话说回来，好像推荐模板自己最近都没看到。。", "gains": false}, {"time": "2023-02-15 16:38:32", "content": "发现个大问题。。之前放量A5-A6只配了fg，坑位还是7坑位。。", "gains": false}, {"time": "2023-02-15 16:41:35", "content": "目前看来起码推荐模型跟配置应该是没问题的。就除了放量漏了。还有之前A2-A4规模也没有全量", "gains": false}, {"time": "2023-02-15 16:59:54", "content": "试图从数据集指标脚本入手，不知道是不是弯路", "gains": false}, {"time": "2023-02-16 15:05:03", "content": "先跟张莹对齐个人指标数据，从组成数据成分看", "gains": false}, {"time": "2023-02-16 15:45:59", "content": "但是想了想，指标理解上没问题，已经属于比较基础的数据了，也很难看出复合成分的变化导致。再底下就是用户行为了。", "gains": false}, {"time": "2023-02-16 17:45:53", "content": "咨询了一会关于字段来源的问题。不一定出现在数据集，可以直接来自hsql的任务字段", "gains": false}, {"time": "2023-02-16 17:46:48", "content": "建立了很多中间指标分析。实际上就是挨个条件过滤，最终定位基本数据指标。结果真的就是推荐模型的创建uv，有一个下降。分母是曝光uv，没有问题", "gains": false}, {"time": "2023-02-16 17:47:27", "content": "分母甚至有微涨。推荐模型对应模板的创建uv，断崖下降", "gains": false}, {"time": "2023-02-16 18:06:27", "content": "捋清楚了前面的放量节点，还有自己失误的一些信息，还有数据看板的逻辑。验证跟自己失误的部分没有关系。看板的逻辑还是应该在早期就介入关注的。", "gains": false}, {"time": "2023-02-16 19:48:27", "content": "看着指标确实没问题，已经接近用户基础数据。只能从细分模板内容、这些情况来分析，看看那是不是模型有问题、过程有bug等等。", "gains": false}, {"time": "2023-02-16 19:59:45", "content": "先确认下打点和日志，模型构建有没有问题吧。", "gains": false}, {"time": "2023-02-16 20:04:04", "content": "好像1.20之后就没rebuild过模型。。这么直接的定位思路，包括看日志，竟然不是第一时间启动", "gains": false}, {"time": "2023-02-16 20:05:40", "content": "垃圾argos，搜索还要限7天。但是从日志看，每天构建是没问题的", "gains": false}, {"time": "2023-02-16 20:16:09", "content": "从打点看，刚好是2月5号到14号直接，没有了模型rebuild。。", "gains": false}, {"time": "2023-02-17 11:31:44", "content": "被方文问了下，得高优排查清楚这个问题了", "gains": false}, {"time": "2023-02-17 12:29:42", "content": "疑问:\n> 1. 是否docx推荐的指标表达式，为什么限制了A5、A6的租户，但是日期又是旧的。\n> > 是想观察这部分租户的指标前后对比？但是限制了推荐模型的模板数据，理论上只有从0开始？但是更奇怪的是1月之前还有数据？> 2. 假如想细分模板看指标数据，是什么导致？或者细分到基础数据，如何看？", "gains": false}, {"time": "2023-02-17 14:19:17", "content": "真是无语了。垃圾基建，日志一直搜不了，又得卡着拉oncall。", "gains": false}, {"time": "2023-02-17 14:22:08", "content": "上次跟进坤踩了很久坑浪费那么多时间也是垃圾基建argos日志丢失。", "gains": false}, {"time": "2023-02-17 14:27:43", "content": "空关键词也搜不出来内容，确认argos问题。。垃圾。先拉oncall，再review一下日志相关代码，怎么快速定位。", "gains": false}, {"time": "2023-02-17 15:11:22", "content": "没拉oncall，看到别人提过一样的问题基本没解决。\n1. 可以并行看hive日志表能不能捞到\n2. 比起捞日志，更重要的是要明确自己找日志想确认什么。而不是拍脑袋随便看看日志找找灵感。即，明确定位思路和手段。看看日志本身还有没有不足的。", "gains": false}, {"time": "2023-02-17 15:19:07", "content": "浪费太多时间了。。得优化一下方式了。虽然很多时间在梳理指标和看板。", "gains": false}, {"time": "2023-02-17 17:26:38", "content": "得精细化捋清楚目前的数据指标。自己也要做日常验证才行。否则出问题、不符合问题，完全没法分析，要结合哪些日志也不知道。哪些日志信息需要日常备份下来以供分析。", "gains": false}, {"time": "2023-02-17 17:33:34", "content": "发现奇怪问题。。LogMatrix日志不是ppe的ipv4的。", "gains": false}, {"time": "2023-02-17 17:38:31", "content": "LogMatrix要区分title是load_from_cache还是rebuild，都会打印。", "gains": false}, {"time": "2023-02-17 17:50:40", "content": "1. 至少模型每日的推荐信息，应该每日可以查看review。以及，每日特定的用户群体，是否match，能够采样打点，进行验证。 => 并且由此，可以关联出来每日的创建模板数量、辐射DAU、以及可以按照模型的维度指标细分查看。\n2. 有不符合的地方能够及时告警，人工解决，才能确保模型是迭代优化的。而不是在有问题的方向上分析数据。\n3. 关于风神看板上的数据指标，如何关联到服务端上的日志对象。必须是内心有数的，清楚细节的，否则容易数据关联错，导致的不符合预期。", "gains": false}, {"time": "2023-02-17 17:59:55", "content": "另外，原本应该怎么做的思路捋清楚了，现在捋一下当前的问题怎么解决：\n1. 首先，自己明确放量坑位是有问题的。（这里待会确认一下能否区分不同入口来观察数据情况）但是不一定是这个导致的当前下降的问题。\n2. 目前指标也大概了解得七七八八，投入的时间也偏多了，需要快速得出结论。优化一下方式，如果日志无法定位，那么想一下下一步要做什么、怎么验证。", "gains": false}, {"time": "2023-02-20 11:29:22", "content": "17号转化率又上升了一大块，多少跟A2-A4的坑位配置有关系。因为这个看板观测的数据就是模型推荐的。至于这些实验数据、跟日志数据，如何更好的设计，还是留待空闲休息的时候再思考。工作时间不要搞需要灵感的事，尽量做手熟、快速产出的事。", "gains": false}, {"time": "2023-02-21 11:42:27", "content": "数分给了结论: @阮锦锦 \n\n\n由于A5-A6租户是在2月9日放量，因此2月6日起的下跌与灰度范围无关。筛选实验的A2-A4租户，大盘模板的创建率在2月6日未出现下降，推荐模板的创建率在2月6日出现明显下降，单看1-7推荐位中，每个推荐位的创建率均有下降，推测与推荐的内容创建用户数变化有关。\n根据以下看板链接可看出，工作周报、2023 团队年度工作规划、会议记录(简洁版)等几个推荐模板的创建用户数均在2月6日出现大幅下降，推测是春节后的开年会议、工作规划这类模板创建量较高，在节后第二周出现回落。\n\n另外，改正tcc配置后几天，有回升的趋势。", "gains": false}], "timeline": [["2023-02-15 15:52:12", "2023-02-15 16:59:59"], ["2023-02-15 17:39:15", "2023-02-15 17:39:00"], ["2023-02-16 15:02:58", "2023-02-16 15:02:58"], ["2023-02-16 15:36:09", "2023-02-16 15:49:32"], ["2023-02-16 16:07:54", "2023-02-16 18:08:16"], ["2023-02-16 19:30:00", "2023-02-16 20:34:21"], ["2023-02-17 11:03:00", "2023-02-17 12:00:00"], ["2023-02-17 14:00:00", "2023-02-17 15:19:24"], ["2023-02-17 17:25:32", "2023-02-17 18:00:00"], ["2023-02-20 11:26:54", "2023-02-20 11:44:48"], ["2023-02-21 11:35:00", "2023-02-21 11:42:35"]]}, {"tid": "430704", "parent": "4307", "title": "【产品方案】仪表盘导入导出方案", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-15 17:23:16", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-02-15 17:23:21", "2023-02-15 17:23:21"]]}, {"tid": "4115", "parent": "41", "title": "对齐任务", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-15 18:05:40", "ddl": "", "level": 2, "marks": [{"time": "2023-02-15 18:06:29", "content": "跟万军顺便对齐了几个产品小需求，哪些陈锐、思磊在跟，以及进度。确保自己接下来有清晰的任务跟进", "gains": false}, {"time": "2023-02-20 11:32:04", "content": "垃圾苹果真的没法忍。。一个早上随航断十几次，一分钟不到就要一直手动重连。", "gains": false}], "timeline": [["2023-02-15 17:53:00", "2023-02-15 18:06:43"]]}, {"tid": "1317", "parent": "13", "title": "跟进：KA版本洗数据脚本关注history表的update_time_v2", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-15 20:01:17", "ddl": "", "level": 2, "marks": [{"time": "2023-02-15 20:03:18", "content": "万军at了一个history表索引没下掉的问题，会议对齐了下", "gains": false}], "timeline": [["2023-02-15 19:43:00", "2023-02-15 20:03:49"]]}, {"tid": "911502", "parent": "9115", "title": "跑步", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-15 20:13:59", "ddl": "", "level": 3, "marks": [{"time": "2023-02-21 20:17:01", "content": "下去兜了一趟，没人打乒乓球，跑步忘带耳机，上来拿完又想干脆明天周会完再跑了，先干活了。", "gains": false}], "timeline": [["2023-02-15 20:59:00", "2023-02-15 22:10:00"], ["2023-02-21 19:59:14", "2023-02-21 20:39:04"], ["2023-02-22 21:00:00", "2023-02-22 21:30:00"], ["2023-03-07 20:05:00", "2023-03-07 21:13:55"], ["2023-03-13 20:13:33", "2023-03-13 21:30:40"]]}, {"tid": "911503", "parent": "9115", "title": "热身", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-15 20:14:07", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-02-15 20:10:00", "2023-02-15 20:59:00"], ["2023-02-21 19:37:43", "2023-02-21 19:59:14"], ["2023-02-22 20:18:33", "2023-02-22 21:00:00"], ["2023-03-07 19:25:00", "2023-03-07 20:05:00"], ["2023-03-13 19:22:02", "2023-03-13 20:13:33"]]}, {"tid": "520605", "parent": "5206", "title": "data-admin", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-15 22:53:37", "ddl": "", "level": 3, "marks": [{"time": "2023-02-15 23:01:04", "content": "需要把之前的sync_record表结构改一下。之前很多无用信息。", "gains": false}, {"time": "2023-02-15 23:12:44", "content": "发现了之前在data-admin里面实现了通过rsync命令行实现文件传输同步的流程。。", "gains": false}, {"time": "2023-02-15 23:17:07", "content": "之前在data-admin里面也是实现了很多逻辑判断的。。而不是简单的操作synchronizer-mysql的服务。那这里step_sync_dbs的逻辑定位就需要再好好调整一下了。看看之前哪些代码可以复用，定位是什么，以及整体流程哪些要放到bash脚本", "gains": false}, {"time": "2023-02-15 23:49:06", "content": "review一下之前data-admin的代码，重新梳理一下data-synchronizer的方案流程图，看看怎么设计。再开始写。", "gains": false}, {"time": "2023-02-16 00:14:59", "content": "check了一下之前的代码，确实实现功能很想现在的nodejs进程。只是之前通过bash调用http去触发，现在直接在容器内启动。本质都是通过js代码去实现。只是之前的代码，比较没有考虑周全，因为没有梳理完成的方案流程，包括scheme检测等。另外就是耦合了具体的favorites库的内容，即包括file_resource同步的逻辑。不过这块是要考虑怎么写，现在也是时候在方案一起捋清楚了。", "gains": false}, {"time": "2023-02-16 00:23:26", "content": "难怪之前只能把data-admin部署到localserver，因为同样需要访问cloud_server_ssh_key去远程rsync文件数据下来。而如果后续想要在管理页面访问当前同步记录情况，又需要通过data-admin部署在cloud_server去访问synchronizer，提供服务。因此，这里划分data-admin服务是必须的，但是得仅做db记录CRUD使用。具体的同步逻辑，迁移到sync_entry启动的各个容器里面去。", "gains": false}, {"time": "2023-02-16 00:40:28", "content": "之前data-admin的代码结构倒是清晰很多。连接双向conn和show tables的逻辑都重复了，还是用sequelize写的。其他的重复倒是不多。", "gains": false}, {"time": "2023-02-16 23:24:32", "content": "现在把data-admin多余的逻辑代码，移到step_sync_dbs，保持只提供db的CRUD逻辑", "gains": false}, {"time": "2023-02-17 00:15:57", "content": "既然改了之前的同步流程，又涉及到同步方向究竟需不需要。local2server和server2local。是不是后面真的有场景需要把local同步到server。。", "gains": false}, {"time": "2023-02-17 00:16:15", "content": "好像迁移服务器的话，是有这个场景。", "gains": false}, {"time": "2023-02-17 00:30:16", "content": "但是之前的双向同步逻辑，主要考虑了双db提供服务，同时写入的模式。这样就要处理数据的冲突等复杂逻辑了。如果只考虑服务器迁移时，才涉及到direction的指定，那其实就不用处理冲突了，而是考虑指定的方向下，比如cloud2local时，就以cloud为写库，local为备份库，此时检测能否增量覆盖同步就行了，如果有数据冲突，就报错且终止，人工介入。这样就会让模式简单许多。\n迁移cloudserver时，同理，local2cloud的模式下，以local库为准，此时cloud库应该是空数据，自然也符合增量写入的场景。", "gains": false}, {"time": "2023-02-17 01:28:42", "content": "又搞了快两个钟，就为了把两部分代码融合到一起，尤其是现在只搞了connectivity检测的部分。把mysql原生和sequelize合并到一起，就为了不浪费之前的代码。。。这ROI也是太低了如果一直搞这种非核心的任务。", "gains": false}, {"time": "2023-02-17 01:31:24", "content": "但是合并之后也是有好处的，移植了之前的代码结构，class还是好，节省很多参数传递。另外之前特殊的sync逻辑也是可以复用一些的，做个参考。另外，之前的流程没有方案流程图梳理，比较简单，现在有了更完善的检测流程，也更加健壮。把两块合起来也是不错的", "gains": false}, {"time": "2023-02-17 22:46:40", "content": "既然设定了direction，以某个为准，那么关于tables的删除，要十分严谨，因为不是纯增量的操作。如果方向错了，那全部被判断为删除。", "gains": false}, {"time": "2023-02-17 23:35:40", "content": "又花了一个钟来调这种没什么价值的东西：把CheckTableScheme步骤切换到SyncTask的类方法里面。把mysql的改成sequelize的", "gains": false}, {"time": "2023-02-18 00:25:02", "content": "现在可以继续实现之前没有的功能了: CheckTableData", "gains": false}, {"time": "2023-02-18 00:30:57", "content": "由于cloud2local之类的direction，并不能标识具体是哪个服务器同步到哪个服务器。甚至同一个服务器都可能重置数据的情况。因此，同步过程中，应该做到仅考虑数据内容本身的检测，不依赖于sync_record之类的记录，来提取上一次同步时间，作为增量同步的时间起点。", "gains": false}, {"time": "2023-02-18 00:31:26", "content": "sync_record的作用应该仅仅用户展示记录信息就好了。不要用作逻辑判断处理", "gains": false}, {"time": "2023-02-18 00:33:47", "content": "同样借鉴table和column的检测，根据direction方向，检测出来adds、removes、sames、modify四部分，再来判断要不要进行同步。如果全部是纯增量adds，那可以自行同步。存在removes和modify，需要人工确认，就行了。", "gains": false}, {"time": "2023-02-18 00:57:14", "content": "又搞了快半个钟nodejs-common，提取了nodejs通用部分。主要logger比较挫，之前stock-crawler写过一遍，复用起来。", "gains": false}, {"time": "2023-02-18 00:57:26", "content": "现在数据校验", "gains": false}, {"time": "2023-02-18 13:26:46", "content": "不要纠结太多，以现有db数据同步场景为驱动，快速实现目的就好。但是各种覆盖、写数据行为之前，做好完善检测，以保守的处理方式就行了。比如检测不通过时，高优error、终止。人工介入，去判断在最简单case之外的场景。", "gains": false}, {"time": "2023-02-18 15:56:28", "content": "又搞了半天把created_at收敛到createTime类型的字段。把datetime收敛到timestamp。但是现在遇到deleteTime默认值不能为null。", "gains": false}, {"time": "2023-02-18 15:56:49", "content": "数据记录的adds、removes、modify检测基本写完了", "gains": false}, {"time": "2023-02-18 15:58:33", "content": "原来是语句没有指定NULL，默认是NOT NULL", "gains": false}, {"time": "2023-02-18 16:18:28", "content": "把blog-mysql和favorites-mysql的所有tables的timestamp字段类型全改好了。现在可以准备数据同步操作了", "gains": false}, {"time": "2023-02-18 18:09:55", "content": "现在开始研究一下怎么生成ddl、新建table", "gains": false}, {"time": "2023-02-18 18:10:20", "content": "记住切忌太多纠结，快速实现需求即可。", "gains": false}, {"time": "2023-02-18 18:11:36", "content": "垃圾sequelize又是各种搜索不到。", "gains": false}, {"time": "2023-02-18 18:11:58", "content": "一搜migrate就是各种命令行，垃圾玩意。", "gains": false}, {"time": "2023-02-18 18:12:58", "content": "用心搜还是有答案的: https://stackoverflow.com/questions/30848530/how-to-programmatically-run-sequelize-migrations", "gains": false}, {"time": "2023-02-18 18:13:43", "content": "但也只是答案。因为sequelize就只能这么搞，通过子命令。说明就是没有答案。说明sequelize就是垃圾。", "gains": false}, {"time": "2023-02-18 18:14:55", "content": "但既然是快速实现需求为目的，不要无谓纠结浪费时间，那么通过exec子命令实现，也行了。sequelize既然垃圾，没那么多时间去浪费在这种逼玩意。太过纠结就是自己垃圾了。", "gains": false}, {"time": "2023-02-18 18:28:35", "content": "又来了。垃圾sequelize，什么资料都乱七八糟的。用法口径都没统一。", "gains": false}, {"time": "2023-02-18 18:32:49", "content": "又是头秃跟一肚子火，又是垃圾玩意", "gains": false}, {"time": "2023-02-18 18:37:19", "content": "其实只要自己不纠结，现在这些子命令、migration还要写入额外config.js文件，都是能快速解决的。何况自己纠结的不一定就是优雅的方式，别人提供的手段，可能是更常见的方案。所以啊，自己很多时间浪费在这种，真就是自找麻烦。自己不意识到，不通过ktask管理发现这其中的巨大时间黑洞，再加上自己执拗的性格，不习惯轻易改变自己认为对的事情，很容易一辈子就浪费太多时间。", "gains": false}, {"time": "2023-02-18 22:03:09", "content": "现在继续快速推进migration的使用。", "gains": false}, {"time": "2023-02-18 22:06:13", "content": "不得不说sequelize还是真的垃圾。对迁移能力做得及其垃圾、独立、兼容性极差，要搞一大堆init、generate。还要依赖Model。傻逼玩意。", "gains": false}, {"time": "2023-02-18 22:09:25", "content": "那看看能不能至少绕过垃圾sequelize执行原生语句，来做迁移。", "gains": false}, {"time": "2023-02-18 22:12:56", "content": "似乎看起来关键是没有生成DDL的原生mysql语句？似乎mysql自身不提供这种导出导入的能力。所以还真的得依赖migration能力来做。", "gains": false}, {"time": "2023-02-18 22:19:30", "content": "太蠢了。浪费这么多时间。实际上还是在错误的方向死抠。既然sequelize没有很好的支持table迁移能力，就不在数据迁移中耦合这些了。让add tables也当成error告警、人工介入就好了。把DDL的操作全职交个db-manger来就好了。", "gains": false}, {"time": "2023-02-18 22:20:07", "content": "果然除了意识到快速推进之前，还要多思考，有没有其他方式尽快折衷解决", "gains": false}, {"time": "2023-02-18 22:38:38", "content": "插入数据的bind变量倒是有点用。很容易就完成新增数据插入", "gains": false}, {"time": "2023-02-18 22:44:52", "content": "原来是bug。时间字段没有拿getTime()比较，直接比较Date对象", "gains": false}, {"time": "2023-02-18 22:45:31", "content": "那现在把只有数据纯增量新增的最简单的情况，的数据同步，已经完成了。这也是可以快速投入使用的大部分场景了。", "gains": false}, {"time": "2023-02-18 22:52:44", "content": "后续可以跑数据同步之前，把所有dbs的init.sql，用CREATE TABLE IF NOT EXISTS来保证新增table就行了。", "gains": false}, {"time": "2023-02-18 22:54:13", "content": "接下来继续data-admin的正事，把同步记录的数据crud处理好。", "gains": false}, {"time": "2023-02-18 22:57:31", "content": "但是既然data-admin现在不用做取上次同步的时间作为updateTime基准，仅仅作为平台记录同步数据情况的监控记录。就不是那么高优完善的事情。", "gains": false}, {"time": "2023-02-18 22:58:00", "content": "既然现在数据同步可用完成，就高优做其他事情。", "gains": false}], "timeline": [["2023-02-15 22:53:37", "2023-02-15 23:17:19"], ["2023-02-15 23:48:33", "2023-02-16 00:40:42"], ["2023-02-16 23:23:37", "2023-02-16 23:23:37"], ["2023-02-16 23:48:42", "2023-02-17 01:32:29"], ["2023-02-17 22:19:13", "2023-02-17 23:38:46"], ["2023-02-18 00:20:05", "2023-02-18 01:18:07"], ["2023-02-18 13:23:03", "2023-02-18 16:45:29"], ["2023-02-18 18:09:19", "2023-02-18 18:33:00"], ["2023-02-18 22:02:48", "2023-02-18 23:02:34"]]}, {"tid": "4205", "parent": "42", "title": "LarkAllHands", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-16 12:03:24", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-02-16 11:05:00", "2023-02-16 12:05:00"]]}, {"tid": "1409", "parent": "14", "title": "买雪松又at了一个boe的问题，创建wiki模板报错", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-16 15:03:41", "ddl": "", "level": 2, "marks": [{"time": "2023-02-21 11:30:05", "content": "上次让先找万军安排定位了。", "gains": false}], "timeline": [["2023-02-17 10:57:26", "2023-02-17 11:03:00"]]}, {"tid": "1410", "parent": "14", "title": "进坤at了一个KA需要导入模板的问题", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-16 15:04:39", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "9119", "parent": "91", "title": "下午茶", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-16 15:28:52", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-02-16 15:02:58", "2023-02-16 15:25:00"], ["2023-02-17 15:19:24", "2023-02-17 15:46:00"], ["2023-03-03 15:02:21", "2023-03-03 15:21:13"], ["2023-03-07 15:12:02", "2023-03-07 15:21:07"], ["2023-03-16 15:23:32", "2023-03-16 15:35:51"]]}, {"tid": "520202", "parent": "5202", "title": "最近favorites一直收藏后处于pending状态", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-16 20:51:32", "ddl": "", "level": 3, "marks": [{"time": "2023-02-16 20:59:44", "content": "没看到mq堆积，那check favorites日志吧。", "gains": false}, {"time": "2023-02-16 21:02:05", "content": "err[Exception (504) Reason: \"channel/connection is not open，看起来似乎又是rabbitmq连接太久自动断开的问题。。", "gains": false}, {"time": "2023-02-16 21:07:15", "content": "应该加个失败自动重连逻辑，否则太脆弱。一重启mq服务就挂", "gains": false}, {"time": "2023-02-16 21:09:26", "content": "快速fix先重启favorites容器就可以了。", "gains": false}], "timeline": [["2023-02-16 20:51:32", "2023-02-16 21:13:15"]]}, {"tid": "520203", "parent": "5202", "title": "修复: mq重连逻辑，以及pending脏数据的定时扫描补偿处理逻辑", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-16 21:10:15", "ddl": "", "level": 3, "marks": [{"time": "2023-02-16 21:12:23", "content": "补偿逻辑需要注意，如果mq堆积着，处于pending状态，以及mq发送失败，处于pending状态，需要不同处理，避免重复发送多个mq", "gains": false}, {"time": "2023-02-16 21:30:45", "content": "关于补偿处理，就需要对mq做幂等性处理，虽然目前主体you-get下载文件是幂等的，但整个逻辑链路不是，如果有太多消息堆积，还是比较耗时。但是要做幂等，就需要前置判断mq状态，需要bilibili-crawler连接db，但目前都是通过mq跟favorites交互的，所以成本高，ROI不高。\n为了不导致大量重复消息堆积，尽量拉长定时扫描区间。但是拉到一天一次，又可能导致一些被删除视频的遗漏拉取。但这种删除+同时mq端出问题的概率比较低。反倒是mq端一出问题，使用的时候导致大量堆积mq，这种case概率还大些。\n因此，结论是拉到4个钟进行一次扫描。如此一天也就6条消息，拖延一周处理，也就42条重复消息", "gains": false}, {"time": "2023-02-19 00:42:52", "content": "补完了。又花了一个多小时，看起来不多的时间，实际上太多了。这些时间投入到其他学习，可以积累成巨大的时间。这些奇奇怪怪的小需求，ROI不高。只是说，前期搭建了一套自己的技术体系。后续有其他需求，可以以相对低的成本补进去。但其实，算上各种app开发，成本还是太高了。", "gains": false}, {"time": "2023-02-19 01:34:57", "content": "pending的触发任务了，但是拉取之后还是没有改pending状态。最后修复一下，开始其他高优任务。", "gains": false}, {"time": "2023-02-19 01:54:42", "content": "定位到垃圾gorm的updatedAt通过gorm指定updateTime还不行。有bug。。更新的时候还是设置了字段", "gains": false}, {"time": "2023-02-19 02:02:54", "content": "垃圾gorm。。。改成CreateTime之后，update语句自己还转成下划线字段。。", "gains": false}, {"time": "2023-02-19 02:06:18", "content": "参考:https://stackoverflow.com/questions/70993570/gorm-not-reading-columns-on-select，原来gorm:\"column:deleteTime\"写成了gorm:\"deleteTime\"", "gains": false}], "timeline": [["2023-02-16 21:24:55", "2023-02-16 21:51:44"], ["2023-02-16 23:22:51", "2023-02-16 23:23:37"], ["2023-02-18 23:02:34", "2023-02-19 00:45:03"], ["2023-02-19 01:34:21", "2023-02-19 02:02:03"], ["2023-02-19 02:02:31", "2023-02-19 02:07:24"]]}, {"tid": "9120", "parent": "91", "title": "被迫浪费时间", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-16 23:45:40", "ddl": "", "level": 2, "marks": [{"time": "2023-02-16 23:48:11", "content": "垃圾深圳大半夜铺路又噪音又放毒气，金海岸租客群每天晚上这个时候各种讨论。搜了一下沥青的有毒成分，群聊了一会。家里找到气味来源原来厨房窗没关。把排气和抽油烟机都开起来了。阳台方向都有轻微气味。全部关了排气，开电梯方向的大门。", "gains": false}, {"time": "2023-02-16 23:48:24", "content": "被迫浪费半个钟。。垃圾深圳。", "gains": false}], "timeline": [["2023-02-16 23:23:37", "2023-02-16 23:48:42"]]}, {"tid": "6107", "parent": "61", "title": "盛典来深圳湾，去manner咖啡", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-17 17:05:26", "ddl": "", "level": 2, "marks": [{"time": "2023-02-17 17:06:38", "content": "万军拉了个KA问题会议打断了，貌似缩略图加载不出来", "gains": false}, {"time": "2023-02-17 17:17:15", "content": "拉完会似乎不用自己做什么。mess了好一会。现在收一下，捋一下高优事情。", "gains": false}], "timeline": [["2023-02-17 15:46:00", "2023-02-17 17:25:32"]]}, {"tid": "520204", "parent": "5202", "title": "高优整理目前的视频收藏目录体系结构。与接下来的task重新整理，关联起来，做好『学习笔记-资源收藏-任务管理』三者联动", "category": null, "status": "阻塞中", "description": "", "priority": 0, "quadrant": 0, "createTime": "2023-02-19 02:01:43", "ddl": "", "level": 3, "marks": [{"time": "2023-02-19 02:42:58", "content": "整理完public_resource的目录", "gains": false}, {"time": "2023-02-19 03:19:21", "content": "大致整理完了，以及一些模型思路《思路模型整理》，明天高优实现四者联动的page功能。把task的目录管理整理出来，把目前的收录关联到任务，以及创建关联笔记。 =》然后快速实践这个模型。用来研究当前的政策历史、与经济走势。", "gains": false}, {"time": "2023-02-19 03:32:51", "content": "想到了，关于继承通用模型的，实际上是继承一个目录体系，不做具体记录项的抽象关联，只有在这个目录体系内的可以做联动，可以用特殊颜色或者标注出来", "gains": false}, {"time": "2023-02-19 10:54:29", "content": "要细说起来，搞这么一套庞大的东西，坑太多了。光是随便一个开源的东西定位bug，都能耗上大量的堪比全职的时间。因此，自己只能尽快搞个70分可以满足自己需求可用的东西出来。然后尽快投入到自己的正事、原始目的当中。不可能为了搞工具而搞工具，浪费这么多生命。投入到正事，万一验证自己的这套模式不可用、比如自己的时间压根没法投入到自己想的那么多巨量的领域学习中去。那这个工具似乎ROI不高。事实上自己的原始目的是，把所有零碎的时间，可以收纳、记录、管理起来，用于学习、提升。那万一自己没有了这个动力、坚持，只想休息放松、享受生活。那可能也沉没成本巨大。\n不过说回来，自己不是这种人。即使休息只想放松享受生活，也是带着思考的、学习艺术、感悟哲学这块提升。", "gains": false}, {"time": "2023-02-19 11:12:23", "content": "发现ktask现在还没有搭建后端服务，数据还没入库。元模型暂时依托到task里面。所以先把task服务搭建起来。", "gains": false}, {"time": "2023-02-19 13:02:03", "content": "但是想到，似乎元模型又不一定是简单的两层目录体系。实际上每个附加的学习类型的task任务，都可以作为一个独立容器，来收纳相关的资料、学习笔记。那这样即使在元模型tasks下，添加一些很细化的task，比如广东各地城市规划、各地GDP研究等。都可以作为一个独立的目录，收纳b站资料和学习笔记。\n=> 那就让这个元模型变得可以继承。并且依附于task，即强绑定了。或者等同的意思。", "gains": false}, {"time": "2023-02-19 20:59:28", "content": "搞这些工具还是走太多弯路了。还不如直接想研究什么，先按照未来的设想，打算用什么做基础笔记，先学习、记录、人工关联。未来如果真的越来越多、模式不错，再逐步完善。否则真的不断浪费生命在毫无意义的事情上面。搞这些垃圾前端坑对自己未来毫无意义。", "gains": false}], "timeline": [["2023-02-19 02:02:03", "2023-02-19 02:02:31"], ["2023-02-19 02:07:24", "2023-02-19 02:56:04"], ["2023-02-19 03:07:00", "2023-02-19 03:19:40"], ["2023-02-19 10:50:32", "2023-02-19 11:14:59"]]}, {"tid": "520103180201", "parent": "5201031802", "title": "先建立仓库和搭建demo", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-19 14:43:39", "ddl": "", "level": 6, "marks": [{"time": "2023-02-19 15:11:19", "content": "又开始反复踩垃圾npx create-react-app的坑。浪费这么多生命，自己写过这么多东西，应该知道，一切代码皆垃圾。不可能按照自己完全想要的方式、完全没有bug的情况来出现。所以，如何尽快避免这些坑，改掉自己的一些心理强迫症，才是救赎之道。", "gains": true}, {"time": "2023-02-19 15:18:08", "content": "初始仓库跑起来了，还是React的首页面。现在加入react，单刀直入写task tree界面", "gains": false}, {"time": "2023-02-19 15:23:31", "content": "拷贝了antd的示例代码，开始出现语法错误了。", "gains": false}, {"time": "2023-02-19 15:25:00", "content": "重新npx create-react-app my-app --template typescript指定ts", "gains": false}, {"time": "2023-02-19 15:35:36", "content": "开始语法报错后。想起来以前用vim跳转定义的灾难性操作。那浪费的时间不是一个数量级的。自己的有些偏执是真的有些可笑和愚蠢。以前浪费那么多时间那么跳转，竟然不尽早想办法做出改进，执迷于大神说过的用原生vim。有些不理智的错误的过度坚持，实际上也是一种愚蠢。实际上treetheme就是自己解决问题的一大进步。以前坚持不想用vim插件，是避免依赖后，切换环境不会使用。但是现在treetheme的同步已经很好了。可以使用了。另一方面自己原生的使用技巧也坚持这么久了。差不多了", "gains": true}, {"time": "2023-02-19 15:36:11", "content": "所以既然现在用ts开发，是不是又要高优解决一下vim的能力问题。否则有些问题定位真的是灾难", "gains": false}, {"time": "2023-02-19 15:57:32", "content": "照着语法提示改，但是垃圾antd和react开始前端页面一堆报错。", "gains": false}, {"time": "2023-02-19 20:25:42", "content": "先解决demo代码为什么报错吧。不折腾垃圾vim插件了。", "gains": false}, {"time": "2023-02-19 20:27:46", "content": "发现npm i安装的react全部是压缩后的代码。没有源码。更不要说跳转定义了。。以前跟源码做映射就非常麻烦。。时常要自己拿源码包去替换node_modules/，如果定位问题需要第三方包，那将是灾难。", "gains": false}, {"time": "2023-02-19 20:28:30", "content": "nodejs就是进化到这样一种垃圾境地。既不稳定，又麻烦。", "gains": false}, {"time": "2023-02-19 20:30:00", "content": "其实回过头来，要解决报错，应该先看最新的用法文档。。。看看哪里用错了，自然解决。。", "gains": false}, {"time": "2023-02-19 20:33:33", "content": "又回到垃圾node的垃圾antd包的垃圾氛围，一个问题搜又搜不到，问题又多。", "gains": false}, {"time": "2023-02-19 20:34:02", "content": "文档又垃圾", "gains": false}, {"time": "2023-02-19 20:35:31", "content": "前提只能先看懂用法。不能瞎碰。", "gains": false}, {"time": "2023-02-19 20:42:31", "content": "编译错误其实稍加理解很好解决。但是今天空白web报错，不是同个问题。", "gains": false}, {"time": "2023-02-19 20:51:54", "content": "顺着报错的error搜了一下，太复杂了。。垃圾前端也越搞越复杂。更垃圾的可能是自己的方式。踩这么多坑搞这么多东西，ROI多少。", "gains": false}, {"time": "2023-02-19 23:18:21", "content": "不要太急切了吧。太焦虑了。压力太大了。就当作慢慢学习、提升、做有趣的事情吧。一点点慢慢做。压力太大就休息、就去玩。去放松。", "gains": false}, {"time": "2023-02-19 23:29:03", "content": "垃圾node垃圾antd就是各种依赖包的版本冲突。", "gains": false}, {"time": "2023-02-19 23:31:14", "content": "垃圾的还是create-react-app导致的两层node_modules/的问题。", "gains": false}, {"time": "2023-02-19 23:31:33", "content": "在里层安装就不会有冲突问题", "gains": false}, {"time": "2023-02-19 23:33:51", "content": "垃圾react跟antd的版本实在受不了了。。各种问题。。垃圾逼玩意。", "gains": false}, {"time": "2023-02-19 23:40:56", "content": "看起来是垃圾react的巨变: https://stackoverflow.com/questions/71831601/ts2786-component-cannot-be-used-as-a-jsx-component", "gains": false}, {"time": "2023-02-19 23:45:37", "content": "按照这里的操作，全部重新来一遍。改成react17", "gains": false}, {"time": "2023-02-19 23:56:54", "content": "照着操作一遍，启动又报其他错。。找不到module 'react-dom/client'", "gains": false}, {"time": "2023-02-20 00:03:35", "content": "antd一堆在react18中报错的组件: https://github.com/ant-design/ant-design/issues/35542\n但是目前的问题是垃圾create-react-app也没法合理降级到17", "gains": false}, {"time": "2023-02-20 00:06:34", "content": "参考: https://stackoverflow.com/questions/71980492/how-do-you-downgrade-from-react-18-to-react-17 注意index.js也要改", "gains": false}, {"time": "2023-02-20 00:19:17", "content": "react版本的问题总算搞定了。但今天那个onCheck类型对不上的问题还在。。js是真的垃圾，版本管理这么烂。", "gains": false}, {"time": "2023-02-20 00:21:19", "content": "还好今天解决过。应该是antd内部自身版本的问题，毕竟那只是demo代码的问题，没有实时更新也理解。现在算是展示树状结构了。。一天下来浪费两个半钟搞这个，但是跨度9个半钟，浪费了一天。", "gains": false}, {"time": "2023-02-20 00:24:41", "content": "既然基础框架搭建完毕，下一步就可以自由发挥了。把task前后端调通。拿到数据渲染。以及，跳转独立的笔记进行编辑。", "gains": false}], "timeline": [["2023-02-19 14:43:44", "2023-02-19 15:39:58"], ["2023-02-19 15:53:13", "2023-02-19 16:00:41"], ["2023-02-19 20:25:15", "2023-02-19 20:52:09"], ["2023-02-19 23:17:29", "2023-02-20 00:25:37"]]}, {"tid": "520504", "parent": "5205", "title": "解决vim开发React+TypeScript的能力问题", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-19 15:39:55", "ddl": "", "level": 3, "marks": [{"time": "2023-02-19 15:48:04", "content": "果然垃圾。又找不到。又屏息气短。", "gains": false}, {"time": "2023-02-19 15:48:35", "content": "只找到一些好几年没维护的高亮插件，没有定义跳转", "gains": false}, {"time": "2023-02-19 15:52:53", "content": "太垃圾了，十分钟找不到先回归任务了。", "gains": false}, {"time": "2023-02-19 18:23:36", "content": "刚刚躺床找了两个链接试一下: \nhttps://thoughtbot.com/blog/modern-typescript-and-react-development-in-vim\nhttps://vimawesome.com/plugin/vim-react-goto-definition", "gains": false}, {"time": "2023-02-19 18:39:53", "content": "其实对于每个任务都关联一个笔记，也是合理的，因为mark本来就要记录这些。", "gains": false}, {"time": "2023-02-19 18:44:13", "content": "一直来对vim插件的原理没有深究，带来很多不便，很多坑都瞎猫碰耗子。\n首先，关于Vundle、NeoBundle、VimPlug、Pathogen分别是什么不清楚。\n第二，关于~/.vim/pack/plugins/start、~/.vim/pack/start/、~/.vim/tpope、~/.vim/bundle几个路径分别什么用不知道。反正教程经常用到这些路径，自己发现插件有些路径有用有些没用，有些还跟教程对不上。具体原理也不清楚。\n第三，关于plugin的载入作用原理不清楚，比如clone了vim-react-goto-definition，放到了vim-go等其他几个插件的路径，结果没有载入。\n第四，有些教程的plugin要执行:source %和:PluginInstall，还有要在.vimrc配置Plugin 'ivo-donchev/vim-react-goto-definition'，这些安装方式和直接通过路径放置，自动载入，有什么区别，也不知道。https://vimawesome.com/plugin/vim-react-goto-definition", "gains": false}, {"time": "2023-02-19 18:45:50", "content": "目前ktask最不方便的地方就是做笔记不方便。", "gains": false}, {"time": "2023-02-19 19:42:00", "content": "看起来vim-react-goto-definition也是垃圾，不要尝试了。", "gains": false}, {"time": "2023-02-19 19:44:03", "content": "coc还一直有维护，看起来也比较全", "gains": false}, {"time": "2023-02-19 19:46:31", "content": "使用教程: https://develop.pulsgarney.com/article/coc-nvim-guideline/", "gains": false}, {"time": "2023-02-19 19:49:24", "content": "原来~/.vim/bundle路径是给Pathogen使用的。同样也需要在~/.vim/autoload/放置pathogen.vim插件", "gains": false}, {"time": "2023-02-19 20:13:44", "content": "放到~/.vim/bundle需要进去yarn build之类的。记得之前放在~/.vim/pack/start，也要到对应clone的仓库里面去build。。", "gains": false}, {"time": "2023-02-19 20:15:43", "content": "遇到需要yarn isntall问题，参考: https://github.com/neoclide/coc.nvim/issues/3258解决，现在coc.vim安装完毕，看看怎么用", "gains": false}, {"time": "2023-02-19 20:16:34", "content": "果然配色是自动生效的", "gains": false}, {"time": "2023-02-19 20:16:57", "content": "gd命令跳转定义也很方便: https://github.com/neoclide/coc.nvim/issues/3044", "gains": false}, {"time": "2023-02-19 20:18:12", "content": "而且速度比垃圾vim-go快多了。真好用啊。早不研究vim插件，真的是一个执念毁了那么多好时光。跟现在一直迟迟不恋爱，可能也是一样的下场。得抓紧了", "gains": true}, {"time": "2023-02-19 20:19:31", "content": "但是好像跳不到react内部去。。那就没用了。。", "gains": false}, {"time": "2023-02-19 20:24:53", "content": "太浪费时间了。又是毫无价值的事情瞎搞了一个多钟。正事又没干。还不如加紧推进正事。也不知道折腾这么多能用上多少。", "gains": false}], "timeline": [["2023-02-19 15:39:58", "2023-02-19 15:53:13"], ["2023-02-19 18:23:09", "2023-02-19 18:47:09"], ["2023-02-19 19:41:06", "2023-02-19 20:25:15"]]}, {"tid": "520103180202", "parent": "5201031802", "title": "实现基础任务界面的框架页面", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-20 00:25:12", "ddl": "", "level": 6, "marks": [{"time": "2023-02-20 01:47:33", "content": "如果把投入的任务时间，标记一下该任务后续的价值。会发现太多时间投入了之后，后续价值为0。随便一想都一大堆。比如rust先ktask，或者说这个中间起到迭代、推进的作用；但是favorites-android的树状图，就真的浪费了很多时间，最后打算用web重新搞一把。这里面很多的问题，可能是一开始没想清楚。可能是做事情的顺序不对，导致一些原本放后、可以避免的事情。甚至不做的事情。\n但是总之，就是没想清楚，没分清高优先级。\n工作这么多年，竟然没对自己这些方面的能力，还有时间投入，做复盘。真不可思议。虽然以前一段时间很颓丧。", "gains": false}, {"time": "2023-02-20 02:09:26", "content": "官方文档有TreeNode用法说明: https://ant.design/components/tree-cn#treenode-props", "gains": false}, {"time": "2023-02-20 02:14:33", "content": "还有TreeNode 参考: https://github.com/ant-design/ant-design/issues/15123", "gains": false}, {"time": "2023-02-20 02:14:59", "content": "现在卡在this.state undefind的问题", "gains": false}, {"time": "2023-02-20 02:18:58", "content": "踩坑: 函数组件中this一直undefined，因为在rendering，参考: https://stackoverflow.com/questions/72567142/why-this-is-returning-undefined-from-my-functional-component-in-reactjs", "gains": false}, {"time": "2023-02-20 02:37:16", "content": "目前遇到的问题是自定义TreeNode无法被Tree接收的问题。参考: https://www.jianshu.com/p/a4ace604c05f", "gains": false}, {"time": "2023-02-20 19:43:51", "content": "cloud上拉代码仓库，发现空间不够了。清理了很多垃圾，释放了二十多G。主要是tree-blog日志和database、视频文件、video-parser视频文件", "gains": false}, {"time": "2023-02-20 23:19:28", "content": "把前端部分到mock接口流程完成。", "gains": false}, {"time": "2023-02-20 23:21:12", "content": "昨晚的TreeNode参考链接是vue的。。，找个react的demo", "gains": false}, {"time": "2023-02-20 23:42:15", "content": "又浪费了22分钟，越搞越焦急，各种头脑风暴搜索，最后找到: https://juejin.cn/post/6907516244153335815。其实反复看到过几遍，看是没有静下心看到正确的使用方式，就自己瞎捣鼓乱使用", "gains": false}, {"time": "2023-02-20 23:45:50", "content": "但是展示上还是有问题。。变成一个复选框跟---", "gains": false}, {"time": "2023-02-20 23:51:29", "content": "原来是title属性没设置。以及React组件中，div用法有误。得传进给自定义的TaskTreeNode。而组件内传给TaskNode的title好像没用", "gains": false}, {"time": "2023-02-20 23:59:14", "content": "又卡了10分钟怎么显示嵌套TreeNode。。怎么试都不行。真的是垃圾。所有东西都不是官方的，垃圾文档什么都没写。", "gains": false}, {"time": "2023-02-21 00:01:16", "content": "静下心思考一些原理、流程、为什么的问题。更加能沉淀知识，以及更加快速解决问题。对于不合适、没办法、官方就是没有的东西，就不要纠结。就算千辛万苦找到方案，也会很多坑。", "gains": false}, {"time": "2023-02-21 00:07:13", "content": "那有没有可能TaskTreeNode是自定义实现的，只通过isTreeNode=1，没办法实质性变成嵌套的行为呢。。这种垃圾实在有可能的。而且还是这么trick的答案。", "gains": false}, {"time": "2023-02-21 00:11:27", "content": "实在垃圾。又浪费快一个钟。", "gains": false}, {"time": "2023-02-21 00:16:40", "content": "看了一遍官方文档提供的属性，貌似不一定自己非得自定义TreeNode组件。又是自己拍脑袋之后在弯路越走越远。这么前后一搞，两晚浪费两个多钟。看似titleRender可以自定义渲染节点的能力。https://zhuanlan.zhihu.com/p/562342054", "gains": false}, {"time": "2023-02-21 00:19:02", "content": "果然上面那个垃圾trick方式，改isTreeNode=1，虽然能展示节点，但是不能嵌套内容。另外，展示节点的效果跟单纯用原生TreeNode没两样。还有各种乱七八糟的坑。明显上层Tree就是把TaskTreeNode当成原生TreeNode来使用。", "gains": false}, {"time": "2023-02-21 00:22:21", "content": "果然可以。算是才找到树状渲染的初步路子。。接下来还要研究useState状态管理、还有接口数据获取。", "gains": false}, {"time": "2023-02-21 00:23:07", "content": "titleRender参考: https://blog.csdn.net/astonishqft/article/details/126092336", "gains": false}, {"time": "2023-02-21 00:28:19", "content": "关于状态管理，参考: https://juejin.cn/post/7106312479185043463  接下来redux是要用上的了。", "gains": false}, {"time": "2023-02-21 00:30:01", "content": "还有useEffect不知道啥。", "gains": false}, {"time": "2023-02-21 00:33:18", "content": "或者其实不应该拍脑袋想到什么就用上什么。而应该先搞定需求，把接口调用模拟请求、数据渲染、实现响应功能，如果这个过程搜索到需要使用redux、useState、useEffect，再了解不迟。", "gains": false}, {"time": "2023-02-21 00:41:01", "content": "好像也还行。暂时没用到useState。先刷牙睡觉了。头快炸了，昨晚三点多后，早上被垃圾吵醒。今晚本来不熬夜的结果又到现在。", "gains": false}, {"time": "2023-02-22 00:09:44", "content": "完成Model和转换渲染。接下来实现各种页面上需要的功能: \n1. 各种信息的展示渲染\n2. 各种操作按钮", "gains": false}, {"time": "2023-02-22 00:21:05", "content": "按钮展示没问题。接下来日期选择。timelines、marks展示。", "gains": false}, {"time": "2023-02-22 00:32:43", "content": "终究还是要引入状态管理了。变量的生命期仅仅是一次性的。要维护pickerOpen的变量，都需要state来维护状态。", "gains": false}, {"time": "2023-02-22 00:39:44", "content": "下一步还是要看怎么把单个task item，独立出来一个组件，去维护state。看setState是以组件为单位的。", "gains": false}, {"time": "2023-02-22 00:51:03", "content": "关于useState的用法，这里讲得清楚，就是函数式组件的方便用法，跟类组件是等价的。 https://dev.to/bytebodger/constructors-in-functional-components-with-hooks-280m", "gains": false}, {"time": "2023-02-22 00:52:27", "content": "但是目前的问题是，TaskTreeNode(node: DataNode)似乎不是真正的Functional Component，React.FC，一在里面使用useState，就会在前端页面报错，无法渲染", "gains": false}, {"time": "2023-02-22 00:52:54", "content": "报错: The above error occurred in the <InternalTreeNode> component", "gains": false}, {"time": "2023-02-22 00:54:18", "content": "状态又开始准备烦躁了。又困又遇到垃圾玩意了。", "gains": false}, {"time": "2023-02-22 01:15:30", "content": "既然antd把Tree当做一个整体的组件，只提供titleRender进行Node渲染覆盖，而不是提供TreeNode自定义子组件的方式，那就顺着现有的来吧。把useState维护到TaskTree组件里面好了。", "gains": false}, {"time": "2023-02-22 01:17:53", "content": "而且看起来TimerPicker也无法隐藏到一个Button里面。所以整个Tree维护一个TimePicker也是合理的。", "gains": false}, {"time": "2023-02-22 01:40:47", "content": "一下子解决了几个问题: \n1. 看到这里titleRender的闭包写法，突然想到只要这里满足Tree组件的函数需求。而闭包里面返回一个自定义的TreeNode组件，就ok了，这样甚至都不要要求什么isTreeNode之类的，就是完全自定义的组件了: https://segmentfault.com/q/1010000039696330\n2. 然后自定义TaskTreeNode组件，不知道怎么通过props传参数，怎么写都不行。搜着发现原来有React.FC和React.Component两种用法，搜了一下，发现原来FC是泛型，可以指定props的自定义类型。参考: https://blog.csdn.net/qq_18913129/article/details/105491090\n3. 不知道为什么titleRender的闭包里面返回时，title无法传递，会报语法错误: return <TaskTreeNode title={v.title} />", "gains": false}, {"time": "2023-02-22 01:46:07", "content": "应该是DataNode类型的title，不是string类型，所以无法赋值。虽然最后渲染出来也是字符串。既然发现上面FC可以泛型指定类型，直接把整个DataNode都传到props里面了。搞定", "gains": false}, {"time": "2023-02-22 01:48:00", "content": "这下成功在TaskTreeNode里面使用useState了", "gains": false}, {"time": "2023-02-22 23:51:51", "content": "今晚搞定timeline和marks的展示。", "gains": false}, {"time": "2023-02-22 23:59:01", "content": "关于timeline和marks的展示，最好直接查看任务详情页里面展示。列表就专门展示任务列表。这样简单多了。否则交互都不好处理。\n另外，现在命令行的体验也是，任务列表就是任务。info才能查看详情。", "gains": false}, {"time": "2023-02-23 00:05:11", "content": "返回jsx多个元素: https://flaviocopes.com/jsx-return-multiple-elements/", "gains": false}, {"time": "2023-02-23 00:12:46", "content": "今晚跑完步早点睡了，不熬夜。", "gains": false}, {"time": "2023-02-23 14:18:58", "content": "调了一些Tag和配色按钮。实际也没什么。午睡了会，继续工作。", "gains": false}, {"time": "2023-02-23 20:29:36", "content": "把Tree看看怎么改成Table+Tree", "gains": false}, {"time": "2023-02-23 20:32:53", "content": "所以发现一开始就应该用树形table。。https://ant.design/components/table-cn#components-table-demo-tree-data\n是今天看了下飞书的任务工具，才意识到这是一个ddl、状态按钮等对齐的table，只是可以缩进下级，成树形展示。", "gains": false}, {"time": "2023-02-23 20:45:48", "content": "所以前面浪费那么多时间踩Tree组件的坑，才过几天时间来看，又属于无用功了。因为Table压根不会遇到titleRender的问题。", "gains": false}, {"time": "2023-02-23 20:58:41", "content": "喝完酒，越写越困。回家写了。", "gains": false}, {"time": "2023-02-23 22:55:57", "content": "完成Table tree的基础渲染。", "gains": false}, {"time": "2023-02-23 23:06:40", "content": "又瞎搞了10分钟在乱调细节，实际上毫无意义。下一步应该是开始添加按钮column，然后跳转详情页。再快速把关联笔记功能搞起来。", "gains": false}, {"time": "2023-02-23 23:41:19", "content": "结果又瞎搞了半个钟。。还是在胡乱调格式。。说白了就是思路不够清晰、做事情不够迅速果断", "gains": false}, {"time": "2023-02-23 23:42:06", "content": "明确一个清晰的action：先寻找页面用什么组件；然后调试button怎么跳转.", "gains": false}, {"time": "2023-02-23 23:42:53", "content": "先看button跳转，那就要引入路由功能了。", "gains": false}, {"time": "2023-02-23 23:50:36", "content": "路由配置参考: https://react-guide.github.io/react-router-cn/docs/guides/basics/RouteConfiguration.html", "gains": false}, {"time": "2023-02-24 00:04:28", "content": "又开始踩垃圾坑了。搞了20分钟愣是没找到垃圾React的路由用法。版本众多，变来变去。搜到的资料乱七八糟。垃圾。不熬夜，早点睡。", "gains": false}, {"time": "2023-02-24 22:17:22", "content": "简单搜了一下发现react-router-dom的资料比较多，没找到react-router，不纠结，直接上手。而且保险起见，React17安装react-router-dom@5而不要v6。也是跟着已有资料的版本来。", "gains": false}, {"time": "2023-02-24 22:26:52", "content": "参考: https://blog.51cto.com/bingcola/5487972 可以路由跳转组件了", "gains": false}, {"time": "2023-02-24 22:34:54", "content": "前端生态是真的垃圾。从框架到库，动不动各种大变动。react-router-dom的v5到v6都能大变样。https://stackoverflow.com/questions/31079081/programmatically-navigate-using-react-router", "gains": false}, {"time": "2023-02-24 22:42:37", "content": "参考: https://stackoverflow.com/questions/42463263/wrapping-a-react-router-link-in-an-html-button 从Link跳转route", "gains": false}, {"time": "2023-02-24 22:42:56", "content": "接下来实现task详情页，以及路由带tid参数", "gains": false}, {"time": "2023-02-24 22:45:09", "content": "然后意识到，其实详情页应该跟列表页是同一个，可以复用的。因为info任何一个节点，都要展示自身的sub tree。这也是目前命令行ktask list --root 1和ktask info 1一样的地方。只是多了Marks和Timelins部分的展示。", "gains": false}, {"time": "2023-02-24 22:45:24", "content": "所以本质上其实drill和info按钮是同一个意思。", "gains": false}, {"time": "2023-02-24 22:46:15", "content": "那么接下来，还是直接合并TaskTree和TaskInfo的页面。只是多实现Marks和Timelines的展示。一切都在一个页面就ok了。", "gains": false}, {"time": "2023-02-24 22:47:29", "content": "所以搞了半天路由又是白费工。", "gains": false}, {"time": "2023-02-24 22:52:14", "content": "timelines在ui界面下，反而有更适合的方式，直接用甘特图的时间区间展示。找一下有没有类似组件。而且后期可以多任务合并展示", "gains": false}, {"time": "2023-02-24 22:54:18", "content": "antd倒是有Timeline组件。但反而不太适合，更像是笔记需要的、或者朋友圈这类时间轴。而不像甘特图的时间进度区间展示。", "gains": false}, {"time": "2023-02-24 22:57:44", "content": "看起来实现甘特图这种没有现成的，是比较耗时。但是定制性比较强。https://juejin.cn/post/7035107631588966430\n所以现在优先考虑一个问题: 有没有什么必须依赖甘特图时间线实现的。\n=> 似乎没有，更加关注跨度和投入几个信息节点，那么先简单实现就好了。目前迫切的是管理笔记功能。", "gains": false}, {"time": "2023-02-24 22:59:06", "content": "关于antd的gantt图: https://github.com/ant-design/ant-design-pro/issues/6582\nhttps://github.com/antvis/G2Plot/issues/1076", "gains": false}, {"time": "2023-02-24 22:59:52", "content": "g2plot已经有了", "gains": false}, {"time": "2023-02-24 23:01:39", "content": "还是实现Marks和Notes更高优", "gains": false}, {"time": "2023-02-24 23:10:21", "content": "感觉marks也可以简单实现。用一个List组件就行了。先不考虑复杂的编辑+富文本编辑框的场景。因为编辑后，是否需要记录editTime等等功能就出来了。目前如果仅仅是为了写错后回去编辑，可以用手动改db的方式。反正目前ktask命令行也没编辑功能。只能后台人工改。", "gains": false}, {"time": "2023-02-24 23:11:15", "content": "先把marks和timeline快速简单实现，然后高优改成后台接口，然后还要改命令行工具。把数据可用后，再高优搞定关联学习note的功能。", "gains": false}, {"time": "2023-02-24 23:42:02", "content": "不要卡太多时间，快速实现marks和timelines，close掉当前事项，继续高效推进其他事情。", "gains": false}, {"time": "2023-02-25 00:02:54", "content": "但是List不能折叠，看一下外层嵌套Collapse", "gains": false}, {"time": "2023-02-25 00:07:54", "content": "成功嵌套。", "gains": false}, {"time": "2023-02-25 00:08:43", "content": "那暂时web实现这块可以先close了。", "gains": false}], "timeline": [["2023-02-20 01:41:37", "2023-02-20 02:37:31"], ["2023-02-20 19:31:00", "2023-02-20 19:48:02"], ["2023-02-20 23:19:12", "2023-02-21 00:41:16"], ["2023-02-21 23:40:33", "2023-02-22 01:01:41"], ["2023-02-22 01:15:00", "2023-02-22 01:50:25"], ["2023-02-22 23:41:53", "2023-02-23 00:12:57"], ["2023-02-23 12:42:42", "2023-02-23 14:15:00"], ["2023-02-23 20:29:20", "2023-02-23 20:59:25"], ["2023-02-23 22:41:50", "2023-02-24 00:05:22"], ["2023-02-24 22:09:03", "2023-02-24 23:11:26"], ["2023-02-24 23:41:32", "2023-02-25 00:12:34"]]}, {"tid": "120421", "parent": "1204", "title": "关注实验数据、打点、日志", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-20 11:36:05", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "110105", "parent": "1101", "title": "写代码", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-20 11:37:04", "ddl": "", "level": 3, "marks": [{"time": "2023-02-20 22:04:33", "content": "cloud跑一下npm start就卡死了，继续重构搜索。", "gains": false}, {"time": "2023-02-21 14:27:43", "content": "开始针对标签搜索的日志流程进行思考，优化", "gains": false}, {"time": "2023-02-21 14:51:57", "content": "捣鼓了之前的日志工具，先可用起来，一边参与日志重构调试。", "gains": false}, {"time": "2023-02-21 17:00:00", "content": "总算基本重构build完，编译通过，接下来debug一遍日志结果，看看有没有变更导致不可用。", "gains": false}, {"time": "2023-02-21 17:08:48", "content": "垃圾开发机又服务不正常了。不知道什么报错了。", "gains": false}, {"time": "2023-02-21 17:19:45", "content": "目前受阻塞20分钟。拉oncall等回复。", "gains": false}, {"time": "2023-02-21 17:20:46", "content": "先启动标签搜索AB实验代码的开发，以及考虑过程中，日志和打点看板的设计、文档梳理。", "gains": false}, {"time": "2023-02-21 17:48:51", "content": "kms恢复正常了，现在验证log是否变更", "gains": false}, {"time": "2023-02-21 18:01:29", "content": "看着原声词搜索的部分没了。", "gains": false}, {"time": "2023-02-21 18:44:37", "content": "kms又超时。。估计boe网络不行。", "gains": false}, {"time": "2023-02-21 18:55:24", "content": "sd lookup suite.security.unified_kms然后ping节点只有2、3ms，估计服务问题", "gains": false}, {"time": "2023-02-21 19:12:51", "content": "通过startraw取消颜色控制符的打印，结果发现还是一样。", "gains": false}, {"time": "2023-02-21 19:19:21", "content": "发现没问题。坑在于自己忘了工具的界面功能区。。第一块因为没有分词列表，所以为空。输入长一点的搜索词就ok了。", "gains": false}, {"time": "2023-02-21 19:30:52", "content": "搜了长词之后，发现确实工具有些问题。但是可以先优化一下中文名的输出。", "gains": false}, {"time": "2023-02-21 20:57:49", "content": "太困了。之前的日志工具写得太复杂了。看着犯困。。", "gains": false}, {"time": "2023-02-21 21:02:03", "content": "发现问题存在于本地的日志，不同field的位置，跟argos拉下来的不太一样。比如key_type和msg的位置顺序不一样。", "gains": false}, {"time": "2023-02-21 21:11:08", "content": "完成fix本地和argos日志位置不对的问题", "gains": false}, {"time": "2023-02-21 21:16:20", "content": "更高优的事情应该先着手标签搜索AB实验的事情，以及新设计的日志信息、打点。\n至于原日志工具如何补充模板name信息，以及进一步完善日志采样、指定userID复现，应该放在后面完善。", "gains": false}, {"time": "2023-02-21 21:29:19", "content": "发现确实重构后漏了一行日志。补上了，再验证一遍。", "gains": false}, {"time": "2023-02-21 21:41:11", "content": "修复了。但是发现标签模板列表为空，又定位了一阵，发现boe应该就是没有打标模板。明天早上可以正式写AB实验的部分了。", "gains": false}, {"time": "2023-02-22 16:35:27", "content": "继续先优化日志工具，主要针对label这部分。方便开发过程中定位。主要关于name的展示。", "gains": false}, {"time": "2023-02-22 17:00:47", "content": "但是boe没有打标的模板数据，需要先mock一些。", "gains": false}, {"time": "2023-02-22 17:11:19", "content": "刚打算完善log，又整理十几分钟当前的埋点文档，把相关的打点看板，收录到一个文档比较。", "gains": false}, {"time": "2023-02-22 17:22:35", "content": "把收敛文档整理完。接下来开始设计具体相关埋点，和日志模块完善。", "gains": false}, {"time": "2023-02-23 17:06:42", "content": "刚重装完mac准备继续任务垃圾kms下游又接口超时了。", "gains": false}, {"time": "2023-02-23 17:06:50", "content": "先其他任务了。", "gains": false}, {"time": "2023-02-24 11:22:54", "content": "其实不用纠结很多，只是把模板name多encode打印出来而已，快速搞完快速推进。。不要继续抠日志了。", "gains": false}, {"time": "2023-02-24 13:53:07", "content": "模板name的日志展示搞定了，顺便修复了多个keyword时得分取最高分流程的展示bug", "gains": false}, {"time": "2023-02-24 14:00:22", "content": "标签搜索部分的展示反而有点问题", "gains": false}, {"time": "2023-02-24 16:17:26", "content": "现在快速接上，捋思路: \n1. 标签问题修复一下。\n2. 接入ab实验逻辑\n3. 打点验证功能\n4. 导入标签(下周)", "gains": false}, {"time": "2023-02-24 16:43:33", "content": "垃圾kms又超时了。。", "gains": false}, {"time": "2023-02-24 16:48:56", "content": "真是一堆垃圾玩意。太低效了。只能先跳过了。先开发核心代码了。", "gains": false}, {"time": "2023-02-24 18:02:20", "content": "刚好把AB实验支持的代码改完，协同kms同学定位。对方加了个实例，另外比对了一下报错的实例，发现sd lookup suite.security.unified_kms查询多出来一个bytesuite的实例。估计就是这个导致的不稳定", "gains": false}, {"time": "2023-02-27 10:48:00", "content": "抓紧时间继续。原定上周写完。", "gains": false}, {"time": "2023-02-27 11:10:09", "content": "花了20min，就定位logviewer的报错。发现自己Mock的标签模板列表，又消失了。不知道谁在变数据。还是自己再想办法mock数据先。", "gains": false}, {"time": "2023-02-27 13:28:23", "content": "早上mock完数据，估计还有缓存，没有直接穿透db获取，现在ok了。。", "gains": false}, {"time": "2023-02-27 13:48:11", "content": "又完善了logviewer的i18nID到templateID的信息。这个也是重要日志。", "gains": false}, {"time": "2023-02-27 14:08:17", "content": "kms又超时了。。这次没有bytesuite了，但是周五加的节点也没了，剩下两个了。", "gains": false}, {"time": "2023-02-27 14:12:47", "content": "早上到现在都只是在完善log信息，继续先写需求代码了。", "gains": false}, {"time": "2023-02-27 14:20:52", "content": "明确一下下一步操作: \n1. 调试tcc配置标签AB实验的功能\n2. 导入标签模板数据，进行验证\n3. 完善标签搜索实验需要的打点统计\n4. 完善日志模块的采样频率、is_debug、specify_uid参数", "gains": false}, {"time": "2023-02-27 14:21:14", "content": "5. 还有很大一部分，单测", "gains": false}, {"time": "2023-02-27 16:23:00", "content": "又优化了标签搜索的日志，关于AB实验的部分也写完了。看起来流程没什么问题。", "gains": false}, {"time": "2023-02-27 16:53:49", "content": "想到metrics打点tag的含义，跟tea埋点上报、风神平台等等数据维度的含义。之前纠结每一条记录，究竟是一次用户行为还是什么。实际上是所有维度的组合数。如果具体维度没有限制、筛选好，就是剩余那些维度的组合量。这是一个爆炸级的数量级。实际上就是张量的概念。所以，关键在于维度信息有多少个、以及如何对维度的筛选条件进行限定。才能得到有用的数量级。稍有不慎，就是一个多维度的组合数量。虽然这里面有一些约束关系，比如某个维度的值，限制了其他维度的可能值，因此不会真正的无限组合，但本质就是这个多维度张量，导致自己对数据基元感觉混乱。", "gains": true}, {"time": "2023-02-27 17:06:05", "content": "不知道为什么metrics看不到打点。流程应该没什么问题了。", "gains": false}, {"time": "2023-02-27 17:07:03", "content": "下一步: 完善日志模块的采样频率、is_debug、specify_uid参数", "gains": false}, {"time": "2023-02-27 19:53:53", "content": "基本写完逻辑，接下来要加网关配置，才能调通链路了。", "gains": false}, {"time": "2023-02-27 20:49:19", "content": "又折腾了一遍kitool安装。。thrift写入treetheme了还好。现在安装完，继续跑thrift", "gains": false}, {"time": "2023-02-27 20:52:49", "content": "垃圾。。折腾完还要thrift 0.9.2。。treetheme配的是0.16.0", "gains": false}, {"time": "2023-02-27 22:20:53", "content": "又定位了好一会thrift-0.9.2安装的问题。make报错了，但是binary生成了，所以没有copy成功。", "gains": false}, {"time": "2023-02-27 23:27:23", "content": "kg执行完毕。", "gains": false}, {"time": "2023-02-27 23:30:44", "content": "剩下的步骤跟以前的笔记有些对不上了。明天再跟万军确认一下好了。。", "gains": false}, {"time": "2023-02-27 23:42:57", "content": "记得上次好像是说，只要改apacana里的thrift文件，导入到网关就行了。甚至生成的client、server部分的go代码都不需要了。因为网关会重新生成？", "gains": false}, {"time": "2023-02-28 11:06:27", "content": "跟万军确认网关发布，准备验证。", "gains": false}, {"time": "2023-02-28 11:14:18", "content": "等万军回复，先绕过网关部分的工作", "gains": false}, {"time": "2023-02-28 11:55:07", "content": "现在提交apacana", "gains": false}, {"time": "2023-02-28 12:59:22", "content": "把其他推荐列表接口一起加上specifyUser，一次性改好了。", "gains": false}, {"time": "2023-02-28 13:04:57", "content": "把系统列表接口也加好了，跑thrift先。", "gains": false}, {"time": "2023-02-28 14:18:59", "content": "提了MR等+2，继续相关代码修改。", "gains": false}, {"time": "2023-02-28 14:36:58", "content": "完成boe tcc配置 & 替换specifyUser代码。接下来：recommend log模块也可以加上log frequency & 先补单测", "gains": false}, {"time": "2023-02-28 14:53:21", "content": "等万军+2。。先补单测了。", "gains": false}, {"time": "2023-02-28 15:42:58", "content": "又跟万军扯了半个钟说服了用法。。", "gains": false}, {"time": "2023-02-28 15:43:18", "content": "thrift还没跑完", "gains": false}, {"time": "2023-02-28 16:40:58", "content": "总算submit。。先buile了。", "gains": false}, {"time": "2023-02-28 17:02:39", "content": "又受思磊阻塞。。先把方法停掉，先写单测了。或者先导入标签。", "gains": false}, {"time": "2023-02-28 20:47:55", "content": "完善了下mock的标签数据，还有isDebug采样之后的日志工具展示。", "gains": false}, {"time": "2023-02-28 20:48:12", "content": "可以先发boe提测的了。明天再导标签了", "gains": false}], "timeline": [["2023-02-20 11:44:48", "2023-02-20 11:57:00"], ["2023-02-20 14:23:04", "2023-02-20 14:23:04"], ["2023-02-20 19:48:02", "2023-02-20 20:22:00"], ["2023-02-21 11:20:05", "2023-02-21 11:29:11"], ["2023-02-21 11:30:29", "2023-02-21 11:35:00"], ["2023-02-21 11:42:35", "2023-02-21 11:45:25"], ["2023-02-21 11:45:25", "2023-02-21 12:00:00"], ["2023-02-21 14:19:33", "2023-02-21 17:41:41"], ["2023-02-21 17:45:22", "2023-02-21 18:04:42"], ["2023-02-21 18:38:22", "2023-02-21 19:37:43"], ["2023-02-21 20:39:04", "2023-02-21 21:42:14"], ["2023-02-22 11:29:51", "2023-02-22 11:29:51"], ["2023-02-22 14:30:35", "2023-02-22 14:36:11"], ["2023-02-22 15:43:18", "2023-02-22 16:00:00"], ["2023-02-22 16:20:18", "2023-02-22 16:20:18"], ["2023-02-22 16:26:09", "2023-02-22 16:26:09"], ["2023-02-22 16:56:12", "2023-02-22 17:22:37"], ["2023-02-23 16:12:00", "2023-02-23 16:26:00"], ["2023-02-23 16:55:18", "2023-02-23 17:07:04"], ["2023-02-23 17:35:02", "2023-02-23 18:00:00"], ["2023-02-23 20:09:35", "2023-02-23 20:09:35"], ["2023-02-24 11:16:45", "2023-02-24 12:00:00"], ["2023-02-24 12:41:35", "2023-02-24 14:16:00"], ["2023-02-24 15:55:34", "2023-02-24 15:55:34"], ["2023-02-24 16:08:41", "2023-02-24 16:08:41"], ["2023-02-24 16:15:50", "2023-02-24 16:43:46"], ["2023-02-24 16:48:14", "2023-02-24 18:06:20"], ["2023-02-27 10:47:45", "2023-02-27 11:31:00"], ["2023-02-27 13:17:02", "2023-02-27 14:37:49"], ["2023-02-27 14:50:00", "2023-02-27 14:50:00"], ["2023-02-27 15:01:49", "2023-02-27 15:50:04"], ["2023-02-27 16:00:09", "2023-02-27 17:19:44"], ["2023-02-27 17:50:00", "2023-02-27 18:00:00"], ["2023-02-27 19:19:05", "2023-02-27 21:06:20"], ["2023-02-27 22:08:08", "2023-02-27 22:24:09"], ["2023-02-27 23:27:07", "2023-02-27 23:45:52"], ["2023-02-28 11:05:33", "2023-02-28 11:31:00"], ["2023-02-28 11:54:51", "2023-02-28 11:57:00"], ["2023-02-28 12:50:00", "2023-02-28 13:05:21"], ["2023-02-28 14:11:43", "2023-02-28 16:01:46"], ["2023-02-28 16:40:24", "2023-02-28 17:39:15"], ["2023-02-28 19:30:03", "2023-02-28 20:48:15"]]}, {"tid": "110106", "parent": "1101", "title": "方案评审", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-20 11:44:38", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-02-22 16:00:00", "2023-02-22 16:20:18"]]}, {"tid": "110110", "parent": "1101", "title": "梳理搜索流程日志收敛到LogObj", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-20 16:11:07", "ddl": "", "level": 3, "marks": [{"time": "2023-02-20 16:12:18", "content": "基本日志都提取到一个位置打印。接下来关注label相关的，看看如何辅助ab实验加日志打点", "gains": false}, {"time": "2023-02-20 18:09:59", "content": "收敛完之后又类似做了一把重构。主要为了抽取独立的标签搜索的逻辑，只对接baseParams参数", "gains": false}], "timeline": [["2023-02-20 14:23:04", "2023-02-20 18:10:00"]]}, {"tid": "1411", "parent": "14", "title": "锦锦反馈mindnote推荐模板热度值都是500", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-20 22:05:01", "ddl": "", "level": 2, "marks": [{"time": "2023-02-20 22:07:08", "content": "从redis、看到cache、abase，发现热度值没问题。最后发现是推荐重构后，templateHeatMap没有对脱离分类关联的推荐模板，再次获取导致的。包括上次搜索不到头脑风暴模板，也是一个原因。以前拿分类关联模板做全量模板。现在很多推荐位置都泄露出了其他模板，导致很多其他的热度值、无法搜索等问题。", "gains": false}, {"time": "2023-02-20 22:07:18", "content": "后续记录一个关联任务优化", "gains": false}, {"time": "2023-02-21 11:51:49", "content": "定位清楚，转为需求任务1108跟进", "gains": false}], "timeline": [["2023-02-20 20:22:00", "2023-02-20 22:07:40"]]}, {"tid": "1108", "parent": "11", "title": "优化：模板全量逻辑，目前由分类辐射，有些推荐、搜索问题", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-21 11:51:13", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "911504", "parent": "9115", "title": "乒乓球", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-21 19:59:07", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "4206", "parent": "42", "title": "TechTalk", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-22 11:12:41", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-02-22 10:30:00", "2023-02-22 11:29:51"], ["2023-02-22 11:29:51", "2023-02-22 11:44:00"]]}, {"tid": "1412", "parent": "14", "title": "陈锐反馈TimeLatency底层panic", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-22 12:40:52", "ddl": "", "level": 2, "marks": [{"time": "2023-02-22 12:43:13", "content": "来回搞半天日志，没搜到，webshell要逃逸。一边听TechTalk，最后还是陈锐截图具体的日志错误stack。不过也大致看错误信息知道是并发写问题。", "gains": false}, {"time": "2023-02-22 14:59:05", "content": "所以刚刚在930301验证了一下并发读写问题，只有map类型才会。结合panic的错误堆栈，应该是github.com/sirupsen/logrus@v1.9.0/entry.go:103行的Fields遍历时导致。Fields的类型定义正是: type Fields map[string]interface{}", "gains": false}, {"time": "2023-02-22 15:08:31", "content": "群里反馈了方案:\n看了下这个真正出问题的还不是在第三方的TimeLatency，应该是在sirupsen/logrus@v1.9.0出现的map并发读写\n所以这里类似的问题，可能logger对象透传到多协程里面，有WithFields之类的用法，可能也会导致\n\n反倒是TimeLatency内部，好像只有一个地方使用了这个logger，估计是外部透传进来的logger，存在传到其他协程的情况，临时解决方式的话，就InitTimeLatency先独立创建logger，不用外部传进来的了。", "gains": false}, {"time": "2023-02-22 15:19:54", "content": "颖鑫反馈了疑问。。发现定位错了。。传进去的Fields是独立参数，跟logger自身无关", "gains": false}, {"time": "2023-03-01 14:46:41", "content": "又报错了。上次没找到原因", "gains": false}, {"time": "2023-03-01 14:51:38", "content": "还是没定位出来。。", "gains": false}], "timeline": [["2023-02-22 11:44:00", "2023-02-22 12:00:00"], ["2023-02-22 14:57:49", "2023-02-22 15:21:55"], ["2023-03-01 14:38:00", "2023-03-01 14:51:57"]]}, {"tid": "4118", "parent": "41", "title": "模板TCC&FG&统一网关&ee/conf 工单同步KA检查", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-22 14:32:48", "ddl": "", "level": 2, "marks": [{"time": "2023-02-23 11:25:33", "content": "先牵头输出文档，把事项推动起来。", "gains": false}, {"time": "2023-02-23 11:45:45", "content": "tcc/fg/统一网关/eeconf 工单同步检查 @葛思磊 @陈锐 这里起了一个文档，提供了示例格式，抽空可以整理一下哈", "gains": false}, {"time": "2023-03-10 16:35:12", "content": "lark导出配置失败，还要找seal oncall申请va节点。。", "gains": false}, {"time": "2023-03-10 16:48:41", "content": "申请完va节点，导出zip完毕，现在写脚本比对。", "gains": false}, {"time": "2023-03-10 17:28:44", "content": "截至目前，大概也就完成基础解析，判断出缺失method的部分。先问下KACICD网关在哪导出。", "gains": false}, {"time": "2023-03-10 18:03:30", "content": "调完各个method内部field的比对逻辑，检测出来不一致的数据不多。只有两个方法的service_timeout不一致。", "gains": false}, {"time": "2023-03-10 18:03:50", "content": "剩下KACICD的导出与比较", "gains": false}, {"time": "2023-03-13 14:52:43", "content": "上周解析了接口的配置，真正字段还没比对。。", "gains": false}, {"time": "2023-03-13 15:20:12", "content": "解析完竟然是相等的。", "gains": false}, {"time": "2023-03-13 15:28:57", "content": "check了一下之前发布遗漏的字段，应该是后续被增量覆盖了。只要最新发布是同步的就ok了。", "gains": false}, {"time": "2023-03-13 15:33:57", "content": "接下来check ee/conf", "gains": false}, {"time": "2023-03-13 16:03:56", "content": "试了一把私有化部署的网关发布，太烂了。diff都没有。", "gains": false}, {"time": "2023-03-13 16:04:04", "content": "现在比对ee/conf", "gains": false}, {"time": "2023-03-13 16:25:28", "content": "发现ee/conf迁移到devops，而且一个少了jp，一个少了ka。。不知道同步有什么问题。但是devops最后的发布记录，内容是在ee/conf仓库的。那么先写代码比较ee/conf仓库好了。需要找个node的yml解析模块", "gains": false}, {"time": "2023-03-13 18:39:33", "content": "总算比对完ee/conf字段", "gains": false}, {"time": "2023-03-13 18:42:08", "content": "剩下两个点:\n1. KACICD需要怎么确认，还是不用确认\n2. 需不需要修复\n3. devops和ee/conf仓库上不一致的是什么情况", "gains": false}, {"time": "2023-03-14 14:08:10", "content": "看看gateway还有哪些需要比对，比如插件配置", "gains": false}, {"time": "2023-03-14 14:13:07", "content": "还要建个meego", "gains": false}, {"time": "2023-03-14 14:20:32", "content": "发现导出压根没有插件相关的。只有发布的时候看运行时配置diff，才能看到middleware_lists", "gains": false}, {"time": "2023-03-14 14:32:14", "content": "kacicd网关确实无法导出配置，而且版本各异，也无须同saas一致，先pass", "gains": false}, {"time": "2023-03-14 14:42:05", "content": "现在处理ee/conf相关的diff，验证修复状态", "gains": false}, {"time": "2023-03-14 14:57:10", "content": "又完善了脚本，展示miss字段的值。", "gains": false}, {"time": "2023-03-14 14:57:23", "content": "现在求证各个相关key的功能和必要性", "gains": false}, {"time": "2023-03-14 15:14:02", "content": "又是一个弯路。发现很多配置项代码都不出现，先check这个，再去贴diff数据值，会省很多时间。", "gains": false}, {"time": "2023-03-14 15:22:28", "content": "等万军check是否无效配置", "gains": false}, {"time": "2023-03-14 16:56:03", "content": "清了一下消息，现在check tcc代码引用的作用", "gains": false}, {"time": "2023-03-14 17:16:17", "content": "ee/conf基本都无须修复", "gains": false}, {"time": "2023-03-14 17:29:59", "content": "网关的也全部确认了下方法，大部分是废弃接口，at了对应人。", "gains": false}, {"time": "2023-03-14 17:30:12", "content": "接下来check陈锐梳理的tcc配置部分", "gains": false}, {"time": "2023-03-14 20:09:51", "content": "check了相似模板推荐接口无流量", "gains": false}, {"time": "2023-03-15 14:46:36", "content": "快速修复网关不一致的发布，又体验无效，拉oncall。找不到保存草稿按钮。", "gains": false}, {"time": "2023-03-15 15:13:04", "content": "发布失败跟着文档的错误指导卡了一会，直接oncall拉语音高效解决。发布中。", "gains": false}, {"time": "2023-03-15 15:16:30", "content": "还有eeconf找一东确认", "gains": false}, {"time": "2023-03-15 15:23:52", "content": "异步message check", "gains": false}, {"time": "2023-03-16 17:39:04", "content": "网关还得再check KA", "gains": false}, {"time": "2023-03-16 17:45:01", "content": "KA同步过，无须操作", "gains": false}], "timeline": [["2023-02-23 11:25:18", "2023-02-23 11:50:00"], ["2023-03-10 16:11:51", "2023-03-10 16:12:10"], ["2023-03-10 16:15:13", "2023-03-10 18:00:00"], ["2023-03-13 10:50:17", "2023-03-13 10:52:00"], ["2023-03-13 14:48:44", "2023-03-13 17:47:09"], ["2023-03-13 17:55:00", "2023-03-13 17:58:00"], ["2023-03-13 18:28:39", "2023-03-13 18:42:23"], ["2023-03-14 11:17:12", "2023-03-14 11:17:23"], ["2023-03-14 14:07:41", "2023-03-14 15:22:31"], ["2023-03-14 16:50:37", "2023-03-14 17:41:06"], ["2023-03-14 17:46:34", "2023-03-14 18:06:30"], ["2023-03-14 19:14:26", "2023-03-14 19:44:07"], ["2023-03-14 19:49:34", "2023-03-14 20:09:55"], ["2023-03-15 14:28:30", "2023-03-15 15:14:22"], ["2023-03-15 15:16:19", "2023-03-15 15:24:18"], ["2023-03-15 17:35:00", "2023-03-15 18:00:00"], ["2023-03-16 17:30:00", "2023-03-16 17:45:53"]]}, {"tid": "930301", "parent": "9303", "title": "验证struct和map的并发写panic", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-22 14:35:59", "ddl": "", "level": 3, "marks": [{"time": "2023-02-22 14:47:56", "content": "验证结论是对一个struct，在不同协程对同一个字段写入，不会panic。但是对一个map的同个key，甚至不同key写入，甚至一个协程写入、另一个协程只是fmt.Printf，都会panic", "gains": false}, {"time": "2023-02-22 14:50:48", "content": "struct的字段能够保证原子的、并发安全的，但是这种操作可能有逻辑错误。参考: https://cloud.tencent.com/developer/article/1882239", "gains": false}], "timeline": [["2023-02-22 14:36:11", "2023-02-22 14:57:49"]]}, {"tid": "4116", "parent": "41", "title": "姚锐让海外TCE也要填资源和降本情况", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-22 16:31:35", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-02-22 19:36:00", "2023-02-22 19:58:38"]]}, {"tid": "12020309", "parent": "120203", "title": "A5A6继续全量星标租户之外的租户+7坑位放量", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-22 19:58:30", "ddl": "", "level": 4, "marks": [{"time": "2023-02-22 22:49:58", "content": "回来自己个人租户验证了下。发现没有模型推荐。。", "gains": false}, {"time": "2023-02-22 22:53:31", "content": "实在有点奇怪。。明天高优定位一下。", "gains": false}], "timeline": [["2023-02-22 19:58:38", "2023-02-22 20:18:33"], ["2023-02-22 22:34:15", "2023-02-22 22:53:44"]]}, {"tid": "4117", "parent": "41", "title": "万军:聊一下文档推荐的调研", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-22 21:57:10", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-02-22 21:30:00", "2023-02-22 22:02:26"]]}, {"tid": "4309", "parent": "43", "title": "其他沟通", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-23 12:00:51", "ddl": "", "level": 2, "marks": [{"time": "2023-02-23 12:01:31", "content": "进坤反馈上次提交一个MR的单测失败被skip了。。一开始以为说单测覆盖率的问题，沟通了好一会发现自己的锅。", "gains": false}], "timeline": [["2023-02-23 11:50:00", "2023-02-23 12:02:17"]]}, {"tid": "4119", "parent": "41", "title": "跟进周五bitable故障演练", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-23 14:23:57", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "120422", "parent": "1204", "title": "加热模板7天调整到1天", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-23 14:31:07", "ddl": "", "level": 3, "marks": [{"time": "2023-02-23 14:31:14", "content": "@卢泽树 记得之前你说上新加热是7天，是不是可能先quick dirty的把这个天数调成1（只在第二天加热）", "gains": false}, {"time": "2023-02-23 14:32:00", "content": "让发了看板链接，先研究下bitable和模板看板数据。然后看看问题什么意思，再看影响，然后再改加热", "gains": false}, {"time": "2023-02-23 14:57:20", "content": "看了半天+记忆ccm和doc的核心指标门户看板，收录到数据整理文档。安頔开始问了，还是得抓紧处理正式。看bitable相关模板数据，改加热模板，然后上线。", "gains": false}, {"time": "2023-02-23 15:11:23", "content": "乱搞一通，curl请求推荐模板看bitable数据。json_pp乱码。不过只是去check了一下bitable置顶模板是哪几个。回过头来看一下安頔的需求，只是不要让加热模板连续7天都曝光，转化率太低。仅仅字面意思而已。", "gains": false}, {"time": "2023-02-23 15:13:09", "content": "着手思考需求后，就开始有疑问了。改成1天能解决什么。曝光应该也不是来自推荐？", "gains": false}, {"time": "2023-02-23 15:16:42", "content": "先确认这个问题: 万军，你之前那个加热模板，是只会出现在最新tab，还是会出现在推荐tab", "gains": false}, {"time": "2023-02-23 15:53:22", "content": "所以一开始正确做法（时间紧急时），就应该直接确认加热部分的逻辑。直接结果导向去改。但是这样少了一些弯路、就少了一些相关思考和数据总结。", "gains": false}, {"time": "2023-02-23 15:58:53", "content": "发现还找不到代码，得找万军问下。", "gains": false}, {"time": "2023-02-23 17:07:14", "content": "开始找万军对齐加热逻辑", "gains": false}, {"time": "2023-02-23 17:26:59", "content": "跟万军对齐了逻辑细节，果然还是高效。问清楚代码在哪里；以及具体的规则；还有tcc配置。\n紧接着的问题就是pm可能有误解。需要对齐先。", "gains": false}, {"time": "2023-02-23 17:31:44", "content": "对比了下，即使同个分类下，新上模板转化率确实也偏低。。还是改一下。", "gains": false}, {"time": "2023-02-23 17:34:54", "content": "发起tcc工单", "gains": false}], "timeline": [["2023-02-23 14:31:24", "2023-02-23 15:16:52"], ["2023-02-23 15:52:25", "2023-02-23 16:00:00"], ["2023-02-23 17:07:04", "2023-02-23 17:35:02"]]}, {"tid": "430705", "parent": "4307", "title": "被万军临时拉上:我的文档库初始化方案", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-23 16:06:16", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-02-23 16:00:00", "2023-02-23 16:12:00"]]}, {"tid": "6108", "parent": "61", "title": "刘雄3.1转邮箱，1908一起吃了八合里", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-23 20:09:03", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-02-23 18:00:00", "2023-02-23 20:09:35"]]}, {"tid": "6109", "parent": "61", "title": "长桥证券推销最后开港卡窗口", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-24 15:08:32", "ddl": "", "level": 2, "marks": [{"time": "2023-02-24 15:53:57", "content": "前面证券开户了。接龙了汇丰办卡。最后发现两个疑问，是两个巨坑: \n1. 汇丰内地卡存10W，是一直不能动的吗，如果动了就要管理费吗 => 是的。 => 那实际上相当于直接缴了10w。。比养老金还坑。拿来投资还有收益。\n2. 这里『在职永久管理费』的意思是必须后续一直字节在职吗 => 这里也是个隐藏坑。\n最后极有可能就是回到每月300的管理费。到时转账也不是零成本的。暂时不是特别需求就直接退出接龙了。", "gains": false}], "timeline": [["2023-02-24 14:16:00", "2023-02-24 15:08:46"], ["2023-02-24 15:38:00", "2023-02-24 15:55:34"]]}, {"tid": "110107", "parent": "1101", "title": "安頔语音对齐搜索标签，还有置顶实验", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-24 15:50:26", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-02-24 15:29:00", "2023-02-24 15:38:00"]]}, {"tid": "520505", "parent": "5205", "title": "rustc在tic和root两个用户独立安装的问题。因此只安装了root，没有安装tic，顺手修复了这个bug。", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-25 00:44:59", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-02-25 00:12:34", "2023-02-25 00:45:18"]]}, {"tid": "960202", "parent": "9602", "title": "test", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-25 15:11:20", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "411105", "parent": "4111", "title": "数据整理", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-27 00:09:37", "ddl": "", "level": 3, "marks": [{"time": "2023-02-27 00:19:43", "content": "先整理置顶模板汇总数据给时光；然后继续整理即的数据梳理文档:飞书数据整理 https://bytedance.feishu.cn/docx/Vzhmdh0wHoBCxbxlaIZcHQCrnDr", "gains": false}, {"time": "2023-02-27 01:32:00", "content": "又自行加了一个多钟的班，整理数据果然比较费时。给时光和安頔都留言了，另外数据也整理了个大概框架，感觉刷牙睡觉了。", "gains": false}, {"time": "2023-02-27 12:10:27", "content": "时光问了下实验数据指标。跟安頔确认。", "gains": false}, {"time": "2023-03-17 15:40:54", "content": "时光让复盘Q1，还有周末发收益数据。找锦锦张莹要先。估计还得自己总结", "gains": false}], "timeline": [["2023-02-27 00:09:43", "2023-02-27 01:35:00"], ["2023-02-27 11:31:00", "2023-02-27 12:10:30"], ["2023-03-17 14:40:37", "2023-03-17 15:17:00"], ["2023-03-17 16:38:05", "2023-03-17 18:00:00"]]}, {"tid": "4120", "parent": "41", "title": "下游kms超时问题跟进", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-27 14:48:30", "ddl": "", "level": 2, "marks": [{"time": "2023-02-27 14:49:22", "content": "开始扯皮浪费时间了，拉了mesh说让拉TCE跟bytesuite，找接口人入口都费劲", "gains": false}, {"time": "2023-02-27 15:01:27", "content": "继续扯皮。oncall没入口，拉人拒绝配合。", "gains": false}, {"time": "2023-02-27 15:01:38", "content": "询问方式没有。", "gains": false}], "timeline": [["2023-02-27 14:39:00", "2023-02-27 14:50:00"], ["2023-02-27 14:50:00", "2023-02-27 15:01:49"]]}, {"tid": "6110", "parent": "61", "title": "这么无聊算了半个钟赵淦森朋友圈的一道初中数学题。。", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-28 00:41:51", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-02-28 00:16:31", "2023-02-28 00:42:49"]]}, {"tid": "120423", "parent": "1204", "title": "全量实验", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-28 10:54:04", "ddl": "", "level": 3, "marks": [{"time": "2023-02-28 10:58:07", "content": "又是坏习惯导致的一些奇葩行为。昨晚明明记着全量的事情。然后因为太晚了，怕打扰别人，要同步信息，所以就没操作。。\n1. 首先，不够明确清晰。既然明确昨天全量，那么有什么理由随便改时间，改了时间也要同步。这样反而让人觉得自己不靠谱、漏事情。\n2. 又是自己想当然的事情，改时间且没同步，太随意，别人就会觉得你的行为很混乱。另外自己觉得无所谓的事情，可能别人觉得很关键，还有你不知道的其他修正工作量。\n3. 还是要注意自己的工作形象，给人靠谱的印象。\n4. 又是自闭拒绝沟通的潜意识。实际上晚上GA就算留个言同步信息，反而是有正面印象的，这么晚close事项。", "gains": true}, {"time": "2023-02-28 11:05:24", "content": "发现还不知道怎么全量。得咨询下", "gains": false}, {"time": "2023-02-28 11:44:45", "content": "秉东、万军都不知道，摸索了好一会，通过快速全量上线->配置中心全量上线了。", "gains": false}, {"time": "2023-02-28 11:50:50", "content": "上线后versionID真的不见了。。还好tcc配置预留了ga功能。", "gains": false}, {"time": "2023-02-28 13:07:16", "content": "tcc配置全量发布完毕。", "gains": false}], "timeline": [["2023-02-28 10:54:10", "2023-02-28 11:05:33"], ["2023-02-28 11:31:00", "2023-02-28 11:54:51"], ["2023-02-28 13:07:07", "2023-02-28 13:07:18"]]}, {"tid": "4121", "parent": "41", "title": "告警太多，收敛到不同群里", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-28 16:00:45", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-02-28 16:01:46", "2023-02-28 16:40:24"]]}, {"tid": "4122", "parent": "41", "title": "二月份meego估分", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-28 17:53:40", "ddl": "", "level": 2, "marks": [{"time": "2023-02-28 21:25:48", "content": "刚到门口就被加急了", "gains": false}, {"time": "2023-02-28 21:46:26", "content": "一堆乌龙事件，找不到meego，电脑又卡，一时间又改超天数", "gains": false}, {"time": "2023-02-28 21:49:40", "content": "一开始还在用这个工具看事项耗时。。还不能看汇总，20多个子任务挨个累计，一点帮助也没有。最后还是找到一个meego直接排期拉满。。", "gains": false}, {"time": "2023-02-28 21:50:17", "content": "从方文加急消息过去了半个钟。。", "gains": false}], "timeline": [["2023-02-28 21:25:37", "2023-02-28 21:53:00"]]}, {"tid": "4123", "parent": "41", "title": "模板运营平台CSRF问题评估", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-28 17:54:08", "ddl": "", "level": 2, "marks": [{"time": "2023-03-10 14:37:51", "content": "早上大致check了一下CSRF的方式，以及飞书云中台的接口，没有referer字段也能调通。\n下午找方文对齐自查标准。让找王磊，但人请假了。\n另外下午一边在梳理网关和链路文档。因为想确认飞书云中台的模板接口走哪个网关，本想直接问万军，想了想不如自己check来得高效、准确、又认知深刻。顺便沉淀文档。", "gains": false}, {"time": "2023-03-10 14:38:19", "content": "按照理解，整改基本也是在网关层整改的。", "gains": false}, {"time": "2023-03-10 14:46:15", "content": "总结一下要输出的信息: \n1. zeus平台将于3月底下线，目前已经迁移到飞书云中台。\n2. 自查以飞书云平台来check。不过zeus接口也一样走lark网关\n3. 目前自查情况，以及需要整改情况", "gains": false}, {"time": "2023-03-10 14:47:18", "content": "果然还是需要前置对齐自查文档规范。。而不是自己埋头瞎搞。王磊给了一份标准文档。直接网关有ccm_csrf防御插件。原理也不是referer字段，而是提取header和cookie校验。里面还有csrf_token字段", "gains": false}, {"time": "2023-03-10 14:50:42", "content": "zeus似乎没有网关？直接suite.web.zeus调用obj_template，看起来logid也是断链的。", "gains": false}, {"time": "2023-03-10 15:13:31", "content": "看起来有两个apigw psm。用的plugin还不太一样。", "gains": false}, {"time": "2023-03-10 15:18:08", "content": "接下来研究网关插件，不同的gw psm是不是可以共用配置插件。", "gains": false}, {"time": "2023-03-10 15:23:32", "content": "发现不同接口配置了不同插件。", "gains": false}, {"time": "2023-03-10 15:26:52", "content": "Zeus相关接口压根没出现在统一网关的配置", "gains": false}, {"time": "2023-03-10 15:29:59", "content": "直接问万军，发现配置在名称为obj_template_thrones的项目", "gains": false}, {"time": "2023-03-10 15:31:12", "content": "业务域一个是OpenApp，业务psm的是CCM。估计这个导致的apigw的psm不同", "gains": false}, {"time": "2023-03-10 15:43:38", "content": "看起来比较简单。可以直接使用ccm_csrf插件。", "gains": false}, {"time": "2023-03-10 15:43:54", "content": "等boe验证一下，线上解封后配置就行了。", "gains": false}, {"time": "2023-03-10 16:02:52", "content": "配置完boe，请求trace好像看不到对应的plugin。", "gains": false}, {"time": "2023-03-10 16:11:16", "content": "等拉oncall咨询", "gains": false}, {"time": "2023-03-14 11:32:43", "content": "拉了oncall都不看消息，体检简直垃圾。", "gains": false}, {"time": "2023-03-14 11:33:01", "content": "发线上试一下", "gains": false}, {"time": "2023-03-14 11:34:13", "content": "自己发现了，配置了之后还要发布。。", "gains": false}, {"time": "2023-03-14 11:34:34", "content": "配置变更是可以看出差异的", "gains": false}, {"time": "2023-03-14 11:41:26", "content": "发布一直报错，oncall刚好回了，顺便问了下，不然自己又得死磕踩坑很久浪费时间。对方有信息，回复eagray集群爆炸了，在扩容了", "gains": true}, {"time": "2023-03-14 11:45:07", "content": "这次需要新增ccm_csrf的接口是内部运营接口，所以风险还好。直接全量应用就ok了", "gains": false}, {"time": "2023-03-14 11:45:40", "content": "boe验证配置成功，现在配置线上", "gains": false}, {"time": "2023-03-14 11:53:54", "content": "提交了飞书pre发布", "gains": false}, {"time": "2023-03-14 12:38:49", "content": "飞书pre验证没问题，配置飞书线上发布", "gains": false}, {"time": "2023-03-14 12:42:06", "content": "发布飞书online中", "gains": false}, {"time": "2023-03-14 12:47:27", "content": "验证没问题，现在发lakr", "gains": false}, {"time": "2023-03-14 12:57:23", "content": "lark线上发完，验证没问题，更新了方文的事项文档进度。关于Lark Pre没找到throne待颖鑫确认是否不存在，还是给权限。", "gains": false}, {"time": "2023-03-14 12:58:35", "content": "颖鑫确认没有，那事项close了", "gains": false}], "timeline": [["2023-03-09 17:20:01", "2023-03-09 17:21:33"], ["2023-03-09 17:37:41", "2023-03-09 18:00:00"], ["2023-03-10 11:30:45", "2023-03-10 12:00:00"], ["2023-03-10 14:10:00", "2023-03-10 16:11:51"], ["2023-03-14 11:17:23", "2023-03-14 11:54:00"], ["2023-03-14 12:33:23", "2023-03-14 12:42:09"], ["2023-03-14 12:45:59", "2023-03-14 13:01:17"]]}, {"tid": "1413", "parent": "14", "title": "万军at了一个英文搜索不稳定的问题", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-02-28 17:58:39", "ddl": "", "level": 2, "marks": [{"time": "2023-03-01 14:09:25", "content": "一开始定位在踩坑就是发现模板名称对不上，后来意识到这个是日志工具的问题，模板是离线导入的所以不一致。后面继续定位发现算法打分的问题。。还是需要提meego优化的。", "gains": false}, {"time": "2023-03-02 20:43:25", "content": "一直催转meego单跟进，先建一个。。", "gains": false}, {"time": "2023-03-02 20:51:34", "content": "新建meego完成", "gains": false}], "timeline": [["2023-03-01 11:08:00", "2023-03-01 12:00:00"], ["2023-03-02 20:43:10", "2023-03-02 21:01:08"]]}, {"tid": "110111", "parent": "1101", "title": "发布boe提测", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-01 14:15:28", "ddl": "", "level": 3, "marks": [{"time": "2023-03-01 14:37:30", "content": "发完boe，写完提测文档", "gains": false}, {"time": "2023-03-01 16:12:31", "content": "真无语。。每次提测都要跟QA沟通一大堆。。", "gains": false}, {"time": "2023-03-01 16:14:07", "content": "每次怎么测试都不会自己思考，需要别人全部讲好细节。。", "gains": false}, {"time": "2023-03-01 16:14:31", "content": "真费劲。", "gains": false}, {"time": "2023-03-01 17:11:10", "content": "大无语。。预留了时间都不准时。", "gains": false}, {"time": "2023-03-01 17:36:03", "content": "9min对完，又花了十几分钟找boe mock的标签打标模板数据。过程代码跳转不了申请了一下依赖", "gains": false}, {"time": "2023-03-02 11:27:59", "content": "昨天发布后陈锐反馈分类tab没数据了。。排查先。", "gains": false}, {"time": "2023-03-02 11:36:42", "content": "垃圾基建真的是时不时超时。解决又不配合。", "gains": false}, {"time": "2023-03-02 11:37:06", "content": "昨天CR有个明显的bug先修了，先发布，估计能解决问题", "gains": false}], "timeline": [["2023-03-01 14:19:20", "2023-03-01 14:37:43"], ["2023-03-01 16:12:17", "2023-03-01 16:15:00"], ["2023-03-01 17:11:19", "2023-03-01 17:36:13"], ["2023-03-02 11:27:24", "2023-03-02 11:37:20"]]}, {"tid": "110112", "parent": "1101", "title": "标签导入", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-01 14:15:34", "ddl": "", "level": 3, "marks": [{"time": "2023-03-02 15:34:36", "content": "CI跑单测太龟速，并行处理。", "gains": false}, {"time": "2023-03-06 14:41:23", "content": "总算写完代码，还没调试", "gains": false}, {"time": "2023-03-06 15:31:11", "content": "写完代码发布两次devops都有各种问题。干脆补充单测调完流程代码没问题先。", "gains": false}, {"time": "2023-03-06 16:58:30", "content": "发现依赖的方法内部过滤了doc。。fix完再发一把。", "gains": false}, {"time": "2023-03-06 17:27:19", "content": "还是没有。。再次scm+devops+调度", "gains": false}, {"time": "2023-03-06 19:31:43", "content": "一个下午发布了24次SCM，每次都重新跑发布流程。。最后加了一把日志。希望这次一次性过。。", "gains": false}, {"time": "2023-03-06 20:01:14", "content": "总算验证搜索ok了。最后把标签id去重。还有配置一下实验标签。还有就是发布cn上线就ok了。", "gains": false}, {"time": "2023-03-06 20:19:28", "content": "发现取回来的标签id已经去重而且排序了。估计接口已经做了。。", "gains": false}, {"time": "2023-03-06 20:19:44", "content": "tcc配置也发起了，等完成就可以验证了。", "gains": false}, {"time": "2023-03-06 20:38:54", "content": "tcc也完成了。服务发布cn要时光审批，明天再接着发了。另外剩下的就是各种细节验证、还有数据看板验证了。", "gains": false}, {"time": "2023-03-07 16:21:52", "content": "有几个变更要导入，顺便改一下成args参数支持。不用每次发布", "gains": false}, {"time": "2023-03-07 16:41:50", "content": "加了args支持，发布上线验证", "gains": false}, {"time": "2023-03-07 16:52:29", "content": "发布完成，重跑了两次失败。快完成了", "gains": false}, {"time": "2023-03-07 16:57:28", "content": "完成", "gains": false}], "timeline": [["2023-03-01 14:37:43", "2023-03-01 14:38:00"], ["2023-03-01 14:51:57", "2023-03-01 14:52:36"], ["2023-03-02 15:34:24", "2023-03-02 16:00:00"], ["2023-03-02 16:16:32", "2023-03-02 16:30:00"], ["2023-03-02 17:00:55", "2023-03-02 17:00:55"], ["2023-03-06 11:05:12", "2023-03-06 12:00:00"], ["2023-03-06 13:15:00", "2023-03-06 18:24:03"], ["2023-03-06 18:52:23", "2023-03-06 20:39:13"], ["2023-03-07 16:21:34", "2023-03-07 17:00:26"]]}, {"tid": "110113", "parent": "1101", "title": "补单测+CR", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-01 14:15:50", "ddl": "", "level": 3, "marks": [{"time": "2023-03-01 14:52:59", "content": "高优这个，抓紧合入，否则有风险", "gains": false}, {"time": "2023-03-02 15:09:16", "content": "调了快一个钟。。就为了Mock两个模板，一大堆关联关系和Mock方法，还有复杂的extra结构，unmarshal的key错了，导致整个流程就没数据", "gains": false}, {"time": "2023-03-02 15:11:13", "content": "总算mock搜索结果成功。再验一下diff覆盖率。不知道为什么一直是0", "gains": false}, {"time": "2023-03-02 17:10:36", "content": "覆盖率补完。", "gains": false}], "timeline": [["2023-03-01 14:52:36", "2023-03-01 15:17:00"], ["2023-03-01 16:00:34", "2023-03-01 16:12:17"], ["2023-03-01 16:15:00", "2023-03-01 16:41:14"], ["2023-03-01 16:45:00", "2023-03-01 17:00:00"], ["2023-03-01 17:36:13", "2023-03-01 17:47:08"], ["2023-03-01 17:55:55", "2023-03-01 18:00:00"], ["2023-03-02 11:37:20", "2023-03-02 12:00:00"], ["2023-03-02 14:08:13", "2023-03-02 15:13:04"], ["2023-03-02 16:30:00", "2023-03-02 17:00:55"], ["2023-03-02 17:00:55", "2023-03-02 17:11:44"], ["2023-03-02 19:00:41", "2023-03-02 19:00:41"]]}, {"tid": "110114", "parent": "1101", "title": "上线验证", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-01 14:16:01", "ddl": "", "level": 3, "marks": [{"time": "2023-03-02 20:05:38", "content": "现在填CheckList准备上线", "gains": false}, {"time": "2023-03-02 20:14:41", "content": "填完CheckList，现在配置线上tcc和AB实验", "gains": false}, {"time": "2023-03-02 20:26:27", "content": "发布debug_config tcc中", "gains": false}, {"time": "2023-03-02 20:34:33", "content": "CI单测又挂。。重试多一遍", "gains": false}, {"time": "2023-03-02 21:10:09", "content": "继续发布tcc配置，合入MR", "gains": false}, {"time": "2023-03-03 11:16:16", "content": "今天三件事: 发布上线；导入标签；填写绩效；今晚还要支持标品演练", "gains": false}, {"time": "2023-03-03 11:32:10", "content": "完成发起工单", "gains": false}], "timeline": [["2023-03-02 20:05:22", "2023-03-02 20:05:22"], ["2023-03-02 20:13:31", "2023-03-02 20:17:03"], ["2023-03-02 20:21:21", "2023-03-02 20:34:43"], ["2023-03-02 21:01:08", "2023-03-02 21:10:17"], ["2023-03-03 11:15:34", "2023-03-03 11:32:20"]]}, {"tid": "1414", "parent": "14", "title": "【KAR升级演练5.10->5.22】【p2】新建sheet时候，选择模版，显示“创建副本，请稍后”，稍后出现弹窗【副本创建失败】（进坤at）", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-01 15:22:03", "ddl": "", "level": 2, "marks": [{"time": "2023-03-01 15:59:55", "content": "定位到sheet的问题，但是模板日志被调warn等级，完全没日志", "gains": false}, {"time": "2023-03-01 16:00:24", "content": "被进坤一顿diss定位思路，但其实属于压根没沟通清楚", "gains": false}], "timeline": [["2023-03-01 15:17:00", "2023-03-01 16:00:34"]]}, {"tid": "4124", "parent": "41", "title": "简单输出文档：共享一分钟定位搜索推荐问题", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-01 18:46:25", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-03-14 19:49:34", "2023-03-14 19:49:34"], ["2023-03-15 15:14:22", "2023-03-15 15:16:19"], ["2023-03-15 15:24:18", "2023-03-15 15:25:00"], ["2023-03-15 15:43:22", "2023-03-15 15:43:22"], ["2023-03-15 16:03:29", "2023-03-15 16:10:00"], ["2023-03-15 16:24:35", "2023-03-15 17:28:53"]]}, {"tid": "911505", "parent": "9115", "title": "羽毛球", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-02 00:05:34", "ddl": "", "level": 3, "marks": [{"time": "2023-03-18 19:28:16", "content": "一大堆子乌龙事。13:30出发去到建行办公楼，草果没提前确认清楚。几个场都在乒乓球比赛。然后他们说去健身房，自己说要先走了。然后又去打了个钟桌球。最后又回去打了会乒乓球，再半场拉了会羽毛球。刚回到。累到腿快断了", "gains": false}], "timeline": [["2023-03-01 19:25:00", "2023-03-01 22:45:00"], ["2023-03-15 19:25:00", "2023-03-15 22:51:00"], ["2023-03-18 13:33:59", "2023-03-18 19:28:26"]]}, {"tid": "4125", "parent": "41", "title": "KA 自定义系统模板库", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-02 11:01:07", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "4126", "parent": "41", "title": "模板服务KA洗数据需求(4月底)", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-02 11:05:03", "ddl": "", "level": 2, "marks": [], "timeline": []}, {"tid": "4127", "parent": "41", "title": "CCM Q1技术需求核对：关联story", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-02 11:18:08", "ddl": "", "level": 2, "marks": [{"time": "2023-03-02 11:26:59", "content": "确认完毕", "gains": false}], "timeline": [["2023-03-02 11:18:12", "2023-03-02 11:27:24"]]}, {"tid": "110115", "parent": "1101", "title": "check一下tenantID改成弱依赖，对推荐流程的影响", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-02 17:11:33", "ddl": "", "level": 3, "marks": [{"time": "2023-03-02 17:55:02", "content": "check大概没问题。顺便做了versionMap收敛到UserInfo。中间被进坤问DRC打断一下。还有改CR，思磊的冲突还没解决。。疯狂催", "gains": false}], "timeline": [["2023-03-02 17:11:44", "2023-03-02 17:56:05"]]}, {"tid": "110116", "parent": "1101", "title": "搜索参数一大堆冲突", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-02 19:29:57", "ddl": "", "level": 3, "marks": [{"time": "2023-03-02 19:30:25", "content": "垃圾开发机又卡死了。。解决着一半思磊的一大堆冲突。。", "gains": false}, {"time": "2023-03-02 19:37:32", "content": "基建真的实在垃圾。。又卡了6min没反映。。重启了。。", "gains": false}, {"time": "2023-03-02 19:38:04", "content": "重启都等半天没反应。。", "gains": false}, {"time": "2023-03-02 20:04:54", "content": "花了一个小时合冲突、踩devbox的坑。。", "gains": false}, {"time": "2023-03-02 20:13:26", "content": "CI没跑过，单测没解决好。又搞了会。", "gains": false}], "timeline": [["2023-03-02 19:00:41", "2023-03-02 20:05:22"], ["2023-03-02 20:05:22", "2023-03-02 20:13:31"], ["2023-03-02 20:17:03", "2023-03-02 20:21:21"]]}, {"tid": "4207", "parent": "42", "title": "绩效填写与自评", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-02 20:54:47", "ddl": "", "level": 2, "marks": [{"time": "2023-03-02 21:17:45", "content": "避免浪费太多时间mess。对于这种非常规的、不擅长的、不够经验的任务，更应该集中思考、开动大脑快速思考，快速找到切入点、快速行动，不管什么方式，需要有所推进，否则很容易陷入mess，没有任何进展、白浪费时间。", "gains": true}, {"time": "2023-03-03 11:33:03", "content": "1. 先整理事项\n2. 整理之前未搜集的收益\n3. 再梳理+润色", "gains": false}, {"time": "2023-03-03 14:52:24", "content": "都有明确思路了还mess。先从半年度梳理的文档、和其他团队文档收集。", "gains": false}, {"time": "2023-03-03 15:40:45", "content": "大概低效看了下进坤的产出，了解了下专利的东西。现在快速梳理。", "gains": false}, {"time": "2023-03-03 16:58:51", "content": "开始写了。。", "gains": false}, {"time": "2023-03-03 22:49:17", "content": "总算高效写完了。", "gains": false}, {"time": "2023-03-07 17:24:19", "content": "还是得先快速推进，梳理一下，写个大概。不要凡事拖到最后", "gains": false}, {"time": "2023-03-07 17:57:58", "content": "mess了好一会后整理起了一分钟定位分享的文档。。", "gains": false}, {"time": "2023-03-08 11:47:03", "content": "与其空想、mess，不如直接动手梳理梳理，思路就不断出现。很多时候都是这样，mess许久不如直接动手整理，很快就推进了。", "gains": true}, {"time": "2023-03-08 14:38:23", "content": "自评写得差不多了就不要mess浪费时间纠结了。先快速推进其他人的。最后有时间再润色、补充", "gains": false}, {"time": "2023-03-08 15:01:15", "content": "即使这样，结果也才完成了自己的", "gains": false}, {"time": "2023-03-08 15:01:56", "content": "接下来写时光的", "gains": false}, {"time": "2023-03-08 20:39:12", "content": "mess了14min", "gains": false}, {"time": "2023-03-08 20:52:04", "content": "又mess 12min。太困了。", "gains": false}, {"time": "2023-03-08 21:03:45", "content": "又mess 10min。。不过延了一天", "gains": false}, {"time": "2023-03-09 14:22:11", "content": "总算写完时光的。", "gains": false}, {"time": "2023-03-09 14:41:21", "content": "又改了些。现在写方文。", "gains": false}, {"time": "2023-03-09 15:15:30", "content": "方文写完，现在李扬", "gains": false}, {"time": "2023-03-09 15:25:09", "content": "李扬写完，现在锦锦", "gains": false}, {"time": "2023-03-09 15:37:00", "content": "先进坤好了", "gains": false}, {"time": "2023-03-09 15:49:43", "content": "进坤写完，现在万军", "gains": false}, {"time": "2023-03-09 16:11:49", "content": "万军写完，现在锦锦", "gains": false}, {"time": "2023-03-09 16:23:33", "content": "锦锦写完，接下来安頔", "gains": false}, {"time": "2023-03-09 16:43:18", "content": "安頔写完，现在玉坤", "gains": false}, {"time": "2023-03-09 16:54:17", "content": "完成。", "gains": false}], "timeline": [["2023-03-02 21:01:08", "2023-03-02 21:01:08"], ["2023-03-02 21:10:17", "2023-03-02 21:18:10"], ["2023-03-03 11:32:20", "2023-03-03 11:35:00"], ["2023-03-03 11:58:13", "2023-03-03 11:58:13"], ["2023-03-03 14:34:23", "2023-03-03 14:34:23"], ["2023-03-03 14:48:05", "2023-03-03 15:02:21"], ["2023-03-03 15:21:13", "2023-03-03 18:00:00"], ["2023-03-03 21:58:40", "2023-03-03 22:53:23"], ["2023-03-07 17:00:26", "2023-03-07 17:00:26"], ["2023-03-07 17:24:00", "2023-03-07 17:37:00"], ["2023-03-08 11:22:54", "2023-03-08 11:22:54"], ["2023-03-08 11:34:15", "2023-03-08 12:00:47"], ["2023-03-08 13:00:39", "2023-03-08 13:00:39"], ["2023-03-08 13:09:09", "2023-03-08 16:04:00"], ["2023-03-08 17:16:23", "2023-03-08 17:30:00"], ["2023-03-08 17:45:44", "2023-03-08 18:00:00"], ["2023-03-08 20:25:01", "2023-03-08 21:20:00"], ["2023-03-09 10:57:44", "2023-03-09 12:00:00"], ["2023-03-09 14:10:00", "2023-03-09 16:30:51"], ["2023-03-09 16:36:49", "2023-03-09 17:03:12"]]}, {"tid": "5201031803", "parent": "52010318", "title": "改造rust工具，改用http接口", "category": null, "status": "进行中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-05 22:54:27", "ddl": "", "level": 5, "marks": [{"time": "2023-03-05 23:27:25", "content": "抽象HttpDB出来，接下来引入reqwest请求库", "gains": false}, {"time": "2023-03-05 23:32:13", "content": "rust踩坑: https://stackoverflow.com/questions/65553557/why-rust-is-failing-to-build-command-for-openssl-sys-v0-9-60-even-after-local-in", "gains": false}, {"time": "2023-03-05 23:35:56", "content": "参考: https://stackoverflow.com/questions/57182594/error-failed-to-run-custom-build-command-for-openssl-v0-9-24 解决，需要apt-get install pkg-config libssl-dev两个。", "gains": false}, {"time": "2023-03-06 00:09:34", "content": "基本链路打通。数据返回需要再处理一下。", "gains": false}, {"time": "2023-03-06 23:01:14", "content": "要改造rust工具，并不像想的那么简单。之前的一大堆ViewTree的展示，还是需要的。只是构建ViewTree的部分，要从json替换到http而已。所以这里的不同DB拆分，更加复杂了。还至少需要抽象出一个View数据层。", "gains": false}, {"time": "2023-03-06 23:02:38", "content": "另外如何做到两个datasource之间的切换，导数据，也是要数据层接口分明的。", "gains": false}, {"time": "2023-03-06 23:07:18", "content": "还有说希望把交互改到vim插件、ncurses等等，这些View展示层也应该抽象出来的", "gains": false}, {"time": "2023-03-06 23:07:38", "content": "接下来看一下怎么抽取一层，可以复用之前的代码", "gains": false}, {"time": "2023-03-06 23:15:27", "content": "之前多少有提取一层model层了。看看怎么重构合适复用。", "gains": false}, {"time": "2023-03-06 23:40:25", "content": "在方案文档梳理一下怎么设计类、如何重构的大体思路。", "gains": false}, {"time": "2023-03-07 23:38:34", "content": "今晚把泛型trait怎么传递解决了。给trait DataSource传递一个trait DataView", "gains": false}, {"time": "2023-03-08 00:18:43", "content": "遇到第一个问题:1. 传给DataSource的T，代表一个DataView，如何构造对象呢？\n2. 这里的T，如何限定为DataView trait的struct呢？", "gains": false}, {"time": "2023-03-08 00:25:35", "content": "第2点好解决。通过T: DataView限定就好", "gains": false}, {"time": "2023-03-08 00:28:06", "content": "关于第1点，应该是设计问题。既然完全通过interface来限定类之间的关系。那么具体实现类在选择的时候，应该实例化对象传参进去。而不是交给抽象的interface去构造对象。这样封装肯定有问题。", "gains": false}, {"time": "2023-03-08 00:35:10", "content": "解决了。同在main实例化DataSource和DataView，把view传给db维护", "gains": false}, {"time": "2023-03-08 00:39:35", "content": "接下来看看DataSource trait本身能否限定一个view属性，限定到DataView，否则内部view.ShowList()方法就真的只能函数参数传view了。", "gains": false}, {"time": "2023-03-08 00:40:50", "content": "看起来似乎是不行的: https://users.rust-lang.org/t/why-doesnt-rust-allow-fields-in-trait/52191/9", "gains": false}, {"time": "2023-03-10 21:52:30", "content": "继续上次遗留问题。", "gains": false}, {"time": "2023-03-10 21:57:02", "content": "警惕又陷入钻牛角尖的误区。rust不支持trait里的field，不是什么设计理念、或者能找到个为什么。总之就是不支持。技术上要实现都是可以实现的。类似C++的virtual class接口和父类、纯虚函数之类的。跟java也不太一样。\n关于rust，这里倒是有人提到，如果需要field，可以设计一个getter。https://users.rust-lang.org/t/why-doesnt-rust-allow-fields-in-trait/52191\n不过还是自己要好好思考一下设计，是不是不合理。或者怎么贴合rust语言的设计，更加合理。", "gains": false}, {"time": "2023-03-10 21:58:15", "content": "提供getter方法倒是可以避免view通过所有方法的参数去传递。直接setter到DataSource的实现类的field就好了。", "gains": false}, {"time": "2023-03-10 22:27:07", "content": "又浪费了好一会时间，纠结println!(\"{:?}\")，想去掉:?。结果发现无法impl Display for Result。因为只能在内建的enum Result的crate里面才能impl。于是只能把结果先match成Ok和Err再打印。就能打印TaskCommandError的自定义格式了。https://doc.rust-lang.org/std/result/", "gains": false}, {"time": "2023-03-10 23:00:52", "content": "早点睡了。明天解决处理: reqwest::blocking::Response::json和unwrap()方法怎么调通的问题", "gains": false}, {"time": "2023-03-11 17:25:00", "content": "基本完成了list接口的获取、解析、展示。", "gains": false}, {"time": "2023-03-11 17:26:13", "content": "接下来改造之前从json读数据，介入到data_model和data_view接口", "gains": false}, {"time": "2023-03-12 13:19:25", "content": "又消除了ViewTaskItem。只保留原始的ModelItem和构建树结构的ViewTaskTree", "gains": false}, {"time": "2023-03-12 13:23:12", "content": "现在改造之前的json_db逻辑，所有展示的都收敛到data_view", "gains": false}, {"time": "2023-03-12 14:17:49", "content": "rust没有重载也不太习惯。。https://juejin.cn/post/7136918610723176461 ", "gains": false}, {"time": "2023-03-12 14:18:10", "content": "new error还需要不同String、&str类型参数换个名字写。。", "gains": false}, {"time": "2023-03-12 14:19:43", "content": "或者要导入不同的trait可以实现。。倒是对trait理解深刻了一点。但是觉得也很多坑。", "gains": false}, {"time": "2023-03-12 14:20:51", "content": "但是最挫的一种做法是在所有参数的位置去做适配，这种就是典型的重复代码屎。一大堆to_string()无效代码，要改起来又是一个灾难。还不如加多一个方法命名", "gains": false}, {"time": "2023-03-12 14:23:38", "content": "可变参数也没有。。。要new error针对format\\!类型的string时，发现没有可变参数。。。https://www.zhihu.com/question/318442390", "gains": false}, {"time": "2023-03-12 15:59:12", "content": "基本把view展示的功能都重构过去了。剩下的逻辑重构也没多大意义，本身start命令的功能都不完善。还是尽快切到http", "gains": false}, {"time": "2023-03-12 23:42:37", "content": "返回response的data为null时，会报错: https://github.com/graphql-rust/juniper/issues/735", "gains": false}, {"time": "2023-03-12 23:55:40", "content": "垃圾rust，又开始因为解析null报错的问题卡住了。准确来说是垃圾serde的bug", "gains": false}, {"time": "2023-03-13 00:01:18", "content": "尤其是原本data就是泛型T。。变得极度恶心起来。", "gains": false}, {"time": "2023-03-13 00:11:17", "content": "没时间搞这些垃圾了。最快速度绕开。", "gains": false}, {"time": "2023-03-13 00:11:27", "content": "浪费了半个多钟了。", "gains": false}, {"time": "2023-03-13 00:17:29", "content": "想绕过只是分分钟的事。。而且也不会增加什么恶心代码。说到底就是自己的死磕、钻牛角尖心理在作祟。非得卡着难题不计成本、不计ROI的去解决。而这种不计ROI的攻克难题，在写代码来讲，是非常危险的。因为到处都是垃圾代码，到处都是bug，很快就会消亡的东西。去磕这种毫无意义。", "gains": true}, {"time": "2023-03-13 00:18:18", "content": "在gateway把有errmsg的直接去掉data字段、不赋值null，就不会出发垃圾reqwest json() serde的解析bug了。", "gains": false}, {"time": "2023-03-13 00:19:27", "content": "但是改成Option是必要的。", "gains": false}, {"time": "2023-03-13 00:29:40", "content": "总算改完start命令。", "gains": false}, {"time": "2023-03-13 00:31:03", "content": "但是timelines和marks还没有展示回来。明天搞完、就可以把现有的db.json数据导入db了。早点睡了。", "gains": false}, {"time": "2023-03-14 00:19:16", "content": "才调完mark命令。。往marks表写入记录。。主要还是各种参数限制判断", "gains": false}, {"time": "2023-03-14 00:55:36", "content": "调完返回timelines的展示格式。。", "gains": false}, {"time": "2023-03-14 01:04:04", "content": "总算返回。", "gains": false}, {"time": "2023-03-14 01:07:48", "content": "marks也限制了超过5个打印首尾。", "gains": false}, {"time": "2023-03-14 01:16:04", "content": "算了先刷牙睡觉了。info命令返回timelines还panic了.", "gains": false}, {"time": "2023-03-14 21:06:14", "content": "太麻烦了还要搞stx2的环境。资源又差，不如回家搞。", "gains": false}, {"time": "2023-03-14 22:37:06", "content": "rust真的太垃圾了。。各种限制", "gains": false}, {"time": "2023-03-14 22:37:40", "content": "为了解除限制搞出一大堆clone更恶心", "gains": false}, {"time": "2023-03-14 22:43:48", "content": "所以不得不把一些需要后置使用的变量，提前构造。比如r.data.ok_or(TaskCommandError::from(&r))，就因为from可以使用&r，而不必r，但是r.data又会被move，所以得提前构造好不必要的r。。性能更加低。。", "gains": false}, {"time": "2023-03-14 22:44:49", "content": "另外关于前面说因为无法重载，不得不针对TaskCommandError针对不同参数类型，搞出来new1、new2等等，发现可以通过实现impl From，来改成TaskCommandError::from()的方式实现", "gains": false}, {"time": "2023-03-14 22:56:07", "content": "不能直接调用resp.to_string()。需要impl Display才可以有to_string()。如果是serde的需要serde_json::to_string", "gains": false}, {"time": "2023-03-14 22:56:45", "content": "踩了很多坑，发现原来之前是对泛型T，缺少了bound，加个where T: Serialize解决很多前面遇到的坑", "gains": false}, {"time": "2023-03-14 23:03:27", "content": "又踩了41min坑，现在回到正题解决报错卡点", "gains": false}, {"time": "2023-03-14 23:04:16", "content": "原来返回success被当成errmsg", "gains": false}, {"time": "2023-03-14 23:05:35", "content": "成功完成timelines和marks的返回格式", "gains": false}, {"time": "2023-03-14 23:08:24", "content": "粗略验证了下总算ok了，洗完澡就可以实现导数据了。", "gains": false}, {"time": "2023-03-14 23:54:32", "content": "现在实现数据切换", "gains": false}, {"time": "2023-03-15 00:22:44", "content": "写一半migrate，发现httpDB的add命令也没有", "gains": false}, {"time": "2023-03-15 00:59:50", "content": "调完add命令", "gains": false}, {"time": "2023-03-15 01:18:02", "content": "又要给add增加forceID参数，否则无法迁移原本任务的id", "gains": false}, {"time": "2023-03-15 01:33:27", "content": "搞定。同理，还有一大堆status之类的字段要加force参数。。", "gains": false}, {"time": "2023-03-15 01:45:05", "content": "总算成功迁移任务了。后面还有marks、timelines的迁移，明晚再搞了。", "gains": false}, {"time": "2023-03-16 02:27:52", "content": "打完羽毛球又跟时光聊到那么晚，太困了。。早点睡了。", "gains": false}, {"time": "2023-03-16 20:02:09", "content": "stx2上这两天make build一直shadowsocks不通，以为server坏了。搞了mac代理发现拉crate成功了。build完成了", "gains": false}, {"time": "2023-03-16 20:35:55", "content": "才在cloud搭建完最新代码调通链路", "gains": false}, {"time": "2023-03-16 23:29:01", "content": "今晚一边在听11周年的废话，浪费时间。", "gains": false}, {"time": "2023-03-17 00:29:22", "content": "继续把error全部切换到BizError", "gains": false}, {"time": "2023-03-17 01:15:49", "content": "直接改起来太多了。。", "gains": false}, {"time": "2023-03-17 01:15:56", "content": "先睡了，明天继续", "gains": false}, {"time": "2023-03-17 23:52:51", "content": "继续改BizError", "gains": false}, {"time": "2023-03-18 00:49:05", "content": "又花了一个钟。。总算改完。。前后又花了几个钟来改写error模块的代码。一开始没用心设计一下，写完全部改一遍工作量太大了。。", "gains": false}, {"time": "2023-03-18 01:10:45", "content": "又回到改BizError之前的问题。。。垃圾post action接口返回的Error一直不展示，不知道哪个环节出了问题。垃圾rust改起来又巨麻烦。", "gains": false}, {"time": "2023-03-18 11:00:41", "content": "调半天意识到migrate流程不会打印ERR。。。", "gains": false}, {"time": "2023-03-18 11:18:49", "content": "103     //   => 2.3 由于参数保证startTime,endTime有宽度，因此不考虑会落在零宽度区间内部的可能。\n104     //          NOTE: 但是这里会导致rust tool在migrate时，现有的零宽度的timelines数据都插入失败。但是考虑到这里价值很低，没必要专门针对这里做逻辑处理。\n=> 果然现在更加精细化管理时间之后，意识到投入这一件事情的巨大成本，以及平时做事情不考虑ROI导致的巨大时间黑洞。现在处理事情确实更加成熟。做有价值的事情。", "gains": true}, {"time": "2023-03-18 11:44:40", "content": "总算最终导入完毕。。", "gains": false}, {"time": "2023-03-18 11:47:43", "content": "接下来还要导marks部分", "gains": false}, {"time": "2023-03-18 12:49:35", "content": "总算marks也导完了。", "gains": false}, {"time": "2023-03-18 13:09:45", "content": "现在开始导最终的数据，并且切换到线上部署了。", "gains": false}, {"time": "2023-03-18 13:16:25", "content": "准备要去打羽毛球了。。", "gains": false}, {"time": "2023-03-18 19:43:32", "content": "因为smysql不通，server key文件需要chmod 600，结果胡乱修了一通。现在ok了", "gains": false}, {"time": "2023-03-18 19:44:41", "content": "现在开始正式导入数据到stx2", "gains": false}], "timeline": [["2023-03-05 23:02:47", "2023-03-06 00:10:10"], ["2023-03-06 22:56:55", "2023-03-06 23:40:29"], ["2023-03-06 23:52:33", "2023-03-07 00:19:09"], ["2023-03-07 23:32:52", "2023-03-08 00:01:00"], ["2023-03-08 00:14:21", "2023-03-08 00:41:00"], ["2023-03-10 00:44:28", "2023-03-10 00:49:34"], ["2023-03-10 21:48:13", "2023-03-10 23:00:59"], ["2023-03-11 12:50:25", "2023-03-11 14:00:00"], ["2023-03-11 16:15:10", "2023-03-11 18:43:06"], ["2023-03-12 00:10:33", "2023-03-12 00:53:28"], ["2023-03-12 11:14:19", "2023-03-12 11:22:56"], ["2023-03-12 12:59:21", "2023-03-12 18:38:33"], ["2023-03-12 20:50:00", "2023-03-12 21:48:00"], ["2023-03-12 22:18:00", "2023-03-13 00:31:18"], ["2023-03-13 21:30:40", "2023-03-13 21:48:40"], ["2023-03-13 23:23:12", "2023-03-14 01:28:35"], ["2023-03-14 20:49:49", "2023-03-14 21:06:18"], ["2023-03-14 22:14:49", "2023-03-14 23:08:34"], ["2023-03-14 23:52:04", "2023-03-15 01:18:09"], ["2023-03-15 01:29:10", "2023-03-15 01:46:51"], ["2023-03-16 01:58:12", "2023-03-16 02:28:00"], ["2023-03-16 19:35:00", "2023-03-16 20:15:00"], ["2023-03-16 20:25:26", "2023-03-16 22:15:00"], ["2023-03-16 23:59:11", "2023-03-17 01:16:29"], ["2023-03-17 23:47:39", "2023-03-18 01:10:52"], ["2023-03-18 10:18:14", "2023-03-18 11:49:47"], ["2023-03-18 12:26:42", "2023-03-18 13:16:35"], ["2023-03-18 19:28:26"]]}, {"tid": "110117", "parent": "1101", "title": "check: 上线后cn搜不到标签。缓存没更新？", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-07 10:56:31", "ddl": "", "level": 3, "marks": [{"time": "2023-03-07 11:07:13", "content": "垃圾argos日志动不动缺失。严重影响效率。", "gains": false}, {"time": "2023-03-07 11:21:04", "content": "为了完善传输日志文件到devbox，简化了sc的流程", "gains": false}, {"time": "2023-03-07 11:21:27", "content": "现在又报上次的错误: scp: Received message too long 458960955\nscp: Ensure the remote shell produces no output for non-interactive sessions", "gains": false}, {"time": "2023-03-07 11:30:29", "content": "暂时先还原了devbox上关于.bashrc完全替换了treetheme的bashrc的操作。还原了non-login的不操作。这里应该之前做datasync设置的non-login特殊处理。后续再看", "gains": false}, {"time": "2023-03-07 11:42:34", "content": "又完善了sc", "gains": false}, {"time": "2023-03-07 11:43:02", "content": "另外家龙反馈线上能搜到。。自己还是没搜到，这真的哪里有问题了。", "gains": false}, {"time": "2023-03-07 11:48:53", "content": "确认了一下请求到哪个实例。发现是online小流量，这部分昨天工单发布覆盖到了。。", "gains": false}, {"time": "2023-03-07 11:56:18", "content": "不纠结怎么置顶header导流了。验证就继续发布online，让家龙验证。", "gains": false}, {"time": "2023-03-07 11:59:12", "content": "思路不够明确。一开始能够在开启实验组+pre的环境下生效，就说明不是缓存的问题。", "gains": false}], "timeline": [["2023-03-07 10:55:00", "2023-03-07 12:00:00"]]}, {"tid": "110118", "parent": "1101", "title": "check: QA反馈一些模板搜索不到", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-07 14:24:31", "ddl": "", "level": 3, "marks": [{"time": "2023-03-07 14:25:28", "content": "比对了一下昨天导入标签记录，扣出了三个关联不到的模板。其他的还有问题，需要进一步定位。", "gains": false}, {"time": "2023-03-07 14:42:30", "content": "垃圾argos又挂。。", "gains": false}, {"time": "2023-03-07 14:54:44", "content": "类似在做线上验证了。。验证了一把，日志链路是正常的。但是这个请求确实没有日志，垃圾argos丢日志。", "gains": false}, {"time": "2023-03-07 14:58:59", "content": "两种可能: 1.刚刚排除了Omit Log的情况竟然出现了，DecideShouldLog有bug。\n2. 从缓存获取的日志，没有analysis标志", "gains": false}, {"time": "2023-03-07 15:00:51", "content": "check了一下确实没放开log。刚刚排除第1点的方式有问题。isDebug还需要specifyUser。这里DecideShouldLog应该放开admins就行", "gains": false}, {"time": "2023-03-07 15:08:38", "content": "完善了一下日志支持admins全打印。构建个scm，发ppe，再复现定位本问题吧。", "gains": false}, {"time": "2023-03-07 15:21:29", "content": "发完ppe，现在验证", "gains": false}, {"time": "2023-03-07 15:39:17", "content": "定位到展示不在模板源，顺便给viewer加个warning class。不够显眼。", "gains": false}, {"time": "2023-03-07 15:39:31", "content": "但是又有问题定位。。", "gains": false}, {"time": "2023-03-07 15:44:13", "content": "才定位到logviewer的bug。重复代码没有提取，关于for loop i=0的地方，时不时出现不一致的表现。。", "gains": false}, {"time": "2023-03-07 15:46:03", "content": "那进一步就是看看为什么不在模板源里面。八成是没配置到category", "gains": false}, {"time": "2023-03-07 15:46:27", "content": "精确搜索也是搜不出来", "gains": false}], "timeline": [["2023-03-07 14:14:00", "2023-03-07 15:12:02"], ["2023-03-07 15:21:07", "2023-03-07 15:51:22"]]}, {"tid": "110119", "parent": "1101", "title": "网关发布isDebug参数", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-07 15:50:11", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "110301", "parent": "1103", "title": "整理文档做团队分享", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-07 15:58:16", "ddl": "", "level": 3, "marks": [], "timeline": [["2023-03-07 17:37:00", "2023-03-07 18:01:45"]]}, {"tid": "110120", "parent": "1101", "title": "fix: metrics打点经qps和labelMap放大，导致内存不断上涨", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-08 10:58:15", "ddl": "", "level": 3, "marks": [{"time": "2023-03-08 11:09:59", "content": "还没到公司就被at一个内存不断上涨的问题。八成metrics打点里面也存在内存泄露。到公司第一时间回滚了工单。还要跟万军扯一通，万军知识还是不够扎实。sock文件、内存泄露、火焰图、agent很多东西敏感度不够", "gains": false}, {"time": "2023-03-08 11:10:26", "content": "但打点代码确实存在倍数放大的问题。", "gains": false}, {"time": "2023-03-08 11:12:25", "content": "这metrics sdk太鸡肋了。先少一个打点，快速fix了。今天一大堆事情。", "gains": false}, {"time": "2023-03-08 11:18:22", "content": "fix完成，跑ci", "gains": false}, {"time": "2023-03-08 11:34:11", "content": "CI无敌龟速还报错重试。焦虑等待重试焦虑。。", "gains": false}, {"time": "2023-03-08 11:41:42", "content": "又重试CI报错，才发现原来build都没通过..", "gains": false}, {"time": "2023-03-08 13:08:53", "content": "合入Mr、scm完成、提交工单、执行中。。", "gains": false}], "timeline": [["2023-03-08 10:17:00", "2023-03-08 11:22:54"], ["2023-03-08 11:22:54", "2023-03-08 11:34:15"], ["2023-03-08 13:00:39", "2023-03-08 13:09:09"]]}, {"tid": "1415", "parent": "14", "title": "搜索不到lark需求提测模板", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-08 16:38:24", "ddl": "", "level": 2, "marks": [{"time": "2023-03-08 16:39:04", "content": "Omit Log。logviewer又报错，才发现Omit有bug。fix了一下。", "gains": false}, {"time": "2023-03-08 16:39:16", "content": "通过netptune复现rpc请求。", "gains": false}, {"time": "2023-03-08 16:39:39", "content": "还需要psm工单请求", "gains": false}, {"time": "2023-03-08 16:50:03", "content": "初步定位可能模板源压根就没有。", "gains": false}, {"time": "2023-03-08 16:54:21", "content": "顺便又优化了点sc的流程", "gains": false}, {"time": "2023-03-08 17:11:01", "content": "分享后还是没有。另外check了user_share表，之前的分享关系已经被delete了。", "gains": false}, {"time": "2023-03-14 14:05:05", "content": "目前能看到链接跟分享关系的体验有点问题。分享关系是跟协作者列表绑定的", "gains": false}], "timeline": [["2023-03-08 16:04:00", "2023-03-08 17:16:23"]]}, {"tid": "4128", "parent": "41", "title": "帮安頔配置一个置顶模板", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-09 16:20:09", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-03-09 17:03:12", "2023-03-09 17:20:01"]]}, {"tid": "1416", "parent": "14", "title": "CPU内存上涨", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-13 11:35:16", "ddl": "", "level": 2, "marks": [{"time": "2023-03-13 11:36:53", "content": "定位结果发现占用大部分CPU的反而是之前万军的旧版list接口，占了15%，因为大量的json.Unmarshal等操作。其次是重构后的biz_checker旁路验证占了5%。上线后到现在没下线。", "gains": false}, {"time": "2023-03-13 14:26:40", "content": "又出现。。先扩容了。解封后再上线。", "gains": false}, {"time": "2023-03-13 14:48:24", "content": "提交扩容工单", "gains": false}, {"time": "2023-03-14 10:46:23", "content": "现在验证代码，下掉biz_checker和v1接口", "gains": false}, {"time": "2023-03-14 11:03:24", "content": "垃圾下游kms又超时", "gains": false}, {"time": "2023-03-14 11:16:16", "content": "发布boe中，提交pre工单审核。boe验证没问题就进一步发布。", "gains": false}, {"time": "2023-03-14 12:45:41", "content": "pre环境验证没问题，继续走工单", "gains": false}, {"time": "2023-03-14 16:48:18", "content": "观察看板验证", "gains": false}, {"time": "2023-03-14 16:50:04", "content": "下降不少", "gains": false}], "timeline": [["2023-03-13 10:52:00", "2023-03-13 11:37:00"], ["2023-03-13 14:19:00", "2023-03-13 14:48:44"], ["2023-03-14 10:46:00", "2023-03-14 11:17:12"], ["2023-03-14 12:42:09", "2023-03-14 12:45:59"], ["2023-03-14 16:48:11", "2023-03-14 16:50:37"]]}, {"tid": "4129", "parent": "41", "title": "看万军和方文的调研文档:LLaMA、BLOOM、NotionAI、chatGPT的调用 => 文档AI助手PRD", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-13 11:54:00", "ddl": "", "level": 2, "marks": [{"time": "2023-03-16 17:22:25", "content": "看了doc&AI结合落地roadmap文档，才意识到这是CCM一个大事项，以及如火如荼展开中。", "gains": false}], "timeline": [["2023-03-13 11:37:00", "2023-03-13 11:52:00"], ["2023-03-16 16:55:00", "2023-03-16 17:30:00"]]}, {"tid": "4130", "parent": "41", "title": "安頔咨询如何确认模板推荐策略", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-13 11:55:23", "ddl": "", "level": 2, "marks": [{"time": "2023-03-13 14:18:09", "content": "复现不了，让mary提供logid", "gains": false}], "timeline": [["2023-03-13 11:52:00", "2023-03-13 12:00:00"], ["2023-03-13 14:11:34", "2023-03-13 14:19:00"]]}, {"tid": "411106", "parent": "4111", "title": "梳理一下KA、CICD、多品牌、跨unit的各种服务版本、发布流程、网关流程、配置流程、接口请求链路等等", "category": null, "status": "待启动", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-13 16:05:53", "ddl": "", "level": 3, "marks": [], "timeline": []}, {"tid": "4131", "parent": "41", "title": "帮锦锦写个性化推荐研发视角看法", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-14 15:36:51", "ddl": "", "level": 2, "marks": [{"time": "2023-03-14 16:00:13", "content": "看了好一会doc模板推荐的数据，模型推荐确实有增长", "gains": false}, {"time": "2023-03-14 16:43:01", "content": "中间被万军拉上去对了个问题。", "gains": false}], "timeline": [["2023-03-14 15:37:04", "2023-03-14 16:43:32"]]}, {"tid": "1417", "parent": "14", "title": "反馈列表筛选文档类型不对的问题", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-14 18:05:22", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-03-14 18:06:30", "2023-03-14 18:07:59"]]}, {"tid": "4311", "parent": "43", "title": "时光1on1", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-15 11:36:29", "ddl": "", "level": 2, "marks": [{"time": "2023-03-16 00:58:09", "content": "跟时光聊了很多，解决了很多困惑。以及咨询了晋升的路径。", "gains": false}], "timeline": [["2023-03-15 10:27:00", "2023-03-15 11:55:00"], ["2023-03-15 23:52:00", "2023-03-16 00:58:24"]]}, {"tid": "4132", "parent": "41", "title": "安頔咨询置顶策略针对单模板开AB实验的规则", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-15 16:24:04", "ddl": "", "level": 2, "marks": [], "timeline": [["2023-03-15 16:10:00", "2023-03-15 16:24:35"]]}, {"tid": "4208", "parent": "42", "title": "Q1需求复盘与Q2需求对齐", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-16 11:40:59", "ddl": "", "level": 2, "marks": [{"time": "2023-03-16 14:31:43", "content": "1. 先让陈锐、万军补齐需求。\n2. 然后再了解各个需求的背景和细节。", "gains": false}, {"time": "2023-03-16 14:50:14", "content": "建了个4118任务的meego", "gains": false}], "timeline": [["2023-03-16 11:46:39", "2023-03-16 12:00:00"], ["2023-03-16 14:27:22", "2023-03-16 14:31:59"], ["2023-03-16 14:34:00", "2023-03-16 14:53:42"]]}, {"tid": "110702", "parent": "1107", "title": "输出升级接口文档&写代码", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-16 12:01:12", "ddl": "", "level": 3, "marks": [{"time": "2023-03-16 16:26:55", "content": "花了9min整理文档。突然意识到又在做一件ROI不高的事情，还不如直接找万军要之前的文档看能不能用。再快速推进。而且，之前的背景上下文肯定要了解的，目前也不是百分百清楚，容易搞叉。", "gains": false}, {"time": "2023-03-16 16:30:34", "content": "找万军没回复，刚好先顺便搭建移动端调试代理。一直来多少是个卡点。", "gains": false}, {"time": "2023-03-17 16:36:15", "content": "顺便checkv2接口的返回结构", "gains": false}, {"time": "2023-03-17 16:38:02", "content": "但其实抓紧411105，顺便挖掘增长点，是比这个历史包袱更重要的事情。", "gains": false}], "timeline": [["2023-03-16 14:31:59", "2023-03-16 14:34:00"], ["2023-03-16 14:53:42", "2023-03-16 15:00:14"], ["2023-03-16 16:15:50", "2023-03-16 16:30:58"], ["2023-03-17 15:59:16", "2023-03-17 16:02:00"], ["2023-03-17 16:28:15", "2023-03-17 16:38:05"]]}, {"tid": "110703", "parent": "1107", "title": "搭建移动端调试代理", "category": null, "status": "已完成", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-16 16:30:08", "ddl": "", "level": 3, "marks": [{"time": "2023-03-16 17:46:48", "content": "卡那么久整理文档，实际上家龙发了二维码直接下载就ok", "gains": false}, {"time": "2023-03-17 14:39:18", "content": "继续咨询家龙怎么打开debug模式。。", "gains": false}, {"time": "2023-03-17 15:44:51", "content": "被QA带偏了浪费一阵时间，直接找张宇问。beta版本跟内测包属于不同概念。", "gains": false}, {"time": "2023-03-17 15:58:38", "content": "但是代理配置了好像也不起作用。。还是应该并行以正事为主。", "gains": false}, {"time": "2023-03-17 15:59:11", "content": "毕竟不用非得代理去确认，直接找万军要了文档了已经", "gains": false}, {"time": "2023-03-17 16:03:58", "content": "看起来不是理解的http 代理。上面写着rust proxy。而且家龙用的是泳道header", "gains": false}, {"time": "2023-03-17 16:18:02", "content": "顺便问了下家龙，说要安装证书，意识到whistle需要授信CA证书，这个操作需要安卓端安装的。安装完重启就ok了。", "gains": false}, {"time": "2023-03-17 16:27:23", "content": "捣腾了一会whistle的filter。直接写/template就ok了", "gains": false}, {"time": "2023-03-17 16:28:09", "content": "所以总算通过proxy，check到了mobile的系统模板列表请求。是v2", "gains": false}], "timeline": [["2023-03-16 16:30:58", "2023-03-16 16:55:00"], ["2023-03-16 17:45:53", "2023-03-16 18:00:00"], ["2023-03-17 14:38:54", "2023-03-17 14:40:37"], ["2023-03-17 15:38:00", "2023-03-17 15:59:16"], ["2023-03-17 16:02:00", "2023-03-17 16:28:15"]]}, {"tid": "4312", "parent": "43", "title": "关注技术前沿&热点", "category": null, "status": "阻塞中", "description": "", "priority": 2, "quadrant": 0, "createTime": "2023-03-17 11:02:17", "ddl": "", "level": 2, "marks": [{"time": "2023-03-17 11:04:36", "content": "了解一下openai的sdk使用，以及开源模型等背景知识", "gains": false}, {"time": "2023-03-17 11:32:04", "content": "从调研文档转而了解二十多分钟docverse", "gains": false}, {"time": "2023-03-17 11:32:35", "content": "不好过度发散，收敛回来", "gains": false}, {"time": "2023-03-17 11:38:46", "content": "看到Meta的LLaMA模型泄露被迫开源", "gains": false}], "timeline": [["2023-03-17 11:04:05", "2023-03-17 12:00:00"], ["2023-03-17 14:07:08", "2023-03-17 14:38:54"]]}], "histories": []}