use serde::{Serialize, Deserialize};

#[derive(Debug, Serialize, Deserialize, <PERSON>lone)]
pub struct TaskMarkModel {
	pub id: Option<String>,
    pub content: String,
    pub markType: String,
    pub docLink: Option<String>,
    pub time: String,
    // pub gains: Option<bool>,
}

impl TaskMarkModel {
    pub fn new() -> Self {
        TaskMarkModel {
            time: "".to_string(),
            content: "".to_string(),
            docLink: None,
            id: None,
            markType: "".to_string(),
            // gains: None,
        }
    }
}

#[derive(Debug, Deserialize, Serialize)]
pub struct TaskItemModel {
    pub id: String,
    pub parent: Option<String>,
    pub title: String,
    pub category: Option<String>,
    pub status: String,
    pub description: String,
    pub priority: i64,
    pub level: i64,
    pub ddl: String,
    pub createTime: String,
    pub subs: Vec<TaskItemModel>,
    pub timeInvested: Option<i64>,
    pub timeSpan: Option<Vec<i64>>,
    pub label: Option<String>,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct TaskTreeModel {
    pub treeRoot: TaskItemModel,
    pub marks: Vec<TaskMarkModel>,
    pub timelines: Vec<Vec<String>>,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct PickupListModel {
    pub tasks: Vec<TaskItemModel>,
}
