use crate::error::TaskCommandError;
use crate::data_model::PickupListModel;
use crate::data_model::TaskTreeModel;
use crate::data_view::DataView;
use crate::data_view::DisplayTreeOptions;

pub trait DataSource<T: DataView> {
    // fn info(&mut self, taskLabel: Option<&String>);
    // fn add(&mut self, task_title: String, parent: String, priority: Option<&String>);
    // fn start(&mut self, taskLabel: String, start_time: Option<&String>) -> Result<(), TaskCommandError>;
    // fn finish(&mut self, task_id: String);
    // fn mv(&mut self, srcTaskID: String, dstTaskID: String);
    // fn mark(&mut self, content: String, taskID: Option<&String>, gains: bool);
    // fn reset(&mut self, taskLabel: String) -> Result<(), TaskCommandError>;
    // fn stop(&mut self, task_id: Option<&String>);
    // fn domain_list(&mut self);
    // fn domain_current(&mut self);
    // fn domain_switch(&mut self, target_domain: String);
    // fn domain_show(&mut self, target_domain: String);
    // fn domain_set_default(&mut self, target_domain: String);

    // getter
    fn getView(&mut self) -> &mut T;
    fn GetList(&mut self, root: Option<&String>, showTime: bool, filterTime: Option<&String>) -> Result<TaskTreeModel, TaskCommandError>;
    fn GetInfo(&mut self, taskLabel: Option<&String>, showTime: bool, filterTime: Option<&String>, level: Option<&String>) -> Result<TaskTreeModel, TaskCommandError>;
    fn SetStart(&mut self, taskLabel: String, startTime: Option<&String>, endTime: Option<&String>) -> Result<TaskTreeModel, TaskCommandError>;
    fn ActionLink(&mut self, taskID: String, label: String) -> Result<TaskTreeModel, TaskCommandError>;
    fn ActionMark(&mut self, taskLabel: Option<&String>, action: &str, content: String, time: Option<&String>) -> Result<TaskTreeModel, TaskCommandError>;
    fn ActionRegen(&mut self, parent: String) -> Result<TaskTreeModel, TaskCommandError>;
    fn ActionFinish(&mut self, taskID: String) -> Result<TaskTreeModel, TaskCommandError>;
    fn ActionRm(&mut self, taskID: String, force: Option<&bool>) -> Result<TaskTreeModel, TaskCommandError>;
    fn AddTask(&mut self, taskTitle: String, parent: String, priority: Option<&String>, forceID: Option<&String>, forceStatus: Option<&String>, ddl: Option<&String>, description: Option<&String>) -> Result<TaskTreeModel, TaskCommandError>;
    fn EditTask(&mut self, taskLabel: String, title: Option<&String>, ddl: Option<&String>, description: Option<&String>) -> Result<TaskTreeModel, TaskCommandError>;
    fn MvTask(&mut self, srcTaskID: String, parent: Option<&String>, dstTaskID: Option<&String>) -> Result<TaskTreeModel, TaskCommandError>;
    fn PickupTask(&mut self, task: Option<&String>, position: Option<&String>) -> Result<PickupListModel, TaskCommandError>;
    fn DoRmPickup(&mut self, position: String) -> Result<(PickupListModel), TaskCommandError>;
    fn GetLast(&mut self, number: Option<&String>) -> Result<PickupListModel, TaskCommandError>;
    fn SearchTasks(&mut self, keyword: String, inMarks: bool, markType: Option<&String>, limit: u32) -> Result<PickupListModel, TaskCommandError>;

    // common interface code
    fn List(&mut self, root: Option<&String>, showTime: bool, filterTime: Option<&String>) -> Result<(), TaskCommandError> {
        let model = self.GetList(root, showTime, filterTime)?;
        let options = DisplayTreeOptions{
            showRoot: false,
            showMarks: false,
            showTime: showTime || filterTime != None,
            verbose: false,
        };
        self.getView().DisplayTree(model, options);
        Ok(())
    }

    fn Info(&mut self, taskLabel: Option<&String>, showTime: bool, filterTime: Option<&String>, level: Option<&String>, verbose: bool) -> Result<(), TaskCommandError> {
        let model = self.GetInfo(taskLabel, showTime, filterTime, level)?;
        let options = DisplayTreeOptions{
            showRoot: true,
            showMarks: true,
            showTime: showTime || filterTime != None,
            verbose: verbose,
        };
        self.getView().DisplayTree(model, options);
        Ok(())
    }

    fn Link(&mut self, taskID: String, label: String) -> Result<(), TaskCommandError>
    {
        let model = self.ActionLink(taskID, label)?;
        let options = DisplayTreeOptions{
            showRoot: true,
            showMarks: true,
            showTime: false,
            verbose: false,
        };
        self.getView().DisplayTree(model, options);
        Ok(())
    }

    fn Start(&mut self, taskLabel: String, startTime: Option<&String>, endTime: Option<&String>) -> Result<(), TaskCommandError>
    {
        let model = self.SetStart(taskLabel, startTime, endTime)?;
        let options = DisplayTreeOptions{
            showRoot: true,
            showMarks: true,
            showTime: false,
            verbose: false,
        };
        self.getView().DisplayTree(model, options);
        Ok(())
    }

    fn Finish(&mut self, taskLabel: String) -> Result<(), TaskCommandError>
    {
        let model = self.ActionFinish(taskLabel)?;
        let options = DisplayTreeOptions{
            showRoot: true,
            showMarks: true,
            showTime: false,
            verbose: false,
        };
        self.getView().DisplayTree(model, options);
        Ok(())
    }

    fn RmTask(&mut self, taskLabel: String, force: Option<&bool>) -> Result<(), TaskCommandError>
    {
        let model = self.ActionRm(taskLabel, force)?;
        let options = DisplayTreeOptions{
            showRoot: true,
            showMarks: true,
            showTime: false,
            verbose: false,
        };
        self.getView().DisplayTree(model, options);
        Ok(())
    }

    fn Mv(&mut self, srcTaskLabel: String, parent: Option<&String>, dstTaskID: Option<&String>) -> Result<(), TaskCommandError>
    {
        let model = self.MvTask(srcTaskLabel, parent, dstTaskID)?;
        let options = DisplayTreeOptions{
            showRoot: true,
            showMarks: true,
            showTime: false,
            verbose: false,
        };
        self.getView().DisplayTree(model, options);
        Ok(())
    }

    fn Regen(&mut self, parent: String) -> Result<(), TaskCommandError>
    {
        let model = self.ActionRegen(parent)?;
        let options = DisplayTreeOptions{
            showRoot: true,
            showMarks: true,
            showTime: false,
            verbose: false,
        };
        self.getView().DisplayTree(model, options);
        Ok(())
    }

    fn Mark(&mut self, taskLabel: Option<&String>, action: &str, content: String, markTime: Option<&String>) -> Result<(), TaskCommandError>
    {
        let model = self.ActionMark(taskLabel, action, content, markTime)?;
        let options = DisplayTreeOptions{
            showRoot: true,
            showMarks: true,
            showTime: false,
            verbose: false,
        };
        self.getView().DisplayTree(model, options);
        Ok(())
    }

    fn Edit(&mut self, taskLabel: String, title: Option<&String>, ddl: Option<&String>, description: Option<&String>) -> Result<(), TaskCommandError>
    {
        let model = self.EditTask(taskLabel, title, ddl, description)?;
        let options = DisplayTreeOptions{
            showRoot: true,
            showMarks: false,
            showTime: false,
            verbose: false,
        };
        self.getView().DisplayTree(model, options);
        Ok(())
    }

    fn Add(&mut self, taskTitle: String, parent: String, priority: Option<&String>, ddl: Option<&String>, description: Option<&String>) -> Result<(), TaskCommandError>
    {
        let model = self.AddTask(taskTitle, parent, priority, None, None, ddl, description)?;
        let options = DisplayTreeOptions{
            showRoot: true,
            showMarks: false,
            showTime: false,
            verbose: false,
        };
        self.getView().DisplayTree(model, options);
        Ok(())
    }

    fn RmPickup(&mut self, position: String) -> Result<(), TaskCommandError>
    {
        let model = self.DoRmPickup(position)?;
        self.getView().DisplayPickupList(model);
        Ok(())
    }

    fn Pickup(&mut self, task: Option<&String>, position: Option<&String>) -> Result<(), TaskCommandError>
    {
        let model = self.PickupTask(task, position)?;
        self.getView().DisplayPickupList(model);
        Ok(())
    }

    fn Last(&mut self, number: Option<&String>) -> Result<(), TaskCommandError> {
        let model = self.GetLast(number)?;
        self.getView().DisplayPickupList(model);
        Ok(())
    }

    fn Search(&mut self, keyword: String, inMarks: bool, markType: Option<&String>, limit: u32) -> Result<(), TaskCommandError> {
        let model = self.SearchTasks(keyword, inMarks, markType, limit)?;
        self.getView().DisplayPickupList(model);
        Ok(())
    }
}
