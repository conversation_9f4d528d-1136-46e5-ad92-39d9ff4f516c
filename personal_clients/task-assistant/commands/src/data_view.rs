
use crate::error::TaskCommandError;
use crate::data_model::PickupListModel;
use crate::data_model::TaskTreeModel;

// use derivative::Derivative;

// #[derive(Derivative)]
pub struct DisplayTreeOptions {
    pub showRoot: bool,
    pub showMarks: bool,
    pub showTime: bool,
    // #[derivative(Default(value = false))]
    pub verbose: bool,
}

pub trait DataView {
    fn DisplayTree(&mut self, model: TaskTreeModel, options: DisplayTreeOptions) -> Result<(), TaskCommandError>;
    fn DisplayPickupList(&mut self, model: PickupListModel) -> Result<(), TaskCommandError>;
}
