use std::fmt;
use colored::Colorize;
use crate::http_model::TaskHttpResponse;
use serde::{Serialize};

#[derive(Debug)]
pub struct TaskCommandError {
    msg: String,
    pub code: i64,
}

// 参考: https://github.com/hyperium/http/issues/374
impl From<chrono::format::ParseError> for TaskCommandError {
    fn from(e: chrono::format::ParseError) -> Self {
        TaskCommandError {
            msg: "66error".to_string(),
            code: 0,
        }
    }
}

impl From<url::ParseError> for TaskCommandError {
    fn from(e: url::ParseError) -> Self {
        TaskCommandError {
            msg: e.to_string(),
            code: 0,
        }
    }
}

impl From<reqwest::Error> for TaskCommandError {
    fn from(e: reqwest::Error) -> Self {
        TaskCommandError {
            msg: e.to_string(),
            code: 0,
        }
    }
}

impl fmt::Display for TaskCommandError {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        let tag = "[ERR]";
        write!(f, "{} {}", tag.bold().red(), self.msg);
        Ok(())
    }
}

impl<T: Serialize> From<&TaskHttpResponse<T>> for TaskCommandError {
    fn from(e: &TaskHttpResponse<T>) -> Self {
        TaskCommandError {
            // msg: serde_json::to_string(e).unwrap(),
            msg: e.message.to_string(),
            code: e.code,
        }
    }
}

impl From<String> for TaskCommandError {
    fn from(e: String) -> Self {
        TaskCommandError {
            msg: e,
            code: 0,
        }
    }
}

impl TaskCommandError {
    pub fn new(errmsg: String) -> TaskCommandError {
        TaskCommandError {
            msg: errmsg,
            code: 0,
        }
    }

    pub fn new1(errmsg: &str) -> TaskCommandError {
        TaskCommandError {
            msg: errmsg.to_string(),
            code: 0,
        }
    }
}
