use std::env;
use std::collections::HashMap;
use crate::data_model::PickupListModel;
use crate::data_model::TaskTreeModel;
use crate::data_source::DataSource;
use crate::data_view::DataView;
use crate::http_model::TaskHttpResponse;
use crate::error::TaskCommandError;

pub struct HttpDB<T: DataView> {
    url_prefix_: String,
    client: reqwest::blocking::Client,
    bullet: String,
    view: T,
}

impl<T: DataView> HttpDB<T> {
    pub fn new(view: T) -> Self {
        let defaultHost = "http://127.0.0.1".to_string(); 
        let host = env::var("KTASK_HOST").unwrap_or(defaultHost.to_string());
        HttpDB {
            // url_prefix_: "https://local.luzeshu.site/api/task".to_string(),
            // url_prefix_: "http://127.0.0.1:16135/api/task".to_string(),
            url_prefix_: format!("{}/api/task", host),
            client: reqwest::blocking::Client::new(),
            bullet: "36fdf066-9e42-11ec-b41e-525400043ced".to_string(),
            view: view,
        }
    }

    fn GetTaskTree(&self, taskLabel: Option<&String>, defaultTaskType: &str, showTime: bool, filterTime: Option<&String>, level: Option<&String>) -> Result<TaskTreeModel, TaskCommandError> {
        let empty = "".to_string(); 
        let times = env::var("KTASK_TIMES").unwrap_or(empty.to_string());
        let params = [
            ("root", &taskLabel.unwrap_or(&empty)),
            ("defaultType", &&defaultTaskType.to_string()),
            ("showTime", &&showTime.to_string()),
            ("filterTime", &filterTime.unwrap_or(&empty)),
            ("level", &level.unwrap_or(&empty)),
            ("investedTimeTimes", &&times),
        ];

        // for (key, value) in params.into_iter() {
        //     println!("{}={}", key, value);
        // }
        let url = format!("{}/list", self.url_prefix_);
        let url = reqwest::Url::parse_with_params(&url, &params)?;
		let resp = self.client.get(url)
            .header("Bullet", self.bullet.as_str())
            .form(&params)
            .send()?;
        if resp.status().is_success() {
            println!("GET /list status=200");
            let r: TaskHttpResponse<TaskTreeModel> = resp.json().unwrap();
            let e = TaskCommandError::from(&r);
            Ok::<TaskTreeModel, TaskCommandError>(r.data.ok_or(e)?)
        } else {
            Err(TaskCommandError::new(resp.status().to_string()))
        }
    }

    fn GetLastTask(&self, number: Option<&String>) -> Result<PickupListModel, TaskCommandError> {
        let empty = &"".to_string();
        let params = HashMap::from([
            ("number", number.unwrap_or(empty).to_string()),
        ]);

        // for (key, value) in params.into_iter() {
        //     println!("{}={}", key, value);
        // }
        let url = format!("{}/last", self.url_prefix_);
        let url = reqwest::Url::parse_with_params(&url, &params)?;
		let resp = self.client.get(url)
            .header("Bullet", self.bullet.as_str())
            .form(&params)
            .send()?;
        if resp.status().is_success() {
            println!("GET /last status=200");
            let r: TaskHttpResponse<PickupListModel> = resp.json().unwrap();
            let e = TaskCommandError::from(&r);
            Ok::<PickupListModel, TaskCommandError>(r.data.ok_or(e)?)
        } else {
            Err(TaskCommandError::new(resp.status().to_string()))
        }
    }

    fn GetSearchResults(&self, keyword: String, inMarks: bool, markType: Option<&String>, limit: u32) -> Result<PickupListModel, TaskCommandError> {
        let empty = &"".to_string();
        let params = HashMap::from([
            ("keyword", keyword),
            ("inMarks", inMarks.to_string()),
            ("markType", markType.unwrap_or(empty).to_string()),
            ("limit", limit.to_string()),
        ]);

        let url = format!("{}/search", self.url_prefix_);
        let url = reqwest::Url::parse_with_params(&url, &params)?;
		let resp = self.client.get(url)
            .header("Bullet", self.bullet.as_str())
            .form(&params)
            .send()?;
        if resp.status().is_success() {
            println!("GET /search status=200");
            let r: TaskHttpResponse<PickupListModel> = resp.json().unwrap();
            let e = TaskCommandError::from(&r);
            Ok::<PickupListModel, TaskCommandError>(r.data.ok_or(e)?)
        } else {
            Err(TaskCommandError::new(resp.status().to_string()))
        }
    }

    fn PostRmPickup(&self, data: HashMap<&str, String>) -> Result<PickupListModel, TaskCommandError> {
        let url = format!("{}/pickdel", self.url_prefix_);
        let url = reqwest::Url::parse_with_params(&url, &data)?;
		let resp = self.client.post(url)
            .header("Bullet", self.bullet.as_str())
            .json(&data)
            .send()?;
        // for (key, value) in data.into_iter() {
        //     println!("{}={}", key, value);
        // }
        if resp.status().is_success() {
            println!("POST /pickdel status=200");
            let r: TaskHttpResponse<PickupListModel> = resp.json().unwrap();
            let e = TaskCommandError::from(&r);
            Ok::<PickupListModel, TaskCommandError>(r.data.ok_or(e)?)
        } else {
            Err(TaskCommandError::new(resp.status().to_string()))
        }
    }

    fn PostPickup(&self, data: HashMap<&str, String>) -> Result<PickupListModel, TaskCommandError> {
        let url = format!("{}/pick", self.url_prefix_);
        let url = reqwest::Url::parse_with_params(&url, &data)?;
		let resp = self.client.post(url)
            .header("Bullet", self.bullet.as_str())
            .json(&data)
            .send()?;
        // for (key, value) in data.into_iter() {
        //     println!("{}={}", key, value);
        // }
        if resp.status().is_success() {
            println!("POST /pick status=200");
            let r: TaskHttpResponse<PickupListModel> = resp.json().unwrap();
            let e = TaskCommandError::from(&r);
            Ok::<PickupListModel, TaskCommandError>(r.data.ok_or(e)?)
        } else {
            Err(TaskCommandError::new(resp.status().to_string()))
        }
    }

    fn PostTaskAction(&self, data: HashMap<&str, String>) -> Result<TaskTreeModel, TaskCommandError> {
        let url = format!("{}/action", self.url_prefix_);
		let resp = self.client.post(url)
            .header("Bullet", self.bullet.as_str())
            .json(&data)
            .send()?;
        // for (key, value) in data.into_iter() {
        //     println!("{}={}", key, value);
        // }
        if resp.status().is_success() {
            println!("POST /action status=200");
            let r: TaskHttpResponse<TaskTreeModel> = resp.json().unwrap();
            let e = TaskCommandError::from(&r);
            Ok::<TaskTreeModel, TaskCommandError>(r.data.ok_or(e)?)
        } else {
            Err(TaskCommandError::new(resp.status().to_string()))
        }
    }

    fn PostTaskMv(&self, data: HashMap<&str, String>) -> Result<TaskTreeModel, TaskCommandError> {
        let url = format!("{}/move", self.url_prefix_);
        let url = reqwest::Url::parse_with_params(&url, &data)?;
		let resp = self.client.post(url)
            .header("Bullet", self.bullet.as_str())
            .json(&data)
            .send()?;
        // for (key, value) in data.into_iter() {
        //     println!("{}={}", key, value);
        // }
        if resp.status().is_success() {
            println!("POST /move status=200");
            let r: TaskHttpResponse<TaskTreeModel> = resp.json().unwrap();
            let e = TaskCommandError::from(&r);
            Ok::<TaskTreeModel, TaskCommandError>(r.data.ok_or(e)?)
        } else {
            Err(TaskCommandError::new(resp.status().to_string()))
        }
    }

    fn PostTaskAdd(&self, data: HashMap<&str, String>) -> Result<TaskTreeModel, TaskCommandError> {
        let url = format!("{}/add", self.url_prefix_);
        let url = reqwest::Url::parse_with_params(&url, &data)?;
		let resp = self.client.post(url)
            .header("Bullet", self.bullet.as_str())
            .json(&data)
            .send()?;
        if resp.status().is_success() {
            println!("POST /add status=200");
            let r: TaskHttpResponse<TaskTreeModel> = resp.json().unwrap();
            let e = TaskCommandError::from(&r);
            Ok::<TaskTreeModel, TaskCommandError>(r.data.ok_or(e)?)
        } else {
            Err(TaskCommandError::new(resp.status().to_string()))
        }
    }

    fn PostTaskEdit(&self, data: HashMap<&str, String>) -> Result<TaskTreeModel, TaskCommandError> {
        let url = format!("{}/edit", self.url_prefix_);
        let url = reqwest::Url::parse_with_params(&url, &data)?;
		let resp = self.client.post(url)
            .header("Bullet", self.bullet.as_str())
            .json(&data)
            .send()?;
        if resp.status().is_success() {
            println!("POST /edit status=200");
            let r: TaskHttpResponse<TaskTreeModel> = resp.json().unwrap();
            let e = TaskCommandError::from(&r);
            Ok::<TaskTreeModel, TaskCommandError>(r.data.ok_or(e)?)
        } else {
            Err(TaskCommandError::new(resp.status().to_string()))
        }
    }
}

impl<T: DataView> DataSource<T> for HttpDB<T> {
    fn ActionMark(&mut self, taskLabel: Option<&String>, action: &str, content: String, time: Option<&String>) -> Result<TaskTreeModel, TaskCommandError> 
    {
        let empty = &"".to_string();
        let data = HashMap::from([
            ("mustLeaf", "false".to_string()),
            ("action", action.to_string()),
            ("content", content.to_string()),
            ("taskIDLabel", taskLabel.unwrap_or(empty).to_string()),
            ("markTime", time.unwrap_or(empty).to_string()),
        ]);
        self.PostTaskAction(data)
    }

    fn ActionRegen(&mut self, parent: String) -> Result<TaskTreeModel, TaskCommandError> 
    {
        let data = HashMap::from([
            ("mustLeaf", "false".to_string()),
            ("action", "regen".to_string()),
            ("taskIDLabel", parent),
        ]);
        self.PostTaskAction(data)
    }

    fn ActionLink(&mut self, taskID: String, label: String) -> Result<TaskTreeModel, TaskCommandError>
    {
        let empty = &"".to_string();
        let data = HashMap::from([
            ("mustLeaf", "false".to_string()),
            ("action", "link".to_string()),
            ("taskIDLabel", taskID),
            ("labelName", label),
        ]);
        self.PostTaskAction(data)
    }

    fn SetStart(&mut self, taskLabel: String, startTime: Option<&String>, endTime: Option<&String>) -> Result<TaskTreeModel, TaskCommandError>
    {
        let empty = &"".to_string();
        let data = HashMap::from([
            ("taskIDLabel", taskLabel),
            ("mustLeaf", "true".to_string()),
            ("action", "start".to_string()),
            ("startTime", startTime.unwrap_or(empty).to_string()),
            ("endTime", endTime.unwrap_or(empty).to_string()),
        ]);
        self.PostTaskAction(data)
    }

    fn getView(&mut self) -> &mut T {
        &mut self.view
    }

    fn GetInfo(&mut self, taskLabel: Option<&String>, showTime: bool, filterTime: Option<&String>, level: Option<&String>) -> Result<TaskTreeModel, TaskCommandError>
    {
        self.GetTaskTree(taskLabel, "inprogress", showTime, filterTime, level)
    }

    fn GetList(&mut self, root: Option<&String>, showTime: bool, filterTime: Option<&String>) -> Result<TaskTreeModel, TaskCommandError>
    {
        self.GetTaskTree(root, "root", showTime, filterTime, Some(&"99".to_string()))
    }

    fn PickupTask(&mut self, task: Option<&String>, position: Option<&String>) -> Result<PickupListModel, TaskCommandError>
    {
        let empty = &"".to_string();
        let data = HashMap::from([
            ("taskIDLabel", task.unwrap_or(empty).to_string()),
            ("position", position.unwrap_or(empty).to_string()),
        ]);
        self.PostPickup(data)
    }

    fn DoRmPickup(&mut self, position: String) -> Result<PickupListModel, TaskCommandError>
    {
        let data = HashMap::from([
            ("position", position),
        ]);
        self.PostRmPickup(data)
    }

    fn GetLast(&mut self, number: Option<&String>) -> Result<PickupListModel, TaskCommandError>
    {
        self.GetLastTask(number)
    }

    fn SearchTasks(&mut self, keyword: String, inMarks: bool, markType: Option<&String>, limit: u32) -> Result<PickupListModel, TaskCommandError>
    {
        self.GetSearchResults(keyword, inMarks, markType, limit)
    }

    fn AddTask(&mut self, taskTitle: String, parent: String, priority: Option<&String>, forceID: Option<&String>, forceStatus: Option<&String>, ddl: Option<&String>, description: Option<&String>) -> Result<TaskTreeModel, TaskCommandError>
    {
        let empty = &"".to_string();
        let data = HashMap::from([
            ("title", taskTitle),
            ("parent", parent),
            ("priority", priority.unwrap_or(empty).to_string()),
            ("forceID", forceID.unwrap_or(empty).to_string()),
            ("forceStatus", forceStatus.unwrap_or(empty).to_string()),
            ("ddl", ddl.unwrap_or(empty).to_string()),
            ("description", description.unwrap_or(empty).to_string()),
        ]);
        self.PostTaskAdd(data)
    }

    fn EditTask(&mut self, taskLabel: String, title: Option<&String>, ddl: Option<&String>, description: Option<&String>) -> Result<TaskTreeModel, TaskCommandError>
    {
        let empty = &"".to_string();
        let data = HashMap::from([
            ("taskIDLabel", taskLabel.to_string()),
            ("title", title.unwrap_or(empty).to_string()),
            ("ddl", ddl.unwrap_or(empty).to_string()),
            ("description", description.unwrap_or(empty).to_string()),
        ]);
        self.PostTaskEdit(data)
    }

    fn ActionFinish(&mut self, taskLabel: String) -> Result<TaskTreeModel, TaskCommandError>
    {
        let data = HashMap::from([
            ("taskIDLabel", taskLabel.to_string()),
            ("mustLeaf", "true".to_string()),
            ("action", "finish".to_string()),
        ]);
        self.PostTaskAction(data)
    }

    fn ActionRm(&mut self, taskLabel: String, force: Option<&bool>) -> Result<TaskTreeModel, TaskCommandError>
    {
        let forceArg = force.unwrap_or(&false);
        let data = HashMap::from([
            ("taskIDLabel", taskLabel.to_string()),
            ("mustLeaf", "true".to_string()),
            ("action", "remove".to_string()),
            ("force", forceArg.to_string()),
        ]);
        self.PostTaskAction(data)
    }

    fn MvTask(&mut self, srcTaskLabel: String, parent: Option<&String>, dstTaskID: Option<&String>) -> Result<TaskTreeModel, TaskCommandError>
    {
        let empty = &"".to_string();
        let data = HashMap::from([
            ("srctask", srcTaskLabel.to_string()),
            // ("parent", parent.to_string()),
            ("parent", parent.unwrap_or(&"".to_string()).to_string()),
            ("dstTaskID", dstTaskID.unwrap_or(empty).to_string()),
        ]);
        self.PostTaskMv(data)
    }
}
