use serde::{Deserialize, Serialize};

#[derive(Debug, Deserialize, Serialize)]
pub struct TaskHttpResponse<T> where T: Serialize, {
    pub data: Option<T>,
    pub code: i64,
    pub message: String,
}

// impl fmt::Display for TaskHttpResponse {
//      fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
//          write!(f, "[{}] {}", self.time.bold(), self.content);
//          Ok(())
//      }
// }
