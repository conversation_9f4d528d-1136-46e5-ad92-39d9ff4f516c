use std::fs;
use std::{thread, time};
use chrono;
use chrono::{NaiveDateTime, DateTime, Local};
use std::collections::{HashMap};
use std::rc::Rc;
use std::cell::RefCell;
use serde_json;

use crate::model::TaskStatus;
use crate::model::ModelTaskItem;
use crate::model::ModelTaskMark;
use crate::model::ModelData;
use crate::data_view::DataView;
use crate::model::ViewTaskTree;
// use crate::model::ViewTaskItem;
use crate::error::TaskCommandError;
use crate::data_source::DataSource;
use crate::data_model::TaskTreeModel;


// #[derive(Default)]
pub struct JsonFileDB<T: DataView> {
    task_len_: usize,
    config_file_: String,
    mdata_: Option<ModelData>,
    refTaskMap: Option<HashMap<String, Rc<RefCell<ViewTaskTree>>>>,
    viewTaskTree: Option<Rc<RefCell<ViewTaskTree>>>,
    view: T,
}

pub fn travel(root: &Rc<RefCell<ViewTaskTree>>, level: i64) {
    let v: &mut ViewTaskTree = &mut *root.borrow_mut();
    let mut item = v.root.borrow_mut();
    item.level = Some(level);

    for k in &v.children {
        travel(k, level + 1);
    }
}

fn convertTaskLabel(taskLabel: String) -> String {
    let mut taskID = taskLabel.to_string();
    // 做一些便捷task标记的映射
    if taskLabel.eq("rest") {
        taskID = "9101".to_string();
    } else if taskLabel.eq("mess") {
        taskID = "9102".to_string();
    } else if taskLabel.eq("wc") {
        taskID = "9103".to_string();
    } else if taskLabel.eq("gohome") {
        taskID = "9104".to_string();
    } else if taskLabel.eq("sleep") {
        taskID = "9105".to_string();
    } else if taskLabel.eq("bath") {
        taskID = "9106".to_string();
    }
    return taskID;
}

fn getStartTime(startTime: Option<&String>) -> Result<String, TaskCommandError> {
    let nowTime: DateTime<Local> = chrono::offset::Local::now();
    let mut start = nowTime.to_string().get(..19).unwrap().to_string();
    if !startTime.is_none() {
        match NaiveDateTime::parse_from_str(startTime.unwrap(), "%Y-%m-%d %H:%M:%S") {
            Ok(v) => return Ok(v.to_string()),
            _ => (),
        }

        // 当成%H:%M的格式来拼接
        let formatStart = format!("{} {}:00", start.get(..10).unwrap(), startTime.unwrap());
        match NaiveDateTime::parse_from_str(&formatStart, "%Y-%m-%d %H:%M:%S") {
            Ok(v) => return Ok(v.to_string()),
            _ => (),
        }

        // 当成%H:%M:%S的格式来拼接
        let formatStart = format!("{} {}", start.get(..10).unwrap(), startTime.unwrap());
        match NaiveDateTime::parse_from_str(&formatStart, "%Y-%m-%d %H:%M:%S") {
            Ok(v) => return Ok(v.to_string()),
            _ => (),
        }

        // 当成%Y-%m-%d %H:%M的格式来拼接
        let formatStart = format!("{}:00", startTime.unwrap());
        match NaiveDateTime::parse_from_str(&formatStart, "%Y-%m-%d %H:%M:%S") {
            Ok(v) => return Ok(v.to_string()),
            _ => (),
        }
    } else {
        return Ok(start);
    }

    return Err(TaskCommandError::new(format!("Wrong Format StartTime {:?}", startTime)));
}

impl<T: DataView> JsonFileDB<T> {
    pub fn new(view: T) -> Self {
        let config_file = "/data/projects/personal_tool/task-assistant/commands/db.json";
        let mut db = JsonFileDB {
            task_len_: 0,
            config_file_: config_file.to_string(),
            mdata_: None,
            refTaskMap: None,
            viewTaskTree: None,
            view: view,
        };

        db.load();
        db.build_task_hashmap();
        db.build_task_tree();
        db.set_level();

        db
    }

    pub fn load(&mut self) {
        let content = fs::read_to_string(&self.config_file_)
            .expect("Something went wrong reading the file");
        self.mdata_ = serde_json::from_str(&content).expect("JSON was not well-formatted");
    }

    pub fn build_task_hashmap(&mut self) {
        let tasks = &self.mdata_.as_ref().unwrap().tasks;
        let mut refTaskMap = HashMap::new();
        self.task_len_ = tasks.len();
        for rc in tasks {
            let task: &ModelTaskItem = &*rc.borrow_mut();
            let tid = task.tid.clone();
            let mut timeline = Vec::<Vec<String>>::new();

            let viewTask = Rc::new(RefCell::new(ViewTaskTree{
                root: Rc::clone(rc),
                children: Vec::new(),
                timeline: task.timeline.to_vec(),
            }));
            refTaskMap.insert(tid, viewTask);
        }
        self.refTaskMap = Some(refTaskMap);
    }

    pub fn build_task_tree(&mut self) {
        let mut topChildren = Vec::new();

        let refTaskMap = self.refTaskMap.as_ref().unwrap();

        let mut keys = refTaskMap.keys().cloned().collect::<Vec<_>>();
        keys.sort();
        for k in &keys {
            let rc: &Rc<RefCell<ViewTaskTree>> = refTaskMap.get(k).unwrap();
            let v: &ViewTaskTree = &*rc.borrow_mut();
            // let root: &ViewTaskItem = v.root.as_ref().unwrap();
            // let task = root.data.borrow_mut();
            let task = v.root.borrow_mut();

            if task.parent != None {
                let parent = task.parent.as_ref().unwrap();
                let mut parentTree = refTaskMap.get(parent).unwrap().borrow_mut();
                parentTree.children.push(Rc::clone(rc));
            } else {
                // 根节点
                topChildren.push(Rc::clone(rc));
            }
        }
        // self.viewTaskTree = Some(taskTreeRoot);
        // 构造虚拟根节点
        let fakeRoot = Rc::new(RefCell::new(ModelTaskItem::new()));
        let taskTreeRoot = ViewTaskTree{
            root: fakeRoot,
            children: topChildren,
            timeline: Vec::new(),
        };
        self.viewTaskTree = Some(Rc::new(RefCell::new(taskTreeRoot)));
    }

    pub fn set_level(&mut self) {
        // 遍历任务树结构，设置level字段
        travel(self.viewTaskTree.as_ref().unwrap(), -1)
    }

    pub fn write(&mut self) {
        let writeBackContent = serde_json::to_string_pretty(&self.mdata_).unwrap();
        fs::write(&self.config_file_, &writeBackContent);
    }

    pub fn add_task(&mut self, title: String, parentID: String, priority: i64) -> Rc<RefCell<ModelTaskItem>>
    {
        let parentOpt = self.find_task(&parentID);
        let parentRc = parentOpt.unwrap();
        let parent: &ViewTaskTree = &*parentRc.borrow_mut();
        let cLen = parent.children.len();
        let mut tid = format!("{}01", parentID);
        if cLen != 0 {
            let lastC = &parent.children[cLen-1];
            let lastCVT: &ViewTaskTree = &*lastC.borrow_mut();
            let lastCID = &*lastCVT.root.borrow_mut().tid;
            tid = format!("{}", lastCID.parse::<i64>().unwrap() + 1);
        }

        let nowTime: DateTime<Local> = chrono::offset::Local::now();
        let createTime = nowTime.to_string().get(..19).unwrap().to_string();
        let task = ModelTaskItem{
            tid: tid.to_string(),
            parent: Some(parentID),
            title: title,
            category: None,
            status: TaskStatus::UNSTARTED,
            description: "".to_string(),
            priority: priority,
            quadrant: 0,
            createTime: createTime,
            ddl: "".to_string(),
            level: Some(0),
            marks: Vec::new(),
            timeline: Vec::new(),
        };
        let data = Rc::new(RefCell::new(task));
        let tasks = &mut self.mdata_.as_mut().unwrap().tasks;
        tasks.push(Rc::clone(&data));
        data
    }

    pub fn find_task(&self, taskID: &String) -> Option<Rc<RefCell<ViewTaskTree>>>
    {
        let refTaskMap: &HashMap<String,Rc<RefCell<ViewTaskTree>>> = self.refTaskMap.as_ref().unwrap();
        let keys: Vec<String> = refTaskMap.keys().cloned().collect::<Vec<_>>();
        for k in &keys {
            if k == taskID {
                // NOTE
                // 不应该返回self.refTaskMap自身的Rc值的引用。而是克隆一个Rc，指向同一个目标数据对象。否则：
                //   问题1: 可能造成self.refTaskMap自身的Rc值被修改，这是不合理的。
                //   问题2: 返回后，相当于self的被借用lifetime被一直延续到上层方法的lifetime。导致self.的其他方法调用无法借用self报错。
                // return refTaskMap.get(k);
                let rc: Rc<RefCell<ViewTaskTree>> = Rc::clone(refTaskMap.get(k).unwrap());
                return Some(rc);
            }
        }
        None
    }

    // 找出所有处于某个status的任务列表（只找基元任务）
    pub fn find_tasks_by_status(&self, status: TaskStatus) -> Vec<Rc<RefCell<ViewTaskTree>>> {
        let mut tasks: Vec<Rc<RefCell<ViewTaskTree>>> = Vec::new();
        let refTaskMap: &HashMap<String,Rc<RefCell<ViewTaskTree>>> = self.refTaskMap.as_ref().unwrap();
        let keys: Vec<String> = refTaskMap.keys().cloned().collect::<Vec<_>>();
        for k in &keys {
            let rc = refTaskMap.get(k).unwrap();
            let v: &mut ViewTaskTree = &mut *rc.borrow_mut();
            let item = v.root.borrow_mut();
            if item.status != status {
                continue;
            }

            // 过滤有子任务的非基元任务
            if v.children.len() > 0 {
                continue;
            }
            tasks.push(Rc::clone(rc));
        }
        tasks
    }

    pub fn add_mark(&mut self, rc: &Rc<RefCell<ViewTaskTree>>, content: String, gains: bool) {
        let v: &mut ViewTaskTree = &mut *rc.borrow_mut();
        let mut item = v.root.borrow_mut();

        let nowTime: DateTime<Local> = chrono::offset::Local::now();
        let now = nowTime.to_string().get(..19).unwrap().to_string();
        let mark = ModelTaskMark{
            time: now,
            gains: Some(gains),
            content: content
        };
        item.marks.push(mark);
    }

    // start/mv: 设置task的新task id
    pub fn set_task_tid(&mut self, rc: &Rc<RefCell<ViewTaskTree>>, tid: String) {
        let v: &mut ViewTaskTree = &mut *rc.borrow_mut();
        let mut item = v.root.borrow_mut();
        item.tid = tid;
    }

    // mv: 设置task的新parent task id
    pub fn set_task_parent(&mut self, rc: &Rc<RefCell<ViewTaskTree>>, parent: Option<String>) {
        let v: &mut ViewTaskTree = &mut *rc.borrow_mut();
        let mut item = v.root.borrow_mut();
        item.parent = parent;
    }

    pub fn decide_status(&mut self, rc: &Rc<RefCell<ViewTaskTree>>) -> TaskStatus
    {
        let v: &mut ViewTaskTree = &mut *rc.borrow_mut();
        let mut item = v.root.borrow_mut();

        let children = &v.children;
        if children.len() == 0 {
            // 没有子任务，属于基元任务
            return item.status;
        }

        // 根据子任务的status，以及父子status关系规则，判断当前节点的status
        let mut isUnstarted = true;
        let mut isFinished = true;
        let mut isRemoved = true;
        let mut isAllUnstartdOrFinished = true;
        let mut hasInprogress = false;
        let mut hasBlocking = false;
        let mut hasHoldon = false;
        for cRc in children {
            let cV: &mut ViewTaskTree = &mut *cRc.borrow_mut();
            let mut cItem = cV.root.borrow_mut();

            if cItem.status != TaskStatus::REMOVED {
                // 记录是否全部为已删除
                isRemoved = false;
            } else {
                // 如果是已删除的任务，不影响后续其他规则逻辑，
                // 比如除了已删除的，其他的都是待启动或者已完成，那也要设置待启动或者已完成
                // 所以除非针对isRemoved=true的情况，否则continue
                continue;
            }

            if cItem.status != TaskStatus::UNSTARTED {
                // 记录是否全部为待启动
                isUnstarted = false;
            }
            if cItem.status != TaskStatus::FINISH {
                // 记录是否全部为已完成
                isFinished = false;
            }
            if cItem.status != TaskStatus::UNSTARTED
                && cItem.status != TaskStatus::FINISH {
                isAllUnstartdOrFinished = false;
            }

            if cItem.status == TaskStatus::IN_PROGRESS {
                hasInprogress = true;
            }
            if cItem.status == TaskStatus::BLOCKING {
                hasBlocking = true;
            }
            if cItem.status == TaskStatus::HOLD_ON {
                hasHoldon = true;
            }
        }
        // 由于初始值3个都是true，所以isRemoved的判断要放在最优先
        if isRemoved {
            // 这个条件满足，说明所有任务都是已删除。没有其他status的task
            return TaskStatus::REMOVED;
        }
        if isUnstarted {
            // 表示除了已删除的task，全部都是待启动
            return TaskStatus::UNSTARTED;
        }
        if isFinished {
            // 表示除了已删除的task，全部都是已完成
            return TaskStatus::FINISH;
        }

        // 按照优先级规则: 进行中>阻塞中>挂起中
        if hasInprogress {
            // 存在进行中的子任务，父任务也为进行中
            return TaskStatus::IN_PROGRESS;
        }
        if hasBlocking {
            // 不存在进行中子任务时，存在阻塞中的子任务，父任务也为阻塞中
            return TaskStatus::BLOCKING;
        }
        if hasHoldon {
            // 不存在进行中或者阻塞中子任务时，存在挂起中的子任务，父任务也为挂起中
            return TaskStatus::HOLD_ON;
        }
        // 最后判断，除了已删除的task，全部是待启动或者已完成两类
        if isAllUnstartdOrFinished {
            return TaskStatus::UNSTARTED;
        }
        return TaskStatus::UNKNOWN;
    }

    // 往父任务进行递归设置status
    pub fn set_status_recusively(&mut self, rc: &Rc<RefCell<ViewTaskTree>>, status: TaskStatus)
    {
        let mut parentStr: String;
        {
            let v: &mut ViewTaskTree = &mut *rc.borrow_mut();
            let mut item = v.root.borrow_mut();
            item.status = status;

            // 往上设置父任务的状态
            let mut parentID = item.parent.as_ref();
            if parentID.is_none() {
                return;
            }
            parentStr = parentID.unwrap().to_string();
        }

        let parentTaskOpt = self.find_task(&parentStr);
        if parentTaskOpt.is_none() {
            // TODO 某种bug，错误的父id指向
        }
        let parentTask = parentTaskOpt.unwrap();
        let parentStatus = self.decide_status(&parentTask);
        self.set_status_recusively(&parentTask, parentStatus);
    }

    pub fn set_status_blocking(&mut self, rc: &Rc<RefCell<ViewTaskTree>>)
    {
        self.set_status_recusively(rc, TaskStatus::BLOCKING);
    }

    // 把当前任务设置为进行中
    pub fn set_status_holdon(&mut self, rc: &Rc<RefCell<ViewTaskTree>>)
    {
        self.set_status_recusively(rc, TaskStatus::HOLD_ON);
    }

    // 把当前任务设置为进行中
    pub fn set_status_inprogress(&mut self, rc: &Rc<RefCell<ViewTaskTree>>)
    {
        self.set_status_recusively(rc, TaskStatus::IN_PROGRESS);
    }

    // 把当前任务设置为已完成
    pub fn set_status_finished(&mut self, rc: &Rc<RefCell<ViewTaskTree>>)
    {
        self.set_status_recusively(rc, TaskStatus::FINISH);
    }

    pub fn add_timeline_start(&mut self, rc: &Rc<RefCell<ViewTaskTree>>, now: String)
    {
        let v: &mut ViewTaskTree = &mut *rc.borrow_mut();
        let mut item = v.root.borrow_mut();
        item.timeline.push(vec![now]);
    }

    pub fn add_timeline_stop(&mut self, rc: &Rc<RefCell<ViewTaskTree>>, now: String)
    {
        let v: &mut ViewTaskTree = &mut *rc.borrow_mut();
        let mut item = v.root.borrow_mut();
        if item.timeline.len() == 0 {
            // TODO 有某种bug，导致数据不一致
            return
        }
        let last = item.timeline.len() - 1;
        item.timeline[last].push(now);
    }

    // start: 把当前进行中的任务设置为阻塞中
    pub fn start_set_blocking(&mut self, rcs: &Vec<Rc<RefCell<ViewTaskTree>>>, now: String)
    {
        for rc in rcs {
            self.set_status_blocking(rc);
            self.add_timeline_stop(rc, now.clone());
        }
    }

    // start: 设置任务的进行状态
    pub fn start_set_inprogress(&mut self, rc: &Rc<RefCell<ViewTaskTree>>, now: String)
    {
        self.set_status_inprogress(rc);
        // 给当前任务添加timeline
        self.add_timeline_start(rc, now);
        // 打印
        let item: &ViewTaskTree = &*rc.borrow_mut();
    }

    // end: 设置任务为挂起
    pub fn stop_set_holdon(&mut self, rcs: &Vec<Rc<RefCell<ViewTaskTree>>>)
    {
        for rc in rcs {
            self.set_status_holdon(rc);
        }
    }
    
    // mv: 设置children任务的parent字段
    pub fn mv_set_children_parent(&mut self, rc: &Rc<RefCell<ViewTaskTree>>, tid: String) {
        let v: &mut ViewTaskTree = &mut *rc.borrow_mut();
        let children: &Vec<Rc<RefCell<ViewTaskTree>>> = &v.children;
        for c in children {
            // let t: &Rc<RefCell<ViewTaskTree>> = c;
            self.set_task_parent(c, Some(tid.to_string()));
        }
    }

    fn GetActionTask(&self, taskLabel: String, mustLeaf: bool) -> Result<Rc<RefCell<ViewTaskTree>>, TaskCommandError> {
        let taskID = convertTaskLabel(taskLabel);
        let taskOpt = self.find_task(&taskID);
        if taskOpt.is_none() {
            return Err(TaskCommandError::new(format!("Not Found Task {}", taskID)));
        }
        // TODO 限制非基元任务不允许start/stop等行动操作
        let taskRc = Rc::clone(taskOpt.as_ref().unwrap());
        if mustLeaf {
            let task: &ViewTaskTree = &*taskRc.borrow_mut();
            if task.children.len() > 0 {
                return Err(TaskCommandError::new(format!("Not Permit on NonLeaf Task {}", taskID)));
            }
        }
        Ok(taskRc)
    }

    fn GetOptionalActionTask(&self, taskLabel: Option<&String>, mustLeaf: bool, defaultTaskType: &str) -> Result<Rc<RefCell<ViewTaskTree>>, TaskCommandError> {
        let mut taskRc: Rc<RefCell<ViewTaskTree>>;
        if taskLabel.is_none() {
            // 默认获取进行中任务
            if defaultTaskType == "inprogress" {
                // 先获取进行中任务，并且只允许只有一个进行中任务
                let inProgressTasks = self.find_tasks_by_status(TaskStatus::IN_PROGRESS);
                if inProgressTasks.len() > 1 {
                    return Err(TaskCommandError::new(format!("In Progress Tasks more than one, len={} all={:?}", inProgressTasks.len(), inProgressTasks)));
                }
                taskRc = Rc::clone(&inProgressTasks[0]);
                Ok(taskRc)
            } else if defaultTaskType == "root" {
                // 默认获取root
                Ok(Rc::clone(self.viewTaskTree.as_ref().unwrap()))
            } else {
                Err(TaskCommandError::new(format!("bug: 错误的defaultTaskType: {}", defaultTaskType)))
            }
        } else {
            Ok(self.GetActionTask(taskLabel.unwrap().clone(), mustLeaf)?)
        }
    }

    fn migrateTask(&mut self, taskRc: &Rc<RefCell<ViewTaskTree>>, dbDst: &mut dyn DataSource<T>) -> Result<(), TaskCommandError> {
        let task: &ViewTaskTree = &taskRc.borrow_mut();
        let data: &ModelTaskItem = &*task.root.borrow_mut();
        println!("{}: {}", data.tid, data.title);
        if data.parent != None {
            let res = dbDst.AddTask(
                data.title.clone(),
                data.parent.as_ref().unwrap().clone(),
                Some(&data.priority.to_string()),
                Some(&data.tid.to_string()),
                Some(&data.status.to_string()),
                None,
                None,
            );
            match res {
                Err(e) => {
                    println!("{}", e);
                    return Ok(())
                }
                _ => (),
            }

            thread::sleep(time::Duration::from_millis(100));

            for t in &data.timeline {
                let start = Some(&t[0]);
                let mut end = None;
                if t.len() > 1 {
                    end = Some(&t[1]);
                }
                let res = dbDst.Start(data.tid.to_string(), start, end);
                match res {
                    Err(e) => {
                        // 导入的过程忽略timeline宽度为0的报错
                        if e.code != 20005 {
                            println!("{}", e);
                        } else {
                            println!("WARN: ignore error: timeline.colappse=0");
                        }
                    },
                    _ => (),
                }
                thread::sleep(time::Duration::from_millis(100));
            }

            for m in &data.marks {
                let mut action = "comment";
                if m.gains != None && m.gains.unwrap() {
                    action = "gains";
                }
                let res = dbDst.ActionMark(Some(&data.tid), action, m.content.to_string(), Some(&m.time));
                match res {
                    Err(e) => {
                        // 导入的过程忽略timeline宽度为0的报错
                        if e.code != 20005 {
                            println!("{}", e);
                        } else {
                            println!("WARN: ignore error: timeline.colappse=0");
                        }
                    },
                    _ => (),
                }
                thread::sleep(time::Duration::from_millis(100));
            }
        }
        for c in &task.children {
            self.migrateTask(c, dbDst)?;
        }
        Ok(())
    }

    pub fn MigrateTo(&mut self, dbDst: &mut dyn DataSource<T>) -> Result<(), TaskCommandError> {
        println!("Migrating...");
        // self.List(None);
        let taskRc = self.GetOptionalActionTask(None, false, "root")?;
        self.migrateTask(&taskRc, dbDst)?;

        Ok(())
    }
}



impl<T: DataView> JsonFileDB<T> {
// impl<T: DataView> DataSource<T> for JsonFileDB<T> {
    fn getView(&mut self) -> &mut T {
        &mut self.view
    }

    fn GetInfo(&mut self, taskLabel: Option<&String>) -> Result<TaskTreeModel, TaskCommandError>
    {
        let taskRc = self.GetOptionalActionTask(taskLabel, false, "inprogress")?;
        let task: &ViewTaskTree = &taskRc.borrow_mut();
        Ok(task.ToTreeModel())
    }

    fn GetList(&mut self, root: Option<&String>) -> Result<TaskTreeModel, TaskCommandError>
    {
        let taskRc = self.GetOptionalActionTask(root, false, "root")?;
        let task: &ViewTaskTree = &taskRc.borrow_mut();
        Ok(task.ToTreeModel())
    }


    fn AddTask(&mut self, taskTitle: String, parent: String, priority: Option<&String>, forceID: Option<&String>, forceStatus: Option<&String>) -> Result<TaskTreeModel, TaskCommandError>
    {
        let taskOpt = self.find_task(&parent);
        if taskOpt.is_none() {
            return Err(TaskCommandError::from(format!("Not Found Parent Task {}", parent)));
        }

        // 默认P2
        let mut iPriority = 2;
        if !priority.is_none() {
            iPriority = priority.unwrap().parse::<i64>().unwrap();
        }

        let taskRc = self.add_task(taskTitle, parent, iPriority);
        self.write();
        println!("Add Task Success!");

        let taskTree = ViewTaskTree {
            root: taskRc,
            children: Vec::new(),
            timeline: Vec::new(),
        };
        Ok(taskTree.ToTreeModel())
    }

    fn SetStart(&mut self, taskLabel: String, startTime: Option<&String>, endTime: Option<&String>) -> Result<TaskTreeModel, TaskCommandError>
    {
        let taskRc = self.GetActionTask(taskLabel, true)?;
        let start = getStartTime(startTime)?;

        // 先把其他进行中任务设置为阻塞
        let inProgressTasks = self.find_tasks_by_status(TaskStatus::IN_PROGRESS);
        self.start_set_blocking(&inProgressTasks, start.clone());
        // 把当前任务设置为进行中
        self.start_set_inprogress(&taskRc, start.clone());
        self.write();
        let task: &ViewTaskTree = &taskRc.borrow_mut();
        Ok(task.ToTreeModel())
    }

    fn finish(&mut self, taskID: String)
    {
        let task = self.find_task(&taskID);
        if task.is_none() {
            println!("Not Found Task {}", taskID);
            return ;
        }
        // 把当前任务设置为已完成
        self.set_status_finished(&task.unwrap());
        self.write();
    }

    fn mv(&mut self, srcTaskID: String, dstTaskID: String)
    {
        let dst = self.find_task(&dstTaskID);
        if !dst.is_none() {
            println!("Found Duplicate Dst Task {}", dstTaskID);
            return ;
        }

        let src = self.find_task(&srcTaskID);
        if src.is_none() {
            println!("Not Found Src Task {}", srcTaskID);
            return ;
        }
        let srcRef :&Rc<RefCell<ViewTaskTree>> = src.as_ref().unwrap();
        {
            let v: &ViewTaskTree = &*srcRef.borrow_mut();
        }
        self.set_task_tid(srcRef, dstTaskID.to_string());
        self.mv_set_children_parent(srcRef, dstTaskID.to_string());
        // self.mv_set_timeline(&srcTaskID, &dstTaskID);
        self.write();
        {
            let v: &ViewTaskTree = &*srcRef.borrow_mut();
            println!("mv success [{}]->[{}]", srcTaskID, dstTaskID);
        }
    }

    fn ActionMark(&mut self, taskLabel: Option<&String>, action: &str, content: String, time: Option<&String>)  -> Result<TaskTreeModel, TaskCommandError> 
    {
        let mut gains = false;
        if action == "gains" {
            gains = true;
        }
        let taskRc = self.GetOptionalActionTask(taskLabel, false, "inprogress")?;
        self.add_mark(&taskRc, content, gains);
        self.write();
        let task: &ViewTaskTree = &taskRc.borrow_mut();
        Ok(task.ToTreeModel())
    }
}
