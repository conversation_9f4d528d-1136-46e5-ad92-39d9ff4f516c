#[macro_use]
extern crate clap;
extern crate lazy_static;
extern crate chrono;
extern crate url;
extern crate libc;
mod error;
mod task_cli;

mod data_source;
mod json_file_db;
mod model;
mod http_db;

mod data_model;
mod http_model;

mod data_view;
mod terminal_view;

use std::time::Duration;

use std::env;

use std::fs::{File, read_to_string, remove_file};
use std::process::{Command, Stdio};
use std::io::{self, Write};
use std::thread;
use std::os::unix::io::{FromRawFd, AsRawFd};
use nix::pty::PtyMaster;
use nix::pty::{posix_openpt, ptsname};

use nix::Error;
use std::ffi::CString;
use std::ffi::CStr;
use std::os::raw::c_char;


use std::os::unix::process::CommandExt; // 导入正确的 CommandExt trait

use clap_complete::{generate, Shell};
use clap::{Parser, CommandFactory};

// 为了使用trait的方法，需要显示使用trait
use data_source::DataSource;
use data_view::DataView;
use terminal_view::TerminalView;

#[derive(StructOpt, Debug)]
struct Config {
    #[structopt(short, long, env = "FI")]   
    file: String,
}

fn main() {
    let mut view = TerminalView::new();
    // let mut db = json_file_db::JsonFileDB::<TerminalView>::new(view);
    let mut db = http_db::HttpDB::<TerminalView>::new(view);

    let cli = task_cli::TaskCli::parse();
    // println!("env: KTASK_DB_FILE: {}", cli.en.unwrap());

    match &cli.command {
        task_cli::TaskSubCommand::Generate {} => {
            generate(Shell::Bash, &mut task_cli::TaskCli::command(), "ktask", &mut std::io::stdout());
        }

        task_cli::TaskSubCommand::Pick {target_task, position} => {
            match db.Pickup(target_task.as_ref(), position.as_ref()) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::Last { number } => {
            match db.Last(number.as_ref()) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::RmPickup { position } => {
            match db.RmPickup(position.to_string()) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::Add { target_task, parent, priority, ddl, description } => {
            // db.add(target_task.to_string(), parent.to_string(), priority.as_ref());
            // shell.generate(&mut Cli::command(), &mut std::io::stdout());
            match db.Add(target_task.to_string(), parent.to_string(), priority.as_ref(), ddl.as_ref(), description.as_ref()) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::Edit { target_task, title, description, ddl} => {
            match db.Edit(target_task.to_string(), title.as_ref(), ddl.as_ref(), description.as_ref()) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::Info { verbose, task_id, show_time, filter_time, level} => {
            // db.info(task_id.as_ref());
            match db.Info(task_id.as_ref(), *show_time, filter_time.as_ref(), level.as_ref(), *verbose) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::List { root, show_time, filter_time } => {
            match db.List(root.as_ref(), *show_time, filter_time.as_ref()) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::Ln { task_id, label } => {
            match db.Link(task_id.to_string(), label.to_string()) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::Start { task_id, start_time, end_time } => {
            match db.Start(task_id.to_string(), start_time.as_ref(), end_time.as_ref()) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::Finish{ task_id } => {
            // db.finish(task_id.as_ref());
            match db.Finish(task_id.to_string()) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::RmTask{ task_id, force } => {
            match db.RmTask(task_id.to_string(), Some(&force)) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::Mv { srcTaskID, parent, dstTaskID } => {
            // db.mv(srcTaskID.to_string(), dstTaskID.to_string());
            match db.Mv(srcTaskID.to_string(), parent.as_ref(), dstTaskID.as_ref()) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::Regen { parent } => {
            match db.Regen(parent.to_string()) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::MarkComment { content, task_id, mark_time } => {
            if !content.is_none() {
                match db.Mark(task_id.as_ref(), "comment", content.as_ref().map(|s| s.to_string()).unwrap_or_else(String::new), mark_time.as_ref()) {
                    Err(e) => println!("{}", e),
                    _ => (),
                }
                return
            }

    		// 创建临时文件
    		let output = Command::new("mktemp").output().expect("Failed to create temporary file");
    		let temp_file = String::from_utf8_lossy(&output.stdout).trim().to_string();

    		// let temp_file_path = env::temp_dir().join("temp_file.txt");
    		// let mut temp_file = File::create(&temp_file_path).expect("Failed to create temp file");
    		// temp_file.write_all(b"Hello, World!").expect("Failed to write to temp file");

    		// 启动vim子进程编辑临时文件
    		Command::new("vim")
    		    .arg(&temp_file)
    		    .stdin(Stdio::inherit())
    		    .stdout(Stdio::inherit())
    		    .stderr(Stdio::inherit())
    		    .spawn()
    		    .expect("Failed to start vim process")
    		    .wait()
    		    .expect("vim process encountered an error");

    		// 读取临时文件的内容
    		// 获取 Vim 编辑的临时文件内容
    		let output = Command::new("cat")
    		    .arg(&temp_file)
    		    .output()
    		    .expect("Failed to get file content");

            let content = String::from_utf8_lossy(&output.stdout);
    		println!("{}", content);

    		// 删除临时文件
    		remove_file(temp_file).expect("Failed to delete temp file");
        
            match db.Mark(task_id.as_ref(), "comment", content.to_string(), mark_time.as_ref()) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::MarkThought { content, task_id, mark_time } => {
            match db.Mark(task_id.as_ref(), "thought", content.to_string(), mark_time.as_ref()) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::MarkDiary { content, task_id, mark_time } => {
            match db.Mark(task_id.as_ref(), "diary", content.to_string(), mark_time.as_ref()) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::MarkNote { content, task_id, mark_time } => {
            match db.Mark(task_id.as_ref(), "note", content.to_string(), mark_time.as_ref()) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::MarkGains { content, task_id, mark_time } => {
            match db.Mark(task_id.as_ref(), "gains", content.to_string(), mark_time.as_ref()) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::Search { keyword, in_marks, mark_type, limit } => {
            match db.Search(keyword.to_string(), *in_marks, mark_type.as_ref(), *limit) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        task_cli::TaskSubCommand::Migrate { } => {
            let mut view2 = TerminalView::new();
            let mut dbSrc = json_file_db::JsonFileDB::<TerminalView>::new(view2);
            match dbSrc.MigrateTo(&mut db) {
                Err(e) => println!("{}", e),
                _ => (),
            }
        }

        // task_cli::TaskSubCommand::Stop{ task_id } => {
        //     db.stop(task_id.as_ref());
        // }
        //
        // task_cli::TaskSubCommand::Reset { task_id } => {
        //     db.reset(task_id.as_ref());
        // }

        // task_cli::TaskSubCommand::Domain { command } => {
        //     match command {
        //         task_cli::DomainSubCommand::List {} => {
        //             db.domain_list();
        //         }

        //         task_cli::DomainSubCommand::Current {} => {
        //             db.domain_current();
        //         }

        //         task_cli::DomainSubCommand::Switch { target_domain } => {
        //             db.domain_switch(target_domain.to_string());
        //         }

        //         task_cli::DomainSubCommand::Show { target_domain } => {
        //             db.domain_show(target_domain.to_string());
        //         }

        //         task_cli::DomainSubCommand::SetDefault { target_domain } => {
        //             db.domain_set_default(target_domain.to_string());
        //         }
        //     }
        //     println!("task domain ");
        // }

        _ => todo!()
    }
}
