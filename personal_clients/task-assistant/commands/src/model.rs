use std::fmt;
use std::rc::Rc;
use std::cell::RefCell;
use serde::{Serialize, Deserialize};
use crate::data_model::TaskItemModel;
use crate::data_model::TaskTreeModel;
use crate::data_model::TaskMarkModel;

// // This only uses std
// pub fn ordered_map<S: Serializer, K: Serialize + Ord, V: Serialize>(
//     value: &HashMap<K, V>,
//     serializer: S,
// ) -> Result<S::Ok, S::Error> {
//     let mut items: Vec<(_, _)> = value.iter().collect();
//     items.sort_by(|a, b| a.0.cmp(&b.0));
//     BTreeMap::from_iter(items).serialize(serializer)
// }
// 
// pub fn ordered_rc_map<S: Serializer, K: Serialize + Ord, V: Serialize>(
//     valueRc: &Rc<RefCell<HashMap<K, V>>>,
//     serializer: S,
// ) -> Result<S::Ok, S::Error> {
//     let value = &*valueRc.borrow_mut();
//     let mut items: Vec<(_, _)> = value.iter().collect();
//     items.sort_by(|a, b| a.0.cmp(&b.0));
//     BTreeMap::from_iter(items).serialize(serializer)
// }


// rust不支持嵌套匿名struct，没办法了。。https://stackoverflow.com/questions/23629201/are-nested-structs-supported-in-rust
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ModelTaskMark {
    pub time: String,
    pub content: String,
    pub gains: Option<bool>,
}

// impl fmt::Display for ModelTaskMark {
//     fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
//         write!(f, "[{}] {}", self.time.bold(), self.content);
//         Ok(())
//     }
// }

#[derive(Serialize, Deserialize, PartialEq, Debug, Copy, Clone)]
pub enum TaskStatus {
    #[serde(rename = "待启动")]
    UNSTARTED,
    #[serde(rename = "进行中")]
    IN_PROGRESS,
    #[serde(rename = "阻塞中")]
    BLOCKING,
    #[serde(rename = "挂起中")]
    HOLD_ON,
    #[serde(rename = "已完成")]
    FINISH,
    #[serde(rename = "已删除")]
    REMOVED,
    #[serde(rename = "未知")]
    UNKNOWN,
}

impl fmt::Display for TaskStatus {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            TaskStatus::UNSTARTED => write!(f, "待启动"),
            TaskStatus::IN_PROGRESS => write!(f, "进行中"),
            TaskStatus::BLOCKING => write!(f, "阻塞中"),
            TaskStatus::HOLD_ON => write!(f, "挂起中"),
            TaskStatus::FINISH => write!(f, "已完成"),
            TaskStatus::REMOVED => write!(f, "已删除"),
            _ => write!(f, "未知"),
        }
    }
}

// #[derive(Debug, Serialize, Deserialize, Clone)]
#[derive(Debug, Serialize, Deserialize)]
pub struct ModelTaskItem {
    pub tid: String,
    pub parent: Option<String>,
    pub title: String,
    pub category: Option<String>,
    pub status: TaskStatus,
    pub description: String,
    pub priority: i64,
    pub quadrant: i64,
    pub createTime: String,
    pub ddl: String,
    pub level: Option<i64>,
    pub marks: Vec<ModelTaskMark>,
    pub timeline: Vec<Vec<String>>,
}

impl ModelTaskItem {
    pub fn new() -> Self {
        ModelTaskItem {
            tid: "".to_string(),
            parent: None,
            title: "".to_string(),
            category: None,
            status: TaskStatus::UNKNOWN,
            description: "".to_string(),
            priority: 0,
            quadrant: 0,
            createTime: "".to_string(),
            ddl: "".to_string(),
            level: None,
            marks: Vec::new(),
            timeline: Vec::new(),
        }
    }
}


// #[derive(Debug, Serialize, Deserialize)]
// pub struct ModelTriviaItem {
//     pub tid: String,
//     pub title: String,
// }

#[derive(Debug, Serialize, Deserialize)]
pub struct ModelHistoryItem {
    pub command: String,
    pub task_id: String,
    pub time: String,
    pub args: String,
}

// // #[derive(Debug)]
// #[derive(Debug, Serialize, Deserialize)]
// pub struct ModelDomain {
//     pub defaultDomain: bool,
//     pub currentDomain: bool,
//     pub tasks: Vec<Rc<RefCell<ModelTaskItem>>>,
//     pub histories: Vec<Rc<RefCell<ModelHistoryItem>>>,
//     pub trivia: Vec<ModelTriviaItem>,
//     // #[serde(serialize_with = "ordered_rc_map")]
//     // pub timeline: Rc<RefCell<HashMap<String, Vec<Vec<String>>>>>
// }

// impl fmt::Display for ModelDomain {
//     fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
//         // The `f` value implements the `Write` trait, which is what the
//         // write! macro is expecting. Note that this formatting ignores the
//         // various flags provided to format strings.
//         write!(f, "({}, {})", self.defaultDomain, self.currentDomain)
//     }
// }

// #[derive(Debug)]
#[derive(Debug, Serialize, Deserialize)]
pub struct ModelData {
    pub doingTask: String,
    // #[serde(serialize_with = "ordered_map")]
    // pub domains: HashMap<String, ModelDomain>,
    // pub trivia: Vec<ModelTriviaItem>,
    pub tasks: Vec<Rc<RefCell<ModelTaskItem>>>,
    pub histories: Vec<Rc<RefCell<ModelHistoryItem>>>,
}

#[derive(Debug)]
pub struct ViewTaskTree {
    // pub root: Option<ViewTaskItem>,
    pub root: Rc<RefCell<ModelTaskItem>>,
    pub children: Vec<Rc<RefCell<ViewTaskTree>>>,
    pub timeline: Vec<Vec<String>>,
}

impl ViewTaskTree {
    fn GetItemModel(&self) -> TaskItemModel {
        let mut subs = Vec::new();
        if self.children.len() > 0 {
            for c in &self.children {
                let cData = &*c.borrow_mut();
                subs.push(cData.GetItemModel());
            }
        }
        let data = &*self.root.borrow_mut();

        TaskItemModel{
            id: data.tid.clone(),
            parent: data.parent.clone(),
            category: data.category.clone(),
            ddl: data.ddl.clone(),
            title: data.title.clone(),
            description: data.description.clone(),
            createTime: data.createTime.clone(),
            priority: data.priority,
            status: data.status.to_string(),
            level: data.level.unwrap(),
            label: None,
            subs: subs,
            timeSpan: None,
            timeInvested: None,
        }
    }

    fn GetMarkModel(&self) -> Vec<TaskMarkModel> {
        let mut marks = Vec::new();
        // TODO 暂时只实现附着某节点的marks，不递归获取子节点的
        let data = &*self.root.borrow_mut();
        for m in &data.marks {
            marks.push(TaskMarkModel{
                time: m.time.clone(),
                content: m.content.clone(),
                docLink: None,
                id: None,
                markType: "".to_string(),
                // gains: m.gains,
            });
        }
        marks
    }

    pub fn ToTreeModel(&self) -> TaskTreeModel {
        let mut m = TaskTreeModel {
            treeRoot: self.GetItemModel(),
            marks: self.GetMarkModel(),
            timelines: self.timeline.to_vec(),
        };
        m
    }
}
