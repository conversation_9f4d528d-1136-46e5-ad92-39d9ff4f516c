use clap::{Parser, Subcommand};

/// A Command-line Tool For Managing Task Better.
#[derive(Parser)]
#[clap(author="luzeshu <<EMAIL>>", version="0.1.0", about, long_about = None)]
pub struct TaskCli {
    /// Name of the person to greet
    #[clap(short, long)]
    name: Option<String>,

    #[clap(env = "KTASK")]
    pub en: Option<String>,

    /// Number of times to greet
    #[clap(short, long, default_value_t = 1)]
    count: u8,

    #[clap(subcommand)]
    pub command: TaskSubCommand,
}

#[derive(Subcommand)]
pub enum TaskSubCommand {
    /// Generate bash_completion
    Generate {
    },
    Pick {
        target_task: Option<String>,
        // #[clap(short, long)]
        position: Option<String>,
    },
    RmPickup {
        position: String,
    },
    Last {
        #[clap(short, long)]
        number: Option<String>,
    },
    Info{
        task_id: Option<String>,
        #[clap(short, takes_value(false))]
        verbose: bool,
        #[clap(long, takes_value(false))]
        show_time: bool,
        #[clap(long)]
        filter_time: Option<String>,
        #[clap(long)]
        level: Option<String>,
    },
    List {
        #[clap(short, long)]
        root: Option<String>,
        #[clap(long, takes_value(false))]
        show_time: bool,
        #[clap(long)]
        filter_time: Option<String>,
    },
    Add {
        target_task: String,
        #[clap(short, long)]
        parent: String,
        #[clap(long)]
        priority: Option<String>,
        #[clap(long)]
        ddl: Option<String>,
        #[clap(long)]
        description: Option<String>,
    },
    Ln {
        task_id: String,
        label: String,
    },
    // Delete {
    //     task_id: String,
    // },
    Edit {
        target_task: String,
        #[clap(long)]
        title: Option<String>,
        #[clap(long)]
        description: Option<String>,
        #[clap(long)]
        ddl: Option<String>,
    },
    // Show {
    //     task_id: String,
    // },
    // Done {
    //     task_id: String,
    // },
    Start {
        task_id: String,
        #[clap(long)]
        start_time: Option<String>,
        #[clap(long)]
        end_time: Option<String>,
    },
    // Stop {
    //     task_id: Option<String>,
    // },
    Finish {
        task_id: String,
    },
    RmTask {
        task_id: String,
        // NOTE 这里takes_value(true)的话表示--force必须带参数。这样就可以用--force来作为bool指定了
        // NOTE 另外，这里force必须用bool，不能是Option<bool>，否则也达不到要的目的。
        #[clap(short, long, takes_value(false))]
        force: bool,
    },
    Mv {
        srcTaskID: String,
        #[clap(short, long)]
        parent: Option<String>,
        dstTaskID: Option<String>,
    },
    Regen {
        parent: String,
    },
    /// Add task progress marks
    // Mark {
    //     content: String,
    //     #[clap(long)]
    //     task_id: Option<String>,
    //     #[clap(short, long, takes_value(false))]
    //     gains: bool,
    // },
    MarkComment {
         content: Option<String>,
         #[clap(long)]
         task_id: Option<String>,
         #[clap(long)]
         mark_time: Option<String>,
    },
    MarkThought {
         content: String,
         #[clap(long)]
         task_id: Option<String>,
         #[clap(long)]
         mark_time: Option<String>,
    },
    MarkDiary {
         content: String,
         #[clap(long)]
         task_id: Option<String>,
         #[clap(long)]
         mark_time: Option<String>,
    },
    MarkNote {
         content: String,
         #[clap(long)]
         task_id: Option<String>,
         #[clap(long)]
         mark_time: Option<String>,
    },
    MarkGains {
         content: String,
         #[clap(long)]
         task_id: Option<String>,
         #[clap(long)]
         mark_time: Option<String>,
    },
    Reset {
        task_id: String,
    },
    Search {
        keyword: String,
        #[clap(long)]
        in_marks: bool,
        #[clap(long)]
        mark_type: Option<String>,
        #[clap(long, default_value_t = 20)]
        limit: u32,
    },
    Migrate {
    },
}
