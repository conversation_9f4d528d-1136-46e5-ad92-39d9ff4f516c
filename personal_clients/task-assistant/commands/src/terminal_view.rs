
use crate::data_model::PickupListModel;
use crate::data_model::TaskTreeModel;
use crate::data_model::TaskMarkModel;
use crate::data_model::TaskItemModel;
use crate::data_view::DisplayTreeOptions;
use crate::error::TaskCommandError;
use crate::DataView;
use colored::Colorize;
use chrono;
use chrono::{NaiveDateTime, DateTime, Local, TimeZone};

fn calcTimespan(start: String, mut end: String) -> i64 {
    let nowTime: DateTime<Local> = chrono::offset::Local::now();
    let nowTimeStr = nowTime.to_string().get(..19).unwrap().to_string();

    if end == "" {
        end = nowTimeStr;
    }

    let naiveStart = NaiveDateTime::parse_from_str(&start, "%Y-%m-%d %H:%M:%S").unwrap();
    let startTime: DateTime<Local> = Local.from_local_datetime(&naiveStart).unwrap();

    let naiveEnd = NaiveDateTime::parse_from_str(&end, "%Y-%m-%d %H:%M:%S").unwrap();
    let endTime: DateTime<Local> = Local.from_local_datetime(&naiveEnd).unwrap();

    let duration = endTime.signed_duration_since(startTime).num_seconds();
    return duration;
}

fn formatDuration(duration: i64) -> String {
	let seconds = duration % 60;
	let minutes = (duration / 60) % 60;
	let hours = (duration / 60) / 60;
    if hours != 0 {
        return format!("{}h{}m{}s", hours, minutes, seconds);
    }
    if minutes != 0 {
        return format!("{}m{}s", minutes, seconds);
    }
    return format!("{}s", seconds);
}

fn formatTimeline(timeline: &Vec<String>) -> String {
    let start = &timeline[0];
    let end = &timeline[1];

    let timespanSeconds = calcTimespan(start.to_string(), end.to_string());
    let timespanStr = formatDuration(timespanSeconds);
    let mut endtag = end.to_string();
    if end == "" {
        endtag = "NULL".to_string();
    }
    let timeStr = format!("[{}, {}]/{}", start, endtag, timespanStr.yellow());
    return timeStr;
}

pub struct TerminalView {
    id: String,
}

impl TerminalView {
    pub fn new() -> Self {
        TerminalView {
            id: "777".to_string(),
        }
    }

    fn PrintTask(&self, task: &TaskItemModel, showTime: bool) {
        // let mut level = "L?".to_string().bold().red();
        // if !self.level.is_none() {
        //     level = format!("L{}", self.level.unwrap()).bold().blue();
        // }
        let level = format!("L{}", task.level).bold().blue();

        // let rc: Rc<RefCell<ModelTaskItem>> = Rc::clone(&task.data);
        // let mut data: &ModelTaskItem = &*rc.borrow_mut();
        // let root: ViewTaskItem = v.root.as_mut().unwrap();
        // let mut data = root.data.borrow_mut();

        // let data = &task.data;

        let mut priority = format!("P{}", task.priority).bold().on_truecolor(0xFF, 0xFA, 0xFA);
        if task.priority == 0 {
            // priority = priority.red()
            priority = priority.truecolor(0xEE, 0x00, 0x00)
        } else if task.priority == 1 {
            // priority = priority.yellow()
            priority = priority.truecolor(0xFF, 0xC1, 0x25)
        } else if task.priority == 2 {
            // priority = priority.blue()
            priority = priority.truecolor(0x00, 0x00, 0xEE)
        } else {
            // priority = priority.green()
            // priority = priority.truecolor(0x22, 0x8B, 0x22)
            priority = priority.truecolor(0x00, 0x8B, 0x00)
        }

        // let width: usize = (task.level * 2 + 2).try_into().unwrap();
        let mut width = task.level * 2;
        if width < 0 {
            width=0;
        }
        let tid = format!("[{: >width$}]", task.id.green(), width = width as usize);

        let mut category = "".bold();
        // if task.category == Some("OKR".to_string()) {
        if task.category != None {
            category = task.category.as_ref().unwrap().on_red().bold();
        }

        // https://www.w3schools.com/charsets/ref_utf_dingbats.asp
        // https://unicode.org/emoji/charts/full-emoji-list.html
        // let markSuccess = String::from_utf8(vec![0xE2, 0x9C, 0x93]).unwrap().bold().green();
        // let markSuccess = String::from_utf8(vec![0xE2, 0x9C, 0x94]).unwrap().bold().green();
        // let markFailed = String::from_utf8(vec![0xE2, 0x9C, 0x95]).unwrap().bold().red();
        let markWriteHand = "📝".to_string().bold().green();
        let markThumbsUp = "👍".to_string().bold().green();
        let markCheckBox = "⭕".to_string().bold().green();
        let markSpeech = "💬".to_string().bold().green();
        let markSuccess = String::from_utf8(vec![0xE2, 0x9C, 0x85]).unwrap().bold().green();
        // let markFailed = String::from_utf8(vec![0xE2, 0x9D, 0x8C]).unwrap().bold().red();

        // echo -e "\e[9myour text goes here\e"
        // \e可以用\x1B替换: https://superuser.com/questions/33914/why-doesnt-echo-support-e-escape-when-using-the-e-argument-in-macosx
        // let mut title = format!("{}", task.title);
        let mut titleText = format!("{}", task.title);
        let mut status = task.status.to_string().bold();
        let mut statusIcon = "";

        let mut title = format!("{}", titleText).white();
        match task.status.as_str() {
            "待启动" => {
                status = status.on_yellow();
                statusIcon = &markCheckBox;
            },
            "进行中" => {
                status = status.on_bright_blue();
                statusIcon = &markWriteHand;
                title = format!("{}", titleText).bold().red();
            },
            "阻塞中" => {
                status = status.on_bright_magenta();
                statusIcon = &markSpeech;
            },
            "挂起中" => {
                status = status.on_purple();
                statusIcon = &markSpeech;
            },
            "已完成" => {
                status = status.on_green();
                statusIcon = &markThumbsUp;
                // 删除线
                title = format!("\x1B[9m{}\x1B[0m{}", titleText, &markSuccess).white();
            },
            "已删除" => {
                status = status.on_red();
                statusIcon = &markSpeech;
                // 删除线
                title = format!("\x1B[9m{}\x1B[0m", titleText).white();
            }
            _ => {
                status = "未知".to_string().bold().red();
                statusIcon = &markSpeech;
            }
        }

        if task.level == 0 || task.level == 1 {
            title = title.bold().cyan();
        } else if task.level == 2 {
            title = title.bold();
        }

        let mut timeSpan = "".to_string();
        let mut timeInvested = "".to_string();
        if task.timeInvested != None && showTime {
            // 空格补齐至10长度，>表示右对齐
            timeInvested = format!("[{:>10}]", formatDuration(task.timeInvested.unwrap()).yellow());
        }

        let mut label = "".to_string();
        if task.label != None {
            label = format!("[{}]", task.label.as_ref().unwrap().bold().green());
        }

        let mut ddl = "".to_string();
        if task.ddl != "" {
            ddl = format!("[ddl:{}]", task.ddl.to_string());
        }

        println!("{}{}{}{}{}{}{}{}{}{}", tid, priority,
            status,
            // statusIcon,
            // category, task.title.bold(),
            level,
            timeInvested,
            timeSpan,
            label,
            category, &title, ddl.yellow().bold())
            // &markSuccess, &markFailed)
    }

    fn PrintTaskTree(&self, task: &TaskItemModel, showRoot: bool, showTime: bool) {
        if showRoot {
            self.PrintTask(task, showTime);
        }
        for s in &task.subs {
            self.PrintTaskTree(s, true, showTime);
        }
        if task.level == 1 {
            println!("");
        }
    }

    fn PrintDescription(&self, task: &TaskItemModel) {
        println!("{}", "[Description]".to_string().bold().green());
        println!("{}", task.description);
        // println!();
    }

    fn PrintMarks(&self, marks: &Vec<TaskMarkModel>, verbose: bool) {
        // 打印marks
        println!("{}", "[Marks]".to_string().bold().green());
        if marks.len() < 5 || verbose {
            for m in marks {
                println!("[{}] {}", m.time.bold(), m.content);
            }
        } else {
            let lastIdx = marks.len() - 1;
            println!("[{}] {}", marks[0].time.bold(), marks[0].content);
            //println!("[{}] {}", marks[1].time.bold(), marks[1].content);
            println!("........");
            println!("[{}] {}", marks[lastIdx-2].time.bold(), marks[lastIdx-2].content);
            println!("[{}] {}", marks[lastIdx-1].time.bold(), marks[lastIdx-1].content);
            println!("[{}] {}", marks[lastIdx].time.bold(), marks[lastIdx].content);
        }
        println!();
    }

    fn PrintTimelines(&self, timelines: &Vec<Vec<String>>, verbose: bool) {
        // 打印timeline
        println!("{}", "[Timelines]".to_string().bold().green());
        // NOTE 这里直接copy一个timeline，否则需要对last添加now，需要&self改成&mut
        // self。目前做不到。此处只是为了信息展示，对self进行修改实际应该在内部封闭，这些限制其实很合理
        let mut timeline = timelines.to_vec();
        if timeline.len() != 0 {
            let lastIdx = timeline.len() - 1;

            let start = timeline[0][0].to_string();
            let last = &mut timeline[lastIdx];

            let mut nowTag = "";

            if last.len() == 1 {
                // 进行中，把now添加到末尾节点
                let nowTime: DateTime<Local> = chrono::offset::Local::now();
                let now = nowTime.to_string().get(..19).unwrap().to_string();
                // 此处不影响mdata，只是viewTree的数据，不会回写json
                last.push(now);
                nowTag = "/now";
            }
            let end = last[1].to_string();
            let timespanSeconds = calcTimespan(start.to_string(), end.to_string());
            let timespanStr = formatDuration(timespanSeconds);
            let timespan = format!("跨度({})", timespanStr).bold().yellow();

            let mut timeInvestedSeconds = 0;
            for t in &timeline {
                let start = &t[0];
                let end = &t[1];
                let timespanSeconds = calcTimespan(start.to_string(), end.to_string());
                timeInvestedSeconds += timespanSeconds;
            }
            let timeInvestedStr = formatDuration(timeInvestedSeconds);
            let timeInvested = format!("投入({})", timeInvestedStr).bold().yellow();

            println!("{}{} {} [{}, {}{}]", "", timespan, timeInvested, start, end, nowTag.bold().red());

            if timeline.len() <= 5 || verbose {
                // 小于5个记录，全部打印
                for t in &timeline {
                    println!("{}", formatTimeline(t));
                }
            } else {
                println!("{}", formatTimeline(&timeline[0]));
                // println!("{}", formatTimeline(&timeline[1]));
                println!("........");
                println!("{}", formatTimeline(&timeline[lastIdx-2]));
                println!("{}", formatTimeline(&timeline[lastIdx-1]));
                println!("{}", formatTimeline(&timeline[lastIdx]));
            }
            // write!(f, "{: >18}+-> {:?}\n", "", timeline);
        }
        println!();
    }
}

impl DataView for TerminalView {
    fn DisplayTree(&mut self, model: TaskTreeModel, options: DisplayTreeOptions) -> Result<(), TaskCommandError>
    {
        self.PrintTaskTree(&model.treeRoot, options.showRoot, options.showTime);
        self.PrintDescription(&model.treeRoot);
        if options.showMarks {
            self.PrintMarks(&model.marks, options.verbose);
            self.PrintTimelines(&model.timelines, options.verbose);
        }
        Ok(())
    }

    fn DisplayPickupList(&mut self, model: PickupListModel) -> Result<(), TaskCommandError>
    {
        for (i, t) in model.tasks.iter().enumerate() {
            // 空格补齐至2长度，>表示右对齐。todo list不会塞入超过100个
            print!("{:>2}/", i.to_string().bold().red());
            self.PrintTaskTree(t, true, false)
        }
        Ok(())
    }
}
