# React Native

# OSX
.DS_Store
.AppleDouble
.LSOverride

# Xcode
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
ios/.xcode.env.local

# Android/IntelliJ
android/app/build/
android/app/src/main/assets/
android/app/src/main/res/bundle/
android/app/src/main/res/raw/
android/build/
android/gradle/
android/gradlew
android/gradlew.bat
android/local.properties
android/keystores/
*.iml
.idea
.gradle
local.properties
*.hprof
.cxx/

# node.js
node_modules/
npm-debug.log
yarn-error.log

# Bundle artifacts
*.jsbundle

# CocoaPods
ios/Pods/
ios/Podfile.lock

# Expo
.expo/
dist/
web-build/

# Flipper
ios/Flipper-Folly

# Metro
.metro-health-check*

# Temporary files
*.tmp
*.temp
.tmp/
.temp/

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
*.tgz
*.tar.gz
*.zip

# Backup files
*.bak
*.backup
