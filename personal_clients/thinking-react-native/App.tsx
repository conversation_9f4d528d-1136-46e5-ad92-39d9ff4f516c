import React, {useState, useEffect} from 'react';
import {
  StatusBar,
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  SafeAreaView,
  BackHandler,
  Linking,
  NativeModules,
  FlatList,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import favoritesService from './src/services/favoritesService';
import TasksScreen from './src/screens/tasks/TasksScreen';
import NotesScreen from './src/screens/notes/NotesScreen';
import FavoritesScreen from './src/screens/favorites/FavoritesScreen';
import DriveScreen from './src/screens/drive/DriveScreen';
import { ToastProvider } from './src/utils/ToastManager';
import AppHeader from './src/components/AppHeader';
import NoteEditHeaderActions from './src/components/NoteEditHeaderActions';

const {ShareIntentModule} = NativeModules;

// Tab导航组件
interface TabBarProps {
  activeTab: string;
  onTabPress: (tab: string) => void;
}

const TabBar: React.FC<TabBarProps> = ({activeTab, onTabPress}) => {
  return (
    <View style={styles.tabBar}>
      <TouchableOpacity
        style={[styles.tabItem, activeTab === 'home' && styles.activeTabItem]}
        onPress={() => onTabPress('home')}>
        <Text
          style={[
            styles.tabText,
            activeTab === 'home' && styles.activeTabText,
          ]}>
          🏠 主页
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tabItem, activeTab === 'messages' && styles.activeTabItem]}
        onPress={() => onTabPress('messages')}>
        <Text
          style={[
            styles.tabText,
            activeTab === 'messages' && styles.activeTabText,
          ]}>
          💬 消息
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={[styles.tabItem, activeTab === 'profile' && styles.activeTabItem]}
        onPress={() => onTabPress('profile')}>
        <Text
          style={[
            styles.tabText,
            activeTab === 'profile' && styles.activeTabText,
          ]}>
          👤 我的
        </Text>
      </TouchableOpacity>
    </View>
  );
};

// 主页内容
const HomeScreen: React.FC<{onNavigate: (screen: string) => void}> = ({
  onNavigate,
}) => (
  <ScrollView contentContainerStyle={styles.screenContent}>
    <Text style={styles.headerTitle}>欢迎使用 Thinking</Text>
    <Text style={styles.headerSubtitle}>
      整合收藏夹、任务管理和笔记功能的个人生产力工具
    </Text>

    <Text style={styles.sectionTitle}>功能模块</Text>

    <TouchableOpacity
      style={[styles.appButton, {backgroundColor: '#FF6B35'}]}
      onPress={() => onNavigate('favorites')}>
      <Text style={styles.appButtonText}>📚 收藏夹</Text>
      <Text style={styles.appButtonDesc}>管理和浏览你的网络收藏</Text>
    </TouchableOpacity>

    <TouchableOpacity
      style={[styles.appButton, {backgroundColor: '#4ECDC4'}]}
      onPress={() => onNavigate('tasks')}>
      <Text style={styles.appButtonText}>✅ 任务管理</Text>
      <Text style={styles.appButtonDesc}>跟踪和管理你的任务进度</Text>
    </TouchableOpacity>

    <TouchableOpacity
      style={[styles.appButton, {backgroundColor: '#45B7D1'}]}
      onPress={() => onNavigate('notes')}>
      <Text style={styles.appButtonText}>📝 笔记</Text>
      <Text style={styles.appButtonDesc}>记录想法和知识管理</Text>
    </TouchableOpacity>

    <TouchableOpacity
      style={[styles.appButton, {backgroundColor: '#9C27B0'}]}
      onPress={() => onNavigate('drive')}>
      <Text style={styles.appButtonText}>☁️ 云盘</Text>
      <Text style={styles.appButtonDesc}>安全可靠的文件存储和管理</Text>
    </TouchableOpacity>
  </ScrollView>
);

// 消息页面
const MessagesScreen: React.FC = () => (
  <ScrollView contentContainerStyle={styles.screenContent}>
    <Text style={styles.screenTitle}>消息中心</Text>
    <View style={styles.messageItem}>
      <Text style={styles.messageTitle}>欢迎使用 Thinking!</Text>
      <Text style={styles.messageContent}>
        感谢使用我们的应用。这里会显示系统通知和重要消息。
      </Text>
      <Text style={styles.messageTime}>刚刚</Text>
    </View>
  </ScrollView>
);

// 个人页面
const ProfileScreen: React.FC = () => (
  <ScrollView contentContainerStyle={styles.screenContent}>
    <View style={styles.profileHeader}>
      <View style={styles.avatar}>
        <Text style={styles.avatarText}>T</Text>
      </View>
      <View>
        <Text style={styles.profileName}>Thinking用户</Text>
        <Text style={styles.profileEmail}><EMAIL></Text>
      </View>
    </View>

    <View style={styles.statsContainer}>
      <View style={styles.statItem}>
        <Text style={styles.statValue}>128</Text>
        <Text style={styles.statLabel}>收藏数</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statValue}>45</Text>
        <Text style={styles.statLabel}>任务数</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statValue}>23</Text>
        <Text style={styles.statLabel}>笔记数</Text>
      </View>
      <View style={styles.statItem}>
        <Text style={styles.statValue}>-</Text>
        <Text style={styles.statLabel}>云盘文件</Text>
      </View>
    </View>
  </ScrollView>
);

// 主应用组件
const App: React.FC = () => {
  const [activeTab, setActiveTab] = useState('home');
  const [currentScreen, setCurrentScreen] = useState('main');
  const [sharedUrl, setSharedUrl] = useState<string>('');
  const [sharedTitle, setSharedTitle] = useState<string>('');
  const [notesPageState, setNotesPageState] = useState<{
    view: string;
    isEditing: boolean;
    showSettings: boolean;
  }>({
    view: 'main',
    isEditing: false,
    showSettings: false,
  });



  const handleTabPress = (tab: string) => {
    setActiveTab(tab);
    setCurrentScreen('main');
  };

  const handleNavigate = (screen: string) => {
    setCurrentScreen(screen);
  };

  const handleBack = () => {
    setCurrentScreen('main');
    // 清除分享的URL和标题
    setSharedUrl('');
    setSharedTitle('');
  };

  // 处理分享内容
  const handleSharedContent = (sharedData: any) => {
    console.log('Received shared data:', sharedData);
    
    if (sharedData && sharedData.text) {
      const sharedText = sharedData.text;
      
      // 提取URL
      let extractedUrl = '';
      let extractedTitle = '';
      
      // 检查是否包含URL
      const urlRegex = /(https?:\/\/[^\s]+)/g;
      const urlMatch = sharedText.match(urlRegex);
      
      if (urlMatch && urlMatch.length > 0) {
        extractedUrl = urlMatch[0];
        
        // 提取标题：URL前面的文本
        const titlePart = sharedText.replace(urlRegex, '').trim();
        if (titlePart) {
          extractedTitle = titlePart;
        }
      } else if (sharedText.includes('bilibili.com') || sharedText.includes('youtube.com')) {
        // 如果是Bilibili或YouTube分享，可能整个文本就是URL
        extractedUrl = sharedText.trim();
      }
      
      if (extractedUrl) {
        console.log('Extracted URL:', extractedUrl);
        console.log('Extracted Title:', extractedTitle);
        
        // 设置分享数据，包含URL和标题
        setSharedUrl(extractedUrl);
        setSharedTitle(extractedTitle);
        
        // 自动导航到收藏夹添加页面
        setCurrentScreen('favorites');
        
        // 清除Intent
        if (ShareIntentModule) {
          ShareIntentModule.clearShare()
            .then(() => console.log('Share intent cleared'))
            .catch((error: any) => console.error('Error clearing share intent:', error));
        }
      }
    }
  };

  // 处理分享内容
  useEffect(() => {
    // 检查应用启动时的分享内容
    const getInitialShare = async () => {
      try {
        if (ShareIntentModule) {
          const sharedData = await ShareIntentModule.getInitialShare();
          if (sharedData) {
            console.log('Found initial share data:', sharedData);
            handleSharedContent(sharedData);
          }
        }
      } catch (error) {
        console.error('Error getting initial share:', error);
      }
    };

    getInitialShare();
  }, []);

  useEffect(() => {
    const backAction = () => {
      // 如果在笔记页面，让NotesScreen处理所有返回逻辑
      if (currentScreen === 'notes') {
        return false; // 让NotesScreen处理返回逻辑
      }

      if (currentScreen !== 'main') {
        setCurrentScreen('main');
        return true;
      }
      Alert.alert('退出应用', '确定要退出应用吗？', [
        {
          text: '取消',
          onPress: () => null,
          style: 'cancel',
        },
        {text: '确定', onPress: () => BackHandler.exitApp()},
      ]);
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => backHandler.remove();
  }, [currentScreen]);

  const renderContent = () => {
    if (currentScreen === 'favorites') {
      return <FavoritesScreen sharedUrl={sharedUrl} sharedTitle={sharedTitle} onBack={handleBack} />;
    }
    if (currentScreen === 'tasks') {
      return <TasksScreen />;
    }
    if (currentScreen === 'notes') {
      return (
        <NotesScreen
          onBack={handleBack}
          onPageStateChange={setNotesPageState}
          pageState={notesPageState}
        />
      );
    }
    if (currentScreen === 'drive') {
      return <DriveScreen onBack={handleBack} />;
    }

    switch (activeTab) {
      case 'home':
        return <HomeScreen onNavigate={handleNavigate} />;
      case 'messages':
        return <MessagesScreen />;
      case 'profile':
        return <ProfileScreen />;
      default:
        return <HomeScreen onNavigate={handleNavigate} />;
    }
  };

  const getHeaderTitle = () => {
    if (currentScreen === 'main') {
      return activeTab === 'home'
        ? 'Thinking'
        : activeTab === 'messages'
          ? '消息中心'
          : '个人中心';
    }
    if (currentScreen === 'notes') {
      if (notesPageState.view === 'add') {
        return '新建笔记';
      } else if (notesPageState.view === 'edit') {
        return '编辑笔记';
      } else if (notesPageState.view === 'list') {
        return '笔记列表';
      } else if (notesPageState.showSettings) {
        return '笔记设置';
      }
      return '笔记';
    }
    return currentScreen === 'favorites'
      ? '收藏夹'
      : currentScreen === 'tasks'
        ? '任务管理'
        : currentScreen === 'drive'
          ? '云盘'
          : '';
  };

  const shouldShowBackButton = () => {
    return currentScreen !== 'main';
  };

  const handleSettingsPress = () => {
    setNotesPageState(prev => ({ ...prev, showSettings: true }));
  };

  const getRightComponent = () => {
    // 在笔记编辑页面（新建或编辑）显示设置按钮，设置页面显示时隐藏按钮
    if (currentScreen === 'notes' && (notesPageState.view === 'add' || notesPageState.view === 'edit') && !notesPageState.showSettings) {
      return (
        <NoteEditHeaderActions
          onSettingsPress={handleSettingsPress}
        />
      );
    }
    return null;
  };

  return (
    <ToastProvider>
      <SafeAreaView style={styles.container}>
        <AppHeader
          title={getHeaderTitle()}
          showBackButton={shouldShowBackButton()}
          onBackPress={handleBack}
          rightComponent={getRightComponent()}
        />

        <View style={styles.content}>
          {renderContent()}
        </View>

        {currentScreen === 'main' && (
          <TabBar activeTab={activeTab} onTabPress={handleTabPress} />
        )}
      </SafeAreaView>
    </ToastProvider>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },

  content: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#49454F',
    marginBottom: 24,
  },
  screen: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  screenContent: {
    padding: 16,
  },
  screenTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 16,
  },
  appButton: {
    padding: 20,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  appButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  appButtonDesc: {
    fontSize: 14,
    color: '#FFFFFF',
    opacity: 0.9,
  },
  messageItem: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
    elevation: 1,
  },
  messageTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 4,
  },
  messageContent: {
    fontSize: 14,
    color: '#49454F',
    marginBottom: 8,
  },
  messageTime: {
    fontSize: 12,
    color: '#79747E',
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    padding: 20,
    borderRadius: 12,
    marginBottom: 24,
    elevation: 2,
  },
  avatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: '#6750A4',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  avatarText: {
    color: '#FFFFFF',
    fontSize: 24,
    fontWeight: 'bold',
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    color: '#49454F',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    backgroundColor: '#FFFFFF',
    flex: 0.31,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6750A4',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#49454F',
  },

  comingSoonContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  comingSoonIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  comingSoonTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 12,
  },
  comingSoonText: {
    fontSize: 16,
    color: '#49454F',
    textAlign: 'center',
    lineHeight: 24,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: '#E6E0E9',
    paddingVertical: 8,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  activeTabItem: {
    // Active state styling handled by text color
  },
  tabText: {
    fontSize: 12,
    color: '#79747E',
    fontWeight: '500',
  },
  activeTabText: {
    color: '#6750A4',
    fontWeight: 'bold',
  },
  favoritesTabBar: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E6E0E9',
  },
  favoritesTab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeFavoritesTab: {
    borderBottomWidth: 2,
    borderBottomColor: '#6750A4',
  },
  favoritesTabText: {
    fontSize: 14,
    color: '#79747E',
    fontWeight: '500',
  },
  activeFavoritesTabText: {
    color: '#6750A4',
    fontWeight: 'bold',
  },
  favoritesContent: {
    flex: 1,
  },
  scrollableContent: {
    flex: 1,
    padding: 16,
  },
  loadingText: {
    textAlign: 'center',
    fontSize: 16,
    color: '#79747E',
    marginTop: 50,
  },
  favoriteItem: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 8,
    marginHorizontal: 16,
    marginBottom: 12,
    elevation: 1,
  },
  favoriteItemContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  favoriteAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  favoriteAvatarText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  favoriteContent: {
    flex: 1,
  },
  favoriteTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  favoriteTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1C1B1F',
    lineHeight: 20,
    flex: 1,
    marginRight: 8,
  },
  favoriteStatusBadge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 10,
    minWidth: 20,
    alignItems: 'center',
  },
  favoriteStatusIcon: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  favoriteUrl: {
    fontSize: 14,
    color: '#49454F',
    marginBottom: 6,
  },
  favoriteStatusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  favoriteStatusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  favoriteStatusCode: {
    fontSize: 11,
    color: '#9E9E9E',
    marginLeft: 8,
    backgroundColor: '#F5F5F5',
    paddingHorizontal: 4,
    paddingVertical: 1,
    borderRadius: 3,
  },
  favoriteFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
  },
  favoriteFooterLeft: {
    flex: 1,
  },
  favoriteRightInfo: {
    alignItems: 'flex-end',
  },
  favoriteAuthor: {
    fontSize: 12,
    color: '#79747E',
    marginBottom: 2,
  },
  favoriteTime: {
    fontSize: 11,
    color: '#9E9E9E',
  },
  favoriteClicks: {
    fontSize: 12,
    color: '#79747E',
    marginBottom: 4,
  },
  favoriteStatusActive: {
    backgroundColor: '#4CAF50',
    width: 18,
    height: 18,
    borderRadius: 9,
    alignItems: 'center',
    justifyContent: 'center',
  },
  favoritesList: {
    flex: 1,
  },
  favoritesListContent: {
    paddingBottom: 16,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 50,
  },
  loadingMoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
  },
  loadingMoreText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#79747E',
  },
  noMoreContainer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  noMoreText: {
    fontSize: 14,
    color: '#9E9E9E',
  },
  statusBar: {
    padding: 12,
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusBarSuccess: {
    backgroundColor: '#E8F5E8',
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  statusBarError: {
    backgroundColor: '#FFEBEE',
    borderLeftWidth: 4,
    borderLeftColor: '#F44336',
  },
  statusBarInfo: {
    backgroundColor: '#E3F2FD',
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  statusBarText: {
    fontSize: 14,
    fontWeight: '500',
  },
  statusBarTextSuccess: {
    color: '#2E7D32',
  },
  statusBarTextError: {
    color: '#C62828',
  },
  statusBarTextInfo: {
    color: '#1565C0',
  },
  diskInfoContent: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  diskInfoText: {
    fontSize: 16,
    color: '#1C1B1F',
    marginBottom: 8,
  },
  fileTreeContainer: {
    marginTop: 16,
  },
  fileTreeTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 8,
  },
  fileTreeItem: {
    fontSize: 14,
    color: '#49454F',
    marginBottom: 4,
    paddingLeft: 8,
  },
  addFormContainer: {
    backgroundColor: '#FFFFFF',
    padding: 16,
    borderRadius: 8,
  },
  addFormTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1C1B1F',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E6E0E9',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#FFFFFF',
    minHeight: 44,
    justifyContent: 'center',
  },
  inputText: {
    fontSize: 16,
    color: '#1C1B1F',
  },
  analyzingText: {
    fontSize: 12,
    color: '#6750A4',
    marginTop: 4,
    fontStyle: 'italic',
  },
  addFormButton: {
    backgroundColor: '#6750A4',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    elevation: 1,
    marginTop: 20,
  },
  addFormButtonDisabled: {
    backgroundColor: '#9E9E9E',
  },
  addFormButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  addFormHint: {
    fontSize: 12,
    color: '#79747E',
    textAlign: 'center',
    marginTop: 12,
    fontStyle: 'italic',
  },
});

export default App; 