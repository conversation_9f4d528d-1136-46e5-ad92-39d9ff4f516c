# 分片上传测试功能说明

## 概述

`test-chunk-upload.js` 是一个全面的分片上传测试工具，可以测试文件的分片上传功能。该工具支持两种模式：

1. **真实分片上传** - 当后端分片上传API可用时使用
2. **模拟分片上传** - 当分片上传API不可用时，使用常规上传API进行模拟测试

## 功能特性

### ✅ 自动检测功能可用性
- 自动检测分片上传API是否可用
- 根据可用性选择合适的测试模式
- 提供清晰的状态反馈

### ✅ 完整的测试流程
- 创建不同大小的测试文件
- 计算分片信息（分片数量、大小等）
- 执行上传操作
- 验证上传结果
- 自动清理测试文件

### ✅ 详细的进度显示
- 实时显示上传进度
- 分片级别的状态反馈
- 详细的错误信息

### ✅ 多种测试场景
- 小文件测试（0.5MB - 单分片）
- 中等文件测试（2.5MB - 多分片）
- 大文件测试（5MB - 多分片）

## 使用方法

### 直接运行测试
```bash
node test-chunk-upload.js
```

### 作为模块使用
```javascript
const ChunkUploadTester = require('./test-chunk-upload');

const tester = new ChunkUploadTester();
tester.runAllTests();
```

## 测试输出示例

### 当分片上传功能可用时
```
🚀 开始运行分片上传测试套件

🔍 检查分片上传功能可用性...
✅ 分片上传功能可用

🧪 开始真实分片上传测试: test-small-chunk.txt (0.5MB)

📄 创建测试文件: test-small-chunk.txt (0.5MB)
🚀 初始化分片上传: test-small-chunk.txt
✅ 分片上传初始化成功:
   任务ID: 12345
   文件名: test-small-chunk.txt
   文件大小: 524288 bytes
   分片大小: 1048576 bytes
   总分片数: 1
   ...
```

### 当分片上传功能不可用时
```
🚀 开始运行分片上传测试套件

🔍 检查分片上传功能可用性...
⚠️  分片上传功能暂不可用，将使用模拟测试

🔄 模拟分片上传测试: test-small-chunk.txt (0.5MB)
📝 说明: 由于分片上传功能暂不可用，将使用常规上传API进行模拟测试

📄 创建测试文件: test-small-chunk.txt (0.5MB)
📊 模拟分片信息:
   文件大小: 524288 bytes
   分片大小: 1048576 bytes
   总分片数: 1
   ...
```

## 测试配置

### 可配置参数
```javascript
const API_BASE_URL = 'https://local.luzeshu.cn/api';
const BULLET_HEADER = '36fdf066-9e42-11ec-b41e-525400043ced';
const CHUNK_SIZE = 1024 * 1024; // 1MB chunks
```

### 测试用例
```javascript
const testCases = [
  { fileName: 'test-small-chunk.txt', sizeInMB: 0.5 },   // 小于1MB，测试单分片
  { fileName: 'test-medium-chunk.txt', sizeInMB: 2.5 },  // 多分片测试
  { fileName: 'test-large-chunk.txt', sizeInMB: 5 },     // 大文件测试
];
```

## API 接口

### 分片上传相关接口

#### 1. 初始化分片上传
```http
POST /api/drive/init-chunk-upload
Content-Type: application/json
Headers: bullet: 36fdf066-9e42-11ec-b41e-525400043ced

{
  "file_name": "example.txt",
  "file_size": 1024000,
  "chunk_size": 1048576,
  "parent_id": null
}
```

#### 2. 上传分片
```http
POST /api/drive/upload-chunk
Content-Type: multipart/form-data
Headers: bullet: 36fdf066-9e42-11ec-b41e-525400043ced

task_id: 12345
chunk_index: 0
chunk_data: [binary data]
```

#### 3. 完成分片上传
```http
POST /api/drive/complete-chunk-upload
Content-Type: application/json
Headers: bullet: 36fdf066-9e42-11ec-b41e-525400043ced

{
  "task_id": "12345"
}
```

## 错误处理

### 常见错误场景
1. **网络连接错误** - 自动重试机制
2. **分片上传失败** - 详细错误信息
3. **文件验证失败** - 完整性检查
4. **API不可用** - 自动降级到模拟模式

### 错误信息示例
```
❌ 初始化分片上传失败: Request failed with status code 404
   状态码: 404
   响应数据: 404 page not found
```

## 技术实现

### 核心类：ChunkUploadTester
- `checkChunkUploadAvailability()` - 检查功能可用性
- `initChunkUpload()` - 初始化分片上传
- `uploadChunk()` - 上传单个分片
- `completeChunkUpload()` - 完成分片上传
- `simulateChunkUpload()` - 模拟分片上传
- `verifyUploadedFile()` - 验证上传结果

### 文件处理
- 动态创建测试文件
- 自动计算分片信息
- 自动清理临时文件

### 进度跟踪
- 实时显示上传进度
- 分片级别的状态更新
- 完整的测试报告

## 部署说明

### 当分片上传功能部署后
1. 确保后端服务正常运行
2. 检查数据库表 `drive_upload_tasks` 是否存在
3. 验证API路由是否正确配置
4. 运行测试验证功能

### 部署检查清单
- [ ] drive-service 服务运行正常
- [ ] gateway 服务运行正常
- [ ] 数据库连接正常
- [ ] API路由配置正确
- [ ] 存储目录权限正确

## 故障排除

### 分片上传返回404
```bash
# 检查服务状态
docker ps | grep -E "(drive-service|gateway)"

# 检查日志
docker logs drive-service
docker logs gateway
```

### 文件上传失败
```bash
# 检查存储目录权限
ls -la /store_root/

# 检查磁盘空间
df -h
```

### 数据库连接问题
```bash
# 检查数据库连接
mysql -u root -p -e "SHOW TABLES;" your_database
```

## 未来扩展

### 计划功能
1. **断点续传** - 支持上传中断后继续
2. **并发上传** - 多分片并行上传
3. **压缩上传** - 分片数据压缩
4. **校验和验证** - 分片完整性检查
5. **上传速度控制** - 限制上传速度

### 性能优化
1. **分片大小优化** - 根据文件大小动态调整
2. **内存使用优化** - 流式处理大文件
3. **网络优化** - 连接池和重试机制
4. **存储优化** - 临时文件管理

## 总结

该测试工具提供了完整的分片上传功能测试，支持自动降级和详细的错误处理。无论分片上传功能是否可用，都能提供有价值的测试结果，为功能开发和部署提供可靠的验证手段。

---

**最后更新**: 2025-07-09  
**版本**: 1.0.0  
**作者**: AI Assistant 