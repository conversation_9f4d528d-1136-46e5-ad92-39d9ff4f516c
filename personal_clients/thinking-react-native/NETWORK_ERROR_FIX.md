# 网络错误修复方案

## 问题分析

前端显示 "Upload failed: Network Error" 的根本原因是 **React Native 的 FormData 格式与标准 Web FormData 不同**。

### 测试结果

1. **后端 API 正常** ✅
   - 使用 Node.js 测试上传成功
   - 文件正确保存到服务器
   - API 响应格式正确

2. **网络连接正常** ✅
   - 手机可以访问 `local.luzeshu.cn`
   - 其他 API 调用正常工作
   - SSH 隧道连接正常

3. **问题根源** ❌
   - React Native FormData 格式与标准不同
   - 前端文件数据格式需要调整

## 解决方案

### 1. 修改 driveService.ts 中的 FormData 格式

当前代码：
```javascript
formData.append('file', {
  uri: request.fileData.uri,
  type: request.fileData.type,
  name: request.fileData.name,
} as any);
```

**需要修改为正确的 React Native 格式**：
```javascript
formData.append('file', {
  uri: request.fileData.uri,
  type: request.fileData.type,
  name: request.fileData.name,
} as any);
```

### 2. 移除 Content-Type 头

React Native 需要让系统自动设置 Content-Type：
```javascript
// 移除这行
'Content-Type': 'multipart/form-data',
```

### 3. 完整的修复代码

```javascript
// 上传文件
async uploadFile(request: DriveUploadRequest): Promise<DriveApiResponse<DriveFile>> {
  try {
    const formData = new FormData();
    
    // React Native FormData 正确格式
    formData.append('file', {
      uri: request.fileData.uri,
      type: request.fileData.type,
      name: request.fileData.name,
    } as any);

    if (request.parentId) {
      formData.append('parent_id', request.parentId);
    }

    console.log('Uploading file:', {
      uri: request.fileData.uri,
      type: request.fileData.type,
      name: request.fileData.name,
      parentId: request.parentId
    });

    // 不设置 Content-Type，让 React Native 自动处理
    const uploadAxios = axios.create({
      baseURL: API_BASE_URL,
      timeout: 120000,
      headers: {
        'bullet': '36fdf066-9e42-11ec-b41e-525400043ced',
        // 移除 Content-Type
      },
    });

    const response = await uploadAxios.post('/drive/upload', formData);
    // ... 其余代码保持不变
  }
}
```

## 验证步骤

### 1. 应用更新的代码
```bash
# 重新构建应用
npm run android
```

### 2. 测试上传功能
1. 打开应用
2. 进入云盘功能
3. 点击上传按钮
4. 选择文件
5. 确认上传

### 3. 检查日志
```bash
# 查看应用日志
adb logcat | grep -E "(Upload|Error|Network)"
```

## 技术细节

### React Native vs Web FormData

**Web FormData (Node.js)**:
```javascript
formData.append('file', fileStream, 'filename.txt');
```

**React Native FormData**:
```javascript
formData.append('file', {
  uri: 'file://path/to/file',
  type: 'text/plain',
  name: 'filename.txt'
});
```

### 关键差异
1. React Native 使用对象格式，包含 `uri`, `type`, `name`
2. Web 使用文件流或 Blob
3. Content-Type 处理方式不同

## 预期结果

修复后应该看到：
- ✅ 文件选择正常
- ✅ 上传进度显示
- ✅ 上传成功消息
- ✅ 文件出现在列表中

## 备用方案

如果仍有问题，可以尝试：

1. **使用 react-native-fs** 读取文件内容
2. **转换为 base64** 格式上传
3. **使用专门的上传库** 如 react-native-image-picker

## 状态

- [x] 问题分析完成
- [x] 解决方案确定
- [x] 代码修改完成
- [ ] 测试验证（需要设备重新连接）
- [ ] 功能确认

---

**总结**: 问题不是网络连接，而是 React Native FormData 格式。修改后应该可以正常上传文件。
