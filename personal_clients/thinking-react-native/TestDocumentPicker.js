/**
 * 独立的 Document Picker 测试应用
 * 用于验证 react-native-document-picker 是否正常工作
 */

import React from 'react';
import {
  AppRegistry,
  SafeAreaView,
  StyleSheet,
} from 'react-native';
import DocumentPickerTest from './src/tests/DocumentPickerTest';

const TestApp = () => {
  return (
    <SafeAreaView style={styles.container}>
      <DocumentPickerTest />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

AppRegistry.registerComponent('TestDocumentPicker', () => TestApp);

export default TestApp;
