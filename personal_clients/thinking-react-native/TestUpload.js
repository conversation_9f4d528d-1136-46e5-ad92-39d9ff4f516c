/**
 * 独立的上传测试应用
 * 用于验证文件上传功能是否正常工作
 */

import React from 'react';
import {
  AppRegistry,
  SafeAreaView,
  StyleSheet,
} from 'react-native';
import DriveUploadTest from './src/tests/DriveUploadTest';

const TestUploadApp = () => {
  return (
    <SafeAreaView style={styles.container}>
      <DriveUploadTest />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

AppRegistry.registerComponent('TestUpload', () => TestUploadApp);

export default TestUploadApp;
