package com.thinking

import android.content.Intent
import com.facebook.react.bridge.ReactApplicationContext
import com.facebook.react.bridge.ReactContextBaseJavaModule
import com.facebook.react.bridge.ReactMethod
import com.facebook.react.bridge.Promise
import com.facebook.react.bridge.WritableMap
import com.facebook.react.bridge.Arguments

class ShareIntentModule(reactContext: ReactApplicationContext) : ReactContextBaseJavaModule(reactContext) {
    
    private var shareData: String? = null
    
    override fun getName(): String {
        return "ShareIntentModule"
    }
    
    @ReactMethod
    fun getInitialShare(promise: Promise) {
        try {
            val activity = currentActivity
            if (activity != null) {
                val intent = activity.intent
                val action = intent.action
                val type = intent.type
                
                if (Intent.ACTION_SEND == action && type != null) {
                    if ("text/plain" == type) {
                        val sharedText = intent.getStringExtra(Intent.EXTRA_TEXT)
                        if (sharedText != null) {
                            val result = Arguments.createMap()
                            result.putString("text", sharedText)
                            result.putString("type", "text")
                            promise.resolve(result)
                            return
                        }
                    }
                }
            }
            promise.resolve(null)
        } catch (e: Exception) {
            promise.reject("GET_INITIAL_SHARE_ERROR", e.message)
        }
    }
    
    @ReactMethod
    fun clearShare(promise: Promise) {
        try {
            val activity = currentActivity
            if (activity != null) {
                // 清除Intent，防止重复处理
                activity.intent.action = Intent.ACTION_MAIN
                activity.intent.removeExtra(Intent.EXTRA_TEXT)
            }
            promise.resolve(true)
        } catch (e: Exception) {
            promise.reject("CLEAR_SHARE_ERROR", e.message)
        }
    }
} 