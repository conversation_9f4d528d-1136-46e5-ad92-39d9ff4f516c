// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 21
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "23.1.7779620"
        kotlinVersion = "1.8.0"
    }
    repositories {
        google()
        mavenCentral()
        maven { url "https://www.jitpack.io" }
    }
    dependencies {
        classpath("com.android.tools.build:gradle:8.1.1")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://www.jitpack.io" }
        // Add for React Native Vector Icons
        maven {
            url "https://maven.google.com"
        }
    }

    // Enable buildConfig for all subprojects to fix react-native-vector-icons issue
    afterEvaluate { project ->
        if (project.hasProperty("android")) {
            android {
                buildFeatures {
                    buildConfig true
                }

                // Ensure consistent Java version across all modules
                compileOptions {
                    sourceCompatibility JavaVersion.VERSION_17
                    targetCompatibility JavaVersion.VERSION_17
                }

                // Set Kotlin JVM target to match Java version
                tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
                    kotlinOptions {
                        jvmTarget = "17"
                    }
                }

                // Force Java toolchain to use Java 17 for all projects
                java {
                    toolchain {
                        languageVersion = JavaLanguageVersion.of(17)
                    }
                }
            }
        }

        // Also apply to tasks directly
        project.tasks.withType(JavaCompile).configureEach {
            sourceCompatibility = JavaVersion.VERSION_17
            targetCompatibility = JavaVersion.VERSION_17
        }

        project.tasks.withType(org.jetbrains.kotlin.gradle.tasks.KotlinCompile).configureEach {
            kotlinOptions {
                jvmTarget = "17"
            }
        }
    }
}