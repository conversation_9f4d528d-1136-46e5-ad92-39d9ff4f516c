/**
 * 网络调试脚本 - 详细检查网络连接问题
 */

const axios = require('axios');

const API_BASE_URL = 'https://local.luzeshu.cn/api';
const BULLET_HEADER = '36fdf066-9e42-11ec-b41e-525400043ced';

async function testNetworkConnectivity() {
  console.log('🔍 开始网络连接调试...\n');

  // 1. 测试基础连接
  console.log('1. 测试基础 HTTPS 连接...');
  try {
    const response = await axios.get('https://local.luzeshu.cn', {
      timeout: 10000,
      validateStatus: () => true, // 接受所有状态码
    });
    console.log(`✅ 基础连接成功 - 状态码: ${response.status}`);
  } catch (error) {
    console.log(`❌ 基础连接失败: ${error.message}`);
    console.log(`   错误代码: ${error.code}`);
    return;
  }

  // 2. 测试 API 端点
  console.log('\n2. 测试 API 端点连接...');
  try {
    const response = await axios.get(`${API_BASE_URL}/drive/storage/info`, {
      headers: {
        'bullet': BULLET_HEADER,
      },
      timeout: 10000,
    });
    console.log(`✅ API 连接成功 - 状态码: ${response.status}`);
    console.log(`   响应数据: ${JSON.stringify(response.data).substring(0, 100)}...`);
  } catch (error) {
    console.log(`❌ API 连接失败: ${error.message}`);
    if (error.response) {
      console.log(`   状态码: ${error.response.status}`);
      console.log(`   响应数据: ${JSON.stringify(error.response.data)}`);
    }
    return;
  }

  // 3. 测试上传端点可达性
  console.log('\n3. 测试上传端点可达性...');
  try {
    // 发送一个空的 POST 请求来测试端点
    const response = await axios.post(`${API_BASE_URL}/drive/upload`, {}, {
      headers: {
        'bullet': BULLET_HEADER,
        'Content-Type': 'application/json',
      },
      timeout: 10000,
      validateStatus: () => true, // 接受所有状态码
    });
    console.log(`✅ 上传端点可达 - 状态码: ${response.status}`);
    console.log(`   响应数据: ${JSON.stringify(response.data)}`);
  } catch (error) {
    console.log(`❌ 上传端点不可达: ${error.message}`);
    if (error.response) {
      console.log(`   状态码: ${error.response.status}`);
      console.log(`   响应数据: ${JSON.stringify(error.response.data)}`);
    }
  }

  // 4. 测试 FormData 上传
  console.log('\n4. 测试 FormData 上传...');
  try {
    const FormData = require('form-data');
    const formData = new FormData();
    formData.append('test', 'test-value');
    
    const response = await axios.post(`${API_BASE_URL}/drive/upload`, formData, {
      headers: {
        ...formData.getHeaders(),
        'bullet': BULLET_HEADER,
      },
      timeout: 30000,
      validateStatus: () => true,
    });
    console.log(`✅ FormData 请求发送成功 - 状态码: ${response.status}`);
    console.log(`   响应数据: ${JSON.stringify(response.data)}`);
  } catch (error) {
    console.log(`❌ FormData 上传失败: ${error.message}`);
    if (error.response) {
      console.log(`   状态码: ${error.response.status}`);
      console.log(`   响应数据: ${JSON.stringify(error.response.data)}`);
    }
  }

  // 5. 检查 DNS 解析
  console.log('\n5. 检查 DNS 解析...');
  try {
    const dns = require('dns').promises;
    const addresses = await dns.lookup('local.luzeshu.cn');
    console.log(`✅ DNS 解析成功: ${addresses.address}`);
  } catch (error) {
    console.log(`❌ DNS 解析失败: ${error.message}`);
  }

  console.log('\n📋 调试总结:');
  console.log('如果所有测试都通过，问题可能在于:');
  console.log('1. React Native FormData 格式问题');
  console.log('2. 文件 URI 格式问题');
  console.log('3. 移动设备网络配置问题');
  console.log('4. SSL 证书问题');
}

async function testMobileNetworkIssues() {
  console.log('\n🔧 移动设备网络问题排查建议:\n');
  
  console.log('1. 检查设备网络连接:');
  console.log('   adb shell ping -c 3 local.luzeshu.cn');
  console.log('');
  
  console.log('2. 检查设备 DNS 设置:');
  console.log('   adb shell nslookup local.luzeshu.cn');
  console.log('');
  
  console.log('3. 检查应用网络权限:');
  console.log('   确保 AndroidManifest.xml 包含 INTERNET 权限');
  console.log('');
  
  console.log('4. 检查网络安全配置:');
  console.log('   确保 network_security_config.xml 允许 HTTPS 连接');
  console.log('');
  
  console.log('5. 查看应用日志:');
  console.log('   adb logcat | grep -E "(Network|HTTP|SSL|thinking)"');
  console.log('');
  
  console.log('6. 测试其他 HTTPS 网站:');
  console.log('   在应用中尝试访问 https://httpbin.org/get');
}

// 运行测试
async function runDebug() {
  await testNetworkConnectivity();
  await testMobileNetworkIssues();
}

runDebug().catch(console.error);
