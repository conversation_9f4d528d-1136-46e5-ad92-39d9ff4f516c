# 光标高度不一致问题修复

## 问题描述
在React Native的TextInput组件中，光标在行首和行中位置会出现高度不一致的问题，表现为：
- 光标在行首时可能变短或变长
- 光标高度随机性变化
- 视觉体验不佳

## 问题原因
1. **字体渲染机制**：React Native在渲染字体时会添加额外的padding
2. **includeFontPadding**：Android默认会包含字体的内部padding
3. **lineHeight与fontSize比例**：不合适的比例会导致光标高度计算错误
4. **textAlignVertical**：垂直对齐方式影响光标位置

## 解决方案

### 1. 关键样式设置
```javascript
contentInput: {
  fontSize: 16,           // 减小字体大小
  lineHeight: 28,         // 调整行高比例 (1.75倍)
  includeFontPadding: false, // 关键：移除字体内部padding
  textAlignVertical: 'top',  // 顶部对齐
  letterSpacing: 0,       // 字符间距
  margin: 0,              // 重置边距
}
```

### 2. TextInput组件属性
```jsx
<TextInput
  style={styles.contentInput}
  multiline
  textAlignVertical="top"
  scrollEnabled={false}           // 禁用滚动
  blurOnSubmit={false}           // 防止提交时失焦
  returnKeyType="default"        // 默认回车键
  enablesReturnKeyAutomatically={false}
/>
```

### 3. 背景横线同步调整
```jsx
<NotePaperBackground height={600} lineHeight={28} />
```

## 技术细节

### includeFontPadding的作用
- `true`（默认）：包含字体的ascender和descender空间
- `false`：移除额外的字体padding，让光标高度更一致

### fontSize与lineHeight的黄金比例
- 推荐比例：1.5 - 1.8倍
- 当前设置：16px字体 + 28px行高 = 1.75倍
- 这个比例在中文字体下表现最佳

### 字符间距优化
- `letterSpacing: 0`：确保字符间距一致
- 配合全角空格替换，保持排版整齐

## 测试验证

### 测试场景
1. 在行首输入文字
2. 在行中插入文字
3. 换行后的光标位置
4. 长文本的光标表现

### 预期效果
- ✅ 光标高度在所有位置保持一致
- ✅ 光标与文字基线对齐
- ✅ 视觉体验流畅自然

## 兼容性说明

### Android
- `includeFontPadding: false` 是关键设置
- 需要配合 `textAlignVertical: 'top'`

### iOS
- iOS对字体渲染处理较好，但同样受益于这些设置
- 保持跨平台一致性

## 进一步优化建议

1. **字体加载优化**：确保霞鹜文楷字体正确加载
2. **性能监控**：观察长文本下的渲染性能
3. **用户反馈**：收集真实设备上的使用体验
4. **A/B测试**：对比不同fontSize和lineHeight组合

## 相关文件
- `src/screens/notes/NotesAddScreen.tsx` - 主要修改文件
- `src/components/NotePaperBackground.tsx` - 背景组件调整

## 注意事项
- 修改后需要重新构建应用
- 建议在多种设备上测试效果
- 关注不同Android版本的兼容性
