# React Native 调试指南

## 快速调试命令

### 1. 详细模式运行应用
```bash
# 使用 npm 脚本
npm run android:verbose

# 或直接使用 npx
npx react-native run-android --verbose
```

### 2. 查看实时日志
```bash
# 只看 React Native 相关日志
npm run logcat

# 查看所有设备日志
npm run logcat:all

# 或直接使用 adb
adb logcat | grep -E "(ReactNative|JS|Bundle|Error|Exception|com.thinkingandroid)"
```

### 3. 查看设备信息
```bash
npm run devices
```

### 4. 启动 Metro 服务器（详细模式）
```bash
npm run start:verbose
```

### 5. 清理和重新构建
```bash
# 清理所有缓存
npm run clean:all

# 只清理 Android
npm run clean:android

# 重置 Metro 缓存
npm run start:reset
```

### 6. 构建详细日志
```bash
# 查看 Gradle 构建详情
npm run build:android

# 更详细的构建日志
npm run build:android:verbose
```

## 使用调试脚本

运行交互式调试脚本：
```bash
npm run debug
```

这个脚本会提供以下选项：
1. 详细模式运行 Android
2. 查看 React Native 日志
3. 查看所有设备日志
4. 查看 Metro 错误日志  
5. 查看 Gradle 构建日志
6. 清理并重新构建
7. 查看连接的设备

## 常见错误类型和解决方法

### 1. Bundle 加载错误
```bash
# 检查 Metro 服务器状态
npm run start:verbose

# 检查设备连接
npm run devices

# 查看网络连接日志
npm run logcat
```

### 2. 构建错误
```bash
# 详细构建日志
npm run build:android:verbose

# 清理重建
npm run clean:all
```

### 3. 依赖问题
```bash
# 清理 node_modules 和重新安装
npm run clean:all

# 检查 Metro 缓存
npm run start:reset
```

### 4. 设备连接问题
```bash
# 检查连接的设备
npm run devices

# 设置端口转发
adb reverse tcp:8081 tcp:8081

# 检查网络日志
npm run logcat
```

## 日志输出到文件

如果需要保存日志到文件进行分析：

```bash
# 保存构建日志
npm run build:android:verbose > android-build.log 2>&1

# 保存运行日志
npm run android:verbose > android-run.log 2>&1

# 保存设备日志
npm run logcat > device.log

# 保存 Metro 日志
npm run start:verbose > metro.log 2>&1
```

## 网络调试

如果遇到网络连接问题（如 Metro 服务器连接）：

```bash
# 检查网络配置
adb shell getprop | grep -E "(wifi|net)"

# 检查端口转发
adb reverse --list

# 重新设置端口转发
adb reverse tcp:8081 tcp:8081

# 检查防火墙设置
sudo ufw status
```

## 性能调试

```bash
# 启用性能监控
adb shell input keyevent 82  # 打开开发者菜单
# 然后选择 "Perf Monitor"

# 查看内存使用
adb shell dumpsys meminfo com.thinkingandroid

# 查看 CPU 使用
adb shell top | grep com.thinkingandroid
```

## 提示
- 始终使用 `--verbose` 标志获取详细信息
- 保存错误日志到文件便于分析
- 网络问题时检查 IP 地址和端口转发
- 定期清理缓存避免奇怪的错误 