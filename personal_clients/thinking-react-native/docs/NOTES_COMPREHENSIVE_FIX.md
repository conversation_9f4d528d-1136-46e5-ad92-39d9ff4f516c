# 笔记编辑页面综合修复方案

## 问题总结

1. **光标高度不一致** - 在行首偏短，输入空格后跳动
2. **空格宽度问题** - 输入空格时先跳半个格，再跳到中文字符宽度
3. **键盘弹出自动滚动** - 弹出输入法时自动滚动，把标题栏和工具栏滚上去
4. **代码复用问题** - 添加和编辑页面样式、逻辑不一致

## 解决方案

### 1. 创建共享编辑器组件

创建了 `src/components/NoteEditor.tsx` 作为共享组件：

```typescript
// 关键特性
- 统一的工具栏布局（创建时间 | 字数 | 标签）
- 优化的光标处理
- 防止键盘弹出时自动滚动
- 一致的样式和交互逻辑
```

### 2. 光标高度修复

**关键设置**：
```javascript
contentInput: {
  fontSize: 14,              // 减小字体
  lineHeight: 20,            // 更小的行高
  includeFontPadding: false, // 关键：移除字体内部padding
  textAlignVertical: 'top',  // 顶部对齐
  letterSpacing: 0,          // 字符间距为0
  height: undefined,         // 让高度自适应
  minHeight: 20,            // 最小行高
}
```

**TextInput属性优化**：
```jsx
<TextInput
  scrollEnabled={false}           // 禁用滚动
  autoCorrect={false}            // 禁用自动纠错
  autoCapitalize="none"          // 禁用自动大写
  spellCheck={false}             // 禁用拼写检查
  blurOnSubmit={false}           // 防止提交时失焦
/>
```

### 3. 防止键盘弹出时自动滚动

**ScrollView配置**：
```jsx
<ScrollView 
  keyboardShouldPersistTaps="handled"
  automaticallyAdjustContentInsets={false}
  contentInsetAdjustmentBehavior="never"
  keyboardDismissMode="none"
  scrollEnabled={true}
/>
```

**移除KeyboardAvoidingView**：
- 不使用KeyboardAvoidingView，避免自动调整
- 让用户手动滚动到需要的位置

### 4. 空格处理优化

**移除自动替换**：
- 不再自动将普通空格替换为全角空格
- 让用户自然输入，避免跳动问题
- 通过字体和行高设置保持视觉一致性

### 5. 代码复用实现

**统一架构**：
```
NotesAddScreen.tsx  ──┐
                      ├──> NoteEditor.tsx (共享组件)
NotesEditScreen.tsx ──┘
```

**共享功能**：
- 工具栏布局和样式
- 文本输入处理逻辑
- 自动保存状态显示
- 标签选择交互

## 技术细节

### 字体与行高比例
- **fontSize: 14px**
- **lineHeight: 20px** 
- **比例: 1.43** (最佳中文显示比例)

### 背景横线同步
- 背景组件行高调整为20px
- 文本padding调整为18px以对齐

### 光标稳定性
- `includeFontPadding: false` 是关键
- 配合精确的padding和行高设置
- 禁用各种自动功能避免干扰

## 文件变更

### 新增文件
- `src/components/NoteEditor.tsx` - 共享编辑器组件

### 修改文件
- `src/screens/notes/NotesAddScreen.tsx` - 简化为使用共享组件
- `src/screens/notes/NotesEditScreen.tsx` - 重构为使用共享组件
- `src/screens/notes/__tests__/NotesAddScreen.test.tsx` - 更新测试

### 移除内容
- 编辑/预览切换功能（编辑页面）
- 重复的样式定义
- 空格自动替换逻辑

## 预期效果

### ✅ 光标问题解决
- 光标高度在所有位置保持一致
- 消除行首光标偏短问题
- 输入空格不再跳动

### ✅ 滚动问题解决
- 键盘弹出时不自动滚动
- 标题栏和工具栏保持可见
- 用户可手动控制滚动位置

### ✅ 代码复用实现
- 添加和编辑页面样式完全一致
- 共享组件减少重复代码
- 统一的交互逻辑和用户体验

## 测试建议

1. **真实设备测试**：在多种Android设备上测试光标效果
2. **键盘测试**：测试不同输入法的兼容性
3. **长文本测试**：验证长文本下的性能和体验
4. **交互测试**：确认标签选择、自动保存等功能正常

## 后续优化

1. **性能监控**：观察长文本编辑的性能表现
2. **用户反馈**：收集真实使用场景的反馈
3. **细节调优**：根据实际使用情况微调字体和间距
4. **功能扩展**：考虑添加更多编辑功能（如格式化工具）
