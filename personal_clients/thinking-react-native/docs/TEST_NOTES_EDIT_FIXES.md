# 笔记编辑页面修复测试

## 修复的问题

1. **状态丢失问题**：从编辑页面跳转到设置页面再返回时内容丢失
2. **未保存内容提示问题**：有未保存内容时退出页面没有提示确认

## 测试场景

### 场景1：编辑页面 -> 设置页面 -> 返回编辑页面
**测试步骤：**
1. 进入笔记列表
2. 点击任意笔记进入编辑页面
3. 修改笔记内容（标题或正文）
4. 点击右上角设置按钮进入设置页面
5. 点击返回按钮回到编辑页面
6. 检查修改的内容是否还在

**预期结果：**
- 修改的内容应该保持不变
- 编辑状态应该正常
- 自动保存功能应该正常工作

### 场景2：有未保存内容时通过返回按钮退出
**测试步骤：**
1. 进入笔记列表
2. 点击任意笔记进入编辑页面
3. 修改笔记内容但不等待自动保存（2秒内）
4. 立即点击返回按钮

**预期结果：**
- 应该弹出确认对话框："您有未保存的更改，确定要返回吗？"
- 点击"继续编辑"应该留在编辑页面
- 点击"返回"应该退出到列表页面

### 场景3：有未保存内容时通过硬件返回键退出
**测试步骤：**
1. 进入笔记列表
2. 点击任意笔记进入编辑页面
3. 修改笔记内容但不等待自动保存（2秒内）
4. 按设备的返回键

**预期结果：**
- 应该弹出确认对话框："您有未保存的更改，确定要返回吗？"
- 点击"继续编辑"应该留在编辑页面
- 点击"返回"应该退出到列表页面

### 场景4：有未保存内容时通过设置页面间接退出
**测试步骤：**
1. 进入笔记列表
2. 点击任意笔记进入编辑页面
3. 修改笔记内容但不等待自动保存（2秒内）
4. 点击设置按钮进入设置页面
5. 在设置页面点击返回按钮

**预期结果：**
- 应该直接返回到编辑页面（因为设置页面现在是覆盖层）
- 修改的内容应该保持不变

### 场景5：没有未保存内容时正常退出
**测试步骤：**
1. 进入笔记列表
2. 点击任意笔记进入编辑页面
3. 修改笔记内容并等待自动保存完成（看到"上次保存"时间更新）
4. 点击返回按钮

**预期结果：**
- 应该直接退出到列表页面，不显示确认对话框

## 技术实现要点

### 1. 设置页面覆盖层实现
- 设置页面现在作为覆盖层显示，不会销毁编辑页面组件
- 使用 `position: 'absolute'` 和 `zIndex: 1000` 实现覆盖效果

### 2. 未保存内容检查机制
- 使用 `forwardRef` 和 `useImperativeHandle` 暴露 `checkBeforeExit` 方法
- 在所有退出路径中调用此方法进行检查
- 支持异步确认对话框

### 3. 状态保持
- 编辑页面组件在设置页面显示时不会被卸载
- 所有编辑状态（标题、内容、标签、未保存标记）都会保持

## 注意事项

1. 自动保存延迟为2秒，测试时需要在2秒内操作才能触发未保存状态
2. 设置页面的覆盖层样式可能需要根据实际UI需求调整
3. BackHandler 不支持异步操作，所以使用 Promise.then() 处理异步确认
