# 笔记编辑系统技术文档

## 概述

本文档详细描述了重构后的笔记编辑系统的技术架构、组件设计、数据流程和关键技术细节。重构的主要目标是：
1. 统一普通笔记和时间线笔记的处理逻辑
2. 消除NotesAddScreen和NotesEditScreen的差异
3. 解决时间线笔记编辑后类型转换的问题
4. 提高代码复用性和可维护性

## 系统架构

### 组件层次结构

```
NotesScreen (主容器)
├── NotesAddScreen (添加笔记 - 统一逻辑)
│   ├── NoteTypeSelector (类型选择)
│   └── NoteEditorContainer ✨ (统一编辑器容器)
│       ├── NoteEditor (普通笔记编辑器)
│       └── TimelineNoteEditor (时间线笔记编辑器)
└── NotesEditScreen (编辑笔记 - 统一逻辑)
    └── NoteEditorContainer ✨ (统一编辑器容器)
        ├── NoteEditor (普通笔记编辑器)
        └── TimelineNoteEditor (时间线笔记编辑器)
```

### 核心组件职责划分

#### NoteEditorContainer.tsx ✨ (统一编辑器容器)
**职责：**
- 根据笔记类型自动选择合适的编辑器组件
- 统一的数据接口和状态管理
- 两种笔记类型的自动保存机制
- 统一的退出检查和数据验证
- 时间线数据的序列化和反序列化

**接口设计：**
```typescript
interface NoteEditorContainerProps {
  // 笔记数据
  noteType: NoteType;
  initialTitle?: string;
  initialContent?: string;
  initialTags?: string[];
  initialTimelineEntries?: TimelineEntry[];
  createdTime?: Date;

  // 保存函数 - 统一接口
  onSave: (data: NoteData) => Promise<{success: boolean, message?: string}>;

  // 事件回调
  onBack?: () => void;
  onDataChange?: (hasChanges: boolean) => void;

  // 配置选项
  autoSaveEnabled?: boolean;
  autoSaveDelay?: number;
}

interface NoteData {
  title: string;
  content: string;
  tags: string[];
  noteType: NoteType;
  timelineEntries?: TimelineEntry[];
}

interface NoteEditorContainerRef {
  getCurrentContent: () => NoteData;
  checkBeforeExit: () => Promise<boolean>;
  saveNow: () => Promise<boolean>;
}
```

#### NoteEditor.tsx (普通笔记编辑器)
**职责：**
- 普通笔记的内部状态管理
- 自动保存机制 (防抖、错误处理)
- 表单输入处理 (实时同步、验证)
- 退出检查逻辑 (未保存更改提醒)
- UI 渲染 (标题输入、内容编辑、标签选择、状态显示)

#### TimelineNoteEditor.tsx (时间线笔记编辑器)
**职责：**
- 时间线条目的管理和编辑
- 时间戳自动生成
- 条目的增删改操作
- 时间线特有的UI渲染

#### NotesAddScreen.tsx (统一添加屏幕)
**职责：**
- 笔记类型选择管理
- 新笔记创建逻辑 (ID 生成、首次保存)
- 导航控制 (类型选择 ↔ 编辑页面)
- 为 NoteEditorContainer 提供保存函数

#### NotesEditScreen.tsx (统一编辑屏幕)
**职责：**
- 现有笔记数据加载和类型识别
- 笔记更新逻辑 (支持两种类型)
- 导航控制
- 为 NoteEditorContainer 提供更新函数

## 数据流程

### 1. 初始化流程

#### 添加笔记流程
```
用户点击添加 → NotesAddScreen
├── 显示 NoteTypeSelector
├── 用户选择类型 → 隐藏选择器
└── 渲染 NoteEditorContainer
    ├── noteType='normal' → 内部渲染 NoteEditor
    └── noteType='timeline' → 内部渲染 TimelineNoteEditor
```

#### 编辑笔记流程
```
用户点击编辑 → NotesEditScreen
├── 加载笔记数据 (loadNote/loadNoteData)
├── 解析笔记类型和时间线数据
├── 设置 noteData 状态
└── 渲染 NoteEditorContainer (预填充数据)
    ├── noteType='normal' → 内部渲染 NoteEditor
    └── noteType='timeline' → 内部渲染 TimelineNoteEditor + 解析条目
```

### 2. 编辑流程

#### 普通笔记编辑
```
用户输入 → NoteEditor.handleTitleChange/handleContentChange
├── 立即更新 latestRef (避免状态延迟)
├── 更新组件状态 (触发重渲染)
├── 标记有未保存更改
├── 触发自动保存 (防抖)
└── 通知 NoteEditorContainer → 通知父组件
```

#### 时间线笔记编辑
```
用户操作 → TimelineNoteEditor.handleAddEntry/handleUpdateEntry/handleDeleteEntry
├── 更新 timelineEntries 状态
├── 标记有未保存更改
├── 触发 NoteEditorContainer.triggerTimelineAutoSave
├── 序列化时间线数据为 JSON
└── 调用父组件保存函数
```

### 3. 保存流程

#### 统一的保存接口
```
NoteEditorContainer.handleSave → 构造 NoteData
├── 普通笔记：直接使用用户输入
├── 时间线笔记：序列化 timelineEntries 为 JSON
└── 调用父组件 onSave(NoteData)
    ├── NotesAddScreen：addNote 或 updateNote
    └── NotesEditScreen：updateNote
```

#### 自动保存机制
```
triggerAutoSave → 清除旧定时器 → 设置新定时器 (2秒)
└── performAutoSave / performTimelineAutoSave
    ├── 检查内容是否为空
    ├── 调用 handleSave 构造统一数据
    ├── 调用父组件 onSave 函数
    ├── 处理保存结果
    └── 更新保存状态
```

#### 手动保存
```
saveNow → 立即调用 handleSave
├── 成功 → 更新保存状态
└── 失败 → 显示错误提示
```

### 4. 退出流程

```
用户尝试退出 → NotesAddScreen/NotesEditScreen.checkBeforeExit
└── 委托给 NoteEditorContainer.checkBeforeExit
    ├── 普通笔记：委托给 NoteEditor.checkBeforeExit
    └── 时间线笔记：NoteEditorContainer 内部处理
        ├── 无未保存更改 → 直接退出
        └── 有未保存更改 → 显示确认对话框
            ├── 保存并返回 → saveNow → 退出
            └── 不保存返回 → 直接退出
```

## 关键技术实现

### 1. 统一容器组件设计

**NoteEditorContainer 核心逻辑：**
```typescript
const NoteEditorContainer = forwardRef<NoteEditorContainerRef, NoteEditorContainerProps>(({
  noteType,
  initialTitle = '',
  initialContent = '',
  initialTags = [],
  initialTimelineEntries = [],
  // ...其他props
}, ref) => {
  // 时间线笔记状态
  const [timelineEntries, setTimelineEntries] = useState<TimelineEntry[]>(initialTimelineEntries);
  const [timelineTitle, setTimelineTitle] = useState(initialTitle);
  const [timelineTags, setTimelineTags] = useState<string[]>(initialTags);

  // 根据笔记类型渲染不同的编辑器
  if (noteType === 'timeline') {
    return (
      <TimelineNoteEditor
        title={timelineTitle}
        entries={timelineEntries}
        selectedTags={timelineTags}
        onTitleChange={handleTimelineTitleChange}
        onAddEntry={handleAddTimelineEntry}
        onUpdateEntry={handleUpdateTimelineEntry}
        onDeleteEntry={handleDeleteTimelineEntry}
        onTagToggle={handleTimelineTagToggle}
      />
    );
  } else {
    return (
      <NoteEditor
        initialTitle={initialTitle}
        initialContent={initialContent}
        initialTags={initialTags}
        onSave={handleSave}
        // ...其他props
      />
    );
  }
});
```

### 2. 时间线数据序列化

**序列化机制：**
```typescript
// 解析时间线内容（支持新旧格式）
const parseTimelineContent = (content: string): TimelineEntry[] => {
  if (!content.trim()) return [];

  try {
    // 尝试解析 JSON 格式（新格式）
    const parsed = JSON.parse(content);
    if (Array.isArray(parsed)) {
      return parsed;
    }
  } catch {
    // 兼容旧格式：[时间] 内容
    const lines = content.split('\n').filter(line => line.trim());
    return lines.map((line, index) => {
      const match = line.match(/^\[(.+?)\]\s*(.+)$/);
      if (match) {
        return {
          id: `entry-${Date.now()}-${index}`,
          timestamp: match[1],
          content: match[2],
        };
      }
      return {
        id: `entry-${Date.now()}-${index}`,
        timestamp: new Date().toISOString(),
        content: line,
      };
    });
  }

  return [];
};

// 序列化时间线内容
const serializeTimelineContent = (entries: TimelineEntry[]): string => {
  if (!entries || entries.length === 0) return '';

  try {
    return JSON.stringify(entries, null, 0); // 紧凑格式
  } catch (error) {
    console.error('Error serializing timeline content:', error);
    return '';
  }
};
```

### 3. 状态同步机制

**问题：** React setState 异步更新导致保存时使用旧值

**解决方案：** 使用 ref 立即保存最新输入值
```typescript
const latestTitleRef = useRef(title);
const latestContentRef = useRef(content);

const handleTitleChange = (text: string) => {
  latestTitleRef.current = text; // 立即更新，不等待状态
  setTitle(text);
  setHasUnsavedChanges(true);
  triggerAutoSave();
};

// 获取内容时使用 ref 值
getCurrentContent: () => ({
  title: latestTitleRef.current,
  content: latestContentRef.current,
  tags: latestTagsRef.current,
})
```

### 4. 统一保存逻辑

**NoteData 统一接口：**
```typescript
interface NoteData {
  title: string;
  content: string;
  tags: string[];
  noteType: NoteType;
  timelineEntries?: TimelineEntry[];
}

// NoteEditorContainer 统一保存函数
const handleSave = async (editorData: {title: string, content: string, tags: string[]}) => {
  const noteData: NoteData = {
    title: noteType === 'timeline' ? timelineTitle : editorData.title,
    content: noteType === 'timeline' ? serializeTimelineContent(timelineEntries) : editorData.content,
    tags: noteType === 'timeline' ? timelineTags : editorData.tags,
    noteType,
    timelineEntries: noteType === 'timeline' ? timelineEntries : undefined,
  };

  const result = await onSave(noteData);

  if (result.success) {
    setHasUnsavedChanges(false);
    onDataChange?.(false);
  }

  return result;
};
```

### 5. 自动保存机制

**防抖实现：**
```typescript
const triggerAutoSave = () => {
  if (!autoSaveEnabled) return;

  // 清除之前的定时器
  if (autoSaveTimeoutRef.current) {
    clearTimeout(autoSaveTimeoutRef.current);
  }

  // 设置新的定时器
  autoSaveTimeoutRef.current = setTimeout(() => {
    performAutoSave();
  }, autoSaveDelay);
};

// 时间线笔记的自动保存
const triggerTimelineAutoSave = () => {
  if (!autoSaveEnabled || noteType !== 'timeline') return;

  if (autoSaveTimeoutRef.current) {
    clearTimeout(autoSaveTimeoutRef.current);
  }

  autoSaveTimeoutRef.current = setTimeout(() => {
    performTimelineAutoSave();
  }, autoSaveDelay);
};
```

**错误处理：**
```typescript
const performAutoSave = async () => {
  try {
    const result = await onSave({
      title: title.trim() || '无标题',
      content: content.trim() || ' ',
      tags: selectedTags,
    });

    if (result.success) {
      setLastSaved(new Date());
      setHasUnsavedChanges(false);
    } else {
      setHasUnsavedChanges(true);
      // 静默失败，不打扰用户
    }
  } catch (error) {
    setHasUnsavedChanges(true);
  }
};
```

### 6. 退出检查机制

**统一的退出检查：**
```typescript
// NoteEditorContainer 中的退出检查
const checkBeforeExit = async (): Promise<boolean> => {
  if (noteType === 'timeline') {
    if (!hasUnsavedChanges) {
      return true;
    }

    return new Promise((resolve) => {
      Alert.alert(
        '未保存的更改',
        '您有未保存的更改，是否要保存？',
        [
          {
            text: '保存并返回',
            onPress: async () => {
              const saved = await saveNow();
              resolve(saved);
            },
          },
          {
            text: '不保存返回',
            style: 'destructive',
            onPress: () => resolve(true),
          },
        ]
      );
    });
  } else {
    return normalEditorRef.current?.checkBeforeExit() || true;
  }
};

// 父组件中的委托
const checkBeforeExit = async (): Promise<boolean> => {
  if (noteEditorRef.current) {
    return await noteEditorRef.current.checkBeforeExit();
  }
  return true;
};
```

### 4. 组件生命周期管理

**清理定时器：**
```typescript
useEffect(() => {
  return () => {
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }
  };
}, []);
```

**数据变化通知：**
```typescript
useEffect(() => {
  onDataChange?.(hasUnsavedChanges);
}, [hasUnsavedChanges, onDataChange]);
```

## 渲染机制

### 1. 条件渲染

**NotesAddScreen：**
```typescript
return (
  <View style={styles.container}>
    {noteType === 'timeline' ? (
      <TimelineNoteEditor {...timelineProps} />
    ) : (
      <NoteEditor
        initialTitle=""
        initialContent=""
        initialTags={[]}
        onSave={handleSave}
        {...otherProps}
      />
    )}
  </View>
);
```

**NotesEditScreen：**
```typescript
return (
  <View style={styles.container}>
    {noteData ? (
      <NoteEditor
        initialTitle={noteData.title}
        initialContent={noteData.content}
        initialTags={noteData.tags}
        onSave={handleSave}
        {...otherProps}
      />
    ) : (
      <LoadingView />
    )}
  </View>
);
```

### 2. 性能优化

**防止不必要的重渲染：**
```typescript
// 使用 useImperativeHandle 缓存方法
useImperativeHandle(ref, () => ({
  getCurrentContent,
  checkBeforeExit,
  saveNow,
}), [hasUnsavedChanges]);

// 防抖自动保存减少 API 调用
const triggerAutoSave = useCallback(debounce(performAutoSave, autoSaveDelay), []);
```

## 异步流程处理

### 1. 数据加载

**错误处理和重试：**
```typescript
const loadNote = async () => {
  try {
    const result = await notesService.getNote(noteId);
    if (result.success) {
      loadNoteData(result.data);
    } else {
      Alert.alert('错误', result.message || '加载笔记失败');
    }
  } catch (error) {
    Alert.alert('错误', '加载笔记失败');
  }
};
```

### 2. 保存操作

**统一的保存接口：**
```typescript
// NotesAddScreen 的保存实现
const handleSave = async (data: {title: string, content: string, tags: string[]}) => {
  try {
    const tagsString = data.tags.length > 0 ? data.tags.join(', ') : undefined;
    const noteContent = noteType === 'timeline' ? getTimelineContent() : data.content;

    let result;
    if (createdNoteId) {
      result = await notesService.updateNote(createdNoteId, data.title, noteContent, undefined, tagsString, noteType);
    } else {
      result = await notesService.addNote(data.title, noteContent, undefined, tagsString, noteType);
      if (result.success && result.data?.note_id) {
        setCreatedNoteId(String(result.data.note_id));
      }
    }
    return result;
  } catch (error) {
    return { success: false, message: '保存失败，请检查网络连接' };
  }
};
```

## 内容丢失问题解决方案

### 问题根因分析

1. **状态同步延迟**：React setState 异步更新
2. **TextInput 值不同步**：onChangeText 可能未及时触发
3. **自动保存时机**：防抖延迟导致最后输入未保存
4. **退出检查缺陷**：未获取到 TextInput 实际值

### 解决方案

1. **使用 ref 立即保存**：绕过状态更新延迟
2. **改进 getCurrentContent**：直接从 ref 获取最新值
3. **统一退出检查**：确保保存最新输入
4. **明确错误处理**：用户明确知道保存状态

### 测试场景

1. **快速输入后立即退出**：✅ 解决
2. **输入后状态更新延迟**：✅ 解决  
3. **TextInput 焦点丢失**：✅ 解决
4. **网络请求失败**：✅ 解决

## 重构成果总结

### 解决的核心问题

1. **架构统一**：NotesAddScreen 和 NotesEditScreen 现在使用完全相同的 NoteEditorContainer
2. **类型安全**：时间线笔记编辑后不再转换为普通笔记，类型保持一致
3. **逻辑复用**：消除了两个屏幕之间的重复代码和差异化处理
4. **数据完整性**：时间线笔记的条目数据通过 JSON 序列化确保不丢失

### 技术改进

1. **统一容器**：NoteEditorContainer 根据笔记类型自动选择合适的编辑器
2. **数据接口**：统一的 NoteData 接口确保数据格式一致性
3. **状态管理**：两种笔记类型使用相同的状态管理和自动保存机制
4. **错误处理**：完善的错误处理和降级机制
5. **向后兼容**：支持旧格式时间线笔记的解析和转换

### 架构优势

1. **可维护性**：组件职责清晰，代码结构简洁
2. **可扩展性**：新增笔记类型只需在 NoteEditorContainer 中添加对应处理
3. **一致性**：用户体验在两种笔记类型间保持一致
4. **稳定性**：统一的数据流和错误处理确保系统稳定

这个重构彻底解决了原有的架构问题，为笔记系统提供了坚实的技术基础。

## 详细技术实现

### UI 组件结构

#### NoteEditor 组件布局
```
NoteEditor
├── titleContainer (固定标题区域)
│   └── titleInput (标题输入框)
├── toolbar (固定工具栏)
│   ├── 创建时间显示
│   ├── 字数统计
│   ├── 保存状态指示
│   └── 标签选择区域
└── editorWrapper (可滚动编辑区域)
    └── editorScrollView
        ├── contentContainer
        │   ├── NotePaperBackground (背景线条)
        │   └── contentInput (多行文本输入)
        └── statusBar (状态栏)
```

#### 关键样式设计
```typescript
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  titleContainer: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E8E8E8',
  },
  titleInput: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1C1B1F',
    fontFamily: 'LXGWWenKai-Regular',
  },
  contentInput: {
    ...getTextInputBaseStyle(),
    ...getEditorPaddingStyle(),
    minHeight: 600,
  },
});
```

### 状态管理详解

#### 状态结构
```typescript
// NoteEditor 内部状态
const [title, setTitle] = useState(initialTitle);
const [content, setContent] = useState(initialContent);
const [selectedTags, setSelectedTags] = useState<string[]>(initialTags);
const [lastSaved, setLastSaved] = useState<Date | null>(null);
const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

// ref 用于立即访问最新值
const latestTitleRef = useRef(title);
const latestContentRef = useRef(content);
const latestTagsRef = useRef(selectedTags);

// 自动保存定时器
const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);
```

#### 状态同步机制
```typescript
// 确保 ref 与状态同步
useEffect(() => {
  latestTitleRef.current = title;
}, [title]);

useEffect(() => {
  latestContentRef.current = content;
}, [content]);

useEffect(() => {
  latestTagsRef.current = selectedTags;
}, [selectedTags]);
```

### 自动保存深度解析

#### 防抖机制实现
```typescript
const triggerAutoSave = () => {
  if (!autoSaveEnabled) return;

  // 清除之前的定时器，实现防抖
  if (autoSaveTimeoutRef.current) {
    clearTimeout(autoSaveTimeoutRef.current);
  }

  // 设置新的定时器
  autoSaveTimeoutRef.current = setTimeout(() => {
    performAutoSave();
  }, autoSaveDelay); // 默认 2000ms
};
```

#### 保存策略
1. **触发条件**：任何内容变化（标题、正文、标签）
2. **防抖延迟**：2秒（可配置）
3. **空内容检查**：标题和内容都为空时不保存
4. **错误处理**：静默失败，不打扰用户编辑
5. **状态更新**：成功后更新保存时间和未保存标记

#### 保存内容处理
```typescript
const performAutoSave = async () => {
  // 检查是否有内容
  if (!title.trim() && !content.trim()) {
    return;
  }

  try {
    const result = await onSave({
      title: title.trim() || '无标题',
      content: content.trim() || ' ', // 空格占位，避免完全空内容
      tags: selectedTags,
    });

    if (result.success) {
      setLastSaved(new Date());
      setHasUnsavedChanges(false);
    } else {
      setHasUnsavedChanges(true);
    }
  } catch (error) {
    setHasUnsavedChanges(true);
  }
};
```

### 输入处理机制

#### 实时输入同步
```typescript
const handleTitleChange = (text: string) => {
  // 1. 立即更新 ref（绕过 React 状态更新延迟）
  latestTitleRef.current = text;

  // 2. 更新组件状态（触发重渲染）
  setTitle(text);

  // 3. 标记有未保存更改
  setHasUnsavedChanges(true);

  // 4. 触发自动保存
  triggerAutoSave();

  // 5. 通知父组件
  onDataChange?.(true);
};
```

#### TextInput 配置
```typescript
<TextInput
  ref={contentInputRef}
  style={styles.contentInput}
  value={content}
  onChangeText={handleContentChange}
  multiline
  textAlignVertical="top"
  scrollEnabled={false} // 禁用内部滚动
  blurOnSubmit={false}
  autoCorrect={false}
  autoCapitalize="none"
  spellCheck={false}
/>
```

### 标签系统实现

#### 预定义标签
```typescript
const PREDEFINED_TAGS = [
  { id: 'work', label: '工作', color: '#FF6B35' },
  { id: 'study', label: '学习', color: '#4ECDC4' },
  { id: 'life', label: '生活', color: '#45B7D1' },
  { id: 'important', label: '重要', color: '#F7DC6F' },
  { id: 'idea', label: '想法', color: '#BB8FCE' },
  { id: 'todo', label: '待办', color: '#85C1E9' },
];
```

#### 标签选择逻辑
```typescript
const handleTagToggle = (tagId: string) => {
  const newTags = selectedTags.includes(tagId)
    ? selectedTags.filter(id => id !== tagId)
    : [...selectedTags, tagId];

  latestTagsRef.current = newTags;
  setSelectedTags(newTags);
  setHasUnsavedChanges(true);
  triggerAutoSave();
  onDataChange?.(true);
};
```

### 退出检查详细流程

#### 检查逻辑
```typescript
const checkBeforeExit = async (): Promise<boolean> => {
  // 1. 检查是否有未保存更改
  if (!hasUnsavedChanges) {
    return true;
  }

  // 2. 显示确认对话框
  return new Promise((resolve) => {
    Alert.alert(
      '未保存的更改',
      '您有未保存的更改，是否要保存？',
      [
        {
          text: '保存并返回',
          onPress: async () => {
            // 3. 尝试保存
            const saved = await saveNow();
            resolve(saved);
          },
        },
        {
          text: '不保存返回',
          style: 'destructive',
          onPress: () => resolve(true),
        },
      ]
    );
  });
};
```

#### 手动保存实现
```typescript
const saveNow = async (): Promise<boolean> => {
  try {
    const result = await onSave({
      title: title.trim() || '无标题',
      content: content.trim() || ' ',
      tags: selectedTags,
    });

    if (result.success) {
      setLastSaved(new Date());
      setHasUnsavedChanges(false);
      return true;
    } else {
      Alert.alert('保存失败', result.message || '保存失败，请检查网络连接');
      return false;
    }
  } catch (error) {
    Alert.alert('保存出错', '保存时发生错误，请检查网络连接');
    return false;
  }
};
```

### 父组件集成

#### NotesAddScreen 集成
```typescript
// 保存函数实现
const handleSave = async (data: {title: string, content: string, tags: string[]}) => {
  try {
    const tagsString = data.tags.length > 0 ? data.tags.join(', ') : undefined;
    const noteContent = noteType === 'timeline' ? getTimelineContent() : data.content;

    let result;
    if (createdNoteId) {
      // 更新现有笔记
      result = await notesService.updateNote(
        createdNoteId, data.title, noteContent, undefined, tagsString, noteType
      );
    } else {
      // 创建新笔记
      result = await notesService.addNote(
        data.title, noteContent, undefined, tagsString, noteType
      );

      // 保存笔记 ID
      if (result.success && result.data?.note_id) {
        setCreatedNoteId(String(result.data.note_id));
      }
    }

    return result;
  } catch (error) {
    return {
      success: false,
      message: '保存失败，请检查网络连接',
    };
  }
};

// 数据变化回调
const handleDataChange = (hasChanges: boolean) => {
  setHasUnsavedChanges(hasChanges);
};

// 退出检查委托
const checkBeforeExit = async (): Promise<boolean> => {
  if (noteType === 'normal' && noteEditorRef.current) {
    return await noteEditorRef.current.checkBeforeExit();
  }
  // 时间线笔记的特殊处理...
};
```

#### NotesEditScreen 集成
```typescript
// 数据加载
const loadNoteData = (note: NoteItem) => {
  const tagArray = note.tags
    ? note.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
    : [];

  setNoteData({
    title: note.title,
    content: note.content,
    tags: tagArray,
    createdTime: new Date(note.createTime),
  });
};

// 更新函数实现
const handleSave = async (data: {title: string, content: string, tags: string[]}) => {
  try {
    const tagsString = data.tags.length > 0 ? data.tags.join(', ') : undefined;
    const result = await notesService.updateNote(
      noteId, data.title, data.content, undefined, tagsString
    );
    return result;
  } catch (error) {
    return {
      success: false,
      message: '更新失败，请检查网络连接',
    };
  }
};
```

### 性能优化策略

#### 1. 防抖优化
- 自动保存使用 2 秒防抖，减少 API 调用频率
- 避免用户快速输入时的频繁网络请求

#### 2. 状态优化
- 使用 ref 避免不必要的状态更新
- useImperativeHandle 缓存方法引用

#### 3. 渲染优化
- 条件渲染减少不必要的组件创建
- 合理的组件拆分避免大组件重渲染

#### 4. 内存管理
- 组件卸载时清理定时器
- 避免内存泄漏

### 错误处理策略

#### 1. 网络错误
- 自动保存失败：静默处理，不打扰用户
- 手动保存失败：显示错误提示，允许重试
- 加载失败：显示错误信息，提供重试选项

#### 2. 数据验证
- 空内容检查：避免保存无意义的空笔记
- 标题默认值：确保笔记有可识别的标题

#### 3. 用户体验
- 明确的保存状态指示
- 友好的错误提示信息
- 合理的重试机制

这个技术文档详细描述了重构后笔记编辑系统的各个方面，为开发团队提供了完整的技术参考。
