/**
 * 最终上传修复验证脚本
 */

const fs = require('fs');

function checkDriveServiceFix() {
  console.log('🔍 检查 driveService.ts 修复...\n');

  const driveServicePath = './src/services/driveService.ts';
  if (!fs.existsSync(driveServicePath)) {
    console.log('❌ driveService.ts 文件不存在');
    return false;
  }

  const content = fs.readFileSync(driveServicePath, 'utf8');
  
  // 检查关键修复点
  const checks = [
    {
      name: '正确的 FormData 格式',
      test: content.includes('formData.append(\'file\', {') && 
            content.includes('uri: request.fileData.uri') &&
            content.includes('type: request.fileData.type') &&
            content.includes('name: request.fileData.name'),
      description: 'React Native FormData 对象格式'
    },
    {
      name: '移除 Content-Type 头',
      test: !content.includes('\'Content-Type\': \'multipart/form-data\''),
      description: '让 React Native 自动设置 Content-Type'
    },
    {
      name: '保留 bullet 头',
      test: content.includes('\'bullet\': \'36fdf066-9e42-11ec-b41e-525400043ced\''),
      description: '认证头正确设置'
    },
    {
      name: '增加超时时间',
      test: content.includes('timeout: 120000'),
      description: '2分钟超时设置'
    },
    {
      name: '详细错误处理',
      test: content.includes('Network Error') && content.includes('timeout'),
      description: '网络错误详细处理'
    },
    {
      name: '调试日志',
      test: content.includes('console.log(\'Uploading file:\''),
      description: '上传调试信息'
    }
  ];

  let allPassed = true;
  checks.forEach(check => {
    if (check.test) {
      console.log(`✅ ${check.name}: ${check.description}`);
    } else {
      console.log(`❌ ${check.name}: ${check.description}`);
      allPassed = false;
    }
  });

  return allPassed;
}

function checkPackageJson() {
  console.log('\n📦 检查 package.json...\n');

  const packageJsonPath = './package.json';
  if (!fs.existsSync(packageJsonPath)) {
    console.log('❌ package.json 不存在');
    return false;
  }

  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  const hasDocumentPicker = packageJson.dependencies && packageJson.dependencies['react-native-document-picker'];
  
  if (hasDocumentPicker) {
    console.log(`✅ react-native-document-picker: ${packageJson.dependencies['react-native-document-picker']}`);
    return true;
  } else {
    console.log('❌ react-native-document-picker 未安装');
    return false;
  }
}

function checkTestFiles() {
  console.log('\n🧪 检查测试文件...\n');

  const testFiles = [
    { path: './src/tests/NetworkTest.js', name: '网络测试' },
    { path: './src/tests/DriveUploadTest.js', name: '上传测试' },
    { path: './test-upload-api.js', name: 'API 测试' },
    { path: './NETWORK_ERROR_FIX.md', name: '修复文档' }
  ];

  let allExist = true;
  testFiles.forEach(file => {
    if (fs.existsSync(file.path)) {
      console.log(`✅ ${file.name}: ${file.path}`);
    } else {
      console.log(`❌ ${file.name}: ${file.path} 不存在`);
      allExist = false;
    }
  });

  return allExist;
}

function generateNextSteps() {
  console.log('\n📋 下一步操作指南:\n');
  
  console.log('1. 重新连接设备:');
  console.log('   adb disconnect');
  console.log('   adb connect 127.0.0.1:19900');
  console.log('');
  
  console.log('2. 重新构建应用:');
  console.log('   npm run android');
  console.log('');
  
  console.log('3. 测试上传功能:');
  console.log('   - 打开应用');
  console.log('   - 进入云盘功能');
  console.log('   - 点击上传按钮');
  console.log('   - 选择文件');
  console.log('   - 观察上传过程');
  console.log('');
  
  console.log('4. 查看调试日志:');
  console.log('   adb logcat | grep -E "(Upload|Error|Network|thinking)"');
  console.log('');
  
  console.log('5. 如果仍有问题:');
  console.log('   - 检查 Metro 日志');
  console.log('   - 使用 NetworkTest.js 测试网络');
  console.log('   - 查看 NETWORK_ERROR_FIX.md 文档');
}

async function runVerification() {
  console.log('🚀 开始最终验证...\n');
  console.log('=====================================\n');

  const driveServiceOK = checkDriveServiceFix();
  const packageJsonOK = checkPackageJson();
  const testFilesOK = checkTestFiles();

  console.log('\n📊 验证结果:');
  console.log('=====================================');

  if (driveServiceOK && packageJsonOK && testFilesOK) {
    console.log('🎉 所有检查都通过了！');
    console.log('');
    console.log('✅ FormData 格式已修复');
    console.log('✅ Content-Type 头已移除');
    console.log('✅ 错误处理已改进');
    console.log('✅ 依赖包已安装');
    console.log('✅ 测试文件已准备');
    console.log('');
    console.log('🔧 修复要点:');
    console.log('   • React Native FormData 使用对象格式 {uri, type, name}');
    console.log('   • 移除 Content-Type 头让系统自动设置');
    console.log('   • 增加详细的错误处理和调试日志');
    console.log('   • 设置合适的超时时间 (2分钟)');
    
  } else {
    console.log('❌ 部分检查失败');
    console.log('');
    if (!driveServiceOK) console.log('❌ driveService.ts 需要修复');
    if (!packageJsonOK) console.log('❌ package.json 需要更新');
    if (!testFilesOK) console.log('❌ 测试文件缺失');
  }

  generateNextSteps();

  console.log('\n🎯 预期结果:');
  console.log('修复后，文件上传应该可以正常工作，不再显示 "Network Error"');
}

// 运行验证
runVerification().catch(console.error);
