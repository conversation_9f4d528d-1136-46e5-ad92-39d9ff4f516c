#!/usr/bin/env node

/**
 * 云盘缩略图和预览功能集成测试
 * 测试整个功能链路：前端 -> Gateway -> Drive Service
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 配置
const BASE_URL = 'https://local.luzeshu.cn/api';
const AUTH_HEADER = 'bullet: 36fdf066-9e42-11ec-b41e-525400043ced';

// 创建axios实例
const api = axios.create({
  baseURL: BASE_URL,
  headers: {
    'bullet': '36fdf066-9e42-11ec-b41e-525400043ced',
    'Content-Type': 'application/json',
  },
  timeout: 10000,
  // 忽略SSL证书验证（开发环境）
  httpsAgent: new (require('https').Agent)({
    rejectUnauthorized: false
  })
});

// 测试结果
let testResults = {
  total: 0,
  passed: 0,
  failed: 0,
  errors: []
};

// 测试函数
async function runTest(testName, testFn) {
  testResults.total++;
  console.log(`\n🧪 测试: ${testName}`);
  
  try {
    await testFn();
    testResults.passed++;
    console.log(`✅ ${testName} - 通过`);
  } catch (error) {
    testResults.failed++;
    testResults.errors.push({ test: testName, error: error.message });
    console.log(`❌ ${testName} - 失败: ${error.message}`);
  }
}

// 1. 测试服务连通性
async function testServiceConnectivity() {
  const response = await api.get('/drive/files');
  if (response.status !== 200) {
    throw new Error(`服务连接失败，状态码: ${response.status}`);
  }
  console.log('   📡 Drive服务连接正常');
}

// 2. 测试文件列表API
async function testFileListAPI() {
  const response = await api.get('/drive/files?offset=0&size=10');
  
  if (!response.data || !Array.isArray(response.data.FileList)) {
    throw new Error('文件列表格式错误');
  }
  
  console.log(`   📁 获取到 ${response.data.FileList.length} 个文件`);
  
  // 检查是否有ThumbnailPath字段
  const filesWithThumbnails = response.data.FileList.filter(file => file.ThumbnailPath);
  console.log(`   🖼️  其中 ${filesWithThumbnails.length} 个文件有缩略图`);
  
  return response.data.FileList;
}

// 3. 测试缩略图生成API
async function testThumbnailGeneration(files) {
  const imageFiles = files.filter(file => 
    file.MimeType && file.MimeType.startsWith('image/')
  );
  
  if (imageFiles.length === 0) {
    console.log('   ⚠️  没有找到图片文件，跳过缩略图生成测试');
    return;
  }
  
  const testFile = imageFiles[0];
  console.log(`   🎯 测试文件: ${testFile.Name} (ID: ${testFile.ID})`);
  
  try {
    const response = await api.post(`/drive/files/${testFile.ID}/thumbnail`);
    
    if (response.status === 200 && response.data.thumbnail_path) {
      console.log(`   ✨ 缩略图生成成功: ${response.data.thumbnail_path}`);
    } else {
      throw new Error('缩略图生成失败');
    }
  } catch (error) {
    if (error.response && error.response.status === 415) {
      console.log('   ℹ️  文件类型不支持缩略图生成');
    } else {
      throw error;
    }
  }
}

// 4. 测试缩略图获取API
async function testThumbnailRetrieval(files) {
  const filesWithThumbnails = files.filter(file => file.ThumbnailPath);
  
  if (filesWithThumbnails.length === 0) {
    console.log('   ⚠️  没有找到有缩略图的文件，跳过缩略图获取测试');
    return;
  }
  
  const testFile = filesWithThumbnails[0];
  console.log(`   🎯 测试文件: ${testFile.Name} (ID: ${testFile.ID})`);
  
  const response = await api.get(`/drive/files/${testFile.ID}/thumbnail`, {
    responseType: 'arraybuffer'
  });
  
  if (response.status === 200 && response.data.byteLength > 0) {
    console.log(`   🖼️  缩略图获取成功，大小: ${response.data.byteLength} bytes`);
    console.log(`   📄 Content-Type: ${response.headers['content-type']}`);
  } else {
    throw new Error('缩略图获取失败');
  }
}

// 5. 测试文件类型识别
async function testFileTypeDetection() {
  const testCases = [
    { mimeType: 'image/jpeg', expected: true },
    { mimeType: 'image/png', expected: true },
    { mimeType: 'video/mp4', expected: true },
    { mimeType: 'text/plain', expected: false },
    { mimeType: 'application/pdf', expected: false },
  ];
  
  // 这里我们模拟前端的文件类型检测逻辑
  const isImageFile = (mimeType) => mimeType && mimeType.startsWith('image/');
  const isVideoFile = (mimeType) => mimeType && mimeType.startsWith('video/');
  const isPreviewSupported = (mimeType) => isImageFile(mimeType) || isVideoFile(mimeType);
  
  for (const testCase of testCases) {
    const result = isPreviewSupported(testCase.mimeType);
    if (result !== testCase.expected) {
      throw new Error(`文件类型检测错误: ${testCase.mimeType}, 期望: ${testCase.expected}, 实际: ${result}`);
    }
  }
  
  console.log('   🔍 文件类型检测逻辑正确');
}

// 6. 测试存储信息API
async function testStorageInfo() {
  const response = await api.get('/drive/storage/info');
  
  if (!response.data || typeof response.data.TotalSpace !== 'number') {
    throw new Error('存储信息格式错误');
  }
  
  console.log(`   💾 总空间: ${(response.data.TotalSpace / 1024 / 1024 / 1024).toFixed(2)} GB`);
  console.log(`   📊 已用空间: ${(response.data.UsedSpace / 1024 / 1024 / 1024).toFixed(2)} GB`);
  console.log(`   📈 使用率: ${response.data.UsagePercent.toFixed(2)}%`);
}

// 主测试函数
async function runAllTests() {
  console.log('🚀 开始云盘缩略图和预览功能集成测试\n');
  console.log('📋 测试配置:');
  console.log(`   🌐 API地址: ${BASE_URL}`);
  console.log(`   🔑 认证头: ${AUTH_HEADER}`);
  
  let files = [];
  
  // 运行所有测试
  await runTest('服务连通性测试', testServiceConnectivity);
  
  await runTest('文件列表API测试', async () => {
    files = await testFileListAPI();
  });
  
  await runTest('缩略图生成API测试', () => testThumbnailGeneration(files));
  
  await runTest('缩略图获取API测试', () => testThumbnailRetrieval(files));
  
  await runTest('文件类型识别测试', testFileTypeDetection);
  
  await runTest('存储信息API测试', testStorageInfo);
  
  // 输出测试结果
  console.log('\n📊 测试结果汇总:');
  console.log(`   总测试数: ${testResults.total}`);
  console.log(`   通过: ${testResults.passed} ✅`);
  console.log(`   失败: ${testResults.failed} ❌`);
  
  if (testResults.failed > 0) {
    console.log('\n❌ 失败的测试:');
    testResults.errors.forEach(error => {
      console.log(`   - ${error.test}: ${error.error}`);
    });
    process.exit(1);
  } else {
    console.log('\n🎉 所有测试通过！云盘缩略图和预览功能正常工作。');
    process.exit(0);
  }
}

// 错误处理
process.on('unhandledRejection', (error) => {
  console.error('\n💥 未处理的错误:', error.message);
  if (error.response) {
    console.error('   响应状态:', error.response.status);
    console.error('   响应数据:', error.response.data);
  }
  process.exit(1);
});

// 运行测试
runAllTests().catch(error => {
  console.error('\n💥 测试执行失败:', error.message);
  process.exit(1);
});
