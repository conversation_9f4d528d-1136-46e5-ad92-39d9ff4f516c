{"name": "thinking", "version": "1.0.0", "description": "React Native app migrating favorites and tasks functionality", "main": "index.js", "scripts": {"start": "react-native start", "start:verbose": "react-native start --verbose", "start:reset": "react-native start --reset-cache", "android": "react-native run-android", "android:verbose": "react-native run-android --verbose", "android:debug": "react-native run-android --variant=debug --verbose", "ios": "react-native run-ios", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "type-check": "tsc --noEmit", "clean": "react-native clean-project-auto", "reset-cache": "npx react-native start --reset-cache", "clean:android": "cd android && ./gradlew clean && cd ..", "clean:all": "rm -rf node_modules && npm install && npm run clean:android", "logcat": "adb logcat | grep -E '(ReactNative|JS|Bundle|Error|Exception|com.thinkingandroid)'", "logcat:all": "adb logcat", "devices": "adb devices -l", "debug": "./debug-scripts.sh", "build:android": "cd android && ./gradlew assembleDebug --info", "build:android:verbose": "cd android && ./gradlew assembleDebug --debug"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.19.8", "@react-navigation/bottom-tabs": "^7.4.0", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.0", "axios": "^1.5.0", "react": "^18.2.0", "react-native": "^0.72.15", "react-native-document-picker": "^9.3.1", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.14.0", "react-native-image-viewing": "^0.2.2", "react-native-markdown-display": "^7.0.2", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^4.8.2", "react-native-screens": "^3.29.0", "react-native-video": "^6.15.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@react-native/typescript-config": "^0.73.1", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/react": "^18.2.6", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "babel-jest": "^29.6.3", "babel-plugin-module-resolver": "^5.0.2", "eslint": "^8.19.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-native": "^4.1.0", "jest": "^29.6.3", "metro-react-native-babel-preset": "0.76.8", "prettier": "^3.0.0", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}