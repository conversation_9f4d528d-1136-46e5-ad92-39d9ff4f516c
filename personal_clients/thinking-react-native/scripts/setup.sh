# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate

# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml
*.hprof

# node.js
#
node_modules/
npm-debug.log
yarn-error.log

# BUCK
buck-out/
\.buckd/
*.keystore
!debug.keystore

# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/

*/fastlane/report.xml
*/fastlane/Preview.html
*/fastlane/screenshots

# Bundle artifact
*.jsbundle

# Ruby / CocoaPods
/ios/Pods/
/vendor/bundle/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary files created during React Native build
.tmp/

# React Native CLI specific
.react-native/

# Metro
.metro-health-check*

# ESLint
.eslintcache

# TypeScript
*.tsbuildinfo

# VS Code
.vscode/

# JetBrains IDEs
.idea/

# macOS
.DS_Store

# Windows
Thumbs.db

# Log files
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# React Native specific
# Android
android/app/build/
android/build/
android/.gradle/
android/gradle/
android/gradlew
android/gradlew.bat
android/local.properties

# iOS
ios/build/
ios/Pods/

# Generated files
*.generated.js
*.generated.ts

# Flipper
flipper-out/

# Other
.expo/
.expo-shared/

#!/bin/bash

echo "🚀 Setting up Thinking Android React Native project..."

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Check if we're in the correct directory
if [ ! -f "package.json" ]; then
    echo -e "${RED}❌ Error: Please run this script from the thinking-android root directory${NC}"
    exit 1
fi

echo -e "${YELLOW}📋 Checking prerequisites...${NC}"

# Check Node.js
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js is not installed. Please install Node.js 18+ first.${NC}"
    exit 1
else
    NODE_VERSION=$(node --version)
    echo -e "${GREEN}✅ Node.js found: $NODE_VERSION${NC}"
fi

# Check npm
if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ npm is not installed${NC}"
    exit 1
else
    NPM_VERSION=$(npm --version)
    echo -e "${GREEN}✅ npm found: $NPM_VERSION${NC}"
fi

# Check Java
if ! command -v java &> /dev/null; then
    echo -e "${YELLOW}⚠️ Java not found. You'll need Java 11+ for Android development${NC}"
    echo -e "${YELLOW}   Install OpenJDK 11 or later: sudo apt install openjdk-11-jdk${NC}"
else
    JAVA_VERSION=$(java -version 2>&1 | head -n 1)
    echo -e "${GREEN}✅ Java found: $JAVA_VERSION${NC}"
fi

# Check Android SDK
if [ ! -d "$ANDROID_HOME" ] && [ ! -d "$ANDROID_SDK_ROOT" ]; then
    echo -e "${YELLOW}⚠️ Android SDK not found${NC}"
    echo -e "${YELLOW}   Please install Android Studio and set ANDROID_HOME environment variable${NC}"
    echo -e "${YELLOW}   Example: export ANDROID_HOME=/path/to/android-sdk${NC}"
else
    echo -e "${GREEN}✅ Android SDK found${NC}"
fi

echo -e "${YELLOW}📦 Installing dependencies...${NC}"

# Install npm dependencies
if npm install; then
    echo -e "${GREEN}✅ Dependencies installed successfully${NC}"
else
    echo -e "${RED}❌ Failed to install dependencies${NC}"
    exit 1
fi

echo -e "${YELLOW}🔧 Setting up Android project...${NC}"

# Make gradlew executable
chmod +x android/gradlew

# Create necessary directories
mkdir -p android/app/src/main/res/mipmap-hdpi
mkdir -p android/app/src/main/res/mipmap-mdpi
mkdir -p android/app/src/main/res/mipmap-xhdpi
mkdir -p android/app/src/main/res/mipmap-xxhdpi
mkdir -p android/app/src/main/res/mipmap-xxxhdpi

# Create simple app icons (placeholder)
for size in hdpi mdpi xhdpi xxhdpi xxxhdpi; do
    if [ ! -f "android/app/src/main/res/mipmap-$size/ic_launcher.png" ]; then
        touch "android/app/src/main/res/mipmap-$size/ic_launcher.png"
        touch "android/app/src/main/res/mipmap-$size/ic_launcher_round.png"
    fi
done

echo -e "${GREEN}✅ Setup completed!${NC}"
echo
echo -e "${YELLOW}📱 To run the app:${NC}"
echo -e "1. Start Metro server: ${GREEN}npm start${NC}"
echo -e "2. In another terminal:"
echo -e "   - For Android: ${GREEN}npm run android${NC}"
echo -e "   - For iOS: ${GREEN}npm run ios${NC} (macOS only)"
echo
echo -e "${YELLOW}📝 Notes:${NC}"
echo -e "- Make sure you have an Android emulator running or device connected"
echo -e "- For the first run, Gradle will download dependencies (may take a while)"
echo -e "- If you encounter issues, try: ${GREEN}npx react-native doctor${NC}"
echo
echo -e "${YELLOW}🔗 API Configuration:${NC}"
echo -e "- Favorites API: http://localhost:8080/api/favorites"
echo -e "- Tasks API: http://localhost:3000/api/tasks"
echo -e "- The app includes mock data for development when APIs are unavailable"
echo
echo -e "${GREEN}🎉 Ready to develop! Happy coding!${NC}" 