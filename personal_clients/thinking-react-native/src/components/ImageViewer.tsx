import React, {useState, useEffect} from 'react';
import {
  Modal,
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  StatusBar,
  Dimensions,
  ScrollView,
} from 'react-native';
import FastImage from 'react-native-fast-image';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

interface ImageItem {
  id: number;
  name: string;
  uri: string;
  thumbnailUri?: string;
  downloadConfig?: {uri: string; headers: Record<string, string>};
  thumbnailConfig?: {uri: string; headers: Record<string, string>};
}

interface ImageViewerProps {
  images: ImageItem[];
  visible: boolean;
  initialIndex?: number;
  onClose: () => void;
  onImagePress?: (image: ImageItem, index: number) => void;
}

const ImageViewer: React.FC<ImageViewerProps> = ({
  images,
  visible,
  initialIndex = 0,
  onClose,
  onImagePress,
}) => {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);

  // 当initialIndex改变时，更新currentIndex
  useEffect(() => {
    console.log('ImageViewer: initialIndex changed to', initialIndex);
    setCurrentIndex(initialIndex || 0);
  }, [initialIndex]);

  // 调试当前显示的图片信息
  useEffect(() => {
    if (visible && images[currentIndex]) {
      console.log('ImageViewer: displaying image', {
        index: currentIndex,
        id: images[currentIndex].id,
        name: images[currentIndex].name,
        uri: images[currentIndex].downloadConfig?.uri || images[currentIndex].uri,
      });
    }
  }, [visible, currentIndex, images]);

  // 清除FastImage缓存以确保图片正确显示
  useEffect(() => {
    if (visible) {
      // 清除所有缓存
      FastImage.clearMemoryCache();
      FastImage.clearDiskCache();
    }
  }, [visible, currentIndex]);

  // 转换为react-native-image-viewing需要的格式
  const imageViewingData = images.map(image => ({
    uri: image.downloadConfig?.uri || image.uri,
    headers: image.downloadConfig?.headers,
  }));

  const handleImageIndexChange = (index: number) => {
    setCurrentIndex(index);
  };

  const handleImagePress = () => {
    if (onImagePress && images[currentIndex]) {
      onImagePress(images[currentIndex], currentIndex);
    }
  };

  const renderHeader = () => (
    <View style={styles.header}>
      <TouchableOpacity style={styles.closeButton} onPress={onClose}>
        <Text style={styles.closeButtonText}>✕</Text>
      </TouchableOpacity>
      <View style={styles.headerInfo}>
        <Text style={styles.headerTitle}>
          {images[currentIndex]?.name || '图片'}
        </Text>
        <Text style={styles.headerSubtitle}>
          {currentIndex + 1} / {images.length}
        </Text>
      </View>
    </View>
  );

  const renderFooter = () => (
    <View style={styles.footer}>
      <Text style={styles.footerText}>
        {images[currentIndex]?.name || ''}
      </Text>
    </View>
  );

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
    }
  };

  const handleNext = () => {
    if (currentIndex < images.length - 1) {
      setCurrentIndex(currentIndex + 1);
    }
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}>
      <StatusBar hidden />
      <View style={styles.fullScreenContainer}>
        {renderHeader()}

        <View style={styles.imageContainer}>
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            maximumZoomScale={3}
            minimumZoomScale={1}
            showsHorizontalScrollIndicator={false}
            showsVerticalScrollIndicator={false}>
            <FastImage
              key={`image-${images[currentIndex]?.id}-${currentIndex}-${Date.now()}`}
              source={{
                ...images[currentIndex]?.downloadConfig || {uri: images[currentIndex]?.uri},
                priority: FastImage.priority.high,
              }}
              style={styles.fullScreenImage}
              resizeMode={FastImage.resizeMode.contain}
            />
          </ScrollView>

          {/* 导航按钮 */}
          {currentIndex > 0 && (
            <TouchableOpacity style={styles.prevButton} onPress={handlePrevious}>
              <Text style={styles.navButtonText}>‹</Text>
            </TouchableOpacity>
          )}

          {currentIndex < images.length - 1 && (
            <TouchableOpacity style={styles.nextButton} onPress={handleNext}>
              <Text style={styles.navButtonText}>›</Text>
            </TouchableOpacity>
          )}
        </View>

        {renderFooter()}
      </View>
    </Modal>
  );
};

// 图片网格组件
interface ImageGridProps {
  images: ImageItem[];
  onImagePress: (image: ImageItem, index: number) => void;
  numColumns?: number;
}

export const ImageGrid: React.FC<ImageGridProps> = ({
  images,
  onImagePress,
  numColumns = 3,
}) => {
  const imageSize = (screenWidth - 40 - (numColumns - 1) * 10) / numColumns;

  return (
    <View style={styles.grid}>
      {images.map((image, index) => (
        <TouchableOpacity
          key={image.id}
          style={[styles.gridItem, {width: imageSize, height: imageSize}]}
          onPress={() => onImagePress(image, index)}>
          <FastImage
            source={{
              ...(image.thumbnailConfig || image.downloadConfig || {uri: image.thumbnailUri || image.uri}),
              priority: FastImage.priority.normal,
            }}
            style={styles.gridImage}
            resizeMode={FastImage.resizeMode.cover}
          />
          <View style={styles.gridOverlay}>
            <Text style={styles.gridImageName} numberOfLines={2}>
              {image.name}
            </Text>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  fullScreenContainer: {
    flex: 1,
    backgroundColor: 'black',
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollView: {
    flex: 1,
    width: '100%',
  },
  scrollContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreenImage: {
    width: screenWidth,
    height: screenHeight - 180, // 减去header和footer的高度
  },
  prevButton: {
    position: 'absolute',
    left: 20,
    top: '50%',
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: -25,
  },
  nextButton: {
    position: 'absolute',
    right: 20,
    top: '50%',
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: -25,
  },
  navButtonText: {
    color: 'white',
    fontSize: 30,
    fontWeight: 'bold',
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 100,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    flexDirection: 'row',
    alignItems: 'flex-end',
    paddingBottom: 10,
    paddingHorizontal: 20,
    zIndex: 1,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  headerInfo: {
    flex: 1,
    marginLeft: 15,
  },
  headerTitle: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
  headerSubtitle: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 14,
    marginTop: 2,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 80,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  footerText: {
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    padding: 20,
  },
  gridItem: {
    marginBottom: 10,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#f5f5f5',
  },
  gridImage: {
    width: '100%',
    height: '100%',
  },
  gridOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    padding: 8,
  },
  gridImageName: {
    color: 'white',
    fontSize: 12,
    textAlign: 'center',
  },
});

export default ImageViewer;
