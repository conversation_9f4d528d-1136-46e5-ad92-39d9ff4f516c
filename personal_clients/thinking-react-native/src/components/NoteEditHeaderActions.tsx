import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
} from 'react-native';

interface NoteEditHeaderActionsProps {
  onSettingsPress: () => void;
  color?: string;
}

const NoteEditHeaderActions: React.FC<NoteEditHeaderActionsProps> = ({
  onSettingsPress,
  color = '#FFFFFF',
}) => {
  return (
    <TouchableOpacity onPress={onSettingsPress} style={styles.settingsButton}>
      <Text style={[styles.settingsIcon, { color }]}>⋮</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  settingsButton: {
    padding: 4,
    borderRadius: 20,
    minWidth: 32,
    minHeight: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  settingsIcon: {
    fontSize: 20,
    fontWeight: 'bold',
    lineHeight: 20,
  },
});

export default NoteEditHeaderActions;
