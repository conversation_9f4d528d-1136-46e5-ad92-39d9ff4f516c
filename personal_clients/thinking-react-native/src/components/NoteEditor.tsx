import React, {useState, useRef, useEffect, useImperativeHandle, forwardRef} from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
} from 'react-native';
import NotePaperBackground from './NotePaperBackground';
import { EDITOR_CONSTANTS, getTextInputBaseStyle, getEditorPaddingStyle } from '../constants/editorConstants';

export interface NoteEditorRef {
  getCurrentContent: () => { title: string; content: string; tags: string[] };
  checkBeforeExit: () => Promise<boolean>;
  saveNow: () => Promise<boolean>;
}

// 预定义标签
const PREDEFINED_TAGS = [
  { id: 'work', label: '工作', color: '#FF6B35' },
  { id: 'study', label: '学习', color: '#4ECDC4' },
  { id: 'life', label: '生活', color: '#45B7D1' },
  { id: 'important', label: '重要', color: '#F7DC6F' },
  { id: 'idea', label: '想法', color: '#BB8FCE' },
  { id: 'todo', label: '待办', color: '#85C1E9' },
];

interface NoteEditorProps {
  // 初始数据
  initialTitle?: string;
  initialContent?: string;
  initialTags?: string[];
  createdTime?: Date;

  // 保存函数 - 由父组件提供具体实现
  onSave: (data: {title: string, content: string, tags: string[]}) => Promise<{success: boolean, message?: string}>;

  // 事件回调
  onBack?: () => void;
  onDataChange?: (hasChanges: boolean) => void;

  // 配置选项
  autoSaveEnabled?: boolean;
  autoSaveDelay?: number;
}

const NoteEditor = forwardRef<NoteEditorRef, NoteEditorProps>(({
  initialTitle = '',
  initialContent = '',
  initialTags = [],
  createdTime,
  onSave,
  onBack,
  onDataChange,
  autoSaveEnabled = true,
  autoSaveDelay = 2000,
}, ref) => {
  // 内部状态管理
  const [title, setTitle] = useState(initialTitle);
  const [content, setContent] = useState(initialContent);
  const [selectedTags, setSelectedTags] = useState<string[]>(initialTags);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // 自动保存相关
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 添加refs来直接访问TextInput的值
  const titleInputRef = useRef<TextInput>(null);
  const contentInputRef = useRef<TextInput>(null);

  // 使用ref保存最新的输入值，避免state更新延迟
  const latestTitleRef = useRef(title);
  const latestContentRef = useRef(content);
  const latestTagsRef = useRef(selectedTags);

  // 更新最新值的ref
  useEffect(() => {
    latestTitleRef.current = title;
  }, [title]);

  useEffect(() => {
    latestContentRef.current = content;
  }, [content]);

  useEffect(() => {
    latestTagsRef.current = selectedTags;
  }, [selectedTags]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, []);

  // 监听数据变化，通知父组件
  useEffect(() => {
    onDataChange?.(hasUnsavedChanges);
  }, [hasUnsavedChanges, onDataChange]);

  // 触发自动保存
  const triggerAutoSave = () => {
    if (!autoSaveEnabled) return;

    // 清除之前的定时器
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    // 设置新的定时器
    autoSaveTimeoutRef.current = setTimeout(() => {
      performAutoSave();
    }, autoSaveDelay);
  };

  // 执行自动保存
  const performAutoSave = async () => {
    if (!title.trim() && !content.trim()) {
      return; // 空内容不保存
    }

    try {
      const result = await onSave({
        title: title.trim() || '无标题',
        content: content.trim() || ' ',
        tags: selectedTags,
      });

      if (result.success) {
        setLastSaved(new Date());
        setHasUnsavedChanges(false);
        console.log('NoteEditor autoSave - Success');
      } else {
        console.error('NoteEditor autoSave - Failed:', result.message);
        setHasUnsavedChanges(true);
      }
    } catch (error) {
      console.error('NoteEditor autoSave - Error:', error);
      setHasUnsavedChanges(true);
    }
  };

  // 手动保存
  const saveNow = async (): Promise<boolean> => {
    try {
      const result = await onSave({
        title: title.trim() || '无标题',
        content: content.trim() || ' ',
        tags: selectedTags,
      });

      if (result.success) {
        setLastSaved(new Date());
        setHasUnsavedChanges(false);
        return true;
      } else {
        Alert.alert('保存失败', result.message || '保存失败，请检查网络连接');
        return false;
      }
    } catch (error) {
      console.error('NoteEditor saveNow - Error:', error);
      Alert.alert('保存出错', '保存时发生错误，请检查网络连接');
      return false;
    }
  };

  // 检查退出前是否需要保存
  const checkBeforeExit = async (): Promise<boolean> => {
    if (!hasUnsavedChanges) {
      return true; // 没有未保存的更改，可以直接退出
    }

    return new Promise((resolve) => {
      Alert.alert(
        '未保存的更改',
        '您有未保存的更改，是否要保存？',
        [
          {
            text: '保存并返回',
            onPress: async () => {
              const saved = await saveNow();
              resolve(saved);
            },
          },
          {
            text: '不保存返回',
            style: 'destructive',
            onPress: () => {
              resolve(true);
            },
          },
        ]
      );
    });
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getCurrentContent: () => {
      return {
        title: latestTitleRef.current,
        content: latestContentRef.current,
        tags: latestTagsRef.current,
      };
    },
    checkBeforeExit,
    saveNow,
  }), [hasUnsavedChanges]);

  // 处理标题变化
  const handleTitleChange = (text: string) => {
    latestTitleRef.current = text;
    setTitle(text);
    setHasUnsavedChanges(true);
    triggerAutoSave();
    onDataChange?.(true);
  };

  // 处理内容变化
  const handleContentChange = (text: string) => {
    latestContentRef.current = text;
    setContent(text);
    setHasUnsavedChanges(true);
    triggerAutoSave();
    onDataChange?.(true);
  };

  // 处理标签切换
  const handleTagToggle = (tagId: string) => {
    const newTags = selectedTags.includes(tagId)
      ? selectedTags.filter(id => id !== tagId)
      : [...selectedTags, tagId];

    latestTagsRef.current = newTags;
    setSelectedTags(newTags);
    setHasUnsavedChanges(true);
    triggerAutoSave();
    onDataChange?.(true);
  };

  // 计算背景线需要的高度，基于内容行数
  const calculateBackgroundHeight = () => {
    const minHeight = 600; // 最小高度
    const contentLines = content.split('\n').length;
    const estimatedHeight = EDITOR_CONSTANTS.PADDING_TOP +
                           (contentLines + 5) * EDITOR_CONSTANTS.LINE_HEIGHT +
                           EDITOR_CONSTANTS.PADDING_BOTTOM;
    return Math.max(minHeight, estimatedHeight);
  };

  return (
    <View style={styles.container}>
      {/* 固定的标题区域 */}
      <View style={styles.titleContainer}>
        <TextInput
          ref={titleInputRef}
          style={styles.titleInput}
          placeholder="笔记标题"
          value={title}
          onChangeText={handleTitleChange}
          maxLength={200}
          placeholderTextColor="#9E9E9E"
          autoFocus={false}
        />
      </View>

      {/* 固定的工具栏 */}
      <View style={styles.toolbar}>
        {/* 创建时间 */}
        {createdTime && (
          <Text style={styles.toolbarText}>
            {createdTime.toLocaleDateString('zh-CN', {
              month: 'short',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </Text>
        )}

        {/* 分隔线 */}
        {createdTime && <View style={styles.separator} />}

        {/* 字数统计 */}
        <Text style={styles.toolbarText}>
          {content.length}字
        </Text>

        {/* 分隔线 */}
        <View style={styles.separator} />

        {/* 标签按钮 */}
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.tagsScrollView}
          contentContainerStyle={styles.tagsScrollContent}>
          {PREDEFINED_TAGS.map(tag => (
            <TouchableOpacity
              key={tag.id}
              style={[
                styles.tagButton,
                { backgroundColor: selectedTags.includes(tag.id) ? tag.color : '#F5F5F5' }
              ]}
              onPress={() => handleTagToggle(tag.id)}>
              <Text style={[
                styles.tagButtonText,
                { color: selectedTags.includes(tag.id) ? '#FFFFFF' : '#666666' }
              ]}>
                {tag.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* 可滚动的编辑区域 */}
      <View style={styles.editorWrapper}>
        <ScrollView
          style={styles.editorScrollView}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
          // 关键：完全禁用键盘相关的自动调整
          automaticallyAdjustContentInsets={false}
          contentInsetAdjustmentBehavior="never"
          keyboardDismissMode="none"
          scrollEnabled={true}
        >
          {/* 正文编辑区域 */}
          <View style={[styles.contentContainer, { minHeight: calculateBackgroundHeight() }]}>
            <View style={styles.editorContainer}>
              <NotePaperBackground
                height={calculateBackgroundHeight()}
                lineHeight={EDITOR_CONSTANTS.LINE_HEIGHT}
              />
              <TextInput
                ref={contentInputRef}
                style={styles.contentInput}
                placeholder="开始记录你的想法..."
                value={content}
                onChangeText={handleContentChange}
                multiline
                textAlignVertical="top"
                placeholderTextColor="#9E9E9E"
                selectionColor="#6750A4"
                cursorColor="#6750A4"
                scrollEnabled={false}
                blurOnSubmit={false}
                returnKeyType="default"
                enablesReturnKeyAutomatically={false}
                // 关键：防止自动滚动和光标问题
                showSoftInputOnFocus={true}
                autoCorrect={false}
                autoCapitalize="none"
                spellCheck={false}
              />
            </View>
          </View>

          {/* 状态栏 */}
          <View style={styles.statusBar}>
            <Text style={styles.statusText}>
              {hasUnsavedChanges ? '有未保存的更改' :
               lastSaved ? `上次保存: ${lastSaved.toLocaleTimeString()}` : ''}
            </Text>
          </View>
        </ScrollView>
      </View>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  editorWrapper: {
    flex: 1,
  },
  editorScrollView: {
    flex: 1,
  },
  titleContainer: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E8E8E8',
  },
  titleInput: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1C1B1F',
    fontFamily: 'LXGWWenKai-Regular',
    padding: 0,
    margin: 0,
    includeFontPadding: false,
  },
  toolbar: {
    backgroundColor: '#FFFFFF',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E8E8E8',
    flexDirection: 'row',
    alignItems: 'center',
  },
  toolbarText: {
    fontSize: 12,
    color: '#666666',
    fontFamily: 'LXGWWenKai-Regular',
  },
  separator: {
    width: 1,
    height: 12,
    backgroundColor: '#E0E0E0',
    marginHorizontal: 8,
  },
  tagsScrollView: {
    flex: 1,
  },
  tagsScrollContent: {
    paddingLeft: 0,
  },
  tagButton: {
    paddingHorizontal: 10,
    paddingVertical: 1,
    borderRadius: 10,
    marginRight: 8,
    borderWidth: 1,
    borderColor: '#E0E0E0',
  },
  tagButtonText: {
    fontSize: 12,
    fontWeight: '500',
    fontFamily: 'LXGWWenKai-Regular',
  },
  contentContainer: {
    flex: 1,
  },
  editorContainer: {
    flex: 1,
    position: 'relative',
  },
  contentInput: {
    flex: 1,
    ...getTextInputBaseStyle(),
    ...getEditorPaddingStyle(),
    backgroundColor: 'transparent',
    zIndex: 1,
    margin: 0,
    // 额外的光标修复设置
    height: undefined,
    minHeight: EDITOR_CONSTANTS.LINE_HEIGHT,
  },
  statusBar: {
    backgroundColor: '#F5F5F5',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderTopWidth: 1,
    borderTopColor: '#E8E8E8',
  },
  statusText: {
    fontSize: 12,
    color: '#9E9E9E',
    fontFamily: 'LXGWWenKai-Regular',
    textAlign: 'center',
  },
});

export default NoteEditor;
