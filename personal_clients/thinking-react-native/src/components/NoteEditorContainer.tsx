import React, {useState, useRef, useEffect, useImperativeHandle, forwardRef} from 'react';
import {View, StyleSheet, Alert} from 'react-native';
import NoteEditor, { NoteEditorRef } from './NoteEditor';
import TimelineNoteEditor, { TimelineNoteEditorRef } from './TimelineNoteEditor';
import { NoteType, TimelineEntry } from '../types';

// 统一的笔记数据接口
export interface NoteData {
  title: string;
  content: string;
  tags: string[];
  noteType: NoteType;
  timelineEntries?: TimelineEntry[];
}

export interface NoteEditorContainerRef {
  getCurrentContent: () => NoteData;
  checkBeforeExit: () => Promise<boolean>;
  saveNow: () => Promise<boolean>;
}

interface NoteEditorContainerProps {
  // 笔记数据
  noteType: NoteType;
  initialTitle?: string;
  initialContent?: string;
  initialTags?: string[];
  initialTimelineEntries?: TimelineEntry[];
  createdTime?: Date;
  
  // 保存函数 - 统一接口
  onSave: (data: NoteData) => Promise<{success: boolean, message?: string}>;
  
  // 事件回调
  onBack?: () => void;
  onDataChange?: (hasChanges: boolean) => void;
  onTypeChange?: (newType: NoteType) => void;
  
  // 配置选项
  autoSaveEnabled?: boolean;
  autoSaveDelay?: number;
  allowTypeChange?: boolean;
}

const NoteEditorContainer = forwardRef<NoteEditorContainerRef, NoteEditorContainerProps>(({
  noteType,
  initialTitle = '',
  initialContent = '',
  initialTags = [],
  initialTimelineEntries = [],
  createdTime,
  onSave,
  onBack,
  onDataChange,
  onTypeChange,
  autoSaveEnabled = true,
  autoSaveDelay = 2000,
  allowTypeChange = false,
}, ref) => {
  
  // 时间线笔记状态
  const [timelineEntries, setTimelineEntries] = useState<TimelineEntry[]>(initialTimelineEntries);
  const [timelineTitle, setTimelineTitle] = useState(initialTitle);
  const [timelineTags, setTimelineTags] = useState<string[]>(initialTags);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // 自动保存相关
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 编辑器引用
  const normalEditorRef = useRef<NoteEditorRef>(null);
  const timelineEditorRef = useRef<TimelineNoteEditorRef>(null);

  // 解析时间线内容
  const parseTimelineContent = (content: string): TimelineEntry[] => {
    if (!content.trim()) return [];

    try {
      // 尝试解析 JSON 格式（新格式）
      const parsed = JSON.parse(content);
      if (Array.isArray(parsed)) {
        return parsed;
      }
    } catch {
      // 如果不是 JSON，尝试解析文本格式（旧格式兼容）
      const lines = content.split('\n').filter(line => line.trim());
      return lines.map((line, index) => {
        // 匹配格式：[时间] 内容 或 [日期时间] 内容
        const match = line.match(/^\[(.+?)\]\s*(.+)$/);
        if (match) {
          return {
            id: `entry-${Date.now()}-${index}`,
            timestamp: match[1],
            content: match[2],
          };
        }
        return {
          id: `entry-${Date.now()}-${index}`,
          timestamp: new Date().toISOString(),
          content: line,
        };
      });
    }

    return [];
  };

  // 序列化时间线内容（使用JSON格式确保数据完整性）
  const serializeTimelineContent = (entries: TimelineEntry[]): string => {
    if (!entries || entries.length === 0) {
      return '';
    }

    try {
      return JSON.stringify(entries, null, 0); // 紧凑格式
    } catch (error) {
      console.error('Error serializing timeline content:', error);
      return '';
    }
  };

  // 初始化时间线数据
  useEffect(() => {
    if (noteType === 'timeline' && initialContent && !initialTimelineEntries.length) {
      const parsedEntries = parseTimelineContent(initialContent);
      setTimelineEntries(parsedEntries);
    }
  }, [noteType, initialContent, initialTimelineEntries]);

  // 清理定时器
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, []);

  // 触发自动保存（仅时间线笔记）
  const triggerTimelineAutoSave = () => {
    if (!autoSaveEnabled || noteType !== 'timeline') return;

    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }

    autoSaveTimeoutRef.current = setTimeout(() => {
      performTimelineAutoSave();
    }, autoSaveDelay);
  };

  // 执行时间线自动保存
  const performTimelineAutoSave = async () => {
    if (noteType !== 'timeline') return;

    try {
      const result = await handleSave({
        title: timelineTitle,
        content: '',
        tags: timelineTags,
      });

      if (result.success) {
        console.log('Timeline auto save successful');
      }
    } catch (error) {
      console.error('Timeline auto save error:', error);
    }
  };

  // 时间线笔记处理函数
  const handleAddTimelineEntry = (entryContent: string) => {
    const newEntry: TimelineEntry = {
      id: Date.now().toString(),
      timestamp: new Date().toISOString(),
      content: entryContent,
    };
    setTimelineEntries(prev => [...prev, newEntry]);
    setHasUnsavedChanges(true);
    onDataChange?.(true);
    triggerTimelineAutoSave();
  };

  const handleUpdateTimelineEntry = (entryId: string, newContent: string) => {
    setTimelineEntries(prev =>
      prev.map(entry =>
        entry.id === entryId ? { ...entry, content: newContent } : entry
      )
    );
    setHasUnsavedChanges(true);
    onDataChange?.(true);
    triggerTimelineAutoSave();
  };

  const handleDeleteTimelineEntry = (entryId: string) => {
    setTimelineEntries(prev => prev.filter(entry => entry.id !== entryId));
    setHasUnsavedChanges(true);
    onDataChange?.(true);
    triggerTimelineAutoSave();
  };

  const handleTimelineTitleChange = (text: string) => {
    setTimelineTitle(text);
    setHasUnsavedChanges(true);
    onDataChange?.(true);
    triggerTimelineAutoSave();
  };

  const handleTimelineTagToggle = (tagId: string) => {
    setTimelineTags(prev => {
      const newTags = prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId];
      setHasUnsavedChanges(true);
      onDataChange?.(true);
      triggerTimelineAutoSave();
      return newTags;
    });
  };

  // 统一的保存函数
  const handleSave = async (editorData: {title: string, content: string, tags: string[]}) => {
    const noteData: NoteData = {
      title: noteType === 'timeline' ? timelineTitle : editorData.title,
      content: noteType === 'timeline' ? serializeTimelineContent(timelineEntries) : editorData.content,
      tags: noteType === 'timeline' ? timelineTags : editorData.tags,
      noteType,
      timelineEntries: noteType === 'timeline' ? timelineEntries : undefined,
    };

    const result = await onSave(noteData);

    if (result.success) {
      setHasUnsavedChanges(false);
      onDataChange?.(false);
    }

    return result;
  };

  // 获取当前内容
  const getCurrentContent = (): NoteData => {
    if (noteType === 'timeline') {
      return {
        title: timelineTitle,
        content: serializeTimelineContent(timelineEntries),
        tags: timelineTags,
        noteType,
        timelineEntries,
      };
    } else {
      const editorContent = normalEditorRef.current?.getCurrentContent();
      return {
        title: editorContent?.title || '',
        content: editorContent?.content || '',
        tags: editorContent?.tags || [],
        noteType,
      };
    }
  };

  // 检查退出前是否需要保存
  const checkBeforeExit = async (): Promise<boolean> => {
    if (noteType === 'timeline') {
      if (!hasUnsavedChanges) {
        return true;
      }

      // 时间线笔记的退出检查逻辑
      return new Promise((resolve) => {
        Alert.alert(
          '未保存的更改',
          '您有未保存的更改，是否要保存？',
          [
            {
              text: '保存并返回',
              onPress: async () => {
                const saved = await saveNow();
                resolve(saved);
              },
            },
            {
              text: '不保存返回',
              style: 'destructive',
              onPress: () => resolve(true),
            },
          ]
        );
      });
    } else {
      return normalEditorRef.current?.checkBeforeExit() || true;
    }
  };

  // 立即保存
  const saveNow = async (): Promise<boolean> => {
    if (noteType === 'timeline') {
      const result = await handleSave({
        title: timelineTitle,
        content: '', // 内容由 handleSave 处理
        tags: timelineTags,
      });
      return result.success;
    } else {
      return normalEditorRef.current?.saveNow() || false;
    }
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getCurrentContent,
    checkBeforeExit,
    saveNow,
  }), [noteType, timelineEntries]);

  // 渲染对应的编辑器
  if (noteType === 'timeline') {
    return (
      <View style={styles.container}>
        <TimelineNoteEditor
          ref={timelineEditorRef}
          title={timelineTitle}
          entries={timelineEntries}
          selectedTags={timelineTags}
          createdTime={createdTime}
          lastSaved={null}
          hasUnsavedChanges={hasUnsavedChanges}
          onTitleChange={handleTimelineTitleChange}
          onAddEntry={handleAddTimelineEntry}
          onUpdateEntry={handleUpdateTimelineEntry}
          onDeleteEntry={handleDeleteTimelineEntry}
          onTagToggle={handleTimelineTagToggle}
        />
      </View>
    );
  } else {
    return (
      <View style={styles.container}>
        <NoteEditor
          ref={normalEditorRef}
          initialTitle={initialTitle}
          initialContent={initialContent}
          initialTags={initialTags}
          createdTime={createdTime}
          onSave={handleSave}
          onBack={onBack}
          onDataChange={onDataChange}
          autoSaveEnabled={autoSaveEnabled}
          autoSaveDelay={autoSaveDelay}
        />
      </View>
    );
  }
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default NoteEditorContainer;