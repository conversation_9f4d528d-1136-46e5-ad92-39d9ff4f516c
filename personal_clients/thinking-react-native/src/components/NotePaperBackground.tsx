import React from 'react';
import {View, StyleSheet, Dimensions} from 'react-native';
import { EDITOR_CONSTANTS } from '../constants/editorConstants';

const {width: screenWidth} = Dimensions.get('window');

interface NotePaperBackgroundProps {
  height: number;
  lineHeight?: number;
  lineColor?: string;
  backgroundColor?: string;
  testID?: string;
}

const NotePaperBackground: React.FC<NotePaperBackgroundProps> = ({
  height,
  lineHeight = EDITOR_CONSTANTS.LINE_HEIGHT,
  lineColor = '#E8E8E8',
  backgroundColor = '#FEFEFE',
  testID,
}) => {
  // 计算需要多少条线
  const numberOfLines = Math.ceil(height / lineHeight);

  const lines = [];
  for (let i = 0; i < numberOfLines; i++) {
    lines.push(
      <View
        key={i}
        style={[
          styles.line,
          {
            // 修复横线位置计算：从顶部padding开始，每行精确对齐，使用可调整的偏移量
            top: EDITOR_CONSTANTS.PADDING_TOP + (i + 1) * lineHeight - EDITOR_CONSTANTS.TEXT_LINE_OFFSET,
            backgroundColor: lineColor,
          },
        ]}
      />
    );
  }

  return (
    <View style={[styles.container, {height, backgroundColor}]} testID={testID}>
      {lines}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    width: '100%',
    left: 0,
    top: 0,
  },
  line: {
    position: 'absolute',
    left: 20,
    right: 20,
    height: 1,
  },
});

export default NotePaperBackground;
