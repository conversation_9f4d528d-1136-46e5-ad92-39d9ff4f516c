import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { NoteType } from '../types';

interface NoteTypeSelectorProps {
  selectedType: NoteType;
  onTypeSelect: (type: NoteType) => void;
}

const NoteTypeSelector: React.FC<NoteTypeSelectorProps> = ({
  selectedType,
  onTypeSelect,
}) => {
  const noteTypes = [
    {
      type: 'normal' as NoteType,
      title: '普通笔记',
      description: '传统的富文本笔记，适合记录想法、文档等',
      icon: '📝',
    },
    {
      type: 'timeline' as NoteType,
      title: '时间线笔记',
      description: '按时间顺序记录的条目，适合日志、进度跟踪等',
      icon: '📅',
    },
  ];

  return (
    <View style={styles.container}>
      <Text style={styles.title}>选择笔记类型</Text>
      <Text style={styles.subtitle}>选择最适合您需求的笔记类型</Text>
      
      <View style={styles.optionsContainer}>
        {noteTypes.map((noteType) => (
          <TouchableOpacity
            key={noteType.type}
            style={[
              styles.optionCard,
              selectedType === noteType.type && styles.selectedCard,
            ]}
            onPress={() => onTypeSelect(noteType.type)}
          >
            <View style={styles.cardHeader}>
              <Text style={styles.cardIcon}>{noteType.icon}</Text>
              <View style={styles.cardTitleContainer}>
                <Text style={[
                  styles.cardTitle,
                  selectedType === noteType.type && styles.selectedCardTitle,
                ]}>
                  {noteType.title}
                </Text>
                {selectedType === noteType.type && (
                  <Text style={styles.selectedIndicator}>✓</Text>
                )}
              </View>
            </View>
            <Text style={[
              styles.cardDescription,
              selectedType === noteType.type && styles.selectedCardDescription,
            ]}>
              {noteType.description}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    color: '#49454F',
    marginBottom: 24,
  },
  optionsContainer: {
    gap: 12,
  },
  optionCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    borderWidth: 2,
    borderColor: '#E6E0E9',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedCard: {
    borderColor: '#6750A4',
    backgroundColor: '#F8F5FF',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  cardIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  cardTitleContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  cardTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1C1B1F',
  },
  selectedCardTitle: {
    color: '#6750A4',
  },
  selectedIndicator: {
    fontSize: 16,
    color: '#6750A4',
    fontWeight: 'bold',
  },
  cardDescription: {
    fontSize: 14,
    color: '#49454F',
    lineHeight: 20,
  },
  selectedCardDescription: {
    color: '#6750A4',
  },
});

export default NoteTypeSelector;
