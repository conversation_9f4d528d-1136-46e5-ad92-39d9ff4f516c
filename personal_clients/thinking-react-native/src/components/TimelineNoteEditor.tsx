import React, { useState, forwardRef, useImperativeHandle } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import { TimelineEntry } from '../types';

export interface TimelineNoteEditorRef {
  getCurrentContent: () => { title: string; entries: TimelineEntry[]; tags: string[] };
}

interface TimelineNoteEditorProps {
  title: string;
  entries: TimelineEntry[];
  selectedTags: string[];
  createdTime?: Date;
  lastSaved?: Date | null;
  hasUnsavedChanges: boolean;
  onTitleChange: (text: string) => void;
  onAddEntry: (content: string) => void;
  onUpdateEntry: (id: string, content: string) => void;
  onDeleteEntry: (id: string) => void;
  onTagToggle: (tagId: string) => void;
}

const TimelineNoteEditor = forwardRef<TimelineNoteEditorRef, TimelineNoteEditorProps>(({
  title,
  entries,
  selectedTags,
  createdTime,
  lastSaved,
  hasUnsavedChanges,
  onTitleChange,
  onAddEntry,
  onUpdateEntry,
  onDeleteEntry,
  onTagToggle,
}, ref) => {
  const [newEntryContent, setNewEntryContent] = useState('');
  const [editingEntryId, setEditingEntryId] = useState<string | null>(null);
  const [editingContent, setEditingContent] = useState('');

  const handleAddEntry = () => {
    if (newEntryContent.trim()) {
      onAddEntry(newEntryContent.trim());
      setNewEntryContent('');
    }
  };

  const handleStartEdit = (entry: TimelineEntry) => {
    setEditingEntryId(entry.id);
    setEditingContent(entry.content);
  };

  const handleSaveEdit = () => {
    if (editingEntryId && editingContent.trim()) {
      onUpdateEntry(editingEntryId, editingContent.trim());
      setEditingEntryId(null);
      setEditingContent('');
    }
  };

  const handleCancelEdit = () => {
    setEditingEntryId(null);
    setEditingContent('');
  };

  const handleDeleteEntry = (entryId: string) => {
    Alert.alert(
      '删除条目',
      '确定要删除这个时间线条目吗？',
      [
        { text: '取消', style: 'cancel' },
        { text: '删除', style: 'destructive', onPress: () => onDeleteEntry(entryId) },
      ]
    );
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const availableTags = ['工作', '学习', '生活', '重要', '想法', '待办'];

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    getCurrentContent: () => ({
      title,
      entries,
      tags: selectedTags,
    }),
  }), [title, entries, selectedTags]);

  return (
    <View style={styles.container}>
      {/* 固定的标题区域 */}
      <View style={styles.titleContainer}>
        <TextInput
          style={styles.titleInput}
          placeholder="时间线笔记标题"
          value={title}
          onChangeText={onTitleChange}
          maxLength={200}
          placeholderTextColor="#9E9E9E"
          autoFocus={false}
        />
      </View>

      {/* 固定的工具栏 */}
      <View style={styles.toolbar}>
        {/* 创建时间 */}
        {createdTime && (
          <Text style={styles.toolbarText}>
            {createdTime.toLocaleDateString('zh-CN', {
              month: 'short',
              day: 'numeric',
              hour: '2-digit',
              minute: '2-digit'
            })}
          </Text>
        )}

        {/* 分隔线 */}
        {createdTime && <View style={styles.separator} />}

        {/* 条目数量 */}
        <Text style={styles.toolbarText}>
          {entries.length}条目
        </Text>

        {/* 分隔线 */}
        <View style={styles.separator} />

        {/* 标签选择 */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.tagsScrollView}>
          {availableTags.map((tag) => (
            <TouchableOpacity
              key={tag}
              style={[
                styles.tagButton,
                selectedTags.includes(tag) && styles.selectedTagButton,
              ]}
              onPress={() => onTagToggle(tag)}
            >
              <Text
                style={[
                  styles.tagButtonText,
                  selectedTags.includes(tag) && styles.selectedTagButtonText,
                ]}
              >
                {tag}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* 时间线内容区域 */}
      <ScrollView style={styles.contentContainer} contentContainerStyle={styles.contentPadding}>
        {/* 时间线条目列表 */}
        {entries.map((entry) => (
          <View key={entry.id} style={styles.timelineEntry}>
            <View style={styles.timelineMarker}>
              <View style={styles.timelineDot} />
              <View style={styles.timelineLine} />
            </View>
            <View style={styles.entryContent}>
              <Text style={styles.entryTimestamp}>{formatTime(entry.timestamp)}</Text>
              {editingEntryId === entry.id ? (
                <View style={styles.editContainer}>
                  <TextInput
                    style={styles.editInput}
                    value={editingContent}
                    onChangeText={setEditingContent}
                    multiline
                    autoFocus
                  />
                  <View style={styles.editActions}>
                    <TouchableOpacity onPress={handleSaveEdit} style={styles.saveButton}>
                      <Text style={styles.saveButtonText}>保存</Text>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={handleCancelEdit} style={styles.cancelButton}>
                      <Text style={styles.cancelButtonText}>取消</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              ) : (
                <View style={styles.entryDisplay}>
                  <Text style={styles.entryText}>{entry.content}</Text>
                  <View style={styles.entryActions}>
                    <TouchableOpacity onPress={() => handleStartEdit(entry)} style={styles.editEntryButton}>
                      <Text style={styles.editEntryButtonText}>编辑</Text>
                    </TouchableOpacity>
                    <TouchableOpacity onPress={() => handleDeleteEntry(entry.id)} style={styles.deleteEntryButton}>
                      <Text style={styles.deleteEntryButtonText}>删除</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            </View>
          </View>
        ))}

        {/* 添加新条目 */}
        <View style={styles.addEntryContainer}>
          <View style={styles.timelineMarker}>
            <View style={styles.timelineDot} />
          </View>
          <View style={styles.addEntryContent}>
            <TextInput
              style={styles.addEntryInput}
              placeholder="添加新的时间线条目..."
              value={newEntryContent}
              onChangeText={setNewEntryContent}
              multiline
              placeholderTextColor="#9E9E9E"
            />
            <TouchableOpacity
              onPress={handleAddEntry}
              style={[styles.addButton, !newEntryContent.trim() && styles.addButtonDisabled]}
              disabled={!newEntryContent.trim()}
            >
              <Text style={styles.addButtonText}>添加</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* 状态栏 */}
        <View style={styles.statusBar}>
          {lastSaved && (
            <Text style={styles.statusText}>
              最后保存: {lastSaved.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}
            </Text>
          )}
          {hasUnsavedChanges && (
            <Text style={styles.unsavedText}>有未保存的更改</Text>
          )}
        </View>
      </ScrollView>
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  titleContainer: {
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E6E0E9',
  },
  titleInput: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1C1B1F',
    padding: 0,
    margin: 0,
  },
  toolbar: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#E6E0E9',
  },
  toolbarText: {
    fontSize: 12,
    color: '#49454F',
  },
  separator: {
    width: 1,
    height: 12,
    backgroundColor: '#CAC4D0',
    marginHorizontal: 8,
  },
  tagsScrollView: {
    flex: 1,
  },
  tagButton: {
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 16,
    backgroundColor: '#F3F0F4',
    marginRight: 8,
  },
  selectedTagButton: {
    backgroundColor: '#E8DEF8',
  },
  tagButtonText: {
    fontSize: 12,
    color: '#49454F',
  },
  selectedTagButtonText: {
    color: '#6750A4',
    fontWeight: '500',
  },
  contentContainer: {
    flex: 1,
  },
  contentPadding: {
    padding: 16,
  },
  timelineEntry: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  timelineMarker: {
    alignItems: 'center',
    marginRight: 12,
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#6750A4',
  },
  timelineLine: {
    width: 2,
    flex: 1,
    backgroundColor: '#E6E0E9',
    marginTop: 4,
  },
  entryContent: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  entryTimestamp: {
    fontSize: 12,
    color: '#6750A4',
    fontWeight: '500',
    marginBottom: 4,
  },
  entryDisplay: {
    flex: 1,
  },
  entryText: {
    fontSize: 14,
    color: '#1C1B1F',
    lineHeight: 20,
    marginBottom: 8,
  },
  entryActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  editEntryButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
  },
  editEntryButtonText: {
    fontSize: 12,
    color: '#6750A4',
  },
  deleteEntryButton: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  deleteEntryButtonText: {
    fontSize: 12,
    color: '#B3261E',
  },
  editContainer: {
    flex: 1,
  },
  editInput: {
    borderWidth: 1,
    borderColor: '#CAC4D0',
    borderRadius: 4,
    padding: 8,
    fontSize: 14,
    color: '#1C1B1F',
    minHeight: 60,
    textAlignVertical: 'top',
    marginBottom: 8,
  },
  editActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  saveButton: {
    backgroundColor: '#6750A4',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
    marginRight: 8,
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  cancelButton: {
    backgroundColor: '#F3F0F4',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 4,
  },
  cancelButtonText: {
    color: '#49454F',
    fontSize: 12,
  },
  addEntryContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  addEntryContent: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 12,
    borderWidth: 2,
    borderColor: '#E6E0E9',
    borderStyle: 'dashed',
  },
  addEntryInput: {
    fontSize: 14,
    color: '#1C1B1F',
    minHeight: 60,
    textAlignVertical: 'top',
    marginBottom: 8,
  },
  addButton: {
    backgroundColor: '#6750A4',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
    alignSelf: 'flex-end',
  },
  addButtonDisabled: {
    backgroundColor: '#CAC4D0',
  },
  addButtonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
  },
  statusBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E6E0E9',
  },
  statusText: {
    fontSize: 12,
    color: '#49454F',
  },
  unsavedText: {
    fontSize: 12,
    color: '#B3261E',
  },
});

export default TimelineNoteEditor;
