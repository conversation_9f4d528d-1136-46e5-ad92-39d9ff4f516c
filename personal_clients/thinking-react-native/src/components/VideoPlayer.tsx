import React, {useState, useRef} from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  Modal,
  StatusBar,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import Video from 'react-native-video';
import FastImage from 'react-native-fast-image';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

interface VideoItem {
  id: number;
  name: string;
  uri: string;
  thumbnailUri?: string;
  duration?: number;
}

interface VideoPlayerProps {
  video: VideoItem;
  visible: boolean;
  onClose: () => void;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  video,
  visible,
  onClose,
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [showControls, setShowControls] = useState(true);
  const videoRef = useRef<Video>(null);

  const handleLoad = (data: any) => {
    setDuration(data.duration);
    setIsLoading(false);
  };

  const handleProgress = (data: any) => {
    setCurrentTime(data.currentTime);
  };

  const handlePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const handleSeek = (time: number) => {
    videoRef.current?.seek(time);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const toggleControls = () => {
    setShowControls(!showControls);
  };

  const renderControls = () => {
    if (!showControls) return null;

    return (
      <View style={styles.controlsContainer}>
        {/* 顶部控制栏 */}
        <View style={styles.topControls}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
          <Text style={styles.videoTitle} numberOfLines={1}>
            {video.name}
          </Text>
        </View>

        {/* 中央播放按钮 */}
        {!isPlaying && (
          <TouchableOpacity
            style={styles.centerPlayButton}
            onPress={handlePlayPause}>
            <Text style={styles.playButtonText}>▶</Text>
          </TouchableOpacity>
        )}

        {/* 底部控制栏 */}
        <View style={styles.bottomControls}>
          <TouchableOpacity
            style={styles.playButton}
            onPress={handlePlayPause}>
            <Text style={styles.controlButtonText}>
              {isPlaying ? '⏸' : '▶'}
            </Text>
          </TouchableOpacity>

          <Text style={styles.timeText}>
            {formatTime(currentTime)}
          </Text>

          <View style={styles.progressContainer}>
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressFill,
                  {width: `${(currentTime / duration) * 100}%`},
                ]}
              />
            </View>
          </View>

          <Text style={styles.timeText}>
            {formatTime(duration)}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}>
      <StatusBar hidden />
      <View style={styles.container}>
        <TouchableOpacity
          style={styles.videoContainer}
          activeOpacity={1}
          onPress={toggleControls}>
          <Video
            ref={videoRef}
            source={{uri: video.uri}}
            style={styles.video}
            resizeMode="contain"
            paused={!isPlaying}
            onLoad={handleLoad}
            onProgress={handleProgress}
            onEnd={() => setIsPlaying(false)}
          />
          
          {isLoading && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color="white" />
              <Text style={styles.loadingText}>加载中...</Text>
            </View>
          )}

          {renderControls()}
        </TouchableOpacity>
      </View>
    </Modal>
  );
};

// 视频缩略图组件
interface VideoThumbnailProps {
  video: VideoItem;
  onPress: () => void;
  size?: number;
}

export const VideoThumbnail: React.FC<VideoThumbnailProps> = ({
  video,
  onPress,
  size = 120,
}) => {
  return (
    <TouchableOpacity
      style={[styles.thumbnailContainer, {width: size, height: size}]}
      onPress={onPress}>
      <FastImage
        source={{
          uri: video.thumbnailUri || 'https://via.placeholder.com/120x120/cccccc/666666?text=Video',
          priority: FastImage.priority.normal,
        }}
        style={styles.thumbnail}
        resizeMode={FastImage.resizeMode.cover}
      />
      <View style={styles.playOverlay}>
        <Text style={styles.playIcon}>▶</Text>
      </View>
      <View style={styles.thumbnailInfo}>
        <Text style={styles.thumbnailTitle} numberOfLines={2}>
          {video.name}
        </Text>
        {video.duration && (
          <Text style={styles.durationText}>
            {Math.floor(video.duration / 60)}:{Math.floor(video.duration % 60).toString().padStart(2, '0')}
          </Text>
        )}
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
    justifyContent: 'center',
    alignItems: 'center',
  },
  videoContainer: {
    width: screenWidth,
    height: screenHeight,
    justifyContent: 'center',
    alignItems: 'center',
  },
  video: {
    width: screenWidth,
    height: screenHeight,
  },
  loadingContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: 'white',
    marginTop: 10,
    fontSize: 16,
  },
  controlsContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'space-between',
  },
  topControls: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingTop: 50,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
  },
  videoTitle: {
    flex: 1,
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 15,
  },
  centerPlayButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playButtonText: {
    color: 'white',
    fontSize: 30,
    marginLeft: 5,
  },
  bottomControls: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 50,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  playButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  controlButtonText: {
    color: 'white',
    fontSize: 20,
  },
  timeText: {
    color: 'white',
    fontSize: 14,
    marginHorizontal: 10,
    minWidth: 40,
  },
  progressContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  progressBar: {
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#007AFF',
    borderRadius: 2,
  },
  // 缩略图样式
  thumbnailContainer: {
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#f5f5f5',
    margin: 5,
  },
  thumbnail: {
    width: '100%',
    height: '70%',
  },
  playOverlay: {
    position: 'absolute',
    top: '20%',
    left: '35%',
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playIcon: {
    color: 'white',
    fontSize: 14,
    marginLeft: 2,
  },
  thumbnailInfo: {
    padding: 8,
    height: '30%',
  },
  thumbnailTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#333',
  },
  durationText: {
    fontSize: 10,
    color: '#666',
    marginTop: 2,
  },
});

export default VideoPlayer;
