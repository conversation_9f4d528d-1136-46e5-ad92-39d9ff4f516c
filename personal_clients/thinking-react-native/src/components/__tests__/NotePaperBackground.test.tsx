import React from 'react';
import {render} from '@testing-library/react-native';
import NotePaperBackground from '../NotePaperBackground';

describe('NotePaperBackground', () => {
  it('renders correctly with default props', () => {
    const {getByTestId} = render(
      <NotePaperBackground height={100} testID="note-paper-background" />
    );
    
    expect(getByTestId('note-paper-background')).toBeTruthy();
  });

  it('renders correct number of lines based on height and lineHeight', () => {
    const height = 100;
    const lineHeight = 20;
    const expectedLines = Math.ceil(height / lineHeight); // 5 lines
    
    const {UNSAFE_getAllByType} = render(
      <NotePaperBackground height={height} lineHeight={lineHeight} />
    );
    
    // The component should render the container plus the expected number of line views
    // We expect 1 container + expectedLines line views
    const views = UNSAFE_getAllByType('View');
    expect(views.length).toBe(expectedLines + 1); // +1 for the container
  });

  it('applies custom colors correctly', () => {
    const customLineColor = '#FF0000';
    const customBackgroundColor = '#00FF00';

    const {getByTestId} = render(
      <NotePaperBackground
        height={100}
        lineColor={customLineColor}
        backgroundColor={customBackgroundColor}
        testID="note-paper-background"
      />
    );

    const container = getByTestId('note-paper-background');
    // The style is an array, so we need to check if any style object contains the backgroundColor
    const styles = Array.isArray(container.props.style) ? container.props.style : [container.props.style];
    const hasBackgroundColor = styles.some(style => style && style.backgroundColor === customBackgroundColor);
    expect(hasBackgroundColor).toBe(true);
  });

  it('handles zero height gracefully', () => {
    const {getByTestId} = render(
      <NotePaperBackground height={0} testID="note-paper-background" />
    );
    
    expect(getByTestId('note-paper-background')).toBeTruthy();
  });

  it('handles very large height values', () => {
    const largeHeight = 10000;
    const {getByTestId} = render(
      <NotePaperBackground height={largeHeight} testID="note-paper-background" />
    );
    
    expect(getByTestId('note-paper-background')).toBeTruthy();
  });
});
