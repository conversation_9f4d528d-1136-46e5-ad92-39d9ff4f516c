/**
 * 编辑器样式常量
 * 统一管理字体大小、行高等样式参数，确保文本输入框与背景横线对齐
 */

export const EDITOR_CONSTANTS = {
  // 基础字体大小
   FONT_SIZE: 16,

  // 行高比例（相对于字体大小）
  LINE_HEIGHT_RATIO: 1.5,

  // 计算得出的行高
  get LINE_HEIGHT() {
    return this.FONT_SIZE * this.LINE_HEIGHT_RATIO; // 24px
  },

  // 内边距设置
  PADDING_TOP: 20,         // 顶部padding，用于对齐第一行
  PADDING_HORIZONTAL: 20,  // 水平padding
  PADDING_BOTTOM: 20,      // 底部padding

  // 文字与背景线的间距调整
  TEXT_LINE_OFFSET: 3,     // 文字相对于背景线的垂直偏移量，可调整文字与线的间距

  // 字体设置
  FONT_FAMILY: 'LXGWWenKai-Regular',

  // 颜色设置
  TEXT_COLOR: '#1C1B1F',
  PLACEHOLDER_COLOR: '#9E9E9E',
  SELECTION_COLOR: '#6750A4',
  CURSOR_COLOR: '#6750A4',
} as const;

/**
 * 获取文本输入框的基础样式
 */
export const getTextInputBaseStyle = () => ({
  fontSize: EDITOR_CONSTANTS.FONT_SIZE,
  lineHeight: EDITOR_CONSTANTS.LINE_HEIGHT,
  fontFamily: EDITOR_CONSTANTS.FONT_FAMILY,
  color: EDITOR_CONSTANTS.TEXT_COLOR,
  includeFontPadding: false,
  textAlignVertical: 'top' as const,
  letterSpacing: 0,
});

/**
 * 获取编辑器内容区域的padding样式
 */
export const getEditorPaddingStyle = () => ({
  paddingHorizontal: EDITOR_CONSTANTS.PADDING_HORIZONTAL,
  paddingTop: EDITOR_CONSTANTS.PADDING_TOP,
  paddingBottom: EDITOR_CONSTANTS.PADDING_BOTTOM,
});
