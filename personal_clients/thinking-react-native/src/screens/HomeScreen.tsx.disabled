import React from 'react';
import {
  View,
  StyleSheet,
  <PERSON>rollView,
  <PERSON><PERSON>,
  RefreshControl,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Button,
  Divider,
  Chip,
  Surface,
  IconButton,
  Text,
} from 'react-native-paper';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Import types
import {RootStackParamList} from '@/types';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList>;

interface QuickAction {
  id: string;
  title: string;
  icon: string;
  action: () => void;
}

interface AppCardProps {
  title: string;
  description: string;
  icon: string;
  color: string;
  onPress: () => void;
  stats?: {
    label: string;
    value: string | number;
  }[];
}

const AppCard: React.FC<AppCardProps> = ({
  title,
  description,
  icon,
  color,
  onPress,
  stats,
}) => (
  <Card style={styles.appCard} onPress={onPress}>
    <Card.Content>
      <View style={styles.cardHeader}>
        <View style={[styles.iconContainer, {backgroundColor: color}]}>
          <Icon name={icon} size={24} color="#FFFFFF" />
        </View>
        <View style={styles.cardTitleContainer}>
          <Title style={styles.cardTitle}>{title}</Title>
          <Paragraph style={styles.cardDescription}>{description}</Paragraph>
        </View>
      </View>
      
      {stats && stats.length > 0 && (
        <>
          <Divider style={styles.cardDivider} />
          <View style={styles.statsContainer}>
            {stats.map((stat, index) => (
              <View key={index} style={styles.statItem}>
                <Text style={styles.statValue}>{stat.value}</Text>
                <Text style={styles.statLabel}>{stat.label}</Text>
              </View>
            ))}
          </View>
        </>
      )}
    </Card.Content>
  </Card>
);

const HomeScreen: React.FC = () => {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const [refreshing, setRefreshing] = React.useState(false);

  // Mock stats data
  const favoritesStats = [
    {label: '收藏', value: 25},
    {label: '分类', value: 8},
  ];

  const tasksStats = [
    {label: '进行中', value: 5},
    {label: '已完成', value: 23},
  ];

  const notesStats = [
    {label: '笔记', value: 12},
    {label: '标签', value: 6},
  ];

  const quickActions: QuickAction[] = [
    {
      id: 'add-favorite',
      title: '添加收藏',
      icon: 'bookmark-add',
      action: () => {
        navigation.navigate('Favorites');
      },
    },
    {
      id: 'new-task',
      title: '新建任务',
      icon: 'add-task',
      action: () => {
        navigation.navigate('Tasks');
      },
    },
    {
      id: 'quick-note',
      title: '快速笔记',
      icon: 'note-add',
      action: () => {
        navigation.navigate('Notes');
      },
    },
    {
      id: 'search',
      title: '全局搜索',
      icon: 'search',
      action: () => {
        Alert.alert('搜索', '全局搜索功能开发中...');
      },
    },
  ];

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  }, []);

  const navigateToApp = (appName: keyof RootStackParamList) => {
    navigation.navigate(appName);
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }>
      
      {/* Welcome Section */}
      <Surface style={styles.welcomeSection}>
        <Title style={styles.welcomeTitle}>欢迎使用 Thinking</Title>
        <Paragraph style={styles.welcomeDescription}>
          整合您的收藏夹、任务管理和笔记，提升工作效率
        </Paragraph>
      </Surface>

      {/* Quick Actions */}
      <View style={styles.quickActionsContainer}>
        <Title style={styles.sectionTitle}>快速操作</Title>
        <View style={styles.quickActionsGrid}>
          {quickActions.map(action => (
            <Surface key={action.id} style={styles.quickActionItem}>
              <IconButton
                icon={action.icon}
                size={24}
                iconColor="#6750A4"
                onPress={action.action}
              />
              <Text style={styles.quickActionText}>{action.title}</Text>
            </Surface>
          ))}
        </View>
      </View>

      {/* App Cards */}
      <View style={styles.appsContainer}>
        <Title style={styles.sectionTitle}>应用</Title>
        
        <AppCard
          title="收藏夹"
          description="管理您的网址收藏和文件索引"
          icon="bookmark"
          color="#FF6B6B"
          stats={favoritesStats}
          onPress={() => navigateToApp('Favorites')}
        />

        <AppCard
          title="任务管理"
          description="高效的任务跟踪和项目管理"
          icon="assignment"
          color="#4ECDC4"
          stats={tasksStats}
          onPress={() => navigateToApp('Tasks')}
        />

        <AppCard
          title="笔记"
          description="记录想法和知识管理"
          icon="note"
          color="#45B7D1"
          stats={notesStats}
          onPress={() => navigateToApp('Notes')}
        />
      </View>

      {/* Recent Activity */}
      <View style={styles.recentSection}>
        <Title style={styles.sectionTitle}>最近活动</Title>
        <Card style={styles.activityCard}>
          <Card.Content>
            <View style={styles.activityItem}>
              <Icon name="bookmark" size={16} color="#FF6B6B" />
              <Text style={styles.activityText}>添加了 3 个收藏项目</Text>
              <Chip mode="outlined" compact>
                2小时前
              </Chip>
            </View>
            <Divider style={styles.activityDivider} />
            <View style={styles.activityItem}>
              <Icon name="check-circle" size={16} color="#4ECDC4" />
              <Text style={styles.activityText}>完成了任务 "实现导航功能"</Text>
              <Chip mode="outlined" compact>
                4小时前
              </Chip>
            </View>
            <Divider style={styles.activityDivider} />
            <View style={styles.activityItem}>
              <Icon name="note" size={16} color="#45B7D1" />
              <Text style={styles.activityText}>创建了新笔记</Text>
              <Chip mode="outlined" compact>
                1天前
              </Chip>
            </View>
          </Card.Content>
        </Card>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          © 2024 Thinking - 让思考更高效
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  welcomeSection: {
    padding: 20,
    marginBottom: 24,
    borderRadius: 12,
    elevation: 2,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 8,
  },
  welcomeDescription: {
    fontSize: 16,
    color: '#49454F',
    lineHeight: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 16,
  },
  quickActionsContainer: {
    marginBottom: 32,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionItem: {
    width: '23%',
    aspectRatio: 1,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    elevation: 1,
  },
  quickActionText: {
    fontSize: 12,
    color: '#49454F',
    textAlign: 'center',
    marginTop: 4,
  },
  appsContainer: {
    marginBottom: 32,
  },
  appCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  cardTitleContainer: {
    flex: 1,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 4,
  },
  cardDescription: {
    fontSize: 14,
    color: '#49454F',
    lineHeight: 20,
  },
  cardDivider: {
    marginVertical: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#6750A4',
  },
  statLabel: {
    fontSize: 12,
    color: '#49454F',
    marginTop: 4,
  },
  recentSection: {
    marginBottom: 32,
  },
  activityCard: {
    borderRadius: 12,
    elevation: 1,
  },
  activityItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
  },
  activityText: {
    flex: 1,
    fontSize: 14,
    color: '#1C1B1F',
    marginLeft: 12,
  },
  activityDivider: {
    marginVertical: 8,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  footerText: {
    fontSize: 12,
    color: '#79747E',
    textAlign: 'center',
  },
});

export default HomeScreen; 