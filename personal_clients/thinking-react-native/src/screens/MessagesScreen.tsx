import React from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Alert,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  List,
  Badge,
  IconButton,
  Divider,
  Switch,
  Text,
  Surface,
  Button,
  Chip,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Import types
import {Message, NotificationSettings} from '@/types';

interface MessageItemProps {
  message: Message;
  onPress: (message: Message) => void;
  onMarkAsRead: (messageId: string) => void;
}

const MessageItem: React.FC<MessageItemProps> = ({
  message,
  onPress,
  onMarkAsRead,
}) => {
  const getMessageIcon = (type: Message['type']) => {
    switch (type) {
      case 'info':
        return 'info';
      case 'warning':
        return 'warning';
      case 'error':
        return 'error';
      case 'success':
        return 'check-circle';
      default:
        return 'notifications';
    }
  };

  const getMessageColor = (type: Message['type']) => {
    switch (type) {
      case 'info':
        return '#2196F3';
      case 'warning':
        return '#FF9800';
      case 'error':
        return '#F44336';
      case 'success':
        return '#4CAF50';
      default:
        return '#6750A4';
    }
  };

  return (
    <Card 
      style={[
        styles.messageCard,
        !message.read && styles.unreadMessage,
      ]}
      onPress={() => onPress(message)}>
      <Card.Content>
        <View style={styles.messageHeader}>
          <View style={styles.messageIcon}>
            <Icon
              name={getMessageIcon(message.type)}
              size={20}
              color={getMessageColor(message.type)}
            />
          </View>
          <View style={styles.messageContent}>
            <Text style={styles.messageTitle}>{message.title}</Text>
            <Text style={styles.messagePreview} numberOfLines={2}>
              {message.content}
            </Text>
            <View style={styles.messageFooter}>
              <Chip mode="outlined" compact>
                {message.source}
              </Chip>
              <Text style={styles.messageTime}>
                {new Date(message.createdAt).toLocaleDateString()}
              </Text>
            </View>
          </View>
          {!message.read && (
            <IconButton
              icon="mark-email-read"
              size={20}
              iconColor="#6750A4"
              onPress={() => onMarkAsRead(message.id)}
            />
          )}
        </View>
      </Card.Content>
    </Card>
  );
};

const MessagesScreen: React.FC = () => {
  const [refreshing, setRefreshing] = React.useState(false);
  const [messages, setMessages] = React.useState<Message[]>([]);
  const [unreadCount, setUnreadCount] = React.useState(0);
  const [notificationSettings, setNotificationSettings] = React.useState<NotificationSettings>({
    enabled: true,
    taskReminders: true,
    favoritesUpdates: false,
    systemMessages: true,
    quietHours: {
      enabled: false,
      start: '22:00',
      end: '08:00',
    },
  });

  // Mock messages data
  React.useEffect(() => {
    const mockMessages: Message[] = [
      {
        id: '1',
        title: '任务提醒',
        content: '您有 3 个任务即将到期，请及时处理。包括"实现React Native导航"、"配置TypeScript"等任务。',
        type: 'warning',
        read: false,
        createdAt: '2024-01-15T14:30:00Z',
        source: 'tasks',
      },
      {
        id: '2',
        title: '收藏夹更新',
        content: '成功添加了新的收藏项目"React Native Paper文档"到Development分类。',
        type: 'success',
        read: false,
        createdAt: '2024-01-15T12:15:00Z',
        source: 'favorites',
      },
      {
        id: '3',
        title: '系统通知',
        content: '应用已成功同步数据，所有功能模块运行正常。下次同步时间：今晚23:00。',
        type: 'info',
        read: true,
        createdAt: '2024-01-15T09:00:00Z',
        source: 'system',
      },
      {
        id: '4',
        title: '磁盘空间警告',
        content: '系统磁盘使用率已达到75%，建议清理不必要的文件以释放空间。',
        type: 'warning',
        read: true,
        createdAt: '2024-01-14T16:45:00Z',
        source: 'system',
      },
      {
        id: '5',
        title: '备份完成',
        content: '所有数据已成功备份到云端，备份文件大小：125MB。',
        type: 'success',
        read: true,
        createdAt: '2024-01-14T02:00:00Z',
        source: 'system',
      },
    ];
    
    setMessages(mockMessages);
    setUnreadCount(mockMessages.filter(msg => !msg.read).length);
  }, []);

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Simulate API call
    setTimeout(() => {
      setRefreshing(false);
    }, 2000);
  }, []);

  const handleMessagePress = (message: Message) => {
    Alert.alert(
      message.title,
      message.content,
      [
        {
          text: '关闭',
          style: 'cancel',
        },
        {
          text: '标记为已读',
          onPress: () => markAsRead(message.id),
        },
      ]
    );
  };

  const markAsRead = (messageId: string) => {
    setMessages(prev =>
      prev.map(msg =>
        msg.id === messageId ? {...msg, read: true} : msg
      )
    );
    setUnreadCount(prev => Math.max(0, prev - 1));
  };

  const markAllAsRead = () => {
    setMessages(prev => prev.map(msg => ({...msg, read: true})));
    setUnreadCount(0);
  };

  const updateNotificationSetting = (
    key: keyof NotificationSettings,
    value: boolean
  ) => {
    setNotificationSettings(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const clearAllMessages = () => {
    Alert.alert(
      '清空消息',
      '确定要删除所有消息吗？此操作不可撤销。',
      [
        {text: '取消', style: 'cancel'},
        {
          text: '确定',
          style: 'destructive',
          onPress: () => {
            setMessages([]);
            setUnreadCount(0);
          },
        },
      ]
    );
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }>
      
      {/* Header Stats */}
      <Surface style={styles.headerStats}>
        <View style={styles.statItem}>
          <Text style={styles.statNumber}>{messages.length}</Text>
          <Text style={styles.statLabel}>总消息</Text>
        </View>
        <Divider style={styles.statDivider} />
        <View style={styles.statItem}>
          <View style={styles.unreadContainer}>
            <Text style={styles.statNumber}>{unreadCount}</Text>
            {unreadCount > 0 && <Badge style={styles.unreadBadge} />}
          </View>
          <Text style={styles.statLabel}>未读</Text>
        </View>
      </Surface>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <Button
          mode="outlined"
          onPress={markAllAsRead}
          disabled={unreadCount === 0}
          style={styles.actionButton}>
          全部已读
        </Button>
        <Button
          mode="outlined"
          onPress={clearAllMessages}
          disabled={messages.length === 0}
          style={styles.actionButton}>
          清空消息
        </Button>
      </View>

      {/* Messages List */}
      <View style={styles.messagesSection}>
        <Title style={styles.sectionTitle}>消息列表</Title>
        {messages.length === 0 ? (
          <Card style={styles.emptyCard}>
            <Card.Content style={styles.emptyContent}>
              <Icon name="inbox" size={48} color="#CAC4D0" />
              <Text style={styles.emptyText}>暂无消息</Text>
              <Text style={styles.emptySubtext}>
                您的通知和系统消息将在这里显示
              </Text>
            </Card.Content>
          </Card>
        ) : (
          messages.map(message => (
            <MessageItem
              key={message.id}
              message={message}
              onPress={handleMessagePress}
              onMarkAsRead={markAsRead}
            />
          ))
        )}
      </View>

      {/* Notification Settings */}
      <View style={styles.settingsSection}>
        <Title style={styles.sectionTitle}>通知设置</Title>
        <Card style={styles.settingsCard}>
          <List.Section>
            <List.Item
              title="启用通知"
              description="接收应用通知和提醒"
              left={props => <List.Icon {...props} icon="notifications" />}
              right={() => (
                <Switch
                  value={notificationSettings.enabled}
                  onValueChange={value => updateNotificationSetting('enabled', value)}
                />
              )}
            />
            <Divider />
            <List.Item
              title="任务提醒"
              description="任务到期和状态变更提醒"
              left={props => <List.Icon {...props} icon="assignment" />}
              right={() => (
                <Switch
                  value={notificationSettings.taskReminders}
                  onValueChange={value => updateNotificationSetting('taskReminders', value)}
                  disabled={!notificationSettings.enabled}
                />
              )}
            />
            <Divider />
            <List.Item
              title="收藏夹更新"
              description="收藏夹变更和同步通知"
              left={props => <List.Icon {...props} icon="bookmark" />}
              right={() => (
                <Switch
                  value={notificationSettings.favoritesUpdates}
                  onValueChange={value => updateNotificationSetting('favoritesUpdates', value)}
                  disabled={!notificationSettings.enabled}
                />
              )}
            />
            <Divider />
            <List.Item
              title="系统消息"
              description="系统状态和维护通知"
              left={props => <List.Icon {...props} icon="settings" />}
              right={() => (
                <Switch
                  value={notificationSettings.systemMessages}
                  onValueChange={value => updateNotificationSetting('systemMessages', value)}
                  disabled={!notificationSettings.enabled}
                />
              )}
            />
          </List.Section>
        </Card>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  headerStats: {
    flexDirection: 'row',
    padding: 20,
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6750A4',
  },
  statLabel: {
    fontSize: 14,
    color: '#49454F',
    marginTop: 4,
  },
  statDivider: {
    width: 1,
    height: 40,
    alignSelf: 'center',
  },
  unreadContainer: {
    position: 'relative',
    alignItems: 'center',
  },
  unreadBadge: {
    position: 'absolute',
    top: -2,
    right: -8,
    backgroundColor: '#F44336',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  actionButton: {
    flex: 0.48,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 16,
  },
  messagesSection: {
    marginBottom: 32,
  },
  messageCard: {
    marginBottom: 12,
    borderRadius: 12,
    elevation: 1,
  },
  unreadMessage: {
    borderLeftWidth: 4,
    borderLeftColor: '#6750A4',
  },
  messageHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  messageIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  messageContent: {
    flex: 1,
  },
  messageTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 4,
  },
  messagePreview: {
    fontSize: 14,
    color: '#49454F',
    lineHeight: 20,
    marginBottom: 8,
  },
  messageFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  messageTime: {
    fontSize: 12,
    color: '#79747E',
  },
  emptyCard: {
    borderRadius: 12,
    elevation: 1,
  },
  emptyContent: {
    alignItems: 'center',
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#49454F',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#79747E',
    textAlign: 'center',
  },
  settingsSection: {
    marginBottom: 32,
  },
  settingsCard: {
    borderRadius: 12,
    elevation: 1,
  },
});

export default MessagesScreen; 