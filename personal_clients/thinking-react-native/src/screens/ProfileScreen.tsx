import React from 'react';
import {
  <PERSON>,
  StyleSheet,
  <PERSON><PERSON><PERSON><PERSON>w,
  <PERSON><PERSON>,
} from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  List,
  Avatar,
  Button,
  Divider,
  Switch,
  Text,
  Surface,
  ProgressBar,
  Chip,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Import types
import {UserProfile, UserPreferences, UserStats} from '@/types';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: string;
  color: string;
  subtitle?: string;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  icon,
  color,
  subtitle,
}) => (
  <Surface style={styles.statCard}>
    <View style={styles.statHeader}>
      <View style={[styles.statIcon, {backgroundColor: color}]}>
        <Icon name={icon} size={20} color="#FFFFFF" />
      </View>
      <View style={styles.statContent}>
        <Text style={styles.statValue}>{value}</Text>
        <Text style={styles.statTitle}>{title}</Text>
        {subtitle && <Text style={styles.statSubtitle}>{subtitle}</Text>}
      </View>
    </View>
  </Surface>
);

const ProfileScreen: React.FC = () => {
  const [userProfile, setUserProfile] = React.useState<UserProfile>({
    id: '1',
    name: '用户',
    email: '<EMAIL>',
    avatar: undefined,
    preferences: {
      theme: 'auto',
      language: 'zh-CN',
      notifications: {
        enabled: true,
        taskReminders: true,
        favoritesUpdates: false,
        systemMessages: true,
        quietHours: {
          enabled: false,
          start: '22:00',
          end: '08:00',
        },
      },
      defaultTaskPriority: 'medium',
      autoArchiveFavorites: false,
      backupEnabled: true,
    },
    stats: {
      totalTasks: 28,
      completedTasks: 23,
      totalFavorites: 45,
      totalTime: 2340, // in minutes
      streakDays: 12,
      joinedAt: '2024-01-10T10:00:00Z',
    },
  });

  const updatePreference = <K extends keyof UserPreferences>(
    key: K,
    value: UserPreferences[K]
  ) => {
    setUserProfile(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [key]: value,
      },
    }));
  };

  const getCompletionRate = () => {
    const {totalTasks, completedTasks} = userProfile.stats;
    return totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}小时${mins}分钟`;
  };

  const formatJoinDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const handleEditProfile = () => {
    Alert.alert('编辑资料', '个人资料编辑功能开发中...');
  };

  const handleBackup = () => {
    Alert.alert(
      '数据备份',
      '确定要立即备份所有数据吗？',
      [
        {text: '取消', style: 'cancel'},
        {
          text: '确定',
          onPress: () => {
            Alert.alert('成功', '数据备份已完成！');
          },
        },
      ]
    );
  };

  const handleExport = () => {
    Alert.alert('导出数据', '数据导出功能开发中...');
  };

  const handleAbout = () => {
    Alert.alert(
      '关于应用',
      'Thinking v1.0.0\n\n一个整合收藏夹、任务管理和笔记功能的高效应用。\n\n© 2024 Thinking'
    );
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}>
      
      {/* Profile Header */}
      <Surface style={styles.profileHeader}>
        <View style={styles.profileInfo}>
          <Avatar.Text
            size={64}
            label={userProfile.name.charAt(0).toUpperCase()}
            style={styles.avatar}
          />
          <View style={styles.profileDetails}>
            <Title style={styles.profileName}>{userProfile.name}</Title>
            <Paragraph style={styles.profileEmail}>{userProfile.email}</Paragraph>
            <Chip mode="outlined" compact style={styles.joinChip}>
              加入于 {formatJoinDate(userProfile.stats.joinedAt)}
            </Chip>
          </View>
        </View>
        <Button
          mode="outlined"
          onPress={handleEditProfile}
          style={styles.editButton}>
          编辑资料
        </Button>
      </Surface>

      {/* Statistics */}
      <View style={styles.statsSection}>
        <Title style={styles.sectionTitle}>统计数据</Title>
        
        <View style={styles.statsGrid}>
          <StatCard
            title="总任务"
            value={userProfile.stats.totalTasks}
            icon="assignment"
            color="#4ECDC4"
          />
          <StatCard
            title="已完成"
            value={userProfile.stats.completedTasks}
            icon="check-circle"
            color="#4CAF50"
          />
        </View>
        
        <View style={styles.statsGrid}>
          <StatCard
            title="收藏数"
            value={userProfile.stats.totalFavorites}
            icon="bookmark"
            color="#FF6B6B"
          />
          <StatCard
            title="连续天数"
            value={userProfile.stats.streakDays}
            icon="local-fire-department"
            color="#FF9800"
          />
        </View>

        <Surface style={styles.progressCard}>
          <View style={styles.progressHeader}>
            <Text style={styles.progressTitle}>任务完成率</Text>
            <Text style={styles.progressValue}>
              {getCompletionRate().toFixed(1)}%
            </Text>
          </View>
          <ProgressBar
            progress={getCompletionRate() / 100}
            color="#6750A4"
            style={styles.progressBar}
          />
          <Text style={styles.progressSubtitle}>
            已完成 {userProfile.stats.completedTasks} / {userProfile.stats.totalTasks} 个任务
          </Text>
        </Surface>

        <StatCard
          title="总投入时间"
          value={formatTime(userProfile.stats.totalTime)}
          icon="schedule"
          color="#9C27B0"
          subtitle="专注工作时间统计"
        />
      </View>

      {/* Preferences */}
      <View style={styles.preferencesSection}>
        <Title style={styles.sectionTitle}>偏好设置</Title>
        <Card style={styles.preferencesCard}>
          <List.Section>
            <List.Item
              title="主题"
              description={
                userProfile.preferences.theme === 'light'
                  ? '浅色模式'
                  : userProfile.preferences.theme === 'dark'
                  ? '深色模式'
                  : '跟随系统'
              }
              left={props => <List.Icon {...props} icon="palette" />}
              right={props => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => {
                Alert.alert('主题设置', '主题设置功能开发中...');
              }}
            />
            <Divider />
            <List.Item
              title="语言"
              description="简体中文"
              left={props => <List.Icon {...props} icon="language" />}
              right={props => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => {
                Alert.alert('语言设置', '语言设置功能开发中...');
              }}
            />
            <Divider />
            <List.Item
              title="默认任务优先级"
              description={
                userProfile.preferences.defaultTaskPriority === 'low'
                  ? '低'
                  : userProfile.preferences.defaultTaskPriority === 'medium'
                  ? '中'
                  : userProfile.preferences.defaultTaskPriority === 'high'
                  ? '高'
                  : '紧急'
              }
              left={props => <List.Icon {...props} icon="flag" />}
              right={props => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => {
                Alert.alert('优先级设置', '优先级设置功能开发中...');
              }}
            />
            <Divider />
            <List.Item
              title="自动归档收藏"
              description="自动归档长时间未访问的收藏"
              left={props => <List.Icon {...props} icon="auto-awesome" />}
              right={() => (
                <Switch
                  value={userProfile.preferences.autoArchiveFavorites}
                  onValueChange={value =>
                    updatePreference('autoArchiveFavorites', value)
                  }
                />
              )}
            />
            <Divider />
            <List.Item
              title="自动备份"
              description="定期备份应用数据"
              left={props => <List.Icon {...props} icon="backup" />}
              right={() => (
                <Switch
                  value={userProfile.preferences.backupEnabled}
                  onValueChange={value => updatePreference('backupEnabled', value)}
                />
              )}
            />
          </List.Section>
        </Card>
      </View>

      {/* Data Management */}
      <View style={styles.dataSection}>
        <Title style={styles.sectionTitle}>数据管理</Title>
        <Card style={styles.dataCard}>
          <List.Section>
            <List.Item
              title="立即备份"
              description="手动备份所有数据"
              left={props => <List.Icon {...props} icon="cloud-upload" />}
              right={props => <List.Icon {...props} icon="chevron-right" />}
              onPress={handleBackup}
            />
            <Divider />
            <List.Item
              title="导出数据"
              description="导出为JSON格式文件"
              left={props => <List.Icon {...props} icon="download" />}
              right={props => <List.Icon {...props} icon="chevron-right" />}
              onPress={handleExport}
            />
            <Divider />
            <List.Item
              title="清除缓存"
              description="清除应用缓存数据"
              left={props => <List.Icon {...props} icon="delete-sweep" />}
              right={props => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => {
                Alert.alert('清除缓存', '缓存清理功能开发中...');
              }}
            />
          </List.Section>
        </Card>
      </View>

      {/* About */}
      <View style={styles.aboutSection}>
        <Title style={styles.sectionTitle}>关于</Title>
        <Card style={styles.aboutCard}>
          <List.Section>
            <List.Item
              title="应用版本"
              description="v1.0.0"
              left={props => <List.Icon {...props} icon="info" />}
              right={props => <List.Icon {...props} icon="chevron-right" />}
              onPress={handleAbout}
            />
            <Divider />
            <List.Item
              title="帮助与反馈"
              description="使用指南和问题反馈"
              left={props => <List.Icon {...props} icon="help" />}
              right={props => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => {
                Alert.alert('帮助', '帮助系统开发中...');
              }}
            />
            <Divider />
            <List.Item
              title="隐私政策"
              description="了解我们如何保护您的隐私"
              left={props => <List.Icon {...props} icon="privacy-tip" />}
              right={props => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => {
                Alert.alert('隐私政策', '隐私政策页面开发中...');
              }}
            />
          </List.Section>
        </Card>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  profileHeader: {
    padding: 20,
    marginBottom: 24,
    borderRadius: 12,
    elevation: 2,
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatar: {
    backgroundColor: '#6750A4',
    marginRight: 16,
  },
  profileDetails: {
    flex: 1,
  },
  profileName: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    color: '#49454F',
    marginBottom: 8,
  },
  joinChip: {
    alignSelf: 'flex-start',
  },
  editButton: {
    alignSelf: 'flex-start',
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 16,
  },
  statsSection: {
    marginBottom: 32,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  statCard: {
    flex: 0.48,
    padding: 16,
    borderRadius: 12,
    elevation: 1,
  },
  statHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  statContent: {
    flex: 1,
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1C1B1F',
  },
  statTitle: {
    fontSize: 12,
    color: '#49454F',
    marginTop: 2,
  },
  statSubtitle: {
    fontSize: 10,
    color: '#79747E',
    marginTop: 2,
  },
  progressCard: {
    padding: 16,
    marginBottom: 12,
    borderRadius: 12,
    elevation: 1,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1C1B1F',
  },
  progressValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#6750A4',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  progressSubtitle: {
    fontSize: 12,
    color: '#49454F',
  },
  preferencesSection: {
    marginBottom: 32,
  },
  preferencesCard: {
    borderRadius: 12,
    elevation: 1,
  },
  dataSection: {
    marginBottom: 32,
  },
  dataCard: {
    borderRadius: 12,
    elevation: 1,
  },
  aboutSection: {
    marginBottom: 32,
  },
  aboutCard: {
    borderRadius: 12,
    elevation: 1,
  },
});

export default ProfileScreen; 