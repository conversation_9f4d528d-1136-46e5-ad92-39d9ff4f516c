import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  StyleSheet,
  FlatList,
  Text,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
  TextInput,
  Modal,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import driveService from '../../services/driveService';
import {DriveFile} from '../../types';
import {COLORS, SPACING} from '../../utils/constants';
import {showToast} from '../../utils/ToastManager';
import ImageViewer from '../../components/ImageViewer';
import VideoPlayer from '../../components/VideoPlayer';

interface DriveListScreenProps {
  parentId?: string | null;
  parentName?: string;
  onBack: () => void;
  onNavigateToFolder: (folderId: string, folderName: string) => void;
  onNavigateToUpload: (parentId?: string | null) => void;
  onNavigateToPreview?: (file: DriveFile) => void;
}

const DriveListScreen: React.FC<DriveListScreenProps> = ({
  parentId,
  parentName,
  onBack,
  onNavigateToFolder,
  onNavigateToUpload,
  onNavigateToPreview,
}) => {
  const [files, setFiles] = useState<DriveFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [showCreateFolder, setShowCreateFolder] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [selectedFile, setSelectedFile] = useState<DriveFile | null>(null);
  const [showFileActions, setShowFileActions] = useState(false);
  const [showImageViewer, setShowImageViewer] = useState(false);
  const [showVideoPlayer, setShowVideoPlayer] = useState(false);
  const [previewImages, setPreviewImages] = useState<any[]>([]);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  const PAGE_SIZE = 20;
  const [currentOffset, setCurrentOffset] = useState(0);

  const loadFiles = useCallback(async (offset = 0, keyword = '', append = false) => {
    try {
      if (!append) {
        setLoading(true);
      } else {
        setLoadingMore(true);
      }

      const response = await driveService.getFileList(
        parentId,
        offset,
        PAGE_SIZE,
        keyword
      );

      if (append) {
        setFiles(prev => [...prev, ...response.items]);
      } else {
        setFiles(response.items);
      }

      setHasMore(response.hasMore);
      setCurrentOffset(offset);
    } catch (error) {
      console.error('Error loading files:', error);
      Alert.alert('错误', '加载文件列表失败，请检查网络连接');
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setRefreshing(false);
    }
  }, [parentId]);

  useEffect(() => {
    loadFiles(0, searchKeyword);
  }, [loadFiles, searchKeyword]);

  const handleRefresh = () => {
    setRefreshing(true);
    setCurrentOffset(0);
    loadFiles(0, searchKeyword);
  };

  const handleLoadMore = () => {
    if (!loadingMore && hasMore) {
      const nextOffset = currentOffset + PAGE_SIZE;
      loadFiles(nextOffset, searchKeyword, true);
    }
  };

  const handleSearch = (text: string) => {
    setSearchKeyword(text);
    setCurrentOffset(0);
  };

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) {
      Alert.alert('错误', '请输入文件夹名称');
      return;
    }

    try {
      const response = await driveService.createFolder({
        name: newFolderName.trim(),
        parentId: parentId,
      });

      if (response.success) {
        showToast('文件夹创建成功', 1000);
        setShowCreateFolder(false);
        setNewFolderName('');
        handleRefresh();
      } else {
        Alert.alert('错误', response.message || '创建文件夹失败');
      }
    } catch (error) {
      console.error('Error creating folder:', error);
      Alert.alert('错误', '创建文件夹失败，请检查网络连接');
    }
  };

  const handleFilePress = (file: DriveFile) => {
    if (file.fileType === 'folder') {
      onNavigateToFolder(file.id, file.name);
    } else if (driveService.isImageFile(file.mimeType)) {
      // 图片预览
      const imageFiles = files.filter(f => driveService.isImageFile(f.mimeType));
      const images = imageFiles.map(f => ({
        id: parseInt(f.id),
        name: f.name,
        uri: driveService.getDownloadUrl(f.id),
        thumbnailUri: driveService.getThumbnailUrl(f.id, 200, 200),
        downloadConfig: driveService.getDownloadUrlWithHeaders(f.id),
        thumbnailConfig: driveService.getThumbnailUrlWithHeaders(f.id, 200, 200),
      }));
      const currentIndex = imageFiles.findIndex(f => f.id === file.id);

      console.log('DriveListScreen: opening image viewer', {
        clickedFileId: file.id,
        clickedFileName: file.name,
        totalImages: images.length,
        currentIndex,
        imageIds: images.map(img => img.id),
      });

      setPreviewImages(images);
      setCurrentImageIndex(Math.max(0, currentIndex));
      setShowImageViewer(true);
    } else if (driveService.isVideoFile(file.mimeType)) {
      // 视频预览
      setSelectedFile(file);
      setShowVideoPlayer(true);
    } else if (onNavigateToPreview) {
      // 对于其他文件，使用专门的预览页面
      onNavigateToPreview(file);
    } else {
      // 对于其他文件，显示操作菜单
      setSelectedFile(file);
      setShowFileActions(true);
    }
  };

  const handleFileLongPress = (file: DriveFile) => {
    setSelectedFile(file);
    setShowFileActions(true);
  };

  const handleDeleteFile = async () => {
    if (!selectedFile) return;

    Alert.alert(
      '确认删除',
      `确定要删除 "${selectedFile.name}" 吗？此操作不可撤销。`,
      [
        {text: '取消', style: 'cancel'},
        {
          text: '删除',
          style: 'destructive',
          onPress: async () => {
            try {
              const response = await driveService.deleteFile(selectedFile.id);
              if (response.success) {
                showToast('删除成功', 1000);
                setShowFileActions(false);
                setSelectedFile(null);
                handleRefresh();
              } else {
                Alert.alert('错误', response.message || '删除失败');
              }
            } catch (error) {
              console.error('Error deleting file:', error);
              Alert.alert('错误', '删除失败，请检查网络连接');
            }
          },
        },
      ]
    );
  };

  const handleDownloadFile = () => {
    if (!selectedFile) return;
    
    const downloadUrl = driveService.getDownloadUrl(selectedFile.id);
    Alert.alert('下载链接', downloadUrl, [
      {text: '取消', style: 'cancel'},
      {text: '复制链接', onPress: () => {
        // 这里可以集成剪贴板功能
        showToast('下载链接已复制', 1000);
      }},
    ]);
    setShowFileActions(false);
  };

  const renderFileItem = ({item}: {item: DriveFile}) => {
    const fileIcon = driveService.getFileIcon(item);
    const fileSize = item.fileType === 'file' ? driveService.formatFileSize(item.size) : '';
    const isPreviewSupported = driveService.isPreviewSupported(item.mimeType);
    const thumbnailUrl = isPreviewSupported ? driveService.getThumbnailUrl(item.id, 60, 60) : null;

    const renderFileIcon = () => {
      if (thumbnailUrl) {
        return (
          <View style={styles.thumbnailContainer}>
            <FastImage
              source={{
                ...driveService.getThumbnailUrlWithHeaders(item.id, 60, 60),
                priority: FastImage.priority.normal,
              }}
              style={styles.thumbnail}
              resizeMode={FastImage.resizeMode.cover}
              fallback={
                <View style={styles.thumbnailFallback}>
                  <Text style={styles.fileIcon}>{fileIcon}</Text>
                </View>
              }
            />
            {driveService.isVideoFile(item.mimeType) && (
              <View style={styles.videoOverlay}>
                <Text style={styles.playIcon}>▶</Text>
              </View>
            )}
          </View>
        );
      }

      return <Text style={styles.fileIcon}>{fileIcon}</Text>;
    };

    return (
      <TouchableOpacity
        style={styles.fileItem}
        onPress={() => handleFilePress(item)}
        onLongPress={() => handleFileLongPress(item)}>
        {renderFileIcon()}
        <View style={styles.fileInfo}>
          <Text style={styles.fileName} numberOfLines={1}>
            {item.name}
          </Text>
          <View style={styles.fileDetails}>
            <Text style={styles.fileSize}>{fileSize}</Text>
            <Text style={styles.fileDate}>
              {new Date(item.updatedAt).toLocaleDateString()}
            </Text>
          </View>
        </View>
        <TouchableOpacity
          style={styles.fileMenuButton}
          onPress={() => handleFileLongPress(item)}>
          <Text style={styles.fileMenuIcon}>⋮</Text>
        </TouchableOpacity>
      </TouchableOpacity>
    );
  };

  const renderHeader = () => (
    <View style={styles.header}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="搜索文件..."
          value={searchKeyword}
          onChangeText={handleSearch}
          placeholderTextColor={COLORS.TEXT_SECONDARY}
        />
      </View>

      {/* Action Buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.actionButton, {backgroundColor: COLORS.SUCCESS}]}
          onPress={() => onNavigateToUpload(parentId)}>
          <Text style={styles.actionButtonText}>⬆️ 上传</Text>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[styles.actionButton, {backgroundColor: COLORS.PRIMARY}]}
          onPress={() => setShowCreateFolder(true)}>
          <Text style={styles.actionButtonText}>📁 新建文件夹</Text>
        </TouchableOpacity>
      </View>

      {/* Breadcrumb */}
      <View style={styles.breadcrumb}>
        <TouchableOpacity onPress={onBack}>
          <Text style={styles.breadcrumbItem}>云盘</Text>
        </TouchableOpacity>
        {parentName && (
          <>
            <Text style={styles.breadcrumbSeparator}> / </Text>
            <Text style={styles.breadcrumbCurrent}>{parentName}</Text>
          </>
        )}
      </View>
    </View>
  );

  const renderFooter = () => {
    if (!loadingMore) return null;
    return (
      <View style={styles.loadingFooter}>
        <ActivityIndicator size="small" color={COLORS.DRIVE_COLOR} />
        <Text style={styles.loadingText}>加载更多...</Text>
      </View>
    );
  };

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>📁</Text>
      <Text style={styles.emptyTitle}>文件夹为空</Text>
      <Text style={styles.emptyDescription}>
        {searchKeyword ? '没有找到匹配的文件' : '开始上传文件或创建文件夹'}
      </Text>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.DRIVE_COLOR} />
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <FlatList
        data={files}
        renderItem={renderFileItem}
        keyExtractor={item => item.id}
        ListHeaderComponent={renderHeader}
        ListFooterComponent={renderFooter}
        ListEmptyComponent={renderEmpty}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        onEndReached={handleLoadMore}
        onEndReachedThreshold={0.1}
        contentContainerStyle={files.length === 0 ? styles.emptyList : undefined}
      />

      {/* Create Folder Modal */}
      <Modal
        visible={showCreateFolder}
        transparent
        animationType="fade"
        onRequestClose={() => setShowCreateFolder(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>新建文件夹</Text>
            <TextInput
              style={styles.modalInput}
              placeholder="输入文件夹名称"
              value={newFolderName}
              onChangeText={setNewFolderName}
              autoFocus
            />
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => {
                  setShowCreateFolder(false);
                  setNewFolderName('');
                }}>
                <Text style={styles.cancelButtonText}>取消</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.modalButton, styles.confirmButton]}
                onPress={handleCreateFolder}>
                <Text style={styles.confirmButtonText}>创建</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>

      {/* File Actions Modal */}
      <Modal
        visible={showFileActions}
        transparent
        animationType="fade"
        onRequestClose={() => setShowFileActions(false)}>
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>{selectedFile?.name}</Text>
            <View style={styles.actionsList}>
              {selectedFile?.fileType === 'file' && (
                <TouchableOpacity
                  style={styles.actionItem}
                  onPress={handleDownloadFile}>
                  <Text style={styles.actionIcon}>⬇️</Text>
                  <Text style={styles.actionText}>下载</Text>
                </TouchableOpacity>
              )}
              <TouchableOpacity
                style={styles.actionItem}
                onPress={() => {
                  setShowFileActions(false);
                  Alert.alert('功能开发中', '重命名功能正在开发中...');
                }}>
                <Text style={styles.actionIcon}>✏️</Text>
                <Text style={styles.actionText}>重命名</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={styles.actionItem}
                onPress={() => {
                  setShowFileActions(false);
                  Alert.alert('功能开发中', '移动功能正在开发中...');
                }}>
                <Text style={styles.actionIcon}>📁</Text>
                <Text style={styles.actionText}>移动</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.actionItem, styles.deleteAction]}
                onPress={handleDeleteFile}>
                <Text style={styles.actionIcon}>🗑️</Text>
                <Text style={[styles.actionText, styles.deleteText]}>删除</Text>
              </TouchableOpacity>
            </View>
            <TouchableOpacity
              style={[styles.modalButton, styles.cancelButton]}
              onPress={() => setShowFileActions(false)}>
              <Text style={styles.cancelButtonText}>取消</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Image Viewer */}
      <ImageViewer
        images={previewImages}
        visible={showImageViewer}
        initialIndex={currentImageIndex}
        onClose={() => setShowImageViewer(false)}
      />

      {/* Video Player */}
      {selectedFile && showVideoPlayer && (
        <VideoPlayer
          video={{
            id: parseInt(selectedFile.id),
            name: selectedFile.name,
            uri: driveService.getDownloadUrl(selectedFile.id),
            thumbnailUri: selectedFile.thumbnailPath ? driveService.getThumbnailUrl(selectedFile.id, 200, 200) : undefined,
          }}
          visible={showVideoPlayer}
          onClose={() => {
            setShowVideoPlayer(false);
            setSelectedFile(null);
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
  },
  loadingText: {
    marginTop: SPACING.SM,
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
  },
  header: {
    padding: SPACING.MD,
    backgroundColor: COLORS.SURFACE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  searchContainer: {
    marginBottom: SPACING.MD,
  },
  searchInput: {
    backgroundColor: COLORS.BACKGROUND,
    borderRadius: 8,
    padding: SPACING.SM,
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    borderWidth: 1,
    borderColor: COLORS.BORDER,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.MD,
  },
  actionButton: {
    flex: 1,
    padding: SPACING.SM,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: SPACING.XS,
  },
  actionButtonText: {
    color: COLORS.SURFACE,
    fontSize: 14,
    fontWeight: 'bold',
  },
  breadcrumb: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  breadcrumbItem: {
    fontSize: 14,
    color: COLORS.PRIMARY,
  },
  breadcrumbSeparator: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
  },
  breadcrumbCurrent: {
    fontSize: 14,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: 'bold',
  },
  fileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.MD,
    backgroundColor: COLORS.SURFACE,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  fileIcon: {
    fontSize: 24,
    marginRight: SPACING.MD,
  },
  thumbnailContainer: {
    width: 48,
    height: 48,
    marginRight: SPACING.MD,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: COLORS.BACKGROUND_SECONDARY,
    position: 'relative',
  },
  thumbnail: {
    width: '100%',
    height: '100%',
  },
  thumbnailFallback: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND_SECONDARY,
  },
  videoOverlay: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    width: 16,
    height: 16,
    marginTop: -8,
    marginLeft: -8,
    borderRadius: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playIcon: {
    color: 'white',
    fontSize: 10,
    marginLeft: 1,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    fontWeight: '500',
    marginBottom: 2,
  },
  fileDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  fileSize: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
  },
  fileDate: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
  },
  fileMenuButton: {
    padding: SPACING.SM,
  },
  fileMenuIcon: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
  },
  loadingFooter: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.MD,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.XL,
  },
  emptyList: {
    flexGrow: 1,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: SPACING.MD,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  emptyDescription: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: SPACING.LG,
    width: '80%',
    maxWidth: 400,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
    textAlign: 'center',
  },
  modalInput: {
    borderWidth: 1,
    borderColor: COLORS.BORDER,
    borderRadius: 8,
    padding: SPACING.SM,
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    padding: SPACING.SM,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: SPACING.XS,
  },
  cancelButton: {
    backgroundColor: COLORS.BORDER,
  },
  confirmButton: {
    backgroundColor: COLORS.PRIMARY,
  },
  cancelButtonText: {
    color: COLORS.TEXT_SECONDARY,
    fontWeight: 'bold',
  },
  confirmButtonText: {
    color: COLORS.SURFACE,
    fontWeight: 'bold',
  },
  actionsList: {
    marginBottom: SPACING.MD,
  },
  actionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.MD,
    borderRadius: 8,
    marginBottom: SPACING.XS,
  },
  actionIcon: {
    fontSize: 20,
    marginRight: SPACING.MD,
  },
  actionText: {
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
  },
  deleteAction: {
    backgroundColor: COLORS.ERROR + '20',
  },
  deleteText: {
    color: COLORS.ERROR,
  },
});

export default DriveListScreen;
