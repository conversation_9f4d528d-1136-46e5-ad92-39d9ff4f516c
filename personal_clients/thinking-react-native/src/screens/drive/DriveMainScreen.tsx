import React, {useState, useEffect} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Text,
  TouchableOpacity,
  Alert,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import driveService from '../../services/driveService';
import {DriveStorageInfo} from '../../types';
import {COLORS, SPACING} from '../../utils/constants';

interface DriveMainScreenProps {
  onNavigateToList: () => void;
  onNavigateToUpload: () => void;
}

const DriveMainScreen: React.FC<DriveMainScreenProps> = ({
  onNavigateToList,
  onNavigateToUpload,
}) => {
  const [storageInfo, setStorageInfo] = useState<DriveStorageInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const loadStorageInfo = async () => {
    try {
      const response = await driveService.getStorageInfo();
      if (response.success) {
        setStorageInfo(response.data);
      } else {
        Alert.alert('错误', response.message || '获取存储信息失败');
      }
    } catch (error) {
      console.error('Error loading storage info:', error);
      Alert.alert('错误', '获取存储信息失败，请检查网络连接');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    loadStorageInfo();
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    loadStorageInfo();
  };

  const formatStorageSize = (bytes: number): string => {
    return driveService.formatFileSize(bytes);
  };

  const getUsageColor = (percent: number): string => {
    if (percent < 50) return COLORS.SUCCESS;
    if (percent < 80) return COLORS.WARNING;
    return COLORS.ERROR;
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={COLORS.DRIVE_COLOR} />
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }>
      
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={[styles.iconContainer, {backgroundColor: COLORS.DRIVE_COLOR}]}>
            <Text style={styles.iconText}>☁️</Text>
          </View>
          <View style={styles.headerText}>
            <Text style={styles.headerTitle}>云盘存储</Text>
            <Text style={styles.headerDescription}>
              安全可靠的文件存储和管理
            </Text>
          </View>
        </View>
      </View>

      {/* Storage Info Card */}
      {storageInfo && (
        <View style={styles.storageCard}>
          <Text style={styles.storageTitle}>存储空间</Text>
          
          <View style={styles.storageBar}>
            <View 
              style={[
                styles.storageProgress, 
                {
                  width: `${storageInfo.usagePercent}%`,
                  backgroundColor: getUsageColor(storageInfo.usagePercent),
                }
              ]} 
            />
          </View>
          
          <View style={styles.storageDetails}>
            <Text style={styles.storageText}>
              已使用 {formatStorageSize(storageInfo.usedSpace)} / 总计 {formatStorageSize(storageInfo.totalSpace)}
            </Text>
            <Text style={[styles.storagePercent, {color: getUsageColor(storageInfo.usagePercent)}]}>
              {storageInfo.usagePercent.toFixed(1)}%
            </Text>
          </View>
          
          <Text style={styles.freeSpaceText}>
            可用空间：{formatStorageSize(storageInfo.freeSpace)}
          </Text>
        </View>
      )}

      {/* Quick Actions */}
      <View style={styles.actionsContainer}>
        <Text style={styles.sectionTitle}>快捷操作</Text>
        
        <TouchableOpacity
          style={[styles.actionButton, {backgroundColor: COLORS.PRIMARY}]}
          onPress={onNavigateToList}>
          <Text style={styles.actionIcon}>📁</Text>
          <View style={styles.actionContent}>
            <Text style={styles.actionTitle}>浏览文件</Text>
            <Text style={styles.actionDescription}>查看和管理您的文件</Text>
          </View>
          <Text style={styles.actionArrow}>›</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, {backgroundColor: COLORS.SUCCESS}]}
          onPress={onNavigateToUpload}>
          <Text style={styles.actionIcon}>⬆️</Text>
          <View style={styles.actionContent}>
            <Text style={styles.actionTitle}>上传文件</Text>
            <Text style={styles.actionDescription}>添加新文件到云盘</Text>
          </View>
          <Text style={styles.actionArrow}>›</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, {backgroundColor: COLORS.INFO}]}
          onPress={() => Alert.alert('功能开发中', '文件分享功能正在开发中...')}>
          <Text style={styles.actionIcon}>🔗</Text>
          <View style={styles.actionContent}>
            <Text style={styles.actionTitle}>分享管理</Text>
            <Text style={styles.actionDescription}>管理文件分享链接</Text>
          </View>
          <Text style={styles.actionArrow}>›</Text>
        </TouchableOpacity>
      </View>

      {/* Features */}
      <View style={styles.featuresContainer}>
        <Text style={styles.sectionTitle}>功能特性</Text>
        
        <View style={styles.featuresList}>
          <View style={styles.featureItem}>
            <Text style={styles.featureIcon}>🔒</Text>
            <Text style={styles.featureText}>安全加密存储</Text>
          </View>
          <View style={styles.featureItem}>
            <Text style={styles.featureIcon}>📱</Text>
            <Text style={styles.featureText}>多设备同步</Text>
          </View>
          <View style={styles.featureItem}>
            <Text style={styles.featureIcon}>🔍</Text>
            <Text style={styles.featureText}>智能搜索</Text>
          </View>
          <View style={styles.featureItem}>
            <Text style={styles.featureIcon}>📤</Text>
            <Text style={styles.featureText}>便捷分享</Text>
          </View>
        </View>
      </View>

      {/* Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>-</Text>
          <Text style={styles.statLabel}>文件总数</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>-</Text>
          <Text style={styles.statLabel}>文件夹数</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>-</Text>
          <Text style={styles.statLabel}>分享链接</Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  contentContainer: {
    padding: SPACING.MD,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
  },
  loadingText: {
    marginTop: SPACING.SM,
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
  },
  header: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: SPACING.LG,
    marginBottom: SPACING.MD,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.MD,
  },
  iconText: {
    fontSize: 24,
  },
  headerText: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 4,
  },
  headerDescription: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
  },
  storageCard: {
    backgroundColor: COLORS.SURFACE,
    borderRadius: 12,
    padding: SPACING.LG,
    marginBottom: SPACING.MD,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  storageTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  storageBar: {
    height: 8,
    backgroundColor: COLORS.BORDER,
    borderRadius: 4,
    marginBottom: SPACING.SM,
  },
  storageProgress: {
    height: '100%',
    borderRadius: 4,
  },
  storageDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.XS,
  },
  storageText: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
  },
  storagePercent: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  freeSpaceText: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
  },
  actionsContainer: {
    marginBottom: SPACING.LG,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: SPACING.MD,
    borderRadius: 12,
    marginBottom: SPACING.SM,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  actionIcon: {
    fontSize: 24,
    marginRight: SPACING.MD,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.SURFACE,
    marginBottom: 2,
  },
  actionDescription: {
    fontSize: 12,
    color: COLORS.SURFACE,
    opacity: 0.8,
  },
  actionArrow: {
    fontSize: 24,
    color: COLORS.SURFACE,
    opacity: 0.7,
  },
  featuresContainer: {
    marginBottom: SPACING.LG,
  },
  featuresList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  featureItem: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.SURFACE,
    padding: SPACING.MD,
    borderRadius: 8,
    marginBottom: SPACING.SM,
  },
  featureIcon: {
    fontSize: 20,
    marginRight: SPACING.SM,
  },
  featureText: {
    fontSize: 14,
    color: COLORS.TEXT_PRIMARY,
    flex: 1,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statCard: {
    flex: 1,
    backgroundColor: COLORS.SURFACE,
    padding: SPACING.MD,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: SPACING.XS,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.DRIVE_COLOR,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
  },
});

export default DriveMainScreen;
