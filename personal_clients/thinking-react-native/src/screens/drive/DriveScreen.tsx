import React, {useState, useEffect} from 'react';
import {BackHandler} from 'react-native';
import DriveMainScreen from './DriveMainScreen';
import DriveListScreen from './DriveListScreen';
import DriveUploadScreen from './DriveUploadScreen';
import FilePreviewScreen from './FilePreviewScreen';
import {DriveFile} from '../../types';

type DriveView = 'main' | 'list' | 'upload' | 'preview';

interface DriveScreenProps {
  onBack?: () => void;
}

interface NavigationState {
  view: DriveView;
  parentId?: string | null;
  parentName?: string;
  file?: DriveFile;
}

const DriveScreen: React.FC<DriveScreenProps> = ({onBack}) => {
  const [navigationStack, setNavigationStack] = useState<NavigationState[]>([
    {view: 'main'},
  ]);

  const currentState = navigationStack[navigationStack.length - 1];

  // 处理Android返回键
  useEffect(() => {
    const backHandler = BackHandler.addEventListener('hardwareBackPress', () => {
      if (navigationStack.length > 1) {
        handleBack();
        return true; // 阻止默认行为
      }
      return false; // 让系统处理
    });

    return () => backHandler.remove();
  }, [navigationStack.length]);

  const handleBack = () => {
    if (navigationStack.length > 1) {
      setNavigationStack(prev => prev.slice(0, -1));
    } else if (onBack) {
      onBack();
    }
  };

  const handleNavigateToList = (parentId?: string | null, parentName?: string) => {
    setNavigationStack(prev => [
      ...prev,
      {
        view: 'list',
        parentId,
        parentName,
      },
    ]);
  };

  const handleNavigateToUpload = (parentId?: string | null) => {
    setNavigationStack(prev => [
      ...prev,
      {
        view: 'upload',
        parentId,
      },
    ]);
  };

  const handleNavigateToFolder = (folderId: string, folderName: string) => {
    setNavigationStack(prev => [
      ...prev,
      {
        view: 'list',
        parentId: folderId,
        parentName: folderName,
      },
    ]);
  };

  const handleUploadSuccess = () => {
    // 上传成功后返回到文件列表页面
    if (navigationStack.length > 1) {
      setNavigationStack(prev => prev.slice(0, -1));
    }
  };

  const handleNavigateToPreview = (file: DriveFile) => {
    setNavigationStack(prev => [
      ...prev,
      {
        view: 'preview',
        file,
      },
    ]);
  };

  // 渲染当前页面
  const renderCurrentView = () => {
    switch (currentState.view) {
      case 'main':
        return (
          <DriveMainScreen
            onNavigateToList={() => handleNavigateToList()}
            onNavigateToUpload={() => handleNavigateToUpload()}
          />
        );

      case 'list':
        return (
          <DriveListScreen
            parentId={currentState.parentId}
            parentName={currentState.parentName}
            onBack={handleBack}
            onNavigateToFolder={handleNavigateToFolder}
            onNavigateToUpload={handleNavigateToUpload}
            onNavigateToPreview={handleNavigateToPreview}
          />
        );

      case 'upload':
        return (
          <DriveUploadScreen
            parentId={currentState.parentId}
            onBack={handleBack}
            onUploadSuccess={handleUploadSuccess}
          />
        );

      case 'preview':
        return currentState.file ? (
          <FilePreviewScreen
            route={{params: {file: currentState.file}}}
            navigation={{
              setOptions: () => {},
              goBack: handleBack,
            } as any}
          />
        ) : null;

      default:
        return (
          <DriveMainScreen
            onNavigateToList={() => handleNavigateToList()}
            onNavigateToUpload={() => handleNavigateToUpload()}
          />
        );
    }
  };

  return renderCurrentView();
};

export default DriveScreen;
