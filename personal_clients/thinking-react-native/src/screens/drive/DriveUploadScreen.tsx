import React, {useState} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Text,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';
import DocumentPicker from 'react-native-document-picker';
import driveService from '../../services/driveService';
import {COLORS, SPACING} from '../../utils/constants';
import {useToast} from '../../utils/ToastManager';

interface DriveUploadScreenProps {
  parentId?: string | null;
  onBack: () => void;
  onUploadSuccess: () => void;
}

interface SelectedFile {
  uri: string;
  name: string;
  type: string;
  size: number;
}

const DriveUploadScreen: React.FC<DriveUploadScreenProps> = ({
  parentId,
  onBack,
  onUploadSuccess,
}) => {
  const [selectedFiles, setSelectedFiles] = useState<SelectedFile[]>([]);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{[key: string]: number}>({});
  const {showToast} = useToast();

  const handleSelectFiles = () => {
    DocumentPicker.pick({
      type: [DocumentPicker.types.allFiles],
      allowMultiSelection: true,
    })
    .then((results) => {
      const files: SelectedFile[] = results.map(result => ({
        uri: result.uri,
        name: result.name || 'unknown',
        type: result.type || 'application/octet-stream',
        size: result.size || 0,
      }));

      setSelectedFiles(prev => [...prev, ...files]);
    })
    .catch((error) => {
      if (DocumentPicker.isCancel(error)) {
        // 用户取消选择
        return;
      }
      console.error('Error selecting files:', error);
      Alert.alert('错误', '选择文件失败');
    });
  };

  const handleRemoveFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
    // 同时清除该文件的进度信息
    const file = selectedFiles[index];
    if (file) {
      setUploadProgress(prev => {
        const newProgress = {...prev};
        delete newProgress[file.name];
        return newProgress;
      });
    }
  };

  const handleUploadFiles = async () => {
    if (selectedFiles.length === 0) {
      Alert.alert('提示', '请先选择要上传的文件');
      return;
    }

    setUploading(true);
    let successCount = 0;
    let failCount = 0;

    for (let i = 0; i < selectedFiles.length; i++) {
      const file = selectedFiles[i];

      try {
        // 初始化进度
        setUploadProgress(prev => ({
          ...prev,
          [file.name]: 0,
        }));

        // 使用分片上传
        const response = await driveService.uploadFileWithProgress({
          fileName: file.name,
          fileSize: file.size,
          parentId: parentId,
          fileData: {
            uri: file.uri,
            type: file.type,
            name: file.name,
          },
          chunkSize: 256 * 1024, // 256KB 分片大小，更适合小文件
          onProgress: (progress) => {
            setUploadProgress(prev => ({
              ...prev,
              [file.name]: progress.percentage,
            }));
          },
        });

        if (response.success) {
          successCount++;
          // 确保进度显示为100%
          setUploadProgress(prev => ({
            ...prev,
            [file.name]: 100,
          }));
        } else {
          failCount++;
          console.error('Upload failed:', response.message);
          showToast(
            `${file.name} 上传失败: ${response.message}`,
            'error',
            3000
          );
        }
      } catch (error) {
        failCount++;
        console.error('Error uploading file:', error);
        showToast(
          `${file.name} 上传出错`,
          'error',
          3000
        );
      }
    }

    setUploading(false);

    if (successCount > 0) {
      showToast(
        `成功上传 ${successCount} 个文件${failCount > 0 ? `，${failCount} 个失败` : ''}`,
        'success',
        3000
      );
      onUploadSuccess();
    } else if (failCount > 0) {
      showToast(
        '所有文件上传失败',
        'error',
        3000
      );
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(2) + ' KB';
    if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(2) + ' MB';
    return (bytes / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
  };

  const getTotalSize = (): number => {
    return selectedFiles.reduce((total, file) => total + file.size, 0);
  };

  const getFileIcon = (type: string): string => {
    if (type.startsWith('image/')) return '🖼️';
    if (type.startsWith('video/')) return '🎥';
    if (type.startsWith('audio/')) return '🎵';
    if (type.includes('pdf')) return '📄';
    if (type.includes('word')) return '📝';
    if (type.includes('excel') || type.includes('spreadsheet')) return '📊';
    if (type.includes('powerpoint') || type.includes('presentation')) return '📽️';
    if (type.includes('zip') || type.includes('rar') || type.includes('7z')) return '📦';
    if (type.includes('text')) return '📝';
    return '📄';
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>上传文件</Text>
          <Text style={styles.headerDescription}>
            选择要上传到云盘的文件
          </Text>
        </View>

        {/* Upload Area */}
        <TouchableOpacity
          style={styles.uploadArea}
          onPress={handleSelectFiles}
          disabled={uploading}>
          <Text style={styles.uploadIcon}>📁</Text>
          <Text style={styles.uploadTitle}>选择文件</Text>
          <Text style={styles.uploadDescription}>
            点击选择要上传的文件
          </Text>
        </TouchableOpacity>

        {/* Selected Files */}
        {selectedFiles.length > 0 && (
          <View style={styles.filesContainer}>
            <View style={styles.filesHeader}>
              <Text style={styles.filesTitle}>
                已选择 {selectedFiles.length} 个文件
              </Text>
              <Text style={styles.filesSize}>
                总大小：{formatFileSize(getTotalSize())}
              </Text>
            </View>

            {selectedFiles.map((file, index) => (
              <View key={index} style={styles.fileItem}>
                <Text style={styles.fileIcon}>{getFileIcon(file.type)}</Text>
                <View style={styles.fileInfo}>
                  <Text style={styles.fileName} numberOfLines={1}>
                    {file.name}
                  </Text>
                  <Text style={styles.fileSize}>
                    {formatFileSize(file.size)}
                  </Text>
                  
                  {/* Upload Progress */}
                  {uploading && uploadProgress[file.name] !== undefined && (
                    <View style={styles.progressContainer}>
                      <View style={styles.progressBar}>
                        <View 
                          style={[
                            styles.progressFill,
                            {width: `${uploadProgress[file.name]}%`}
                          ]} 
                        />
                      </View>
                      <Text style={styles.progressText}>
                        {uploadProgress[file.name].toFixed(1)}%
                      </Text>
                    </View>
                  )}
                </View>
                
                {!uploading && (
                  <TouchableOpacity
                    style={styles.removeButton}
                    onPress={() => handleRemoveFile(index)}>
                    <Text style={styles.removeIcon}>✕</Text>
                  </TouchableOpacity>
                )}
              </View>
            ))}
          </View>
        )}

        {/* Upload Tips */}
        <View style={styles.tipsContainer}>
          <Text style={styles.tipsTitle}>上传说明</Text>
          <Text style={styles.tipItem}>• 支持所有类型的文件</Text>
          <Text style={styles.tipItem}>• 大文件会自动使用分片上传</Text>
          <Text style={styles.tipItem}>• 可同时选择多个文件</Text>
          <Text style={styles.tipItem}>• 上传过程中请保持网络连接</Text>
        </View>
      </ScrollView>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <TouchableOpacity
          style={[styles.actionButton, styles.cancelButton]}
          onPress={onBack}
          disabled={uploading}>
          <Text style={styles.cancelButtonText}>取消</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.actionButton,
            styles.uploadButton,
            (selectedFiles.length === 0 || uploading) && styles.disabledButton,
          ]}
          onPress={handleUploadFiles}
          disabled={selectedFiles.length === 0 || uploading}>
          {uploading ? (
            <View style={styles.uploadingContainer}>
              <ActivityIndicator size="small" color={COLORS.SURFACE} />
              <Text style={styles.uploadButtonText}>上传中...</Text>
            </View>
          ) : (
            <Text style={styles.uploadButtonText}>
              开始上传 ({selectedFiles.length})
            </Text>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.SURFACE,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: SPACING.MD,
  },
  header: {
    marginBottom: SPACING.LG,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  headerDescription: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
  },
  uploadArea: {
    borderWidth: 2,
    borderColor: COLORS.BORDER,
    borderStyle: 'dashed',
    borderRadius: 12,
    padding: SPACING.XL,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.LG,
  },
  uploadIcon: {
    fontSize: 48,
    marginBottom: SPACING.XS,
  },
  uploadTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  uploadDescription: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
  },
  filesContainer: {
    marginBottom: SPACING.LG,
  },
  filesHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.MD,
  },
  filesTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
  },
  filesSize: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
  },
  fileItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.BACKGROUND,
    borderRadius: 8,
    padding: SPACING.MD,
    marginBottom: SPACING.XS,
  },
  fileIcon: {
    fontSize: 24,
    marginRight: SPACING.MD,
  },
  fileInfo: {
    flex: 1,
  },
  fileName: {
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
    marginBottom: 4,
  },
  fileSize: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
  },
  progressContainer: {
    marginTop: SPACING.XS,
    flexDirection: 'row',
    alignItems: 'center',
  },
  progressBar: {
    flex: 1,
    height: 4,
    backgroundColor: COLORS.BORDER,
    borderRadius: 2,
    marginRight: SPACING.XS,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: COLORS.SUCCESS,
    borderRadius: 2,
  },
  progressText: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
    width: 45,
    textAlign: 'right',
  },
  removeButton: {
    padding: SPACING.XS,
  },
  removeIcon: {
    fontSize: 18,
    color: COLORS.ERROR,
  },
  tipsContainer: {
    backgroundColor: COLORS.BACKGROUND,
    borderRadius: 8,
    padding: SPACING.MD,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  tipItem: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  bottomActions: {
    flexDirection: 'row',
    padding: SPACING.MD,
    borderTopWidth: 1,
    borderTopColor: COLORS.BORDER,
    backgroundColor: COLORS.SURFACE,
  },
  actionButton: {
    flex: 1,
    height: 44,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cancelButton: {
    marginRight: SPACING.MD,
    backgroundColor: COLORS.BACKGROUND,
  },
  cancelButtonText: {
    color: COLORS.TEXT_PRIMARY,
    fontSize: 16,
  },
  uploadButton: {
    backgroundColor: COLORS.SUCCESS,
  },
  uploadButtonText: {
    color: COLORS.SURFACE,
    fontSize: 16,
    fontWeight: 'bold',
  },
  disabledButton: {
    opacity: 0.5,
  },
  uploadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default DriveUploadScreen;
