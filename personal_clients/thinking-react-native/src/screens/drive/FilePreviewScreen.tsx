import React, {useState, useEffect} from 'react';
import {
  View,
  StyleSheet,
  Text,
  TouchableOpacity,
  StatusBar,
  Alert,
  ActivityIndicator,
  ScrollView,
  Dimensions,
} from 'react-native';
import {useRoute, useNavigation, RouteProp} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import FastImage from 'react-native-fast-image';
import Video from 'react-native-video';
import driveService from '../../services/driveService';
import {DriveFile} from '../../types';
import {COLORS, SPACING} from '../../utils/constants';
import {showToast} from '../../utils/ToastManager';

const {width: screenWidth, height: screenHeight} = Dimensions.get('window');

type RootStackParamList = {
  FilePreview: {
    file: DriveFile;
  };
};

type FilePreviewScreenRouteProp = RouteProp<RootStackParamList, 'FilePreview'>;
type FilePreviewScreenNavigationProp = StackNavigationProp<RootStackParamList, 'FilePreview'>;

interface Props {
  route: FilePreviewScreenRouteProp;
  navigation: FilePreviewScreenNavigationProp;
}

const FilePreviewScreen: React.FC<Props> = ({route, navigation}) => {
  const {file} = route.params;
  const [loading, setLoading] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);

  useEffect(() => {
    navigation.setOptions({
      title: file.name,
      headerRight: () => (
        <TouchableOpacity
          style={styles.headerButton}
          onPress={handleDownload}>
          <Text style={styles.headerButtonText}>下载</Text>
        </TouchableOpacity>
      ),
    });
  }, [navigation, file.name]);

  const handleDownload = async () => {
    try {
      setLoading(true);
      const downloadUrl = driveService.getDownloadUrl(file.id);
      // 这里可以集成文件下载功能
      showToast('开始下载文件');
      console.log('Download URL:', downloadUrl);
    } catch (error) {
      console.error('Download error:', error);
      Alert.alert('错误', '下载失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  const handleVideoLoad = (data: any) => {
    setDuration(data.duration);
  };

  const handleVideoProgress = (data: any) => {
    setCurrentTime(data.currentTime);
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const renderImagePreview = () => (
    <ScrollView
      style={styles.previewContainer}
      contentContainerStyle={styles.imageContainer}
      maximumZoomScale={3}
      minimumZoomScale={1}
      showsHorizontalScrollIndicator={false}
      showsVerticalScrollIndicator={false}>
      <FastImage
        key={`preview-${file.id}-${Date.now()}`}
        source={{
          ...driveService.getDownloadUrlWithHeaders(file.id),
          priority: FastImage.priority.high,
        }}
        style={styles.image}
        resizeMode={FastImage.resizeMode.contain}
      />
    </ScrollView>
  );

  const renderVideoPreview = () => (
    <View style={styles.previewContainer}>
      <Video
        source={{uri: driveService.getDownloadUrl(file.id)}}
        style={styles.video}
        resizeMode="contain"
        paused={!isPlaying}
        onLoad={handleVideoLoad}
        onProgress={handleVideoProgress}
        onEnd={() => setIsPlaying(false)}
        controls={true}
      />
      
      <View style={styles.videoInfo}>
        <Text style={styles.videoTitle}>{file.name}</Text>
        <Text style={styles.videoDetails}>
          大小: {driveService.formatFileSize(file.size)}
        </Text>
        {duration > 0 && (
          <Text style={styles.videoDetails}>
            时长: {formatTime(duration)}
          </Text>
        )}
      </View>
    </View>
  );

  const renderUnsupportedPreview = () => (
    <View style={styles.unsupportedContainer}>
      <Text style={styles.fileIcon}>
        {driveService.getFileIcon(file)}
      </Text>
      <Text style={styles.fileName}>{file.name}</Text>
      <Text style={styles.fileDetails}>
        大小: {driveService.formatFileSize(file.size)}
      </Text>
      <Text style={styles.fileDetails}>
        类型: {file.mimeType || '未知'}
      </Text>
      <Text style={styles.fileDetails}>
        修改时间: {new Date(file.updatedAt).toLocaleString()}
      </Text>
      
      <TouchableOpacity
        style={styles.downloadButton}
        onPress={handleDownload}
        disabled={loading}>
        {loading ? (
          <ActivityIndicator color={COLORS.SURFACE} />
        ) : (
          <Text style={styles.downloadButtonText}>下载文件</Text>
        )}
      </TouchableOpacity>
    </View>
  );

  const renderPreview = () => {
    if (driveService.isImageFile(file.mimeType)) {
      return renderImagePreview();
    }
    
    if (driveService.isVideoFile(file.mimeType)) {
      return renderVideoPreview();
    }
    
    return renderUnsupportedPreview();
  };

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={COLORS.PRIMARY} />
      {renderPreview()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  headerButton: {
    marginRight: SPACING.MD,
    paddingHorizontal: SPACING.SM,
    paddingVertical: SPACING.XS,
  },
  headerButtonText: {
    color: COLORS.SURFACE,
    fontSize: 16,
    fontWeight: '500',
  },
  previewContainer: {
    flex: 1,
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: screenWidth,
    height: screenHeight - 100,
  },
  video: {
    width: screenWidth,
    height: screenWidth * 9 / 16, // 16:9 aspect ratio
    backgroundColor: 'black',
  },
  videoInfo: {
    padding: SPACING.MD,
    backgroundColor: COLORS.SURFACE,
  },
  videoTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.XS,
  },
  videoDetails: {
    fontSize: 14,
    color: COLORS.TEXT_SECONDARY,
    marginBottom: SPACING.XS,
  },
  unsupportedContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.LG,
  },
  fileIcon: {
    fontSize: 64,
    marginBottom: SPACING.MD,
  },
  fileName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: SPACING.MD,
  },
  fileDetails: {
    fontSize: 16,
    color: COLORS.TEXT_SECONDARY,
    textAlign: 'center',
    marginBottom: SPACING.XS,
  },
  downloadButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingHorizontal: SPACING.LG,
    paddingVertical: SPACING.MD,
    borderRadius: 8,
    marginTop: SPACING.LG,
    minWidth: 120,
    alignItems: 'center',
  },
  downloadButtonText: {
    color: COLORS.SURFACE,
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default FilePreviewScreen;
