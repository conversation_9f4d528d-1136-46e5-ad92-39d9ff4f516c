import React from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {
  Text,
  Card,
  Title,
  Paragraph,
  Button,
  Surface,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';

const DiskInfoScreen: React.FC = () => {
  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <Surface style={styles.headerCard}>
        <View style={styles.headerContent}>
          <Icon name="storage" size={48} color="#2196F3" />
          <Title style={styles.headerTitle}>磁盘信息</Title>
          <Paragraph style={styles.headerDescription}>
            查看系统磁盘使用情况和文件系统信息
          </Paragraph>
        </View>
      </Surface>

      <Card style={styles.statusCard}>
        <Card.Content>
          <Title>开发状态</Title>
          <Paragraph style={styles.statusText}>
            磁盘信息页面正在开发中...
          </Paragraph>
        </Card.Content>
      </Card>

      <View style={styles.actionButtons}>
        <Button
          mode="outlined"
          onPress={() => console.log('刷新信息')}
          style={styles.actionButton}>
          刷新
        </Button>
        <Button
          mode="contained"
          onPress={() => console.log('详细信息')}
          style={styles.actionButton}>
          详细信息
        </Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  headerCard: {
    padding: 24,
    marginBottom: 24,
    borderRadius: 12,
    elevation: 2,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginTop: 16,
    marginBottom: 8,
  },
  headerDescription: {
    fontSize: 16,
    color: '#49454F',
    textAlign: 'center',
    lineHeight: 24,
  },
  statusCard: {
    marginBottom: 24,
    borderRadius: 12,
    elevation: 1,
  },
  statusText: {
    fontSize: 14,
    color: '#49454F',
    marginBottom: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 0.48,
  },
});

export default DiskInfoScreen; 