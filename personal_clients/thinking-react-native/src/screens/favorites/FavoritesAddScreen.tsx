import React, {useState, useEffect} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Title,
  Paragraph,
  Button,
  Surface,
} from 'react-native-paper';
// import Icon from 'react-native-vector-icons/MaterialIcons';
import favoritesService from '../../services/favoritesService';

interface FavoritesAddScreenProps {
  initialUrl?: string;
  initialTitle?: string;
  onBack?: () => void;
  onSave?: () => void;
}

const FavoritesAddScreen: React.FC<FavoritesAddScreenProps> = ({
  initialUrl = '',
  initialTitle = '',
  onBack,
  onSave,
}) => {
  const [url, setUrl] = useState(initialUrl);
  const [title, setTitle] = useState(initialTitle);
  const [saving, setSaving] = useState(false);
  useEffect(() => {
    setUrl(initialUrl);
    setTitle(initialTitle);
  }, [initialUrl, initialTitle]);

  const handleSave = async () => {
    if (!url.trim()) {
      Alert.alert('错误', '请输入网址');
      return;
    }

    if (!title.trim()) {
      Alert.alert('错误', '请输入标题');
      return;
    }

    try {
      setSaving(true);
      await favoritesService.addFavorite(url.trim(), title.trim());
      Alert.alert('成功', '收藏添加成功！', [
        {
          text: '确定',
          onPress: () => {
            if (onSave) {
              onSave();
            }
          },
        },
      ]);
    } catch (error) {
      console.error('Error adding favorite:', error);
      Alert.alert('错误', '添加收藏失败');
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    if (onBack) {
      onBack();
    }
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <Surface style={styles.headerCard}>
        <View style={styles.headerContent}>
          <Text style={styles.headerIcon}>📚</Text>
          <Title style={styles.headerTitle}>添加收藏</Title>
          <Paragraph style={styles.headerDescription}>
            添加新的网址收藏或文件索引
          </Paragraph>
        </View>
      </Surface>

      <Card style={styles.formCard}>
        <Card.Content>
          <Title style={styles.formTitle}>收藏信息</Title>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>网址 *</Text>
            <TextInput
              style={styles.textInput}
              value={url}
              onChangeText={setUrl}
              placeholder="请输入要收藏的网址"
              multiline={false}
              autoCapitalize="none"
              keyboardType="url"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>标题 *</Text>
            <TextInput
              style={styles.textInput}
              value={title}
              onChangeText={setTitle}
              placeholder="请输入收藏标题"
              multiline={false}
            />
          </View>

          {initialUrl && (
            <View style={styles.shareInfo}>
              <Text style={styles.shareIcon}>🔗</Text>
              <Text style={styles.shareText}>来自分享</Text>
            </View>
          )}
        </Card.Content>
      </Card>

      <View style={styles.actionButtons}>
        <Button
          mode="outlined"
          onPress={handleCancel}
          style={styles.actionButton}
          disabled={saving}>
          取消
        </Button>
        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.actionButton}
          loading={saving}
          disabled={saving}>
          保存
        </Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  headerCard: {
    padding: 24,
    marginBottom: 24,
    borderRadius: 12,
    elevation: 2,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerIcon: {
    fontSize: 48,
    marginBottom: 8,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginTop: 16,
    marginBottom: 8,
  },
  headerDescription: {
    fontSize: 16,
    color: '#49454F',
    textAlign: 'center',
    lineHeight: 24,
  },
  formCard: {
    marginBottom: 24,
    borderRadius: 12,
    elevation: 1,
  },
  formTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1B1F',
    marginBottom: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#49454F',
    marginBottom: 8,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#FFFFFF',
    minHeight: 48,
  },
  shareInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F3E5F5',
    padding: 8,
    borderRadius: 6,
    marginTop: 8,
  },
  shareIcon: {
    fontSize: 16,
    marginRight: 4,
  },
  shareText: {
    fontSize: 12,
    color: '#6750A4',
    fontWeight: '500',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 0.48,
  },
});

export default FavoritesAddScreen; 