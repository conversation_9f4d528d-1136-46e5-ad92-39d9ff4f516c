import React, {useState, useEffect} from 'react';
import {
  View,
  StyleSheet,
  Alert,
  TouchableOpacity,
  Text,
  TextInput,
  FlatList,
  RefreshControl,
  Linking,
} from 'react-native';

// Import services and types
import favoritesService from '../../services/favoritesService';
import {FavoriteItem} from '../../types';

interface FavoritesListScreenProps {
  favorites?: FavoriteItem[];
  refreshing?: boolean;
  onBack?: () => void;
}

const FavoritesListScreen: React.FC<FavoritesListScreenProps> = ({
  favorites: propFavorites,
  refreshing = false,
  onBack,
}) => {
  const [favorites, setFavorites] = useState<FavoriteItem[]>(propFavorites || []);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<FavoriteItem[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [loading, setLoading] = useState(!propFavorites);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [searchHasMore, setSearchHasMore] = useState(true);
  const [currentOffset, setCurrentOffset] = useState(0);
  const [searchOffset, setSearchOffset] = useState(0);
  const [initialized, setInitialized] = useState(false);
  const [isFirstLoad, setIsFirstLoad] = useState(true);
  const PAGE_SIZE = 20;

  // Load favorites if not provided via props
  useEffect(() => {
    if (!initialized) {
      setInitialized(true);
      if (!propFavorites) {
        loadFavorites(true);
      } else {
        setFavorites(propFavorites);
        setHasMore(propFavorites.length >= PAGE_SIZE);
        setIsFirstLoad(false); // 如果有props数据，直接设置为非首次加载
      }
    }
  }, []);

  const loadFavorites = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setLoading(true);
        setCurrentOffset(0);
      }

      const offset = isRefresh ? 0 : currentOffset;
      const data = await favoritesService.getFavorites(offset, PAGE_SIZE);

      if (isRefresh) {
        setFavorites(data.items);
        setCurrentOffset(PAGE_SIZE);
      } else {
        setFavorites(prev => [...prev, ...data.items]);
        setCurrentOffset(prev => prev + PAGE_SIZE);
      }

      // 根据服务器返回的has_more字段或者实际返回的数据量判断是否还有更多数据
      setHasMore(data.hasMore || data.items.length >= PAGE_SIZE);

      // 加载完成后，允许onEndReached触发（无论是首次加载还是刷新）
      if (isFirstLoad) {
        setIsFirstLoad(false);
      }

    } catch (error) {
      console.error('Error loading favorites:', error);
      Alert.alert('错误', '加载收藏列表失败');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const loadMoreFavorites = async () => {
    // 防止首次加载时触发onEndReached
    if (isFirstLoad || loadingMore || !hasMore) return;

    try {
      setLoadingMore(true);
      await loadFavorites(false);
    } catch (error) {
      console.error('Error loading more favorites:', error);
    }
  };

  const handleRefresh = async () => {
    if (searchQuery.trim()) {
      // 如果在搜索状态，刷新搜索结果
      await searchFavorites(searchQuery.trim(), true);
    } else {
      // 下拉刷新时，暂时设置首次加载标志以防止onEndReached触发
      setIsFirstLoad(true);
      await loadFavorites(true);
      // 确保刷新完成后重置标志
      setIsFirstLoad(false);
    }
  };

  const searchFavorites = async (keyword: string, isRefresh = false) => {
    try {
      setIsSearching(true);
      if (isRefresh) {
        setLoading(true);
        setSearchOffset(0);
      }

      const offset = isRefresh ? 0 : searchOffset;
      const data = await favoritesService.getFavorites(offset, PAGE_SIZE, keyword);

      if (isRefresh) {
        setSearchResults(data.items);
        setSearchOffset(PAGE_SIZE);
      } else {
        setSearchResults(prev => [...prev, ...data.items]);
        setSearchOffset(prev => prev + PAGE_SIZE);
      }

      setSearchHasMore(data.hasMore || data.items.length >= PAGE_SIZE);

    } catch (error) {
      console.error('Error searching favorites:', error);
      Alert.alert('错误', '搜索收藏失败');
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setIsSearching(false);
    }
  };

  const loadMoreSearchResults = async () => {
    if (loadingMore || !searchHasMore || !searchQuery.trim()) return;

    try {
      setLoadingMore(true);
      await searchFavorites(searchQuery.trim(), false);
    } catch (error) {
      console.error('Error loading more search results:', error);
    }
  };

  const handleItemPress = async (item: FavoriteItem) => {
    try {
      const url = item.origin || '';
      if (url) {
        const supported = await Linking.canOpenURL(url);
        if (supported) {
          await Linking.openURL(url);
          // Increment click count
          if (item.id) {
            await favoritesService.incrementClickCount(item.id);
          }
        } else {
          Alert.alert('错误', '无法打开链接');
        }
      }
    } catch (error) {
      console.error('Error opening URL:', error);
      Alert.alert('错误', '打开链接失败');
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await favoritesService.deleteFavorite(id);
      setFavorites(prev => prev.filter(item => item.id !== id));
      Alert.alert('成功', '收藏已删除');
    } catch (error) {
      console.error('Error deleting favorite:', error);
      Alert.alert('错误', '删除失败');
    }
  };



  // 处理搜索输入变化
  const handleSearchChange = (text: string) => {
    setSearchQuery(text);

    if (text.trim()) {
      // 开始搜索
      searchFavorites(text.trim(), true);
    } else {
      // 清空搜索，显示原始列表
      setSearchResults([]);
      setIsSearching(false);
    }
  };

  // 获取当前显示的数据
  const currentData = searchQuery.trim() ? searchResults : favorites;
  const currentHasMore = searchQuery.trim() ? searchHasMore : hasMore;

  const renderItem = ({item}: {item: FavoriteItem}) => {
    // 确保所有数据都是安全的
    const safeTitle = String(item?.title || '无标题');
    const safeUrl = String(item?.origin || '');
    const safeId = String(item?.id || '');
    const safeAuthor = String(item?.author || '未知UP主');
    const safeClickCount = item?.clickCount ? Number(item.clickCount) : 0;
    const safeState = item?.state || 'unknown';

    // 格式化完整时间戳
    const formatDateTime = (dateStr: string) => {
      try {
        const date = new Date(dateStr);
        return date.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        });
      } catch (error) {
        return dateStr;
      }
    };

    // 获取状态显示信息
    const getStateInfo = (state: string) => {
      switch (state) {
        case 'success':
          return { text: '成功', color: '#4CAF50', bgColor: '#E8F5E8' };
        case 'pending':
          return { text: '等待中', color: '#FF9800', bgColor: '#FFF3E0' };
        case 'processing':
          return { text: '处理中', color: '#2196F3', bgColor: '#E3F2FD' };
        case 'failed':
          return { text: '失败', color: '#F44336', bgColor: '#FFEBEE' };
        case 'archived':
          return { text: '已归档', color: '#9E9E9E', bgColor: '#F5F5F5' };
        default:
          return { text: '未知', color: '#757575', bgColor: '#F5F5F5' };
      }
    };

    const stateInfo = getStateInfo(safeState);

    return (
      <TouchableOpacity
        style={styles.listItem}
        onPress={() => handleItemPress(item)}
      >
        <View style={styles.itemContent}>
          <View style={styles.itemHeader}>
            {/* 左侧头像区域 */}
            <View style={styles.avatarContainer}>
              <View style={styles.avatar}>
                <Text style={styles.avatarText}>
                  {safeAuthor.charAt(0).toUpperCase()}
                </Text>
              </View>
              <Text style={styles.authorName} numberOfLines={1}>
                {safeAuthor}
              </Text>
            </View>

            {/* 中间内容区域 */}
            <View style={styles.itemMainContent}>
              <Text style={styles.itemTitle} numberOfLines={2}>
                {safeTitle}
              </Text>
              <Text style={styles.itemUrl} numberOfLines={1}>
                {safeUrl}
              </Text>

              {/* 状态信息 */}
              <View style={styles.statusContainer}>
                <View style={[styles.statusBadge, { backgroundColor: stateInfo.bgColor }]}>
                  <Text style={[styles.statusText, { color: stateInfo.color }]}>
                    {stateInfo.text}
                  </Text>
                </View>
                {item?.errorMessage && (
                  <Text style={styles.errorMessage} numberOfLines={1}>
                    {item.errorMessage}
                  </Text>
                )}
              </View>
            </View>

            {/* 右侧删除按钮 */}
            <TouchableOpacity
              onPress={() => {
                Alert.alert(
                  '删除收藏',
                  `确定要删除"${safeTitle}"吗？`,
                  [
                    {text: '取消', style: 'cancel'},
                    {
                      text: '删除',
                      style: 'destructive',
                      onPress: () => handleDelete(safeId),
                    },
                  ],
                );
              }}
              style={styles.deleteButton}
            >
              <Text style={styles.deleteButtonText}>删除</Text>
            </TouchableOpacity>
          </View>

          {/* 底部信息 */}
          <View style={styles.itemFooter}>
            <View style={styles.footerLeft}>
              {safeClickCount > 0 && (
                <View style={styles.clickBadge}>
                  <Text style={styles.clickBadgeText}>点击 {safeClickCount} 次</Text>
                </View>
              )}
            </View>
            <Text style={styles.itemDate}>
              {formatDateTime(item?.createTime || new Date().toISOString())}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>🔖</Text>
      <Text style={styles.emptyTitle}>
        {String(searchQuery ? '没有找到匹配的收藏' : '暂无收藏')}
      </Text>
      <Text style={styles.emptyText}>
        {String(searchQuery ? '尝试使用其他关键词搜索' : '开始添加您的第一个收藏吧')}
      </Text>
    </View>
  );

  const renderFooter = () => {
    if (!currentHasMore) {
      return (
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>没有更多数据了</Text>
        </View>
      );
    }

    if (loadingMore) {
      return (
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>加载中...</Text>
        </View>
      );
    }

    return null;
  };

  const handleEndReached = () => {
    if (searchQuery.trim()) {
      loadMoreSearchResults();
    } else {
      loadMoreFavorites();
    }
  };

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        {onBack && (
          <TouchableOpacity onPress={onBack} style={styles.backButton}>
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
        )}
        <View style={styles.searchInputContainer}>
          <TextInput
            placeholder="搜索收藏..."
            value={searchQuery}
            onChangeText={handleSearchChange}
            style={styles.searchBar}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              onPress={() => handleSearchChange('')}
              style={styles.clearButton}
            >
              <Text style={styles.clearButtonText}>✕</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Search Results Info */}
      {searchQuery.trim() && (
        <View style={styles.searchResultsContainer}>
          <Text style={styles.searchResultsText}>
            找到 {currentData.length} 条匹配的收藏
          </Text>
        </View>
      )}

      {/* Favorites List */}
      <FlatList
        testID="favorites-flatlist"
        data={currentData}
        renderItem={renderItem}
        keyExtractor={(item, index) => String(item?.id || `item-${index}`)}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing || loading} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={renderEmpty}
        ListFooterComponent={renderFooter}
        onEndReached={handleEndReached}
        onEndReachedThreshold={0.1}
        showsVerticalScrollIndicator={false}
      />

      {/* FAB */}
      <TouchableOpacity
        style={styles.fab}
        onPress={() => Alert.alert('添加收藏', '添加收藏功能开发中...')}
      >
        <Text style={styles.fabText}>+</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#FFFFFF',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
  },
  searchBar: {
    flex: 1,
    height: 40,
    paddingHorizontal: 12,
    backgroundColor: 'transparent',
  },
  clearButton: {
    width: 30,
    height: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  clearButtonText: {
    fontSize: 16,
    color: '#79747E',
  },
  searchResultsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#F3E5F5',
  },
  searchResultsText: {
    fontSize: 12,
    color: '#6750A4',
    fontWeight: '500',
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  backButtonText: {
    fontSize: 20,
    color: '#6750A4',
    fontWeight: 'bold',
  },
  listContainer: {
    padding: 16,
    paddingBottom: 80,
  },
  listItem: {
    marginBottom: 12,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  itemContent: {
    padding: 16,
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  avatarContainer: {
    alignItems: 'center',
    marginRight: 12,
    width: 60,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E3F2FD',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  avatarText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1976D2',
  },
  authorName: {
    fontSize: 10,
    color: '#666666',
    textAlign: 'center',
    width: 60,
  },
  itemMainContent: {
    flex: 1,
    marginRight: 12,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 4,
  },
  itemUrl: {
    fontSize: 12,
    color: '#6750A4',
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
  },
  statusText: {
    fontSize: 11,
    fontWeight: '500',
  },
  errorMessage: {
    fontSize: 11,
    color: '#F44336',
    flex: 1,
  },
  deleteButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
    backgroundColor: '#FFEBEE',
    alignSelf: 'flex-start',
  },
  deleteButtonText: {
    fontSize: 12,
    color: '#F44336',
    fontWeight: '500',
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#F0F0F0',
  },
  footerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  itemDate: {
    fontSize: 12,
    color: '#79747E',
  },
  clickBadge: {
    backgroundColor: '#E8F5E8',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  clickBadgeText: {
    fontSize: 10,
    color: '#4CAF50',
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 64,
  },
  emptyIcon: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    color: '#49454F',
    textAlign: 'center',
    marginBottom: 12,
    paddingHorizontal: 32,
  },
  footerContainer: {
    paddingVertical: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#79747E',
    textAlign: 'center',
  },
  fab: {
    position: 'absolute',
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#6750A4',
    alignItems: 'center',
    justifyContent: 'center',
    margin: 16,
    right: 0,
    bottom: 0,
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.3,
    shadowRadius: 6,
  },
  fabText: {
    fontSize: 24,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
});

export default FavoritesListScreen;
