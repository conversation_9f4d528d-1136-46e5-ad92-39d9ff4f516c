import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
  Text,
  TouchableOpacity,
  Alert,
  BackHandler,
} from 'react-native';

// Import services and types
import favoritesService from '../../services/favoritesService';
import {FavoriteItem, DiskInfo} from '../../types';
import FavoritesListScreen from './FavoritesListScreen';

const {width} = Dimensions.get('window');

interface StatsCardProps {
  title: string;
  value: number;
  color: string;
  emoji: string;
  onPress?: () => void;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  color,
  emoji,
  onPress,
}) => (
  <TouchableOpacity style={[styles.statsCard, {width: (width - 48) / 2}]} onPress={onPress}>
    <View style={styles.statsCardContent}>
      <View style={styles.statsHeader}>
        <Text style={styles.statsEmoji}>{emoji}</Text>
        <Text style={[styles.statsValue, {color}]}>{value}</Text>
      </View>
      <Text style={styles.statsTitle}>{title}</Text>
    </View>
  </TouchableOpacity>
);

const FavoritesMainScreen: React.FC = () => {
  const [currentView, setCurrentView] = useState<'main' | 'list'>('main');
  const [favorites, setFavorites] = useState<FavoriteItem[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [diskInfo, setDiskInfo] = useState<DiskInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Load data
  const loadData = useCallback(async () => {
    try {
      const [favoritesData, diskData] = await Promise.all([
        favoritesService.getFavorites(0, 20),
        favoritesService.getDiskInfo(),
      ]);

      setFavorites(favoritesData.items);
      setTotalCount(favoritesData.total); // 使用服务器返回的total字段
      setDiskInfo(diskData);
    } catch (error) {
      console.error('Error loading favorites data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  // Handle back button for list view
  useEffect(() => {
    const backAction = () => {
      if (currentView === 'list') {
        setCurrentView('main');
        // 不需要刷新数据，因为从列表页返回时数据没有改变
        return true; // 阻止默认返回行为
      }
      return false; // 让默认返回行为继续
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => backHandler.remove();
  }, [currentView]);

  const handleRefresh = () => {
    setRefreshing(true);
    loadData();
  };

  const handleAddFavorite = () => {
    Alert.prompt(
      '添加收藏',
      '请输入要收藏的网址：',
      [
        {text: '取消', style: 'cancel'},
        {
          text: '添加',
          onPress: async (url) => {
            if (url && url.trim()) {
              try {
                await favoritesService.addFavorite(url.trim(), '新收藏');
                Alert.alert('成功', '收藏添加成功！');
                loadData();
              } catch (error) {
                Alert.alert('错误', '添加收藏失败');
              }
            }
          },
        },
      ],
      'plain-text',
      '',
      'url'
    );
  };

  const handleViewList = () => {
    setCurrentView('list');
  };

  const handleViewDisk = () => {
    Alert.alert('磁盘信息', '导航到磁盘信息页面功能需要配置导航后实现');
  };

  const getFavoritesByState = () => {
    const success = favorites.filter(f => f.state === 'success').length;
    const pending = favorites.filter(f => f.state === 'pending').length;
    const failed = favorites.filter(f => f.state === 'failed').length;
    const processing = favorites.filter(f => f.state === 'processing').length;
    const archived = favorites.filter(f => f.state === 'archived').length;
    return {success, pending, failed, processing, archived, total: totalCount}; // 使用服务器返回的total
  };

  const stats = getFavoritesByState();

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  // Show list view
  if (currentView === 'list') {
    return (
      <FavoritesListScreen
        favorites={undefined} // 让列表页面自己管理数据，支持分页
        refreshing={false}
        onBack={() => {
          setCurrentView('main');
          // 不需要刷新数据，因为从列表页返回时数据没有改变
        }}
      />
    );
  }

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }>

      {/* Header */}
      <View style={styles.headerSection}>
        <View style={styles.headerContent}>
          <Text style={styles.headerEmoji}>🔖</Text>
          <Text style={styles.headerTitle}>收藏夹</Text>
          <Text style={styles.headerDescription}>
            管理您的网址收藏和文件索引
          </Text>
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActionsContainer}>
        <Text style={styles.sectionTitle}>快速操作</Text>
        <View style={styles.quickActionsRow}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleAddFavorite}>
            <Text style={styles.actionButtonText}>➕ 添加收藏</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, styles.actionButtonOutlined]}
            onPress={handleViewList}>
            <Text style={styles.actionButtonTextOutlined}>📋 查看列表</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Statistics Cards */}
      <View style={styles.statsContainer}>
        <Text style={styles.sectionTitle}>统计概览</Text>
        <View style={styles.statsRow}>
          <StatsCard
            title="总收藏"
            value={stats.total}
            color="#6750A4"
            emoji="🔖"
          />
          <StatsCard
            title="成功"
            value={stats.success}
            color="#4CAF50"
            emoji="✅"
          />
        </View>
        <View style={styles.statsRow}>
          <StatsCard
            title="失败"
            value={stats.failed}
            color="#F44336"
            emoji="❌"
          />
          <StatsCard
            title="处理中"
            value={stats.pending + stats.processing}
            color="#FF9800"
            emoji="⏳"
          />
        </View>
        <View style={styles.statsRow}>
          <StatsCard
            title="磁盘"
            value={diskInfo && diskInfo.fsTree ? diskInfo.fsTree.length : 0}
            color="#2196F3"
            emoji="💾"
            onPress={handleViewDisk}
          />
          <StatsCard
            title="归档"
            value={stats.archived}
            color="#9E9E9E"
            emoji="📦"
          />
        </View>
      </View>

      {/* Empty State */}
      {favorites.length === 0 && (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyEmoji}>🔖</Text>
          <Text style={styles.emptyTitle}>还没有收藏</Text>
          <Text style={styles.emptyDescription}>
            开始添加您的第一个收藏吧
          </Text>
          <TouchableOpacity
            style={styles.emptyButton}
            onPress={handleAddFavorite}>
            <Text style={styles.emptyButtonText}>添加收藏</Text>
          </TouchableOpacity>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  contentContainer: {
    paddingBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#49454F',
  },
  headerSection: {
    backgroundColor: '#6750A4',
    paddingHorizontal: 16,
    paddingTop: 20,
    paddingBottom: 24,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerEmoji: {
    fontSize: 48,
    marginBottom: 8,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  headerDescription: {
    fontSize: 16,
    color: '#E8DEF8',
    textAlign: 'center',
    lineHeight: 24,
  },
  quickActionsContainer: {
    margin: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1B1F',
    marginBottom: 12,
  },
  quickActionsRow: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    backgroundColor: '#6750A4',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonOutlined: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#6750A4',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  actionButtonTextOutlined: {
    color: '#6750A4',
    fontSize: 14,
    fontWeight: '500',
  },
  statsContainer: {
    margin: 16,
    marginTop: 0,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  statsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statsCardContent: {
    alignItems: 'center',
  },
  statsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statsEmoji: {
    fontSize: 20,
    marginRight: 8,
  },
  statsValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  statsTitle: {
    fontSize: 12,
    color: '#49454F',
    textAlign: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 32,
  },
  emptyEmoji: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1B1F',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#49454F',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  emptyButton: {
    backgroundColor: '#6750A4',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },

});

export default FavoritesMainScreen;
