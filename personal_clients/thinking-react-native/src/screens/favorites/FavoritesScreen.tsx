import React, {useState, useEffect} from 'react';
import FavoritesMainScreen from './FavoritesMainScreen';
import FavoritesAddScreen from './FavoritesAddScreen';

interface FavoritesScreenProps {
  sharedUrl?: string;
  sharedTitle?: string;
  onBack?: () => void;
}

const FavoritesScreen: React.FC<FavoritesScreenProps> = ({
  sharedUrl,
  sharedTitle,
  onBack,
}) => {
  const [currentView, setCurrentView] = useState<'main' | 'add'>('main');

  // 当有分享数据时，自动跳转到添加页面
  useEffect(() => {
    if (sharedUrl) {
      setCurrentView('add');
    }
  }, [sharedUrl]);

  const handleBackToMain = () => {
    setCurrentView('main');
    if (onBack) {
      onBack();
    }
  };

  if (currentView === 'add') {
    return (
      <FavoritesAddScreen
        initialUrl={sharedUrl}
        initialTitle={sharedTitle}
        onBack={handleBackToMain}
        onSave={handleBackToMain}
      />
    );
  }

  return <FavoritesMainScreen />;
};

export default FavoritesScreen;