import React, {useState, useRef, useEffect, useImperativeHandle, forwardRef} from 'react';
import {View, StyleSheet, Alert} from 'react-native';
import notesService from '../../services/notesService';
import NoteEditorContainer, { NoteEditorContainerRef, NoteData } from '../../components/NoteEditorContainer';
import NoteTypeSelector from '../../components/NoteTypeSelector';
import { NoteType } from '../../types';

export interface NotesAddScreenRef {
  checkBeforeExit: () => Promise<boolean>;
}

interface NotesAddScreenProps {
  onBack?: () => void;
  onSave?: () => void;
  onTypeSelectorStateChange?: (isInTypeSelector: boolean) => void;
  showTypeSelector?: boolean;
  onTypeSelectorChange?: (show: boolean) => void;
}

const NotesAddScreen = forwardRef<NotesAddScreenRef, NotesAddScreenProps>(({onBack, onSave, onTypeSelectorStateChange, showTypeSelector: externalShowTypeSelector, onTypeSelectorChange}, ref) => {
  const [createdTime] = useState(new Date());
  const [noteType, setNoteType] = useState<NoteType>('normal');

  // 使用外部传入的状态，如果没有传入则使用默认值
  const showTypeSelector = externalShowTypeSelector ?? true;

  // NoteEditorContainer的ref，用于获取最新内容
  const noteEditorRef = useRef<NoteEditorContainerRef>(null);

  // 跟踪已创建的笔记ID，防止重复创建
  const [createdNoteId, setCreatedNoteId] = useState<string | null>(null);

  // 跟踪是否有未保存的更改（从NoteEditorContainer传递过来）
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);



  // 为NoteEditorContainer提供的保存函数
  const handleSave = async (noteData: NoteData) => {
    console.log('NotesAddScreen handleSave called with:', noteData);

    try {
      const tagsString = noteData.tags.length > 0 ? noteData.tags.join(', ') : undefined;

      let result;
      if (createdNoteId) {
        // 如果已经创建过笔记，则更新现有笔记
        console.log('NotesAddScreen handleSave - Updating existing note:', createdNoteId);
        result = await notesService.updateNote(
          createdNoteId,
          noteData.title,
          noteData.content,
          undefined,
          tagsString,
          noteData.noteType
        );
      } else {
        // 第一次保存，创建新笔记
        console.log('NotesAddScreen handleSave - Creating new note');
        result = await notesService.addNote(
          noteData.title,
          noteData.content,
          undefined,
          tagsString,
          noteData.noteType
        );

        // 保存创建的笔记ID（只有在真正成功时）
        if (result.success && result.data?.note_id && result.data.note_id > 0) {
          const newNoteId = String(result.data.note_id);
          console.log('NotesAddScreen handleSave - New note created with ID:', newNoteId);
          setCreatedNoteId(newNoteId);
        }
      }

      console.log('NotesAddScreen handleSave - Result:', result);
      return result;
    } catch (error) {
      console.error('NotesAddScreen handleSave - Error:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '保存失败，请检查网络连接',
      };
    }
  };

  // 处理数据变化的回调
  const handleDataChange = (hasChanges: boolean) => {
    setHasUnsavedChanges(hasChanges);
  };



  const handleTypeSelect = (type: NoteType) => {
    setNoteType(type);
    if (onTypeSelectorChange) {
      onTypeSelectorChange(false);
    }
    if (onTypeSelectorStateChange) {
      onTypeSelectorStateChange(false);
    }
  };

  // 通知父组件类型选择状态
  useEffect(() => {
    if (onTypeSelectorStateChange) {
      onTypeSelectorStateChange(showTypeSelector);
    }
  }, [showTypeSelector, onTypeSelectorStateChange]);



  // 退出确认函数
  const checkBeforeExit = async (): Promise<boolean> => {
    console.log('NotesAddScreen checkBeforeExit called, hasUnsavedChanges:', hasUnsavedChanges);

    // 委托给NoteEditorContainer处理
    if (noteEditorRef.current) {
      return await noteEditorRef.current.checkBeforeExit();
    }

    // 如果没有编辑器引用，直接返回true
    return true;
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    checkBeforeExit,
  }), [hasUnsavedChanges]);

  if (showTypeSelector) {
    return (
      <NoteTypeSelector
        selectedType={noteType}
        onTypeSelect={handleTypeSelect}
      />
    );
  }

  return (
    <View style={styles.container}>
      <NoteEditorContainer
        ref={noteEditorRef}
        noteType={noteType}
        initialTitle=""
        initialContent=""
        initialTags={[]}
        createdTime={createdTime}
        onSave={handleSave}
        onBack={onBack}
        onDataChange={handleDataChange}
        autoSaveEnabled={true}
        autoSaveDelay={2000}
      />
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
});

export default NotesAddScreen;
