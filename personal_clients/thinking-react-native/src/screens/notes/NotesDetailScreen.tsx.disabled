import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Share,
} from 'react-native';
import {useNavigation, useRoute} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';
import {RouteProp} from '@react-navigation/native';
import notesService from '../../services/notesService';
import {NoteItem, NotesStackParamList} from '../../types';

type NotesDetailScreenNavigationProp = StackNavigationProp<NotesStackParamList, 'NotesDetail'>;
type NotesDetailScreenRouteProp = RouteProp<NotesStackParamList, 'NotesDetail'>;

const NotesDetailScreen: React.FC = () => {
  const navigation = useNavigation<NotesDetailScreenNavigationProp>();
  const route = useRoute<NotesDetailScreenRouteProp>();
  const {noteId} = route.params;
  
  const [note, setNote] = useState<NoteItem | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadNote();
  }, [noteId]);

  const loadNote = async () => {
    try {
      setLoading(true);
      const result = await notesService.getNote(noteId);
      if (result.success) {
        setNote(result.data);
      } else {
        Alert.alert('错误', result.message || '加载笔记失败');
        navigation.goBack();
      }
    } catch (error) {
      console.error('Failed to load note:', error);
      Alert.alert('错误', '加载笔记失败');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const handleEdit = () => {
    navigation.navigate('NotesEdit', {noteId, note: note || undefined});
  };

  const handleDelete = () => {
    Alert.alert(
      '确认删除',
      '确定要删除这条笔记吗？此操作无法撤销。',
      [
        {text: '取消', style: 'cancel'},
        {text: '删除', style: 'destructive', onPress: confirmDelete},
      ]
    );
  };

  const confirmDelete = async () => {
    try {
      const result = await notesService.deleteNote(noteId);
      if (result.success) {
        Alert.alert('成功', '笔记已删除', [
          {text: '确定', onPress: () => navigation.goBack()},
        ]);
      } else {
        Alert.alert('错误', result.message || '删除失败');
      }
    } catch (error) {
      console.error('Delete note error:', error);
      Alert.alert('错误', '删除笔记失败');
    }
  };

  const handleShare = async () => {
    if (!note) return;

    try {
      const shareContent = `${note.title}\n\n${note.content}`;
      await Share.share({
        message: shareContent,
        title: note.title,
      });
    } catch (error) {
      console.error('Share error:', error);
    }
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6750A4" />
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  if (!note) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>笔记不存在</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.headerButton}>
          <Text style={styles.headerButtonText}>← 返回</Text>
        </TouchableOpacity>
        <View style={styles.headerActions}>
          <TouchableOpacity onPress={handleShare} style={styles.actionButton}>
            <Text style={styles.actionButtonText}>📤</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={handleEdit} style={styles.actionButton}>
            <Text style={styles.actionButtonText}>✏️</Text>
          </TouchableOpacity>
          <TouchableOpacity onPress={handleDelete} style={styles.actionButton}>
            <Text style={styles.actionButtonText}>🗑️</Text>
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.content}>
        {/* Title */}
        <Text style={styles.title}>{note.title}</Text>

        {/* Meta Info */}
        <View style={styles.metaContainer}>
          <Text style={styles.metaText}>
            创建时间：{notesService.formatCreateTime(note.createTime)}
          </Text>
          <Text style={styles.metaText}>
            更新时间：{notesService.formatCreateTime(note.updateTime)}
          </Text>
        </View>

        {/* Tags */}
        {(note.category || note.tags) && (
          <View style={styles.tagsContainer}>
            {note.category && (
              <View style={styles.categoryTag}>
                <Text style={styles.categoryTagText}>{note.category}</Text>
              </View>
            )}
            {note.tags && (
              <View style={styles.tagsWrapper}>
                {note.tags.split(',').map((tag, index) => (
                  <View key={index} style={styles.tag}>
                    <Text style={styles.tagText}>{tag.trim()}</Text>
                  </View>
                ))}
              </View>
            )}
          </View>
        )}

        {/* Content */}
        <View style={styles.contentContainer}>
          <Text style={styles.contentText}>{note.content}</Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#49454F',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
  },
  errorText: {
    fontSize: 16,
    color: '#49454F',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#FFFFFF',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  headerButton: {
    paddingHorizontal: 8,
    paddingVertical: 8,
  },
  headerButtonText: {
    fontSize: 16,
    color: '#6750A4',
  },
  headerActions: {
    flexDirection: 'row',
  },
  actionButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginLeft: 8,
  },
  actionButtonText: {
    fontSize: 18,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 16,
    lineHeight: 32,
  },
  metaContainer: {
    marginBottom: 16,
  },
  metaText: {
    fontSize: 12,
    color: '#49454F',
    marginBottom: 4,
  },
  tagsContainer: {
    marginBottom: 20,
  },
  categoryTag: {
    backgroundColor: '#6750A4',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    alignSelf: 'flex-start',
    marginBottom: 8,
  },
  categoryTagText: {
    fontSize: 14,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  tagsWrapper: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  tag: {
    backgroundColor: '#E8E8E8',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
    marginBottom: 4,
  },
  tagText: {
    fontSize: 12,
    color: '#49454F',
  },
  contentContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  contentText: {
    fontSize: 16,
    color: '#1C1B1F',
    lineHeight: 24,
  },
});

export default NotesDetailScreen;
