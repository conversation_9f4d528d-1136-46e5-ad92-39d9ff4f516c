import React, {useState, useEffect, useRef, useImperativeHandle, forwardRef} from 'react';
import {
  View,
  Text,
  Alert,
  ActivityIndicator,
  StyleSheet,
} from 'react-native';
import notesService from '../../services/notesService';
import {NoteItem, NoteType, TimelineEntry} from '../../types';
import NoteEditorContainer, { NoteEditorContainerRef, NoteData } from '../../components/NoteEditorContainer';

export interface NotesEditScreenRef {
  checkBeforeExit: () => Promise<boolean>;
}

interface NotesEditScreenProps {
  noteId: string;
  note?: NoteItem;
  onBack?: () => void;
  onSave?: () => void;
  onBeforeUnload?: (hasUnsavedChanges: boolean) => Promise<boolean>; // 返回 true 表示允许卸载
}

const NotesEditScreen = forwardRef<NotesEditScreenRef, NotesEditScreenProps>(({
  noteId,
  note: initialNote,
  onBack,
  onSave,
  onBeforeUnload,
}, ref) => {


  const [noteData, setNoteData] = useState<{
    title: string;
    content: string;
    tags: string[];
    createdTime: Date;
    noteType: NoteType;
    timelineEntries?: TimelineEntry[];
  } | null>(null);

  // 跟踪是否有未保存的更改（从NoteEditorContainer传递过来）
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // NoteEditorContainer的ref，用于获取最新内容
  const noteEditorRef = useRef<NoteEditorContainerRef>(null);

  useEffect(() => {
    if (initialNote) {
      loadNoteData(initialNote);
    } else {
      loadNote();
    }
  }, []);

  const loadNote = async () => {
    try {
      const result = await notesService.getNote(noteId);
      if (result.success) {
        loadNoteData(result.data);
      } else {
        Alert.alert('错误', result.message || '加载笔记失败');
        if (onBack) {
          onBack();
        }
      }
    } catch (error) {
      console.error('Failed to load note:', error);
      Alert.alert('错误', '加载笔记失败');
      if (onBack) {
        onBack();
      }
    }
  };

  const loadNoteData = (note: NoteItem) => {
    const noteTitle = note.title;
    const noteContent = note.content;
    const tagArray = note.tags ? note.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [];
    const noteType = note.note_type || 'normal';

    // 解析时间线数据
    let timelineEntries: TimelineEntry[] = [];
    if (noteType === 'timeline' && noteContent) {
      try {
        // 尝试解析 JSON 格式
        timelineEntries = JSON.parse(noteContent);
      } catch {
        // 如果不是 JSON，尝试解析文本格式
        const lines = noteContent.split('\n').filter(line => line.trim());
        timelineEntries = lines.map((line, index) => {
          const match = line.match(/^\[(.+?)\]\s*(.+)$/);
          if (match) {
            return {
              id: `entry-${index}`,
              timestamp: match[1],
              content: match[2],
            };
          }
          return {
            id: `entry-${index}`,
            timestamp: new Date().toISOString(),
            content: line,
          };
        });
      }
    }

    setNoteData({
      title: noteTitle,
      content: noteContent,
      tags: tagArray,
      createdTime: new Date(note.createTime),
      noteType,
      timelineEntries,
    });

    // 加载完成后，重置状态
    setHasUnsavedChanges(false);
  };

  // 为NoteEditorContainer提供的保存函数
  const handleSave = async (noteData: NoteData) => {
    console.log('NotesEditScreen handleSave called with:', noteData);

    try {
      const tagsString = noteData.tags.length > 0 ? noteData.tags.join(', ') : undefined;
      const result = await notesService.updateNote(
        noteId,
        noteData.title,
        noteData.content,
        undefined,
        tagsString,
        noteData.noteType
      );

      console.log('NotesEditScreen handleSave - Result:', result);
      return result;
    } catch (error) {
      console.error('NotesEditScreen handleSave - Error:', error);
      return {
        success: false,
        message: error instanceof Error ? error.message : '更新失败，请检查网络连接',
      };
    }
  };

  // 处理数据变化的回调
  const handleDataChange = (hasChanges: boolean) => {
    setHasUnsavedChanges(hasChanges);
  };

  // 暴露给父组件的退出检查函数
  const checkBeforeExit = async (): Promise<boolean> => {
    console.log('NotesEditScreen checkBeforeExit called, hasUnsavedChanges:', hasUnsavedChanges);

    // 委托给NoteEditor处理
    if (noteEditorRef.current) {
      return await noteEditorRef.current.checkBeforeExit();
    }

    // 如果没有NoteEditor引用，直接返回true
    return true;
  };

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    checkBeforeExit,
  }), [hasUnsavedChanges]);



  return (
    <View style={styles.container}>
      {noteData ? (
        <NoteEditorContainer
          ref={noteEditorRef}
          noteType={noteData.noteType}
          initialTitle={noteData.title}
          initialContent={noteData.content}
          initialTags={noteData.tags}
          initialTimelineEntries={noteData.timelineEntries}
          createdTime={noteData.createdTime}
          onSave={handleSave}
          onBack={onBack}
          onDataChange={handleDataChange}
          autoSaveEnabled={true}
          autoSaveDelay={2000}
        />
      ) : (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#6750A4" />
          <Text style={styles.loadingText}>加载中...</Text>
        </View>
      )}
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#49454F',
    fontFamily: 'LXGWWenKai-Regular',
  },
});

export default NotesEditScreen;
