import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { useToast } from '../../utils/ToastManager';
import notesService from '../../services/notesService';
import {NoteItem} from '../../types';

interface NotesListScreenProps {
  onBack?: () => void;
  onCreateNote?: () => void;
  onEditNote?: (noteId: string, note: NoteItem) => void;
  onViewNote?: (noteId: string) => void;
}

const PAGE_SIZE = 20;

const NotesListScreen: React.FC<NotesListScreenProps> = ({
  onBack,
  onCreateNote,
  onEditNote,
  onViewNote,
}) => {
  const { showToast } = useToast();
  const [notes, setNotes] = useState<NoteItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [loadingMore, setLoadingMore] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<NoteItem[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [currentOffset, setCurrentOffset] = useState(0);
  const [searchOffset, setSearchOffset] = useState(0);
  const [hasMore, setHasMore] = useState(true);
  const [searchHasMore, setSearchHasMore] = useState(true);
  const [isFirstLoad, setIsFirstLoad] = useState(true);

  const loadNotes = async (isRefresh = false) => {
    try {
      if (isRefresh) {
        setLoading(true);
        setCurrentOffset(0);
      }

      const offset = isRefresh ? 0 : currentOffset;
      const data = await notesService.getNotes(offset, PAGE_SIZE);

      if (isRefresh) {
        setNotes(data.items);
        setCurrentOffset(PAGE_SIZE);
      } else {
        setNotes(prev => [...prev, ...data.items]);
        setCurrentOffset(prev => prev + PAGE_SIZE);
      }

      // 根据服务器返回的has_more字段或者实际返回的数据量判断是否还有更多数据
      setHasMore(data.hasMore || data.items.length >= PAGE_SIZE);

      // 加载完成后，允许onEndReached触发（无论是首次加载还是刷新）
      if (isFirstLoad) {
        setIsFirstLoad(false);
      }

    } catch (error) {
      console.error('Error loading notes:', error);
      Alert.alert('错误', '加载笔记列表失败');
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  };

  const loadMoreNotes = () => {
    if (!hasMore || loadingMore || isFirstLoad) return;
    setLoadingMore(true);
    loadNotes(false);
  };

  const loadMoreSearchResults = () => {
    if (!searchHasMore || loadingMore || !searchQuery.trim()) return;
    setLoadingMore(true);
    searchNotes(searchQuery.trim(), false);
  };

  useEffect(() => {
    loadNotes(true);
  }, []);

  const handleRefresh = () => {
    setRefreshing(true);
    if (searchQuery.trim()) {
      searchNotes(searchQuery.trim(), true);
    } else {
      loadNotes(true);
    }
  };

  const searchNotes = async (keyword: string, isRefresh = false) => {
    try {
      setIsSearching(true);
      if (isRefresh) {
        setLoading(true);
        setSearchOffset(0);
      }

      const offset = isRefresh ? 0 : searchOffset;
      const data = await notesService.getNotes(offset, PAGE_SIZE, keyword);

      if (isRefresh) {
        setSearchResults(data.items);
        setSearchOffset(PAGE_SIZE);
      } else {
        setSearchResults(prev => [...prev, ...data.items]);
        setSearchOffset(prev => prev + PAGE_SIZE);
      }

      setSearchHasMore(data.hasMore || data.items.length >= PAGE_SIZE);

    } catch (error) {
      console.error('Error searching notes:', error);
      Alert.alert('错误', '搜索笔记失败');
    } finally {
      setLoading(false);
      setLoadingMore(false);
      setIsSearching(false);
    }
  };

  // 处理搜索输入变化
  const handleSearchChange = (text: string) => {
    setSearchQuery(text);

    if (text.trim()) {
      // 开始搜索
      searchNotes(text.trim(), true);
    } else {
      // 清空搜索，显示原始列表
      setSearchResults([]);
      setIsSearching(false);
    }
  };

  // 获取当前显示的数据
  const currentData = searchQuery.trim() ? searchResults : notes;
  const currentHasMore = searchQuery.trim() ? searchHasMore : hasMore;

  const handleNotePress = (note: NoteItem) => {
    if (onViewNote) {
      onViewNote(note.id);
    } else {
      Alert.alert('查看笔记', `笔记: ${note.title}`);
    }
  };

  const handleAddNote = () => {
    if (onCreateNote) {
      onCreateNote();
    } else {
      Alert.alert('创建笔记', '笔记创建功能正在开发中...');
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await notesService.deleteNote(id);
      setNotes(prev => prev.filter(item => item.id !== id));
      showToast('笔记已删除', 'success', 1000);
    } catch (error) {
      console.error('Error deleting note:', error);
      showToast('删除失败', 'error', 1000);
    }
  };

  const renderNoteItem = ({item}: {item: NoteItem}) => {
    const safeTitle = item?.title || '无标题';
    const safeContent = item?.content || '无内容';
    const safeId = item?.id || '';
    const safeCategory = item?.category || '';
    const safeTags = item?.tags || '';

    const formatDateTime = (dateString: string) => {
      try {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now.getTime() - date.getTime();
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
          return date.toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
          });
        } else if (diffDays < 7) {
          return `${diffDays}天前`;
        } else {
          return date.toLocaleDateString('zh-CN', {
            month: 'short',
            day: 'numeric'
          });
        }
      } catch (error) {
        return '未知时间';
      }
    };

    return (
      <TouchableOpacity
        style={styles.listItem}
        onPress={() => handleNotePress(item)}
      >
        <View style={styles.itemContent}>
          <View style={styles.itemHeader}>
            {/* 左侧图标区域 */}
            <View style={styles.avatarContainer}>
              <View style={styles.avatar}>
                <Text style={styles.avatarText}>
                  📝
                </Text>
              </View>
              <Text style={styles.authorName} numberOfLines={1}>
                笔记
              </Text>
            </View>

            {/* 中间内容区域 */}
            <View style={styles.contentContainer}>
              <Text style={styles.itemTitle} numberOfLines={2}>
                {safeTitle}
              </Text>
              <Text style={styles.itemDescription} numberOfLines={2}>
                {safeContent}
              </Text>
            </View>

            {/* 右侧删除按钮 */}
            <TouchableOpacity
              onPress={() => {
                Alert.alert(
                  '删除笔记',
                  `确定要删除"${safeTitle}"吗？`,
                  [
                    {text: '取消', style: 'cancel'},
                    {
                      text: '删除',
                      style: 'destructive',
                      onPress: () => handleDelete(safeId),
                    },
                  ],
                );
              }}
              style={styles.deleteButton}
            >
              <Text style={styles.deleteButtonText}>删除</Text>
            </TouchableOpacity>
          </View>

          {/* 底部信息 */}
          <View style={styles.itemFooter}>
            <View style={styles.footerLeft}>
              {safeCategory && (
                <View style={styles.categoryBadge}>
                  <Text style={styles.categoryBadgeText}>{safeCategory}</Text>
                </View>
              )}
              {safeTags && (
                <View style={styles.tagsBadge}>
                  <Text style={styles.tagsBadgeText}>{safeTags}</Text>
                </View>
              )}
            </View>
            <Text style={styles.itemDate}>
              {formatDateTime(item?.createTime || new Date().toISOString())}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderEmpty = () => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>📝</Text>
      <Text style={styles.emptyTitle}>
        {String(searchQuery ? '没有找到匹配的笔记' : '暂无笔记')}
      </Text>
      <Text style={styles.emptyText}>
        {String(searchQuery ? '尝试使用其他关键词搜索' : '开始添加您的第一个笔记吧')}
      </Text>
    </View>
  );

  const renderFooter = () => {
    if (!currentHasMore) {
      return (
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>没有更多数据了</Text>
        </View>
      );
    }

    if (loadingMore) {
      return (
        <View style={styles.footerContainer}>
          <Text style={styles.footerText}>加载中...</Text>
        </View>
      );
    }

    return null;
  };

  const handleEndReached = () => {
    if (searchQuery.trim()) {
      loadMoreSearchResults();
    } else {
      loadMoreNotes();
    }
  };

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        {onBack && (
          <TouchableOpacity onPress={onBack} style={styles.backButton}>
            <Text style={styles.backButtonText}>←</Text>
          </TouchableOpacity>
        )}
        <View style={styles.searchInputContainer}>
          <TextInput
            placeholder="搜索笔记..."
            value={searchQuery}
            onChangeText={handleSearchChange}
            style={styles.searchBar}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity
              onPress={() => handleSearchChange('')}
              style={styles.clearButton}
            >
              <Text style={styles.clearButtonText}>✕</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Search Results Info */}
      {searchQuery.trim() && (
        <View style={styles.searchResultsContainer}>
          <Text style={styles.searchResultsText}>
            找到 {currentData.length} 条匹配的笔记
          </Text>
        </View>
      )}

      {/* Notes List */}
      <FlatList
        testID="notes-flatlist"
        data={currentData}
        renderItem={renderNoteItem}
        keyExtractor={(item, index) => String(item?.id || `item-${index}`)}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing || loading} onRefresh={handleRefresh} />
        }
        ListEmptyComponent={renderEmpty}
        ListFooterComponent={renderFooter}
        onEndReached={handleEndReached}
        onEndReachedThreshold={0.1}
        showsVerticalScrollIndicator={false}
      />

      {/* FAB */}
      <TouchableOpacity
        style={styles.fab}
        onPress={handleAddNote}
      >
        <Text style={styles.fabText}>+</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  searchInputContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 8,
    paddingHorizontal: 12,
  },
  searchBar: {
    flex: 1,
    paddingVertical: 8,
    fontSize: 16,
    color: '#1C1B1F',
  },
  clearButton: {
    padding: 4,
  },
  clearButtonText: {
    fontSize: 16,
    color: '#6750A4',
    fontWeight: 'bold',
  },
  searchResultsContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    backgroundColor: '#F3E5F5',
  },
  searchResultsText: {
    fontSize: 12,
    color: '#6750A4',
    fontWeight: '500',
  },
  backButton: {
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  backButtonText: {
    fontSize: 20,
    color: '#6750A4',
    fontWeight: 'bold',
  },
  listContainer: {
    padding: 16,
    paddingBottom: 80,
  },
  listItem: {
    marginBottom: 12,
    borderRadius: 12,
    backgroundColor: '#FFFFFF',
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  itemContent: {
    padding: 16,
  },
  itemHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  avatarContainer: {
    alignItems: 'center',
    marginRight: 12,
    width: 50,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#E8DEF8',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  avatarText: {
    fontSize: 16,
  },
  authorName: {
    fontSize: 10,
    color: '#49454F',
    textAlign: 'center',
    fontWeight: '500',
  },
  contentContainer: {
    flex: 1,
    marginRight: 12,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 4,
    lineHeight: 22,
  },
  itemDescription: {
    fontSize: 14,
    color: '#49454F',
    lineHeight: 20,
  },
  deleteButton: {
    backgroundColor: '#FFEBEE',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    alignSelf: 'flex-start',
  },
  deleteButtonText: {
    fontSize: 12,
    color: '#D32F2F',
    fontWeight: '500',
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  footerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryBadge: {
    backgroundColor: '#E8DEF8',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginRight: 8,
  },
  categoryBadgeText: {
    fontSize: 10,
    color: '#6750A4',
    fontWeight: '500',
  },
  tagsBadge: {
    backgroundColor: '#F0F0F0',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 2,
    marginRight: 8,
  },
  tagsBadgeText: {
    fontSize: 10,
    color: '#666666',
    fontWeight: '500',
  },
  itemDate: {
    fontSize: 12,
    color: '#79747E',
    fontWeight: '400',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 60,
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 14,
    color: '#79747E',
    textAlign: 'center',
    lineHeight: 20,
  },
  footerContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#79747E',
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#6750A4',
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 6,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 3},
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
  fabText: {
    fontSize: 24,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
});

export default NotesListScreen;
