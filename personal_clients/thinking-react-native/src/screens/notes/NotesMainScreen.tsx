import React, {useState, useEffect, useCallback} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
  Text,
  TouchableOpacity,
  Alert,
  BackHandler,
} from 'react-native';
import notesService from '../../services/notesService';
import {NoteItem} from '../../types';

const {width} = Dimensions.get('window');

interface NotesMainScreenProps {
  onViewList?: () => void;
  onCreateNote?: () => void;
}

interface StatsCardProps {
  title: string;
  value: number;
  color: string;
  emoji: string;
  onPress?: () => void;
}

const StatsCard: React.FC<StatsCardProps> = ({
  title,
  value,
  color,
  emoji,
  onPress,
}) => (
  <TouchableOpacity style={[styles.statsCard, {width: (width - 48) / 2}]} onPress={onPress}>
    <View style={styles.statsCardContent}>
      <View style={styles.statsHeader}>
        <Text style={styles.statsEmoji}>{emoji}</Text>
        <Text style={[styles.statsValue, {color}]}>{value}</Text>
      </View>
      <Text style={styles.statsTitle}>{title}</Text>
    </View>
  </TouchableOpacity>
);

const NotesMainScreen: React.FC<NotesMainScreenProps> = ({onViewList, onCreateNote}) => {
  const [notes, setNotes] = useState<NoteItem[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Load data
  const loadData = useCallback(async () => {
    try {
      const notesData = await notesService.getNotes(0, 20);
      setNotes(notesData.items);
      setTotalCount(notesData.total);
    } catch (error) {
      console.error('Error loading notes data:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, []);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const handleRefresh = () => {
    setRefreshing(true);
    loadData();
  };

  const handleCreateNote = () => {
    if (onCreateNote) {
      onCreateNote();
    } else {
      Alert.alert('创建笔记', '笔记创建功能正在开发中...');
    }
  };

  const handleViewList = () => {
    if (onViewList) {
      onViewList();
    }
  };

  const getNotesByCategory = () => {
    const withCategory = notes.filter(n => n.category && n.category.trim()).length;
    const withoutCategory = notes.filter(n => !n.category || !n.category.trim()).length;
    const withTags = notes.filter(n => n.tags && n.tags.trim()).length;
    return {withCategory, withoutCategory, withTags, total: totalCount};
  };

  const stats = getNotesByCategory();

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>加载中...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
      }>

      {/* Header */}
      <View style={styles.headerSection}>
        <View style={styles.headerContent}>
          <Text style={styles.headerEmoji}>📝</Text>
          <Text style={styles.headerTitle}>笔记</Text>
          <Text style={styles.headerDescription}>
            记录想法和知识管理
          </Text>
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActionsContainer}>
        <Text style={styles.sectionTitle}>快速操作</Text>
        <View style={styles.quickActionsRow}>
          <TouchableOpacity
            style={styles.actionButton}
            onPress={handleCreateNote}>
            <Text style={styles.actionButtonText}>➕ 新建笔记</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.actionButton, styles.actionButtonOutlined]}
            onPress={handleViewList}>
            <Text style={styles.actionButtonTextOutlined}>📋 查看列表</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Statistics Cards */}
      <View style={styles.statsContainer}>
        <Text style={styles.sectionTitle}>统计概览</Text>
        <View style={styles.statsRow}>
          <StatsCard
            title="总笔记"
            value={stats.total}
            color="#6750A4"
            emoji="📝"
          />
          <StatsCard
            title="有分类"
            value={stats.withCategory}
            color="#4CAF50"
            emoji="📁"
          />
        </View>
        <View style={styles.statsRow}>
          <StatsCard
            title="无分类"
            value={stats.withoutCategory}
            color="#FF9800"
            emoji="📄"
          />
          <StatsCard
            title="有标签"
            value={stats.withTags}
            color="#2196F3"
            emoji="🏷️"
          />
        </View>
      </View>

      {/* Empty State */}
      {notes.length === 0 && (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyEmoji}>📝</Text>
          <Text style={styles.emptyTitle}>还没有笔记</Text>
          <Text style={styles.emptyDescription}>
            开始记录您的第一个想法吧
          </Text>
          <TouchableOpacity
            style={styles.emptyButton}
            onPress={handleCreateNote}>
            <Text style={styles.emptyButtonText}>新建笔记</Text>
          </TouchableOpacity>
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  contentContainer: {
    paddingBottom: 20,
  },
  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#49454F',
  },
  headerSection: {
    backgroundColor: '#6750A4',
    paddingHorizontal: 16,
    paddingTop: 20,
    paddingBottom: 24,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerEmoji: {
    fontSize: 48,
    marginBottom: 8,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 4,
  },
  headerDescription: {
    fontSize: 16,
    color: '#E8DEF8',
    textAlign: 'center',
    lineHeight: 24,
  },
  quickActionsContainer: {
    margin: 16,
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1C1B1F',
    marginBottom: 12,
  },
  quickActionsRow: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    backgroundColor: '#6750A4',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonOutlined: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#6750A4',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  actionButtonTextOutlined: {
    color: '#6750A4',
    fontSize: 14,
    fontWeight: '500',
  },
  statsContainer: {
    margin: 16,
    marginTop: 0,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  statsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statsCardContent: {
    alignItems: 'center',
  },
  statsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statsEmoji: {
    fontSize: 20,
    marginRight: 8,
  },
  statsValue: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  statsTitle: {
    fontSize: 12,
    color: '#49454F',
    textAlign: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 32,
  },
  emptyEmoji: {
    fontSize: 64,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1C1B1F',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyDescription: {
    fontSize: 16,
    color: '#49454F',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  emptyButton: {
    backgroundColor: '#6750A4',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  emptyButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default NotesMainScreen;
