import React, {useState, useEffect, useRef} from 'react';
import {<PERSON><PERSON>, BackHandler} from 'react-native';
import NotesMainScreen from './NotesMainScreen';
import NotesListScreen from './NotesListScreen';
import NotesAddScreen, {NotesAddScreenRef} from './NotesAddScreen';
import NotesEditScreen, {NotesEditScreenRef} from './NotesEditScreen';
import NotesSettingsScreen from './NotesSettingsScreen';
import {NoteItem} from '../../types';

type NotesView = 'main' | 'list' | 'add' | 'edit';

interface NotesScreenProps {
  onBack?: () => void;
  onPageStateChange?: (state: {
    view: string;
    isEditing: boolean;
    showSettings: boolean;
  }) => void;
  pageState?: {
    view: string;
    isEditing: boolean;
    showSettings: boolean;
  };
}

const NotesScreen: React.FC<NotesScreenProps> = ({
  onBack,
  onPageStateChange,
  pageState
}) => {
  const [currentView, setCurrentView] = useState<NotesView>('main');
  const [editingNote, setEditingNote] = useState<{id: string; note?: NoteItem} | null>(null);

  // 跟踪是否在类型选择页面
  const [isInTypeSelector, setIsInTypeSelector] = useState(false);
  // 控制新建笔记的类型选择状态
  const [showTypeSelector, setShowTypeSelector] = useState(true);

  // 编辑页面的 ref
  const editScreenRef = useRef<NotesEditScreenRef>(null);
  // 新建页面的 ref
  const addScreenRef = useRef<NotesAddScreenRef>(null);

  // 同步页面状态到App.tsx
  useEffect(() => {
    if (onPageStateChange) {
      onPageStateChange({
        view: currentView,
        isEditing: (currentView === 'add' || currentView === 'edit') && !isInTypeSelector,
        showSettings: pageState?.showSettings || false,
      });
    }
  }, [currentView, onPageStateChange, pageState?.showSettings, isInTypeSelector]);

  const handleCreateNote = () => {
    setShowTypeSelector(true); // 重置类型选择状态
    setCurrentView('add');
  };

  const handleViewNote = (noteId: string) => {
    // For now, viewing a note means editing it
    // In the future, you could add a separate view screen
    handleEditNote(noteId);
  };

  const handleEditNote = (noteId: string, note?: NoteItem) => {
    setEditingNote({id: noteId, note});
    setCurrentView('edit');
  };

  // Handle back button for list view - 复用收藏夹列表的返回手势逻辑
  useEffect(() => {
    const backAction = () => {
      // 如果显示设置页面，返回到之前的页面
      if (pageState?.showSettings) {
        if (onPageStateChange) {
          onPageStateChange({
            view: currentView,
            isEditing: (currentView === 'add' || currentView === 'edit') && !isInTypeSelector,
            showSettings: false,
          });
        }
        return true;
      }

      if (currentView === 'list') {
        setCurrentView('main');
        return true; // 阻止默认返回行为
      }
      if (currentView === 'add') {
        // 如果在类型选择页面，返回到主页
        if (showTypeSelector) {
          setCurrentView('main');
        } else {
          // 如果在编辑页面，检查未保存更改
          // 使用异步处理，但不阻塞BackHandler
          (async () => {
            try {
              const canExit = await addScreenRef.current?.checkBeforeExit();
              if (canExit) {
                setShowTypeSelector(true); // 返回到类型选择页面
              }
            } catch (error) {
              console.error('Error in checkBeforeExit:', error);
              setShowTypeSelector(true); // 出错时也允许退出
            }
          })();
        }
        return true; // 阻止默认返回行为
      }
      if (currentView === 'edit') {
        // 对于编辑页面，异步检查未保存更改
        // 使用异步处理，但不阻塞BackHandler
        (async () => {
          try {
            const canExit = await editScreenRef.current?.checkBeforeExit();
            if (canExit) {
              setEditingNote(null);
              setCurrentView('list'); // 编辑页面只能从列表进入，所以返回到列表
            }
          } catch (error) {
            console.error('Error in checkBeforeExit:', error);
            setEditingNote(null);
            setCurrentView('list'); // 出错时也允许退出
          }
        })();
        return true; // 阻止默认返回行为，让编辑页面处理
      }
      if (currentView === 'main') {
        // 从笔记主页返回到app主页
        if (onBack) {
          onBack();
        }
        return true; // 阻止默认返回行为
      }
      return true; // 阻止所有默认返回行为，由NotesScreen完全控制
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction,
    );

    return () => backHandler.remove();
  }, [currentView, pageState?.showSettings, onPageStateChange, onBack, isInTypeSelector, showTypeSelector]);

  // 渲染主要内容
  const renderMainContent = () => {
    // Show add note view
    if (currentView === 'add') {
      return (
        <NotesAddScreen
          ref={addScreenRef}
          onBack={async () => {
            // 如果在类型选择页面，返回到主页
            if (showTypeSelector) {
              setCurrentView('main');
            } else {
              // 如果在编辑页面，检查未保存更改
              const canExit = await addScreenRef.current?.checkBeforeExit();
              if (canExit) {
                setShowTypeSelector(true); // 返回到类型选择页面
              }
            }
          }}
          onSave={() => {
            // Refresh the main screen data when a note is saved
            setCurrentView('main');
          }}
          onTypeSelectorStateChange={setIsInTypeSelector}
          showTypeSelector={showTypeSelector}
          onTypeSelectorChange={setShowTypeSelector}
        />
      );
    }

    // Show edit note view
    if (currentView === 'edit' && editingNote) {
      return (
        <NotesEditScreen
          ref={editScreenRef}
          noteId={editingNote.id}
          note={editingNote.note}
          onBack={async () => {
            // 检查是否有未保存的更改
            const canExit = await editScreenRef.current?.checkBeforeExit();
            if (canExit) {
              setEditingNote(null);
              setCurrentView('list'); // 编辑页面只能从列表进入，所以返回到列表
            }
          }}
          onSave={() => {
            setEditingNote(null);
            setCurrentView('list'); // 编辑笔记保存后回到列表
          }}
        />
      );
    }

    // Show list view
    if (currentView === 'list') {
      return (
        <NotesListScreen
          onBack={() => {
            setCurrentView('main');
          }}
          onCreateNote={handleCreateNote}
          onViewNote={handleViewNote}
          onEditNote={handleEditNote}
        />
      );
    }

    // Show main view
    return (
      <NotesMainScreen
        onViewList={() => setCurrentView('list')}
        onCreateNote={handleCreateNote}
      />
    );
  };

  return (
    <>
      {/* 主要内容 */}
      {renderMainContent()}

      {/* 设置页面作为覆盖层 */}
      {pageState?.showSettings && (
        <NotesSettingsScreen
          onBack={() => {
            if (onPageStateChange) {
              onPageStateChange({
                view: currentView,
                isEditing: (currentView === 'add' || currentView === 'edit') && !isInTypeSelector,
                showSettings: false,
              });
            }
          }}
        />
      )}
    </>
  );
};

export default NotesScreen;