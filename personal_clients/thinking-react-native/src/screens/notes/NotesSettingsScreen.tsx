import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
} from 'react-native';

interface NotesSettingsScreenProps {
  onBack: () => void;
}

const NotesSettingsScreen: React.FC<NotesSettingsScreenProps> = ({ onBack }) => {
  const [showLineNumbers, setShowLineNumbers] = useState(false);
  const [selectedFont, setSelectedFont] = useState('霞鹜文楷');
  const [selectedBackground, setSelectedBackground] = useState('信纸样式');

  const fontOptions = ['霞鹜文楷', '系统默认', 'Roboto', 'Arial'];
  const backgroundOptions = ['信纸样式', '纯白背景', '护眼绿', '暖黄色'];

  const handleFontSelect = (font: string) => {
    setSelectedFont(font);
  };

  const handleBackgroundSelect = (background: string) => {
    setSelectedBackground(background);
  };

  return (
    <View style={styles.overlay}>
      <View style={styles.container}>
        <ScrollView style={styles.content} contentContainerStyle={styles.contentContainer}>
        {/* 字体设置 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>字体设置</Text>
          {fontOptions.map((font) => (
            <TouchableOpacity
              key={font}
              style={[
                styles.optionItem,
                selectedFont === font && styles.selectedOption,
              ]}
              onPress={() => handleFontSelect(font)}
            >
              <Text style={[
                styles.optionText,
                selectedFont === font && styles.selectedOptionText,
              ]}>
                {font}
              </Text>
              {selectedFont === font && (
                <Text style={styles.checkmark}>✓</Text>
              )}
            </TouchableOpacity>
          ))}
        </View>

        {/* 背景设置 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>背景样式</Text>
          {backgroundOptions.map((background) => (
            <TouchableOpacity
              key={background}
              style={[
                styles.optionItem,
                selectedBackground === background && styles.selectedOption,
              ]}
              onPress={() => handleBackgroundSelect(background)}
            >
              <Text style={[
                styles.optionText,
                selectedBackground === background && styles.selectedOptionText,
              ]}>
                {background}
              </Text>
              {selectedBackground === background && (
                <Text style={styles.checkmark}>✓</Text>
              )}
            </TouchableOpacity>
          ))}
        </View>

        {/* 显示设置 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>显示设置</Text>
          <View style={styles.switchItem}>
            <Text style={styles.switchLabel}>显示行序号</Text>
            <Switch
              value={showLineNumbers}
              onValueChange={setShowLineNumbers}
              trackColor={{ false: '#E0E0E0', true: '#6750A4' }}
              thumbColor={showLineNumbers ? '#FFFFFF' : '#F4F3F4'}
            />
          </View>
        </View>

        {/* 预览区域 */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>预览</Text>
          <View style={styles.previewContainer}>
            <Text style={styles.previewText}>
              这是一个预览示例{'\n'}
              字体：{selectedFont}{'\n'}
              背景：{selectedBackground}{'\n'}
              行序号：{showLineNumbers ? '显示' : '隐藏'}
            </Text>
          </View>
        </View>
      </ScrollView>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    zIndex: 1000,
    justifyContent: 'center',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
    width: '100%',
    height: '100%',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 16,
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 12,
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderRadius: 8,
    marginBottom: 4,
  },
  selectedOption: {
    backgroundColor: '#E8DEF8',
  },
  optionText: {
    fontSize: 16,
    color: '#49454F',
  },
  selectedOptionText: {
    color: '#6750A4',
    fontWeight: '500',
  },
  checkmark: {
    fontSize: 16,
    color: '#6750A4',
    fontWeight: 'bold',
  },
  switchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  switchLabel: {
    fontSize: 16,
    color: '#49454F',
  },
  previewContainer: {
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    padding: 12,
    borderWidth: 1,
    borderColor: '#E6E0E9',
  },
  previewText: {
    fontSize: 14,
    color: '#49454F',
    lineHeight: 20,
  },
});

export default NotesSettingsScreen;
