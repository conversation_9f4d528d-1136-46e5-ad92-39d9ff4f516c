import React from 'react';
import {render, fireEvent, waitFor} from '@testing-library/react-native';
import NotesEditScreen from '../NotesEditScreen';
import notesService from '../../../services/notesService';
import {NoteItem} from '../../../types';

// Mock the services
jest.mock('../../../services/notesService');
const mockNotesService = notesService as jest.Mocked<typeof notesService>;

// Mock the markdown component
jest.mock('react-native-markdown-display', () => {
  return function MockMarkdown({children}: {children: string}) {
    return children;
  };
});

// Mock the paper background component
jest.mock('../../../components/NotePaperBackground', () => {
  return function MockNotePaperBackground() {
    return null;
  };
});

describe('NotesEditScreen', () => {
  const mockNote: NoteItem = {
    id: '1',
    title: 'Test Note',
    content: 'Test content',
    tags: 'work,important',
    category: null,
    createTime: '2023-01-01T00:00:00Z',
    updateTime: '2023-01-01T00:00:00Z',
  };

  const defaultProps = {
    noteId: '1',
    note: mockNote,
    onBack: jest.fn(),
    onSave: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockNotesService.getNote.mockResolvedValue({
      success: true,
      data: mockNote,
      message: 'Success',
    });
    mockNotesService.updateNote.mockResolvedValue({
      success: true,
      data: true,
      message: 'Updated successfully',
    });
  });

  it('renders correctly with initial note data', async () => {
    const {getByDisplayValue, getByText} = render(
      <NotesEditScreen {...defaultProps} />
    );

    await waitFor(() => {
      expect(getByDisplayValue('Test Note')).toBeTruthy();
      expect(getByDisplayValue('Test content')).toBeTruthy();
    });
  });

  it('loads note data when no initial note is provided', async () => {
    const propsWithoutNote = {
      ...defaultProps,
      note: undefined,
    };

    render(<NotesEditScreen {...propsWithoutNote} />);

    await waitFor(() => {
      expect(mockNotesService.getNote).toHaveBeenCalledWith('1');
    });
  });

  it('handles title input changes and triggers auto-save', async () => {
    const {getByDisplayValue} = render(
      <NotesEditScreen {...defaultProps} />
    );

    const titleInput = getByDisplayValue('Test Note');
    fireEvent.changeText(titleInput, 'Updated Title');

    expect(titleInput.props.value).toBe('Updated Title');

    // Wait for auto-save to be triggered
    await waitFor(() => {
      expect(mockNotesService.updateNote).toHaveBeenCalled();
    }, {timeout: 3000});
  });

  it('handles content input changes and triggers auto-save', async () => {
    const {getByDisplayValue} = render(
      <NotesEditScreen {...defaultProps} />
    );

    const contentInput = getByDisplayValue('Test content');
    fireEvent.changeText(contentInput, 'Updated content');

    expect(contentInput.props.value).toBe('Updated content');

    // Wait for auto-save to be triggered
    await waitFor(() => {
      expect(mockNotesService.updateNote).toHaveBeenCalled();
    }, {timeout: 3000});
  });

  it('toggles between edit and preview modes', async () => {
    const {getByText, queryByDisplayValue} = render(
      <NotesEditScreen {...defaultProps} />
    );

    // Initially in edit mode
    await waitFor(() => {
      expect(queryByDisplayValue('Test content')).toBeTruthy();
    });

    // Switch to preview mode
    const previewButton = getByText('预览');
    fireEvent.press(previewButton);

    // Content input should not be visible in preview mode
    expect(queryByDisplayValue('Test content')).toBeFalsy();
  });

  it('handles tag selection', async () => {
    const {getByText} = render(
      <NotesEditScreen {...defaultProps} />
    );

    await waitFor(() => {
      const workTag = getByText('工作');
      expect(workTag).toBeTruthy();
      
      // Tag should be selected initially (from mockNote.tags)
      fireEvent.press(workTag);
    });
  });

  it('shows loading state when initially loading note', () => {
    const propsWithoutNote = {
      ...defaultProps,
      note: undefined,
    };

    const {getByText} = render(<NotesEditScreen {...propsWithoutNote} />);
    expect(getByText('加载中...')).toBeTruthy();
  });

  it('handles back navigation with unsaved changes', async () => {
    const mockOnBack = jest.fn();
    const {getByDisplayValue} = render(
      <NotesEditScreen {...defaultProps} onBack={mockOnBack} />
    );

    // Make changes
    const titleInput = getByDisplayValue('Test Note');
    fireEvent.changeText(titleInput, 'Changed Title');

    // Simulate back navigation - this would typically be handled by the parent component
    // In a real scenario, this would be triggered by hardware back button
    // For testing purposes, we'll just verify the state
    expect(titleInput.props.value).toBe('Changed Title');
  });

  it('handles auto-save errors gracefully', async () => {
    mockNotesService.updateNote.mockRejectedValue(new Error('Network error'));

    const {getByDisplayValue} = render(
      <NotesEditScreen {...defaultProps} />
    );

    const titleInput = getByDisplayValue('Test Note');
    fireEvent.changeText(titleInput, 'Updated Title');

    // Auto-save should be attempted but fail silently
    await waitFor(() => {
      expect(mockNotesService.updateNote).toHaveBeenCalled();
    }, {timeout: 3000});
  });
});
