import React, {useState} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  Chip,
  Surface,
  IconButton,
  Divider,
} from 'react-native-paper';
import {useNavigation} from '@react-navigation/native';
import {StackNavigationProp} from '@react-navigation/stack';

// Import services and types
import {tasksService} from '@/services/tasksService';
import {TasksStackParamList, TaskPriority} from '@/types';

type TaskAddScreenNavigationProp = StackNavigationProp<TasksStackParamList>;

const TaskAddScreen: React.FC = () => {
  const navigation = useNavigation<TaskAddScreenNavigationProp>();
  
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [priority, setPriority] = useState<TaskPriority>('medium');
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');
  const [estimatedDuration, setEstimatedDuration] = useState('');
  const [loading, setLoading] = useState(false);

  const priorities: {value: TaskPriority; label: string; color: string}[] = [
    {value: 'low', label: '低', color: '#9E9E9E'},
    {value: 'medium', label: '中', color: '#2196F3'},
    {value: 'high', label: '高', color: '#FF9800'},
    {value: 'urgent', label: '紧急', color: '#F44336'},
  ];

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleSubmit = async () => {
    if (!title.trim()) {
      Alert.alert('错误', '请输入任务标题');
      return;
    }

    setLoading(true);
    try {
      const taskData = {
        title: title.trim(),
        description: description.trim() || undefined,
        priority,
        tags,
        estimatedDuration: estimatedDuration ? parseInt(estimatedDuration, 10) : undefined,
        status: 'todo' as const,
      };

      const response = await tasksService.createTask(taskData);
      
      if (response.success) {
        Alert.alert(
          '成功',
          '任务创建成功',
          [
            {
              text: '确定',
              onPress: () => navigation.goBack(),
            },
          ],
        );
      }
    } catch (error) {
      console.error('Error creating task:', error);
      Alert.alert('错误', '创建任务失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <Card style={styles.formCard}>
        <Card.Content>
          <Text style={styles.sectionTitle}>基本信息</Text>
          
          <TextInput
            label="任务标题 *"
            value={title}
            onChangeText={setTitle}
            mode="outlined"
            style={styles.input}
            placeholder="输入任务标题"
          />
          
          <TextInput
            label="任务描述"
            value={description}
            onChangeText={setDescription}
            mode="outlined"
            multiline
            numberOfLines={4}
            style={styles.input}
            placeholder="输入任务描述（可选）"
          />
          
          <Divider style={styles.divider} />
          
          <Text style={styles.sectionTitle}>优先级</Text>
          <View style={styles.priorityContainer}>
            {priorities.map(p => (
              <Chip
                key={p.value}
                mode={priority === p.value ? 'flat' : 'outlined'}
                onPress={() => setPriority(p.value)}
                style={[
                  styles.priorityChip,
                  priority === p.value && {backgroundColor: p.color + '20'},
                ]}>
                <Text style={{color: p.color}}>{p.label}</Text>
              </Chip>
            ))}
          </View>
          
          <Divider style={styles.divider} />
          
          <Text style={styles.sectionTitle}>标签</Text>
          <View style={styles.tagInputContainer}>
            <TextInput
              label="添加标签"
              value={newTag}
              onChangeText={setNewTag}
              mode="outlined"
              style={styles.tagInput}
              placeholder="输入标签"
              onSubmitEditing={handleAddTag}
            />
            <IconButton
              icon="add"
              mode="contained"
              onPress={handleAddTag}
              disabled={!newTag.trim()}
            />
          </View>
          
          {tags.length > 0 && (
            <View style={styles.tagsContainer}>
              {tags.map(tag => (
                <Chip
                  key={tag}
                  mode="outlined"
                  onClose={() => handleRemoveTag(tag)}
                  style={styles.tagChip}>
                  {tag}
                </Chip>
              ))}
            </View>
          )}
          
          <Divider style={styles.divider} />
          
          <Text style={styles.sectionTitle}>预估时间</Text>
          <TextInput
            label="预估时间（分钟）"
            value={estimatedDuration}
            onChangeText={setEstimatedDuration}
            mode="outlined"
            keyboardType="numeric"
            style={styles.input}
            placeholder="输入预估时间"
          />
        </Card.Content>
      </Card>
      
      <View style={styles.buttonContainer}>
        <Button
          mode="outlined"
          onPress={() => navigation.goBack()}
          style={styles.button}
          disabled={loading}>
          取消
        </Button>
        <Button
          mode="contained"
          onPress={handleSubmit}
          style={styles.button}
          loading={loading}
          disabled={loading}>
          创建任务
        </Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  formCard: {
    borderRadius: 12,
    elevation: 2,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 16,
  },
  input: {
    marginBottom: 16,
  },
  divider: {
    marginVertical: 16,
  },
  priorityContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  priorityChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  tagInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  tagInput: {
    flex: 1,
    marginRight: 8,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  tagChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    flex: 0.48,
  },
});

export default TaskAddScreen; 