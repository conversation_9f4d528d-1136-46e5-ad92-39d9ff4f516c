import React from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {
  Text,
  Card,
  Title,
  Paragraph,
  Button,
  Chip,
  Surface,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';

const TaskDetailScreen: React.FC = () => {
  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <Surface style={styles.headerCard}>
        <View style={styles.headerContent}>
          <Icon name="assignment" size={48} color="#4ECDC4" />
          <Title style={styles.headerTitle}>任务详情</Title>
          <Paragraph style={styles.headerDescription}>
            查看任务的详细信息和进度
          </Paragraph>
        </View>
      </Surface>

      <Card style={styles.statusCard}>
        <Card.Content>
          <Title>开发状态</Title>
          <Paragraph style={styles.statusText}>
            任务详情页面正在开发中...
          </Paragraph>
        </Card.Content>
      </Card>

      <View style={styles.actionButtons}>
        <Button
          mode="outlined"
          onPress={() => console.log('编辑任务')}
          style={styles.actionButton}>
          编辑任务
        </Button>
        <Button
          mode="contained"
          onPress={() => console.log('标记完成')}
          style={styles.actionButton}>
          标记完成
        </Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  headerCard: {
    padding: 24,
    marginBottom: 24,
    borderRadius: 12,
    elevation: 2,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginTop: 16,
    marginBottom: 8,
  },
  headerDescription: {
    fontSize: 16,
    color: '#49454F',
    textAlign: 'center',
    lineHeight: 24,
  },
  statusCard: {
    marginBottom: 24,
    borderRadius: 12,
    elevation: 1,
  },
  statusText: {
    fontSize: 14,
    color: '#49454F',
    marginBottom: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 0.48,
  },
});

export default TaskDetailScreen; 