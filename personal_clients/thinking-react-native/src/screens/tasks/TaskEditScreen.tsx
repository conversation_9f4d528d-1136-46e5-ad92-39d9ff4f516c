import React from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
} from 'react-native';
import {
  Text,
  Card,
  Title,
  Paragraph,
  Button,
  Surface,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialIcons';

const TaskEditScreen: React.FC = () => {
  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <Surface style={styles.headerCard}>
        <View style={styles.headerContent}>
          <Icon name="edit" size={48} color="#FF9800" />
          <Title style={styles.headerTitle}>编辑任务</Title>
          <Paragraph style={styles.headerDescription}>
            修改任务信息和状态
          </Paragraph>
        </View>
      </Surface>

      <Card style={styles.statusCard}>
        <Card.Content>
          <Title>开发状态</Title>
          <Paragraph style={styles.statusText}>
            任务编辑页面正在开发中...
          </Paragraph>
        </Card.Content>
      </Card>

      <View style={styles.actionButtons}>
        <Button
          mode="outlined"
          onPress={() => console.log('取消编辑')}
          style={styles.actionButton}>
          取消
        </Button>
        <Button
          mode="contained"
          onPress={() => console.log('保存更改')}
          style={styles.actionButton}>
          保存
        </Button>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  headerCard: {
    padding: 24,
    marginBottom: 24,
    borderRadius: 12,
    elevation: 2,
  },
  headerContent: {
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginTop: 16,
    marginBottom: 8,
  },
  headerDescription: {
    fontSize: 16,
    color: '#49454F',
    textAlign: 'center',
    lineHeight: 24,
  },
  statusCard: {
    marginBottom: 24,
    borderRadius: 12,
    elevation: 1,
  },
  statusText: {
    fontSize: 14,
    color: '#49454F',
    marginBottom: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 0.48,
  },
});

export default TaskEditScreen; 