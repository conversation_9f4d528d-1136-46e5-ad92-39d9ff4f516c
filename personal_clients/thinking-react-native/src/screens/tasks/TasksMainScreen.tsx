import React from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Text,
  TouchableOpacity,
  Alert,
} from 'react-native';

const TasksMainScreen: React.FC = () => {
  const handleCreateTask = () => {
    Alert.alert('创建任务', '任务管理功能正在开发中...');
  };

  const handleViewAllTasks = () => {
    Alert.alert('查看任务', '任务列表功能正在开发中...');
  };

  return (
    <ScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}>

      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerContent}>
          <View style={[styles.iconContainer, {backgroundColor: '#4ECDC4'}]}>
            <Text style={styles.iconText}>📋</Text>
          </View>
          <View style={styles.headerText}>
            <Text style={styles.headerTitle}>任务管理</Text>
            <Text style={styles.headerDescription}>
              高效的任务跟踪和项目管理
            </Text>
          </View>
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.actionsContainer}>
        <TouchableOpacity
          onPress={handleCreateTask}
          style={styles.actionButton}>
          <Text style={styles.actionButtonText}>➕ 新建任务</Text>
        </TouchableOpacity>
        <TouchableOpacity
          onPress={handleViewAllTasks}
          style={[styles.actionButton, styles.actionButtonOutlined]}>
          <Text style={styles.actionButtonTextOutlined}>📋 所有任务</Text>
        </TouchableOpacity>
      </View>

      {/* Coming Soon Card */}
      <View style={styles.comingSoonCard}>
        <View style={styles.comingSoonContent}>
          <Text style={styles.constructionIcon}>🚧</Text>
          <Text style={styles.comingSoonTitle}>功能开发中</Text>
          <Text style={styles.comingSoonText}>
            任务管理功能正在积极开发中，敬请期待！
          </Text>
          <Text style={styles.comingSoonFeatures}>
            即将推出的功能：
          </Text>
          <View style={styles.featuresList}>
            <Text style={styles.featureItem}>• 任务创建和编辑</Text>
            <Text style={styles.featureItem}>• 优先级管理</Text>
            <Text style={styles.featureItem}>• 进度跟踪</Text>
            <Text style={styles.featureItem}>• 时间统计</Text>
            <Text style={styles.featureItem}>• 团队协作</Text>
          </View>
        </View>
      </View>

      {/* Placeholder Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>0</Text>
          <Text style={styles.statLabel}>总任务</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>0</Text>
          <Text style={styles.statLabel}>已完成</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>0</Text>
          <Text style={styles.statLabel}>进行中</Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8F9FA',
  },
  contentContainer: {
    padding: 16,
    paddingBottom: 32,
  },
  header: {
    backgroundColor: '#FFFFFF',
    padding: 20,
    marginBottom: 24,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  iconText: {
    fontSize: 32,
    color: '#FFFFFF',
  },
  headerText: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 4,
  },
  headerDescription: {
    fontSize: 16,
    color: '#49454F',
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 24,
  },
  actionButton: {
    flex: 0.48,
    backgroundColor: '#6750A4',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonOutlined: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#6750A4',
  },
  actionButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: 'bold',
  },
  actionButtonTextOutlined: {
    color: '#6750A4',
    fontSize: 16,
    fontWeight: 'bold',
  },
  comingSoonCard: {
    backgroundColor: '#FFFFFF',
    marginBottom: 24,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  comingSoonContent: {
    alignItems: 'center',
    paddingVertical: 24,
  },
  constructionIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  comingSoonTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 8,
  },
  comingSoonText: {
    fontSize: 16,
    color: '#49454F',
    textAlign: 'center',
    marginBottom: 16,
  },
  comingSoonFeatures: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1C1B1F',
    marginBottom: 12,
  },
  featuresList: {
    alignItems: 'flex-start',
  },
  featureItem: {
    fontSize: 14,
    color: '#49454F',
    marginBottom: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: '#FFFFFF',
    flex: 0.31,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  statValue: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6750A4',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#49454F',
    textAlign: 'center',
  },
});

export default TasksMainScreen;