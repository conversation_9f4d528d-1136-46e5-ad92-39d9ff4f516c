import axios from 'axios';
import {
  DriveFile,
  DriveStorageInfo,
  DriveApiResponse,
  DriveFileListResponse,
  DriveUploadRequest,
  DriveUploadTask,
  DriveChunkUploadRequest,
  DriveUploadProgress,
  DriveInitChunkUploadRequest,
  DriveUploadChunkRequest,
  DriveCompleteChunkUploadRequest,
  DriveFolderCreateRequest,
  DriveFileRenameRequest,
  DriveFileMoveRequest,
  PaginatedResponse,
} from '../types';

// API configuration
// 使用 adb reverse 端口转发: adb reverse tcp:8080 tcp:443
const API_BASE_URL = 'https://local.luzeshu.cn/api';
const API_HEADERS = {
  'Content-Type': 'application/json',
  'bullet': '36fdf066-9e42-11ec-b41e-525400043ced',
};

// 开发模式配置 - 设置为false来测试真实API错误处理
const USE_MOCK_ON_ERROR = false;

// 上传进度回调接口
interface UploadProgress {
  percentage: number;
  uploadedBytes: number;
  totalBytes: number;
  currentChunk: number;
  totalChunks: number;
}

// 分片上传请求接口
interface ChunkUploadRequest {
  fileName: string;
  fileSize: number;
  parentId?: string | null;
  fileData: any;
  chunkSize: number;
  onProgress?: (progress: UploadProgress) => void;
}

class DriveService {
  private axiosInstance;

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000, // 30秒超时，文件上传可能需要更长时间
      headers: API_HEADERS,
    });
  }

  // 获取文件列表
  async getFileList(
    parentId?: string | null,
    offset = 0,
    size = 20,
    keyword?: string
  ): Promise<PaginatedResponse<DriveFile>> {
    try {
      const params: any = {
        offset: offset,
        size: size,
      };

      if (parentId) {
        params.parent_id = parentId;
      }

      if (keyword && keyword.trim()) {
        params.keyword = keyword.trim();
      }

      const response = await this.axiosInstance.get('/drive/files', {
        params: params,
      });

      console.log('Drive API Response:', JSON.stringify(response.data, null, 2));

      // 支持多种响应格式
      let driveData: any = null;
      let filesList: any[] = [];

      if (response.data?.data) {
        driveData = response.data;
        filesList = response.data.data;
      } else if (Array.isArray(response.data)) {
        filesList = response.data;
      } else {
        throw new Error('Invalid response format');
      }

      if (Array.isArray(filesList)) {
        const items: DriveFile[] = filesList.map((item: any) => ({
          id: item.ID?.toString() || item.id?.toString() || '',
          name: item.Name || item.name || '',
          path: item.Path || item.path || '',
          parentId: item.ParentID?.toString() || item.parent_id?.toString() || null,
          fileType: item.FileType || item.file_type || 'file',
          mimeType: item.MimeType || item.mime_type || null,
          size: item.Size || item.size || 0,
          hash: item.Hash || item.hash || null,
          storagePath: item.StoragePath || item.storage_path || null,
          thumbnailPath: item.ThumbnailPath || item.thumbnail_path || null,
          isPublic: item.IsPublic !== undefined ? item.IsPublic : (item.is_public || false),
          downloadCount: item.DownloadCount || item.download_count || 0,
          createdAt: item.CreatedAt || item.created_at || new Date().toISOString(),
          updatedAt: item.UpdatedAt || item.updated_at || new Date().toISOString(),
        }));

        return {
          items,
          total: driveData?.total || items.length,
          page: Math.floor(offset / size) + 1,
          pageSize: size,
          hasMore: driveData?.has_more !== undefined ? driveData.has_more : items.length >= size,
        };
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error fetching drive files:', error);

      if (USE_MOCK_ON_ERROR) {
        return this.getMockFileList(offset, size);
      } else {
        // 返回空列表而不是抛出错误
        return {
          items: [],
          total: 0,
          page: Math.floor(offset / size) + 1,
          pageSize: size,
          hasMore: false,
        };
      }
    }
  }

  // 获取存储信息
  async getStorageInfo(): Promise<DriveApiResponse<DriveStorageInfo>> {
    try {
      const response = await this.axiosInstance.get('/drive/storage/info');

      if (response.data) {
        const data = response.data;
        return {
          success: true,
          data: {
            totalSpace: data.total_space || 0,
            usedSpace: data.used_space || 0,
            freeSpace: data.free_space || 0,
            usagePercent: data.usage_percent || 0,
          },
          message: 'Storage info retrieved successfully',
        };
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error fetching storage info:', error);

      if (USE_MOCK_ON_ERROR) {
        return {
          success: true,
          data: this.getMockStorageInfo(),
          message: 'Storage info retrieved successfully (mock)',
        };
      } else {
        return {
          success: false,
          data: {
            totalSpace: 0,
            usedSpace: 0,
            freeSpace: 0,
            usagePercent: 0,
          },
          message: error instanceof Error ? error.message : '获取存储信息失败，请检查网络连接',
        };
      }
    }
  }

  // 创建文件夹
  async createFolder(request: DriveFolderCreateRequest): Promise<DriveApiResponse<DriveFile>> {
    try {
      const requestData: any = {
        name: request.name,
      };

      if (request.parentId) {
        requestData.parent_id = request.parentId;
      }

      const response = await this.axiosInstance.post('/drive/folders', requestData);

      if (response.data?.data) {
        const folder = response.data.data;
        return {
          success: true,
          data: {
            id: folder.ID?.toString() || folder.id?.toString() || '',
            name: folder.Name || folder.name || '',
            path: folder.Path || folder.path || '',
            parentId: folder.ParentID?.toString() || folder.parent_id?.toString() || null,
            fileType: 'folder',
            mimeType: null,
            size: 0,
            hash: null,
            storagePath: folder.StoragePath || folder.storage_path || null,
            isPublic: folder.IsPublic !== undefined ? folder.IsPublic : (folder.is_public || false),
            downloadCount: 0,
            createdAt: folder.CreatedAt || folder.created_at || new Date().toISOString(),
            updatedAt: folder.UpdatedAt || folder.updated_at || new Date().toISOString(),
          },
          message: response.data.message || 'Folder created successfully',
        };
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error creating folder:', error);

      if (USE_MOCK_ON_ERROR) {
        return {
          success: true,
          data: this.getMockFolder(request.name),
          message: 'Folder created successfully (mock)',
        };
      } else {
        return {
          success: false,
          data: {} as DriveFile,
          message: error instanceof Error ? error.message : '创建文件夹失败，请检查网络连接',
        };
      }
    }
  }

  // 上传文件
  async uploadFile(request: DriveUploadRequest): Promise<DriveApiResponse<DriveFile>> {
    try {
      const formData = new FormData();

      // React Native FormData 需要特定格式
      formData.append('file', {
        uri: request.fileData.uri,
        type: request.fileData.type,
        name: request.fileData.name,
      } as any);

      if (request.parentId) {
        formData.append('parent_id', request.parentId);
      }

      console.log('=== 文件上传详细信息 ===');
      console.log('文件 URI:', request.fileData.uri);
      console.log('文件类型:', request.fileData.type);
      console.log('文件名:', request.fileData.name);
      console.log('父目录 ID:', request.parentId);
      console.log('API 基础 URL:', API_BASE_URL);
      console.log('完整上传 URL:', `${API_BASE_URL}/drive/upload`);
      console.log('FormData 内容:', formData);
      console.log('========================');

      // 使用原生 fetch 而不是 axios，避免 Content-Type 问题
      const response = await fetch(`${API_BASE_URL}/drive/upload`, {
        method: 'POST',
        headers: {
          'bullet': '36fdf066-9e42-11ec-b41e-525400043ced',
          // 不设置 Content-Type，让浏览器自动设置 multipart/form-data
        },
        body: formData,
      });

      console.log('=== 上传响应信息 ===');
      console.log('响应状态:', response.status);
      console.log('响应状态文本:', response.statusText);
      console.log('==================');

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const responseData = await response.json();
      console.log('响应数据:', responseData);

      if (responseData?.data) {
        const file = responseData.data;
        return {
          success: true,
          data: {
            id: file.ID?.toString() || file.id?.toString() || '',
            name: file.Name || file.name || '',
            path: file.Path || file.path || '',
            parentId: file.ParentID?.toString() || file.parent_id?.toString() || null,
            fileType: 'file',
            mimeType: file.MimeType || file.mime_type || null,
            size: file.Size || file.size || 0,
            hash: file.Hash || file.hash || null,
            storagePath: file.StoragePath || file.storage_path || null,
            isPublic: file.IsPublic !== undefined ? file.IsPublic : (file.is_public || false),
            downloadCount: file.DownloadCount || file.download_count || 0,
            createdAt: file.CreatedAt || file.created_at || new Date().toISOString(),
            updatedAt: file.UpdatedAt || file.updated_at || new Date().toISOString(),
          },
          message: responseData.message || 'File uploaded successfully',
        };
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error('=== 文件上传错误详情 ===');
      console.error('错误对象:', error);
      console.error('错误类型:', typeof error);
      console.error('错误构造函数:', error?.constructor?.name);

      if (error && typeof error === 'object') {
        const errorObj = error as any;
        console.error('错误消息:', errorObj.message);
        console.error('错误代码:', errorObj.code);
        console.error('错误堆栈:', errorObj.stack);

        if ('response' in errorObj) {
          console.error('响应状态:', errorObj.response?.status);
          console.error('响应头:', errorObj.response?.headers);
          console.error('响应数据:', errorObj.response?.data);
        }

        if ('request' in errorObj) {
          console.error('请求对象:', errorObj.request);
          console.error('请求 URL:', errorObj.config?.url);
          console.error('请求方法:', errorObj.config?.method);
          console.error('请求头:', errorObj.config?.headers);
        }
      }
      console.error('========================');

      // 详细的错误信息
      let errorMessage = '文件上传失败';
      if (error instanceof Error) {
        if (error.message.includes('Network Error')) {
          errorMessage = '网络连接失败，请检查网络设置';
        } else if (error.message.includes('timeout')) {
          errorMessage = '上传超时，请重试';
        } else {
          errorMessage = error.message;
        }
      }

      // 如果是 axios 错误，提供更多信息
      if (error && typeof error === 'object' && 'response' in error) {
        const axiosError = error as any;
        if (axiosError.response) {
          errorMessage = `服务器错误 ${axiosError.response.status}: ${axiosError.response.data?.message || '未知错误'}`;
        } else if (axiosError.request) {
          errorMessage = '无法连接到服务器，请检查网络连接';
        }
      }

      if (USE_MOCK_ON_ERROR) {
        return {
          success: true,
          data: this.getMockFile(request.fileName),
          message: 'File uploaded successfully (mock)',
        };
      } else {
        return {
          success: false,
          data: {} as DriveFile,
          message: errorMessage,
        };
      }
    }
  }

  // 初始化分片上传
  async initChunkUpload(request: DriveInitChunkUploadRequest): Promise<DriveApiResponse<DriveUploadTask>> {
    try {
      const requestData: any = {
        file_name: request.fileName,
        file_size: request.fileSize,
        chunk_size: request.chunkSize || 1024 * 1024, // 默认1MB
      };

      if (request.parentId) {
        requestData.parent_id = request.parentId;
      }

      const response = await this.axiosInstance.post('/drive/init-chunk-upload', requestData);

      if (response.data?.data) {
        const task = response.data.data;
        return {
          success: true,
          data: {
            id: task.ID?.toString() || task.id?.toString() || '',
            fileName: task.FileName || task.file_name || '',
            fileSize: task.FileSize || task.file_size || 0,
            chunkSize: task.ChunkSize || task.chunk_size || 0,
            totalChunks: task.TotalChunks || task.total_chunks || 0,
            uploadedChunks: task.UploadedChunks || task.uploaded_chunks || 0,
            uploadPath: task.UploadPath || task.upload_path || '',
            tempDir: task.TempDir || task.temp_dir || '',
            status: task.Status || task.status || 'pending',
            errorMessage: task.ErrorMessage || task.error_message || null,
            createdAt: task.CreatedAt || task.created_at || new Date().toISOString(),
          },
          message: response.data.message || 'Chunk upload initialized successfully',
        };
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error initializing chunk upload:', error);
      return {
        success: false,
        data: {} as DriveUploadTask,
        message: error instanceof Error ? error.message : '初始化分片上传失败',
      };
    }
  }

  // 上传分片
  async uploadChunk(request: DriveUploadChunkRequest): Promise<DriveApiResponse<DriveUploadTask>> {
    try {
      const formData = new FormData();
      formData.append('task_id', request.taskId);
      formData.append('chunk_index', request.chunkIndex.toString());
      formData.append('chunk_data', request.chunkData);

      const response = await fetch(`${API_BASE_URL}/drive/upload-chunk`, {
        method: 'POST',
        headers: {
          'bullet': '36fdf066-9e42-11ec-b41e-525400043ced',
        },
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const responseData = await response.json();

      if (responseData?.data) {
        const task = responseData.data;
        return {
          success: true,
          data: {
            id: task.ID?.toString() || task.id?.toString() || '',
            fileName: task.FileName || task.file_name || '',
            fileSize: task.FileSize || task.file_size || 0,
            chunkSize: task.ChunkSize || task.chunk_size || 0,
            totalChunks: task.TotalChunks || task.total_chunks || 0,
            uploadedChunks: task.UploadedChunks || task.uploaded_chunks || 0,
            uploadPath: task.UploadPath || task.upload_path || '',
            tempDir: task.TempDir || task.temp_dir || '',
            status: task.Status || task.status || 'pending',
            errorMessage: task.ErrorMessage || task.error_message || null,
            createdAt: task.CreatedAt || task.created_at || new Date().toISOString(),
          },
          message: responseData.message || 'Chunk uploaded successfully',
        };
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error uploading chunk:', error);
      return {
        success: false,
        data: {} as DriveUploadTask,
        message: error instanceof Error ? error.message : '上传分片失败',
      };
    }
  }

  // 完成分片上传
  async completeChunkUpload(request: DriveCompleteChunkUploadRequest): Promise<DriveApiResponse<DriveFile>> {
    try {
      const requestData = {
        task_id: request.taskId,
      };

      const response = await this.axiosInstance.post('/drive/complete-chunk-upload', requestData);

      if (response.data?.data) {
        const file = response.data.data;
        return {
          success: true,
          data: {
            id: file.ID?.toString() || file.id?.toString() || '',
            name: file.Name || file.name || '',
            path: file.Path || file.path || '',
            parentId: file.ParentID?.toString() || file.parent_id?.toString() || null,
            fileType: 'file',
            mimeType: file.MimeType || file.mime_type || null,
            size: file.Size || file.size || 0,
            hash: file.Hash || file.hash || null,
            storagePath: file.StoragePath || file.storage_path || null,
            isPublic: file.IsPublic !== undefined ? file.IsPublic : (file.is_public || false),
            downloadCount: file.DownloadCount || file.download_count || 0,
            createdAt: file.CreatedAt || file.created_at || new Date().toISOString(),
            updatedAt: file.UpdatedAt || file.updated_at || new Date().toISOString(),
          },
          message: response.data.message || 'File upload completed successfully',
        };
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error completing chunk upload:', error);
      return {
        success: false,
        data: {} as DriveFile,
        message: error instanceof Error ? error.message : '完成分片上传失败',
      };
    }
  }

  // 分片上传文件
  async uploadFileWithProgress(request: ChunkUploadRequest): Promise<DriveApiResponse<DriveFile>> {
    try {
      // 1. 初始化分片上传
      const initResponse = await fetch(`${API_BASE_URL}/drive/init-chunk-upload`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'bullet': '36fdf066-9e42-11ec-b41e-525400043ced',
        },
        body: JSON.stringify({
          file_name: request.fileName,
          file_size: request.fileSize,
          parent_id: request.parentId ? parseInt(request.parentId) : null,
          chunk_size: request.chunkSize,
        }),
      });

      if (!initResponse.ok) {
        throw new Error(`初始化上传失败: ${initResponse.statusText}`);
      }

      const initData = await initResponse.json();
      const taskId = initData.data.ID;

      // 2. 建立WebSocket连接以接收进度更新
      const ws = new WebSocket(`wss://local.luzeshu.cn/api/drive/upload-progress?task_id=${taskId}`);
      
      ws.onmessage = (event) => {
        const progress = JSON.parse(event.data);
        if (request.onProgress) {
          request.onProgress({
            percentage: progress.progress,
            uploadedBytes: progress.uploaded_bytes,
            totalBytes: progress.total_bytes,
            currentChunk: Math.floor(progress.uploaded_bytes / request.chunkSize),
            totalChunks: Math.ceil(request.fileSize / request.chunkSize),
          });
        }
      };

      // 3. 分片上传
      const totalChunks = Math.ceil(request.fileSize / request.chunkSize);
      const fileData = await fetch(request.fileData.uri).then(r => r.blob());

      for (let i = 0; i < totalChunks; i++) {
        const start = i * request.chunkSize;
        const end = Math.min(start + request.chunkSize, request.fileSize);
        const chunk = fileData.slice(start, end);

        const formData = new FormData();
        formData.append('task_id', taskId.toString());
        formData.append('chunk_index', i.toString());
        formData.append('chunk_data', {
          uri: request.fileData.uri,
          type: request.fileData.type,
          name: `chunk_${i}`,
        } as any);

        const chunkResponse = await fetch(`${API_BASE_URL}/drive/upload-chunk`, {
          method: 'POST',
          headers: {
            'bullet': '36fdf066-9e42-11ec-b41e-525400043ced',
          },
          body: formData,
        });

        if (!chunkResponse.ok) {
          ws.close();
          throw new Error(`上传分片 ${i} 失败: ${chunkResponse.statusText}`);
        }
      }

      // 4. 完成上传
      const completeResponse = await fetch(`${API_BASE_URL}/drive/complete-chunk-upload`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'bullet': '36fdf066-9e42-11ec-b41e-525400043ced',
        },
        body: JSON.stringify({
          task_id: taskId,
        }),
      });

      ws.close();

      if (!completeResponse.ok) {
        throw new Error(`完成上传失败: ${completeResponse.statusText}`);
      }

      const completeData = await completeResponse.json();
      return {
        success: true,
        data: completeData.data,
      };
    } catch (error: any) {
      console.error('Upload error:', error);
      return {
        success: false,
        data: null as any,
        message: error.message || '上传失败',
      };
    }
  }

  // 读取文件为Blob对象
  private async readFileAsBlob(fileData: any): Promise<Blob | null> {
    try {
      // 在React Native中，我们需要使用fetch读取文件
      const response = await fetch(fileData.uri);
      const blob = await response.blob();
      return blob;
    } catch (error) {
      console.error('Error reading file as blob:', error);
      return null;
    }
  }

  // Mock数据方法
  private getMockFileList(offset: number, size: number): PaginatedResponse<DriveFile> {
    const mockFiles: DriveFile[] = [
      {
        id: '1',
        name: '文档',
        path: '/文档',
        parentId: null,
        fileType: 'folder',
        mimeType: null,
        size: 0,
        hash: null,
        storagePath: null,
        isPublic: false,
        downloadCount: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: '2',
        name: '示例文件.txt',
        path: '/示例文件.txt',
        parentId: null,
        fileType: 'file',
        mimeType: 'text/plain',
        size: 1024,
        hash: 'abc123',
        storagePath: null,
        isPublic: false,
        downloadCount: 5,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ];

    const startIndex = offset;
    const endIndex = Math.min(offset + size, mockFiles.length);
    const items = mockFiles.slice(startIndex, endIndex);

    return {
      items,
      total: mockFiles.length,
      page: Math.floor(offset / size) + 1,
      pageSize: size,
      hasMore: endIndex < mockFiles.length,
    };
  }

  private getMockStorageInfo(): DriveStorageInfo {
    return {
      totalSpace: 1024 * 1024 * 1024 * 100, // 100GB
      usedSpace: 1024 * 1024 * 1024 * 25,   // 25GB
      freeSpace: 1024 * 1024 * 1024 * 75,   // 75GB
      usagePercent: 25,
    };
  }

  private getMockFolder(name: string): DriveFile {
    return {
      id: Date.now().toString(),
      name,
      path: `/${name}`,
      parentId: null,
      fileType: 'folder',
      mimeType: null,
      size: 0,
      hash: null,
      storagePath: null,
      isPublic: false,
      downloadCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  private getMockFile(name: string): DriveFile {
    return {
      id: Date.now().toString(),
      name,
      path: `/${name}`,
      parentId: null,
      fileType: 'file',
      mimeType: 'application/octet-stream',
      size: 1024,
      hash: 'mock-hash',
      storagePath: null,
      isPublic: false,
      downloadCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  // 删除文件
  async deleteFile(fileId: string): Promise<DriveApiResponse<boolean>> {
    try {
      const response = await this.axiosInstance.delete(`/drive/files/${fileId}`);

      return {
        success: true,
        data: true,
        message: response.data.message || 'File deleted successfully',
      };
    } catch (error) {
      console.error('Error deleting file:', error);

      if (USE_MOCK_ON_ERROR) {
        return {
          success: true,
          data: true,
          message: 'File deleted successfully (mock)',
        };
      } else {
        return {
          success: false,
          data: false,
          message: error instanceof Error ? error.message : '删除文件失败，请检查网络连接',
        };
      }
    }
  }

  // 重命名文件
  async renameFile(request: DriveFileRenameRequest): Promise<DriveApiResponse<DriveFile>> {
    try {
      const requestData = {
        new_name: request.newName,
      };

      const response = await this.axiosInstance.put(`/drive/files/${request.fileId}/rename`, requestData);

      if (response.data?.data) {
        const file = response.data.data;
        return {
          success: true,
          data: {
            id: file.ID?.toString() || file.id?.toString() || '',
            name: file.Name || file.name || '',
            path: file.Path || file.path || '',
            parentId: file.ParentID?.toString() || file.parent_id?.toString() || null,
            fileType: file.FileType || file.file_type || 'file',
            mimeType: file.MimeType || file.mime_type || null,
            size: file.Size || file.size || 0,
            hash: file.Hash || file.hash || null,
            storagePath: file.StoragePath || file.storage_path || null,
            isPublic: file.IsPublic !== undefined ? file.IsPublic : (file.is_public || false),
            downloadCount: file.DownloadCount || file.download_count || 0,
            createdAt: file.CreatedAt || file.created_at || new Date().toISOString(),
            updatedAt: file.UpdatedAt || file.updated_at || new Date().toISOString(),
          },
          message: response.data.message || 'File renamed successfully',
        };
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error renaming file:', error);

      if (USE_MOCK_ON_ERROR) {
        return {
          success: true,
          data: this.getMockFile(request.newName),
          message: 'File renamed successfully (mock)',
        };
      } else {
        return {
          success: false,
          data: {} as DriveFile,
          message: error instanceof Error ? error.message : '重命名文件失败，请检查网络连接',
        };
      }
    }
  }

  // 移动文件
  async moveFile(request: DriveFileMoveRequest): Promise<DriveApiResponse<DriveFile>> {
    try {
      const requestData = {
        new_parent_id: request.newParentId,
      };

      const response = await this.axiosInstance.put(`/drive/files/${request.fileId}/move`, requestData);

      if (response.data?.data) {
        const file = response.data.data;
        return {
          success: true,
          data: {
            id: file.ID?.toString() || file.id?.toString() || '',
            name: file.Name || file.name || '',
            path: file.Path || file.path || '',
            parentId: file.ParentID?.toString() || file.parent_id?.toString() || null,
            fileType: file.FileType || file.file_type || 'file',
            mimeType: file.MimeType || file.mime_type || null,
            size: file.Size || file.size || 0,
            hash: file.Hash || file.hash || null,
            storagePath: file.StoragePath || file.storage_path || null,
            isPublic: file.IsPublic !== undefined ? file.IsPublic : (file.is_public || false),
            downloadCount: file.DownloadCount || file.download_count || 0,
            createdAt: file.CreatedAt || file.created_at || new Date().toISOString(),
            updatedAt: file.UpdatedAt || file.updated_at || new Date().toISOString(),
          },
          message: response.data.message || 'File moved successfully',
        };
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error moving file:', error);

      if (USE_MOCK_ON_ERROR) {
        return {
          success: true,
          data: this.getMockFile('moved-file'),
          message: 'File moved successfully (mock)',
        };
      } else {
        return {
          success: false,
          data: {} as DriveFile,
          message: error instanceof Error ? error.message : '移动文件失败，请检查网络连接',
        };
      }
    }
  }

  // 下载文件URL
  getDownloadUrl(fileId: string): string {
    return `${API_BASE_URL}/drive/files/${fileId}/download`;
  }

  // 获取带认证头的下载URL配置（用于FastImage）
  getDownloadUrlWithHeaders(fileId: string): {uri: string; headers: Record<string, string>} {
    // 添加文件ID作为查询参数，确保每个文件有唯一的URL
    const timestamp = Date.now();
    return {
      uri: `${API_BASE_URL}/drive/files/${fileId}/download?fileId=${fileId}&t=${timestamp}`,
      headers: API_HEADERS,
    };
  }

  // 获取缩略图URL
  getThumbnailUrl(fileId: string, width?: number, height?: number): string {
    let url = `${API_BASE_URL}/drive/files/${fileId}/thumbnail`;
    const params = [];

    if (width) {
      params.push(`width=${width}`);
    }
    if (height) {
      params.push(`height=${height}`);
    }

    if (params.length > 0) {
      url += `?${params.join('&')}`;
    }

    return url;
  }

  // 获取带认证头的缩略图URL配置（用于FastImage）
  getThumbnailUrlWithHeaders(fileId: string, width?: number, height?: number): {uri: string; headers: Record<string, string>} {
    // 添加文件ID和时间戳确保唯一性
    const baseUrl = this.getThumbnailUrl(fileId, width, height);
    const separator = baseUrl.includes('?') ? '&' : '?';
    const timestamp = Date.now();
    return {
      uri: `${baseUrl}${separator}fileId=${fileId}&t=${timestamp}`,
      headers: API_HEADERS,
    };
  }

  // 生成缩略图
  async generateThumbnail(
    fileId: string,
    width?: number,
    height?: number,
  ): Promise<DriveApiResponse<{thumbnail_path: string}>> {
    try {
      const params: any = {};

      if (width) {
        params.width = width;
      }
      if (height) {
        params.height = height;
      }

      const response = await this.axiosInstance.post(
        `/drive/files/${fileId}/thumbnail`,
        {},
        {params},
      );

      return {
        success: true,
        data: response.data,
        message: response.data.message || 'Thumbnail generated successfully',
      };
    } catch (error) {
      console.error('Error generating thumbnail:', error);
      return {
        success: false,
        data: {thumbnail_path: ''},
        message: error instanceof Error ? error.message : '生成缩略图失败',
      };
    }
  }

  // 判断文件是否为图片
  isImageFile(mimeType?: string): boolean {
    if (!mimeType) return false;
    return mimeType.startsWith('image/');
  }

  // 判断文件是否为视频
  isVideoFile(mimeType?: string): boolean {
    if (!mimeType) return false;
    return mimeType.startsWith('video/');
  }

  // 判断文件是否支持预览
  isPreviewSupported(mimeType?: string): boolean {
    return this.isImageFile(mimeType) || this.isVideoFile(mimeType);
  }

  // 格式化文件大小
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  // 获取文件图标
  getFileIcon(file: DriveFile): string {
    if (file.fileType === 'folder') {
      return '📁';
    }

    const mimeType = file.mimeType || '';
    const extension = file.name.split('.').pop()?.toLowerCase() || '';

    // 根据MIME类型或扩展名返回图标
    if (mimeType.startsWith('image/') || ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg'].includes(extension)) {
      return '🖼️';
    }
    if (mimeType.startsWith('video/') || ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv'].includes(extension)) {
      return '🎥';
    }
    if (mimeType.startsWith('audio/') || ['mp3', 'wav', 'flac', 'aac', 'ogg'].includes(extension)) {
      return '🎵';
    }
    if (mimeType.includes('pdf') || extension === 'pdf') {
      return '📄';
    }
    if (mimeType.includes('word') || ['doc', 'docx'].includes(extension)) {
      return '📝';
    }
    if (mimeType.includes('excel') || ['xls', 'xlsx'].includes(extension)) {
      return '📊';
    }
    if (mimeType.includes('powerpoint') || ['ppt', 'pptx'].includes(extension)) {
      return '📈';
    }
    if (mimeType.startsWith('text/') || ['txt', 'md', 'log'].includes(extension)) {
      return '📃';
    }
    if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {
      return '📦';
    }

    return '📄'; // 默认文件图标
  }
}

export default new DriveService();
