import axios from 'axios';
import {
  FavoriteItem,
  FavoriteAddRequest,
  DiskInfo,
  FsTreeItem,
  FavoritesApiResponse,
  PaginatedResponse,
} from '../types';

// API configuration based on favorites-android
const API_BASE_URL = 'https://local.luzeshu.cn/api';
const API_HEADERS = {
  'Content-Type': 'application/json',
  'bullet': '36fdf066-9e42-11ec-b41e-525400043ced',
};

class FavoritesService {
  private axiosInstance;

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: API_HEADERS,
    });
  }

  // Add a favorite item (migrated from AddToFavorites API)
  async addFavorite(url: string, title: string): Promise<FavoritesApiResponse<boolean>> {
    try {
      const requestData: FavoriteAddRequest = {
        collect_type: '1001',
        origin: url,
        title: title,
        save_path: '/',
        remark: 'n',
      };

      const response = await this.axiosInstance.post('/favorites/add', requestData);
      
      return {
        success: true,
        data: true,
        message: 'Favorite added successfully',
      };
    } catch (error) {
      console.error('Error adding favorite:', error);
      
      // Return mock success for development
      return {
        success: true,
        data: true,
        message: 'Favorite added successfully (mock)',
      };
    }
  }

  // Get favorites list (migrated from GetFavoritesPage API)
  async getFavorites(offset = 0, size = 20, keyword?: string): Promise<PaginatedResponse<FavoriteItem>> {
    try {
      const params: any = {
        offset: offset,
        size: size  // 修复参数名，gateway期望的是size而不是count
      };

      if (keyword && keyword.trim()) {
        params.keyword = keyword.trim();
      }

      const response = await this.axiosInstance.get('/favorites/list', {
        params: params
      });

      console.log('API Response:', JSON.stringify(response.data, null, 2));

      // 支持多种响应格式
      let favoritesData: any = null;
      let favoritesList: any[] = [];

      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        // Gateway返回的格式: { data: [...], has_more: boolean, total: number }
        favoritesList = response.data.data;
        favoritesData = {
          Total: response.data.total,
          HasMore: response.data.has_more,
          total: response.data.total,
          has_more: response.data.has_more
        };
      } else if (response.data && response.data.data) {
        favoritesData = response.data.data;
        // 尝试不同的字段名
        favoritesList = favoritesData.FavoritesList || favoritesData.favorites_list || favoritesData.items || [];
      } else if (response.data && Array.isArray(response.data)) {
        // 直接返回数组的情况
        favoritesList = response.data;
        favoritesData = { Total: favoritesList.length, HasMore: false };
      }

      if (favoritesList && favoritesList.length >= 0) {
        const items: FavoriteItem[] = favoritesList.map((item: any) => {
          return {
            id: String(item.id || item.ID || ''),
            origin: item.origin || item.Origin || '',
            title: item.title || item.Title || '',
            author: item.author || item.Author || '',
            state: item.state || item.State || 'unknown',
            savePath: item.save_path || item.SavePath || '/',
            collectType: item.collect_type || item.CollectType || '1001',
            createTime: item.create_time || item.CreateTime || new Date().toISOString(),
            clickCount: item.click_count || item.ClickCount || 0,
            errorMessage: item.error_message || item.ErrorMessage || null,
            statusCode: item.status_code || item.StatusCode || null,
          };
        });

        return {
          items,
          total: favoritesData?.Total || favoritesData?.total || items.length,
          page: Math.floor(offset / size) + 1,
          pageSize: size,
          hasMore: favoritesData?.HasMore !== undefined ? favoritesData.HasMore :
                   favoritesData?.has_more !== undefined ? favoritesData.has_more :
                   items.length >= size,
        };
      }
      
      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error fetching favorites:', error);
      
      // Return mock data for development
      return this.getMockFavorites(offset, size);
    }
  }

  // Get disk info (migrated from GetDiskInfo API)
  async getDiskInfo(): Promise<DiskInfo> {
    try {
      const response = await this.axiosInstance.get('/favorites/disk/info');
      
      if (response.data) {
        const data = response.data;
        return {
          label: data.disk_label || 'Unknown',
          usage: data.disk_usage || '0GB / 0GB',
          progress: data.disk_progress || 0,
          fsTree: this.parseFsTree(data.fs_tree) || this.getMockFsTree(),
        };
      }
      
      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error fetching disk info:', error);
      
      // Return mock data for development
      return this.getMockDiskInfo();
    }
  }

  // Helper method to parse file system tree
  private parseFsTree(fsTreeStr: string): FsTreeItem[] {
    try {
      if (!fsTreeStr) {
        return this.getMockFsTree();
      }
      
      // Parse the fs_tree string - format may vary
      // For now, return mock data
      return this.getMockFsTree();
    } catch (error) {
      console.error('Error parsing fs tree:', error);
      return this.getMockFsTree();
    }
  }

  // Mock data methods
  private getMockFavorites(offset = 0, size = 20): PaginatedResponse<FavoriteItem> {
    const mockItems: FavoriteItem[] = [
      {
        id: '1',
        origin: 'https://reactnative.dev/',
        title: 'React Native Documentation',
        author: 'Facebook',
        state: 'active',
        savePath: '/',
        collectType: '1001',
        createTime: '2024-01-15T10:00:00Z',
        clickCount: 15,
      },
      {
        id: '2',
        origin: 'https://www.typescriptlang.org/docs/',
        title: 'TypeScript Handbook',
        author: 'Microsoft',
        state: 'active',
        savePath: '/',
        collectType: '1001',
        createTime: '2024-01-14T15:30:00Z',
        clickCount: 8,
      },
      {
        id: '3',
        origin: 'https://m3.material.io/',
        title: 'Material Design 3',
        author: 'Google',
        state: 'active',
        savePath: '/',
        collectType: '1001',
        createTime: '2024-01-13T09:15:00Z',
        clickCount: 23,
      },
      {
        id: '4',
        origin: 'https://github.com/facebook/react-native',
        title: 'React Native GitHub',
        author: 'Facebook',
        state: 'active',
        savePath: '/development',
        collectType: '1001',
        createTime: '2024-01-12T14:20:00Z',
        clickCount: 42,
      },
      {
        id: '5',
        origin: 'https://expo.dev/',
        title: 'Expo Platform',
        author: 'Expo',
        state: 'active',
        savePath: '/tools',
        collectType: '1001',
        createTime: '2024-01-11T11:45:00Z',
        clickCount: 18,
      },
    ];

    const startIndex = offset;
    const endIndex = Math.min(startIndex + size, mockItems.length);
    const items = mockItems.slice(startIndex, endIndex);

    return {
      items,
      total: mockItems.length,
      page: Math.floor(offset / size) + 1,
      pageSize: size,
      hasMore: endIndex < mockItems.length,
    };
  }

  private getMockDiskInfo(): DiskInfo {
    return {
      label: '/storage/emulated/0',
      usage: '83GB / 128GB',
      progress: 65,
      fsTree: this.getMockFsTree(),
    };
  }

  private getMockFsTree(): FsTreeItem[] {
    return [
      {level: 1, name: 'Documents'},
      {level: 1, name: 'Downloads'},
      {level: 2, name: 'favorites'},
      {level: 3, name: 'videos'},
      {level: 2, name: 'images'},
      {level: 1, name: 'Pictures'},
      {level: 2, name: 'Camera'},
      {level: 2, name: 'Screenshots'},
      {level: 1, name: 'Music'},
      {level: 1, name: 'Android'},
      {level: 2, name: 'data'},
      {level: 3, name: 'com.thinking'},
    ];
  }



  // Delete a favorite item
  async deleteFavorite(favoriteId: string): Promise<boolean> {
    try {
      const response = await this.axiosInstance.delete(`/favorites/${favoriteId}`);
      return response.status === 200;
    } catch (error) {
      console.error('Error deleting favorite:', error);
      // Return mock success for development
      return true;
    }
  }

  // Utility methods
  async incrementClickCount(favoriteId: string): Promise<void> {
    try {
      // This would call an API to increment click count
      console.log(`Incrementing click count for favorite: ${favoriteId}`);
    } catch (error) {
      console.error('Error incrementing click count:', error);
    }
  }

  extractUrlFromText(text: string): string | null {
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    const match = text.match(urlRegex);
    return match ? match[0] : null;
  }

  formatCreateTime(createTime: string): string {
    try {
      const date = new Date(createTime);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (error) {
      return createTime;
    }
  }
}

export const favoritesService = new FavoritesService();
export default favoritesService; 