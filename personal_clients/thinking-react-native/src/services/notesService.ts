import axios from 'axios';
import {
  NoteItem,
  NoteAddRequest,
  NotesApiResponse,
  PaginatedResponse,
} from '../types';

// API configuration
const API_BASE_URL = 'https://local.luzeshu.cn/api';
const API_HEADERS = {
  'Content-Type': 'application/json',
  'bullet': '36fdf066-9e42-11ec-b41e-525400043ced',
};

// 开发模式配置 - 设置为false来测试真实API错误处理
const USE_MOCK_ON_ERROR = false;

class NotesService {
  private axiosInstance;

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: API_HEADERS,
    });
  }

  // Add a note
  async addNote(title: string, content: string, category?: string, tags?: string, noteType: string = 'normal'): Promise<NotesApiResponse<{ note_id: number }>> {
    try {
      const requestData: NoteAddRequest = {
        title,
        content,
        note_type: noteType as 'normal' | 'timeline',
        category,
        tags,
      };

      const response = await this.axiosInstance.post('/notes/add', requestData);
      
      return {
        success: true,
        data: response.data,
        message: response.data.message || 'Note added successfully',
      };
    } catch (error) {
      console.error('Error adding note:', error);
      
      if (USE_MOCK_ON_ERROR) {
        // 只在明确开启mock模式时才返回mock成功
        return {
          success: true,
          data: { note_id: Math.floor(Math.random() * 1000) },
          message: 'Note added successfully (mock)',
        };
      } else {
        // 返回真实的API错误状态，但保持数据类型一致
        return {
          success: false,
          data: { note_id: -1 }, // 使用-1表示失败的情况
          message: error instanceof Error ? error.message : '保存笔记失败，请检查网络连接',
        };
      }
    }
  }

  // Get notes list
  async getNotes(offset = 0, size = 20, keyword?: string, category?: string): Promise<PaginatedResponse<NoteItem>> {
    try {
      const params: any = {
        offset: offset,
        size: size
      };

      if (keyword && keyword.trim()) {
        params.keyword = keyword.trim();
      }

      if (category && category.trim()) {
        params.category = category.trim();
      }

      const response = await this.axiosInstance.get('/notes/list', {
        params: params
      });

      console.log('Notes API Response:', JSON.stringify(response.data, null, 2));

      // 支持多种响应格式
      let notesData: any = null;
      let notesList: any[] = [];

      if (response.data && response.data.data && Array.isArray(response.data.data)) {
        // Gateway返回的格式: { data: [...], has_more: boolean, total: number }
        notesList = response.data.data;
        notesData = {
          Total: response.data.total,
          HasMore: response.data.has_more,
          total: response.data.total,
          has_more: response.data.has_more
        };
      } else if (response.data && Array.isArray(response.data)) {
        // 直接返回数组的情况
        notesList = response.data;
        notesData = { Total: notesList.length, HasMore: false };
      }

      if (notesList && notesList.length >= 0) {
        const items: NoteItem[] = notesList.map((item: any) => {
          return {
            id: String(item.id || item.ID || ''),
            title: item.title || item.Title || '',
            content: item.content || item.Content || '',
            category: item.category || item.Category || null,
            tags: item.tags || item.Tags || null,
            note_type: item.note_type || item.NoteType || 'normal',
            createTime: item.create_time || item.CreateTime || new Date().toISOString(),
            updateTime: item.update_time || item.UpdateTime || new Date().toISOString(),
          };
        });

        return {
          items,
          total: notesData?.Total || notesData?.total || items.length,
          page: Math.floor(offset / size) + 1,
          pageSize: size,
          hasMore: notesData?.HasMore !== undefined ? notesData.HasMore :
                   notesData?.has_more !== undefined ? notesData.has_more :
                   items.length >= size,
        };
      }
      
      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error fetching notes:', error);
      
      // Return mock data for development
      return this.getMockNotes(offset, size);
    }
  }

  // Get a single note
  async getNote(noteId: string): Promise<NotesApiResponse<NoteItem>> {
    try {
      const response = await this.axiosInstance.get(`/notes/${noteId}`);
      
      if (response.data && response.data.data) {
        const item = response.data.data;
        const note: NoteItem = {
          id: String(item.id || item.ID || ''),
          title: item.title || item.Title || '',
          content: item.content || item.Content || '',
          category: item.category || item.Category || null,
          tags: item.tags || item.Tags || null,
          note_type: item.note_type || item.NoteType || 'normal',
          createTime: item.create_time || item.CreateTime || new Date().toISOString(),
          updateTime: item.update_time || item.UpdateTime || new Date().toISOString(),
        };

        return {
          success: true,
          data: note,
          message: 'Note retrieved successfully',
        };
      }
      
      throw new Error('Invalid response format');
    } catch (error) {
      console.error('Error fetching note:', error);
      
      // Return mock data for development
      return {
        success: true,
        data: this.getMockNote(noteId),
        message: 'Note retrieved successfully (mock)',
      };
    }
  }

  // Update a note
  async updateNote(noteId: string, title?: string, content?: string, category?: string, tags?: string, noteType?: string): Promise<NotesApiResponse<boolean>> {
    try {
      const requestData: any = {};
      
      if (title !== undefined) requestData.title = title;
      if (content !== undefined) requestData.content = content;
      if (category !== undefined) requestData.category = category;
      if (tags !== undefined) requestData.tags = tags;
      if (noteType !== undefined) requestData.note_type = noteType;

      const response = await this.axiosInstance.put(`/notes/${noteId}`, requestData);
      
      return {
        success: true,
        data: true,
        message: response.data.message || 'Note updated successfully',
      };
    } catch (error) {
      console.error('Error updating note:', error);
      
      if (USE_MOCK_ON_ERROR) {
        // 只在明确开启mock模式时才返回mock成功
        return {
          success: true,
          data: true,
          message: 'Note updated successfully (mock)',
        };
      } else {
        // 返回真实的API错误状态
        return {
          success: false,
          data: false,
          message: error instanceof Error ? error.message : '更新笔记失败，请检查网络连接',
        };
      }
    }
  }

  // Delete a note
  async deleteNote(noteId: string): Promise<NotesApiResponse<boolean>> {
    try {
      const response = await this.axiosInstance.delete(`/notes/${noteId}`);
      
      return {
        success: true,
        data: true,
        message: response.data.message || 'Note deleted successfully',
      };
    } catch (error) {
      console.error('Error deleting note:', error);
      
      if (USE_MOCK_ON_ERROR) {
        // 只在明确开启mock模式时才返回mock成功
        return {
          success: true,
          data: true,
          message: 'Note deleted successfully (mock)',
        };
      } else {
        // 返回真实的API错误状态
        return {
          success: false,
          data: false,
          message: error instanceof Error ? error.message : '删除笔记失败，请检查网络连接',
        };
      }
    }
  }

  // Mock data methods
  private getMockNotes(offset = 0, size = 20): PaginatedResponse<NoteItem> {
    const mockItems: NoteItem[] = [
      {
        id: '1',
        title: 'React Native 学习笔记',
        content: '今天学习了React Native的基础知识，包括组件、状态管理等...',
        category: '学习',
        tags: 'React Native,移动开发',
        note_type: 'normal',
        createTime: '2024-01-15T10:00:00Z',
        updateTime: '2024-01-15T10:00:00Z',
      },
      {
        id: '2',
        title: '项目架构设计',
        content: '设计了新项目的整体架构，采用微服务模式...',
        category: '工作',
        tags: '架构,微服务',
        note_type: 'normal',
        createTime: '2024-01-14T15:30:00Z',
        updateTime: '2024-01-14T15:30:00Z',
      },
      {
        id: '3',
        title: '读书笔记：《代码整洁之道》',
        content: '这本书强调了编写可读、可维护代码的重要性...',
        category: '读书',
        tags: '编程,代码质量',
        note_type: 'normal',
        createTime: '2024-01-13T09:15:00Z',
        updateTime: '2024-01-13T09:15:00Z',
      },
    ];

    const startIndex = offset;
    const endIndex = Math.min(startIndex + size, mockItems.length);
    const items = mockItems.slice(startIndex, endIndex);

    return {
      items,
      total: mockItems.length,
      page: Math.floor(offset / size) + 1,
      pageSize: size,
      hasMore: endIndex < mockItems.length,
    };
  }

  private getMockNote(noteId: string): NoteItem {
    return {
      id: noteId,
      title: 'Mock Note Title',
      content: 'This is a mock note content for development purposes.',
      category: 'Mock',
      tags: 'mock,development',
      note_type: 'normal',
      createTime: new Date().toISOString(),
      updateTime: new Date().toISOString(),
    };
  }

  // Utility methods
  formatCreateTime(createTime: string): string {
    try {
      const date = new Date(createTime);
      return date.toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
      });
    } catch (error) {
      return createTime;
    }
  }
}

export const notesService = new NotesService();
export default notesService;
