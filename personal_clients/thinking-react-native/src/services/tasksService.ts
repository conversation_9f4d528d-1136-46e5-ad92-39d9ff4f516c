import axios from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import {
  Task,
  TaskStatus,
  TaskPriority,
  TaskMark,
  TaskMarkType,
  TaskFilter,
  TaskStats,
  ApiResponse,
  PaginatedResponse,
} from '@/types';

// Base URL for the tasks API (adjust based on your backend)
const BASE_URL = 'http://localhost:3000/api/tasks';

class TasksService {
  private axiosInstance;

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Add request interceptor for authentication if needed
    this.axiosInstance.interceptors.request.use(async config => {
      const token = await AsyncStorage.getItem('auth_token');
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    });
  }

  // Task CRUD Operations
  async getTasks(
    page = 1,
    pageSize = 50,
    filter?: TaskFilter,
  ): Promise<PaginatedResponse<Task>> {
    try {
      const response = await this.axiosInstance.get('/list', {
        params: {
          page,
          pageSize,
          ...filter,
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching tasks:', error);
      // Return mock data for development
      return this.getMockTasks();
    }
  }

  async getTaskById(id: string): Promise<Task> {
    try {
      const response = await this.axiosInstance.get(`/${id}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching task:', error);
      throw new Error('Failed to fetch task');
    }
  }

  async createTask(task: Omit<Task, 'id' | 'createdAt' | 'updatedAt' | 'marks' | 'level' | 'children'>): Promise<ApiResponse<Task>> {
    try {
      const response = await this.axiosInstance.post('/create', task);
      return response.data;
    } catch (error) {
      console.error('Error creating task:', error);
      // Return mock success for development
      return {
        success: true,
        data: {
          id: Date.now().toString(),
          ...task,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          marks: [],
          level: 0,
          children: [],
        },
        timestamp: new Date().toISOString(),
      };
    }
  }

  async updateTask(id: string, updates: Partial<Task>): Promise<ApiResponse<Task>> {
    try {
      const response = await this.axiosInstance.put(`/${id}`, updates);
      return response.data;
    } catch (error) {
      console.error('Error updating task:', error);
      throw new Error('Failed to update task');
    }
  }

  async deleteTask(id: string): Promise<ApiResponse<boolean>> {
    try {
      const response = await this.axiosInstance.delete(`/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting task:', error);
      throw new Error('Failed to delete task');
    }
  }

  // Task Status Operations (matching Rust CLI commands)
  async startTask(id: string): Promise<ApiResponse<Task>> {
    try {
      const response = await this.axiosInstance.post(`/${id}/start`);
      return response.data;
    } catch (error) {
      console.error('Error starting task:', error);
      throw new Error('Failed to start task');
    }
  }

  async pauseTask(id: string): Promise<ApiResponse<Task>> {
    try {
      const response = await this.axiosInstance.post(`/${id}/pause`);
      return response.data;
    } catch (error) {
      console.error('Error pausing task:', error);
      throw new Error('Failed to pause task');
    }
  }

  async finishTask(id: string): Promise<ApiResponse<Task>> {
    try {
      const response = await this.axiosInstance.post(`/${id}/finish`);
      return response.data;
    } catch (error) {
      console.error('Error finishing task:', error);
      throw new Error('Failed to finish task');
    }
  }

  async cancelTask(id: string): Promise<ApiResponse<Task>> {
    try {
      const response = await this.axiosInstance.post(`/${id}/cancel`);
      return response.data;
    } catch (error) {
      console.error('Error cancelling task:', error);
      throw new Error('Failed to cancel task');
    }
  }

  // Task Marks Operations
  async addMark(taskId: string, mark: Omit<TaskMark, 'id' | 'taskId' | 'createdAt'>): Promise<ApiResponse<TaskMark>> {
    try {
      const response = await this.axiosInstance.post(`/${taskId}/marks`, mark);
      return response.data;
    } catch (error) {
      console.error('Error adding mark:', error);
      throw new Error('Failed to add mark');
    }
  }

  async updateMark(taskId: string, markId: string, updates: Partial<TaskMark>): Promise<ApiResponse<TaskMark>> {
    try {
      const response = await this.axiosInstance.put(`/${taskId}/marks/${markId}`, updates);
      return response.data;
    } catch (error) {
      console.error('Error updating mark:', error);
      throw new Error('Failed to update mark');
    }
  }

  async deleteMark(taskId: string, markId: string): Promise<ApiResponse<boolean>> {
    try {
      const response = await this.axiosInstance.delete(`/${taskId}/marks/${markId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting mark:', error);
      throw new Error('Failed to delete mark');
    }
  }

  async getMarks(taskId: string): Promise<TaskMark[]> {
    try {
      const response = await this.axiosInstance.get(`/${taskId}/marks`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching marks:', error);
      return [];
    }
  }

  // Task Statistics and Analytics
  async getTaskStats(filter?: TaskFilter): Promise<TaskStats> {
    try {
      const response = await this.axiosInstance.get('/stats', {
        params: filter,
      });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching task stats:', error);
      return this.getMockStats();
    }
  }

  async getTasksByStatus(status: TaskStatus): Promise<Task[]> {
    try {
      const response = await this.axiosInstance.get('/by-status', {
        params: {status},
      });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching tasks by status:', error);
      return [];
    }
  }

  async getTasksByPriority(priority: TaskPriority): Promise<Task[]> {
    try {
      const response = await this.axiosInstance.get('/by-priority', {
        params: {priority},
      });
      return response.data.data;
    } catch (error) {
      console.error('Error fetching tasks by priority:', error);
      return [];
    }
  }

  // Task Tree Operations
  async getTaskTree(): Promise<Task[]> {
    try {
      const response = await this.axiosInstance.get('/tree');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching task tree:', error);
      return this.getMockTaskTree();
    }
  }

  async getSubtasks(parentId: string): Promise<Task[]> {
    try {
      const response = await this.axiosInstance.get(`/${parentId}/subtasks`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching subtasks:', error);
      return [];
    }
  }

  // Search and Filter
  async searchTasks(query: string): Promise<Task[]> {
    try {
      const response = await this.axiosInstance.get('/search', {
        params: {q: query},
      });
      return response.data.data;
    } catch (error) {
      console.error('Error searching tasks:', error);
      return [];
    }
  }

  async getTags(): Promise<string[]> {
    try {
      const response = await this.axiosInstance.get('/tags');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching tags:', error);
      return ['work', 'personal', 'urgent', 'learning', 'project', 'bug-fix'];
    }
  }

  // Mock data for development
  private getMockTasks(): PaginatedResponse<Task> {
    const mockTasks: Task[] = [
      {
        id: '1',
        title: 'Implement React Native App',
        description: 'Create the thinking-android React Native application with favorites and tasks functionality',
        status: 'in_progress',
        priority: 'high',
        tags: ['react-native', 'mobile-app', 'development'],
        createdAt: '2024-01-15T09:00:00Z',
        updatedAt: '2024-01-15T11:30:00Z',
        startedAt: '2024-01-15T09:30:00Z',
        dueDate: '2024-01-20T17:00:00Z',
        marks: [],
        level: 0,
        children: [],
        duration: 150,
        estimatedDuration: 480,
      },
      {
        id: '2',
        title: 'Set up project structure',
        description: 'Create the basic project structure with TypeScript configuration',
        status: 'completed',
        priority: 'medium',
        tags: ['setup', 'typescript', 'configuration'],
        createdAt: '2024-01-15T09:00:00Z',
        updatedAt: '2024-01-15T10:00:00Z',
        startedAt: '2024-01-15T09:00:00Z',
        finishedAt: '2024-01-15T10:00:00Z',
        parentId: '1',
        marks: [],
        level: 1,
        children: [],
        duration: 60,
        estimatedDuration: 60,
      },
      {
        id: '3',
        title: 'Implement navigation',
        description: 'Set up React Navigation with bottom tabs and stack navigation',
        status: 'completed',
        priority: 'medium',
        tags: ['navigation', 'react-navigation'],
        createdAt: '2024-01-15T10:00:00Z',
        updatedAt: '2024-01-15T11:00:00Z',
        startedAt: '2024-01-15T10:15:00Z',
        finishedAt: '2024-01-15T11:00:00Z',
        parentId: '1',
        marks: [],
        level: 1,
        children: [],
        duration: 45,
        estimatedDuration: 60,
      },
      {
        id: '4',
        title: 'Create services layer',
        description: 'Implement API services for favorites and tasks',
        status: 'in_progress',
        priority: 'high',
        tags: ['api', 'services', 'http'],
        createdAt: '2024-01-15T11:00:00Z',
        updatedAt: '2024-01-15T11:30:00Z',
        startedAt: '2024-01-15T11:15:00Z',
        parentId: '1',
        marks: [],
        level: 1,
        children: [],
        duration: 45,
        estimatedDuration: 120,
      },
      {
        id: '5',
        title: 'Review code quality',
        description: 'Perform code review and ensure best practices',
        status: 'todo',
        priority: 'low',
        tags: ['code-review', 'quality'],
        createdAt: '2024-01-15T12:00:00Z',
        updatedAt: '2024-01-15T12:00:00Z',
        dueDate: '2024-01-18T17:00:00Z',
        marks: [],
        level: 0,
        children: [],
        estimatedDuration: 90,
      },
    ];

    return {
      items: mockTasks,
      total: mockTasks.length,
      page: 1,
      pageSize: 50,
      hasMore: false,
    };
  }

  private getMockTaskTree(): Task[] {
    return this.getMockTasks().items;
  }

  private getMockStats(): TaskStats {
    return {
      total: 5,
      byStatus: {
        todo: 1,
        in_progress: 2,
        completed: 2,
        cancelled: 0,
        paused: 0,
      },
      byPriority: {
        low: 1,
        medium: 2,
        high: 2,
        urgent: 0,
      },
      totalDuration: 300,
      averageDuration: 75,
    };
  }
}

export const tasksService = new TasksService();
export default tasksService; 