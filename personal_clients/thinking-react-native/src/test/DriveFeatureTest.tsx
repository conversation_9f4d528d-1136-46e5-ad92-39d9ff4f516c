import React, {useState, useEffect} from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import FastImage from 'react-native-fast-image';
import driveService from '../services/driveService';
import {DriveFile} from '../types';
import ImageViewer from '../components/ImageViewer';
import VideoPlayer from '../components/VideoPlayer';
import {COLORS, SPACING} from '../utils/constants';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message?: string;
  duration?: number;
}

const DriveFeatureTest: React.FC = () => {
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const [files, setFiles] = useState<DriveFile[]>([]);
  const [showImageViewer, setShowImageViewer] = useState(false);
  const [showVideoPlayer, setShowVideoPlayer] = useState(false);
  const [selectedFile, setSelectedFile] = useState<DriveFile | null>(null);
  const [previewImages, setPreviewImages] = useState<any[]>([]);

  // 初始化测试用例
  const initTests = (): TestResult[] => [
    { name: '服务连接测试', status: 'pending' },
    { name: '文件列表获取', status: 'pending' },
    { name: '缩略图URL生成', status: 'pending' },
    { name: '图片文件识别', status: 'pending' },
    { name: '视频文件识别', status: 'pending' },
    { name: '缩略图显示测试', status: 'pending' },
    { name: '图片预览组件', status: 'pending' },
    { name: '视频播放组件', status: 'pending' },
  ];

  // 更新测试结果
  const updateTestResult = (index: number, status: TestResult['status'], message?: string, duration?: number) => {
    setTestResults(prev => prev.map((test, i) => 
      i === index ? { ...test, status, message, duration } : test
    ));
  };

  // 运行单个测试
  const runTest = async (testFn: () => Promise<void>, index: number): Promise<void> => {
    const startTime = Date.now();
    try {
      await testFn();
      const duration = Date.now() - startTime;
      updateTestResult(index, 'success', '通过', duration);
    } catch (error) {
      const duration = Date.now() - startTime;
      updateTestResult(index, 'error', error instanceof Error ? error.message : '未知错误', duration);
      throw error;
    }
  };

  // 测试1: 服务连接
  const testServiceConnection = async () => {
    const response = await driveService.getFileList();
    if (!response.success) {
      throw new Error('服务连接失败');
    }
  };

  // 测试2: 文件列表获取
  const testFileListFetch = async () => {
    const response = await driveService.getFileList();
    if (!response.success || !response.data.files) {
      throw new Error('文件列表获取失败');
    }
    setFiles(response.data.files);
    if (response.data.files.length === 0) {
      throw new Error('文件列表为空');
    }
  };

  // 测试3: 缩略图URL生成
  const testThumbnailUrlGeneration = async () => {
    if (files.length === 0) {
      throw new Error('没有文件可测试');
    }
    
    const testFile = files[0];
    const thumbnailUrl = driveService.getThumbnailUrl(testFile.id, 200, 200);
    
    if (!thumbnailUrl || !thumbnailUrl.includes('/thumbnail')) {
      throw new Error('缩略图URL生成失败');
    }
  };

  // 测试4: 图片文件识别
  const testImageFileDetection = async () => {
    const testCases = [
      { mimeType: 'image/jpeg', expected: true },
      { mimeType: 'image/png', expected: true },
      { mimeType: 'text/plain', expected: false },
      { mimeType: 'video/mp4', expected: false },
    ];

    for (const testCase of testCases) {
      const result = driveService.isImageFile(testCase.mimeType);
      if (result !== testCase.expected) {
        throw new Error(`图片识别错误: ${testCase.mimeType}`);
      }
    }
  };

  // 测试5: 视频文件识别
  const testVideoFileDetection = async () => {
    const testCases = [
      { mimeType: 'video/mp4', expected: true },
      { mimeType: 'video/avi', expected: true },
      { mimeType: 'image/jpeg', expected: false },
      { mimeType: 'text/plain', expected: false },
    ];

    for (const testCase of testCases) {
      const result = driveService.isVideoFile(testCase.mimeType);
      if (result !== testCase.expected) {
        throw new Error(`视频识别错误: ${testCase.mimeType}`);
      }
    }
  };

  // 测试6: 缩略图显示
  const testThumbnailDisplay = async () => {
    const imageFiles = files.filter(f => driveService.isImageFile(f.mimeType));
    if (imageFiles.length === 0) {
      throw new Error('没有图片文件可测试缩略图显示');
    }
    
    // 这里我们只是验证缩略图URL是否正确生成
    const testFile = imageFiles[0];
    const thumbnailUrl = driveService.getThumbnailUrl(testFile.id);
    
    if (!thumbnailUrl) {
      throw new Error('缩略图URL生成失败');
    }
  };

  // 测试7: 图片预览组件
  const testImagePreviewComponent = async () => {
    const imageFiles = files.filter(f => driveService.isImageFile(f.mimeType));
    if (imageFiles.length === 0) {
      throw new Error('没有图片文件可测试预览组件');
    }

    // 准备预览数据
    const images = imageFiles.slice(0, 3).map(f => ({
      id: parseInt(f.id),
      name: f.name,
      uri: driveService.getDownloadUrl(f.id),
      thumbnailUri: driveService.getThumbnailUrl(f.id, 200, 200),
      downloadConfig: driveService.getDownloadUrlWithHeaders(f.id),
      thumbnailConfig: driveService.getThumbnailUrlWithHeaders(f.id, 200, 200),
    }));

    setPreviewImages(images);
    
    // 模拟显示预览
    setTimeout(() => {
      setShowImageViewer(true);
      setTimeout(() => setShowImageViewer(false), 1000);
    }, 500);
  };

  // 测试8: 视频播放组件
  const testVideoPlayerComponent = async () => {
    const videoFiles = files.filter(f => driveService.isVideoFile(f.mimeType));
    if (videoFiles.length === 0) {
      throw new Error('没有视频文件可测试播放组件');
    }

    setSelectedFile(videoFiles[0]);
    
    // 模拟显示视频播放器
    setTimeout(() => {
      setShowVideoPlayer(true);
      setTimeout(() => {
        setShowVideoPlayer(false);
        setSelectedFile(null);
      }, 1000);
    }, 500);
  };

  // 运行所有测试
  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults(initTests());

    const tests = [
      testServiceConnection,
      testFileListFetch,
      testThumbnailUrlGeneration,
      testImageFileDetection,
      testVideoFileDetection,
      testThumbnailDisplay,
      testImagePreviewComponent,
      testVideoPlayerComponent,
    ];

    for (let i = 0; i < tests.length; i++) {
      try {
        await runTest(tests[i], i);
        // 添加延迟以便观察测试过程
        await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
        console.error(`测试 ${i + 1} 失败:`, error);
        // 继续执行其他测试
      }
    }

    setIsRunning(false);
  };

  // 渲染测试结果
  const renderTestResult = (test: TestResult, index: number) => {
    const getStatusIcon = () => {
      switch (test.status) {
        case 'success': return '✅';
        case 'error': return '❌';
        default: return '⏳';
      }
    };

    const getStatusColor = () => {
      switch (test.status) {
        case 'success': return COLORS.SUCCESS;
        case 'error': return COLORS.ERROR;
        default: return COLORS.TEXT_SECONDARY;
      }
    };

    return (
      <View key={index} style={styles.testItem}>
        <View style={styles.testHeader}>
          <Text style={styles.testIcon}>{getStatusIcon()}</Text>
          <Text style={styles.testName}>{test.name}</Text>
          {test.duration && (
            <Text style={styles.testDuration}>{test.duration}ms</Text>
          )}
        </View>
        {test.message && (
          <Text style={[styles.testMessage, { color: getStatusColor() }]}>
            {test.message}
          </Text>
        )}
      </View>
    );
  };

  // 渲染文件列表示例
  const renderFileExample = () => {
    if (files.length === 0) return null;

    const imageFiles = files.filter(f => driveService.isImageFile(f.mimeType)).slice(0, 3);
    const videoFiles = files.filter(f => driveService.isVideoFile(f.mimeType)).slice(0, 3);

    return (
      <View style={styles.exampleSection}>
        <Text style={styles.sectionTitle}>文件示例</Text>
        
        {imageFiles.length > 0 && (
          <View style={styles.fileTypeSection}>
            <Text style={styles.fileTypeTitle}>图片文件 ({imageFiles.length})</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {imageFiles.map(file => (
                <TouchableOpacity
                  key={file.id}
                  style={styles.fileItem}
                  onPress={() => {
                    const images = imageFiles.map(f => ({
                      id: parseInt(f.id),
                      name: f.name,
                      uri: driveService.getDownloadUrl(f.id),
                      thumbnailUri: driveService.getThumbnailUrl(f.id, 200, 200),
                      downloadConfig: driveService.getDownloadUrlWithHeaders(f.id),
                      thumbnailConfig: driveService.getThumbnailUrlWithHeaders(f.id, 200, 200),
                    }));
                    setPreviewImages(images);
                    setShowImageViewer(true);
                  }}>
                  <FastImage
                    source={{
                      ...driveService.getThumbnailUrlWithHeaders(file.id, 80, 80),
                      priority: FastImage.priority.normal,
                    }}
                    style={styles.thumbnail}
                    resizeMode={FastImage.resizeMode.cover}
                  />
                  <Text style={styles.fileName} numberOfLines={1}>
                    {file.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}

        {videoFiles.length > 0 && (
          <View style={styles.fileTypeSection}>
            <Text style={styles.fileTypeTitle}>视频文件 ({videoFiles.length})</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              {videoFiles.map(file => (
                <TouchableOpacity
                  key={file.id}
                  style={styles.fileItem}
                  onPress={() => {
                    setSelectedFile(file);
                    setShowVideoPlayer(true);
                  }}>
                  <View style={styles.videoThumbnailContainer}>
                    <FastImage
                      source={{
                        uri: driveService.getThumbnailUrl(file.id, 80, 80),
                        priority: FastImage.priority.normal,
                      }}
                      style={styles.thumbnail}
                      resizeMode={FastImage.resizeMode.cover}
                    />
                    <View style={styles.playOverlay}>
                      <Text style={styles.playIcon}>▶</Text>
                    </View>
                  </View>
                  <Text style={styles.fileName} numberOfLines={1}>
                    {file.name}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        )}
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <Text style={styles.title}>云盘功能测试</Text>
        
        <TouchableOpacity
          style={[styles.runButton, isRunning && styles.runButtonDisabled]}
          onPress={runAllTests}
          disabled={isRunning}>
          {isRunning ? (
            <ActivityIndicator color={COLORS.SURFACE} />
          ) : (
            <Text style={styles.runButtonText}>运行测试</Text>
          )}
        </TouchableOpacity>

        <View style={styles.resultsSection}>
          <Text style={styles.sectionTitle}>测试结果</Text>
          {testResults.map(renderTestResult)}
        </View>

        {renderFileExample()}
      </ScrollView>

      {/* Image Viewer */}
      <ImageViewer
        images={previewImages}
        visible={showImageViewer}
        initialIndex={0}
        onClose={() => setShowImageViewer(false)}
      />

      {/* Video Player */}
      {selectedFile && showVideoPlayer && (
        <VideoPlayer
          video={{
            id: parseInt(selectedFile.id),
            name: selectedFile.name,
            uri: driveService.getDownloadUrl(selectedFile.id),
            thumbnailUri: selectedFile.thumbnailPath ? driveService.getThumbnailUrl(selectedFile.id, 200, 200) : undefined,
          }}
          visible={showVideoPlayer}
          onClose={() => {
            setShowVideoPlayer(false);
            setSelectedFile(null);
          }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.BACKGROUND,
  },
  scrollView: {
    flex: 1,
    padding: SPACING.MD,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
    marginBottom: SPACING.LG,
  },
  runButton: {
    backgroundColor: COLORS.PRIMARY,
    paddingVertical: SPACING.MD,
    paddingHorizontal: SPACING.LG,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: SPACING.LG,
  },
  runButtonDisabled: {
    backgroundColor: COLORS.TEXT_SECONDARY,
  },
  runButtonText: {
    color: COLORS.SURFACE,
    fontSize: 16,
    fontWeight: 'bold',
  },
  resultsSection: {
    marginBottom: SPACING.LG,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.MD,
  },
  testItem: {
    backgroundColor: COLORS.SURFACE,
    padding: SPACING.MD,
    borderRadius: 8,
    marginBottom: SPACING.SM,
  },
  testHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  testIcon: {
    fontSize: 16,
    marginRight: SPACING.SM,
  },
  testName: {
    flex: 1,
    fontSize: 16,
    color: COLORS.TEXT_PRIMARY,
  },
  testDuration: {
    fontSize: 12,
    color: COLORS.TEXT_SECONDARY,
  },
  testMessage: {
    fontSize: 14,
    marginTop: SPACING.XS,
    marginLeft: 24,
  },
  exampleSection: {
    marginBottom: SPACING.LG,
  },
  fileTypeSection: {
    marginBottom: SPACING.MD,
  },
  fileTypeTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.TEXT_PRIMARY,
    marginBottom: SPACING.SM,
  },
  fileItem: {
    width: 100,
    marginRight: SPACING.SM,
    alignItems: 'center',
  },
  thumbnail: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: COLORS.BACKGROUND_SECONDARY,
  },
  videoThumbnailContainer: {
    position: 'relative',
  },
  playOverlay: {
    position: 'absolute',
    top: 30,
    left: 30,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  playIcon: {
    color: 'white',
    fontSize: 10,
    marginLeft: 1,
  },
  fileName: {
    fontSize: 12,
    color: COLORS.TEXT_PRIMARY,
    textAlign: 'center',
    marginTop: SPACING.XS,
  },
});

export default DriveFeatureTest;
