/**
 * 测试 react-native-document-picker 功能
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import DocumentPicker from 'react-native-document-picker';

const DocumentPickerTest = () => {
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [testResults, setTestResults] = useState([]);

  const addTestResult = (test, success, message) => {
    setTestResults(prev => [...prev, {
      test,
      success,
      message,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const testSingleFilePicker = async () => {
    try {
      const result = await DocumentPicker.pickSingle({
        type: [DocumentPicker.types.allFiles],
      });
      
      setSelectedFiles([result]);
      addTestResult('单文件选择', true, `选择了文件: ${result.name}`);
      
      console.log('Selected file:', result);
    } catch (error) {
      if (DocumentPicker.isCancel(error)) {
        addTestResult('单文件选择', true, '用户取消选择');
      } else {
        addTestResult('单文件选择', false, `错误: ${error.message}`);
        console.error('Error picking file:', error);
      }
    }
  };

  const testMultipleFilePicker = async () => {
    try {
      const results = await DocumentPicker.pick({
        type: [DocumentPicker.types.allFiles],
        allowMultiSelection: true,
      });
      
      setSelectedFiles(results);
      addTestResult('多文件选择', true, `选择了 ${results.length} 个文件`);
      
      console.log('Selected files:', results);
    } catch (error) {
      if (DocumentPicker.isCancel(error)) {
        addTestResult('多文件选择', true, '用户取消选择');
      } else {
        addTestResult('多文件选择', false, `错误: ${error.message}`);
        console.error('Error picking files:', error);
      }
    }
  };

  const testImagePicker = async () => {
    try {
      const result = await DocumentPicker.pickSingle({
        type: [DocumentPicker.types.images],
      });
      
      setSelectedFiles([result]);
      addTestResult('图片选择', true, `选择了图片: ${result.name}`);
      
      console.log('Selected image:', result);
    } catch (error) {
      if (DocumentPicker.isCancel(error)) {
        addTestResult('图片选择', true, '用户取消选择');
      } else {
        addTestResult('图片选择', false, `错误: ${error.message}`);
        console.error('Error picking image:', error);
      }
    }
  };

  const clearResults = () => {
    setTestResults([]);
    setSelectedFiles([]);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <Text style={styles.title}>Document Picker 测试</Text>
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.button} onPress={testSingleFilePicker}>
            <Text style={styles.buttonText}>测试单文件选择</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.button} onPress={testMultipleFilePicker}>
            <Text style={styles.buttonText}>测试多文件选择</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.button} onPress={testImagePicker}>
            <Text style={styles.buttonText}>测试图片选择</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.button, styles.clearButton]} onPress={clearResults}>
            <Text style={styles.buttonText}>清除结果</Text>
          </TouchableOpacity>
        </View>

        {selectedFiles.length > 0 && (
          <View style={styles.filesContainer}>
            <Text style={styles.sectionTitle}>选择的文件:</Text>
            {selectedFiles.map((file, index) => (
              <View key={index} style={styles.fileItem}>
                <Text style={styles.fileName}>📄 {file.name}</Text>
                <Text style={styles.fileInfo}>类型: {file.type}</Text>
                <Text style={styles.fileInfo}>大小: {(file.size / 1024).toFixed(2)} KB</Text>
                <Text style={styles.fileInfo}>URI: {file.uri}</Text>
              </View>
            ))}
          </View>
        )}

        {testResults.length > 0 && (
          <View style={styles.resultsContainer}>
            <Text style={styles.sectionTitle}>测试结果:</Text>
            {testResults.map((result, index) => (
              <View key={index} style={[
                styles.resultItem,
                result.success ? styles.successResult : styles.errorResult
              ]}>
                <Text style={styles.resultTest}>{result.test}</Text>
                <Text style={styles.resultMessage}>{result.message}</Text>
                <Text style={styles.resultTime}>{result.timestamp}</Text>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  clearButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  filesContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  fileItem: {
    backgroundColor: '#f8f9fa',
    padding: 10,
    borderRadius: 6,
    marginBottom: 10,
  },
  fileName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 5,
    color: '#333',
  },
  fileInfo: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  resultsContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
  },
  resultItem: {
    padding: 10,
    borderRadius: 6,
    marginBottom: 10,
  },
  successResult: {
    backgroundColor: '#d4edda',
    borderColor: '#c3e6cb',
    borderWidth: 1,
  },
  errorResult: {
    backgroundColor: '#f8d7da',
    borderColor: '#f5c6cb',
    borderWidth: 1,
  },
  resultTest: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 5,
  },
  resultMessage: {
    fontSize: 14,
    marginBottom: 5,
  },
  resultTime: {
    fontSize: 12,
    color: '#666',
  },
});

export default DocumentPickerTest;
