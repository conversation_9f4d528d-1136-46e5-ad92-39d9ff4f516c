/**
 * 云盘功能测试
 * 验证云盘子应用的基本功能是否正常工作
 */

import driveService from '../services/driveService';

// 测试云盘服务
const testDriveService = async () => {
  console.log('=== 开始测试云盘服务 ===');
  
  try {
    // 测试获取文件列表
    console.log('测试获取文件列表...');
    const fileListResponse = await driveService.getFileList();
    console.log('文件列表响应:', {
      itemsCount: fileListResponse.items.length,
      total: fileListResponse.total,
      hasMore: fileListResponse.hasMore,
    });
    
    // 测试获取存储信息
    console.log('测试获取存储信息...');
    const storageResponse = await driveService.getStorageInfo();
    console.log('存储信息响应:', {
      success: storageResponse.success,
      totalSpace: storageResponse.data.totalSpace,
      usedSpace: storageResponse.data.usedSpace,
      usagePercent: storageResponse.data.usagePercent,
    });
    
    // 测试文件大小格式化
    console.log('测试文件大小格式化...');
    const testSizes = [0, 1024, 1024 * 1024, 1024 * 1024 * 1024];
    testSizes.forEach(size => {
      console.log(`${size} bytes = ${driveService.formatFileSize(size)}`);
    });
    
    // 测试文件图标获取
    console.log('测试文件图标获取...');
    const testFiles = [
      { fileType: 'folder', name: 'test-folder', mimeType: null },
      { fileType: 'file', name: 'test.jpg', mimeType: 'image/jpeg' },
      { fileType: 'file', name: 'test.pdf', mimeType: 'application/pdf' },
      { fileType: 'file', name: 'test.txt', mimeType: 'text/plain' },
    ];
    
    testFiles.forEach(file => {
      const icon = driveService.getFileIcon(file);
      console.log(`${file.name} (${file.fileType}) = ${icon}`);
    });
    
    console.log('=== 云盘服务测试完成 ===');
    return true;
    
  } catch (error) {
    console.error('云盘服务测试失败:', error);
    return false;
  }
};

// 测试云盘组件导入
const testDriveComponents = () => {
  console.log('=== 开始测试云盘组件导入 ===');
  
  try {
    // 这些导入应该不会抛出错误
    const DriveScreen = require('../screens/drive/DriveScreen').default;
    const DriveMainScreen = require('../screens/drive/DriveMainScreen').default;
    const DriveListScreen = require('../screens/drive/DriveListScreen').default;
    const DriveUploadScreen = require('../screens/drive/DriveUploadScreen').default;
    
    console.log('所有云盘组件导入成功');
    console.log('组件类型检查:', {
      DriveScreen: typeof DriveScreen,
      DriveMainScreen: typeof DriveMainScreen,
      DriveListScreen: typeof DriveListScreen,
      DriveUploadScreen: typeof DriveUploadScreen,
    });
    
    console.log('=== 云盘组件导入测试完成 ===');
    return true;
    
  } catch (error) {
    console.error('云盘组件导入测试失败:', error);
    return false;
  }
};

// 运行所有测试
const runAllTests = async () => {
  console.log('🚀 开始运行云盘功能测试...\n');
  
  const results = {
    serviceTest: false,
    componentTest: false,
  };
  
  // 测试组件导入
  results.componentTest = testDriveComponents();
  console.log('');
  
  // 测试服务功能
  results.serviceTest = await testDriveService();
  console.log('');
  
  // 输出测试结果
  console.log('📊 测试结果汇总:');
  console.log(`- 组件导入测试: ${results.componentTest ? '✅ 通过' : '❌ 失败'}`);
  console.log(`- 服务功能测试: ${results.serviceTest ? '✅ 通过' : '❌ 失败'}`);
  
  const allPassed = Object.values(results).every(result => result === true);
  console.log(`\n🎯 总体结果: ${allPassed ? '✅ 所有测试通过' : '❌ 部分测试失败'}`);
  
  if (allPassed) {
    console.log('\n🎉 云盘功能已成功集成到应用中！');
    console.log('📱 您现在可以在应用主页面看到云盘功能入口');
    console.log('☁️ 云盘功能包括：文件浏览、上传、存储管理等');
  } else {
    console.log('\n⚠️ 请检查失败的测试项目并修复相关问题');
  }
  
  return allPassed;
};

// 导出测试函数
export { testDriveService, testDriveComponents, runAllTests };

// 如果直接运行此文件，执行所有测试
if (require.main === module) {
  runAllTests().then(success => {
    process.exit(success ? 0 : 1);
  });
}
