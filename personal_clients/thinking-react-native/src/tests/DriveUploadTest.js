/**
 * 测试云盘上传功能
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import DocumentPicker from 'react-native-document-picker';
import driveService from '../services/driveService';

const DriveUploadTest = () => {
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [uploadResults, setUploadResults] = useState([]);
  const [storageInfo, setStorageInfo] = useState(null);

  const addUploadResult = (fileName, success, message) => {
    setUploadResults(prev => [...prev, {
      fileName,
      success,
      message,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const getStorageInfo = async () => {
    try {
      const response = await driveService.getStorageInfo();
      if (response.success) {
        setStorageInfo(response.data);
        Alert.alert('存储信息', `总空间: ${(response.data.totalSpace / 1024 / 1024 / 1024).toFixed(2)} GB\n已使用: ${(response.data.usedSpace / 1024 / 1024 / 1024).toFixed(2)} GB\n可用空间: ${(response.data.freeSpace / 1024 / 1024 / 1024).toFixed(2)} GB`);
      } else {
        Alert.alert('错误', response.message);
      }
    } catch (error) {
      Alert.alert('错误', '获取存储信息失败: ' + error.message);
    }
  };

  const selectFiles = async () => {
    try {
      const results = await DocumentPicker.pick({
        type: [DocumentPicker.types.allFiles],
        allowMultiSelection: true,
      });
      
      setSelectedFiles(results);
      Alert.alert('成功', `选择了 ${results.length} 个文件`);
    } catch (error) {
      if (DocumentPicker.isCancel(error)) {
        console.log('用户取消选择');
      } else {
        Alert.alert('错误', '选择文件失败: ' + error.message);
      }
    }
  };

  const uploadFiles = async () => {
    if (selectedFiles.length === 0) {
      Alert.alert('提示', '请先选择文件');
      return;
    }

    setUploading(true);
    setUploadResults([]);

    for (const file of selectedFiles) {
      try {
        const fileData = {
          uri: file.uri,
          type: file.type,
          name: file.name,
        };

        const response = await driveService.uploadFile({
          fileName: file.name,
          fileSize: file.size,
          parentId: null, // 上传到根目录
          fileData: fileData,
        });

        if (response.success) {
          addUploadResult(file.name, true, '上传成功');
        } else {
          addUploadResult(file.name, false, response.message);
        }
      } catch (error) {
        addUploadResult(file.name, false, error.message);
      }
    }

    setUploading(false);
    Alert.alert('上传完成', '所有文件上传处理完成');
  };

  const clearAll = () => {
    setSelectedFiles([]);
    setUploadResults([]);
    setStorageInfo(null);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <Text style={styles.title}>云盘上传测试</Text>
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity style={styles.button} onPress={getStorageInfo}>
            <Text style={styles.buttonText}>获取存储信息</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.button} onPress={selectFiles}>
            <Text style={styles.buttonText}>选择文件</Text>
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.button, uploading && styles.disabledButton]} 
            onPress={uploadFiles}
            disabled={uploading}
          >
            {uploading ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text style={styles.buttonText}>上传文件</Text>
            )}
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.button, styles.clearButton]} onPress={clearAll}>
            <Text style={styles.buttonText}>清除所有</Text>
          </TouchableOpacity>
        </View>

        {storageInfo && (
          <View style={styles.infoContainer}>
            <Text style={styles.sectionTitle}>存储信息:</Text>
            <Text style={styles.infoText}>总空间: {(storageInfo.totalSpace / 1024 / 1024 / 1024).toFixed(2)} GB</Text>
            <Text style={styles.infoText}>已使用: {(storageInfo.usedSpace / 1024 / 1024 / 1024).toFixed(2)} GB</Text>
            <Text style={styles.infoText}>可用空间: {(storageInfo.freeSpace / 1024 / 1024 / 1024).toFixed(2)} GB</Text>
            <Text style={styles.infoText}>使用率: {storageInfo.usagePercent.toFixed(2)}%</Text>
          </View>
        )}

        {selectedFiles.length > 0 && (
          <View style={styles.filesContainer}>
            <Text style={styles.sectionTitle}>选择的文件:</Text>
            {selectedFiles.map((file, index) => (
              <View key={index} style={styles.fileItem}>
                <Text style={styles.fileName}>📄 {file.name}</Text>
                <Text style={styles.fileInfo}>类型: {file.type}</Text>
                <Text style={styles.fileInfo}>大小: {(file.size / 1024).toFixed(2)} KB</Text>
              </View>
            ))}
          </View>
        )}

        {uploadResults.length > 0 && (
          <View style={styles.resultsContainer}>
            <Text style={styles.sectionTitle}>上传结果:</Text>
            {uploadResults.map((result, index) => (
              <View key={index} style={[
                styles.resultItem,
                result.success ? styles.successResult : styles.errorResult
              ]}>
                <Text style={styles.resultFileName}>{result.fileName}</Text>
                <Text style={styles.resultMessage}>{result.message}</Text>
                <Text style={styles.resultTime}>{result.timestamp}</Text>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#999',
  },
  clearButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  infoContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  filesContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
    marginBottom: 20,
  },
  fileItem: {
    backgroundColor: '#f8f9fa',
    padding: 10,
    borderRadius: 6,
    marginBottom: 10,
  },
  fileName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 5,
    color: '#333',
  },
  fileInfo: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  resultsContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
  },
  resultItem: {
    padding: 10,
    borderRadius: 6,
    marginBottom: 10,
  },
  successResult: {
    backgroundColor: '#d4edda',
    borderColor: '#c3e6cb',
    borderWidth: 1,
  },
  errorResult: {
    backgroundColor: '#f8d7da',
    borderColor: '#f5c6cb',
    borderWidth: 1,
  },
  resultFileName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 5,
  },
  resultMessage: {
    fontSize: 14,
    marginBottom: 5,
  },
  resultTime: {
    fontSize: 12,
    color: '#666',
  },
});

export default DriveUploadTest;
