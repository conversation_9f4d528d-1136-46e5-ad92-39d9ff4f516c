/**
 * 网络连接测试
 */

import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import axios from 'axios';

const NetworkTest = () => {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState([]);

  const addResult = (test, success, message, details = null) => {
    setResults(prev => [...prev, {
      test,
      success,
      message,
      details,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const testBasicConnection = async () => {
    try {
      const response = await axios.get('https://local.luzeshu.cn/api/drive/storage/info', {
        headers: {
          'bullet': '36fdf066-9e42-11ec-b41e-525400043ced',
        },
        timeout: 10000,
      });
      
      addResult(
        '基础连接测试', 
        true, 
        '连接成功', 
        `状态码: ${response.status}, 数据: ${JSON.stringify(response.data).substring(0, 100)}...`
      );
    } catch (error) {
      addResult(
        '基础连接测试', 
        false, 
        error.message,
        error.response ? `状态码: ${error.response.status}` : '无响应'
      );
    }
  };

  const testFileList = async () => {
    try {
      const response = await axios.get('https://local.luzeshu.cn/api/drive/files', {
        headers: {
          'bullet': '36fdf066-9e42-11ec-b41e-525400043ced',
        },
        params: {
          offset: 0,
          size: 5,
        },
        timeout: 10000,
      });
      
      addResult(
        '文件列表测试', 
        true, 
        `获取到 ${response.data.total} 个文件`,
        `状态码: ${response.status}`
      );
    } catch (error) {
      addResult(
        '文件列表测试', 
        false, 
        error.message,
        error.response ? `状态码: ${error.response.status}` : '无响应'
      );
    }
  };

  const testFormDataUpload = async () => {
    try {
      // 创建一个简单的文本文件进行测试
      const formData = new FormData();
      
      // 模拟文件数据
      const testFileData = {
        uri: 'data:text/plain;base64,SGVsbG8gV29ybGQ=', // "Hello World" in base64
        type: 'text/plain',
        name: 'test.txt',
      };
      
      formData.append('file', testFileData);
      
      const response = await axios.post('https://local.luzeshu.cn/api/drive/upload', formData, {
        headers: {
          'bullet': '36fdf066-9e42-11ec-b41e-525400043ced',
          'Content-Type': 'multipart/form-data',
        },
        timeout: 30000,
      });
      
      addResult(
        'FormData 上传测试', 
        true, 
        '上传成功',
        `状态码: ${response.status}, 响应: ${JSON.stringify(response.data).substring(0, 100)}...`
      );
    } catch (error) {
      addResult(
        'FormData 上传测试', 
        false, 
        error.message,
        error.response ? `状态码: ${error.response.status}, 响应: ${JSON.stringify(error.response.data)}` : '无响应'
      );
    }
  };

  const runAllTests = async () => {
    setTesting(true);
    setResults([]);
    
    await testBasicConnection();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testFileList();
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    await testFormDataUpload();
    
    setTesting(false);
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <Text style={styles.title}>网络连接测试</Text>
        
        <View style={styles.buttonContainer}>
          <TouchableOpacity 
            style={[styles.button, testing && styles.disabledButton]} 
            onPress={runAllTests}
            disabled={testing}
          >
            {testing ? (
              <ActivityIndicator color="white" />
            ) : (
              <Text style={styles.buttonText}>运行所有测试</Text>
            )}
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.button} onPress={testBasicConnection}>
            <Text style={styles.buttonText}>基础连接测试</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.button} onPress={testFileList}>
            <Text style={styles.buttonText}>文件列表测试</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.button} onPress={testFormDataUpload}>
            <Text style={styles.buttonText}>FormData 上传测试</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={[styles.button, styles.clearButton]} onPress={clearResults}>
            <Text style={styles.buttonText}>清除结果</Text>
          </TouchableOpacity>
        </View>

        {results.length > 0 && (
          <View style={styles.resultsContainer}>
            <Text style={styles.sectionTitle}>测试结果:</Text>
            {results.map((result, index) => (
              <View key={index} style={[
                styles.resultItem,
                result.success ? styles.successResult : styles.errorResult
              ]}>
                <Text style={styles.resultTest}>{result.test}</Text>
                <Text style={styles.resultMessage}>{result.message}</Text>
                {result.details && (
                  <Text style={styles.resultDetails}>{result.details}</Text>
                )}
                <Text style={styles.resultTime}>{result.timestamp}</Text>
              </View>
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  buttonContainer: {
    marginBottom: 20,
  },
  button: {
    backgroundColor: '#007AFF',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: '#999',
  },
  clearButton: {
    backgroundColor: '#FF3B30',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333',
  },
  resultsContainer: {
    backgroundColor: 'white',
    padding: 15,
    borderRadius: 8,
  },
  resultItem: {
    padding: 10,
    borderRadius: 6,
    marginBottom: 10,
  },
  successResult: {
    backgroundColor: '#d4edda',
    borderColor: '#c3e6cb',
    borderWidth: 1,
  },
  errorResult: {
    backgroundColor: '#f8d7da',
    borderColor: '#f5c6cb',
    borderWidth: 1,
  },
  resultTest: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 5,
  },
  resultMessage: {
    fontSize: 14,
    marginBottom: 5,
  },
  resultDetails: {
    fontSize: 12,
    color: '#666',
    marginBottom: 5,
    fontStyle: 'italic',
  },
  resultTime: {
    fontSize: 12,
    color: '#666',
  },
});

export default NetworkTest;
