// Navigation Types
export type RootStackParamList = {
  MainTabs: undefined;
  Favorites: undefined;
  Tasks: undefined;
  Notes: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Messages: undefined;
  Profile: undefined;
};

export type FavoritesStackParamList = {
  FavoritesMain: undefined;
  FavoritesList: undefined;
  FavoritesAdd: undefined;
  DiskInfo: undefined;
};

export type FavoritesTabParamList = {
  DiskInfo: undefined;
  FavoritesList: undefined;
  Notifications: undefined;
};

export type TasksStackParamList = {
  TasksMain: undefined;
  TaskDetail: {taskId: string};
  TaskAdd: undefined;
  TaskEdit: {taskId: string};
};

export type NotesStackParamList = {
  NotesMain: undefined;
  NotesAdd: undefined;
  NotesList: undefined;
  NotesEdit: {noteId: string; note?: NoteItem};
  NotesDetail: {noteId: string};
};

// Favorites Types (migrated from Android app)
export interface FavoriteItem {
  id?: string;
  origin: string; // URL
  title: string;
  author: string;
  state: string; // success, pending, failed, unknown, processing, archived
  savePath: string;
  collectType: string;
  createTime: string;
  clickCount?: number;
  errorMessage?: string | null;
  statusCode?: number | null;
}

export interface FavoriteAddRequest {
  collect_type: string;
  origin: string;
  title: string;
  save_path: string;
  remark: string;
}

export interface DiskInfo {
  label: string;
  usage: string;
  progress: number; // 0-100
  fsTree: FsTreeItem[];
}

export interface FsTreeItem {
  level: number;
  name: string;
}

export interface FileSystemItem {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  modifiedAt?: string;
  children?: FileSystemItem[];
  isExpanded?: boolean;
  level: number;
}

export interface FavoritesApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

// Notes Types
export type NoteType = 'normal' | 'timeline';

export interface NoteItem {
  id: string;
  title: string;
  content: string;
  note_type: NoteType;
  category?: string | null;
  tags?: string | null;
  createTime: string;
  updateTime: string;
}

export interface NoteAddRequest {
  title: string;
  content: string;
  note_type?: NoteType;
  category?: string;
  tags?: string;
}

export interface TimelineEntry {
  id: string;
  timestamp: string;
  content: string;
}

export interface NotesApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

// Tasks Types (migrated from Rust CLI)
export interface Task {
  id: string;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  startedAt?: string;
  finishedAt?: string;
  dueDate?: string;
  parentId?: string;
  children?: Task[];
  marks: TaskMark[];
  level: number;
  duration?: number; // in minutes
  estimatedDuration?: number; // in minutes
}

export type TaskStatus = 'todo' | 'in_progress' | 'completed' | 'cancelled' | 'paused';

export type TaskPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface TaskMark {
  id: string;
  taskId: string;
  content: string;
  type: TaskMarkType;
  createdAt: string;
  metadata?: Record<string, any>;
}

export type TaskMarkType = 'note' | 'progress' | 'issue' | 'solution' | 'reference';

export interface TaskFilter {
  status?: TaskStatus[];
  priority?: TaskPriority[];
  tags?: string[];
  dateRange?: {
    start: string;
    end: string;
  };
  search?: string;
}

export interface TaskStats {
  total: number;
  byStatus: Record<TaskStatus, number>;
  byPriority: Record<TaskPriority, number>;
  totalDuration: number;
  averageDuration: number;
}

// Common Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  hasMore: boolean;
}

export interface AppTheme {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
  success: string;
  warning: string;
  error: string;
  info: string;
}

// Drive Types
export interface DriveFile {
  id: string;
  name: string;
  path: string;
  parentId?: string | null;
  fileType: 'file' | 'folder';
  mimeType?: string | null;
  size: number;
  hash?: string | null;
  storagePath?: string | null;
  thumbnailPath?: string | null;
  isPublic: boolean;
  downloadCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface DriveStorageInfo {
  totalSpace: number;
  usedSpace: number;
  freeSpace: number;
  usagePercent: number;
}

export interface DriveUploadTask {
  id: string;
  fileName: string;
  fileSize: number;
  chunkSize: number;
  totalChunks: number;
  uploadedChunks: number;
  uploadPath: string;
  tempDir: string;
  status: 'pending' | 'uploading' | 'completed' | 'failed';
  errorMessage?: string | null;
  createdAt: string;
}

export interface DriveShare {
  id: string;
  fileId: string;
  shareCode: string;
  password?: string | null;
  expireTime?: string | null;
  downloadLimit: number;
  downloadCount: number;
  isActive: boolean;
  createdAt: string;
}

export interface DriveApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

export interface DriveFileListResponse {
  items: DriveFile[];
  total: number;
  hasMore: boolean;
}

export interface DriveUploadRequest {
  fileName: string;
  fileSize: number;
  parentId?: string | null;
  fileData: any; // File or Blob
}

export interface DriveChunkUploadRequest {
  fileName: string;
  fileSize: number;
  parentId?: string | null;
  fileData: any; // File or Blob
  chunkSize?: number;
  onProgress?: (progress: {
    uploadedBytes: number;
    totalBytes: number;
    percentage: number;
    currentChunk: number;
    totalChunks: number;
  }) => void;
}

export interface DriveUploadProgress {
  uploadedBytes: number;
  totalBytes: number;
  percentage: number;
  currentChunk: number;
  totalChunks: number;
}

export interface DriveInitChunkUploadRequest {
  fileName: string;
  fileSize: number;
  parentId?: string | null;
  chunkSize?: number;
}

export interface DriveUploadChunkRequest {
  taskId: string;
  chunkIndex: number;
  chunkData: any; // Binary data
}

export interface DriveCompleteChunkUploadRequest {
  taskId: string;
}

export interface DriveFolderCreateRequest {
  name: string;
  parentId?: string | null;
}

export interface DriveFileRenameRequest {
  fileId: string;
  newName: string;
}

export interface DriveFileMoveRequest {
  fileId: string;
  newParentId?: string | null;
}

export type DriveStackParamList = {
  DriveMain: undefined;
  DriveList: { parentId?: string | null; parentName?: string };
  DriveUpload: { parentId?: string | null };
  DriveFileDetail: { fileId: string };
};

// Messages Types
export interface Message {
  id: string;
  title: string;
  content: string;
  type: 'info' | 'warning' | 'error' | 'success';
  read: boolean;
  createdAt: string;
  source: 'system' | 'favorites' | 'tasks' | 'notes' | 'drive';
}

export interface NotificationSettings {
  enabled: boolean;
  taskReminders: boolean;
  favoritesUpdates: boolean;
  systemMessages: boolean;
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
  };
}

// Profile Types
export interface UserProfile {
  id: string;
  name: string;
  email?: string;
  avatar?: string;
  preferences: UserPreferences;
  stats: UserStats;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  notifications: NotificationSettings;
  defaultTaskPriority: TaskPriority;
  autoArchiveFavorites: boolean;
  backupEnabled: boolean;
}

export interface UserStats {
  totalTasks: number;
  completedTasks: number;
  totalFavorites: number;
  totalTime: number; // in minutes
  streakDays: number;
  joinedAt: string;
} 