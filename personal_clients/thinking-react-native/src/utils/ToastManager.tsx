import React, { createContext, useContext, useState, ReactNode } from 'react';
import Toast from '../components/Toast';

interface ToastContextType {
  showToast: (message: string, type?: 'success' | 'error' | 'info', duration?: number) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

interface ToastProviderProps {
  children: ReactNode;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [toastConfig, setToastConfig] = useState<{
    message: string;
    visible: boolean;
    type: 'success' | 'error' | 'info';
    duration: number;
  }>({
    message: '',
    visible: false,
    type: 'success',
    duration: 1000,
  });

  const showToast = (
    message: string,
    type: 'success' | 'error' | 'info' = 'success',
    duration: number = 1000
  ) => {
    setToastConfig({
      message,
      visible: true,
      type,
      duration,
    });
  };

  const hideToast = () => {
    setToastConfig(prev => ({
      ...prev,
      visible: false,
    }));
  };

  return (
    <ToastContext.Provider value={{ showToast }}>
      {children}
      <Toast
        message={toastConfig.message}
        visible={toastConfig.visible}
        type={toastConfig.type}
        duration={toastConfig.duration}
        onHide={hideToast}
      />
    </ToastContext.Provider>
  );
};

export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};
