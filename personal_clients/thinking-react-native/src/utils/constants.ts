// App Information
export const APP_NAME = 'Thinking Android';
export const APP_VERSION = '1.0.0';
export const APP_DESCRIPTION = '整合收藏夹、任务管理和笔记功能的高效应用';

// API Configuration
export const API_BASE_URLS = {
  FAVORITES: 'http://localhost:8080/api/favorites',
  TASKS: 'http://localhost:3000/api/tasks',
  NOTES: 'http://localhost:3001/api/notes',
  DRIVE: 'http://localhost:8080/api/drive',
};

export const API_TIMEOUT = 10000; // 10 seconds

// Colors
export const COLORS = {
  PRIMARY: '#6750A4',
  SECONDARY: '#625B71',
  SUCCESS: '#4CAF50',
  WARNING: '#FF9800',
  ERROR: '#F44336',
  INFO: '#2196F3',
  
  // App Specific Colors
  FAVORITES_COLOR: '#FF6B6B',
  TASKS_COLOR: '#4ECDC4',
  NOTES_COLOR: '#45B7D1',
  DRIVE_COLOR: '#9C27B0',
  
  // Semantic Colors
  BACKGROUND: '#F8F9FA',
  SURFACE: '#FFFBFE',
  TEXT_PRIMARY: '#1C1B1F',
  TEXT_SECONDARY: '#49454F',
  TEXT_DISABLED: '#79747E',
  BORDER: '#E7E0EC',
};

// Spacing
export const SPACING = {
  XS: 4,
  SM: 8,
  MD: 16,
  LG: 24,
  XL: 32,
  XXL: 48,
};

// Typography
export const TYPOGRAPHY = {
  FONT_SIZES: {
    XS: 12,
    SM: 14,
    MD: 16,
    LG: 18,
    XL: 20,
    XXL: 24,
    XXXL: 32,
  },
  FONT_WEIGHTS: {
    REGULAR: '400',
    MEDIUM: '500',
    BOLD: '700',
  },
  LINE_HEIGHTS: {
    TIGHT: 1.2,
    NORMAL: 1.5,
    LOOSE: 1.8,
  },
};

// Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'auth_token',
  USER_PREFERENCES: 'user_preferences',
  FAVORITES_CACHE: 'favorites_cache',
  TASKS_CACHE: 'tasks_cache',
  APP_SETTINGS: 'app_settings',
};

// Screen Names
export const SCREEN_NAMES = {
  // Main Tabs
  HOME: 'Home',
  MESSAGES: 'Messages',
  PROFILE: 'Profile',
  
  // Sub Apps
  FAVORITES: 'Favorites',
  TASKS: 'Tasks',
  NOTES: 'Notes',
  DRIVE: 'Drive',
  
  // Favorites Screens
  FAVORITES_MAIN: 'FavoritesMain',
  FAVORITES_ADD: 'FavoritesAdd',
  FAVORITES_EDIT: 'FavoritesEdit',
  FAVORITES_DETAIL: 'FavoritesDetail',
  
  // Tasks Screens
  TASKS_MAIN: 'TasksMain',
  TASKS_ADD: 'TasksAdd',
  TASKS_EDIT: 'TasksEdit',
  TASKS_DETAIL: 'TasksDetail',
  
  // Notes Screens
  NOTES_MAIN: 'NotesMain',
  NOTES_ADD: 'NotesAdd',
  NOTES_EDIT: 'NotesEdit',
  NOTES_DETAIL: 'NotesDetail',

  // Drive Screens
  DRIVE_MAIN: 'DriveMain',
  DRIVE_LIST: 'DriveList',
  DRIVE_UPLOAD: 'DriveUpload',
  DRIVE_FILE_DETAIL: 'DriveFileDetail',
};

// Default Values
export const DEFAULTS = {
  PAGINATION: {
    PAGE_SIZE: 20,
    INITIAL_PAGE: 1,
  },
  TASK_PRIORITY: 'medium' as const,
  THEME: 'auto' as const,
  LANGUAGE: 'zh-CN',
};

// Regular Expressions
export const REGEX = {
  URL: /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/,
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^1[3-9]\d{9}$/,
};

// Feature Flags
export const FEATURES = {
  FAVORITES_ENABLED: true,
  TASKS_ENABLED: true,
  NOTES_ENABLED: true, // Now available
  BACKUP_ENABLED: true,
  ANALYTICS_ENABLED: false,
  OFFLINE_MODE: true,
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '网络连接错误，请检查网络设置',
  TIMEOUT_ERROR: '请求超时，请稍后重试',
  UNKNOWN_ERROR: '未知错误，请稍后重试',
  VALIDATION_ERROR: '输入数据不符合要求',
  PERMISSION_ERROR: '权限不足，无法执行操作',
  NOT_FOUND_ERROR: '请求的资源不存在',
  SERVER_ERROR: '服务器错误，请稍后重试',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  SAVE_SUCCESS: '保存成功',
  DELETE_SUCCESS: '删除成功',
  UPDATE_SUCCESS: '更新成功',
  SYNC_SUCCESS: '同步成功',
  BACKUP_SUCCESS: '备份成功',
  EXPORT_SUCCESS: '导出成功',
};

// Time Constants
export const TIME = {
  SECOND: 1000,
  MINUTE: 60 * 1000,
  HOUR: 60 * 60 * 1000,
  DAY: 24 * 60 * 60 * 1000,
  
  // Refresh intervals
  SYNC_INTERVAL: 5 * 60 * 1000, // 5 minutes
  BACKUP_INTERVAL: 24 * 60 * 60 * 1000, // 24 hours
};

// Limits
export const LIMITS = {
  MAX_FAVORITES: 1000,
  MAX_TASKS: 500,
  MAX_NOTES: 200,
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_TITLE_LENGTH: 100,
  MAX_DESCRIPTION_LENGTH: 500,
  MAX_TAGS: 10,
}; 