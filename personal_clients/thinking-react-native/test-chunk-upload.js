/**
 * 测试分片上传功能
 * Test chunk upload functionality
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const path = require('path');

const API_BASE_URL = 'https://local.luzeshu.cn/api';
const BULLET_HEADER = '36fdf066-9e42-11ec-b41e-525400043ced';
const CHUNK_SIZE = 1024 * 1024; // 1MB chunks

class ChunkUploadTester {
  constructor() {
    this.axiosInstance = axios.create({
      baseURL: API_BASE_URL,
      timeout: 60000, // 60秒超时
      headers: {
        'Content-Type': 'application/json',
        'bullet': BULLET_HEADER,
      },
    });
    
    this.chunkUploadAvailable = false;
  }

  /**
   * 检查分片上传功能是否可用
   */
  async checkChunkUploadAvailability() {
    try {
      console.log('🔍 检查分片上传功能可用性...');
      
      // 尝试调用初始化接口
      const response = await this.axiosInstance.post('/drive/init-chunk-upload', {
        file_name: 'test-availability.txt',
        file_size: 1024,
        chunk_size: 512,
      });
      
      this.chunkUploadAvailable = true;
      console.log('✅ 分片上传功能可用');
      return true;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        console.log('⚠️  分片上传功能暂不可用，将使用模拟测试');
        this.chunkUploadAvailable = false;
        return false;
      } else {
        console.log('⚠️  分片上传功能检查失败，将使用模拟测试');
        this.chunkUploadAvailable = false;
        return false;
      }
    }
  }

  /**
   * 创建测试文件
   */
  createTestFile(fileName, sizeInMB) {
    const filePath = path.join(__dirname, fileName);
    const sizeInBytes = sizeInMB * 1024 * 1024;
    
    console.log(`📄 创建测试文件: ${fileName} (${sizeInMB}MB)`);
    
    // 创建指定大小的测试文件
    const buffer = Buffer.alloc(sizeInBytes, 'A');
    fs.writeFileSync(filePath, buffer);
    
    return {
      path: filePath,
      name: fileName,
      size: sizeInBytes,
    };
  }

  /**
   * 初始化分片上传
   */
  async initChunkUpload(fileName, fileSize, parentId = null) {
    try {
      console.log(`🚀 初始化分片上传: ${fileName}`);
      
      const requestData = {
        file_name: fileName,
        file_size: fileSize,
        chunk_size: CHUNK_SIZE,
      };

      if (parentId) {
        requestData.parent_id = parentId;
      }

      const response = await this.axiosInstance.post('/drive/init-chunk-upload', requestData);

      if (response.data?.data) {
        const task = response.data.data;
        console.log('✅ 分片上传初始化成功:');
        console.log(`   任务ID: ${task.ID || task.id}`);
        console.log(`   文件名: ${task.FileName || task.file_name}`);
        console.log(`   文件大小: ${task.FileSize || task.file_size} bytes`);
        console.log(`   分片大小: ${task.ChunkSize || task.chunk_size} bytes`);
        console.log(`   总分片数: ${task.TotalChunks || task.total_chunks}`);
        console.log(`   上传路径: ${task.UploadPath || task.upload_path}`);
        console.log(`   临时目录: ${task.TempDir || task.temp_dir}`);
        
        return {
          success: true,
          data: {
            id: task.ID?.toString() || task.id?.toString() || '',
            fileName: task.FileName || task.file_name || '',
            fileSize: task.FileSize || task.file_size || 0,
            chunkSize: task.ChunkSize || task.chunk_size || 0,
            totalChunks: task.TotalChunks || task.total_chunks || 0,
            uploadedChunks: task.UploadedChunks || task.uploaded_chunks || 0,
            uploadPath: task.UploadPath || task.upload_path || '',
            tempDir: task.TempDir || task.temp_dir || '',
            status: task.Status || task.status || 'pending',
          },
        };
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error('❌ 初始化分片上传失败:', error.message);
      if (error.response) {
        console.error('   状态码:', error.response.status);
        console.error('   响应数据:', error.response.data);
      }
      return { success: false, error: error.message };
    }
  }

  /**
   * 上传单个分片
   */
  async uploadChunk(taskId, chunkIndex, chunkData) {
    try {
      console.log(`📤 上传分片 ${chunkIndex + 1}`);
      
      const formData = new FormData();
      formData.append('task_id', taskId);
      formData.append('chunk_index', chunkIndex.toString());
      formData.append('chunk_data', chunkData);

      const response = await axios.post(`${API_BASE_URL}/drive/upload-chunk`, formData, {
        headers: {
          ...formData.getHeaders(),
          'bullet': BULLET_HEADER,
        },
        timeout: 60000,
      });

      if (response.data?.data) {
        const task = response.data.data;
        console.log(`✅ 分片 ${chunkIndex + 1} 上传成功`);
        console.log(`   已上传分片: ${task.UploadedChunks || task.uploaded_chunks}/${task.TotalChunks || task.total_chunks}`);
        
        return {
          success: true,
          data: {
            id: task.ID?.toString() || task.id?.toString() || '',
            uploadedChunks: task.UploadedChunks || task.uploaded_chunks || 0,
            totalChunks: task.TotalChunks || task.total_chunks || 0,
            status: task.Status || task.status || 'pending',
          },
        };
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error(`❌ 分片 ${chunkIndex + 1} 上传失败:`, error.message);
      if (error.response) {
        console.error('   状态码:', error.response.status);
        console.error('   响应数据:', error.response.data);
      }
      return { success: false, error: error.message };
    }
  }

  /**
   * 完成分片上传
   */
  async completeChunkUpload(taskId) {
    try {
      console.log('🏁 完成分片上传');
      
      const requestData = {
        task_id: taskId,
      };

      const response = await this.axiosInstance.post('/drive/complete-chunk-upload', requestData);

      if (response.data?.data) {
        const file = response.data.data;
        console.log('✅ 分片上传完成:');
        console.log(`   文件ID: ${file.ID || file.id}`);
        console.log(`   文件名: ${file.Name || file.name}`);
        console.log(`   文件大小: ${file.Size || file.size} bytes`);
        console.log(`   文件路径: ${file.Path || file.path}`);
        console.log(`   存储路径: ${file.StoragePath || file.storage_path}`);
        console.log(`   哈希值: ${file.Hash || file.hash}`);
        
        return {
          success: true,
          data: {
            id: file.ID?.toString() || file.id?.toString() || '',
            name: file.Name || file.name || '',
            path: file.Path || file.path || '',
            size: file.Size || file.size || 0,
            hash: file.Hash || file.hash || null,
            storagePath: file.StoragePath || file.storage_path || null,
          },
        };
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error('❌ 完成分片上传失败:', error.message);
      if (error.response) {
        console.error('   状态码:', error.response.status);
        console.error('   响应数据:', error.response.data);
      }
      return { success: false, error: error.message };
    }
  }

  /**
   * 模拟分片上传（使用常规上传API）
   */
  async simulateChunkUpload(fileName, sizeInMB) {
    console.log(`\n🔄 模拟分片上传测试: ${fileName} (${sizeInMB}MB)`);
    console.log('📝 说明: 由于分片上传功能暂不可用，将使用常规上传API进行模拟测试\n');
    
    let testFile = null;
    
    try {
      // 1. 创建测试文件
      testFile = this.createTestFile(fileName, sizeInMB);
      
      // 2. 模拟分片信息计算
      const totalChunks = Math.ceil(testFile.size / CHUNK_SIZE);
      console.log(`📊 模拟分片信息:`);
      console.log(`   文件大小: ${testFile.size} bytes`);
      console.log(`   分片大小: ${CHUNK_SIZE} bytes`);
      console.log(`   总分片数: ${totalChunks}`);
      
      // 3. 使用常规上传API
      console.log('\n⬆️ 使用常规上传API上传文件...');
      
      const formData = new FormData();
      formData.append('file', fs.createReadStream(testFile.path));
      
      const response = await axios.post(`${API_BASE_URL}/drive/upload`, formData, {
        headers: {
          ...formData.getHeaders(),
          'bullet': BULLET_HEADER,
        },
        timeout: 60000,
      });

      if (response.data?.data) {
        const file = response.data.data;
        console.log('✅ 模拟分片上传成功:');
        console.log(`   文件ID: ${file.ID || file.id}`);
        console.log(`   文件名: ${file.Name || file.name}`);
        console.log(`   文件大小: ${file.Size || file.size} bytes`);
        console.log(`   文件路径: ${file.Path || file.path}`);
        console.log(`   存储路径: ${file.StoragePath || file.storage_path}`);
        console.log(`   哈希值: ${file.Hash || file.hash}`);
        
        return {
          success: true,
          file: {
            id: file.ID?.toString() || file.id?.toString() || '',
            name: file.Name || file.name || '',
            path: file.Path || file.path || '',
            size: file.Size || file.size || 0,
            hash: file.Hash || file.hash || null,
            storagePath: file.StoragePath || file.storage_path || null,
          },
          simulatedChunks: totalChunks,
        };
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error('\n💥 模拟分片上传失败:', error.message);
      if (error.response) {
        console.error('   状态码:', error.response.status);
        console.error('   响应数据:', error.response.data);
      }
      return {
        success: false,
        error: error.message,
      };
    } finally {
      // 清理测试文件
      if (testFile && fs.existsSync(testFile.path)) {
        fs.unlinkSync(testFile.path);
        console.log(`🗑️ 清理测试文件: ${testFile.name}`);
      }
    }
  }

  /**
   * 测试真实的分片上传流程
   */
  async testRealChunkUpload(fileName, sizeInMB) {
    console.log(`\n🧪 开始真实分片上传测试: ${fileName} (${sizeInMB}MB)\n`);
    
    let testFile = null;
    let uploadSuccess = false;
    
    try {
      // 1. 创建测试文件
      testFile = this.createTestFile(fileName, sizeInMB);
      
      // 2. 初始化分片上传
      const initResult = await this.initChunkUpload(testFile.name, testFile.size);
      if (!initResult.success) {
        throw new Error(`初始化失败: ${initResult.error}`);
      }
      
      const uploadTask = initResult.data;
      const totalChunks = uploadTask.totalChunks;
      
      // 3. 读取文件并分片上传
      const fileBuffer = fs.readFileSync(testFile.path);
      
      for (let chunkIndex = 0; chunkIndex < totalChunks; chunkIndex++) {
        const start = chunkIndex * CHUNK_SIZE;
        const end = Math.min(start + CHUNK_SIZE, testFile.size);
        const chunkData = fileBuffer.slice(start, end);
        
        const chunkResult = await this.uploadChunk(uploadTask.id, chunkIndex, chunkData);
        if (!chunkResult.success) {
          throw new Error(`分片 ${chunkIndex + 1} 上传失败: ${chunkResult.error}`);
        }
        
        // 显示进度
        const progress = Math.round(((chunkIndex + 1) / totalChunks) * 100);
        console.log(`📊 上传进度: ${progress}% (${chunkIndex + 1}/${totalChunks})`);
      }
      
      // 4. 完成分片上传
      const completeResult = await this.completeChunkUpload(uploadTask.id);
      if (!completeResult.success) {
        throw new Error(`完成上传失败: ${completeResult.error}`);
      }
      
      uploadSuccess = true;
      console.log('\n🎉 真实分片上传测试成功完成!');
      
      return {
        success: true,
        file: completeResult.data,
        uploadTask: uploadTask,
      };
      
    } catch (error) {
      console.error('\n💥 真实分片上传测试失败:', error.message);
      return {
        success: false,
        error: error.message,
      };
    } finally {
      // 清理测试文件
      if (testFile && fs.existsSync(testFile.path)) {
        fs.unlinkSync(testFile.path);
        console.log(`🗑️ 清理测试文件: ${testFile.name}`);
      }
    }
  }

  /**
   * 验证上传的文件
   */
  async verifyUploadedFile(fileName) {
    try {
      console.log(`\n🔍 验证上传的文件: ${fileName}`);
      
      const response = await this.axiosInstance.get('/drive/files', {
        params: {
          offset: 0,
          size: 50,
        },
      });

      if (response.data?.data) {
        const files = response.data.data;
        const uploadedFile = files.find(file => file.Name === fileName || file.name === fileName);
        
        if (uploadedFile) {
          console.log('✅ 文件验证成功:');
          console.log(`   文件名: ${uploadedFile.Name || uploadedFile.name}`);
          console.log(`   文件大小: ${uploadedFile.Size || uploadedFile.size} bytes`);
          console.log(`   创建时间: ${uploadedFile.CreatedAt || uploadedFile.created_at}`);
          console.log(`   更新时间: ${uploadedFile.UpdatedAt || uploadedFile.updated_at}`);
          return { success: true, file: uploadedFile };
        } else {
          console.log('❌ 文件未在列表中找到');
          return { success: false, error: '文件未找到' };
        }
      }

      throw new Error('Invalid response format');
    } catch (error) {
      console.error('❌ 验证文件失败:', error.message);
      return { success: false, error: error.message };
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests() {
    console.log('🚀 开始运行分片上传测试套件\n');
    
    // 检查分片上传功能可用性
    await this.checkChunkUploadAvailability();
    
    const testCases = [
      { fileName: 'test-small-chunk.txt', sizeInMB: 0.5 },   // 小于1MB，测试单分片
      { fileName: 'test-medium-chunk.txt', sizeInMB: 2.5 },  // 多分片测试
      { fileName: 'test-large-chunk.txt', sizeInMB: 5 },     // 大文件测试
    ];
    
    let successCount = 0;
    let failCount = 0;
    
    for (const testCase of testCases) {
      let result;
      
      if (this.chunkUploadAvailable) {
        // 使用真实的分片上传
        result = await this.testRealChunkUpload(testCase.fileName, testCase.sizeInMB);
      } else {
        // 使用模拟的分片上传
        result = await this.simulateChunkUpload(testCase.fileName, testCase.sizeInMB);
      }
      
      if (result.success) {
        successCount++;
        
        // 验证上传的文件
        await this.verifyUploadedFile(testCase.fileName);
      } else {
        failCount++;
      }
      
      // 测试之间稍微等待
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('\n📊 测试结果总结:');
    console.log(`✅ 成功: ${successCount} 个测试`);
    console.log(`❌ 失败: ${failCount} 个测试`);
    console.log(`📈 成功率: ${Math.round((successCount / testCases.length) * 100)}%`);
    
    if (this.chunkUploadAvailable) {
      if (successCount === testCases.length) {
        console.log('\n🎉 所有真实分片上传测试都成功完成!');
      } else {
        console.log('\n⚠️ 部分真实分片上传测试失败，请检查错误信息');
      }
    } else {
      if (successCount === testCases.length) {
        console.log('\n🎉 所有模拟分片上传测试都成功完成!');
        console.log('💡 提示: 当分片上传功能部署后，可以测试真实的分片上传流程');
      } else {
        console.log('\n⚠️ 部分模拟分片上传测试失败，请检查错误信息');
      }
    }
  }
}

// 运行测试
async function main() {
  const tester = new ChunkUploadTester();
  
  try {
    await tester.runAllTests();
  } catch (error) {
    console.error('💥 测试运行失败:', error);
  }
}

// 如果直接运行此文件，则执行测试
if (require.main === module) {
  main().catch(console.error);
}

module.exports = ChunkUploadTester; 