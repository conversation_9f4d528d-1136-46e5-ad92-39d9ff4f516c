/**
 * 测试云盘API功能
 * 验证前端与后端API的集成是否正常
 */

const axios = require('axios');

const API_BASE_URL = 'https://local.luzeshu.cn/api';
const API_HEADERS = {
  'Content-Type': 'application/json',
  'bullet': '36fdf066-9e42-11ec-b41e-525400043ced',
};

async function testDriveAPI() {
  console.log('🚀 开始测试云盘API...\n');

  try {
    // 测试存储信息
    console.log('📊 测试存储信息API...');
    const storageResponse = await axios.get(`${API_BASE_URL}/drive/storage/info`, {
      headers: API_HEADERS,
    });
    
    console.log('✅ 存储信息获取成功:');
    console.log(`   总空间: ${formatBytes(storageResponse.data.total_space)}`);
    console.log(`   已使用: ${formatBytes(storageResponse.data.used_space)}`);
    console.log(`   可用空间: ${formatBytes(storageResponse.data.free_space)}`);
    console.log(`   使用率: ${storageResponse.data.usage_percent.toFixed(2)}%\n`);

    // 测试文件列表
    console.log('📁 测试文件列表API...');
    const filesResponse = await axios.get(`${API_BASE_URL}/drive/files`, {
      headers: API_HEADERS,
      params: {
        offset: 0,
        size: 10,
      },
    });

    console.log('✅ 文件列表获取成功:');
    console.log(`   文件总数: ${filesResponse.data.total}`);
    console.log(`   当前页文件数: ${filesResponse.data.data.length}`);
    console.log('   文件列表:');
    filesResponse.data.data.forEach((file, index) => {
      const icon = file.FileType === 'folder' ? '📁' : '📄';
      console.log(`     ${index + 1}. ${icon} ${file.Name} (${file.FileType})`);
    });
    console.log('');

    // 测试创建文件夹
    console.log('🆕 测试创建文件夹API...');
    const folderName = `测试文件夹_${Date.now()}`;
    const createResponse = await axios.post(`${API_BASE_URL}/drive/folders`, {
      name: folderName,
    }, {
      headers: API_HEADERS,
    });

    console.log('✅ 文件夹创建成功:');
    console.log(`   文件夹名: ${createResponse.data.data.Name}`);
    console.log(`   文件夹路径: ${createResponse.data.data.Path}`);
    console.log(`   创建时间: ${createResponse.data.data.CreatedAt}\n`);

    // 再次获取文件列表验证
    console.log('🔄 验证文件夹是否创建成功...');
    const verifyResponse = await axios.get(`${API_BASE_URL}/drive/files`, {
      headers: API_HEADERS,
      params: {
        offset: 0,
        size: 10,
      },
    });

    console.log('✅ 验证成功:');
    console.log(`   当前文件总数: ${verifyResponse.data.total}`);
    const newFolder = verifyResponse.data.data.find(f => f.Name === folderName);
    if (newFolder) {
      console.log(`   ✅ 新文件夹已出现在列表中: ${newFolder.Name}`);
    } else {
      console.log(`   ❌ 新文件夹未在列表中找到`);
    }

    console.log('\n🎉 所有API测试通过！云盘功能正常工作。');
    console.log('\n📱 前端应用现在可以：');
    console.log('   • 查看存储空间使用情况');
    console.log('   • 浏览文件和文件夹列表');
    console.log('   • 创建新文件夹');
    console.log('   • 选择和上传文件（需要在手机上测试）');
    console.log('   • 删除文件和文件夹');

  } catch (error) {
    console.error('❌ API测试失败:', error.message);
    if (error.response) {
      console.error('   响应状态:', error.response.status);
      console.error('   响应数据:', error.response.data);
    }
  }
}

function formatBytes(bytes) {
  if (bytes === 0) return '0 B';
  
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 运行测试
testDriveAPI().then(() => {
  process.exit(0);
}).catch((error) => {
  console.error('测试执行失败:', error);
  process.exit(1);
});
