#!/usr/bin/env node

/**
 * 最终上传测试脚本 - 验证修复后的功能
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const WebSocket = require('ws');

const API_BASE_URL = 'https://local.luzeshu.cn/api';
const BULLET_HEADER = '36fdf066-9e42-11ec-b41e-525400043ced';

// 动态计算分片大小（与前端逻辑一致）
function calculateChunkSize(fileSize) {
  const MIN_CHUNK_SIZE = 64 * 1024;    // 64KB 最小分片
  const MAX_CHUNK_SIZE = 1 * 1024 * 1024; // 1MB 最大分片，确保nginx兼容
  const TARGET_CHUNKS = Math.max(20, Math.ceil(fileSize / MAX_CHUNK_SIZE)); // 确保分片不超过1MB

  // 根据文件大小计算理想分片大小
  let idealChunkSize = Math.ceil(fileSize / TARGET_CHUNKS);

  // 限制在合理范围内
  idealChunkSize = Math.max(MIN_CHUNK_SIZE, idealChunkSize);
  idealChunkSize = Math.min(MAX_CHUNK_SIZE, idealChunkSize);

  console.log(`文件大小: ${fileSize} bytes, 计算分片大小: ${idealChunkSize} bytes, 预计分片数: ${Math.ceil(fileSize / idealChunkSize)}`);

  return idealChunkSize;
}

// 创建测试文件
function createTestFile(size = 176 * 1024 * 1024) { // 176MB 模拟视频文件
  const filename = '/tmp/test-final-upload.bin';
  const buffer = Buffer.alloc(size, 'B');
  fs.writeFileSync(filename, buffer);
  console.log(`✅ 创建测试文件: ${filename} (${size} bytes)`);
  return { filename, size };
}

// 测试完整上传流程
async function testCompleteUploadFlow() {
  console.log('🧪 开始最终上传测试...\n');

  let testFile = null;
  let wsConnected = false;
  let progressReceived = false;

  try {
    // 1. 创建测试文件
    testFile = createTestFile();
    const chunkSize = calculateChunkSize(testFile.size);
    
    // 2. 初始化分片上传
    console.log('\n🚀 初始化分片上传...');
    const initResponse = await axios.post(`${API_BASE_URL}/drive/init-chunk-upload`, {
      file_name: `test-final-upload-${Date.now()}.bin`,
      file_size: testFile.size,
      parent_id: null,
      chunk_size: chunkSize
    }, {
      headers: {
        'Content-Type': 'application/json',
        'bullet': BULLET_HEADER,
      },
      timeout: 10000,
    });

    const taskId = initResponse.data.data.ID;
    console.log(`✅ 初始化成功 - 任务ID: ${taskId}`);
    
    // 3. 启动WebSocket监听
    console.log('\n🔌 建立WebSocket连接...');
    const ws = new WebSocket(`wss://local.luzeshu.cn/api/drive/upload-progress?task_id=${taskId}`, {
      rejectUnauthorized: false
    });

    ws.on('open', () => {
      wsConnected = true;
      console.log('✅ WebSocket连接成功');
    });

    ws.on('message', (data) => {
      try {
        const progress = JSON.parse(data.toString());
        progressReceived = true;
        console.log(`📊 收到进度更新: ${progress.progress}% (${progress.uploaded_bytes}/${progress.total_bytes})`);
      } catch (error) {
        console.log(`❌ 解析进度消息失败: ${error.message}`);
      }
    });

    ws.on('error', (error) => {
      console.log(`❌ WebSocket错误: ${error.message}`);
    });

    // 等待WebSocket连接
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 4. 上传分片
    const totalChunks = Math.ceil(testFile.size / chunkSize);
    console.log(`\n📤 开始上传 ${totalChunks} 个分片...`);
    
    for (let i = 0; i < totalChunks; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, testFile.size);
      
      const formData = new FormData();
      formData.append('task_id', taskId.toString());
      formData.append('chunk_index', i.toString());
      
      const chunkData = fs.createReadStream(testFile.filename, { start, end: end - 1 });
      formData.append('chunk_data', chunkData, `chunk_${i}`);

      const chunkResponse = await axios.post(`${API_BASE_URL}/drive/upload-chunk`, formData, {
        headers: {
          ...formData.getHeaders(),
          'bullet': BULLET_HEADER,
        },
        timeout: 30000,
      });

      console.log(`✅ 分片 ${i + 1}/${totalChunks} 上传成功`);
      
      // 等待一下，让进度更新有时间传播
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    // 5. 完成上传（使用修复后的格式）
    console.log('\n🏁 完成上传...');
    const completeResponse = await axios.post(`${API_BASE_URL}/drive/complete-chunk-upload`, {
      task_id: taskId.toString(), // 修复：使用字符串格式
    }, {
      headers: {
        'Content-Type': 'application/json',
        'bullet': BULLET_HEADER,
      },
      timeout: 10000,
    });

    console.log(`✅ 上传完成 - 文件ID: ${completeResponse.data.data.ID}`);
    
    // 6. 关闭WebSocket
    ws.close();
    
    // 7. 等待一下确保所有进度都收到
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // 8. 总结结果
    console.log('\n📋 测试结果总结:');
    console.log(`   WebSocket连接: ${wsConnected ? '✅' : '❌'}`);
    console.log(`   进度更新接收: ${progressReceived ? '✅' : '❌'}`);
    console.log(`   分片上传: ✅`);
    console.log(`   完成上传: ✅`);
    console.log(`   动态分片大小: ✅ (${chunkSize} bytes, ${totalChunks} 个分片)`);
    
    if (wsConnected && progressReceived) {
      console.log('\n🎉 所有测试通过！上传进度功能完全正常！');
      return true;
    } else {
      console.log('\n⚠️  部分测试失败，请检查WebSocket连接和进度推送。');
      return false;
    }
    
  } catch (error) {
    console.log(`\n💥 测试失败: ${error.message}`);
    if (error.response) {
      console.log(`状态码: ${error.response.status}`);
      console.log(`响应: ${JSON.stringify(error.response.data)}`);
    }
    return false;
  } finally {
    // 清理测试文件
    if (testFile && fs.existsSync(testFile.filename)) {
      fs.unlinkSync(testFile.filename);
      console.log('🗑️  清理测试文件');
    }
  }
}

// 运行测试
if (require.main === module) {
  testCompleteUploadFlow().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { testCompleteUploadFlow };
