# 图片预览功能测试指南

## 测试步骤

1. **打开thinking app**
   - 确保应用已安装并运行
   - 导航到云存储(Drive)功能

2. **测试图片列表显示**
   - 检查图片文件是否显示缩略图
   - 验证缩略图是否正确加载（不是转圈）

3. **测试单张图片预览**
   - 点击任意图片文件
   - 验证是否打开FilePreviewScreen
   - 检查图片是否正确显示（不是转圈）

4. **测试图片查看器**
   - 在文件列表中点击"预览图片"按钮
   - 验证ImageViewer是否正确打开
   - 测试左右切换功能
   - 检查每张图片是否都能正确显示

## 预期结果

### 正常情况
- 缩略图正常显示
- 图片预览快速加载
- 切换图片时每张都能正确显示
- API日志显示正确的下载请求

### 修复前的问题
- nginx日志显示400错误
- 图片一直转圈不显示
- 切换图片时显示相同内容

## 监控日志

```bash
# 监控drive-service日志
tail -f /data/docker-volumes/drive-service/runtime/drive-service-logs/stderr.log

# 监控nginx日志
docker logs nginx-admin --tail 10 -f

# 测试API
curl -H "bullet: 36fdf066-9e42-11ec-b41e-525400043ced" \
  https://local.luzeshu.cn/api/drive/files/24/download -v
```

## 修复内容

1. **添加认证头支持**
   - `getDownloadUrlWithHeaders()` - 返回带认证头的下载URL
   - `getThumbnailUrlWithHeaders()` - 返回带认证头的缩略图URL

2. **替换图片查看器**
   - 移除react-native-image-viewing依赖
   - 使用自定义FastImage组件支持headers
   - 添加手动导航控制

3. **更新所有图片组件**
   - FilePreviewScreen.tsx
   - DriveListScreen.tsx  
   - ImageViewer.tsx
   - DriveFeatureTest.tsx
