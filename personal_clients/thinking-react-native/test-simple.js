#!/usr/bin/env node

/**
 * 简单的上传测试脚本
 */

const axios = require('axios');

const API_BASE_URL = 'https://local.luzeshu.cn/api';
const BULLET_HEADER = '36fdf066-9e42-11ec-b41e-525400043ced';

async function testBasicAPI() {
  console.log('🧪 测试基础API连接...');

  try {
    // 测试存储信息API
    const response = await axios.get(`${API_BASE_URL}/drive/storage/info`, {
      headers: {
        'bullet': BULLET_HEADER,
      },
      timeout: 10000,
    });

    console.log('✅ API连接成功');
    console.log('响应状态:', response.status);
    console.log('响应数据:', JSON.stringify(response.data, null, 2));
    
    return true;
  } catch (error) {
    console.log('❌ API连接失败:', error.message);
    if (error.response) {
      console.log('状态码:', error.response.status);
      console.log('响应数据:', error.response.data);
    }
    return false;
  }
}

async function testWebSocketConnection() {
  console.log('\n🔌 测试WebSocket连接...');
  
  const WebSocket = require('ws');
  
  return new Promise((resolve) => {
    const ws = new WebSocket(`wss://local.luzeshu.cn/api/drive/upload-progress?task_id=test123`, {
      rejectUnauthorized: false
    });

    let connected = false;

    ws.on('open', () => {
      connected = true;
      console.log('✅ WebSocket连接成功');
      ws.close();
    });

    ws.on('error', (error) => {
      console.log('❌ WebSocket连接失败:', error.message);
      resolve(false);
    });

    ws.on('close', () => {
      console.log('🔌 WebSocket连接关闭');
      resolve(connected);
    });

    // 5秒超时
    setTimeout(() => {
      if (!connected) {
        console.log('⏰ WebSocket连接超时');
        ws.close();
        resolve(false);
      }
    }, 5000);
  });
}

async function runTests() {
  console.log('🚀 开始简单测试...\n');

  const apiResult = await testBasicAPI();
  const wsResult = await testWebSocketConnection();

  console.log('\n📋 测试结果:');
  console.log(`API连接: ${apiResult ? '✅' : '❌'}`);
  console.log(`WebSocket连接: ${wsResult ? '✅' : '❌'}`);

  if (apiResult && wsResult) {
    console.log('\n🎉 基础功能测试通过！');
    process.exit(0);
  } else {
    console.log('\n⚠️ 部分测试失败');
    process.exit(1);
  }
}

if (require.main === module) {
  runTests();
}
