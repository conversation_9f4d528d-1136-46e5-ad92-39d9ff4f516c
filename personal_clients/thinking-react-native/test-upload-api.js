/**
 * 测试上传 API 是否正常工作
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

const API_BASE_URL = 'https://local.luzeshu.cn/api';
const BULLET_HEADER = '36fdf066-9e42-11ec-b41e-525400043ced';

async function testUploadAPI() {
  console.log('🚀 开始测试上传 API...\n');

  try {
    // 1. 创建一个测试文件
    const testContent = 'Hello World! This is a test file.';
    const testFileName = 'test-upload.txt';
    fs.writeFileSync(testFileName, testContent);

    console.log('📄 创建测试文件:', testFileName);

    // 2. 准备 FormData
    const formData = new FormData();
    formData.append('file', fs.createReadStream(testFileName));

    console.log('📦 准备 FormData...');

    // 3. 发送上传请求
    console.log('⬆️ 开始上传...');
    
    const response = await axios.post(`${API_BASE_URL}/drive/upload`, formData, {
      headers: {
        ...formData.getHeaders(),
        'bullet': BULLET_HEADER,
      },
      timeout: 30000,
    });

    console.log('✅ 上传成功!');
    console.log('   状态码:', response.status);
    console.log('   响应数据:', JSON.stringify(response.data, null, 2));

    // 4. 清理测试文件
    fs.unlinkSync(testFileName);
    console.log('🗑️ 清理测试文件');

    // 5. 验证文件是否出现在列表中
    console.log('\n🔍 验证文件是否上传成功...');
    const listResponse = await axios.get(`${API_BASE_URL}/drive/files`, {
      headers: {
        'bullet': BULLET_HEADER,
      },
      params: {
        offset: 0,
        size: 20,
      },
    });

    const uploadedFile = listResponse.data.data.find(file => file.Name === testFileName);
    if (uploadedFile) {
      console.log('✅ 文件已出现在列表中:');
      console.log('   文件名:', uploadedFile.Name);
      console.log('   文件大小:', uploadedFile.Size);
      console.log('   创建时间:', uploadedFile.CreatedAt);
    } else {
      console.log('❌ 文件未在列表中找到');
    }

  } catch (error) {
    console.error('❌ 上传测试失败:', error.message);
    
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   响应数据:', error.response.data);
    } else if (error.request) {
      console.error('   请求失败，无响应');
    } else {
      console.error('   错误详情:', error);
    }

    // 清理测试文件
    try {
      fs.unlinkSync('test-upload.txt');
    } catch (e) {
      // 忽略清理错误
    }
  }
}

async function testFormDataStructure() {
  console.log('\n🔬 测试 FormData 结构...\n');

  // 模拟 React Native 的 FormData 结构
  const mockReactNativeFormData = {
    _parts: [
      ['file', {
        uri: 'file:///path/to/file.txt',
        type: 'text/plain',
        name: 'file.txt'
      }],
      ['parent_id', null]
    ]
  };

  console.log('React Native FormData 结构:');
  console.log(JSON.stringify(mockReactNativeFormData, null, 2));

  // Node.js FormData 结构
  const nodeFormData = new FormData();
  nodeFormData.append('file', 'test content', 'file.txt');
  nodeFormData.append('parent_id', '');

  console.log('\nNode.js FormData headers:');
  console.log(nodeFormData.getHeaders());
}

// 运行测试
async function runTests() {
  await testFormDataStructure();
  await testUploadAPI();
  
  console.log('\n📋 总结:');
  console.log('1. 如果上传成功，说明后端 API 正常');
  console.log('2. 如果上传失败，需要检查前端 FormData 格式');
  console.log('3. React Native 的 FormData 格式与 Node.js 不同');
  console.log('4. 可能需要调整前端的文件数据格式');
}

runTests().catch(console.error);
