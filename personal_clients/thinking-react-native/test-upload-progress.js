#!/usr/bin/env node

/**
 * 完整的上传进度测试脚本
 * 测试大文件上传进度条功能
 */

const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');
const WebSocket = require('ws');

const API_BASE_URL = 'https://local.luzeshu.cn/api';
const BULLET_HEADER = '36fdf066-9e42-11ec-b41e-525400043ced';

// 创建测试文件
function createTestFile(size = 5 * 1024 * 1024) { // 5MB
  const filename = '/tmp/test-upload-file.bin';
  const buffer = Buffer.alloc(size, 'A');
  fs.writeFileSync(filename, buffer);
  console.log(`✅ 创建测试文件: ${filename} (${size} bytes)`);
  return filename;
}

// 测试WebSocket连接
function testWebSocketConnection(taskId) {
  return new Promise((resolve, reject) => {
    const ws = new WebSocket(`wss://local.luzeshu.cn/api/drive/upload-progress?task_id=${taskId}`, {
      rejectUnauthorized: false
    });

    let connected = false;
    let progressReceived = false;

    ws.on('open', () => {
      connected = true;
      console.log(`✅ WebSocket连接成功 (task_id: ${taskId})`);
    });

    ws.on('message', (data) => {
      try {
        const progress = JSON.parse(data.toString());
        progressReceived = true;
        console.log(`📊 收到进度更新: ${progress.progress}% (${progress.uploaded_bytes}/${progress.total_bytes})`);
      } catch (error) {
        console.log(`❌ 解析进度消息失败: ${error.message}`);
      }
    });

    ws.on('error', (error) => {
      console.log(`❌ WebSocket错误: ${error.message}`);
      reject(error);
    });

    ws.on('close', () => {
      console.log(`🔌 WebSocket连接关闭`);
      resolve({ connected, progressReceived });
    });

    // 10秒后关闭连接
    setTimeout(() => {
      ws.close();
    }, 10000);
  });
}

// 测试分片上传初始化
async function testInitChunkUpload(filename, fileSize) {
  try {
    console.log('\n🚀 测试分片上传初始化...');
    
    const response = await axios.post(`${API_BASE_URL}/drive/init-chunk-upload`, {
      file_name: 'test-upload-file.bin',
      file_size: fileSize,
      parent_id: null,
      chunk_size: 1024 * 1024 // 1MB
    }, {
      headers: {
        'Content-Type': 'application/json',
        'bullet': BULLET_HEADER,
      },
      timeout: 10000,
    });

    console.log(`✅ 初始化成功 - 任务ID: ${response.data.data.ID}`);
    return response.data.data.ID;
  } catch (error) {
    console.log(`❌ 初始化失败: ${error.message}`);
    if (error.response) {
      console.log(`   状态码: ${error.response.status}`);
      console.log(`   响应: ${JSON.stringify(error.response.data)}`);
    }
    throw error;
  }
}

// 测试分片上传
async function testChunkUpload(taskId, filename, chunkIndex = 0) {
  try {
    console.log(`\n📤 测试分片上传 (chunk ${chunkIndex})...`);
    
    const formData = new FormData();
    formData.append('task_id', taskId.toString());
    formData.append('chunk_index', chunkIndex.toString());
    
    // 读取文件的一部分作为分片
    const chunkSize = 1024 * 1024; // 1MB
    const start = chunkIndex * chunkSize;
    const fileStats = fs.statSync(filename);
    const end = Math.min(start + chunkSize, fileStats.size);
    
    const chunkData = fs.createReadStream(filename, { start, end: end - 1 });
    formData.append('chunk_data', chunkData, `chunk_${chunkIndex}`);

    const response = await axios.post(`${API_BASE_URL}/drive/upload-chunk`, formData, {
      headers: {
        ...formData.getHeaders(),
        'bullet': BULLET_HEADER,
      },
      timeout: 30000,
    });

    console.log(`✅ 分片上传成功 (chunk ${chunkIndex})`);
    return response.data;
  } catch (error) {
    console.log(`❌ 分片上传失败: ${error.message}`);
    if (error.response) {
      console.log(`   状态码: ${error.response.status}`);
      console.log(`   响应: ${JSON.stringify(error.response.data)}`);
    }
    throw error;
  }
}

// 测试完成上传
async function testCompleteUpload(taskId) {
  try {
    console.log('\n🏁 测试完成上传...');
    
    const response = await axios.post(`${API_BASE_URL}/drive/complete-chunk-upload`, {
      task_id: taskId,
    }, {
      headers: {
        'Content-Type': 'application/json',
        'bullet': BULLET_HEADER,
      },
      timeout: 10000,
    });

    console.log(`✅ 上传完成 - 文件ID: ${response.data.data.ID}`);
    return response.data.data;
  } catch (error) {
    console.log(`❌ 完成上传失败: ${error.message}`);
    if (error.response) {
      console.log(`   状态码: ${error.response.status}`);
      console.log(`   响应: ${JSON.stringify(error.response.data)}`);
    }
    throw error;
  }
}

// 主测试函数
async function runCompleteTest() {
  console.log('🧪 开始完整的上传进度测试...\n');

  try {
    // 1. 创建测试文件
    const filename = createTestFile();
    const fileStats = fs.statSync(filename);
    
    // 2. 初始化分片上传
    const taskId = await testInitChunkUpload(filename, fileStats.size);
    
    // 3. 启动WebSocket监听（异步）
    const wsPromise = testWebSocketConnection(taskId);
    
    // 4. 上传分片
    const chunkSize = 1024 * 1024; // 1MB
    const totalChunks = Math.ceil(fileStats.size / chunkSize);
    
    for (let i = 0; i < totalChunks; i++) {
      await testChunkUpload(taskId, filename, i);
      // 等待一下，让进度更新有时间传播
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    // 5. 完成上传
    await testCompleteUpload(taskId);
    
    // 6. 等待WebSocket测试完成
    const wsResult = await wsPromise;
    
    // 7. 清理测试文件
    fs.unlinkSync(filename);
    console.log('🗑️  清理测试文件');
    
    // 8. 总结结果
    console.log('\n📋 测试结果总结:');
    console.log(`   WebSocket连接: ${wsResult.connected ? '✅' : '❌'}`);
    console.log(`   进度更新接收: ${wsResult.progressReceived ? '✅' : '❌'}`);
    console.log(`   分片上传: ✅`);
    console.log(`   上传完成: ✅`);
    
    if (wsResult.connected && wsResult.progressReceived) {
      console.log('\n🎉 所有测试通过！上传进度功能正常工作。');
      process.exit(0);
    } else {
      console.log('\n⚠️  部分测试失败，请检查WebSocket连接和进度推送。');
      process.exit(1);
    }
    
  } catch (error) {
    console.log(`\n💥 测试失败: ${error.message}`);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runCompleteTest();
}

module.exports = { runCompleteTest };
