#!/usr/bin/env node

/**
 * 查找所有使用React Navigation的文件
 */

const fs = require('fs');
const path = require('path');

function findFilesWithNavigation(dir, results = []) {
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // 跳过node_modules和其他不需要的目录
      if (!['node_modules', '.git', 'android', 'ios', '__tests__'].includes(file)) {
        findFilesWithNavigation(filePath, results);
      }
    } else if (file.endsWith('.tsx') || file.endsWith('.ts')) {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 检查是否使用了React Navigation相关的hooks或组件
        const navigationPatterns = [
          'useNavigation',
          'useRoute',
          'useFocusEffect',
          'useIsFocused',
          'NavigationContainer',
          '@react-navigation/native',
          '@react-navigation/stack',
          '@react-navigation/bottom-tabs',
          'StackNavigationProp',
          'BottomTabNavigationProp'
        ];
        
        const foundPatterns = [];
        for (const pattern of navigationPatterns) {
          if (content.includes(pattern)) {
            foundPatterns.push(pattern);
          }
        }
        
        if (foundPatterns.length > 0) {
          results.push({
            file: filePath,
            patterns: foundPatterns
          });
        }
      } catch (error) {
        console.log(`无法读取文件: ${filePath}`);
      }
    }
  }
  
  return results;
}

console.log('🔍 查找所有使用React Navigation的文件...\n');

const results = findFilesWithNavigation('./src');

if (results.length === 0) {
  console.log('✅ 没有找到使用React Navigation的文件');
} else {
  console.log(`❌ 找到 ${results.length} 个文件使用React Navigation:\n`);
  
  results.forEach((result, index) => {
    console.log(`${index + 1}. ${result.file}`);
    console.log(`   使用的模式: ${result.patterns.join(', ')}`);
    console.log('');
  });
  
  console.log('🔧 建议操作:');
  console.log('1. 禁用这些文件 (重命名为 .disabled)');
  console.log('2. 或者修改这些文件移除导航依赖');
}
