{"compilerOptions": {"target": "esnext", "lib": ["es2017", "dom"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "./", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/screens/*": ["src/screens/*"], "@/services/*": ["src/services/*"], "@/types/*": ["src/types/*"], "@/utils/*": ["src/utils/*"]}}, "include": ["src/**/*", "App.tsx", "index.js"], "exclude": ["node_modules", "android", "ios"]}