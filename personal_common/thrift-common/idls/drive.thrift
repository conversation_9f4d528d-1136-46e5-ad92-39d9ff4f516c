include "base.thrift"

// 文件信息结构
struct DriveFile {
  1: i64 ID
  2: string Name
  3: string Path
  4: optional i64 ParentID
  5: string FileType  // "file" or "folder"
  6: optional string MimeType
  7: i64 Size
  8: optional string Hash
  9: optional string StoragePath
  10: optional string ThumbnailPath
  11: bool IsPublic
  12: i64 DownloadCount
  13: string CreatedAt
  14: string UpdatedAt
}

// 文件分享信息
struct DriveShare {
  1: i64 ID
  2: i64 FileID
  3: string ShareCode
  4: optional string Password
  5: optional string ExpireTime
  6: i64 DownloadLimit
  7: i64 DownloadCount
  8: bool IsActive
  9: string CreatedAt
}

// 上传任务信息
struct DriveUploadTask {
  1: i64 ID
  2: string FileName
  3: i64 FileSize
  4: i64 ChunkSize
  5: i64 TotalChunks
  6: i64 UploadedChunks
  7: string UploadPath
  8: string TempDir
  9: string Status  // "pending", "uploading", "completed", "failed"
  10: optional string ErrorMessage
  11: string CreatedAt
}

// 获取文件列表请求
struct GetFileListRequest {
  1: optional i64 ParentID  // 父目录ID，NULL表示根目录
  2: i64 Offset
  3: i64 Count
  4: optional string Keyword  // 搜索关键词
  255: base.Base Base
}

// 获取文件列表响应
struct GetFileListResponse {
  1: list<DriveFile> FileList
  2: i64 Total
  3: bool HasMore
  255: base.BaseResponse BaseResp
}

// 创建文件夹请求
struct CreateFolderRequest {
  1: string Name
  2: optional i64 ParentID
  255: base.Base Base
}

// 创建文件夹响应
struct CreateFolderResponse {
  1: DriveFile Folder
  255: base.BaseResponse BaseResp
}

// 上传文件请求
struct UploadFileRequest {
  1: string FileName
  2: i64 FileSize
  3: optional i64 ParentID
  4: binary FileData  // 文件数据
  255: base.Base Base
}

// 上传文件响应
struct UploadFileResponse {
  1: DriveFile File
  255: base.BaseResponse BaseResp
}

// 分片上传初始化请求
struct InitChunkUploadRequest {
  1: string FileName
  2: i64 FileSize
  3: optional i64 ParentID
  4: i64 ChunkSize
  255: base.Base Base
}

// 分片上传初始化响应
struct InitChunkUploadResponse {
  1: DriveUploadTask UploadTask
  255: base.BaseResponse BaseResp
}

// 分片上传请求
struct UploadChunkRequest {
  1: i64 TaskID
  2: i64 ChunkIndex
  3: binary ChunkData
  255: base.Base Base
}

// 分片上传响应
struct UploadChunkResponse {
  1: DriveUploadTask UploadTask
  255: base.BaseResponse BaseResp
}

// 完成分片上传请求
struct CompleteChunkUploadRequest {
  1: i64 TaskID
  255: base.Base Base
}

// 完成分片上传响应
struct CompleteChunkUploadResponse {
  1: DriveFile File
  255: base.BaseResponse BaseResp
}

// 下载文件请求
struct DownloadFileRequest {
  1: i64 FileID
  255: base.Base Base
}

// 下载文件响应
struct DownloadFileResponse {
  1: binary FileData
  2: string FileName
  3: string MimeType
  255: base.BaseResponse BaseResp
}

// 删除文件请求
struct DeleteFileRequest {
  1: i64 FileID
  255: base.Base Base
}

// 删除文件响应
struct DeleteFileResponse {
  255: base.BaseResponse BaseResp
}

// 重命名文件请求
struct RenameFileRequest {
  1: i64 FileID
  2: string NewName
  255: base.Base Base
}

// 重命名文件响应
struct RenameFileResponse {
  1: DriveFile File
  255: base.BaseResponse BaseResp
}

// 移动文件请求
struct MoveFileRequest {
  1: i64 FileID
  2: optional i64 NewParentID
  255: base.Base Base
}

// 移动文件响应
struct MoveFileResponse {
  1: DriveFile File
  255: base.BaseResponse BaseResp
}

// 创建分享请求
struct CreateShareRequest {
  1: i64 FileID
  2: optional string Password
  3: optional string ExpireTime
  4: i64 DownloadLimit
  255: base.Base Base
}

// 创建分享响应
struct CreateShareResponse {
  1: DriveShare Share
  255: base.BaseResponse BaseResp
}

// 获取分享信息请求
struct GetShareInfoRequest {
  1: string ShareCode
  2: optional string Password
  255: base.Base Base
}

// 获取分享信息响应
struct GetShareInfoResponse {
  1: DriveShare Share
  2: DriveFile File
  255: base.BaseResponse BaseResp
}

// 通过分享下载文件请求
struct DownloadSharedFileRequest {
  1: string ShareCode
  2: optional string Password
  255: base.Base Base
}

// 通过分享下载文件响应
struct DownloadSharedFileResponse {
  1: binary FileData
  2: string FileName
  3: string MimeType
  255: base.BaseResponse BaseResp
}

// 获取存储信息请求
struct GetStorageInfoRequest {
  255: base.Base Base
}

// 获取存储信息响应
struct GetStorageInfoResponse {
  1: i64 TotalSpace
  2: i64 UsedSpace
  3: i64 FreeSpace
  4: double UsagePercent
  255: base.BaseResponse BaseResp
}

// 获取缩略图请求
struct GetThumbnailRequest {
  1: i64 FileID
  2: optional i32 Width   // 缩略图宽度，默认200
  3: optional i32 Height  // 缩略图高度，默认200
}

// 获取缩略图响应
struct GetThumbnailResponse {
  1: binary ThumbnailData
  2: string MimeType
  255: base.BaseResponse BaseResp
}

// 生成缩略图请求
struct GenerateThumbnailRequest {
  1: i64 FileID
  2: optional i32 Width   // 缩略图宽度，默认200
  3: optional i32 Height  // 缩略图高度，默认200
}

// 生成缩略图响应
struct GenerateThumbnailResponse {
  1: string ThumbnailPath
  255: base.BaseResponse BaseResp
}

// 上传进度回调请求
struct UploadProgressRequest {
  1: i64 TaskID
  2: i64 UploadedBytes
  3: i64 TotalBytes
  4: double Progress  // 0-100的进度百分比
  255: base.Base Base
}

// 上传进度回调响应
struct UploadProgressResponse {
  255: base.BaseResponse BaseResp
}

service drive {
  // 文件管理
  GetFileListResponse GetFileList(1: GetFileListRequest req),
  CreateFolderResponse CreateFolder(1: CreateFolderRequest req),
  UploadFileResponse UploadFile(1: UploadFileRequest req),
  DownloadFileResponse DownloadFile(1: DownloadFileRequest req),
  DeleteFileResponse DeleteFile(1: DeleteFileRequest req),
  RenameFileResponse RenameFile(1: RenameFileRequest req),
  MoveFileResponse MoveFile(1: MoveFileRequest req),
  
  // 分片上传
  InitChunkUploadResponse InitChunkUpload(1: InitChunkUploadRequest req),
  UploadChunkResponse UploadChunk(1: UploadChunkRequest req),
  CompleteChunkUploadResponse CompleteChunkUpload(1: CompleteChunkUploadRequest req),
  
  // 文件分享
  CreateShareResponse CreateShare(1: CreateShareRequest req),
  GetShareInfoResponse GetShareInfo(1: GetShareInfoRequest req),
  DownloadSharedFileResponse DownloadSharedFile(1: DownloadSharedFileRequest req),
  
  // 存储信息
  GetStorageInfoResponse GetStorageInfo(1: GetStorageInfoRequest req),

  // 缩略图
  GetThumbnailResponse GetThumbnail(1: GetThumbnailRequest req),
  GenerateThumbnailResponse GenerateThumbnail(1: GenerateThumbnailRequest req),
  
  // 上传进度回调
  UploadProgressResponse UploadProgress(1: UploadProgressRequest req),
}
