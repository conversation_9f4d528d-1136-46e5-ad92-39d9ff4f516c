namespace go favorites

include "./base.thrift"

struct CallbackDelayFetchRequest {
  1: string SyncMetaTable (go.tag='json:"sync_meta_table"')
  2: string CloudDbTunnel (go.tag='json:"cloud_db_tunnel"')
  255: base.Base Base
}

struct CallbackDelayFetchResponse {
  1: map<string, string> ResultInfo
  255: base.BaseResponse BaseResp
}

struct CallbackSyncDataRequest {
  1: string SyncMetaTable (go.tag='json:"sync_meta_table"')
  2: string CloudDbTunnel (go.tag='json:"cloud_db_tunnel"')
  3: string SyncTunnel (go.tag='json:"sync_tunnel"')
  255: base.Base Base
}

struct CallbackSyncDataResponse {
  1: map<string, string> ResultInfo
  255: base.BaseResponse BaseResp
}

struct AddToFavoritesRequest {
  1: string CollectType (go.tag='json:"collect_type"')
  2: string Origin      (go.tag='json:"origin"')
  3: string Title       (go.tag='json:"title"')
  4: string SavePath    (go.tag='json:"save_path"')
  5: string Remark      (go.tag='json:"remark"')
  255: base.Base Base
}

struct AddToFavoritesResponse {
  255: base.BaseResponse BaseResp
}

struct GetFavoritesPageRequest {
  1: i64 Offset
  2: i64 Count
  3: optional string Keyword
  255: base.Base Base
}

struct FavoritesItem {
  1: i64 Id                   (go.tag='json:"id"')
  2: string Origin            (go.tag='json:"origin"')
  3: optional string Title    (go.tag='json:"title"')
  4: optional string Author   (go.tag='json:"author"')
  5: string State             (go.tag='json:"state"')
  6: optional string SavePath (go.tag='json:"save_path"')
  7: optional i64 CollectType (go.tag='json:"collect_type"')
  8: string CreateTime        (go.tag='json:"create_time"')
}

struct GetFavoritesPageResponse {
  1: list<FavoritesItem> FavoritesList
  2: bool HasMore
  3: i64 Total
  255: base.BaseResponse BaseResp
}

struct GetDiskInfoRequest {
  1: i64 Depth   (go.tag='json:"depth"')
  2: string Root (go.tag='json:"root"')
}

struct GetDiskInfoResponse {
  1: string DiskLabel
  2: string DiskUsage
  3: i64 DiskProgress
  4: string FsTree
  255: base.BaseResponse BaseResp
}

struct DeleteFavoriteRequest {
  1: i64 FavoriteId
  255: base.Base Base
}

struct DeleteFavoriteResponse {
  255: base.BaseResponse BaseResp
}



service favorites {
  CallbackDelayFetchResponse CallbackDelayFetch(1: CallbackDelayFetchRequest req),
  CallbackSyncDataResponse CallbackSyncData(1: CallbackSyncDataRequest req),
  AddToFavoritesResponse AddToFavorites(1: AddToFavoritesRequest req),
  GetFavoritesPageResponse GetFavoritesPage(1: GetFavoritesPageRequest req),
  GetDiskInfoResponse GetDiskInfo(1: GetDiskInfoRequest req),
  DeleteFavoriteResponse DeleteFavorite(1: DeleteFavoriteRequest req),

}
