namespace go gateway

include "./base.thrift"

// 上传进度通知请求
struct UploadProgressNotifyRequest {
  1: i64 TaskID
  2: i64 UploadedBytes
  3: i64 TotalBytes
  4: double Progress  // 0-100的进度百分比
  255: base.Base Base
}

// 上传进度通知响应
struct UploadProgressNotifyResponse {
  255: base.BaseResponse BaseResp
}

service gateway {
  // 上传进度通知
  UploadProgressNotifyResponse UploadProgressNotify(1: UploadProgressNotifyRequest req),
}
