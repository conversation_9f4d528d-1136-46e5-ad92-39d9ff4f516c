namespace go notes

include "./base.thrift"

struct AddNoteRequest {
  1: string Title       (go.tag='json:"title"')
  2: string Content     (go.tag='json:"content"')
  3: optional string NoteType (go.tag='json:"note_type"')
  4: optional string Category (go.tag='json:"category"')
  5: optional string Tags     (go.tag='json:"tags"')
  255: base.Base Base
}

struct AddNoteResponse {
  1: i64 NoteId
  255: base.BaseResponse BaseResp
}

struct GetNotesPageRequest {
  1: i64 Offset
  2: i64 Count
  3: optional string Keyword
  4: optional string Category
  255: base.Base Base
}

struct NoteItem {
  1: i64 Id                   (go.tag='json:"id"')
  2: string Title             (go.tag='json:"title"')
  3: string Content           (go.tag='json:"content"')
  4: string NoteType          (go.tag='json:"note_type"')
  5: optional string Category (go.tag='json:"category"')
  6: optional string Tags     (go.tag='json:"tags"')
  7: string CreateTime        (go.tag='json:"create_time"')
  8: string UpdateTime        (go.tag='json:"update_time"')
}

struct GetNotesPageResponse {
  1: list<NoteItem> NotesList
  2: bool HasMore
  3: i64 Total
  255: base.BaseResponse BaseResp
}

struct UpdateNoteRequest {
  1: i64 NoteId
  2: optional string Title    (go.tag='json:"title"')
  3: optional string Content  (go.tag='json:"content"')
  4: optional string NoteType (go.tag='json:"note_type"')
  5: optional string Category (go.tag='json:"category"')
  6: optional string Tags     (go.tag='json:"tags"')
  255: base.Base Base
}

struct UpdateNoteResponse {
  255: base.BaseResponse BaseResp
}

struct DeleteNoteRequest {
  1: i64 NoteId
  255: base.Base Base
}

struct DeleteNoteResponse {
  255: base.BaseResponse BaseResp
}

struct GetNoteRequest {
  1: i64 NoteId
  255: base.Base Base
}

struct GetNoteResponse {
  1: NoteItem Note
  255: base.BaseResponse BaseResp
}

service notes {
  AddNoteResponse AddNote(1: AddNoteRequest req),
  GetNotesPageResponse GetNotesPage(1: GetNotesPageRequest req),
  UpdateNoteResponse UpdateNote(1: UpdateNoteRequest req),
  DeleteNoteResponse DeleteNote(1: DeleteNoteRequest req),
  GetNoteResponse GetNote(1: GetNoteRequest req),
}
