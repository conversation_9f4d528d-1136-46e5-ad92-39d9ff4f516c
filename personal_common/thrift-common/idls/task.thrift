namespace go task

include "./base.thrift"

struct AddTaskRequest {
  1: string Title (go.tag='json:"title"')
  2: string Parent (go.tag='json:"parent"')
  3: optional string Priority (go.tag='json:"priority"')
  4: optional string Mark (go.tag='json:"mark"')
  5: optional string ForceID (go.tag='json:"forceID"')
  6: optional string ForceStatus (go.tag='json:"forceStatus"')
  7: optional string Description (go.tag='json:"description"')
  8: optional string DDL (go.tag='json:"ddl"')
  255: base.Base Base
}

struct AddTaskResponse {
  1: TaskItem TreeRoot (go.tag='json:"treeRoot"')
  2: list<TaskMarkItem> Marks (go.tag='json:"marks"')
  3: list<list<string>> Timelines (go.tag='json:"timelines"')
  255: base.BaseResponse BaseResp
}

struct EditTaskRequest {
  1: string IDLabel (go.tag='json:"taskIDLabel"')
  2: optional string Title (go.tag='json:"title"')
  3: optional string Description (go.tag='json:"description"')
  4: optional string DDL (go.tag='json:"ddl"')
  255: base.Base Base
}

struct EditTaskResponse {
  1: TaskItem TreeRoot (go.tag='json:"treeRoot"')
  2: list<TaskMarkItem> Marks (go.tag='json:"marks"')
  3: list<list<string>> Timelines (go.tag='json:"timelines"')
  255: base.BaseResponse BaseResp
}

struct TaskMarkItem {
  1: string ID(go.tag='json:"id"')
  2: string Content(go.tag='json:"content"')
  3: string MarkType (go.tag='json:"markType"')
  4: optional string DocLink(go.tag='json:"docLink"')
  5: string Time (go.tag='json:"time"')
}

struct TaskActionRequest {
  1: optional string IDLabel (go.tag='json:"taskIDLabel"')
  2: string Action (go.tag='json:"action"')
  3: string MustLeaf(go.tag='json:"mustLeaf"')
  4: optional string StartTime (go.tag='json:"startTime"')
  5: optional string EndTime (go.tag='json:"endTime"')
  6: optional string Content (go.tag='json:"content"')
  7: optional string MarkTime (go.tag='json:"markTime"')
  8: optional string Force (go.tag='json:"force"')
  9: optional string LabelName (go.tag='json:"labelName"')
  255: base.Base Base
}

struct TaskActionResponse {
  1: TaskItem TreeRoot (go.tag='json:"treeRoot"')
  2: list<TaskMarkItem> Marks (go.tag='json:"marks"')
  3: list<list<string>> Timelines (go.tag='json:"timelines"')
  255: base.BaseResponse BaseResp
}

struct TaskMvRequest {
  1: string SrcTask (go.tag='json:"srcTask"')
  2: optional string Parent(go.tag='json:"parent"')
  3: optional string DstTaskID(go.tag='json:"dstTaskID"')
  255: base.Base Base
}

struct TaskMvResponse {
  1: TaskItem TreeRoot (go.tag='json:"treeRoot"')
  2: list<TaskMarkItem> Marks (go.tag='json:"marks"')
  3: list<list<string>> Timelines (go.tag='json:"timelines"')
  255: base.BaseResponse BaseResp
}

struct GetLastRequest {
  1: optional string Number(go.tag='json:"number" form:"number"')
  255: base.Base Base
}

struct GetLastResponse {
  1: list<TaskItem> Tasks (go.tag='json:"tasks"')
  255: base.BaseResponse BaseResp
}

struct PickTaskRequest {
  1: optional string IDLabel (go.tag='json:"taskIDLabel"')
  2: optional string Position(go.tag='json:"position"')
  255: base.Base Base
}

struct PickTaskResponse {
  1: list<TaskItem> Tasks (go.tag='json:"tasks"')
  255: base.BaseResponse BaseResp
}

struct PickDelRequest {
  1: string Position(go.tag='json:"position"')
  255: base.Base Base
}

struct PickDelResponse {
  1: list<TaskItem> Tasks (go.tag='json:"tasks"')
  255: base.BaseResponse BaseResp
}

struct TaskItem {
  1: string ID (go.tag='json:"id"')
  2: string Title    (go.tag='json:"title"')
  3: string Description (go.tag='json:"description"')
  4: string Parent (go.tag='json:"parent"')
  5: string Status (go.tag='json:"status"')
  6: i64 Priority (go.tag='json:"priority"')
  7: i64 Level (go.tag='json:"level"')
  8: string Category (go.tag='json:"category"')
  9: string DDL (go.tag='json:"ddl"')
  10: string CreateTime (go.tag='json:"createTime"')
  11: list<TaskItem> Subs (go.tag='json:"subs"')
  12: i64 TimeInvested (go.tag='json:"timeInvested"')
  13: list<i64> TimeSpan (go.tag='json:"timeSpan"')
  14: optional string Label (go.tag='json:"label"')
}

struct ListTaskRequest {
  1: optional string Root (go.tag='json:"root" form:"root"')
  2: optional string Status (go.tag='json:"status" form:"status"')
  3: optional string Priority(go.tag='json:"priority" form:"priority"')
  4: optional string DDL(go.tag='json:"ddl" form:"ddl"')
  5: optional string Grep (go.tag='json:"grep" form:"grep"')
  6: optional string DefaultType (go.tag='json:"defaultType" form:"defaultType"')
  7: optional string ShowTime (go.tag='json:"showTime" form:"showTime"')
  8: optional string FilterTime (go.tag='json:"filterTime" form:"filterTime"')
  9: optional string InvestedTimeTimes (go.tag='json:"investedTimeTimes" form:"investedTimeTimes"')
  10: optional string Level (go.tag='json:"level" form:"level"')
  255: base.Base Base
}

struct ListTaskResponse {
  1: TaskItem TreeRoot (go.tag='json:"treeRoot"')
  2: list<TaskMarkItem> Marks (go.tag='json:"marks"')
  3: list<list<string>> Timelines (go.tag='json:"timelines"')
  4: i64 MaxLevel (go.tag='json:"maxLevel"')
  255: base.BaseResponse BaseResp
}

struct GetTaskPageRequest {
  1: i64 Offset
  2: i64 Count
  255: base.Base Base
}

struct GetTaskPageResponse {
  1: list<TaskItem> TaskList
  2: bool HasMore
  3: i64 Total
  255: base.BaseResponse BaseResp
}

struct SearchTaskRequest {
  1: string Keyword (go.tag='json:"keyword" form:"keyword"')
  2: optional string InMarks (go.tag='json:"inMarks" form:"inMarks"')
  3: optional string MarkType (go.tag='json:"markType" form:"markType"')
  4: optional string Limit (go.tag='json:"limit" form:"limit"')
  255: base.Base Base
}

struct SearchTaskResponse {
  1: list<TaskItem> Tasks (go.tag='json:"tasks"')
  255: base.BaseResponse BaseResp
}

service task {
  AddTaskResponse AddTask(1: AddTaskRequest req),
  EditTaskResponse EditTask(1: EditTaskRequest req),
  ListTaskResponse ListTask(1: ListTaskRequest req),
  GetLastResponse GetLast(1: GetLastRequest req),
  TaskActionResponse TaskAction(1: TaskActionRequest req),
  TaskMvResponse TaskMv(1: TaskMvRequest req),
  GetTaskPageResponse GetTaskPage(1: GetTaskPageRequest req),
  PickTaskResponse PickTask(1: PickTaskRequest req),
  PickDelResponse PickDel(1: PickDelRequest req),
  SearchTaskResponse SearchTask(1: SearchTaskRequest req),
}
