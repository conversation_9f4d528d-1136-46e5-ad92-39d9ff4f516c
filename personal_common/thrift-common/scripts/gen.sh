#!/bin/bash

THRIFT_BIN=thrift

if ! [[ $(command -v thrift) ]]; then
  THRIFT_BIN=/tmp/thrift-0.16.0/thrift-0.16.0/compiler/cpp/thrift 
  if [[ ! -f ${THRIFT_BIN} ]]; then
      echo "*** Error: Not Found ${THRIFT_BIN}!"
      exit 1
  fi
fi

${THRIFT_BIN} -r --gen py idls/bilibili.thrift
find gen-py/ -type f -name "*.py" -exec sed -i 's/import base.ttypes/import importlib\nbase = importlib.import_module("thrift-common.gen-py.base")\nttypes = importlib.import_module("thrift-common.gen-py.base.ttypes")/g' {} \;
${THRIFT_BIN} -r --gen go idls/bilibili.thrift
${THRIFT_BIN} -r --gen go idls/favorites.thrift
${THRIFT_BIN} -r --gen go idls/task.thrift
${THRIFT_BIN} -r --gen go idls/notes.thrift
${THRIFT_BIN} -r --gen go idls/drive.thrift
${THRIFT_BIN} -r --gen go idls/gateway.thrift

find gen-go/ -type f -name "*.go" -exec sed -i 's/"base"/"thrift-common\/gen-go\/base"/g' {} \;
rm -rf thrift-common-go/gen-go && mv gen-go thrift-common-go/
# rm -rf ~/go/src/thrift-common && cp -R thrift-common-go ~/go/src/thrift-common
rm -rf /usr/local/go/src/thrift-common && cp -R thrift-common-go /usr/local/go/src/thrift-common
rm -rf thrift-common-py/gen-py && mv gen-py thrift-common-py/

# 可能是/usr/local/lib/python3.7/dist-packages，也可能是python3.8，使用python_lib_path替代
python_lib_path=$(python3 -c "import pip; print(pip.__path__[0].rstrip('/pip'))")
echo "Gen to ${python_lib_path}/thrift-common"
# rm -rf ~/.local/lib/python3.7/site-packages/thrift-common && cp -R thrift-common-py ~/.local/lib/python3.7/site-packages/thrift-common
# rm -rf /usr/local/lib/python3.7/dist-packages/thrift-common && cp -R thrift-common-py /usr/local/lib/python3.7/dist-packages/thrift-common
rm -rf ${python_lib_path}/thrift-common && cp -R thrift-common-py ${python_lib_path}/thrift-common
