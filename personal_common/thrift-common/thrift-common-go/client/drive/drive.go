package drive

import (
	"os"
	"thrift-common/gen-go/drive"

	"github.com/apache/thrift/lib/go/thrift"
	wErr "github.com/pkg/errors"
)

func GetDriveClient() (*drive.DriveClient, error) {
	addr := ":18084"
	if os.Getenv("ENV") == "deploy" {
		addr = "drive-service:18084"
	}
	var transport thrift.TTransport
	var err error
	transport, err = thrift.NewTSocket(addr)
	if err != nil {
		return nil, wErr.Wrap(err, "")
	}
	var protocolFactory thrift.TProtocolFactory
	protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
	var transportFactory thrift.TTransportFactory
	transportFactory = thrift.NewTTransportFactory()
	transport, err = transportFactory.GetTransport(transport)
	if err != nil {
		return nil, wErr.Wrap(err, "")
	}
	if err := transport.Open(); err != nil {
		return nil, wErr.Wrap(err, "")
	}
	client := drive.NewDriveClientFactory(transport, protocolFactory)
	return client, nil
}
