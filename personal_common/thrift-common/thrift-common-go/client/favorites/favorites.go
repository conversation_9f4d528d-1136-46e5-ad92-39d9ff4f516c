package favorites

import (
	"os"
	"thrift-common/gen-go/favorites"

	"github.com/apache/thrift/lib/go/thrift"
	wErr "github.com/pkg/errors"
)

func GetFavoritesClient() (*favorites.FavoritesClient, error) {
	addr := ":18081"
	if os.Getenv("ENV") == "deploy" {
		addr = "favorites-service:18081"
	}
	var transport thrift.TTransport
	var err error
	transport, err = thrift.NewTSocket(addr)
	if err != nil {
		return nil, wErr.Wrap(err, "")
	}
	var protocolFactory thrift.TProtocolFactory
	protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
	var transportFactory thrift.TTransportFactory
	transportFactory = thrift.NewTTransportFactory()
	transport, err = transportFactory.GetTransport(transport)
	if err != nil {
		return nil, wErr.Wrap(err, "")
	}
	if err := transport.Open(); err != nil {
		return nil, wErr.Wrap(err, "")
	}

	iprot := protocolFactory.GetProtocol(transport)
	oprot := protocolFactory.GetProtocol(transport)
	client := favorites.NewFavoritesClient(thrift.NewTStandardClient(iprot, oprot))
	return client, nil
}
