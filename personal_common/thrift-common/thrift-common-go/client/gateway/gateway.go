package gateway

import (
	"os"
	"thrift-common/gen-go/gateway"

	"github.com/apache/thrift/lib/go/thrift"
	wErr "github.com/pkg/errors"
)

func GetGatewayClient() (*gateway.GatewayClient, error) {
	addr := ":16135"
	if os.Getenv("ENV") == "deploy" {
		addr = "gateway:16135"
	}
	var transport thrift.TTransport
	var err error
	transport, err = thrift.NewTSocket(addr)
	if err != nil {
		return nil, wErr.Wrap(err, "")
	}
	var protocolFactory thrift.TProtocolFactory
	protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
	var transportFactory thrift.TTransportFactory
	transportFactory = thrift.NewTTransportFactory()
	transport, err = transportFactory.GetTransport(transport)
	if err != nil {
		return nil, wErr.Wrap(err, "")
	}
	if err := transport.Open(); err != nil {
		return nil, wErr.Wrap(err, "")
	}
	client := gateway.NewGatewayClientFactory(transport, protocolFactory)
	return client, nil
}
