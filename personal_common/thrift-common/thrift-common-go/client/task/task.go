package task

import (
	"os"
	"thrift-common/gen-go/task"

	"github.com/apache/thrift/lib/go/thrift"
	wErr "github.com/pkg/errors"
)

func GetTaskClient() (*task.TaskClient, error) {
	addr := ":18082"
	if os.Getenv("ENV") == "deploy" {
		addr = "task-service:18082"
	}
	var transport thrift.TTransport
	var err error
	transport, err = thrift.NewTSocket(addr)
	if err != nil {
		return nil, wErr.Wrap(err, "")
	}
	var protocolFactory thrift.TProtocolFactory
	protocolFactory = thrift.NewTBinaryProtocolFactoryDefault()
	var transportFactory thrift.TTransportFactory
	transportFactory = thrift.NewTTransportFactory()
	transport, err = transportFactory.GetTransport(transport)
	if err != nil {
		return nil, wErr.Wrap(err, "")
	}
	if err := transport.Open(); err != nil {
		return nil, wErr.Wrap(err, "")
	}

	iprot := protocolFactory.GetProtocol(transport)
	oprot := protocolFactory.GetProtocol(transport)
	client := task.NewTaskClient(thrift.NewTStandardClient(iprot, oprot))
	return client, nil
}
