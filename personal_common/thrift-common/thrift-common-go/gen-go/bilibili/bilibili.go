// Code generated by Thrift Compiler (0.16.0). DO NOT EDIT.

package bilibili

import (
	"bytes"
	"context"
	"fmt"
	"time"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"thrift-common/gen-go/base"

)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

var _ = base.GoUnusedProtection__
// Attributes:
//  - URL
//  - IsMultiple
//  - SavePath
//  - Base
type PullVideoRequest struct {
  URL string `thrift:"Url,1" db:"Url" json:"Url"`
  IsMultiple bool `thrift:"IsMultiple,2" db:"IsMultiple" json:"IsMultiple"`
  SavePath string `thrift:"SavePath,3" db:"SavePath" json:"SavePath"`
  // unused fields # 4 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewPullVideoRequest() *PullVideoRequest {
  return &PullVideoRequest{}
}


func (p *PullVideoRequest) GetURL() string {
  return p.URL
}

func (p *PullVideoRequest) GetIsMultiple() bool {
  return p.IsMultiple
}

func (p *PullVideoRequest) GetSavePath() string {
  return p.SavePath
}
var PullVideoRequest_Base_DEFAULT *base.Base
func (p *PullVideoRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return PullVideoRequest_Base_DEFAULT
  }
return p.Base
}
func (p *PullVideoRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *PullVideoRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *PullVideoRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.URL = v
}
  return nil
}

func (p *PullVideoRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.IsMultiple = v
}
  return nil
}

func (p *PullVideoRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.SavePath = v
}
  return nil
}

func (p *PullVideoRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *PullVideoRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "PullVideoRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *PullVideoRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Url", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Url: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.URL)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Url (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Url: ", p), err) }
  return err
}

func (p *PullVideoRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "IsMultiple", thrift.BOOL, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:IsMultiple: ", p), err) }
  if err := oprot.WriteBool(ctx, bool(p.IsMultiple)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.IsMultiple (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:IsMultiple: ", p), err) }
  return err
}

func (p *PullVideoRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "SavePath", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:SavePath: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.SavePath)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.SavePath (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:SavePath: ", p), err) }
  return err
}

func (p *PullVideoRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *PullVideoRequest) Equals(other *PullVideoRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.URL != other.URL { return false }
  if p.IsMultiple != other.IsMultiple { return false }
  if p.SavePath != other.SavePath { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *PullVideoRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("PullVideoRequest(%+v)", *p)
}

// Attributes:
//  - Duration
//  - FilesNum
//  - Title
//  - FilesStatus
//  - BaseResp
type PullVideoResponse struct {
  Duration int64 `thrift:"duration,1" db:"duration" json:"duration"`
  FilesNum *int64 `thrift:"files_num,2" db:"files_num" json:"files_num,omitempty"`
  Title *string `thrift:"title,3" db:"title" json:"title,omitempty"`
  FilesStatus *string `thrift:"files_status,4" db:"files_status" json:"files_status,omitempty"`
  // unused fields # 5 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewPullVideoResponse() *PullVideoResponse {
  return &PullVideoResponse{}
}


func (p *PullVideoResponse) GetDuration() int64 {
  return p.Duration
}
var PullVideoResponse_FilesNum_DEFAULT int64
func (p *PullVideoResponse) GetFilesNum() int64 {
  if !p.IsSetFilesNum() {
    return PullVideoResponse_FilesNum_DEFAULT
  }
return *p.FilesNum
}
var PullVideoResponse_Title_DEFAULT string
func (p *PullVideoResponse) GetTitle() string {
  if !p.IsSetTitle() {
    return PullVideoResponse_Title_DEFAULT
  }
return *p.Title
}
var PullVideoResponse_FilesStatus_DEFAULT string
func (p *PullVideoResponse) GetFilesStatus() string {
  if !p.IsSetFilesStatus() {
    return PullVideoResponse_FilesStatus_DEFAULT
  }
return *p.FilesStatus
}
var PullVideoResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *PullVideoResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return PullVideoResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *PullVideoResponse) IsSetFilesNum() bool {
  return p.FilesNum != nil
}

func (p *PullVideoResponse) IsSetTitle() bool {
  return p.Title != nil
}

func (p *PullVideoResponse) IsSetFilesStatus() bool {
  return p.FilesStatus != nil
}

func (p *PullVideoResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *PullVideoResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *PullVideoResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Duration = v
}
  return nil
}

func (p *PullVideoResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.FilesNum = &v
}
  return nil
}

func (p *PullVideoResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Title = &v
}
  return nil
}

func (p *PullVideoResponse)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.FilesStatus = &v
}
  return nil
}

func (p *PullVideoResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *PullVideoResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "PullVideoResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *PullVideoResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "duration", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:duration: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Duration)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.duration (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:duration: ", p), err) }
  return err
}

func (p *PullVideoResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetFilesNum() {
    if err := oprot.WriteFieldBegin(ctx, "files_num", thrift.I64, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:files_num: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.FilesNum)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.files_num (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:files_num: ", p), err) }
  }
  return err
}

func (p *PullVideoResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetTitle() {
    if err := oprot.WriteFieldBegin(ctx, "title", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:title: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Title)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.title (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:title: ", p), err) }
  }
  return err
}

func (p *PullVideoResponse) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetFilesStatus() {
    if err := oprot.WriteFieldBegin(ctx, "files_status", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:files_status: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.FilesStatus)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.files_status (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:files_status: ", p), err) }
  }
  return err
}

func (p *PullVideoResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *PullVideoResponse) Equals(other *PullVideoResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Duration != other.Duration { return false }
  if p.FilesNum != other.FilesNum {
    if p.FilesNum == nil || other.FilesNum == nil {
      return false
    }
    if (*p.FilesNum) != (*other.FilesNum) { return false }
  }
  if p.Title != other.Title {
    if p.Title == nil || other.Title == nil {
      return false
    }
    if (*p.Title) != (*other.Title) { return false }
  }
  if p.FilesStatus != other.FilesStatus {
    if p.FilesStatus == nil || other.FilesStatus == nil {
      return false
    }
    if (*p.FilesStatus) != (*other.FilesStatus) { return false }
  }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *PullVideoResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("PullVideoResponse(%+v)", *p)
}

type Bilibili interface {
  // Parameters:
  //  - Req
  PullVideo(ctx context.Context, req *PullVideoRequest) (_r *PullVideoResponse, _err error)
}

type BilibiliClient struct {
  c thrift.TClient
  meta thrift.ResponseMeta
}

func NewBilibiliClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *BilibiliClient {
  return &BilibiliClient{
    c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
  }
}

func NewBilibiliClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *BilibiliClient {
  return &BilibiliClient{
    c: thrift.NewTStandardClient(iprot, oprot),
  }
}

func NewBilibiliClient(c thrift.TClient) *BilibiliClient {
  return &BilibiliClient{
    c: c,
  }
}

func (p *BilibiliClient) Client_() thrift.TClient {
  return p.c
}

func (p *BilibiliClient) LastResponseMeta_() thrift.ResponseMeta {
  return p.meta
}

func (p *BilibiliClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
  p.meta = meta
}

// Parameters:
//  - Req
func (p *BilibiliClient) PullVideo(ctx context.Context, req *PullVideoRequest) (_r *PullVideoResponse, _err error) {
  var _args0 BilibiliPullVideoArgs
  _args0.Req = req
  var _result2 BilibiliPullVideoResult
  var _meta1 thrift.ResponseMeta
  _meta1, _err = p.Client_().Call(ctx, "PullVideo", &_args0, &_result2)
  p.SetLastResponseMeta_(_meta1)
  if _err != nil {
    return
  }
  if _ret3 := _result2.GetSuccess(); _ret3 != nil {
    return _ret3, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "PullVideo failed: unknown result")
}

type BilibiliProcessor struct {
  processorMap map[string]thrift.TProcessorFunction
  handler Bilibili
}

func (p *BilibiliProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
  p.processorMap[key] = processor
}

func (p *BilibiliProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
  processor, ok = p.processorMap[key]
  return processor, ok
}

func (p *BilibiliProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
  return p.processorMap
}

func NewBilibiliProcessor(handler Bilibili) *BilibiliProcessor {

  self4 := &BilibiliProcessor{handler:handler, processorMap:make(map[string]thrift.TProcessorFunction)}
  self4.processorMap["PullVideo"] = &bilibiliProcessorPullVideo{handler:handler}
return self4
}

func (p *BilibiliProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
  if err2 != nil { return false, thrift.WrapTException(err2) }
  if processor, ok := p.GetProcessorFunction(name); ok {
    return processor.Process(ctx, seqId, iprot, oprot)
  }
  iprot.Skip(ctx, thrift.STRUCT)
  iprot.ReadMessageEnd(ctx)
  x5 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function " + name)
  oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
  x5.Write(ctx, oprot)
  oprot.WriteMessageEnd(ctx)
  oprot.Flush(ctx)
  return false, x5

}

type bilibiliProcessorPullVideo struct {
  handler Bilibili
}

func (p *bilibiliProcessorPullVideo) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := BilibiliPullVideoArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "PullVideo", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := BilibiliPullVideoResult{}
  var retval *PullVideoResponse
  if retval, err2 = p.handler.PullVideo(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing PullVideo: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "PullVideo", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "PullVideo", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}


// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//  - Req
type BilibiliPullVideoArgs struct {
  Req *PullVideoRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewBilibiliPullVideoArgs() *BilibiliPullVideoArgs {
  return &BilibiliPullVideoArgs{}
}

var BilibiliPullVideoArgs_Req_DEFAULT *PullVideoRequest
func (p *BilibiliPullVideoArgs) GetReq() *PullVideoRequest {
  if !p.IsSetReq() {
    return BilibiliPullVideoArgs_Req_DEFAULT
  }
return p.Req
}
func (p *BilibiliPullVideoArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *BilibiliPullVideoArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *BilibiliPullVideoArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &PullVideoRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *BilibiliPullVideoArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "PullVideo_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *BilibiliPullVideoArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *BilibiliPullVideoArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("BilibiliPullVideoArgs(%+v)", *p)
}

// Attributes:
//  - Success
type BilibiliPullVideoResult struct {
  Success *PullVideoResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewBilibiliPullVideoResult() *BilibiliPullVideoResult {
  return &BilibiliPullVideoResult{}
}

var BilibiliPullVideoResult_Success_DEFAULT *PullVideoResponse
func (p *BilibiliPullVideoResult) GetSuccess() *PullVideoResponse {
  if !p.IsSetSuccess() {
    return BilibiliPullVideoResult_Success_DEFAULT
  }
return p.Success
}
func (p *BilibiliPullVideoResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *BilibiliPullVideoResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *BilibiliPullVideoResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &PullVideoResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *BilibiliPullVideoResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "PullVideo_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *BilibiliPullVideoResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *BilibiliPullVideoResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("BilibiliPullVideoResult(%+v)", *p)
}


