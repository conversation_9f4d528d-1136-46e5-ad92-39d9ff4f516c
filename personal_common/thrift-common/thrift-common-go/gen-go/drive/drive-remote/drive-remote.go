// Code generated by Thrift Compiler (0.16.0). DO NOT EDIT.

package main

import (
	"context"
	"flag"
	"fmt"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"thrift-common/gen-go/base"
	"drive"
)

var _ = base.GoUnusedProtection__
var _ = drive.GoUnusedProtection__

func Usage() {
  fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
  flag.PrintDefaults()
  fmt.Fprintln(os.Stderr, "\nFunctions:")
  fmt.Fprintln(os.Stderr, "  GetFileListResponse GetFileList(GetFileListRequest req)")
  fmt.Fprintln(os.St<PERSON>r, "  CreateFolderResponse CreateFolder(CreateFolderRequest req)")
  fmt.Fprintln(os.Stderr, "  UploadFileResponse UploadFile(UploadFileRequest req)")
  fmt.Fprintln(os.Stderr, "  DownloadFileResponse DownloadFile(DownloadFileRequest req)")
  fmt.Fprintln(os.Stderr, "  DeleteFileResponse DeleteFile(DeleteFileRequest req)")
  fmt.Fprintln(os.Stderr, "  RenameFileResponse RenameFile(RenameFileRequest req)")
  fmt.Fprintln(os.Stderr, "  MoveFileResponse MoveFile(MoveFileRequest req)")
  fmt.Fprintln(os.Stderr, "  InitChunkUploadResponse InitChunkUpload(InitChunkUploadRequest req)")
  fmt.Fprintln(os.Stderr, "  UploadChunkResponse UploadChunk(UploadChunkRequest req)")
  fmt.Fprintln(os.Stderr, "  CompleteChunkUploadResponse CompleteChunkUpload(CompleteChunkUploadRequest req)")
  fmt.Fprintln(os.Stderr, "  CreateShareResponse CreateShare(CreateShareRequest req)")
  fmt.Fprintln(os.Stderr, "  GetShareInfoResponse GetShareInfo(GetShareInfoRequest req)")
  fmt.Fprintln(os.Stderr, "  DownloadSharedFileResponse DownloadSharedFile(DownloadSharedFileRequest req)")
  fmt.Fprintln(os.Stderr, "  GetStorageInfoResponse GetStorageInfo(GetStorageInfoRequest req)")
  fmt.Fprintln(os.Stderr, "  GetThumbnailResponse GetThumbnail(GetThumbnailRequest req)")
  fmt.Fprintln(os.Stderr, "  GenerateThumbnailResponse GenerateThumbnail(GenerateThumbnailRequest req)")
  fmt.Fprintln(os.Stderr, "  UploadProgressResponse UploadProgress(UploadProgressRequest req)")
  fmt.Fprintln(os.Stderr)
  os.Exit(0)
}

type httpHeaders map[string]string

func (h httpHeaders) String() string {
  var m map[string]string = h
  return fmt.Sprintf("%s", m)
}

func (h httpHeaders) Set(value string) error {
  parts := strings.Split(value, ": ")
  if len(parts) != 2 {
    return fmt.Errorf("header should be of format 'Key: Value'")
  }
  h[parts[0]] = parts[1]
  return nil
}

func main() {
  flag.Usage = Usage
  var host string
  var port int
  var protocol string
  var urlString string
  var framed bool
  var useHttp bool
  headers := make(httpHeaders)
  var parsedUrl *url.URL
  var trans thrift.TTransport
  _ = strconv.Atoi
  _ = math.Abs
  flag.Usage = Usage
  flag.StringVar(&host, "h", "localhost", "Specify host and port")
  flag.IntVar(&port, "p", 9090, "Specify port")
  flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
  flag.StringVar(&urlString, "u", "", "Specify the url")
  flag.BoolVar(&framed, "framed", false, "Use framed transport")
  flag.BoolVar(&useHttp, "http", false, "Use http")
  flag.Var(headers, "H", "Headers to set on the http(s) request (e.g. -H \"Key: Value\")")
  flag.Parse()
  
  if len(urlString) > 0 {
    var err error
    parsedUrl, err = url.Parse(urlString)
    if err != nil {
      fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
      flag.Usage()
    }
    host = parsedUrl.Host
    useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http" || parsedUrl.Scheme == "https"
  } else if useHttp {
    _, err := url.Parse(fmt.Sprint("http://", host, ":", port))
    if err != nil {
      fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
      flag.Usage()
    }
  }
  
  cmd := flag.Arg(0)
  var err error
  var cfg *thrift.TConfiguration = nil
  if useHttp {
    trans, err = thrift.NewTHttpClient(parsedUrl.String())
    if len(headers) > 0 {
      httptrans := trans.(*thrift.THttpClient)
      for key, value := range headers {
        httptrans.SetHeader(key, value)
      }
    }
  } else {
    portStr := fmt.Sprint(port)
    if strings.Contains(host, ":") {
           host, portStr, err = net.SplitHostPort(host)
           if err != nil {
                   fmt.Fprintln(os.Stderr, "error with host:", err)
                   os.Exit(1)
           }
    }
    trans = thrift.NewTSocketConf(net.JoinHostPort(host, portStr), cfg)
    if err != nil {
      fmt.Fprintln(os.Stderr, "error resolving address:", err)
      os.Exit(1)
    }
    if framed {
      trans = thrift.NewTFramedTransportConf(trans, cfg)
    }
  }
  if err != nil {
    fmt.Fprintln(os.Stderr, "Error creating transport", err)
    os.Exit(1)
  }
  defer trans.Close()
  var protocolFactory thrift.TProtocolFactory
  switch protocol {
  case "compact":
    protocolFactory = thrift.NewTCompactProtocolFactoryConf(cfg)
    break
  case "simplejson":
    protocolFactory = thrift.NewTSimpleJSONProtocolFactoryConf(cfg)
    break
  case "json":
    protocolFactory = thrift.NewTJSONProtocolFactory()
    break
  case "binary", "":
    protocolFactory = thrift.NewTBinaryProtocolFactoryConf(cfg)
    break
  default:
    fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
    Usage()
    os.Exit(1)
  }
  iprot := protocolFactory.GetProtocol(trans)
  oprot := protocolFactory.GetProtocol(trans)
  client := drive.NewDriveClient(thrift.NewTStandardClient(iprot, oprot))
  if err := trans.Open(); err != nil {
    fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
    os.Exit(1)
  }
  
  switch cmd {
  case "GetFileList":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetFileList requires 1 args")
      flag.Usage()
    }
    arg72 := flag.Arg(1)
    mbTrans73 := thrift.NewTMemoryBufferLen(len(arg72))
    defer mbTrans73.Close()
    _, err74 := mbTrans73.WriteString(arg72)
    if err74 != nil {
      Usage()
      return
    }
    factory75 := thrift.NewTJSONProtocolFactory()
    jsProt76 := factory75.GetProtocol(mbTrans73)
    argvalue0 := drive.NewGetFileListRequest()
    err77 := argvalue0.Read(context.Background(), jsProt76)
    if err77 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetFileList(context.Background(), value0))
    fmt.Print("\n")
    break
  case "CreateFolder":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "CreateFolder requires 1 args")
      flag.Usage()
    }
    arg78 := flag.Arg(1)
    mbTrans79 := thrift.NewTMemoryBufferLen(len(arg78))
    defer mbTrans79.Close()
    _, err80 := mbTrans79.WriteString(arg78)
    if err80 != nil {
      Usage()
      return
    }
    factory81 := thrift.NewTJSONProtocolFactory()
    jsProt82 := factory81.GetProtocol(mbTrans79)
    argvalue0 := drive.NewCreateFolderRequest()
    err83 := argvalue0.Read(context.Background(), jsProt82)
    if err83 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.CreateFolder(context.Background(), value0))
    fmt.Print("\n")
    break
  case "UploadFile":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "UploadFile requires 1 args")
      flag.Usage()
    }
    arg84 := flag.Arg(1)
    mbTrans85 := thrift.NewTMemoryBufferLen(len(arg84))
    defer mbTrans85.Close()
    _, err86 := mbTrans85.WriteString(arg84)
    if err86 != nil {
      Usage()
      return
    }
    factory87 := thrift.NewTJSONProtocolFactory()
    jsProt88 := factory87.GetProtocol(mbTrans85)
    argvalue0 := drive.NewUploadFileRequest()
    err89 := argvalue0.Read(context.Background(), jsProt88)
    if err89 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.UploadFile(context.Background(), value0))
    fmt.Print("\n")
    break
  case "DownloadFile":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "DownloadFile requires 1 args")
      flag.Usage()
    }
    arg90 := flag.Arg(1)
    mbTrans91 := thrift.NewTMemoryBufferLen(len(arg90))
    defer mbTrans91.Close()
    _, err92 := mbTrans91.WriteString(arg90)
    if err92 != nil {
      Usage()
      return
    }
    factory93 := thrift.NewTJSONProtocolFactory()
    jsProt94 := factory93.GetProtocol(mbTrans91)
    argvalue0 := drive.NewDownloadFileRequest()
    err95 := argvalue0.Read(context.Background(), jsProt94)
    if err95 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.DownloadFile(context.Background(), value0))
    fmt.Print("\n")
    break
  case "DeleteFile":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "DeleteFile requires 1 args")
      flag.Usage()
    }
    arg96 := flag.Arg(1)
    mbTrans97 := thrift.NewTMemoryBufferLen(len(arg96))
    defer mbTrans97.Close()
    _, err98 := mbTrans97.WriteString(arg96)
    if err98 != nil {
      Usage()
      return
    }
    factory99 := thrift.NewTJSONProtocolFactory()
    jsProt100 := factory99.GetProtocol(mbTrans97)
    argvalue0 := drive.NewDeleteFileRequest()
    err101 := argvalue0.Read(context.Background(), jsProt100)
    if err101 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.DeleteFile(context.Background(), value0))
    fmt.Print("\n")
    break
  case "RenameFile":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "RenameFile requires 1 args")
      flag.Usage()
    }
    arg102 := flag.Arg(1)
    mbTrans103 := thrift.NewTMemoryBufferLen(len(arg102))
    defer mbTrans103.Close()
    _, err104 := mbTrans103.WriteString(arg102)
    if err104 != nil {
      Usage()
      return
    }
    factory105 := thrift.NewTJSONProtocolFactory()
    jsProt106 := factory105.GetProtocol(mbTrans103)
    argvalue0 := drive.NewRenameFileRequest()
    err107 := argvalue0.Read(context.Background(), jsProt106)
    if err107 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.RenameFile(context.Background(), value0))
    fmt.Print("\n")
    break
  case "MoveFile":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "MoveFile requires 1 args")
      flag.Usage()
    }
    arg108 := flag.Arg(1)
    mbTrans109 := thrift.NewTMemoryBufferLen(len(arg108))
    defer mbTrans109.Close()
    _, err110 := mbTrans109.WriteString(arg108)
    if err110 != nil {
      Usage()
      return
    }
    factory111 := thrift.NewTJSONProtocolFactory()
    jsProt112 := factory111.GetProtocol(mbTrans109)
    argvalue0 := drive.NewMoveFileRequest()
    err113 := argvalue0.Read(context.Background(), jsProt112)
    if err113 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.MoveFile(context.Background(), value0))
    fmt.Print("\n")
    break
  case "InitChunkUpload":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "InitChunkUpload requires 1 args")
      flag.Usage()
    }
    arg114 := flag.Arg(1)
    mbTrans115 := thrift.NewTMemoryBufferLen(len(arg114))
    defer mbTrans115.Close()
    _, err116 := mbTrans115.WriteString(arg114)
    if err116 != nil {
      Usage()
      return
    }
    factory117 := thrift.NewTJSONProtocolFactory()
    jsProt118 := factory117.GetProtocol(mbTrans115)
    argvalue0 := drive.NewInitChunkUploadRequest()
    err119 := argvalue0.Read(context.Background(), jsProt118)
    if err119 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.InitChunkUpload(context.Background(), value0))
    fmt.Print("\n")
    break
  case "UploadChunk":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "UploadChunk requires 1 args")
      flag.Usage()
    }
    arg120 := flag.Arg(1)
    mbTrans121 := thrift.NewTMemoryBufferLen(len(arg120))
    defer mbTrans121.Close()
    _, err122 := mbTrans121.WriteString(arg120)
    if err122 != nil {
      Usage()
      return
    }
    factory123 := thrift.NewTJSONProtocolFactory()
    jsProt124 := factory123.GetProtocol(mbTrans121)
    argvalue0 := drive.NewUploadChunkRequest()
    err125 := argvalue0.Read(context.Background(), jsProt124)
    if err125 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.UploadChunk(context.Background(), value0))
    fmt.Print("\n")
    break
  case "CompleteChunkUpload":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "CompleteChunkUpload requires 1 args")
      flag.Usage()
    }
    arg126 := flag.Arg(1)
    mbTrans127 := thrift.NewTMemoryBufferLen(len(arg126))
    defer mbTrans127.Close()
    _, err128 := mbTrans127.WriteString(arg126)
    if err128 != nil {
      Usage()
      return
    }
    factory129 := thrift.NewTJSONProtocolFactory()
    jsProt130 := factory129.GetProtocol(mbTrans127)
    argvalue0 := drive.NewCompleteChunkUploadRequest()
    err131 := argvalue0.Read(context.Background(), jsProt130)
    if err131 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.CompleteChunkUpload(context.Background(), value0))
    fmt.Print("\n")
    break
  case "CreateShare":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "CreateShare requires 1 args")
      flag.Usage()
    }
    arg132 := flag.Arg(1)
    mbTrans133 := thrift.NewTMemoryBufferLen(len(arg132))
    defer mbTrans133.Close()
    _, err134 := mbTrans133.WriteString(arg132)
    if err134 != nil {
      Usage()
      return
    }
    factory135 := thrift.NewTJSONProtocolFactory()
    jsProt136 := factory135.GetProtocol(mbTrans133)
    argvalue0 := drive.NewCreateShareRequest()
    err137 := argvalue0.Read(context.Background(), jsProt136)
    if err137 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.CreateShare(context.Background(), value0))
    fmt.Print("\n")
    break
  case "GetShareInfo":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetShareInfo requires 1 args")
      flag.Usage()
    }
    arg138 := flag.Arg(1)
    mbTrans139 := thrift.NewTMemoryBufferLen(len(arg138))
    defer mbTrans139.Close()
    _, err140 := mbTrans139.WriteString(arg138)
    if err140 != nil {
      Usage()
      return
    }
    factory141 := thrift.NewTJSONProtocolFactory()
    jsProt142 := factory141.GetProtocol(mbTrans139)
    argvalue0 := drive.NewGetShareInfoRequest()
    err143 := argvalue0.Read(context.Background(), jsProt142)
    if err143 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetShareInfo(context.Background(), value0))
    fmt.Print("\n")
    break
  case "DownloadSharedFile":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "DownloadSharedFile requires 1 args")
      flag.Usage()
    }
    arg144 := flag.Arg(1)
    mbTrans145 := thrift.NewTMemoryBufferLen(len(arg144))
    defer mbTrans145.Close()
    _, err146 := mbTrans145.WriteString(arg144)
    if err146 != nil {
      Usage()
      return
    }
    factory147 := thrift.NewTJSONProtocolFactory()
    jsProt148 := factory147.GetProtocol(mbTrans145)
    argvalue0 := drive.NewDownloadSharedFileRequest()
    err149 := argvalue0.Read(context.Background(), jsProt148)
    if err149 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.DownloadSharedFile(context.Background(), value0))
    fmt.Print("\n")
    break
  case "GetStorageInfo":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetStorageInfo requires 1 args")
      flag.Usage()
    }
    arg150 := flag.Arg(1)
    mbTrans151 := thrift.NewTMemoryBufferLen(len(arg150))
    defer mbTrans151.Close()
    _, err152 := mbTrans151.WriteString(arg150)
    if err152 != nil {
      Usage()
      return
    }
    factory153 := thrift.NewTJSONProtocolFactory()
    jsProt154 := factory153.GetProtocol(mbTrans151)
    argvalue0 := drive.NewGetStorageInfoRequest()
    err155 := argvalue0.Read(context.Background(), jsProt154)
    if err155 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetStorageInfo(context.Background(), value0))
    fmt.Print("\n")
    break
  case "GetThumbnail":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetThumbnail requires 1 args")
      flag.Usage()
    }
    arg156 := flag.Arg(1)
    mbTrans157 := thrift.NewTMemoryBufferLen(len(arg156))
    defer mbTrans157.Close()
    _, err158 := mbTrans157.WriteString(arg156)
    if err158 != nil {
      Usage()
      return
    }
    factory159 := thrift.NewTJSONProtocolFactory()
    jsProt160 := factory159.GetProtocol(mbTrans157)
    argvalue0 := drive.NewGetThumbnailRequest()
    err161 := argvalue0.Read(context.Background(), jsProt160)
    if err161 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetThumbnail(context.Background(), value0))
    fmt.Print("\n")
    break
  case "GenerateThumbnail":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GenerateThumbnail requires 1 args")
      flag.Usage()
    }
    arg162 := flag.Arg(1)
    mbTrans163 := thrift.NewTMemoryBufferLen(len(arg162))
    defer mbTrans163.Close()
    _, err164 := mbTrans163.WriteString(arg162)
    if err164 != nil {
      Usage()
      return
    }
    factory165 := thrift.NewTJSONProtocolFactory()
    jsProt166 := factory165.GetProtocol(mbTrans163)
    argvalue0 := drive.NewGenerateThumbnailRequest()
    err167 := argvalue0.Read(context.Background(), jsProt166)
    if err167 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GenerateThumbnail(context.Background(), value0))
    fmt.Print("\n")
    break
  case "UploadProgress":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "UploadProgress requires 1 args")
      flag.Usage()
    }
    arg168 := flag.Arg(1)
    mbTrans169 := thrift.NewTMemoryBufferLen(len(arg168))
    defer mbTrans169.Close()
    _, err170 := mbTrans169.WriteString(arg168)
    if err170 != nil {
      Usage()
      return
    }
    factory171 := thrift.NewTJSONProtocolFactory()
    jsProt172 := factory171.GetProtocol(mbTrans169)
    argvalue0 := drive.NewUploadProgressRequest()
    err173 := argvalue0.Read(context.Background(), jsProt172)
    if err173 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.UploadProgress(context.Background(), value0))
    fmt.Print("\n")
    break
  case "":
    Usage()
    break
  default:
    fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
  }
}
