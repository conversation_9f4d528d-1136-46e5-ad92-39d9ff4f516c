// Code generated by Thrift Compiler (0.16.0). DO NOT EDIT.

package drive

import (
	"bytes"
	"context"
	"fmt"
	"time"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"thrift-common/gen-go/base"

)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

var _ = base.GoUnusedProtection__
// Attributes:
//  - ID
//  - Name
//  - Path
//  - ParentID
//  - FileType
//  - MimeType
//  - Size
//  - Hash
//  - StoragePath
//  - ThumbnailPath
//  - IsPublic
//  - DownloadCount
//  - CreatedAt
//  - UpdatedAt
type DriveFile struct {
  ID int64 `thrift:"ID,1" db:"ID" json:"ID"`
  Name string `thrift:"Name,2" db:"Name" json:"Name"`
  Path string `thrift:"Path,3" db:"Path" json:"Path"`
  ParentID *int64 `thrift:"ParentID,4" db:"ParentID" json:"ParentID,omitempty"`
  FileType string `thrift:"FileType,5" db:"FileType" json:"FileType"`
  MimeType *string `thrift:"MimeType,6" db:"MimeType" json:"MimeType,omitempty"`
  Size int64 `thrift:"Size,7" db:"Size" json:"Size"`
  Hash *string `thrift:"Hash,8" db:"Hash" json:"Hash,omitempty"`
  StoragePath *string `thrift:"StoragePath,9" db:"StoragePath" json:"StoragePath,omitempty"`
  ThumbnailPath *string `thrift:"ThumbnailPath,10" db:"ThumbnailPath" json:"ThumbnailPath,omitempty"`
  IsPublic bool `thrift:"IsPublic,11" db:"IsPublic" json:"IsPublic"`
  DownloadCount int64 `thrift:"DownloadCount,12" db:"DownloadCount" json:"DownloadCount"`
  CreatedAt string `thrift:"CreatedAt,13" db:"CreatedAt" json:"CreatedAt"`
  UpdatedAt string `thrift:"UpdatedAt,14" db:"UpdatedAt" json:"UpdatedAt"`
}

func NewDriveFile() *DriveFile {
  return &DriveFile{}
}


func (p *DriveFile) GetID() int64 {
  return p.ID
}

func (p *DriveFile) GetName() string {
  return p.Name
}

func (p *DriveFile) GetPath() string {
  return p.Path
}
var DriveFile_ParentID_DEFAULT int64
func (p *DriveFile) GetParentID() int64 {
  if !p.IsSetParentID() {
    return DriveFile_ParentID_DEFAULT
  }
return *p.ParentID
}

func (p *DriveFile) GetFileType() string {
  return p.FileType
}
var DriveFile_MimeType_DEFAULT string
func (p *DriveFile) GetMimeType() string {
  if !p.IsSetMimeType() {
    return DriveFile_MimeType_DEFAULT
  }
return *p.MimeType
}

func (p *DriveFile) GetSize() int64 {
  return p.Size
}
var DriveFile_Hash_DEFAULT string
func (p *DriveFile) GetHash() string {
  if !p.IsSetHash() {
    return DriveFile_Hash_DEFAULT
  }
return *p.Hash
}
var DriveFile_StoragePath_DEFAULT string
func (p *DriveFile) GetStoragePath() string {
  if !p.IsSetStoragePath() {
    return DriveFile_StoragePath_DEFAULT
  }
return *p.StoragePath
}
var DriveFile_ThumbnailPath_DEFAULT string
func (p *DriveFile) GetThumbnailPath() string {
  if !p.IsSetThumbnailPath() {
    return DriveFile_ThumbnailPath_DEFAULT
  }
return *p.ThumbnailPath
}

func (p *DriveFile) GetIsPublic() bool {
  return p.IsPublic
}

func (p *DriveFile) GetDownloadCount() int64 {
  return p.DownloadCount
}

func (p *DriveFile) GetCreatedAt() string {
  return p.CreatedAt
}

func (p *DriveFile) GetUpdatedAt() string {
  return p.UpdatedAt
}
func (p *DriveFile) IsSetParentID() bool {
  return p.ParentID != nil
}

func (p *DriveFile) IsSetMimeType() bool {
  return p.MimeType != nil
}

func (p *DriveFile) IsSetHash() bool {
  return p.Hash != nil
}

func (p *DriveFile) IsSetStoragePath() bool {
  return p.StoragePath != nil
}

func (p *DriveFile) IsSetThumbnailPath() bool {
  return p.ThumbnailPath != nil
}

func (p *DriveFile) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 10:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField10(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 11:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField11(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 12:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField12(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 13:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField13(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 14:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField14(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveFile)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ID = v
}
  return nil
}

func (p *DriveFile)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Name = v
}
  return nil
}

func (p *DriveFile)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Path = v
}
  return nil
}

func (p *DriveFile)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.ParentID = &v
}
  return nil
}

func (p *DriveFile)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.FileType = v
}
  return nil
}

func (p *DriveFile)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.MimeType = &v
}
  return nil
}

func (p *DriveFile)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.Size = v
}
  return nil
}

func (p *DriveFile)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.Hash = &v
}
  return nil
}

func (p *DriveFile)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.StoragePath = &v
}
  return nil
}

func (p *DriveFile)  ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 10: ", err)
} else {
  p.ThumbnailPath = &v
}
  return nil
}

func (p *DriveFile)  ReadField11(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 11: ", err)
} else {
  p.IsPublic = v
}
  return nil
}

func (p *DriveFile)  ReadField12(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 12: ", err)
} else {
  p.DownloadCount = v
}
  return nil
}

func (p *DriveFile)  ReadField13(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 13: ", err)
} else {
  p.CreatedAt = v
}
  return nil
}

func (p *DriveFile)  ReadField14(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 14: ", err)
} else {
  p.UpdatedAt = v
}
  return nil
}

func (p *DriveFile) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DriveFile"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
    if err := p.writeField10(ctx, oprot); err != nil { return err }
    if err := p.writeField11(ctx, oprot); err != nil { return err }
    if err := p.writeField12(ctx, oprot); err != nil { return err }
    if err := p.writeField13(ctx, oprot); err != nil { return err }
    if err := p.writeField14(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveFile) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ID", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ID: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.ID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ID (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ID: ", p), err) }
  return err
}

func (p *DriveFile) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Name", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Name: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Name)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Name (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Name: ", p), err) }
  return err
}

func (p *DriveFile) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Path", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Path: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Path)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Path (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Path: ", p), err) }
  return err
}

func (p *DriveFile) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetParentID() {
    if err := oprot.WriteFieldBegin(ctx, "ParentID", thrift.I64, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:ParentID: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.ParentID)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ParentID (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:ParentID: ", p), err) }
  }
  return err
}

func (p *DriveFile) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileType", thrift.STRING, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:FileType: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.FileType)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileType (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:FileType: ", p), err) }
  return err
}

func (p *DriveFile) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMimeType() {
    if err := oprot.WriteFieldBegin(ctx, "MimeType", thrift.STRING, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:MimeType: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.MimeType)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.MimeType (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:MimeType: ", p), err) }
  }
  return err
}

func (p *DriveFile) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Size", thrift.I64, 7); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:Size: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Size)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Size (7) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 7:Size: ", p), err) }
  return err
}

func (p *DriveFile) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetHash() {
    if err := oprot.WriteFieldBegin(ctx, "Hash", thrift.STRING, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:Hash: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Hash)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Hash (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:Hash: ", p), err) }
  }
  return err
}

func (p *DriveFile) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetStoragePath() {
    if err := oprot.WriteFieldBegin(ctx, "StoragePath", thrift.STRING, 9); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:StoragePath: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.StoragePath)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.StoragePath (9) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 9:StoragePath: ", p), err) }
  }
  return err
}

func (p *DriveFile) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetThumbnailPath() {
    if err := oprot.WriteFieldBegin(ctx, "ThumbnailPath", thrift.STRING, 10); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:ThumbnailPath: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ThumbnailPath)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ThumbnailPath (10) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 10:ThumbnailPath: ", p), err) }
  }
  return err
}

func (p *DriveFile) writeField11(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "IsPublic", thrift.BOOL, 11); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:IsPublic: ", p), err) }
  if err := oprot.WriteBool(ctx, bool(p.IsPublic)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.IsPublic (11) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 11:IsPublic: ", p), err) }
  return err
}

func (p *DriveFile) writeField12(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "DownloadCount", thrift.I64, 12); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 12:DownloadCount: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.DownloadCount)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.DownloadCount (12) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 12:DownloadCount: ", p), err) }
  return err
}

func (p *DriveFile) writeField13(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "CreatedAt", thrift.STRING, 13); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 13:CreatedAt: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.CreatedAt)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.CreatedAt (13) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 13:CreatedAt: ", p), err) }
  return err
}

func (p *DriveFile) writeField14(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "UpdatedAt", thrift.STRING, 14); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 14:UpdatedAt: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.UpdatedAt)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.UpdatedAt (14) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 14:UpdatedAt: ", p), err) }
  return err
}

func (p *DriveFile) Equals(other *DriveFile) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ID != other.ID { return false }
  if p.Name != other.Name { return false }
  if p.Path != other.Path { return false }
  if p.ParentID != other.ParentID {
    if p.ParentID == nil || other.ParentID == nil {
      return false
    }
    if (*p.ParentID) != (*other.ParentID) { return false }
  }
  if p.FileType != other.FileType { return false }
  if p.MimeType != other.MimeType {
    if p.MimeType == nil || other.MimeType == nil {
      return false
    }
    if (*p.MimeType) != (*other.MimeType) { return false }
  }
  if p.Size != other.Size { return false }
  if p.Hash != other.Hash {
    if p.Hash == nil || other.Hash == nil {
      return false
    }
    if (*p.Hash) != (*other.Hash) { return false }
  }
  if p.StoragePath != other.StoragePath {
    if p.StoragePath == nil || other.StoragePath == nil {
      return false
    }
    if (*p.StoragePath) != (*other.StoragePath) { return false }
  }
  if p.ThumbnailPath != other.ThumbnailPath {
    if p.ThumbnailPath == nil || other.ThumbnailPath == nil {
      return false
    }
    if (*p.ThumbnailPath) != (*other.ThumbnailPath) { return false }
  }
  if p.IsPublic != other.IsPublic { return false }
  if p.DownloadCount != other.DownloadCount { return false }
  if p.CreatedAt != other.CreatedAt { return false }
  if p.UpdatedAt != other.UpdatedAt { return false }
  return true
}

func (p *DriveFile) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveFile(%+v)", *p)
}

// Attributes:
//  - ID
//  - FileID
//  - ShareCode
//  - Password
//  - ExpireTime
//  - DownloadLimit
//  - DownloadCount
//  - IsActive
//  - CreatedAt
type DriveShare struct {
  ID int64 `thrift:"ID,1" db:"ID" json:"ID"`
  FileID int64 `thrift:"FileID,2" db:"FileID" json:"FileID"`
  ShareCode string `thrift:"ShareCode,3" db:"ShareCode" json:"ShareCode"`
  Password *string `thrift:"Password,4" db:"Password" json:"Password,omitempty"`
  ExpireTime *string `thrift:"ExpireTime,5" db:"ExpireTime" json:"ExpireTime,omitempty"`
  DownloadLimit int64 `thrift:"DownloadLimit,6" db:"DownloadLimit" json:"DownloadLimit"`
  DownloadCount int64 `thrift:"DownloadCount,7" db:"DownloadCount" json:"DownloadCount"`
  IsActive bool `thrift:"IsActive,8" db:"IsActive" json:"IsActive"`
  CreatedAt string `thrift:"CreatedAt,9" db:"CreatedAt" json:"CreatedAt"`
}

func NewDriveShare() *DriveShare {
  return &DriveShare{}
}


func (p *DriveShare) GetID() int64 {
  return p.ID
}

func (p *DriveShare) GetFileID() int64 {
  return p.FileID
}

func (p *DriveShare) GetShareCode() string {
  return p.ShareCode
}
var DriveShare_Password_DEFAULT string
func (p *DriveShare) GetPassword() string {
  if !p.IsSetPassword() {
    return DriveShare_Password_DEFAULT
  }
return *p.Password
}
var DriveShare_ExpireTime_DEFAULT string
func (p *DriveShare) GetExpireTime() string {
  if !p.IsSetExpireTime() {
    return DriveShare_ExpireTime_DEFAULT
  }
return *p.ExpireTime
}

func (p *DriveShare) GetDownloadLimit() int64 {
  return p.DownloadLimit
}

func (p *DriveShare) GetDownloadCount() int64 {
  return p.DownloadCount
}

func (p *DriveShare) GetIsActive() bool {
  return p.IsActive
}

func (p *DriveShare) GetCreatedAt() string {
  return p.CreatedAt
}
func (p *DriveShare) IsSetPassword() bool {
  return p.Password != nil
}

func (p *DriveShare) IsSetExpireTime() bool {
  return p.ExpireTime != nil
}

func (p *DriveShare) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveShare)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ID = v
}
  return nil
}

func (p *DriveShare)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.FileID = v
}
  return nil
}

func (p *DriveShare)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.ShareCode = v
}
  return nil
}

func (p *DriveShare)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Password = &v
}
  return nil
}

func (p *DriveShare)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.ExpireTime = &v
}
  return nil
}

func (p *DriveShare)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.DownloadLimit = v
}
  return nil
}

func (p *DriveShare)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.DownloadCount = v
}
  return nil
}

func (p *DriveShare)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.IsActive = v
}
  return nil
}

func (p *DriveShare)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.CreatedAt = v
}
  return nil
}

func (p *DriveShare) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DriveShare"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveShare) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ID", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ID: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.ID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ID (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ID: ", p), err) }
  return err
}

func (p *DriveShare) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileID", thrift.I64, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:FileID: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.FileID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileID (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:FileID: ", p), err) }
  return err
}

func (p *DriveShare) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ShareCode", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:ShareCode: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.ShareCode)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ShareCode (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:ShareCode: ", p), err) }
  return err
}

func (p *DriveShare) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetPassword() {
    if err := oprot.WriteFieldBegin(ctx, "Password", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:Password: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Password)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Password (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:Password: ", p), err) }
  }
  return err
}

func (p *DriveShare) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetExpireTime() {
    if err := oprot.WriteFieldBegin(ctx, "ExpireTime", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:ExpireTime: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ExpireTime)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ExpireTime (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:ExpireTime: ", p), err) }
  }
  return err
}

func (p *DriveShare) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "DownloadLimit", thrift.I64, 6); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:DownloadLimit: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.DownloadLimit)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.DownloadLimit (6) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 6:DownloadLimit: ", p), err) }
  return err
}

func (p *DriveShare) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "DownloadCount", thrift.I64, 7); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:DownloadCount: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.DownloadCount)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.DownloadCount (7) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 7:DownloadCount: ", p), err) }
  return err
}

func (p *DriveShare) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "IsActive", thrift.BOOL, 8); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:IsActive: ", p), err) }
  if err := oprot.WriteBool(ctx, bool(p.IsActive)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.IsActive (8) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 8:IsActive: ", p), err) }
  return err
}

func (p *DriveShare) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "CreatedAt", thrift.STRING, 9); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:CreatedAt: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.CreatedAt)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.CreatedAt (9) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 9:CreatedAt: ", p), err) }
  return err
}

func (p *DriveShare) Equals(other *DriveShare) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ID != other.ID { return false }
  if p.FileID != other.FileID { return false }
  if p.ShareCode != other.ShareCode { return false }
  if p.Password != other.Password {
    if p.Password == nil || other.Password == nil {
      return false
    }
    if (*p.Password) != (*other.Password) { return false }
  }
  if p.ExpireTime != other.ExpireTime {
    if p.ExpireTime == nil || other.ExpireTime == nil {
      return false
    }
    if (*p.ExpireTime) != (*other.ExpireTime) { return false }
  }
  if p.DownloadLimit != other.DownloadLimit { return false }
  if p.DownloadCount != other.DownloadCount { return false }
  if p.IsActive != other.IsActive { return false }
  if p.CreatedAt != other.CreatedAt { return false }
  return true
}

func (p *DriveShare) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveShare(%+v)", *p)
}

// Attributes:
//  - ID
//  - FileName
//  - FileSize
//  - ChunkSize
//  - TotalChunks
//  - UploadedChunks
//  - UploadPath
//  - TempDir
//  - Status
//  - ErrorMessage
//  - CreatedAt
type DriveUploadTask struct {
  ID int64 `thrift:"ID,1" db:"ID" json:"ID"`
  FileName string `thrift:"FileName,2" db:"FileName" json:"FileName"`
  FileSize int64 `thrift:"FileSize,3" db:"FileSize" json:"FileSize"`
  ChunkSize int64 `thrift:"ChunkSize,4" db:"ChunkSize" json:"ChunkSize"`
  TotalChunks int64 `thrift:"TotalChunks,5" db:"TotalChunks" json:"TotalChunks"`
  UploadedChunks int64 `thrift:"UploadedChunks,6" db:"UploadedChunks" json:"UploadedChunks"`
  UploadPath string `thrift:"UploadPath,7" db:"UploadPath" json:"UploadPath"`
  TempDir string `thrift:"TempDir,8" db:"TempDir" json:"TempDir"`
  Status string `thrift:"Status,9" db:"Status" json:"Status"`
  ErrorMessage *string `thrift:"ErrorMessage,10" db:"ErrorMessage" json:"ErrorMessage,omitempty"`
  CreatedAt string `thrift:"CreatedAt,11" db:"CreatedAt" json:"CreatedAt"`
}

func NewDriveUploadTask() *DriveUploadTask {
  return &DriveUploadTask{}
}


func (p *DriveUploadTask) GetID() int64 {
  return p.ID
}

func (p *DriveUploadTask) GetFileName() string {
  return p.FileName
}

func (p *DriveUploadTask) GetFileSize() int64 {
  return p.FileSize
}

func (p *DriveUploadTask) GetChunkSize() int64 {
  return p.ChunkSize
}

func (p *DriveUploadTask) GetTotalChunks() int64 {
  return p.TotalChunks
}

func (p *DriveUploadTask) GetUploadedChunks() int64 {
  return p.UploadedChunks
}

func (p *DriveUploadTask) GetUploadPath() string {
  return p.UploadPath
}

func (p *DriveUploadTask) GetTempDir() string {
  return p.TempDir
}

func (p *DriveUploadTask) GetStatus() string {
  return p.Status
}
var DriveUploadTask_ErrorMessage_DEFAULT string
func (p *DriveUploadTask) GetErrorMessage() string {
  if !p.IsSetErrorMessage() {
    return DriveUploadTask_ErrorMessage_DEFAULT
  }
return *p.ErrorMessage
}

func (p *DriveUploadTask) GetCreatedAt() string {
  return p.CreatedAt
}
func (p *DriveUploadTask) IsSetErrorMessage() bool {
  return p.ErrorMessage != nil
}

func (p *DriveUploadTask) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 10:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField10(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 11:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField11(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveUploadTask)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ID = v
}
  return nil
}

func (p *DriveUploadTask)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.FileName = v
}
  return nil
}

func (p *DriveUploadTask)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.FileSize = v
}
  return nil
}

func (p *DriveUploadTask)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.ChunkSize = v
}
  return nil
}

func (p *DriveUploadTask)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.TotalChunks = v
}
  return nil
}

func (p *DriveUploadTask)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.UploadedChunks = v
}
  return nil
}

func (p *DriveUploadTask)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.UploadPath = v
}
  return nil
}

func (p *DriveUploadTask)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.TempDir = v
}
  return nil
}

func (p *DriveUploadTask)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.Status = v
}
  return nil
}

func (p *DriveUploadTask)  ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 10: ", err)
} else {
  p.ErrorMessage = &v
}
  return nil
}

func (p *DriveUploadTask)  ReadField11(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 11: ", err)
} else {
  p.CreatedAt = v
}
  return nil
}

func (p *DriveUploadTask) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DriveUploadTask"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
    if err := p.writeField10(ctx, oprot); err != nil { return err }
    if err := p.writeField11(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveUploadTask) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ID", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ID: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.ID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ID (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ID: ", p), err) }
  return err
}

func (p *DriveUploadTask) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileName", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:FileName: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.FileName)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileName (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:FileName: ", p), err) }
  return err
}

func (p *DriveUploadTask) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileSize", thrift.I64, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:FileSize: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.FileSize)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileSize (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:FileSize: ", p), err) }
  return err
}

func (p *DriveUploadTask) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ChunkSize", thrift.I64, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:ChunkSize: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.ChunkSize)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ChunkSize (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:ChunkSize: ", p), err) }
  return err
}

func (p *DriveUploadTask) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "TotalChunks", thrift.I64, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:TotalChunks: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.TotalChunks)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.TotalChunks (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:TotalChunks: ", p), err) }
  return err
}

func (p *DriveUploadTask) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "UploadedChunks", thrift.I64, 6); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:UploadedChunks: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.UploadedChunks)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.UploadedChunks (6) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 6:UploadedChunks: ", p), err) }
  return err
}

func (p *DriveUploadTask) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "UploadPath", thrift.STRING, 7); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:UploadPath: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.UploadPath)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.UploadPath (7) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 7:UploadPath: ", p), err) }
  return err
}

func (p *DriveUploadTask) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "TempDir", thrift.STRING, 8); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:TempDir: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.TempDir)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.TempDir (8) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 8:TempDir: ", p), err) }
  return err
}

func (p *DriveUploadTask) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Status", thrift.STRING, 9); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:Status: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Status)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Status (9) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 9:Status: ", p), err) }
  return err
}

func (p *DriveUploadTask) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetErrorMessage() {
    if err := oprot.WriteFieldBegin(ctx, "ErrorMessage", thrift.STRING, 10); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:ErrorMessage: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ErrorMessage)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ErrorMessage (10) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 10:ErrorMessage: ", p), err) }
  }
  return err
}

func (p *DriveUploadTask) writeField11(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "CreatedAt", thrift.STRING, 11); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:CreatedAt: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.CreatedAt)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.CreatedAt (11) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 11:CreatedAt: ", p), err) }
  return err
}

func (p *DriveUploadTask) Equals(other *DriveUploadTask) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ID != other.ID { return false }
  if p.FileName != other.FileName { return false }
  if p.FileSize != other.FileSize { return false }
  if p.ChunkSize != other.ChunkSize { return false }
  if p.TotalChunks != other.TotalChunks { return false }
  if p.UploadedChunks != other.UploadedChunks { return false }
  if p.UploadPath != other.UploadPath { return false }
  if p.TempDir != other.TempDir { return false }
  if p.Status != other.Status { return false }
  if p.ErrorMessage != other.ErrorMessage {
    if p.ErrorMessage == nil || other.ErrorMessage == nil {
      return false
    }
    if (*p.ErrorMessage) != (*other.ErrorMessage) { return false }
  }
  if p.CreatedAt != other.CreatedAt { return false }
  return true
}

func (p *DriveUploadTask) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveUploadTask(%+v)", *p)
}

// Attributes:
//  - ParentID
//  - Offset
//  - Count
//  - Keyword
//  - Base
type GetFileListRequest struct {
  ParentID *int64 `thrift:"ParentID,1" db:"ParentID" json:"ParentID,omitempty"`
  Offset int64 `thrift:"Offset,2" db:"Offset" json:"Offset"`
  Count int64 `thrift:"Count,3" db:"Count" json:"Count"`
  Keyword *string `thrift:"Keyword,4" db:"Keyword" json:"Keyword,omitempty"`
  // unused fields # 5 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewGetFileListRequest() *GetFileListRequest {
  return &GetFileListRequest{}
}

var GetFileListRequest_ParentID_DEFAULT int64
func (p *GetFileListRequest) GetParentID() int64 {
  if !p.IsSetParentID() {
    return GetFileListRequest_ParentID_DEFAULT
  }
return *p.ParentID
}

func (p *GetFileListRequest) GetOffset() int64 {
  return p.Offset
}

func (p *GetFileListRequest) GetCount() int64 {
  return p.Count
}
var GetFileListRequest_Keyword_DEFAULT string
func (p *GetFileListRequest) GetKeyword() string {
  if !p.IsSetKeyword() {
    return GetFileListRequest_Keyword_DEFAULT
  }
return *p.Keyword
}
var GetFileListRequest_Base_DEFAULT *base.Base
func (p *GetFileListRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return GetFileListRequest_Base_DEFAULT
  }
return p.Base
}
func (p *GetFileListRequest) IsSetParentID() bool {
  return p.ParentID != nil
}

func (p *GetFileListRequest) IsSetKeyword() bool {
  return p.Keyword != nil
}

func (p *GetFileListRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *GetFileListRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetFileListRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ParentID = &v
}
  return nil
}

func (p *GetFileListRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Offset = v
}
  return nil
}

func (p *GetFileListRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Count = v
}
  return nil
}

func (p *GetFileListRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Keyword = &v
}
  return nil
}

func (p *GetFileListRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *GetFileListRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetFileListRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetFileListRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetParentID() {
    if err := oprot.WriteFieldBegin(ctx, "ParentID", thrift.I64, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ParentID: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.ParentID)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ParentID (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ParentID: ", p), err) }
  }
  return err
}

func (p *GetFileListRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Offset", thrift.I64, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Offset: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Offset)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Offset (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Offset: ", p), err) }
  return err
}

func (p *GetFileListRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Count", thrift.I64, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Count: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Count)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Count (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Count: ", p), err) }
  return err
}

func (p *GetFileListRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetKeyword() {
    if err := oprot.WriteFieldBegin(ctx, "Keyword", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:Keyword: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Keyword)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Keyword (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:Keyword: ", p), err) }
  }
  return err
}

func (p *GetFileListRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *GetFileListRequest) Equals(other *GetFileListRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ParentID != other.ParentID {
    if p.ParentID == nil || other.ParentID == nil {
      return false
    }
    if (*p.ParentID) != (*other.ParentID) { return false }
  }
  if p.Offset != other.Offset { return false }
  if p.Count != other.Count { return false }
  if p.Keyword != other.Keyword {
    if p.Keyword == nil || other.Keyword == nil {
      return false
    }
    if (*p.Keyword) != (*other.Keyword) { return false }
  }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *GetFileListRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetFileListRequest(%+v)", *p)
}

// Attributes:
//  - FileList
//  - Total
//  - HasMore
//  - BaseResp
type GetFileListResponse struct {
  FileList []*DriveFile `thrift:"FileList,1" db:"FileList" json:"FileList"`
  Total int64 `thrift:"Total,2" db:"Total" json:"Total"`
  HasMore bool `thrift:"HasMore,3" db:"HasMore" json:"HasMore"`
  // unused fields # 4 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewGetFileListResponse() *GetFileListResponse {
  return &GetFileListResponse{}
}


func (p *GetFileListResponse) GetFileList() []*DriveFile {
  return p.FileList
}

func (p *GetFileListResponse) GetTotal() int64 {
  return p.Total
}

func (p *GetFileListResponse) GetHasMore() bool {
  return p.HasMore
}
var GetFileListResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *GetFileListResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return GetFileListResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *GetFileListResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *GetFileListResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetFileListResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*DriveFile, 0, size)
  p.FileList =  tSlice
  for i := 0; i < size; i ++ {
    _elem0 := &DriveFile{}
    if err := _elem0.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem0), err)
    }
    p.FileList = append(p.FileList, _elem0)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *GetFileListResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Total = v
}
  return nil
}

func (p *GetFileListResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.HasMore = v
}
  return nil
}

func (p *GetFileListResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *GetFileListResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetFileListResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetFileListResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileList", thrift.LIST, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:FileList: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.FileList)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.FileList {
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:FileList: ", p), err) }
  return err
}

func (p *GetFileListResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Total", thrift.I64, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Total: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Total)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Total (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Total: ", p), err) }
  return err
}

func (p *GetFileListResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "HasMore", thrift.BOOL, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:HasMore: ", p), err) }
  if err := oprot.WriteBool(ctx, bool(p.HasMore)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.HasMore (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:HasMore: ", p), err) }
  return err
}

func (p *GetFileListResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *GetFileListResponse) Equals(other *GetFileListResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if len(p.FileList) != len(other.FileList) { return false }
  for i, _tgt := range p.FileList {
    _src1 := other.FileList[i]
    if !_tgt.Equals(_src1) { return false }
  }
  if p.Total != other.Total { return false }
  if p.HasMore != other.HasMore { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *GetFileListResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetFileListResponse(%+v)", *p)
}

// Attributes:
//  - Name
//  - ParentID
//  - Base
type CreateFolderRequest struct {
  Name string `thrift:"Name,1" db:"Name" json:"Name"`
  ParentID *int64 `thrift:"ParentID,2" db:"ParentID" json:"ParentID,omitempty"`
  // unused fields # 3 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewCreateFolderRequest() *CreateFolderRequest {
  return &CreateFolderRequest{}
}


func (p *CreateFolderRequest) GetName() string {
  return p.Name
}
var CreateFolderRequest_ParentID_DEFAULT int64
func (p *CreateFolderRequest) GetParentID() int64 {
  if !p.IsSetParentID() {
    return CreateFolderRequest_ParentID_DEFAULT
  }
return *p.ParentID
}
var CreateFolderRequest_Base_DEFAULT *base.Base
func (p *CreateFolderRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return CreateFolderRequest_Base_DEFAULT
  }
return p.Base
}
func (p *CreateFolderRequest) IsSetParentID() bool {
  return p.ParentID != nil
}

func (p *CreateFolderRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *CreateFolderRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CreateFolderRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Name = v
}
  return nil
}

func (p *CreateFolderRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.ParentID = &v
}
  return nil
}

func (p *CreateFolderRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *CreateFolderRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CreateFolderRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CreateFolderRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Name", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Name: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Name)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Name (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Name: ", p), err) }
  return err
}

func (p *CreateFolderRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetParentID() {
    if err := oprot.WriteFieldBegin(ctx, "ParentID", thrift.I64, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:ParentID: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.ParentID)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ParentID (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:ParentID: ", p), err) }
  }
  return err
}

func (p *CreateFolderRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *CreateFolderRequest) Equals(other *CreateFolderRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Name != other.Name { return false }
  if p.ParentID != other.ParentID {
    if p.ParentID == nil || other.ParentID == nil {
      return false
    }
    if (*p.ParentID) != (*other.ParentID) { return false }
  }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *CreateFolderRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CreateFolderRequest(%+v)", *p)
}

// Attributes:
//  - Folder
//  - BaseResp
type CreateFolderResponse struct {
  Folder *DriveFile `thrift:"Folder,1" db:"Folder" json:"Folder"`
  // unused fields # 2 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewCreateFolderResponse() *CreateFolderResponse {
  return &CreateFolderResponse{}
}

var CreateFolderResponse_Folder_DEFAULT *DriveFile
func (p *CreateFolderResponse) GetFolder() *DriveFile {
  if !p.IsSetFolder() {
    return CreateFolderResponse_Folder_DEFAULT
  }
return p.Folder
}
var CreateFolderResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *CreateFolderResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return CreateFolderResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *CreateFolderResponse) IsSetFolder() bool {
  return p.Folder != nil
}

func (p *CreateFolderResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *CreateFolderResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CreateFolderResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Folder = &DriveFile{}
  if err := p.Folder.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Folder), err)
  }
  return nil
}

func (p *CreateFolderResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *CreateFolderResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CreateFolderResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CreateFolderResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Folder", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Folder: ", p), err) }
  if err := p.Folder.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Folder), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Folder: ", p), err) }
  return err
}

func (p *CreateFolderResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *CreateFolderResponse) Equals(other *CreateFolderResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.Folder.Equals(other.Folder) { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *CreateFolderResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CreateFolderResponse(%+v)", *p)
}

// Attributes:
//  - FileName
//  - FileSize
//  - ParentID
//  - FileData
//  - Base
type UploadFileRequest struct {
  FileName string `thrift:"FileName,1" db:"FileName" json:"FileName"`
  FileSize int64 `thrift:"FileSize,2" db:"FileSize" json:"FileSize"`
  ParentID *int64 `thrift:"ParentID,3" db:"ParentID" json:"ParentID,omitempty"`
  FileData []byte `thrift:"FileData,4" db:"FileData" json:"FileData"`
  // unused fields # 5 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewUploadFileRequest() *UploadFileRequest {
  return &UploadFileRequest{}
}


func (p *UploadFileRequest) GetFileName() string {
  return p.FileName
}

func (p *UploadFileRequest) GetFileSize() int64 {
  return p.FileSize
}
var UploadFileRequest_ParentID_DEFAULT int64
func (p *UploadFileRequest) GetParentID() int64 {
  if !p.IsSetParentID() {
    return UploadFileRequest_ParentID_DEFAULT
  }
return *p.ParentID
}

func (p *UploadFileRequest) GetFileData() []byte {
  return p.FileData
}
var UploadFileRequest_Base_DEFAULT *base.Base
func (p *UploadFileRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return UploadFileRequest_Base_DEFAULT
  }
return p.Base
}
func (p *UploadFileRequest) IsSetParentID() bool {
  return p.ParentID != nil
}

func (p *UploadFileRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *UploadFileRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *UploadFileRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.FileName = v
}
  return nil
}

func (p *UploadFileRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.FileSize = v
}
  return nil
}

func (p *UploadFileRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.ParentID = &v
}
  return nil
}

func (p *UploadFileRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBinary(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.FileData = v
}
  return nil
}

func (p *UploadFileRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *UploadFileRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UploadFileRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UploadFileRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileName", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:FileName: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.FileName)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileName (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:FileName: ", p), err) }
  return err
}

func (p *UploadFileRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileSize", thrift.I64, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:FileSize: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.FileSize)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileSize (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:FileSize: ", p), err) }
  return err
}

func (p *UploadFileRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetParentID() {
    if err := oprot.WriteFieldBegin(ctx, "ParentID", thrift.I64, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:ParentID: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.ParentID)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ParentID (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:ParentID: ", p), err) }
  }
  return err
}

func (p *UploadFileRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileData", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:FileData: ", p), err) }
  if err := oprot.WriteBinary(ctx, p.FileData); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileData (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:FileData: ", p), err) }
  return err
}

func (p *UploadFileRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *UploadFileRequest) Equals(other *UploadFileRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.FileName != other.FileName { return false }
  if p.FileSize != other.FileSize { return false }
  if p.ParentID != other.ParentID {
    if p.ParentID == nil || other.ParentID == nil {
      return false
    }
    if (*p.ParentID) != (*other.ParentID) { return false }
  }
  if bytes.Compare(p.FileData, other.FileData) != 0 { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *UploadFileRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UploadFileRequest(%+v)", *p)
}

// Attributes:
//  - File
//  - BaseResp
type UploadFileResponse struct {
  File *DriveFile `thrift:"File,1" db:"File" json:"File"`
  // unused fields # 2 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewUploadFileResponse() *UploadFileResponse {
  return &UploadFileResponse{}
}

var UploadFileResponse_File_DEFAULT *DriveFile
func (p *UploadFileResponse) GetFile() *DriveFile {
  if !p.IsSetFile() {
    return UploadFileResponse_File_DEFAULT
  }
return p.File
}
var UploadFileResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *UploadFileResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return UploadFileResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *UploadFileResponse) IsSetFile() bool {
  return p.File != nil
}

func (p *UploadFileResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *UploadFileResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *UploadFileResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.File = &DriveFile{}
  if err := p.File.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.File), err)
  }
  return nil
}

func (p *UploadFileResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *UploadFileResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UploadFileResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UploadFileResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "File", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:File: ", p), err) }
  if err := p.File.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.File), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:File: ", p), err) }
  return err
}

func (p *UploadFileResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *UploadFileResponse) Equals(other *UploadFileResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.File.Equals(other.File) { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *UploadFileResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UploadFileResponse(%+v)", *p)
}

// Attributes:
//  - FileName
//  - FileSize
//  - ParentID
//  - ChunkSize
//  - Base
type InitChunkUploadRequest struct {
  FileName string `thrift:"FileName,1" db:"FileName" json:"FileName"`
  FileSize int64 `thrift:"FileSize,2" db:"FileSize" json:"FileSize"`
  ParentID *int64 `thrift:"ParentID,3" db:"ParentID" json:"ParentID,omitempty"`
  ChunkSize int64 `thrift:"ChunkSize,4" db:"ChunkSize" json:"ChunkSize"`
  // unused fields # 5 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewInitChunkUploadRequest() *InitChunkUploadRequest {
  return &InitChunkUploadRequest{}
}


func (p *InitChunkUploadRequest) GetFileName() string {
  return p.FileName
}

func (p *InitChunkUploadRequest) GetFileSize() int64 {
  return p.FileSize
}
var InitChunkUploadRequest_ParentID_DEFAULT int64
func (p *InitChunkUploadRequest) GetParentID() int64 {
  if !p.IsSetParentID() {
    return InitChunkUploadRequest_ParentID_DEFAULT
  }
return *p.ParentID
}

func (p *InitChunkUploadRequest) GetChunkSize() int64 {
  return p.ChunkSize
}
var InitChunkUploadRequest_Base_DEFAULT *base.Base
func (p *InitChunkUploadRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return InitChunkUploadRequest_Base_DEFAULT
  }
return p.Base
}
func (p *InitChunkUploadRequest) IsSetParentID() bool {
  return p.ParentID != nil
}

func (p *InitChunkUploadRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *InitChunkUploadRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *InitChunkUploadRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.FileName = v
}
  return nil
}

func (p *InitChunkUploadRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.FileSize = v
}
  return nil
}

func (p *InitChunkUploadRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.ParentID = &v
}
  return nil
}

func (p *InitChunkUploadRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.ChunkSize = v
}
  return nil
}

func (p *InitChunkUploadRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *InitChunkUploadRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "InitChunkUploadRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *InitChunkUploadRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileName", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:FileName: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.FileName)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileName (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:FileName: ", p), err) }
  return err
}

func (p *InitChunkUploadRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileSize", thrift.I64, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:FileSize: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.FileSize)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileSize (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:FileSize: ", p), err) }
  return err
}

func (p *InitChunkUploadRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetParentID() {
    if err := oprot.WriteFieldBegin(ctx, "ParentID", thrift.I64, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:ParentID: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.ParentID)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ParentID (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:ParentID: ", p), err) }
  }
  return err
}

func (p *InitChunkUploadRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ChunkSize", thrift.I64, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:ChunkSize: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.ChunkSize)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ChunkSize (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:ChunkSize: ", p), err) }
  return err
}

func (p *InitChunkUploadRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *InitChunkUploadRequest) Equals(other *InitChunkUploadRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.FileName != other.FileName { return false }
  if p.FileSize != other.FileSize { return false }
  if p.ParentID != other.ParentID {
    if p.ParentID == nil || other.ParentID == nil {
      return false
    }
    if (*p.ParentID) != (*other.ParentID) { return false }
  }
  if p.ChunkSize != other.ChunkSize { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *InitChunkUploadRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("InitChunkUploadRequest(%+v)", *p)
}

// Attributes:
//  - UploadTask
//  - BaseResp
type InitChunkUploadResponse struct {
  UploadTask *DriveUploadTask `thrift:"UploadTask,1" db:"UploadTask" json:"UploadTask"`
  // unused fields # 2 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewInitChunkUploadResponse() *InitChunkUploadResponse {
  return &InitChunkUploadResponse{}
}

var InitChunkUploadResponse_UploadTask_DEFAULT *DriveUploadTask
func (p *InitChunkUploadResponse) GetUploadTask() *DriveUploadTask {
  if !p.IsSetUploadTask() {
    return InitChunkUploadResponse_UploadTask_DEFAULT
  }
return p.UploadTask
}
var InitChunkUploadResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *InitChunkUploadResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return InitChunkUploadResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *InitChunkUploadResponse) IsSetUploadTask() bool {
  return p.UploadTask != nil
}

func (p *InitChunkUploadResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *InitChunkUploadResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *InitChunkUploadResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.UploadTask = &DriveUploadTask{}
  if err := p.UploadTask.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.UploadTask), err)
  }
  return nil
}

func (p *InitChunkUploadResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *InitChunkUploadResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "InitChunkUploadResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *InitChunkUploadResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "UploadTask", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:UploadTask: ", p), err) }
  if err := p.UploadTask.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.UploadTask), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:UploadTask: ", p), err) }
  return err
}

func (p *InitChunkUploadResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *InitChunkUploadResponse) Equals(other *InitChunkUploadResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.UploadTask.Equals(other.UploadTask) { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *InitChunkUploadResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("InitChunkUploadResponse(%+v)", *p)
}

// Attributes:
//  - TaskID
//  - ChunkIndex
//  - ChunkData
//  - Base
type UploadChunkRequest struct {
  TaskID int64 `thrift:"TaskID,1" db:"TaskID" json:"TaskID"`
  ChunkIndex int64 `thrift:"ChunkIndex,2" db:"ChunkIndex" json:"ChunkIndex"`
  ChunkData []byte `thrift:"ChunkData,3" db:"ChunkData" json:"ChunkData"`
  // unused fields # 4 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewUploadChunkRequest() *UploadChunkRequest {
  return &UploadChunkRequest{}
}


func (p *UploadChunkRequest) GetTaskID() int64 {
  return p.TaskID
}

func (p *UploadChunkRequest) GetChunkIndex() int64 {
  return p.ChunkIndex
}

func (p *UploadChunkRequest) GetChunkData() []byte {
  return p.ChunkData
}
var UploadChunkRequest_Base_DEFAULT *base.Base
func (p *UploadChunkRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return UploadChunkRequest_Base_DEFAULT
  }
return p.Base
}
func (p *UploadChunkRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *UploadChunkRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *UploadChunkRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.TaskID = v
}
  return nil
}

func (p *UploadChunkRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.ChunkIndex = v
}
  return nil
}

func (p *UploadChunkRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBinary(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.ChunkData = v
}
  return nil
}

func (p *UploadChunkRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *UploadChunkRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UploadChunkRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UploadChunkRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "TaskID", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:TaskID: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.TaskID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.TaskID (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:TaskID: ", p), err) }
  return err
}

func (p *UploadChunkRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ChunkIndex", thrift.I64, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:ChunkIndex: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.ChunkIndex)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ChunkIndex (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:ChunkIndex: ", p), err) }
  return err
}

func (p *UploadChunkRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ChunkData", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:ChunkData: ", p), err) }
  if err := oprot.WriteBinary(ctx, p.ChunkData); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ChunkData (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:ChunkData: ", p), err) }
  return err
}

func (p *UploadChunkRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *UploadChunkRequest) Equals(other *UploadChunkRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.TaskID != other.TaskID { return false }
  if p.ChunkIndex != other.ChunkIndex { return false }
  if bytes.Compare(p.ChunkData, other.ChunkData) != 0 { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *UploadChunkRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UploadChunkRequest(%+v)", *p)
}

// Attributes:
//  - UploadTask
//  - BaseResp
type UploadChunkResponse struct {
  UploadTask *DriveUploadTask `thrift:"UploadTask,1" db:"UploadTask" json:"UploadTask"`
  // unused fields # 2 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewUploadChunkResponse() *UploadChunkResponse {
  return &UploadChunkResponse{}
}

var UploadChunkResponse_UploadTask_DEFAULT *DriveUploadTask
func (p *UploadChunkResponse) GetUploadTask() *DriveUploadTask {
  if !p.IsSetUploadTask() {
    return UploadChunkResponse_UploadTask_DEFAULT
  }
return p.UploadTask
}
var UploadChunkResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *UploadChunkResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return UploadChunkResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *UploadChunkResponse) IsSetUploadTask() bool {
  return p.UploadTask != nil
}

func (p *UploadChunkResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *UploadChunkResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *UploadChunkResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.UploadTask = &DriveUploadTask{}
  if err := p.UploadTask.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.UploadTask), err)
  }
  return nil
}

func (p *UploadChunkResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *UploadChunkResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UploadChunkResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UploadChunkResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "UploadTask", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:UploadTask: ", p), err) }
  if err := p.UploadTask.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.UploadTask), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:UploadTask: ", p), err) }
  return err
}

func (p *UploadChunkResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *UploadChunkResponse) Equals(other *UploadChunkResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.UploadTask.Equals(other.UploadTask) { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *UploadChunkResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UploadChunkResponse(%+v)", *p)
}

// Attributes:
//  - TaskID
//  - Base
type CompleteChunkUploadRequest struct {
  TaskID int64 `thrift:"TaskID,1" db:"TaskID" json:"TaskID"`
  // unused fields # 2 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewCompleteChunkUploadRequest() *CompleteChunkUploadRequest {
  return &CompleteChunkUploadRequest{}
}


func (p *CompleteChunkUploadRequest) GetTaskID() int64 {
  return p.TaskID
}
var CompleteChunkUploadRequest_Base_DEFAULT *base.Base
func (p *CompleteChunkUploadRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return CompleteChunkUploadRequest_Base_DEFAULT
  }
return p.Base
}
func (p *CompleteChunkUploadRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *CompleteChunkUploadRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CompleteChunkUploadRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.TaskID = v
}
  return nil
}

func (p *CompleteChunkUploadRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *CompleteChunkUploadRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CompleteChunkUploadRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CompleteChunkUploadRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "TaskID", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:TaskID: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.TaskID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.TaskID (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:TaskID: ", p), err) }
  return err
}

func (p *CompleteChunkUploadRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *CompleteChunkUploadRequest) Equals(other *CompleteChunkUploadRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.TaskID != other.TaskID { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *CompleteChunkUploadRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CompleteChunkUploadRequest(%+v)", *p)
}

// Attributes:
//  - File
//  - BaseResp
type CompleteChunkUploadResponse struct {
  File *DriveFile `thrift:"File,1" db:"File" json:"File"`
  // unused fields # 2 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewCompleteChunkUploadResponse() *CompleteChunkUploadResponse {
  return &CompleteChunkUploadResponse{}
}

var CompleteChunkUploadResponse_File_DEFAULT *DriveFile
func (p *CompleteChunkUploadResponse) GetFile() *DriveFile {
  if !p.IsSetFile() {
    return CompleteChunkUploadResponse_File_DEFAULT
  }
return p.File
}
var CompleteChunkUploadResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *CompleteChunkUploadResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return CompleteChunkUploadResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *CompleteChunkUploadResponse) IsSetFile() bool {
  return p.File != nil
}

func (p *CompleteChunkUploadResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *CompleteChunkUploadResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CompleteChunkUploadResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.File = &DriveFile{}
  if err := p.File.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.File), err)
  }
  return nil
}

func (p *CompleteChunkUploadResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *CompleteChunkUploadResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CompleteChunkUploadResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CompleteChunkUploadResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "File", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:File: ", p), err) }
  if err := p.File.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.File), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:File: ", p), err) }
  return err
}

func (p *CompleteChunkUploadResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *CompleteChunkUploadResponse) Equals(other *CompleteChunkUploadResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.File.Equals(other.File) { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *CompleteChunkUploadResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CompleteChunkUploadResponse(%+v)", *p)
}

// Attributes:
//  - FileID
//  - Base
type DownloadFileRequest struct {
  FileID int64 `thrift:"FileID,1" db:"FileID" json:"FileID"`
  // unused fields # 2 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewDownloadFileRequest() *DownloadFileRequest {
  return &DownloadFileRequest{}
}


func (p *DownloadFileRequest) GetFileID() int64 {
  return p.FileID
}
var DownloadFileRequest_Base_DEFAULT *base.Base
func (p *DownloadFileRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return DownloadFileRequest_Base_DEFAULT
  }
return p.Base
}
func (p *DownloadFileRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *DownloadFileRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DownloadFileRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.FileID = v
}
  return nil
}

func (p *DownloadFileRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *DownloadFileRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DownloadFileRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DownloadFileRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileID", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:FileID: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.FileID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileID (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:FileID: ", p), err) }
  return err
}

func (p *DownloadFileRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *DownloadFileRequest) Equals(other *DownloadFileRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.FileID != other.FileID { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *DownloadFileRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DownloadFileRequest(%+v)", *p)
}

// Attributes:
//  - FileData
//  - FileName
//  - MimeType
//  - BaseResp
type DownloadFileResponse struct {
  FileData []byte `thrift:"FileData,1" db:"FileData" json:"FileData"`
  FileName string `thrift:"FileName,2" db:"FileName" json:"FileName"`
  MimeType string `thrift:"MimeType,3" db:"MimeType" json:"MimeType"`
  // unused fields # 4 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewDownloadFileResponse() *DownloadFileResponse {
  return &DownloadFileResponse{}
}


func (p *DownloadFileResponse) GetFileData() []byte {
  return p.FileData
}

func (p *DownloadFileResponse) GetFileName() string {
  return p.FileName
}

func (p *DownloadFileResponse) GetMimeType() string {
  return p.MimeType
}
var DownloadFileResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *DownloadFileResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return DownloadFileResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *DownloadFileResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *DownloadFileResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DownloadFileResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBinary(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.FileData = v
}
  return nil
}

func (p *DownloadFileResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.FileName = v
}
  return nil
}

func (p *DownloadFileResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.MimeType = v
}
  return nil
}

func (p *DownloadFileResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *DownloadFileResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DownloadFileResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DownloadFileResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileData", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:FileData: ", p), err) }
  if err := oprot.WriteBinary(ctx, p.FileData); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileData (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:FileData: ", p), err) }
  return err
}

func (p *DownloadFileResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileName", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:FileName: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.FileName)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileName (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:FileName: ", p), err) }
  return err
}

func (p *DownloadFileResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "MimeType", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:MimeType: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.MimeType)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.MimeType (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:MimeType: ", p), err) }
  return err
}

func (p *DownloadFileResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *DownloadFileResponse) Equals(other *DownloadFileResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if bytes.Compare(p.FileData, other.FileData) != 0 { return false }
  if p.FileName != other.FileName { return false }
  if p.MimeType != other.MimeType { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *DownloadFileResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DownloadFileResponse(%+v)", *p)
}

// Attributes:
//  - FileID
//  - Base
type DeleteFileRequest struct {
  FileID int64 `thrift:"FileID,1" db:"FileID" json:"FileID"`
  // unused fields # 2 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewDeleteFileRequest() *DeleteFileRequest {
  return &DeleteFileRequest{}
}


func (p *DeleteFileRequest) GetFileID() int64 {
  return p.FileID
}
var DeleteFileRequest_Base_DEFAULT *base.Base
func (p *DeleteFileRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return DeleteFileRequest_Base_DEFAULT
  }
return p.Base
}
func (p *DeleteFileRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *DeleteFileRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DeleteFileRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.FileID = v
}
  return nil
}

func (p *DeleteFileRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *DeleteFileRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DeleteFileRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DeleteFileRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileID", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:FileID: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.FileID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileID (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:FileID: ", p), err) }
  return err
}

func (p *DeleteFileRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *DeleteFileRequest) Equals(other *DeleteFileRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.FileID != other.FileID { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *DeleteFileRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DeleteFileRequest(%+v)", *p)
}

// Attributes:
//  - BaseResp
type DeleteFileResponse struct {
  // unused fields # 1 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewDeleteFileResponse() *DeleteFileResponse {
  return &DeleteFileResponse{}
}

var DeleteFileResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *DeleteFileResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return DeleteFileResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *DeleteFileResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *DeleteFileResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DeleteFileResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *DeleteFileResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DeleteFileResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DeleteFileResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *DeleteFileResponse) Equals(other *DeleteFileResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *DeleteFileResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DeleteFileResponse(%+v)", *p)
}

// Attributes:
//  - FileID
//  - NewName_
//  - Base
type RenameFileRequest struct {
  FileID int64 `thrift:"FileID,1" db:"FileID" json:"FileID"`
  NewName_ string `thrift:"NewName,2" db:"NewName" json:"NewName"`
  // unused fields # 3 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewRenameFileRequest() *RenameFileRequest {
  return &RenameFileRequest{}
}


func (p *RenameFileRequest) GetFileID() int64 {
  return p.FileID
}

func (p *RenameFileRequest) GetNewName_() string {
  return p.NewName_
}
var RenameFileRequest_Base_DEFAULT *base.Base
func (p *RenameFileRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return RenameFileRequest_Base_DEFAULT
  }
return p.Base
}
func (p *RenameFileRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *RenameFileRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *RenameFileRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.FileID = v
}
  return nil
}

func (p *RenameFileRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.NewName_ = v
}
  return nil
}

func (p *RenameFileRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *RenameFileRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "RenameFileRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *RenameFileRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileID", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:FileID: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.FileID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileID (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:FileID: ", p), err) }
  return err
}

func (p *RenameFileRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "NewName", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:NewName: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.NewName_)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.NewName (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:NewName: ", p), err) }
  return err
}

func (p *RenameFileRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *RenameFileRequest) Equals(other *RenameFileRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.FileID != other.FileID { return false }
  if p.NewName_ != other.NewName_ { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *RenameFileRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("RenameFileRequest(%+v)", *p)
}

// Attributes:
//  - File
//  - BaseResp
type RenameFileResponse struct {
  File *DriveFile `thrift:"File,1" db:"File" json:"File"`
  // unused fields # 2 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewRenameFileResponse() *RenameFileResponse {
  return &RenameFileResponse{}
}

var RenameFileResponse_File_DEFAULT *DriveFile
func (p *RenameFileResponse) GetFile() *DriveFile {
  if !p.IsSetFile() {
    return RenameFileResponse_File_DEFAULT
  }
return p.File
}
var RenameFileResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *RenameFileResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return RenameFileResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *RenameFileResponse) IsSetFile() bool {
  return p.File != nil
}

func (p *RenameFileResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *RenameFileResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *RenameFileResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.File = &DriveFile{}
  if err := p.File.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.File), err)
  }
  return nil
}

func (p *RenameFileResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *RenameFileResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "RenameFileResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *RenameFileResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "File", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:File: ", p), err) }
  if err := p.File.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.File), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:File: ", p), err) }
  return err
}

func (p *RenameFileResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *RenameFileResponse) Equals(other *RenameFileResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.File.Equals(other.File) { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *RenameFileResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("RenameFileResponse(%+v)", *p)
}

// Attributes:
//  - FileID
//  - NewParentID_
//  - Base
type MoveFileRequest struct {
  FileID int64 `thrift:"FileID,1" db:"FileID" json:"FileID"`
  NewParentID_ *int64 `thrift:"NewParentID,2" db:"NewParentID" json:"NewParentID,omitempty"`
  // unused fields # 3 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewMoveFileRequest() *MoveFileRequest {
  return &MoveFileRequest{}
}


func (p *MoveFileRequest) GetFileID() int64 {
  return p.FileID
}
var MoveFileRequest_NewParentID__DEFAULT int64
func (p *MoveFileRequest) GetNewParentID_() int64 {
  if !p.IsSetNewParentID_() {
    return MoveFileRequest_NewParentID__DEFAULT
  }
return *p.NewParentID_
}
var MoveFileRequest_Base_DEFAULT *base.Base
func (p *MoveFileRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return MoveFileRequest_Base_DEFAULT
  }
return p.Base
}
func (p *MoveFileRequest) IsSetNewParentID_() bool {
  return p.NewParentID_ != nil
}

func (p *MoveFileRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *MoveFileRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *MoveFileRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.FileID = v
}
  return nil
}

func (p *MoveFileRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.NewParentID_ = &v
}
  return nil
}

func (p *MoveFileRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *MoveFileRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "MoveFileRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *MoveFileRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileID", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:FileID: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.FileID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileID (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:FileID: ", p), err) }
  return err
}

func (p *MoveFileRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetNewParentID_() {
    if err := oprot.WriteFieldBegin(ctx, "NewParentID", thrift.I64, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:NewParentID: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.NewParentID_)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.NewParentID (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:NewParentID: ", p), err) }
  }
  return err
}

func (p *MoveFileRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *MoveFileRequest) Equals(other *MoveFileRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.FileID != other.FileID { return false }
  if p.NewParentID_ != other.NewParentID_ {
    if p.NewParentID_ == nil || other.NewParentID_ == nil {
      return false
    }
    if (*p.NewParentID_) != (*other.NewParentID_) { return false }
  }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *MoveFileRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("MoveFileRequest(%+v)", *p)
}

// Attributes:
//  - File
//  - BaseResp
type MoveFileResponse struct {
  File *DriveFile `thrift:"File,1" db:"File" json:"File"`
  // unused fields # 2 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewMoveFileResponse() *MoveFileResponse {
  return &MoveFileResponse{}
}

var MoveFileResponse_File_DEFAULT *DriveFile
func (p *MoveFileResponse) GetFile() *DriveFile {
  if !p.IsSetFile() {
    return MoveFileResponse_File_DEFAULT
  }
return p.File
}
var MoveFileResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *MoveFileResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return MoveFileResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *MoveFileResponse) IsSetFile() bool {
  return p.File != nil
}

func (p *MoveFileResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *MoveFileResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *MoveFileResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.File = &DriveFile{}
  if err := p.File.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.File), err)
  }
  return nil
}

func (p *MoveFileResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *MoveFileResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "MoveFileResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *MoveFileResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "File", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:File: ", p), err) }
  if err := p.File.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.File), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:File: ", p), err) }
  return err
}

func (p *MoveFileResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *MoveFileResponse) Equals(other *MoveFileResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.File.Equals(other.File) { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *MoveFileResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("MoveFileResponse(%+v)", *p)
}

// Attributes:
//  - FileID
//  - Password
//  - ExpireTime
//  - DownloadLimit
//  - Base
type CreateShareRequest struct {
  FileID int64 `thrift:"FileID,1" db:"FileID" json:"FileID"`
  Password *string `thrift:"Password,2" db:"Password" json:"Password,omitempty"`
  ExpireTime *string `thrift:"ExpireTime,3" db:"ExpireTime" json:"ExpireTime,omitempty"`
  DownloadLimit int64 `thrift:"DownloadLimit,4" db:"DownloadLimit" json:"DownloadLimit"`
  // unused fields # 5 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewCreateShareRequest() *CreateShareRequest {
  return &CreateShareRequest{}
}


func (p *CreateShareRequest) GetFileID() int64 {
  return p.FileID
}
var CreateShareRequest_Password_DEFAULT string
func (p *CreateShareRequest) GetPassword() string {
  if !p.IsSetPassword() {
    return CreateShareRequest_Password_DEFAULT
  }
return *p.Password
}
var CreateShareRequest_ExpireTime_DEFAULT string
func (p *CreateShareRequest) GetExpireTime() string {
  if !p.IsSetExpireTime() {
    return CreateShareRequest_ExpireTime_DEFAULT
  }
return *p.ExpireTime
}

func (p *CreateShareRequest) GetDownloadLimit() int64 {
  return p.DownloadLimit
}
var CreateShareRequest_Base_DEFAULT *base.Base
func (p *CreateShareRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return CreateShareRequest_Base_DEFAULT
  }
return p.Base
}
func (p *CreateShareRequest) IsSetPassword() bool {
  return p.Password != nil
}

func (p *CreateShareRequest) IsSetExpireTime() bool {
  return p.ExpireTime != nil
}

func (p *CreateShareRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *CreateShareRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CreateShareRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.FileID = v
}
  return nil
}

func (p *CreateShareRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Password = &v
}
  return nil
}

func (p *CreateShareRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.ExpireTime = &v
}
  return nil
}

func (p *CreateShareRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.DownloadLimit = v
}
  return nil
}

func (p *CreateShareRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *CreateShareRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CreateShareRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CreateShareRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileID", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:FileID: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.FileID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileID (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:FileID: ", p), err) }
  return err
}

func (p *CreateShareRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetPassword() {
    if err := oprot.WriteFieldBegin(ctx, "Password", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Password: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Password)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Password (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Password: ", p), err) }
  }
  return err
}

func (p *CreateShareRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetExpireTime() {
    if err := oprot.WriteFieldBegin(ctx, "ExpireTime", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:ExpireTime: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ExpireTime)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ExpireTime (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:ExpireTime: ", p), err) }
  }
  return err
}

func (p *CreateShareRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "DownloadLimit", thrift.I64, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:DownloadLimit: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.DownloadLimit)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.DownloadLimit (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:DownloadLimit: ", p), err) }
  return err
}

func (p *CreateShareRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *CreateShareRequest) Equals(other *CreateShareRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.FileID != other.FileID { return false }
  if p.Password != other.Password {
    if p.Password == nil || other.Password == nil {
      return false
    }
    if (*p.Password) != (*other.Password) { return false }
  }
  if p.ExpireTime != other.ExpireTime {
    if p.ExpireTime == nil || other.ExpireTime == nil {
      return false
    }
    if (*p.ExpireTime) != (*other.ExpireTime) { return false }
  }
  if p.DownloadLimit != other.DownloadLimit { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *CreateShareRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CreateShareRequest(%+v)", *p)
}

// Attributes:
//  - Share
//  - BaseResp
type CreateShareResponse struct {
  Share *DriveShare `thrift:"Share,1" db:"Share" json:"Share"`
  // unused fields # 2 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewCreateShareResponse() *CreateShareResponse {
  return &CreateShareResponse{}
}

var CreateShareResponse_Share_DEFAULT *DriveShare
func (p *CreateShareResponse) GetShare() *DriveShare {
  if !p.IsSetShare() {
    return CreateShareResponse_Share_DEFAULT
  }
return p.Share
}
var CreateShareResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *CreateShareResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return CreateShareResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *CreateShareResponse) IsSetShare() bool {
  return p.Share != nil
}

func (p *CreateShareResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *CreateShareResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CreateShareResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Share = &DriveShare{}
  if err := p.Share.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Share), err)
  }
  return nil
}

func (p *CreateShareResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *CreateShareResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CreateShareResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CreateShareResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Share", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Share: ", p), err) }
  if err := p.Share.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Share), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Share: ", p), err) }
  return err
}

func (p *CreateShareResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *CreateShareResponse) Equals(other *CreateShareResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.Share.Equals(other.Share) { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *CreateShareResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CreateShareResponse(%+v)", *p)
}

// Attributes:
//  - ShareCode
//  - Password
//  - Base
type GetShareInfoRequest struct {
  ShareCode string `thrift:"ShareCode,1" db:"ShareCode" json:"ShareCode"`
  Password *string `thrift:"Password,2" db:"Password" json:"Password,omitempty"`
  // unused fields # 3 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewGetShareInfoRequest() *GetShareInfoRequest {
  return &GetShareInfoRequest{}
}


func (p *GetShareInfoRequest) GetShareCode() string {
  return p.ShareCode
}
var GetShareInfoRequest_Password_DEFAULT string
func (p *GetShareInfoRequest) GetPassword() string {
  if !p.IsSetPassword() {
    return GetShareInfoRequest_Password_DEFAULT
  }
return *p.Password
}
var GetShareInfoRequest_Base_DEFAULT *base.Base
func (p *GetShareInfoRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return GetShareInfoRequest_Base_DEFAULT
  }
return p.Base
}
func (p *GetShareInfoRequest) IsSetPassword() bool {
  return p.Password != nil
}

func (p *GetShareInfoRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *GetShareInfoRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetShareInfoRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ShareCode = v
}
  return nil
}

func (p *GetShareInfoRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Password = &v
}
  return nil
}

func (p *GetShareInfoRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *GetShareInfoRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetShareInfoRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetShareInfoRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ShareCode", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ShareCode: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.ShareCode)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ShareCode (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ShareCode: ", p), err) }
  return err
}

func (p *GetShareInfoRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetPassword() {
    if err := oprot.WriteFieldBegin(ctx, "Password", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Password: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Password)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Password (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Password: ", p), err) }
  }
  return err
}

func (p *GetShareInfoRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *GetShareInfoRequest) Equals(other *GetShareInfoRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ShareCode != other.ShareCode { return false }
  if p.Password != other.Password {
    if p.Password == nil || other.Password == nil {
      return false
    }
    if (*p.Password) != (*other.Password) { return false }
  }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *GetShareInfoRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetShareInfoRequest(%+v)", *p)
}

// Attributes:
//  - Share
//  - File
//  - BaseResp
type GetShareInfoResponse struct {
  Share *DriveShare `thrift:"Share,1" db:"Share" json:"Share"`
  File *DriveFile `thrift:"File,2" db:"File" json:"File"`
  // unused fields # 3 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewGetShareInfoResponse() *GetShareInfoResponse {
  return &GetShareInfoResponse{}
}

var GetShareInfoResponse_Share_DEFAULT *DriveShare
func (p *GetShareInfoResponse) GetShare() *DriveShare {
  if !p.IsSetShare() {
    return GetShareInfoResponse_Share_DEFAULT
  }
return p.Share
}
var GetShareInfoResponse_File_DEFAULT *DriveFile
func (p *GetShareInfoResponse) GetFile() *DriveFile {
  if !p.IsSetFile() {
    return GetShareInfoResponse_File_DEFAULT
  }
return p.File
}
var GetShareInfoResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *GetShareInfoResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return GetShareInfoResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *GetShareInfoResponse) IsSetShare() bool {
  return p.Share != nil
}

func (p *GetShareInfoResponse) IsSetFile() bool {
  return p.File != nil
}

func (p *GetShareInfoResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *GetShareInfoResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetShareInfoResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Share = &DriveShare{}
  if err := p.Share.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Share), err)
  }
  return nil
}

func (p *GetShareInfoResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  p.File = &DriveFile{}
  if err := p.File.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.File), err)
  }
  return nil
}

func (p *GetShareInfoResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *GetShareInfoResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetShareInfoResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetShareInfoResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Share", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Share: ", p), err) }
  if err := p.Share.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Share), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Share: ", p), err) }
  return err
}

func (p *GetShareInfoResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "File", thrift.STRUCT, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:File: ", p), err) }
  if err := p.File.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.File), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:File: ", p), err) }
  return err
}

func (p *GetShareInfoResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *GetShareInfoResponse) Equals(other *GetShareInfoResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.Share.Equals(other.Share) { return false }
  if !p.File.Equals(other.File) { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *GetShareInfoResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetShareInfoResponse(%+v)", *p)
}

// Attributes:
//  - ShareCode
//  - Password
//  - Base
type DownloadSharedFileRequest struct {
  ShareCode string `thrift:"ShareCode,1" db:"ShareCode" json:"ShareCode"`
  Password *string `thrift:"Password,2" db:"Password" json:"Password,omitempty"`
  // unused fields # 3 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewDownloadSharedFileRequest() *DownloadSharedFileRequest {
  return &DownloadSharedFileRequest{}
}


func (p *DownloadSharedFileRequest) GetShareCode() string {
  return p.ShareCode
}
var DownloadSharedFileRequest_Password_DEFAULT string
func (p *DownloadSharedFileRequest) GetPassword() string {
  if !p.IsSetPassword() {
    return DownloadSharedFileRequest_Password_DEFAULT
  }
return *p.Password
}
var DownloadSharedFileRequest_Base_DEFAULT *base.Base
func (p *DownloadSharedFileRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return DownloadSharedFileRequest_Base_DEFAULT
  }
return p.Base
}
func (p *DownloadSharedFileRequest) IsSetPassword() bool {
  return p.Password != nil
}

func (p *DownloadSharedFileRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *DownloadSharedFileRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DownloadSharedFileRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ShareCode = v
}
  return nil
}

func (p *DownloadSharedFileRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Password = &v
}
  return nil
}

func (p *DownloadSharedFileRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *DownloadSharedFileRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DownloadSharedFileRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DownloadSharedFileRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ShareCode", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ShareCode: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.ShareCode)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ShareCode (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ShareCode: ", p), err) }
  return err
}

func (p *DownloadSharedFileRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetPassword() {
    if err := oprot.WriteFieldBegin(ctx, "Password", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Password: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Password)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Password (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Password: ", p), err) }
  }
  return err
}

func (p *DownloadSharedFileRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *DownloadSharedFileRequest) Equals(other *DownloadSharedFileRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ShareCode != other.ShareCode { return false }
  if p.Password != other.Password {
    if p.Password == nil || other.Password == nil {
      return false
    }
    if (*p.Password) != (*other.Password) { return false }
  }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *DownloadSharedFileRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DownloadSharedFileRequest(%+v)", *p)
}

// Attributes:
//  - FileData
//  - FileName
//  - MimeType
//  - BaseResp
type DownloadSharedFileResponse struct {
  FileData []byte `thrift:"FileData,1" db:"FileData" json:"FileData"`
  FileName string `thrift:"FileName,2" db:"FileName" json:"FileName"`
  MimeType string `thrift:"MimeType,3" db:"MimeType" json:"MimeType"`
  // unused fields # 4 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewDownloadSharedFileResponse() *DownloadSharedFileResponse {
  return &DownloadSharedFileResponse{}
}


func (p *DownloadSharedFileResponse) GetFileData() []byte {
  return p.FileData
}

func (p *DownloadSharedFileResponse) GetFileName() string {
  return p.FileName
}

func (p *DownloadSharedFileResponse) GetMimeType() string {
  return p.MimeType
}
var DownloadSharedFileResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *DownloadSharedFileResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return DownloadSharedFileResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *DownloadSharedFileResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *DownloadSharedFileResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DownloadSharedFileResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBinary(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.FileData = v
}
  return nil
}

func (p *DownloadSharedFileResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.FileName = v
}
  return nil
}

func (p *DownloadSharedFileResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.MimeType = v
}
  return nil
}

func (p *DownloadSharedFileResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *DownloadSharedFileResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DownloadSharedFileResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DownloadSharedFileResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileData", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:FileData: ", p), err) }
  if err := oprot.WriteBinary(ctx, p.FileData); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileData (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:FileData: ", p), err) }
  return err
}

func (p *DownloadSharedFileResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileName", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:FileName: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.FileName)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileName (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:FileName: ", p), err) }
  return err
}

func (p *DownloadSharedFileResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "MimeType", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:MimeType: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.MimeType)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.MimeType (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:MimeType: ", p), err) }
  return err
}

func (p *DownloadSharedFileResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *DownloadSharedFileResponse) Equals(other *DownloadSharedFileResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if bytes.Compare(p.FileData, other.FileData) != 0 { return false }
  if p.FileName != other.FileName { return false }
  if p.MimeType != other.MimeType { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *DownloadSharedFileResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DownloadSharedFileResponse(%+v)", *p)
}

// Attributes:
//  - Base
type GetStorageInfoRequest struct {
  // unused fields # 1 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewGetStorageInfoRequest() *GetStorageInfoRequest {
  return &GetStorageInfoRequest{}
}

var GetStorageInfoRequest_Base_DEFAULT *base.Base
func (p *GetStorageInfoRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return GetStorageInfoRequest_Base_DEFAULT
  }
return p.Base
}
func (p *GetStorageInfoRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *GetStorageInfoRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetStorageInfoRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *GetStorageInfoRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetStorageInfoRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetStorageInfoRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *GetStorageInfoRequest) Equals(other *GetStorageInfoRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *GetStorageInfoRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetStorageInfoRequest(%+v)", *p)
}

// Attributes:
//  - TotalSpace
//  - UsedSpace
//  - FreeSpace
//  - UsagePercent
//  - BaseResp
type GetStorageInfoResponse struct {
  TotalSpace int64 `thrift:"TotalSpace,1" db:"TotalSpace" json:"TotalSpace"`
  UsedSpace int64 `thrift:"UsedSpace,2" db:"UsedSpace" json:"UsedSpace"`
  FreeSpace int64 `thrift:"FreeSpace,3" db:"FreeSpace" json:"FreeSpace"`
  UsagePercent float64 `thrift:"UsagePercent,4" db:"UsagePercent" json:"UsagePercent"`
  // unused fields # 5 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewGetStorageInfoResponse() *GetStorageInfoResponse {
  return &GetStorageInfoResponse{}
}


func (p *GetStorageInfoResponse) GetTotalSpace() int64 {
  return p.TotalSpace
}

func (p *GetStorageInfoResponse) GetUsedSpace() int64 {
  return p.UsedSpace
}

func (p *GetStorageInfoResponse) GetFreeSpace() int64 {
  return p.FreeSpace
}

func (p *GetStorageInfoResponse) GetUsagePercent() float64 {
  return p.UsagePercent
}
var GetStorageInfoResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *GetStorageInfoResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return GetStorageInfoResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *GetStorageInfoResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *GetStorageInfoResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.DOUBLE {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetStorageInfoResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.TotalSpace = v
}
  return nil
}

func (p *GetStorageInfoResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.UsedSpace = v
}
  return nil
}

func (p *GetStorageInfoResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.FreeSpace = v
}
  return nil
}

func (p *GetStorageInfoResponse)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadDouble(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.UsagePercent = v
}
  return nil
}

func (p *GetStorageInfoResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *GetStorageInfoResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetStorageInfoResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetStorageInfoResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "TotalSpace", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:TotalSpace: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.TotalSpace)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.TotalSpace (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:TotalSpace: ", p), err) }
  return err
}

func (p *GetStorageInfoResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "UsedSpace", thrift.I64, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:UsedSpace: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.UsedSpace)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.UsedSpace (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:UsedSpace: ", p), err) }
  return err
}

func (p *GetStorageInfoResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FreeSpace", thrift.I64, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:FreeSpace: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.FreeSpace)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FreeSpace (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:FreeSpace: ", p), err) }
  return err
}

func (p *GetStorageInfoResponse) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "UsagePercent", thrift.DOUBLE, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:UsagePercent: ", p), err) }
  if err := oprot.WriteDouble(ctx, float64(p.UsagePercent)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.UsagePercent (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:UsagePercent: ", p), err) }
  return err
}

func (p *GetStorageInfoResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *GetStorageInfoResponse) Equals(other *GetStorageInfoResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.TotalSpace != other.TotalSpace { return false }
  if p.UsedSpace != other.UsedSpace { return false }
  if p.FreeSpace != other.FreeSpace { return false }
  if p.UsagePercent != other.UsagePercent { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *GetStorageInfoResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetStorageInfoResponse(%+v)", *p)
}

// Attributes:
//  - FileID
//  - Width
//  - Height
type GetThumbnailRequest struct {
  FileID int64 `thrift:"FileID,1" db:"FileID" json:"FileID"`
  Width *int32 `thrift:"Width,2" db:"Width" json:"Width,omitempty"`
  Height *int32 `thrift:"Height,3" db:"Height" json:"Height,omitempty"`
}

func NewGetThumbnailRequest() *GetThumbnailRequest {
  return &GetThumbnailRequest{}
}


func (p *GetThumbnailRequest) GetFileID() int64 {
  return p.FileID
}
var GetThumbnailRequest_Width_DEFAULT int32
func (p *GetThumbnailRequest) GetWidth() int32 {
  if !p.IsSetWidth() {
    return GetThumbnailRequest_Width_DEFAULT
  }
return *p.Width
}
var GetThumbnailRequest_Height_DEFAULT int32
func (p *GetThumbnailRequest) GetHeight() int32 {
  if !p.IsSetHeight() {
    return GetThumbnailRequest_Height_DEFAULT
  }
return *p.Height
}
func (p *GetThumbnailRequest) IsSetWidth() bool {
  return p.Width != nil
}

func (p *GetThumbnailRequest) IsSetHeight() bool {
  return p.Height != nil
}

func (p *GetThumbnailRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetThumbnailRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.FileID = v
}
  return nil
}

func (p *GetThumbnailRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Width = &v
}
  return nil
}

func (p *GetThumbnailRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Height = &v
}
  return nil
}

func (p *GetThumbnailRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetThumbnailRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetThumbnailRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileID", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:FileID: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.FileID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileID (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:FileID: ", p), err) }
  return err
}

func (p *GetThumbnailRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetWidth() {
    if err := oprot.WriteFieldBegin(ctx, "Width", thrift.I32, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Width: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Width)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Width (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Width: ", p), err) }
  }
  return err
}

func (p *GetThumbnailRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetHeight() {
    if err := oprot.WriteFieldBegin(ctx, "Height", thrift.I32, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Height: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Height)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Height (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Height: ", p), err) }
  }
  return err
}

func (p *GetThumbnailRequest) Equals(other *GetThumbnailRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.FileID != other.FileID { return false }
  if p.Width != other.Width {
    if p.Width == nil || other.Width == nil {
      return false
    }
    if (*p.Width) != (*other.Width) { return false }
  }
  if p.Height != other.Height {
    if p.Height == nil || other.Height == nil {
      return false
    }
    if (*p.Height) != (*other.Height) { return false }
  }
  return true
}

func (p *GetThumbnailRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetThumbnailRequest(%+v)", *p)
}

// Attributes:
//  - ThumbnailData
//  - MimeType
//  - BaseResp
type GetThumbnailResponse struct {
  ThumbnailData []byte `thrift:"ThumbnailData,1" db:"ThumbnailData" json:"ThumbnailData"`
  MimeType string `thrift:"MimeType,2" db:"MimeType" json:"MimeType"`
  // unused fields # 3 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewGetThumbnailResponse() *GetThumbnailResponse {
  return &GetThumbnailResponse{}
}


func (p *GetThumbnailResponse) GetThumbnailData() []byte {
  return p.ThumbnailData
}

func (p *GetThumbnailResponse) GetMimeType() string {
  return p.MimeType
}
var GetThumbnailResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *GetThumbnailResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return GetThumbnailResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *GetThumbnailResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *GetThumbnailResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetThumbnailResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBinary(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ThumbnailData = v
}
  return nil
}

func (p *GetThumbnailResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.MimeType = v
}
  return nil
}

func (p *GetThumbnailResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *GetThumbnailResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetThumbnailResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetThumbnailResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ThumbnailData", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ThumbnailData: ", p), err) }
  if err := oprot.WriteBinary(ctx, p.ThumbnailData); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ThumbnailData (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ThumbnailData: ", p), err) }
  return err
}

func (p *GetThumbnailResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "MimeType", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:MimeType: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.MimeType)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.MimeType (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:MimeType: ", p), err) }
  return err
}

func (p *GetThumbnailResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *GetThumbnailResponse) Equals(other *GetThumbnailResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if bytes.Compare(p.ThumbnailData, other.ThumbnailData) != 0 { return false }
  if p.MimeType != other.MimeType { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *GetThumbnailResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetThumbnailResponse(%+v)", *p)
}

// Attributes:
//  - FileID
//  - Width
//  - Height
type GenerateThumbnailRequest struct {
  FileID int64 `thrift:"FileID,1" db:"FileID" json:"FileID"`
  Width *int32 `thrift:"Width,2" db:"Width" json:"Width,omitempty"`
  Height *int32 `thrift:"Height,3" db:"Height" json:"Height,omitempty"`
}

func NewGenerateThumbnailRequest() *GenerateThumbnailRequest {
  return &GenerateThumbnailRequest{}
}


func (p *GenerateThumbnailRequest) GetFileID() int64 {
  return p.FileID
}
var GenerateThumbnailRequest_Width_DEFAULT int32
func (p *GenerateThumbnailRequest) GetWidth() int32 {
  if !p.IsSetWidth() {
    return GenerateThumbnailRequest_Width_DEFAULT
  }
return *p.Width
}
var GenerateThumbnailRequest_Height_DEFAULT int32
func (p *GenerateThumbnailRequest) GetHeight() int32 {
  if !p.IsSetHeight() {
    return GenerateThumbnailRequest_Height_DEFAULT
  }
return *p.Height
}
func (p *GenerateThumbnailRequest) IsSetWidth() bool {
  return p.Width != nil
}

func (p *GenerateThumbnailRequest) IsSetHeight() bool {
  return p.Height != nil
}

func (p *GenerateThumbnailRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I32 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GenerateThumbnailRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.FileID = v
}
  return nil
}

func (p *GenerateThumbnailRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Width = &v
}
  return nil
}

func (p *GenerateThumbnailRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI32(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Height = &v
}
  return nil
}

func (p *GenerateThumbnailRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GenerateThumbnailRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GenerateThumbnailRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FileID", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:FileID: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.FileID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FileID (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:FileID: ", p), err) }
  return err
}

func (p *GenerateThumbnailRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetWidth() {
    if err := oprot.WriteFieldBegin(ctx, "Width", thrift.I32, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Width: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Width)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Width (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Width: ", p), err) }
  }
  return err
}

func (p *GenerateThumbnailRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetHeight() {
    if err := oprot.WriteFieldBegin(ctx, "Height", thrift.I32, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Height: ", p), err) }
    if err := oprot.WriteI32(ctx, int32(*p.Height)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Height (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Height: ", p), err) }
  }
  return err
}

func (p *GenerateThumbnailRequest) Equals(other *GenerateThumbnailRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.FileID != other.FileID { return false }
  if p.Width != other.Width {
    if p.Width == nil || other.Width == nil {
      return false
    }
    if (*p.Width) != (*other.Width) { return false }
  }
  if p.Height != other.Height {
    if p.Height == nil || other.Height == nil {
      return false
    }
    if (*p.Height) != (*other.Height) { return false }
  }
  return true
}

func (p *GenerateThumbnailRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GenerateThumbnailRequest(%+v)", *p)
}

// Attributes:
//  - ThumbnailPath
//  - BaseResp
type GenerateThumbnailResponse struct {
  ThumbnailPath string `thrift:"ThumbnailPath,1" db:"ThumbnailPath" json:"ThumbnailPath"`
  // unused fields # 2 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewGenerateThumbnailResponse() *GenerateThumbnailResponse {
  return &GenerateThumbnailResponse{}
}


func (p *GenerateThumbnailResponse) GetThumbnailPath() string {
  return p.ThumbnailPath
}
var GenerateThumbnailResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *GenerateThumbnailResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return GenerateThumbnailResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *GenerateThumbnailResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *GenerateThumbnailResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GenerateThumbnailResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ThumbnailPath = v
}
  return nil
}

func (p *GenerateThumbnailResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *GenerateThumbnailResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GenerateThumbnailResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GenerateThumbnailResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ThumbnailPath", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ThumbnailPath: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.ThumbnailPath)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ThumbnailPath (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ThumbnailPath: ", p), err) }
  return err
}

func (p *GenerateThumbnailResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *GenerateThumbnailResponse) Equals(other *GenerateThumbnailResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ThumbnailPath != other.ThumbnailPath { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *GenerateThumbnailResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GenerateThumbnailResponse(%+v)", *p)
}

// Attributes:
//  - TaskID
//  - UploadedBytes
//  - TotalBytes
//  - Progress
//  - Base
type UploadProgressRequest struct {
  TaskID int64 `thrift:"TaskID,1" db:"TaskID" json:"TaskID"`
  UploadedBytes int64 `thrift:"UploadedBytes,2" db:"UploadedBytes" json:"UploadedBytes"`
  TotalBytes int64 `thrift:"TotalBytes,3" db:"TotalBytes" json:"TotalBytes"`
  Progress float64 `thrift:"Progress,4" db:"Progress" json:"Progress"`
  // unused fields # 5 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewUploadProgressRequest() *UploadProgressRequest {
  return &UploadProgressRequest{}
}


func (p *UploadProgressRequest) GetTaskID() int64 {
  return p.TaskID
}

func (p *UploadProgressRequest) GetUploadedBytes() int64 {
  return p.UploadedBytes
}

func (p *UploadProgressRequest) GetTotalBytes() int64 {
  return p.TotalBytes
}

func (p *UploadProgressRequest) GetProgress() float64 {
  return p.Progress
}
var UploadProgressRequest_Base_DEFAULT *base.Base
func (p *UploadProgressRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return UploadProgressRequest_Base_DEFAULT
  }
return p.Base
}
func (p *UploadProgressRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *UploadProgressRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.DOUBLE {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *UploadProgressRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.TaskID = v
}
  return nil
}

func (p *UploadProgressRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.UploadedBytes = v
}
  return nil
}

func (p *UploadProgressRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.TotalBytes = v
}
  return nil
}

func (p *UploadProgressRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadDouble(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Progress = v
}
  return nil
}

func (p *UploadProgressRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *UploadProgressRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UploadProgressRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UploadProgressRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "TaskID", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:TaskID: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.TaskID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.TaskID (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:TaskID: ", p), err) }
  return err
}

func (p *UploadProgressRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "UploadedBytes", thrift.I64, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:UploadedBytes: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.UploadedBytes)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.UploadedBytes (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:UploadedBytes: ", p), err) }
  return err
}

func (p *UploadProgressRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "TotalBytes", thrift.I64, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:TotalBytes: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.TotalBytes)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.TotalBytes (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:TotalBytes: ", p), err) }
  return err
}

func (p *UploadProgressRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Progress", thrift.DOUBLE, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:Progress: ", p), err) }
  if err := oprot.WriteDouble(ctx, float64(p.Progress)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Progress (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:Progress: ", p), err) }
  return err
}

func (p *UploadProgressRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *UploadProgressRequest) Equals(other *UploadProgressRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.TaskID != other.TaskID { return false }
  if p.UploadedBytes != other.UploadedBytes { return false }
  if p.TotalBytes != other.TotalBytes { return false }
  if p.Progress != other.Progress { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *UploadProgressRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UploadProgressRequest(%+v)", *p)
}

// Attributes:
//  - BaseResp
type UploadProgressResponse struct {
  // unused fields # 1 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewUploadProgressResponse() *UploadProgressResponse {
  return &UploadProgressResponse{}
}

var UploadProgressResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *UploadProgressResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return UploadProgressResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *UploadProgressResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *UploadProgressResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *UploadProgressResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *UploadProgressResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UploadProgressResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UploadProgressResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *UploadProgressResponse) Equals(other *UploadProgressResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *UploadProgressResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UploadProgressResponse(%+v)", *p)
}

type Drive interface {
  // Parameters:
  //  - Req
  GetFileList(ctx context.Context, req *GetFileListRequest) (_r *GetFileListResponse, _err error)
  // Parameters:
  //  - Req
  CreateFolder(ctx context.Context, req *CreateFolderRequest) (_r *CreateFolderResponse, _err error)
  // Parameters:
  //  - Req
  UploadFile(ctx context.Context, req *UploadFileRequest) (_r *UploadFileResponse, _err error)
  // Parameters:
  //  - Req
  DownloadFile(ctx context.Context, req *DownloadFileRequest) (_r *DownloadFileResponse, _err error)
  // Parameters:
  //  - Req
  DeleteFile(ctx context.Context, req *DeleteFileRequest) (_r *DeleteFileResponse, _err error)
  // Parameters:
  //  - Req
  RenameFile(ctx context.Context, req *RenameFileRequest) (_r *RenameFileResponse, _err error)
  // Parameters:
  //  - Req
  MoveFile(ctx context.Context, req *MoveFileRequest) (_r *MoveFileResponse, _err error)
  // Parameters:
  //  - Req
  InitChunkUpload(ctx context.Context, req *InitChunkUploadRequest) (_r *InitChunkUploadResponse, _err error)
  // Parameters:
  //  - Req
  UploadChunk(ctx context.Context, req *UploadChunkRequest) (_r *UploadChunkResponse, _err error)
  // Parameters:
  //  - Req
  CompleteChunkUpload(ctx context.Context, req *CompleteChunkUploadRequest) (_r *CompleteChunkUploadResponse, _err error)
  // Parameters:
  //  - Req
  CreateShare(ctx context.Context, req *CreateShareRequest) (_r *CreateShareResponse, _err error)
  // Parameters:
  //  - Req
  GetShareInfo(ctx context.Context, req *GetShareInfoRequest) (_r *GetShareInfoResponse, _err error)
  // Parameters:
  //  - Req
  DownloadSharedFile(ctx context.Context, req *DownloadSharedFileRequest) (_r *DownloadSharedFileResponse, _err error)
  // Parameters:
  //  - Req
  GetStorageInfo(ctx context.Context, req *GetStorageInfoRequest) (_r *GetStorageInfoResponse, _err error)
  // Parameters:
  //  - Req
  GetThumbnail(ctx context.Context, req *GetThumbnailRequest) (_r *GetThumbnailResponse, _err error)
  // Parameters:
  //  - Req
  GenerateThumbnail(ctx context.Context, req *GenerateThumbnailRequest) (_r *GenerateThumbnailResponse, _err error)
  // Parameters:
  //  - Req
  UploadProgress(ctx context.Context, req *UploadProgressRequest) (_r *UploadProgressResponse, _err error)
}

type DriveClient struct {
  c thrift.TClient
  meta thrift.ResponseMeta
}

func NewDriveClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *DriveClient {
  return &DriveClient{
    c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
  }
}

func NewDriveClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *DriveClient {
  return &DriveClient{
    c: thrift.NewTStandardClient(iprot, oprot),
  }
}

func NewDriveClient(c thrift.TClient) *DriveClient {
  return &DriveClient{
    c: c,
  }
}

func (p *DriveClient) Client_() thrift.TClient {
  return p.c
}

func (p *DriveClient) LastResponseMeta_() thrift.ResponseMeta {
  return p.meta
}

func (p *DriveClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
  p.meta = meta
}

// Parameters:
//  - Req
func (p *DriveClient) GetFileList(ctx context.Context, req *GetFileListRequest) (_r *GetFileListResponse, _err error) {
  var _args2 DriveGetFileListArgs
  _args2.Req = req
  var _result4 DriveGetFileListResult
  var _meta3 thrift.ResponseMeta
  _meta3, _err = p.Client_().Call(ctx, "GetFileList", &_args2, &_result4)
  p.SetLastResponseMeta_(_meta3)
  if _err != nil {
    return
  }
  if _ret5 := _result4.GetSuccess(); _ret5 != nil {
    return _ret5, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "GetFileList failed: unknown result")
}

// Parameters:
//  - Req
func (p *DriveClient) CreateFolder(ctx context.Context, req *CreateFolderRequest) (_r *CreateFolderResponse, _err error) {
  var _args6 DriveCreateFolderArgs
  _args6.Req = req
  var _result8 DriveCreateFolderResult
  var _meta7 thrift.ResponseMeta
  _meta7, _err = p.Client_().Call(ctx, "CreateFolder", &_args6, &_result8)
  p.SetLastResponseMeta_(_meta7)
  if _err != nil {
    return
  }
  if _ret9 := _result8.GetSuccess(); _ret9 != nil {
    return _ret9, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "CreateFolder failed: unknown result")
}

// Parameters:
//  - Req
func (p *DriveClient) UploadFile(ctx context.Context, req *UploadFileRequest) (_r *UploadFileResponse, _err error) {
  var _args10 DriveUploadFileArgs
  _args10.Req = req
  var _result12 DriveUploadFileResult
  var _meta11 thrift.ResponseMeta
  _meta11, _err = p.Client_().Call(ctx, "UploadFile", &_args10, &_result12)
  p.SetLastResponseMeta_(_meta11)
  if _err != nil {
    return
  }
  if _ret13 := _result12.GetSuccess(); _ret13 != nil {
    return _ret13, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "UploadFile failed: unknown result")
}

// Parameters:
//  - Req
func (p *DriveClient) DownloadFile(ctx context.Context, req *DownloadFileRequest) (_r *DownloadFileResponse, _err error) {
  var _args14 DriveDownloadFileArgs
  _args14.Req = req
  var _result16 DriveDownloadFileResult
  var _meta15 thrift.ResponseMeta
  _meta15, _err = p.Client_().Call(ctx, "DownloadFile", &_args14, &_result16)
  p.SetLastResponseMeta_(_meta15)
  if _err != nil {
    return
  }
  if _ret17 := _result16.GetSuccess(); _ret17 != nil {
    return _ret17, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "DownloadFile failed: unknown result")
}

// Parameters:
//  - Req
func (p *DriveClient) DeleteFile(ctx context.Context, req *DeleteFileRequest) (_r *DeleteFileResponse, _err error) {
  var _args18 DriveDeleteFileArgs
  _args18.Req = req
  var _result20 DriveDeleteFileResult
  var _meta19 thrift.ResponseMeta
  _meta19, _err = p.Client_().Call(ctx, "DeleteFile", &_args18, &_result20)
  p.SetLastResponseMeta_(_meta19)
  if _err != nil {
    return
  }
  if _ret21 := _result20.GetSuccess(); _ret21 != nil {
    return _ret21, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "DeleteFile failed: unknown result")
}

// Parameters:
//  - Req
func (p *DriveClient) RenameFile(ctx context.Context, req *RenameFileRequest) (_r *RenameFileResponse, _err error) {
  var _args22 DriveRenameFileArgs
  _args22.Req = req
  var _result24 DriveRenameFileResult
  var _meta23 thrift.ResponseMeta
  _meta23, _err = p.Client_().Call(ctx, "RenameFile", &_args22, &_result24)
  p.SetLastResponseMeta_(_meta23)
  if _err != nil {
    return
  }
  if _ret25 := _result24.GetSuccess(); _ret25 != nil {
    return _ret25, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "RenameFile failed: unknown result")
}

// Parameters:
//  - Req
func (p *DriveClient) MoveFile(ctx context.Context, req *MoveFileRequest) (_r *MoveFileResponse, _err error) {
  var _args26 DriveMoveFileArgs
  _args26.Req = req
  var _result28 DriveMoveFileResult
  var _meta27 thrift.ResponseMeta
  _meta27, _err = p.Client_().Call(ctx, "MoveFile", &_args26, &_result28)
  p.SetLastResponseMeta_(_meta27)
  if _err != nil {
    return
  }
  if _ret29 := _result28.GetSuccess(); _ret29 != nil {
    return _ret29, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "MoveFile failed: unknown result")
}

// Parameters:
//  - Req
func (p *DriveClient) InitChunkUpload(ctx context.Context, req *InitChunkUploadRequest) (_r *InitChunkUploadResponse, _err error) {
  var _args30 DriveInitChunkUploadArgs
  _args30.Req = req
  var _result32 DriveInitChunkUploadResult
  var _meta31 thrift.ResponseMeta
  _meta31, _err = p.Client_().Call(ctx, "InitChunkUpload", &_args30, &_result32)
  p.SetLastResponseMeta_(_meta31)
  if _err != nil {
    return
  }
  if _ret33 := _result32.GetSuccess(); _ret33 != nil {
    return _ret33, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "InitChunkUpload failed: unknown result")
}

// Parameters:
//  - Req
func (p *DriveClient) UploadChunk(ctx context.Context, req *UploadChunkRequest) (_r *UploadChunkResponse, _err error) {
  var _args34 DriveUploadChunkArgs
  _args34.Req = req
  var _result36 DriveUploadChunkResult
  var _meta35 thrift.ResponseMeta
  _meta35, _err = p.Client_().Call(ctx, "UploadChunk", &_args34, &_result36)
  p.SetLastResponseMeta_(_meta35)
  if _err != nil {
    return
  }
  if _ret37 := _result36.GetSuccess(); _ret37 != nil {
    return _ret37, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "UploadChunk failed: unknown result")
}

// Parameters:
//  - Req
func (p *DriveClient) CompleteChunkUpload(ctx context.Context, req *CompleteChunkUploadRequest) (_r *CompleteChunkUploadResponse, _err error) {
  var _args38 DriveCompleteChunkUploadArgs
  _args38.Req = req
  var _result40 DriveCompleteChunkUploadResult
  var _meta39 thrift.ResponseMeta
  _meta39, _err = p.Client_().Call(ctx, "CompleteChunkUpload", &_args38, &_result40)
  p.SetLastResponseMeta_(_meta39)
  if _err != nil {
    return
  }
  if _ret41 := _result40.GetSuccess(); _ret41 != nil {
    return _ret41, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "CompleteChunkUpload failed: unknown result")
}

// Parameters:
//  - Req
func (p *DriveClient) CreateShare(ctx context.Context, req *CreateShareRequest) (_r *CreateShareResponse, _err error) {
  var _args42 DriveCreateShareArgs
  _args42.Req = req
  var _result44 DriveCreateShareResult
  var _meta43 thrift.ResponseMeta
  _meta43, _err = p.Client_().Call(ctx, "CreateShare", &_args42, &_result44)
  p.SetLastResponseMeta_(_meta43)
  if _err != nil {
    return
  }
  if _ret45 := _result44.GetSuccess(); _ret45 != nil {
    return _ret45, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "CreateShare failed: unknown result")
}

// Parameters:
//  - Req
func (p *DriveClient) GetShareInfo(ctx context.Context, req *GetShareInfoRequest) (_r *GetShareInfoResponse, _err error) {
  var _args46 DriveGetShareInfoArgs
  _args46.Req = req
  var _result48 DriveGetShareInfoResult
  var _meta47 thrift.ResponseMeta
  _meta47, _err = p.Client_().Call(ctx, "GetShareInfo", &_args46, &_result48)
  p.SetLastResponseMeta_(_meta47)
  if _err != nil {
    return
  }
  if _ret49 := _result48.GetSuccess(); _ret49 != nil {
    return _ret49, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "GetShareInfo failed: unknown result")
}

// Parameters:
//  - Req
func (p *DriveClient) DownloadSharedFile(ctx context.Context, req *DownloadSharedFileRequest) (_r *DownloadSharedFileResponse, _err error) {
  var _args50 DriveDownloadSharedFileArgs
  _args50.Req = req
  var _result52 DriveDownloadSharedFileResult
  var _meta51 thrift.ResponseMeta
  _meta51, _err = p.Client_().Call(ctx, "DownloadSharedFile", &_args50, &_result52)
  p.SetLastResponseMeta_(_meta51)
  if _err != nil {
    return
  }
  if _ret53 := _result52.GetSuccess(); _ret53 != nil {
    return _ret53, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "DownloadSharedFile failed: unknown result")
}

// Parameters:
//  - Req
func (p *DriveClient) GetStorageInfo(ctx context.Context, req *GetStorageInfoRequest) (_r *GetStorageInfoResponse, _err error) {
  var _args54 DriveGetStorageInfoArgs
  _args54.Req = req
  var _result56 DriveGetStorageInfoResult
  var _meta55 thrift.ResponseMeta
  _meta55, _err = p.Client_().Call(ctx, "GetStorageInfo", &_args54, &_result56)
  p.SetLastResponseMeta_(_meta55)
  if _err != nil {
    return
  }
  if _ret57 := _result56.GetSuccess(); _ret57 != nil {
    return _ret57, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "GetStorageInfo failed: unknown result")
}

// Parameters:
//  - Req
func (p *DriveClient) GetThumbnail(ctx context.Context, req *GetThumbnailRequest) (_r *GetThumbnailResponse, _err error) {
  var _args58 DriveGetThumbnailArgs
  _args58.Req = req
  var _result60 DriveGetThumbnailResult
  var _meta59 thrift.ResponseMeta
  _meta59, _err = p.Client_().Call(ctx, "GetThumbnail", &_args58, &_result60)
  p.SetLastResponseMeta_(_meta59)
  if _err != nil {
    return
  }
  if _ret61 := _result60.GetSuccess(); _ret61 != nil {
    return _ret61, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "GetThumbnail failed: unknown result")
}

// Parameters:
//  - Req
func (p *DriveClient) GenerateThumbnail(ctx context.Context, req *GenerateThumbnailRequest) (_r *GenerateThumbnailResponse, _err error) {
  var _args62 DriveGenerateThumbnailArgs
  _args62.Req = req
  var _result64 DriveGenerateThumbnailResult
  var _meta63 thrift.ResponseMeta
  _meta63, _err = p.Client_().Call(ctx, "GenerateThumbnail", &_args62, &_result64)
  p.SetLastResponseMeta_(_meta63)
  if _err != nil {
    return
  }
  if _ret65 := _result64.GetSuccess(); _ret65 != nil {
    return _ret65, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "GenerateThumbnail failed: unknown result")
}

// Parameters:
//  - Req
func (p *DriveClient) UploadProgress(ctx context.Context, req *UploadProgressRequest) (_r *UploadProgressResponse, _err error) {
  var _args66 DriveUploadProgressArgs
  _args66.Req = req
  var _result68 DriveUploadProgressResult
  var _meta67 thrift.ResponseMeta
  _meta67, _err = p.Client_().Call(ctx, "UploadProgress", &_args66, &_result68)
  p.SetLastResponseMeta_(_meta67)
  if _err != nil {
    return
  }
  if _ret69 := _result68.GetSuccess(); _ret69 != nil {
    return _ret69, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "UploadProgress failed: unknown result")
}

type DriveProcessor struct {
  processorMap map[string]thrift.TProcessorFunction
  handler Drive
}

func (p *DriveProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
  p.processorMap[key] = processor
}

func (p *DriveProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
  processor, ok = p.processorMap[key]
  return processor, ok
}

func (p *DriveProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
  return p.processorMap
}

func NewDriveProcessor(handler Drive) *DriveProcessor {

  self70 := &DriveProcessor{handler:handler, processorMap:make(map[string]thrift.TProcessorFunction)}
  self70.processorMap["GetFileList"] = &driveProcessorGetFileList{handler:handler}
  self70.processorMap["CreateFolder"] = &driveProcessorCreateFolder{handler:handler}
  self70.processorMap["UploadFile"] = &driveProcessorUploadFile{handler:handler}
  self70.processorMap["DownloadFile"] = &driveProcessorDownloadFile{handler:handler}
  self70.processorMap["DeleteFile"] = &driveProcessorDeleteFile{handler:handler}
  self70.processorMap["RenameFile"] = &driveProcessorRenameFile{handler:handler}
  self70.processorMap["MoveFile"] = &driveProcessorMoveFile{handler:handler}
  self70.processorMap["InitChunkUpload"] = &driveProcessorInitChunkUpload{handler:handler}
  self70.processorMap["UploadChunk"] = &driveProcessorUploadChunk{handler:handler}
  self70.processorMap["CompleteChunkUpload"] = &driveProcessorCompleteChunkUpload{handler:handler}
  self70.processorMap["CreateShare"] = &driveProcessorCreateShare{handler:handler}
  self70.processorMap["GetShareInfo"] = &driveProcessorGetShareInfo{handler:handler}
  self70.processorMap["DownloadSharedFile"] = &driveProcessorDownloadSharedFile{handler:handler}
  self70.processorMap["GetStorageInfo"] = &driveProcessorGetStorageInfo{handler:handler}
  self70.processorMap["GetThumbnail"] = &driveProcessorGetThumbnail{handler:handler}
  self70.processorMap["GenerateThumbnail"] = &driveProcessorGenerateThumbnail{handler:handler}
  self70.processorMap["UploadProgress"] = &driveProcessorUploadProgress{handler:handler}
return self70
}

func (p *DriveProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
  if err2 != nil { return false, thrift.WrapTException(err2) }
  if processor, ok := p.GetProcessorFunction(name); ok {
    return processor.Process(ctx, seqId, iprot, oprot)
  }
  iprot.Skip(ctx, thrift.STRUCT)
  iprot.ReadMessageEnd(ctx)
  x71 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function " + name)
  oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
  x71.Write(ctx, oprot)
  oprot.WriteMessageEnd(ctx)
  oprot.Flush(ctx)
  return false, x71

}

type driveProcessorGetFileList struct {
  handler Drive
}

func (p *driveProcessorGetFileList) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := DriveGetFileListArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "GetFileList", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := DriveGetFileListResult{}
  var retval *GetFileListResponse
  if retval, err2 = p.handler.GetFileList(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetFileList: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "GetFileList", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "GetFileList", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type driveProcessorCreateFolder struct {
  handler Drive
}

func (p *driveProcessorCreateFolder) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := DriveCreateFolderArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "CreateFolder", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := DriveCreateFolderResult{}
  var retval *CreateFolderResponse
  if retval, err2 = p.handler.CreateFolder(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing CreateFolder: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "CreateFolder", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "CreateFolder", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type driveProcessorUploadFile struct {
  handler Drive
}

func (p *driveProcessorUploadFile) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := DriveUploadFileArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "UploadFile", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := DriveUploadFileResult{}
  var retval *UploadFileResponse
  if retval, err2 = p.handler.UploadFile(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing UploadFile: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "UploadFile", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "UploadFile", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type driveProcessorDownloadFile struct {
  handler Drive
}

func (p *driveProcessorDownloadFile) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := DriveDownloadFileArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "DownloadFile", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := DriveDownloadFileResult{}
  var retval *DownloadFileResponse
  if retval, err2 = p.handler.DownloadFile(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing DownloadFile: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "DownloadFile", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "DownloadFile", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type driveProcessorDeleteFile struct {
  handler Drive
}

func (p *driveProcessorDeleteFile) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := DriveDeleteFileArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "DeleteFile", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := DriveDeleteFileResult{}
  var retval *DeleteFileResponse
  if retval, err2 = p.handler.DeleteFile(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing DeleteFile: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "DeleteFile", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "DeleteFile", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type driveProcessorRenameFile struct {
  handler Drive
}

func (p *driveProcessorRenameFile) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := DriveRenameFileArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "RenameFile", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := DriveRenameFileResult{}
  var retval *RenameFileResponse
  if retval, err2 = p.handler.RenameFile(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing RenameFile: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "RenameFile", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "RenameFile", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type driveProcessorMoveFile struct {
  handler Drive
}

func (p *driveProcessorMoveFile) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := DriveMoveFileArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "MoveFile", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := DriveMoveFileResult{}
  var retval *MoveFileResponse
  if retval, err2 = p.handler.MoveFile(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing MoveFile: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "MoveFile", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "MoveFile", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type driveProcessorInitChunkUpload struct {
  handler Drive
}

func (p *driveProcessorInitChunkUpload) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := DriveInitChunkUploadArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "InitChunkUpload", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := DriveInitChunkUploadResult{}
  var retval *InitChunkUploadResponse
  if retval, err2 = p.handler.InitChunkUpload(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing InitChunkUpload: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "InitChunkUpload", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "InitChunkUpload", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type driveProcessorUploadChunk struct {
  handler Drive
}

func (p *driveProcessorUploadChunk) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := DriveUploadChunkArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "UploadChunk", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := DriveUploadChunkResult{}
  var retval *UploadChunkResponse
  if retval, err2 = p.handler.UploadChunk(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing UploadChunk: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "UploadChunk", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "UploadChunk", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type driveProcessorCompleteChunkUpload struct {
  handler Drive
}

func (p *driveProcessorCompleteChunkUpload) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := DriveCompleteChunkUploadArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "CompleteChunkUpload", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := DriveCompleteChunkUploadResult{}
  var retval *CompleteChunkUploadResponse
  if retval, err2 = p.handler.CompleteChunkUpload(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing CompleteChunkUpload: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "CompleteChunkUpload", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "CompleteChunkUpload", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type driveProcessorCreateShare struct {
  handler Drive
}

func (p *driveProcessorCreateShare) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := DriveCreateShareArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "CreateShare", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := DriveCreateShareResult{}
  var retval *CreateShareResponse
  if retval, err2 = p.handler.CreateShare(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing CreateShare: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "CreateShare", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "CreateShare", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type driveProcessorGetShareInfo struct {
  handler Drive
}

func (p *driveProcessorGetShareInfo) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := DriveGetShareInfoArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "GetShareInfo", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := DriveGetShareInfoResult{}
  var retval *GetShareInfoResponse
  if retval, err2 = p.handler.GetShareInfo(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetShareInfo: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "GetShareInfo", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "GetShareInfo", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type driveProcessorDownloadSharedFile struct {
  handler Drive
}

func (p *driveProcessorDownloadSharedFile) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := DriveDownloadSharedFileArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "DownloadSharedFile", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := DriveDownloadSharedFileResult{}
  var retval *DownloadSharedFileResponse
  if retval, err2 = p.handler.DownloadSharedFile(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing DownloadSharedFile: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "DownloadSharedFile", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "DownloadSharedFile", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type driveProcessorGetStorageInfo struct {
  handler Drive
}

func (p *driveProcessorGetStorageInfo) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := DriveGetStorageInfoArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "GetStorageInfo", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := DriveGetStorageInfoResult{}
  var retval *GetStorageInfoResponse
  if retval, err2 = p.handler.GetStorageInfo(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetStorageInfo: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "GetStorageInfo", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "GetStorageInfo", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type driveProcessorGetThumbnail struct {
  handler Drive
}

func (p *driveProcessorGetThumbnail) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := DriveGetThumbnailArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "GetThumbnail", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := DriveGetThumbnailResult{}
  var retval *GetThumbnailResponse
  if retval, err2 = p.handler.GetThumbnail(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetThumbnail: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "GetThumbnail", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "GetThumbnail", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type driveProcessorGenerateThumbnail struct {
  handler Drive
}

func (p *driveProcessorGenerateThumbnail) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := DriveGenerateThumbnailArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "GenerateThumbnail", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := DriveGenerateThumbnailResult{}
  var retval *GenerateThumbnailResponse
  if retval, err2 = p.handler.GenerateThumbnail(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GenerateThumbnail: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "GenerateThumbnail", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "GenerateThumbnail", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type driveProcessorUploadProgress struct {
  handler Drive
}

func (p *driveProcessorUploadProgress) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := DriveUploadProgressArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "UploadProgress", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := DriveUploadProgressResult{}
  var retval *UploadProgressResponse
  if retval, err2 = p.handler.UploadProgress(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing UploadProgress: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "UploadProgress", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "UploadProgress", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}


// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//  - Req
type DriveGetFileListArgs struct {
  Req *GetFileListRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewDriveGetFileListArgs() *DriveGetFileListArgs {
  return &DriveGetFileListArgs{}
}

var DriveGetFileListArgs_Req_DEFAULT *GetFileListRequest
func (p *DriveGetFileListArgs) GetReq() *GetFileListRequest {
  if !p.IsSetReq() {
    return DriveGetFileListArgs_Req_DEFAULT
  }
return p.Req
}
func (p *DriveGetFileListArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *DriveGetFileListArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveGetFileListArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &GetFileListRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *DriveGetFileListArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetFileList_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveGetFileListArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *DriveGetFileListArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveGetFileListArgs(%+v)", *p)
}

// Attributes:
//  - Success
type DriveGetFileListResult struct {
  Success *GetFileListResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewDriveGetFileListResult() *DriveGetFileListResult {
  return &DriveGetFileListResult{}
}

var DriveGetFileListResult_Success_DEFAULT *GetFileListResponse
func (p *DriveGetFileListResult) GetSuccess() *GetFileListResponse {
  if !p.IsSetSuccess() {
    return DriveGetFileListResult_Success_DEFAULT
  }
return p.Success
}
func (p *DriveGetFileListResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *DriveGetFileListResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveGetFileListResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &GetFileListResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *DriveGetFileListResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetFileList_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveGetFileListResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *DriveGetFileListResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveGetFileListResult(%+v)", *p)
}

// Attributes:
//  - Req
type DriveCreateFolderArgs struct {
  Req *CreateFolderRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewDriveCreateFolderArgs() *DriveCreateFolderArgs {
  return &DriveCreateFolderArgs{}
}

var DriveCreateFolderArgs_Req_DEFAULT *CreateFolderRequest
func (p *DriveCreateFolderArgs) GetReq() *CreateFolderRequest {
  if !p.IsSetReq() {
    return DriveCreateFolderArgs_Req_DEFAULT
  }
return p.Req
}
func (p *DriveCreateFolderArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *DriveCreateFolderArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveCreateFolderArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &CreateFolderRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *DriveCreateFolderArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CreateFolder_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveCreateFolderArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *DriveCreateFolderArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveCreateFolderArgs(%+v)", *p)
}

// Attributes:
//  - Success
type DriveCreateFolderResult struct {
  Success *CreateFolderResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewDriveCreateFolderResult() *DriveCreateFolderResult {
  return &DriveCreateFolderResult{}
}

var DriveCreateFolderResult_Success_DEFAULT *CreateFolderResponse
func (p *DriveCreateFolderResult) GetSuccess() *CreateFolderResponse {
  if !p.IsSetSuccess() {
    return DriveCreateFolderResult_Success_DEFAULT
  }
return p.Success
}
func (p *DriveCreateFolderResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *DriveCreateFolderResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveCreateFolderResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &CreateFolderResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *DriveCreateFolderResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CreateFolder_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveCreateFolderResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *DriveCreateFolderResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveCreateFolderResult(%+v)", *p)
}

// Attributes:
//  - Req
type DriveUploadFileArgs struct {
  Req *UploadFileRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewDriveUploadFileArgs() *DriveUploadFileArgs {
  return &DriveUploadFileArgs{}
}

var DriveUploadFileArgs_Req_DEFAULT *UploadFileRequest
func (p *DriveUploadFileArgs) GetReq() *UploadFileRequest {
  if !p.IsSetReq() {
    return DriveUploadFileArgs_Req_DEFAULT
  }
return p.Req
}
func (p *DriveUploadFileArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *DriveUploadFileArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveUploadFileArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &UploadFileRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *DriveUploadFileArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UploadFile_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveUploadFileArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *DriveUploadFileArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveUploadFileArgs(%+v)", *p)
}

// Attributes:
//  - Success
type DriveUploadFileResult struct {
  Success *UploadFileResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewDriveUploadFileResult() *DriveUploadFileResult {
  return &DriveUploadFileResult{}
}

var DriveUploadFileResult_Success_DEFAULT *UploadFileResponse
func (p *DriveUploadFileResult) GetSuccess() *UploadFileResponse {
  if !p.IsSetSuccess() {
    return DriveUploadFileResult_Success_DEFAULT
  }
return p.Success
}
func (p *DriveUploadFileResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *DriveUploadFileResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveUploadFileResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &UploadFileResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *DriveUploadFileResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UploadFile_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveUploadFileResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *DriveUploadFileResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveUploadFileResult(%+v)", *p)
}

// Attributes:
//  - Req
type DriveDownloadFileArgs struct {
  Req *DownloadFileRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewDriveDownloadFileArgs() *DriveDownloadFileArgs {
  return &DriveDownloadFileArgs{}
}

var DriveDownloadFileArgs_Req_DEFAULT *DownloadFileRequest
func (p *DriveDownloadFileArgs) GetReq() *DownloadFileRequest {
  if !p.IsSetReq() {
    return DriveDownloadFileArgs_Req_DEFAULT
  }
return p.Req
}
func (p *DriveDownloadFileArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *DriveDownloadFileArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveDownloadFileArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &DownloadFileRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *DriveDownloadFileArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DownloadFile_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveDownloadFileArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *DriveDownloadFileArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveDownloadFileArgs(%+v)", *p)
}

// Attributes:
//  - Success
type DriveDownloadFileResult struct {
  Success *DownloadFileResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewDriveDownloadFileResult() *DriveDownloadFileResult {
  return &DriveDownloadFileResult{}
}

var DriveDownloadFileResult_Success_DEFAULT *DownloadFileResponse
func (p *DriveDownloadFileResult) GetSuccess() *DownloadFileResponse {
  if !p.IsSetSuccess() {
    return DriveDownloadFileResult_Success_DEFAULT
  }
return p.Success
}
func (p *DriveDownloadFileResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *DriveDownloadFileResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveDownloadFileResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &DownloadFileResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *DriveDownloadFileResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DownloadFile_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveDownloadFileResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *DriveDownloadFileResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveDownloadFileResult(%+v)", *p)
}

// Attributes:
//  - Req
type DriveDeleteFileArgs struct {
  Req *DeleteFileRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewDriveDeleteFileArgs() *DriveDeleteFileArgs {
  return &DriveDeleteFileArgs{}
}

var DriveDeleteFileArgs_Req_DEFAULT *DeleteFileRequest
func (p *DriveDeleteFileArgs) GetReq() *DeleteFileRequest {
  if !p.IsSetReq() {
    return DriveDeleteFileArgs_Req_DEFAULT
  }
return p.Req
}
func (p *DriveDeleteFileArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *DriveDeleteFileArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveDeleteFileArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &DeleteFileRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *DriveDeleteFileArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DeleteFile_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveDeleteFileArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *DriveDeleteFileArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveDeleteFileArgs(%+v)", *p)
}

// Attributes:
//  - Success
type DriveDeleteFileResult struct {
  Success *DeleteFileResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewDriveDeleteFileResult() *DriveDeleteFileResult {
  return &DriveDeleteFileResult{}
}

var DriveDeleteFileResult_Success_DEFAULT *DeleteFileResponse
func (p *DriveDeleteFileResult) GetSuccess() *DeleteFileResponse {
  if !p.IsSetSuccess() {
    return DriveDeleteFileResult_Success_DEFAULT
  }
return p.Success
}
func (p *DriveDeleteFileResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *DriveDeleteFileResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveDeleteFileResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &DeleteFileResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *DriveDeleteFileResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DeleteFile_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveDeleteFileResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *DriveDeleteFileResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveDeleteFileResult(%+v)", *p)
}

// Attributes:
//  - Req
type DriveRenameFileArgs struct {
  Req *RenameFileRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewDriveRenameFileArgs() *DriveRenameFileArgs {
  return &DriveRenameFileArgs{}
}

var DriveRenameFileArgs_Req_DEFAULT *RenameFileRequest
func (p *DriveRenameFileArgs) GetReq() *RenameFileRequest {
  if !p.IsSetReq() {
    return DriveRenameFileArgs_Req_DEFAULT
  }
return p.Req
}
func (p *DriveRenameFileArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *DriveRenameFileArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveRenameFileArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &RenameFileRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *DriveRenameFileArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "RenameFile_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveRenameFileArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *DriveRenameFileArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveRenameFileArgs(%+v)", *p)
}

// Attributes:
//  - Success
type DriveRenameFileResult struct {
  Success *RenameFileResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewDriveRenameFileResult() *DriveRenameFileResult {
  return &DriveRenameFileResult{}
}

var DriveRenameFileResult_Success_DEFAULT *RenameFileResponse
func (p *DriveRenameFileResult) GetSuccess() *RenameFileResponse {
  if !p.IsSetSuccess() {
    return DriveRenameFileResult_Success_DEFAULT
  }
return p.Success
}
func (p *DriveRenameFileResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *DriveRenameFileResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveRenameFileResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &RenameFileResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *DriveRenameFileResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "RenameFile_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveRenameFileResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *DriveRenameFileResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveRenameFileResult(%+v)", *p)
}

// Attributes:
//  - Req
type DriveMoveFileArgs struct {
  Req *MoveFileRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewDriveMoveFileArgs() *DriveMoveFileArgs {
  return &DriveMoveFileArgs{}
}

var DriveMoveFileArgs_Req_DEFAULT *MoveFileRequest
func (p *DriveMoveFileArgs) GetReq() *MoveFileRequest {
  if !p.IsSetReq() {
    return DriveMoveFileArgs_Req_DEFAULT
  }
return p.Req
}
func (p *DriveMoveFileArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *DriveMoveFileArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveMoveFileArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &MoveFileRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *DriveMoveFileArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "MoveFile_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveMoveFileArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *DriveMoveFileArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveMoveFileArgs(%+v)", *p)
}

// Attributes:
//  - Success
type DriveMoveFileResult struct {
  Success *MoveFileResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewDriveMoveFileResult() *DriveMoveFileResult {
  return &DriveMoveFileResult{}
}

var DriveMoveFileResult_Success_DEFAULT *MoveFileResponse
func (p *DriveMoveFileResult) GetSuccess() *MoveFileResponse {
  if !p.IsSetSuccess() {
    return DriveMoveFileResult_Success_DEFAULT
  }
return p.Success
}
func (p *DriveMoveFileResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *DriveMoveFileResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveMoveFileResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &MoveFileResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *DriveMoveFileResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "MoveFile_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveMoveFileResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *DriveMoveFileResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveMoveFileResult(%+v)", *p)
}

// Attributes:
//  - Req
type DriveInitChunkUploadArgs struct {
  Req *InitChunkUploadRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewDriveInitChunkUploadArgs() *DriveInitChunkUploadArgs {
  return &DriveInitChunkUploadArgs{}
}

var DriveInitChunkUploadArgs_Req_DEFAULT *InitChunkUploadRequest
func (p *DriveInitChunkUploadArgs) GetReq() *InitChunkUploadRequest {
  if !p.IsSetReq() {
    return DriveInitChunkUploadArgs_Req_DEFAULT
  }
return p.Req
}
func (p *DriveInitChunkUploadArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *DriveInitChunkUploadArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveInitChunkUploadArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &InitChunkUploadRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *DriveInitChunkUploadArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "InitChunkUpload_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveInitChunkUploadArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *DriveInitChunkUploadArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveInitChunkUploadArgs(%+v)", *p)
}

// Attributes:
//  - Success
type DriveInitChunkUploadResult struct {
  Success *InitChunkUploadResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewDriveInitChunkUploadResult() *DriveInitChunkUploadResult {
  return &DriveInitChunkUploadResult{}
}

var DriveInitChunkUploadResult_Success_DEFAULT *InitChunkUploadResponse
func (p *DriveInitChunkUploadResult) GetSuccess() *InitChunkUploadResponse {
  if !p.IsSetSuccess() {
    return DriveInitChunkUploadResult_Success_DEFAULT
  }
return p.Success
}
func (p *DriveInitChunkUploadResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *DriveInitChunkUploadResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveInitChunkUploadResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &InitChunkUploadResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *DriveInitChunkUploadResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "InitChunkUpload_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveInitChunkUploadResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *DriveInitChunkUploadResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveInitChunkUploadResult(%+v)", *p)
}

// Attributes:
//  - Req
type DriveUploadChunkArgs struct {
  Req *UploadChunkRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewDriveUploadChunkArgs() *DriveUploadChunkArgs {
  return &DriveUploadChunkArgs{}
}

var DriveUploadChunkArgs_Req_DEFAULT *UploadChunkRequest
func (p *DriveUploadChunkArgs) GetReq() *UploadChunkRequest {
  if !p.IsSetReq() {
    return DriveUploadChunkArgs_Req_DEFAULT
  }
return p.Req
}
func (p *DriveUploadChunkArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *DriveUploadChunkArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveUploadChunkArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &UploadChunkRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *DriveUploadChunkArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UploadChunk_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveUploadChunkArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *DriveUploadChunkArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveUploadChunkArgs(%+v)", *p)
}

// Attributes:
//  - Success
type DriveUploadChunkResult struct {
  Success *UploadChunkResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewDriveUploadChunkResult() *DriveUploadChunkResult {
  return &DriveUploadChunkResult{}
}

var DriveUploadChunkResult_Success_DEFAULT *UploadChunkResponse
func (p *DriveUploadChunkResult) GetSuccess() *UploadChunkResponse {
  if !p.IsSetSuccess() {
    return DriveUploadChunkResult_Success_DEFAULT
  }
return p.Success
}
func (p *DriveUploadChunkResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *DriveUploadChunkResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveUploadChunkResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &UploadChunkResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *DriveUploadChunkResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UploadChunk_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveUploadChunkResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *DriveUploadChunkResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveUploadChunkResult(%+v)", *p)
}

// Attributes:
//  - Req
type DriveCompleteChunkUploadArgs struct {
  Req *CompleteChunkUploadRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewDriveCompleteChunkUploadArgs() *DriveCompleteChunkUploadArgs {
  return &DriveCompleteChunkUploadArgs{}
}

var DriveCompleteChunkUploadArgs_Req_DEFAULT *CompleteChunkUploadRequest
func (p *DriveCompleteChunkUploadArgs) GetReq() *CompleteChunkUploadRequest {
  if !p.IsSetReq() {
    return DriveCompleteChunkUploadArgs_Req_DEFAULT
  }
return p.Req
}
func (p *DriveCompleteChunkUploadArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *DriveCompleteChunkUploadArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveCompleteChunkUploadArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &CompleteChunkUploadRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *DriveCompleteChunkUploadArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CompleteChunkUpload_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveCompleteChunkUploadArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *DriveCompleteChunkUploadArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveCompleteChunkUploadArgs(%+v)", *p)
}

// Attributes:
//  - Success
type DriveCompleteChunkUploadResult struct {
  Success *CompleteChunkUploadResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewDriveCompleteChunkUploadResult() *DriveCompleteChunkUploadResult {
  return &DriveCompleteChunkUploadResult{}
}

var DriveCompleteChunkUploadResult_Success_DEFAULT *CompleteChunkUploadResponse
func (p *DriveCompleteChunkUploadResult) GetSuccess() *CompleteChunkUploadResponse {
  if !p.IsSetSuccess() {
    return DriveCompleteChunkUploadResult_Success_DEFAULT
  }
return p.Success
}
func (p *DriveCompleteChunkUploadResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *DriveCompleteChunkUploadResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveCompleteChunkUploadResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &CompleteChunkUploadResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *DriveCompleteChunkUploadResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CompleteChunkUpload_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveCompleteChunkUploadResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *DriveCompleteChunkUploadResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveCompleteChunkUploadResult(%+v)", *p)
}

// Attributes:
//  - Req
type DriveCreateShareArgs struct {
  Req *CreateShareRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewDriveCreateShareArgs() *DriveCreateShareArgs {
  return &DriveCreateShareArgs{}
}

var DriveCreateShareArgs_Req_DEFAULT *CreateShareRequest
func (p *DriveCreateShareArgs) GetReq() *CreateShareRequest {
  if !p.IsSetReq() {
    return DriveCreateShareArgs_Req_DEFAULT
  }
return p.Req
}
func (p *DriveCreateShareArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *DriveCreateShareArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveCreateShareArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &CreateShareRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *DriveCreateShareArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CreateShare_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveCreateShareArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *DriveCreateShareArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveCreateShareArgs(%+v)", *p)
}

// Attributes:
//  - Success
type DriveCreateShareResult struct {
  Success *CreateShareResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewDriveCreateShareResult() *DriveCreateShareResult {
  return &DriveCreateShareResult{}
}

var DriveCreateShareResult_Success_DEFAULT *CreateShareResponse
func (p *DriveCreateShareResult) GetSuccess() *CreateShareResponse {
  if !p.IsSetSuccess() {
    return DriveCreateShareResult_Success_DEFAULT
  }
return p.Success
}
func (p *DriveCreateShareResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *DriveCreateShareResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveCreateShareResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &CreateShareResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *DriveCreateShareResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CreateShare_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveCreateShareResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *DriveCreateShareResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveCreateShareResult(%+v)", *p)
}

// Attributes:
//  - Req
type DriveGetShareInfoArgs struct {
  Req *GetShareInfoRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewDriveGetShareInfoArgs() *DriveGetShareInfoArgs {
  return &DriveGetShareInfoArgs{}
}

var DriveGetShareInfoArgs_Req_DEFAULT *GetShareInfoRequest
func (p *DriveGetShareInfoArgs) GetReq() *GetShareInfoRequest {
  if !p.IsSetReq() {
    return DriveGetShareInfoArgs_Req_DEFAULT
  }
return p.Req
}
func (p *DriveGetShareInfoArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *DriveGetShareInfoArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveGetShareInfoArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &GetShareInfoRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *DriveGetShareInfoArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetShareInfo_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveGetShareInfoArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *DriveGetShareInfoArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveGetShareInfoArgs(%+v)", *p)
}

// Attributes:
//  - Success
type DriveGetShareInfoResult struct {
  Success *GetShareInfoResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewDriveGetShareInfoResult() *DriveGetShareInfoResult {
  return &DriveGetShareInfoResult{}
}

var DriveGetShareInfoResult_Success_DEFAULT *GetShareInfoResponse
func (p *DriveGetShareInfoResult) GetSuccess() *GetShareInfoResponse {
  if !p.IsSetSuccess() {
    return DriveGetShareInfoResult_Success_DEFAULT
  }
return p.Success
}
func (p *DriveGetShareInfoResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *DriveGetShareInfoResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveGetShareInfoResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &GetShareInfoResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *DriveGetShareInfoResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetShareInfo_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveGetShareInfoResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *DriveGetShareInfoResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveGetShareInfoResult(%+v)", *p)
}

// Attributes:
//  - Req
type DriveDownloadSharedFileArgs struct {
  Req *DownloadSharedFileRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewDriveDownloadSharedFileArgs() *DriveDownloadSharedFileArgs {
  return &DriveDownloadSharedFileArgs{}
}

var DriveDownloadSharedFileArgs_Req_DEFAULT *DownloadSharedFileRequest
func (p *DriveDownloadSharedFileArgs) GetReq() *DownloadSharedFileRequest {
  if !p.IsSetReq() {
    return DriveDownloadSharedFileArgs_Req_DEFAULT
  }
return p.Req
}
func (p *DriveDownloadSharedFileArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *DriveDownloadSharedFileArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveDownloadSharedFileArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &DownloadSharedFileRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *DriveDownloadSharedFileArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DownloadSharedFile_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveDownloadSharedFileArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *DriveDownloadSharedFileArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveDownloadSharedFileArgs(%+v)", *p)
}

// Attributes:
//  - Success
type DriveDownloadSharedFileResult struct {
  Success *DownloadSharedFileResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewDriveDownloadSharedFileResult() *DriveDownloadSharedFileResult {
  return &DriveDownloadSharedFileResult{}
}

var DriveDownloadSharedFileResult_Success_DEFAULT *DownloadSharedFileResponse
func (p *DriveDownloadSharedFileResult) GetSuccess() *DownloadSharedFileResponse {
  if !p.IsSetSuccess() {
    return DriveDownloadSharedFileResult_Success_DEFAULT
  }
return p.Success
}
func (p *DriveDownloadSharedFileResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *DriveDownloadSharedFileResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveDownloadSharedFileResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &DownloadSharedFileResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *DriveDownloadSharedFileResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DownloadSharedFile_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveDownloadSharedFileResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *DriveDownloadSharedFileResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveDownloadSharedFileResult(%+v)", *p)
}

// Attributes:
//  - Req
type DriveGetStorageInfoArgs struct {
  Req *GetStorageInfoRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewDriveGetStorageInfoArgs() *DriveGetStorageInfoArgs {
  return &DriveGetStorageInfoArgs{}
}

var DriveGetStorageInfoArgs_Req_DEFAULT *GetStorageInfoRequest
func (p *DriveGetStorageInfoArgs) GetReq() *GetStorageInfoRequest {
  if !p.IsSetReq() {
    return DriveGetStorageInfoArgs_Req_DEFAULT
  }
return p.Req
}
func (p *DriveGetStorageInfoArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *DriveGetStorageInfoArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveGetStorageInfoArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &GetStorageInfoRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *DriveGetStorageInfoArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetStorageInfo_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveGetStorageInfoArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *DriveGetStorageInfoArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveGetStorageInfoArgs(%+v)", *p)
}

// Attributes:
//  - Success
type DriveGetStorageInfoResult struct {
  Success *GetStorageInfoResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewDriveGetStorageInfoResult() *DriveGetStorageInfoResult {
  return &DriveGetStorageInfoResult{}
}

var DriveGetStorageInfoResult_Success_DEFAULT *GetStorageInfoResponse
func (p *DriveGetStorageInfoResult) GetSuccess() *GetStorageInfoResponse {
  if !p.IsSetSuccess() {
    return DriveGetStorageInfoResult_Success_DEFAULT
  }
return p.Success
}
func (p *DriveGetStorageInfoResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *DriveGetStorageInfoResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveGetStorageInfoResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &GetStorageInfoResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *DriveGetStorageInfoResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetStorageInfo_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveGetStorageInfoResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *DriveGetStorageInfoResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveGetStorageInfoResult(%+v)", *p)
}

// Attributes:
//  - Req
type DriveGetThumbnailArgs struct {
  Req *GetThumbnailRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewDriveGetThumbnailArgs() *DriveGetThumbnailArgs {
  return &DriveGetThumbnailArgs{}
}

var DriveGetThumbnailArgs_Req_DEFAULT *GetThumbnailRequest
func (p *DriveGetThumbnailArgs) GetReq() *GetThumbnailRequest {
  if !p.IsSetReq() {
    return DriveGetThumbnailArgs_Req_DEFAULT
  }
return p.Req
}
func (p *DriveGetThumbnailArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *DriveGetThumbnailArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveGetThumbnailArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &GetThumbnailRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *DriveGetThumbnailArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetThumbnail_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveGetThumbnailArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *DriveGetThumbnailArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveGetThumbnailArgs(%+v)", *p)
}

// Attributes:
//  - Success
type DriveGetThumbnailResult struct {
  Success *GetThumbnailResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewDriveGetThumbnailResult() *DriveGetThumbnailResult {
  return &DriveGetThumbnailResult{}
}

var DriveGetThumbnailResult_Success_DEFAULT *GetThumbnailResponse
func (p *DriveGetThumbnailResult) GetSuccess() *GetThumbnailResponse {
  if !p.IsSetSuccess() {
    return DriveGetThumbnailResult_Success_DEFAULT
  }
return p.Success
}
func (p *DriveGetThumbnailResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *DriveGetThumbnailResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveGetThumbnailResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &GetThumbnailResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *DriveGetThumbnailResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetThumbnail_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveGetThumbnailResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *DriveGetThumbnailResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveGetThumbnailResult(%+v)", *p)
}

// Attributes:
//  - Req
type DriveGenerateThumbnailArgs struct {
  Req *GenerateThumbnailRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewDriveGenerateThumbnailArgs() *DriveGenerateThumbnailArgs {
  return &DriveGenerateThumbnailArgs{}
}

var DriveGenerateThumbnailArgs_Req_DEFAULT *GenerateThumbnailRequest
func (p *DriveGenerateThumbnailArgs) GetReq() *GenerateThumbnailRequest {
  if !p.IsSetReq() {
    return DriveGenerateThumbnailArgs_Req_DEFAULT
  }
return p.Req
}
func (p *DriveGenerateThumbnailArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *DriveGenerateThumbnailArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveGenerateThumbnailArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &GenerateThumbnailRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *DriveGenerateThumbnailArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GenerateThumbnail_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveGenerateThumbnailArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *DriveGenerateThumbnailArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveGenerateThumbnailArgs(%+v)", *p)
}

// Attributes:
//  - Success
type DriveGenerateThumbnailResult struct {
  Success *GenerateThumbnailResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewDriveGenerateThumbnailResult() *DriveGenerateThumbnailResult {
  return &DriveGenerateThumbnailResult{}
}

var DriveGenerateThumbnailResult_Success_DEFAULT *GenerateThumbnailResponse
func (p *DriveGenerateThumbnailResult) GetSuccess() *GenerateThumbnailResponse {
  if !p.IsSetSuccess() {
    return DriveGenerateThumbnailResult_Success_DEFAULT
  }
return p.Success
}
func (p *DriveGenerateThumbnailResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *DriveGenerateThumbnailResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveGenerateThumbnailResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &GenerateThumbnailResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *DriveGenerateThumbnailResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GenerateThumbnail_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveGenerateThumbnailResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *DriveGenerateThumbnailResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveGenerateThumbnailResult(%+v)", *p)
}

// Attributes:
//  - Req
type DriveUploadProgressArgs struct {
  Req *UploadProgressRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewDriveUploadProgressArgs() *DriveUploadProgressArgs {
  return &DriveUploadProgressArgs{}
}

var DriveUploadProgressArgs_Req_DEFAULT *UploadProgressRequest
func (p *DriveUploadProgressArgs) GetReq() *UploadProgressRequest {
  if !p.IsSetReq() {
    return DriveUploadProgressArgs_Req_DEFAULT
  }
return p.Req
}
func (p *DriveUploadProgressArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *DriveUploadProgressArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveUploadProgressArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &UploadProgressRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *DriveUploadProgressArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UploadProgress_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveUploadProgressArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *DriveUploadProgressArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveUploadProgressArgs(%+v)", *p)
}

// Attributes:
//  - Success
type DriveUploadProgressResult struct {
  Success *UploadProgressResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewDriveUploadProgressResult() *DriveUploadProgressResult {
  return &DriveUploadProgressResult{}
}

var DriveUploadProgressResult_Success_DEFAULT *UploadProgressResponse
func (p *DriveUploadProgressResult) GetSuccess() *UploadProgressResponse {
  if !p.IsSetSuccess() {
    return DriveUploadProgressResult_Success_DEFAULT
  }
return p.Success
}
func (p *DriveUploadProgressResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *DriveUploadProgressResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DriveUploadProgressResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &UploadProgressResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *DriveUploadProgressResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UploadProgress_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DriveUploadProgressResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *DriveUploadProgressResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DriveUploadProgressResult(%+v)", *p)
}


