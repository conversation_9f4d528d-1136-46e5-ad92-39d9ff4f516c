// Code generated by Thrift Compiler (0.16.0). DO NOT EDIT.

package main

import (
	"context"
	"flag"
	"fmt"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"thrift-common/gen-go/base"
	"favorites"
)

var _ = base.GoUnusedProtection__
var _ = favorites.GoUnusedProtection__

func Usage() {
  fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
  flag.PrintDefaults()
  fmt.Fprintln(os.Stderr, "\nFunctions:")
  fmt.Fprintln(os.Stderr, "  CallbackDelayFetchResponse CallbackDelayFetch(CallbackDelayFetchRequest req)")
  fmt.Fprintln(os.<PERSON>r, "  CallbackSyncDataResponse CallbackSyncData(CallbackSyncDataRequest req)")
  fmt.Fprintln(os.Stderr, "  AddToFavoritesResponse AddToFavorites(AddToFavoritesRequest req)")
  fmt.Fprintln(os.Stderr, "  GetFavoritesPageResponse GetFavoritesPage(GetFavoritesPageRequest req)")
  fmt.Fprintln(os.Stderr, "  GetDiskInfoResponse GetDiskInfo(GetDiskInfoRequest req)")
  fmt.Fprintln(os.Stderr, "  DeleteFavoriteResponse DeleteFavorite(DeleteFavoriteRequest req)")
  fmt.Fprintln(os.Stderr)
  os.Exit(0)
}

type httpHeaders map[string]string

func (h httpHeaders) String() string {
  var m map[string]string = h
  return fmt.Sprintf("%s", m)
}

func (h httpHeaders) Set(value string) error {
  parts := strings.Split(value, ": ")
  if len(parts) != 2 {
    return fmt.Errorf("header should be of format 'Key: Value'")
  }
  h[parts[0]] = parts[1]
  return nil
}

func main() {
  flag.Usage = Usage
  var host string
  var port int
  var protocol string
  var urlString string
  var framed bool
  var useHttp bool
  headers := make(httpHeaders)
  var parsedUrl *url.URL
  var trans thrift.TTransport
  _ = strconv.Atoi
  _ = math.Abs
  flag.Usage = Usage
  flag.StringVar(&host, "h", "localhost", "Specify host and port")
  flag.IntVar(&port, "p", 9090, "Specify port")
  flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
  flag.StringVar(&urlString, "u", "", "Specify the url")
  flag.BoolVar(&framed, "framed", false, "Use framed transport")
  flag.BoolVar(&useHttp, "http", false, "Use http")
  flag.Var(headers, "H", "Headers to set on the http(s) request (e.g. -H \"Key: Value\")")
  flag.Parse()
  
  if len(urlString) > 0 {
    var err error
    parsedUrl, err = url.Parse(urlString)
    if err != nil {
      fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
      flag.Usage()
    }
    host = parsedUrl.Host
    useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http" || parsedUrl.Scheme == "https"
  } else if useHttp {
    _, err := url.Parse(fmt.Sprint("http://", host, ":", port))
    if err != nil {
      fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
      flag.Usage()
    }
  }
  
  cmd := flag.Arg(0)
  var err error
  var cfg *thrift.TConfiguration = nil
  if useHttp {
    trans, err = thrift.NewTHttpClient(parsedUrl.String())
    if len(headers) > 0 {
      httptrans := trans.(*thrift.THttpClient)
      for key, value := range headers {
        httptrans.SetHeader(key, value)
      }
    }
  } else {
    portStr := fmt.Sprint(port)
    if strings.Contains(host, ":") {
           host, portStr, err = net.SplitHostPort(host)
           if err != nil {
                   fmt.Fprintln(os.Stderr, "error with host:", err)
                   os.Exit(1)
           }
    }
    trans = thrift.NewTSocketConf(net.JoinHostPort(host, portStr), cfg)
    if err != nil {
      fmt.Fprintln(os.Stderr, "error resolving address:", err)
      os.Exit(1)
    }
    if framed {
      trans = thrift.NewTFramedTransportConf(trans, cfg)
    }
  }
  if err != nil {
    fmt.Fprintln(os.Stderr, "Error creating transport", err)
    os.Exit(1)
  }
  defer trans.Close()
  var protocolFactory thrift.TProtocolFactory
  switch protocol {
  case "compact":
    protocolFactory = thrift.NewTCompactProtocolFactoryConf(cfg)
    break
  case "simplejson":
    protocolFactory = thrift.NewTSimpleJSONProtocolFactoryConf(cfg)
    break
  case "json":
    protocolFactory = thrift.NewTJSONProtocolFactory()
    break
  case "binary", "":
    protocolFactory = thrift.NewTBinaryProtocolFactoryConf(cfg)
    break
  default:
    fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
    Usage()
    os.Exit(1)
  }
  iprot := protocolFactory.GetProtocol(trans)
  oprot := protocolFactory.GetProtocol(trans)
  client := favorites.NewFavoritesClient(thrift.NewTStandardClient(iprot, oprot))
  if err := trans.Open(); err != nil {
    fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
    os.Exit(1)
  }
  
  switch cmd {
  case "CallbackDelayFetch":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "CallbackDelayFetch requires 1 args")
      flag.Usage()
    }
    arg34 := flag.Arg(1)
    mbTrans35 := thrift.NewTMemoryBufferLen(len(arg34))
    defer mbTrans35.Close()
    _, err36 := mbTrans35.WriteString(arg34)
    if err36 != nil {
      Usage()
      return
    }
    factory37 := thrift.NewTJSONProtocolFactory()
    jsProt38 := factory37.GetProtocol(mbTrans35)
    argvalue0 := favorites.NewCallbackDelayFetchRequest()
    err39 := argvalue0.Read(context.Background(), jsProt38)
    if err39 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.CallbackDelayFetch(context.Background(), value0))
    fmt.Print("\n")
    break
  case "CallbackSyncData":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "CallbackSyncData requires 1 args")
      flag.Usage()
    }
    arg40 := flag.Arg(1)
    mbTrans41 := thrift.NewTMemoryBufferLen(len(arg40))
    defer mbTrans41.Close()
    _, err42 := mbTrans41.WriteString(arg40)
    if err42 != nil {
      Usage()
      return
    }
    factory43 := thrift.NewTJSONProtocolFactory()
    jsProt44 := factory43.GetProtocol(mbTrans41)
    argvalue0 := favorites.NewCallbackSyncDataRequest()
    err45 := argvalue0.Read(context.Background(), jsProt44)
    if err45 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.CallbackSyncData(context.Background(), value0))
    fmt.Print("\n")
    break
  case "AddToFavorites":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "AddToFavorites requires 1 args")
      flag.Usage()
    }
    arg46 := flag.Arg(1)
    mbTrans47 := thrift.NewTMemoryBufferLen(len(arg46))
    defer mbTrans47.Close()
    _, err48 := mbTrans47.WriteString(arg46)
    if err48 != nil {
      Usage()
      return
    }
    factory49 := thrift.NewTJSONProtocolFactory()
    jsProt50 := factory49.GetProtocol(mbTrans47)
    argvalue0 := favorites.NewAddToFavoritesRequest()
    err51 := argvalue0.Read(context.Background(), jsProt50)
    if err51 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.AddToFavorites(context.Background(), value0))
    fmt.Print("\n")
    break
  case "GetFavoritesPage":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetFavoritesPage requires 1 args")
      flag.Usage()
    }
    arg52 := flag.Arg(1)
    mbTrans53 := thrift.NewTMemoryBufferLen(len(arg52))
    defer mbTrans53.Close()
    _, err54 := mbTrans53.WriteString(arg52)
    if err54 != nil {
      Usage()
      return
    }
    factory55 := thrift.NewTJSONProtocolFactory()
    jsProt56 := factory55.GetProtocol(mbTrans53)
    argvalue0 := favorites.NewGetFavoritesPageRequest()
    err57 := argvalue0.Read(context.Background(), jsProt56)
    if err57 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetFavoritesPage(context.Background(), value0))
    fmt.Print("\n")
    break
  case "GetDiskInfo":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetDiskInfo requires 1 args")
      flag.Usage()
    }
    arg58 := flag.Arg(1)
    mbTrans59 := thrift.NewTMemoryBufferLen(len(arg58))
    defer mbTrans59.Close()
    _, err60 := mbTrans59.WriteString(arg58)
    if err60 != nil {
      Usage()
      return
    }
    factory61 := thrift.NewTJSONProtocolFactory()
    jsProt62 := factory61.GetProtocol(mbTrans59)
    argvalue0 := favorites.NewGetDiskInfoRequest()
    err63 := argvalue0.Read(context.Background(), jsProt62)
    if err63 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetDiskInfo(context.Background(), value0))
    fmt.Print("\n")
    break
  case "DeleteFavorite":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "DeleteFavorite requires 1 args")
      flag.Usage()
    }
    arg64 := flag.Arg(1)
    mbTrans65 := thrift.NewTMemoryBufferLen(len(arg64))
    defer mbTrans65.Close()
    _, err66 := mbTrans65.WriteString(arg64)
    if err66 != nil {
      Usage()
      return
    }
    factory67 := thrift.NewTJSONProtocolFactory()
    jsProt68 := factory67.GetProtocol(mbTrans65)
    argvalue0 := favorites.NewDeleteFavoriteRequest()
    err69 := argvalue0.Read(context.Background(), jsProt68)
    if err69 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.DeleteFavorite(context.Background(), value0))
    fmt.Print("\n")
    break
  case "":
    Usage()
    break
  default:
    fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
  }
}
