// Code generated by Thrift Compiler (0.16.0). DO NOT EDIT.

package favorites

import (
	"bytes"
	"context"
	"fmt"
	"time"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"thrift-common/gen-go/base"

)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

var _ = base.GoUnusedProtection__
// Attributes:
//  - SyncMetaTable
//  - CloudDbTunnel
//  - Base
type CallbackDelayFetchRequest struct {
  SyncMetaTable string `thrift:"SyncMetaTable,1" db:"SyncMetaTable" json:"sync_meta_table"`
  CloudDbTunnel string `thrift:"CloudDbTunnel,2" db:"CloudDbTunnel" json:"cloud_db_tunnel"`
  // unused fields # 3 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewCallbackDelayFetchRequest() *CallbackDelayFetchRequest {
  return &CallbackDelayFetchRequest{}
}


func (p *CallbackDelayFetchRequest) GetSyncMetaTable() string {
  return p.SyncMetaTable
}

func (p *CallbackDelayFetchRequest) GetCloudDbTunnel() string {
  return p.CloudDbTunnel
}
var CallbackDelayFetchRequest_Base_DEFAULT *base.Base
func (p *CallbackDelayFetchRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return CallbackDelayFetchRequest_Base_DEFAULT
  }
return p.Base
}
func (p *CallbackDelayFetchRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *CallbackDelayFetchRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CallbackDelayFetchRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.SyncMetaTable = v
}
  return nil
}

func (p *CallbackDelayFetchRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.CloudDbTunnel = v
}
  return nil
}

func (p *CallbackDelayFetchRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *CallbackDelayFetchRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CallbackDelayFetchRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CallbackDelayFetchRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "SyncMetaTable", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:SyncMetaTable: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.SyncMetaTable)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.SyncMetaTable (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:SyncMetaTable: ", p), err) }
  return err
}

func (p *CallbackDelayFetchRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "CloudDbTunnel", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:CloudDbTunnel: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.CloudDbTunnel)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.CloudDbTunnel (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:CloudDbTunnel: ", p), err) }
  return err
}

func (p *CallbackDelayFetchRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *CallbackDelayFetchRequest) Equals(other *CallbackDelayFetchRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.SyncMetaTable != other.SyncMetaTable { return false }
  if p.CloudDbTunnel != other.CloudDbTunnel { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *CallbackDelayFetchRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CallbackDelayFetchRequest(%+v)", *p)
}

// Attributes:
//  - ResultInfo
//  - BaseResp
type CallbackDelayFetchResponse struct {
  ResultInfo map[string]string `thrift:"ResultInfo,1" db:"ResultInfo" json:"ResultInfo"`
  // unused fields # 2 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewCallbackDelayFetchResponse() *CallbackDelayFetchResponse {
  return &CallbackDelayFetchResponse{}
}


func (p *CallbackDelayFetchResponse) GetResultInfo() map[string]string {
  return p.ResultInfo
}
var CallbackDelayFetchResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *CallbackDelayFetchResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return CallbackDelayFetchResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *CallbackDelayFetchResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *CallbackDelayFetchResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.MAP {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CallbackDelayFetchResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  _, _, size, err := iprot.ReadMapBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading map begin: ", err)
  }
  tMap := make(map[string]string, size)
  p.ResultInfo =  tMap
  for i := 0; i < size; i ++ {
var _key0 string
    if v, err := iprot.ReadString(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _key0 = v
}
var _val1 string
    if v, err := iprot.ReadString(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _val1 = v
}
    p.ResultInfo[_key0] = _val1
  }
  if err := iprot.ReadMapEnd(ctx); err != nil {
    return thrift.PrependError("error reading map end: ", err)
  }
  return nil
}

func (p *CallbackDelayFetchResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *CallbackDelayFetchResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CallbackDelayFetchResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CallbackDelayFetchResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ResultInfo", thrift.MAP, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ResultInfo: ", p), err) }
  if err := oprot.WriteMapBegin(ctx, thrift.STRING, thrift.STRING, len(p.ResultInfo)); err != nil {
    return thrift.PrependError("error writing map begin: ", err)
  }
  for k, v := range p.ResultInfo {
    if err := oprot.WriteString(ctx, string(k)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    if err := oprot.WriteString(ctx, string(v)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
  }
  if err := oprot.WriteMapEnd(ctx); err != nil {
    return thrift.PrependError("error writing map end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ResultInfo: ", p), err) }
  return err
}

func (p *CallbackDelayFetchResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *CallbackDelayFetchResponse) Equals(other *CallbackDelayFetchResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if len(p.ResultInfo) != len(other.ResultInfo) { return false }
  for k, _tgt := range p.ResultInfo {
    _src2 := other.ResultInfo[k]
    if _tgt != _src2 { return false }
  }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *CallbackDelayFetchResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CallbackDelayFetchResponse(%+v)", *p)
}

// Attributes:
//  - SyncMetaTable
//  - CloudDbTunnel
//  - SyncTunnel
//  - Base
type CallbackSyncDataRequest struct {
  SyncMetaTable string `thrift:"SyncMetaTable,1" db:"SyncMetaTable" json:"sync_meta_table"`
  CloudDbTunnel string `thrift:"CloudDbTunnel,2" db:"CloudDbTunnel" json:"cloud_db_tunnel"`
  SyncTunnel string `thrift:"SyncTunnel,3" db:"SyncTunnel" json:"sync_tunnel"`
  // unused fields # 4 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewCallbackSyncDataRequest() *CallbackSyncDataRequest {
  return &CallbackSyncDataRequest{}
}


func (p *CallbackSyncDataRequest) GetSyncMetaTable() string {
  return p.SyncMetaTable
}

func (p *CallbackSyncDataRequest) GetCloudDbTunnel() string {
  return p.CloudDbTunnel
}

func (p *CallbackSyncDataRequest) GetSyncTunnel() string {
  return p.SyncTunnel
}
var CallbackSyncDataRequest_Base_DEFAULT *base.Base
func (p *CallbackSyncDataRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return CallbackSyncDataRequest_Base_DEFAULT
  }
return p.Base
}
func (p *CallbackSyncDataRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *CallbackSyncDataRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CallbackSyncDataRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.SyncMetaTable = v
}
  return nil
}

func (p *CallbackSyncDataRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.CloudDbTunnel = v
}
  return nil
}

func (p *CallbackSyncDataRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.SyncTunnel = v
}
  return nil
}

func (p *CallbackSyncDataRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *CallbackSyncDataRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CallbackSyncDataRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CallbackSyncDataRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "SyncMetaTable", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:SyncMetaTable: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.SyncMetaTable)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.SyncMetaTable (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:SyncMetaTable: ", p), err) }
  return err
}

func (p *CallbackSyncDataRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "CloudDbTunnel", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:CloudDbTunnel: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.CloudDbTunnel)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.CloudDbTunnel (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:CloudDbTunnel: ", p), err) }
  return err
}

func (p *CallbackSyncDataRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "SyncTunnel", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:SyncTunnel: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.SyncTunnel)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.SyncTunnel (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:SyncTunnel: ", p), err) }
  return err
}

func (p *CallbackSyncDataRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *CallbackSyncDataRequest) Equals(other *CallbackSyncDataRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.SyncMetaTable != other.SyncMetaTable { return false }
  if p.CloudDbTunnel != other.CloudDbTunnel { return false }
  if p.SyncTunnel != other.SyncTunnel { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *CallbackSyncDataRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CallbackSyncDataRequest(%+v)", *p)
}

// Attributes:
//  - ResultInfo
//  - BaseResp
type CallbackSyncDataResponse struct {
  ResultInfo map[string]string `thrift:"ResultInfo,1" db:"ResultInfo" json:"ResultInfo"`
  // unused fields # 2 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewCallbackSyncDataResponse() *CallbackSyncDataResponse {
  return &CallbackSyncDataResponse{}
}


func (p *CallbackSyncDataResponse) GetResultInfo() map[string]string {
  return p.ResultInfo
}
var CallbackSyncDataResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *CallbackSyncDataResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return CallbackSyncDataResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *CallbackSyncDataResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *CallbackSyncDataResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.MAP {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *CallbackSyncDataResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  _, _, size, err := iprot.ReadMapBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading map begin: ", err)
  }
  tMap := make(map[string]string, size)
  p.ResultInfo =  tMap
  for i := 0; i < size; i ++ {
var _key3 string
    if v, err := iprot.ReadString(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _key3 = v
}
var _val4 string
    if v, err := iprot.ReadString(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _val4 = v
}
    p.ResultInfo[_key3] = _val4
  }
  if err := iprot.ReadMapEnd(ctx); err != nil {
    return thrift.PrependError("error reading map end: ", err)
  }
  return nil
}

func (p *CallbackSyncDataResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *CallbackSyncDataResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CallbackSyncDataResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *CallbackSyncDataResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ResultInfo", thrift.MAP, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ResultInfo: ", p), err) }
  if err := oprot.WriteMapBegin(ctx, thrift.STRING, thrift.STRING, len(p.ResultInfo)); err != nil {
    return thrift.PrependError("error writing map begin: ", err)
  }
  for k, v := range p.ResultInfo {
    if err := oprot.WriteString(ctx, string(k)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    if err := oprot.WriteString(ctx, string(v)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
  }
  if err := oprot.WriteMapEnd(ctx); err != nil {
    return thrift.PrependError("error writing map end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ResultInfo: ", p), err) }
  return err
}

func (p *CallbackSyncDataResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *CallbackSyncDataResponse) Equals(other *CallbackSyncDataResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if len(p.ResultInfo) != len(other.ResultInfo) { return false }
  for k, _tgt := range p.ResultInfo {
    _src5 := other.ResultInfo[k]
    if _tgt != _src5 { return false }
  }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *CallbackSyncDataResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("CallbackSyncDataResponse(%+v)", *p)
}

// Attributes:
//  - CollectType
//  - Origin
//  - Title
//  - SavePath
//  - Remark
//  - Base
type AddToFavoritesRequest struct {
  CollectType string `thrift:"CollectType,1" db:"CollectType" json:"collect_type"`
  Origin string `thrift:"Origin,2" db:"Origin" json:"origin"`
  Title string `thrift:"Title,3" db:"Title" json:"title"`
  SavePath string `thrift:"SavePath,4" db:"SavePath" json:"save_path"`
  Remark string `thrift:"Remark,5" db:"Remark" json:"remark"`
  // unused fields # 6 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewAddToFavoritesRequest() *AddToFavoritesRequest {
  return &AddToFavoritesRequest{}
}


func (p *AddToFavoritesRequest) GetCollectType() string {
  return p.CollectType
}

func (p *AddToFavoritesRequest) GetOrigin() string {
  return p.Origin
}

func (p *AddToFavoritesRequest) GetTitle() string {
  return p.Title
}

func (p *AddToFavoritesRequest) GetSavePath() string {
  return p.SavePath
}

func (p *AddToFavoritesRequest) GetRemark() string {
  return p.Remark
}
var AddToFavoritesRequest_Base_DEFAULT *base.Base
func (p *AddToFavoritesRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return AddToFavoritesRequest_Base_DEFAULT
  }
return p.Base
}
func (p *AddToFavoritesRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *AddToFavoritesRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *AddToFavoritesRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.CollectType = v
}
  return nil
}

func (p *AddToFavoritesRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Origin = v
}
  return nil
}

func (p *AddToFavoritesRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Title = v
}
  return nil
}

func (p *AddToFavoritesRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.SavePath = v
}
  return nil
}

func (p *AddToFavoritesRequest)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Remark = v
}
  return nil
}

func (p *AddToFavoritesRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *AddToFavoritesRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AddToFavoritesRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *AddToFavoritesRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "CollectType", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:CollectType: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.CollectType)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.CollectType (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:CollectType: ", p), err) }
  return err
}

func (p *AddToFavoritesRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Origin", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Origin: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Origin)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Origin (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Origin: ", p), err) }
  return err
}

func (p *AddToFavoritesRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Title", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Title: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Title)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Title (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Title: ", p), err) }
  return err
}

func (p *AddToFavoritesRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "SavePath", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:SavePath: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.SavePath)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.SavePath (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:SavePath: ", p), err) }
  return err
}

func (p *AddToFavoritesRequest) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Remark", thrift.STRING, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:Remark: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Remark)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Remark (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:Remark: ", p), err) }
  return err
}

func (p *AddToFavoritesRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *AddToFavoritesRequest) Equals(other *AddToFavoritesRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.CollectType != other.CollectType { return false }
  if p.Origin != other.Origin { return false }
  if p.Title != other.Title { return false }
  if p.SavePath != other.SavePath { return false }
  if p.Remark != other.Remark { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *AddToFavoritesRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("AddToFavoritesRequest(%+v)", *p)
}

// Attributes:
//  - BaseResp
type AddToFavoritesResponse struct {
  // unused fields # 1 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewAddToFavoritesResponse() *AddToFavoritesResponse {
  return &AddToFavoritesResponse{}
}

var AddToFavoritesResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *AddToFavoritesResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return AddToFavoritesResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *AddToFavoritesResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *AddToFavoritesResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *AddToFavoritesResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *AddToFavoritesResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AddToFavoritesResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *AddToFavoritesResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *AddToFavoritesResponse) Equals(other *AddToFavoritesResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *AddToFavoritesResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("AddToFavoritesResponse(%+v)", *p)
}

// Attributes:
//  - Offset
//  - Count
//  - Keyword
//  - Base
type GetFavoritesPageRequest struct {
  Offset int64 `thrift:"Offset,1" db:"Offset" json:"Offset"`
  Count int64 `thrift:"Count,2" db:"Count" json:"Count"`
  Keyword *string `thrift:"Keyword,3" db:"Keyword" json:"Keyword,omitempty"`
  // unused fields # 4 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewGetFavoritesPageRequest() *GetFavoritesPageRequest {
  return &GetFavoritesPageRequest{}
}


func (p *GetFavoritesPageRequest) GetOffset() int64 {
  return p.Offset
}

func (p *GetFavoritesPageRequest) GetCount() int64 {
  return p.Count
}
var GetFavoritesPageRequest_Keyword_DEFAULT string
func (p *GetFavoritesPageRequest) GetKeyword() string {
  if !p.IsSetKeyword() {
    return GetFavoritesPageRequest_Keyword_DEFAULT
  }
return *p.Keyword
}
var GetFavoritesPageRequest_Base_DEFAULT *base.Base
func (p *GetFavoritesPageRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return GetFavoritesPageRequest_Base_DEFAULT
  }
return p.Base
}
func (p *GetFavoritesPageRequest) IsSetKeyword() bool {
  return p.Keyword != nil
}

func (p *GetFavoritesPageRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *GetFavoritesPageRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetFavoritesPageRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Offset = v
}
  return nil
}

func (p *GetFavoritesPageRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Count = v
}
  return nil
}

func (p *GetFavoritesPageRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Keyword = &v
}
  return nil
}

func (p *GetFavoritesPageRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *GetFavoritesPageRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetFavoritesPageRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetFavoritesPageRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Offset", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Offset: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Offset)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Offset (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Offset: ", p), err) }
  return err
}

func (p *GetFavoritesPageRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Count", thrift.I64, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Count: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Count)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Count (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Count: ", p), err) }
  return err
}

func (p *GetFavoritesPageRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetKeyword() {
    if err := oprot.WriteFieldBegin(ctx, "Keyword", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Keyword: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Keyword)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Keyword (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Keyword: ", p), err) }
  }
  return err
}

func (p *GetFavoritesPageRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *GetFavoritesPageRequest) Equals(other *GetFavoritesPageRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Offset != other.Offset { return false }
  if p.Count != other.Count { return false }
  if p.Keyword != other.Keyword {
    if p.Keyword == nil || other.Keyword == nil {
      return false
    }
    if (*p.Keyword) != (*other.Keyword) { return false }
  }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *GetFavoritesPageRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetFavoritesPageRequest(%+v)", *p)
}

// Attributes:
//  - ID
//  - Origin
//  - Title
//  - Author
//  - State
//  - SavePath
//  - CollectType
//  - CreateTime
type FavoritesItem struct {
  ID int64 `thrift:"Id,1" db:"Id" json:"id"`
  Origin string `thrift:"Origin,2" db:"Origin" json:"origin"`
  Title *string `thrift:"Title,3" db:"Title" json:"title"`
  Author *string `thrift:"Author,4" db:"Author" json:"author"`
  State string `thrift:"State,5" db:"State" json:"state"`
  SavePath *string `thrift:"SavePath,6" db:"SavePath" json:"save_path"`
  CollectType *int64 `thrift:"CollectType,7" db:"CollectType" json:"collect_type"`
  CreateTime string `thrift:"CreateTime,8" db:"CreateTime" json:"create_time"`
}

func NewFavoritesItem() *FavoritesItem {
  return &FavoritesItem{}
}


func (p *FavoritesItem) GetID() int64 {
  return p.ID
}

func (p *FavoritesItem) GetOrigin() string {
  return p.Origin
}
var FavoritesItem_Title_DEFAULT string
func (p *FavoritesItem) GetTitle() string {
  if !p.IsSetTitle() {
    return FavoritesItem_Title_DEFAULT
  }
return *p.Title
}
var FavoritesItem_Author_DEFAULT string
func (p *FavoritesItem) GetAuthor() string {
  if !p.IsSetAuthor() {
    return FavoritesItem_Author_DEFAULT
  }
return *p.Author
}

func (p *FavoritesItem) GetState() string {
  return p.State
}
var FavoritesItem_SavePath_DEFAULT string
func (p *FavoritesItem) GetSavePath() string {
  if !p.IsSetSavePath() {
    return FavoritesItem_SavePath_DEFAULT
  }
return *p.SavePath
}
var FavoritesItem_CollectType_DEFAULT int64
func (p *FavoritesItem) GetCollectType() int64 {
  if !p.IsSetCollectType() {
    return FavoritesItem_CollectType_DEFAULT
  }
return *p.CollectType
}

func (p *FavoritesItem) GetCreateTime() string {
  return p.CreateTime
}
func (p *FavoritesItem) IsSetTitle() bool {
  return p.Title != nil
}

func (p *FavoritesItem) IsSetAuthor() bool {
  return p.Author != nil
}

func (p *FavoritesItem) IsSetSavePath() bool {
  return p.SavePath != nil
}

func (p *FavoritesItem) IsSetCollectType() bool {
  return p.CollectType != nil
}

func (p *FavoritesItem) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *FavoritesItem)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ID = v
}
  return nil
}

func (p *FavoritesItem)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Origin = v
}
  return nil
}

func (p *FavoritesItem)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Title = &v
}
  return nil
}

func (p *FavoritesItem)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Author = &v
}
  return nil
}

func (p *FavoritesItem)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.State = v
}
  return nil
}

func (p *FavoritesItem)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.SavePath = &v
}
  return nil
}

func (p *FavoritesItem)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.CollectType = &v
}
  return nil
}

func (p *FavoritesItem)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.CreateTime = v
}
  return nil
}

func (p *FavoritesItem) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "FavoritesItem"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *FavoritesItem) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Id", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Id: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.ID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Id (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Id: ", p), err) }
  return err
}

func (p *FavoritesItem) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Origin", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Origin: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Origin)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Origin (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Origin: ", p), err) }
  return err
}

func (p *FavoritesItem) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetTitle() {
    if err := oprot.WriteFieldBegin(ctx, "Title", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Title: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Title)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Title (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Title: ", p), err) }
  }
  return err
}

func (p *FavoritesItem) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetAuthor() {
    if err := oprot.WriteFieldBegin(ctx, "Author", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:Author: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Author)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Author (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:Author: ", p), err) }
  }
  return err
}

func (p *FavoritesItem) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "State", thrift.STRING, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:State: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.State)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.State (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:State: ", p), err) }
  return err
}

func (p *FavoritesItem) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSavePath() {
    if err := oprot.WriteFieldBegin(ctx, "SavePath", thrift.STRING, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:SavePath: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.SavePath)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.SavePath (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:SavePath: ", p), err) }
  }
  return err
}

func (p *FavoritesItem) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCollectType() {
    if err := oprot.WriteFieldBegin(ctx, "CollectType", thrift.I64, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:CollectType: ", p), err) }
    if err := oprot.WriteI64(ctx, int64(*p.CollectType)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.CollectType (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:CollectType: ", p), err) }
  }
  return err
}

func (p *FavoritesItem) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "CreateTime", thrift.STRING, 8); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:CreateTime: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.CreateTime)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.CreateTime (8) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 8:CreateTime: ", p), err) }
  return err
}

func (p *FavoritesItem) Equals(other *FavoritesItem) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ID != other.ID { return false }
  if p.Origin != other.Origin { return false }
  if p.Title != other.Title {
    if p.Title == nil || other.Title == nil {
      return false
    }
    if (*p.Title) != (*other.Title) { return false }
  }
  if p.Author != other.Author {
    if p.Author == nil || other.Author == nil {
      return false
    }
    if (*p.Author) != (*other.Author) { return false }
  }
  if p.State != other.State { return false }
  if p.SavePath != other.SavePath {
    if p.SavePath == nil || other.SavePath == nil {
      return false
    }
    if (*p.SavePath) != (*other.SavePath) { return false }
  }
  if p.CollectType != other.CollectType {
    if p.CollectType == nil || other.CollectType == nil {
      return false
    }
    if (*p.CollectType) != (*other.CollectType) { return false }
  }
  if p.CreateTime != other.CreateTime { return false }
  return true
}

func (p *FavoritesItem) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("FavoritesItem(%+v)", *p)
}

// Attributes:
//  - FavoritesList
//  - HasMore
//  - Total
//  - BaseResp
type GetFavoritesPageResponse struct {
  FavoritesList []*FavoritesItem `thrift:"FavoritesList,1" db:"FavoritesList" json:"FavoritesList"`
  HasMore bool `thrift:"HasMore,2" db:"HasMore" json:"HasMore"`
  Total int64 `thrift:"Total,3" db:"Total" json:"Total"`
  // unused fields # 4 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewGetFavoritesPageResponse() *GetFavoritesPageResponse {
  return &GetFavoritesPageResponse{}
}


func (p *GetFavoritesPageResponse) GetFavoritesList() []*FavoritesItem {
  return p.FavoritesList
}

func (p *GetFavoritesPageResponse) GetHasMore() bool {
  return p.HasMore
}

func (p *GetFavoritesPageResponse) GetTotal() int64 {
  return p.Total
}
var GetFavoritesPageResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *GetFavoritesPageResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return GetFavoritesPageResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *GetFavoritesPageResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *GetFavoritesPageResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetFavoritesPageResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*FavoritesItem, 0, size)
  p.FavoritesList =  tSlice
  for i := 0; i < size; i ++ {
    _elem6 := &FavoritesItem{}
    if err := _elem6.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem6), err)
    }
    p.FavoritesList = append(p.FavoritesList, _elem6)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *GetFavoritesPageResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.HasMore = v
}
  return nil
}

func (p *GetFavoritesPageResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Total = v
}
  return nil
}

func (p *GetFavoritesPageResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *GetFavoritesPageResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetFavoritesPageResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetFavoritesPageResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FavoritesList", thrift.LIST, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:FavoritesList: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.FavoritesList)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.FavoritesList {
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:FavoritesList: ", p), err) }
  return err
}

func (p *GetFavoritesPageResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "HasMore", thrift.BOOL, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:HasMore: ", p), err) }
  if err := oprot.WriteBool(ctx, bool(p.HasMore)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.HasMore (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:HasMore: ", p), err) }
  return err
}

func (p *GetFavoritesPageResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Total", thrift.I64, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Total: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Total)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Total (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Total: ", p), err) }
  return err
}

func (p *GetFavoritesPageResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *GetFavoritesPageResponse) Equals(other *GetFavoritesPageResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if len(p.FavoritesList) != len(other.FavoritesList) { return false }
  for i, _tgt := range p.FavoritesList {
    _src7 := other.FavoritesList[i]
    if !_tgt.Equals(_src7) { return false }
  }
  if p.HasMore != other.HasMore { return false }
  if p.Total != other.Total { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *GetFavoritesPageResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetFavoritesPageResponse(%+v)", *p)
}

// Attributes:
//  - Depth
//  - Root
type GetDiskInfoRequest struct {
  Depth int64 `thrift:"Depth,1" db:"Depth" json:"depth"`
  Root string `thrift:"Root,2" db:"Root" json:"root"`
}

func NewGetDiskInfoRequest() *GetDiskInfoRequest {
  return &GetDiskInfoRequest{}
}


func (p *GetDiskInfoRequest) GetDepth() int64 {
  return p.Depth
}

func (p *GetDiskInfoRequest) GetRoot() string {
  return p.Root
}
func (p *GetDiskInfoRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetDiskInfoRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Depth = v
}
  return nil
}

func (p *GetDiskInfoRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Root = v
}
  return nil
}

func (p *GetDiskInfoRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetDiskInfoRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetDiskInfoRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Depth", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Depth: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Depth)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Depth (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Depth: ", p), err) }
  return err
}

func (p *GetDiskInfoRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Root", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Root: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Root)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Root (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Root: ", p), err) }
  return err
}

func (p *GetDiskInfoRequest) Equals(other *GetDiskInfoRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Depth != other.Depth { return false }
  if p.Root != other.Root { return false }
  return true
}

func (p *GetDiskInfoRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetDiskInfoRequest(%+v)", *p)
}

// Attributes:
//  - DiskLabel
//  - DiskUsage
//  - DiskProgress
//  - FsTree
//  - BaseResp
type GetDiskInfoResponse struct {
  DiskLabel string `thrift:"DiskLabel,1" db:"DiskLabel" json:"DiskLabel"`
  DiskUsage string `thrift:"DiskUsage,2" db:"DiskUsage" json:"DiskUsage"`
  DiskProgress int64 `thrift:"DiskProgress,3" db:"DiskProgress" json:"DiskProgress"`
  FsTree string `thrift:"FsTree,4" db:"FsTree" json:"FsTree"`
  // unused fields # 5 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewGetDiskInfoResponse() *GetDiskInfoResponse {
  return &GetDiskInfoResponse{}
}


func (p *GetDiskInfoResponse) GetDiskLabel() string {
  return p.DiskLabel
}

func (p *GetDiskInfoResponse) GetDiskUsage() string {
  return p.DiskUsage
}

func (p *GetDiskInfoResponse) GetDiskProgress() int64 {
  return p.DiskProgress
}

func (p *GetDiskInfoResponse) GetFsTree() string {
  return p.FsTree
}
var GetDiskInfoResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *GetDiskInfoResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return GetDiskInfoResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *GetDiskInfoResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *GetDiskInfoResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetDiskInfoResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.DiskLabel = v
}
  return nil
}

func (p *GetDiskInfoResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.DiskUsage = v
}
  return nil
}

func (p *GetDiskInfoResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.DiskProgress = v
}
  return nil
}

func (p *GetDiskInfoResponse)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.FsTree = v
}
  return nil
}

func (p *GetDiskInfoResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *GetDiskInfoResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetDiskInfoResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetDiskInfoResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "DiskLabel", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:DiskLabel: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.DiskLabel)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.DiskLabel (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:DiskLabel: ", p), err) }
  return err
}

func (p *GetDiskInfoResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "DiskUsage", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:DiskUsage: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.DiskUsage)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.DiskUsage (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:DiskUsage: ", p), err) }
  return err
}

func (p *GetDiskInfoResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "DiskProgress", thrift.I64, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:DiskProgress: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.DiskProgress)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.DiskProgress (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:DiskProgress: ", p), err) }
  return err
}

func (p *GetDiskInfoResponse) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FsTree", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:FsTree: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.FsTree)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FsTree (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:FsTree: ", p), err) }
  return err
}

func (p *GetDiskInfoResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *GetDiskInfoResponse) Equals(other *GetDiskInfoResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.DiskLabel != other.DiskLabel { return false }
  if p.DiskUsage != other.DiskUsage { return false }
  if p.DiskProgress != other.DiskProgress { return false }
  if p.FsTree != other.FsTree { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *GetDiskInfoResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetDiskInfoResponse(%+v)", *p)
}

// Attributes:
//  - FavoriteId
//  - Base
type DeleteFavoriteRequest struct {
  FavoriteId int64 `thrift:"FavoriteId,1" db:"FavoriteId" json:"FavoriteId"`
  // unused fields # 2 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewDeleteFavoriteRequest() *DeleteFavoriteRequest {
  return &DeleteFavoriteRequest{}
}


func (p *DeleteFavoriteRequest) GetFavoriteId() int64 {
  return p.FavoriteId
}
var DeleteFavoriteRequest_Base_DEFAULT *base.Base
func (p *DeleteFavoriteRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return DeleteFavoriteRequest_Base_DEFAULT
  }
return p.Base
}
func (p *DeleteFavoriteRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *DeleteFavoriteRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DeleteFavoriteRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.FavoriteId = v
}
  return nil
}

func (p *DeleteFavoriteRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *DeleteFavoriteRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DeleteFavoriteRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DeleteFavoriteRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "FavoriteId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:FavoriteId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.FavoriteId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.FavoriteId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:FavoriteId: ", p), err) }
  return err
}

func (p *DeleteFavoriteRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *DeleteFavoriteRequest) Equals(other *DeleteFavoriteRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.FavoriteId != other.FavoriteId { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *DeleteFavoriteRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DeleteFavoriteRequest(%+v)", *p)
}

// Attributes:
//  - BaseResp
type DeleteFavoriteResponse struct {
  // unused fields # 1 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewDeleteFavoriteResponse() *DeleteFavoriteResponse {
  return &DeleteFavoriteResponse{}
}

var DeleteFavoriteResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *DeleteFavoriteResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return DeleteFavoriteResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *DeleteFavoriteResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *DeleteFavoriteResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DeleteFavoriteResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *DeleteFavoriteResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DeleteFavoriteResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DeleteFavoriteResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *DeleteFavoriteResponse) Equals(other *DeleteFavoriteResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *DeleteFavoriteResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DeleteFavoriteResponse(%+v)", *p)
}

type Favorites interface {
  // Parameters:
  //  - Req
  CallbackDelayFetch(ctx context.Context, req *CallbackDelayFetchRequest) (_r *CallbackDelayFetchResponse, _err error)
  // Parameters:
  //  - Req
  CallbackSyncData(ctx context.Context, req *CallbackSyncDataRequest) (_r *CallbackSyncDataResponse, _err error)
  // Parameters:
  //  - Req
  AddToFavorites(ctx context.Context, req *AddToFavoritesRequest) (_r *AddToFavoritesResponse, _err error)
  // Parameters:
  //  - Req
  GetFavoritesPage(ctx context.Context, req *GetFavoritesPageRequest) (_r *GetFavoritesPageResponse, _err error)
  // Parameters:
  //  - Req
  GetDiskInfo(ctx context.Context, req *GetDiskInfoRequest) (_r *GetDiskInfoResponse, _err error)
  // Parameters:
  //  - Req
  DeleteFavorite(ctx context.Context, req *DeleteFavoriteRequest) (_r *DeleteFavoriteResponse, _err error)
}

type FavoritesClient struct {
  c thrift.TClient
  meta thrift.ResponseMeta
}

func NewFavoritesClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *FavoritesClient {
  return &FavoritesClient{
    c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
  }
}

func NewFavoritesClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *FavoritesClient {
  return &FavoritesClient{
    c: thrift.NewTStandardClient(iprot, oprot),
  }
}

func NewFavoritesClient(c thrift.TClient) *FavoritesClient {
  return &FavoritesClient{
    c: c,
  }
}

func (p *FavoritesClient) Client_() thrift.TClient {
  return p.c
}

func (p *FavoritesClient) LastResponseMeta_() thrift.ResponseMeta {
  return p.meta
}

func (p *FavoritesClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
  p.meta = meta
}

// Parameters:
//  - Req
func (p *FavoritesClient) CallbackDelayFetch(ctx context.Context, req *CallbackDelayFetchRequest) (_r *CallbackDelayFetchResponse, _err error) {
  var _args8 FavoritesCallbackDelayFetchArgs
  _args8.Req = req
  var _result10 FavoritesCallbackDelayFetchResult
  var _meta9 thrift.ResponseMeta
  _meta9, _err = p.Client_().Call(ctx, "CallbackDelayFetch", &_args8, &_result10)
  p.SetLastResponseMeta_(_meta9)
  if _err != nil {
    return
  }
  if _ret11 := _result10.GetSuccess(); _ret11 != nil {
    return _ret11, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "CallbackDelayFetch failed: unknown result")
}

// Parameters:
//  - Req
func (p *FavoritesClient) CallbackSyncData(ctx context.Context, req *CallbackSyncDataRequest) (_r *CallbackSyncDataResponse, _err error) {
  var _args12 FavoritesCallbackSyncDataArgs
  _args12.Req = req
  var _result14 FavoritesCallbackSyncDataResult
  var _meta13 thrift.ResponseMeta
  _meta13, _err = p.Client_().Call(ctx, "CallbackSyncData", &_args12, &_result14)
  p.SetLastResponseMeta_(_meta13)
  if _err != nil {
    return
  }
  if _ret15 := _result14.GetSuccess(); _ret15 != nil {
    return _ret15, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "CallbackSyncData failed: unknown result")
}

// Parameters:
//  - Req
func (p *FavoritesClient) AddToFavorites(ctx context.Context, req *AddToFavoritesRequest) (_r *AddToFavoritesResponse, _err error) {
  var _args16 FavoritesAddToFavoritesArgs
  _args16.Req = req
  var _result18 FavoritesAddToFavoritesResult
  var _meta17 thrift.ResponseMeta
  _meta17, _err = p.Client_().Call(ctx, "AddToFavorites", &_args16, &_result18)
  p.SetLastResponseMeta_(_meta17)
  if _err != nil {
    return
  }
  if _ret19 := _result18.GetSuccess(); _ret19 != nil {
    return _ret19, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "AddToFavorites failed: unknown result")
}

// Parameters:
//  - Req
func (p *FavoritesClient) GetFavoritesPage(ctx context.Context, req *GetFavoritesPageRequest) (_r *GetFavoritesPageResponse, _err error) {
  var _args20 FavoritesGetFavoritesPageArgs
  _args20.Req = req
  var _result22 FavoritesGetFavoritesPageResult
  var _meta21 thrift.ResponseMeta
  _meta21, _err = p.Client_().Call(ctx, "GetFavoritesPage", &_args20, &_result22)
  p.SetLastResponseMeta_(_meta21)
  if _err != nil {
    return
  }
  if _ret23 := _result22.GetSuccess(); _ret23 != nil {
    return _ret23, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "GetFavoritesPage failed: unknown result")
}

// Parameters:
//  - Req
func (p *FavoritesClient) GetDiskInfo(ctx context.Context, req *GetDiskInfoRequest) (_r *GetDiskInfoResponse, _err error) {
  var _args24 FavoritesGetDiskInfoArgs
  _args24.Req = req
  var _result26 FavoritesGetDiskInfoResult
  var _meta25 thrift.ResponseMeta
  _meta25, _err = p.Client_().Call(ctx, "GetDiskInfo", &_args24, &_result26)
  p.SetLastResponseMeta_(_meta25)
  if _err != nil {
    return
  }
  if _ret27 := _result26.GetSuccess(); _ret27 != nil {
    return _ret27, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "GetDiskInfo failed: unknown result")
}

// Parameters:
//  - Req
func (p *FavoritesClient) DeleteFavorite(ctx context.Context, req *DeleteFavoriteRequest) (_r *DeleteFavoriteResponse, _err error) {
  var _args28 FavoritesDeleteFavoriteArgs
  _args28.Req = req
  var _result30 FavoritesDeleteFavoriteResult
  var _meta29 thrift.ResponseMeta
  _meta29, _err = p.Client_().Call(ctx, "DeleteFavorite", &_args28, &_result30)
  p.SetLastResponseMeta_(_meta29)
  if _err != nil {
    return
  }
  if _ret31 := _result30.GetSuccess(); _ret31 != nil {
    return _ret31, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "DeleteFavorite failed: unknown result")
}

type FavoritesProcessor struct {
  processorMap map[string]thrift.TProcessorFunction
  handler Favorites
}

func (p *FavoritesProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
  p.processorMap[key] = processor
}

func (p *FavoritesProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
  processor, ok = p.processorMap[key]
  return processor, ok
}

func (p *FavoritesProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
  return p.processorMap
}

func NewFavoritesProcessor(handler Favorites) *FavoritesProcessor {

  self32 := &FavoritesProcessor{handler:handler, processorMap:make(map[string]thrift.TProcessorFunction)}
  self32.processorMap["CallbackDelayFetch"] = &favoritesProcessorCallbackDelayFetch{handler:handler}
  self32.processorMap["CallbackSyncData"] = &favoritesProcessorCallbackSyncData{handler:handler}
  self32.processorMap["AddToFavorites"] = &favoritesProcessorAddToFavorites{handler:handler}
  self32.processorMap["GetFavoritesPage"] = &favoritesProcessorGetFavoritesPage{handler:handler}
  self32.processorMap["GetDiskInfo"] = &favoritesProcessorGetDiskInfo{handler:handler}
  self32.processorMap["DeleteFavorite"] = &favoritesProcessorDeleteFavorite{handler:handler}
return self32
}

func (p *FavoritesProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
  if err2 != nil { return false, thrift.WrapTException(err2) }
  if processor, ok := p.GetProcessorFunction(name); ok {
    return processor.Process(ctx, seqId, iprot, oprot)
  }
  iprot.Skip(ctx, thrift.STRUCT)
  iprot.ReadMessageEnd(ctx)
  x33 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function " + name)
  oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
  x33.Write(ctx, oprot)
  oprot.WriteMessageEnd(ctx)
  oprot.Flush(ctx)
  return false, x33

}

type favoritesProcessorCallbackDelayFetch struct {
  handler Favorites
}

func (p *favoritesProcessorCallbackDelayFetch) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := FavoritesCallbackDelayFetchArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "CallbackDelayFetch", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := FavoritesCallbackDelayFetchResult{}
  var retval *CallbackDelayFetchResponse
  if retval, err2 = p.handler.CallbackDelayFetch(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing CallbackDelayFetch: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "CallbackDelayFetch", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "CallbackDelayFetch", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type favoritesProcessorCallbackSyncData struct {
  handler Favorites
}

func (p *favoritesProcessorCallbackSyncData) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := FavoritesCallbackSyncDataArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "CallbackSyncData", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := FavoritesCallbackSyncDataResult{}
  var retval *CallbackSyncDataResponse
  if retval, err2 = p.handler.CallbackSyncData(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing CallbackSyncData: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "CallbackSyncData", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "CallbackSyncData", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type favoritesProcessorAddToFavorites struct {
  handler Favorites
}

func (p *favoritesProcessorAddToFavorites) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := FavoritesAddToFavoritesArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "AddToFavorites", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := FavoritesAddToFavoritesResult{}
  var retval *AddToFavoritesResponse
  if retval, err2 = p.handler.AddToFavorites(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing AddToFavorites: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "AddToFavorites", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "AddToFavorites", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type favoritesProcessorGetFavoritesPage struct {
  handler Favorites
}

func (p *favoritesProcessorGetFavoritesPage) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := FavoritesGetFavoritesPageArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "GetFavoritesPage", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := FavoritesGetFavoritesPageResult{}
  var retval *GetFavoritesPageResponse
  if retval, err2 = p.handler.GetFavoritesPage(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetFavoritesPage: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "GetFavoritesPage", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "GetFavoritesPage", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type favoritesProcessorGetDiskInfo struct {
  handler Favorites
}

func (p *favoritesProcessorGetDiskInfo) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := FavoritesGetDiskInfoArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "GetDiskInfo", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := FavoritesGetDiskInfoResult{}
  var retval *GetDiskInfoResponse
  if retval, err2 = p.handler.GetDiskInfo(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetDiskInfo: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "GetDiskInfo", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "GetDiskInfo", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type favoritesProcessorDeleteFavorite struct {
  handler Favorites
}

func (p *favoritesProcessorDeleteFavorite) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := FavoritesDeleteFavoriteArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "DeleteFavorite", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := FavoritesDeleteFavoriteResult{}
  var retval *DeleteFavoriteResponse
  if retval, err2 = p.handler.DeleteFavorite(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing DeleteFavorite: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "DeleteFavorite", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "DeleteFavorite", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}


// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//  - Req
type FavoritesCallbackDelayFetchArgs struct {
  Req *CallbackDelayFetchRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewFavoritesCallbackDelayFetchArgs() *FavoritesCallbackDelayFetchArgs {
  return &FavoritesCallbackDelayFetchArgs{}
}

var FavoritesCallbackDelayFetchArgs_Req_DEFAULT *CallbackDelayFetchRequest
func (p *FavoritesCallbackDelayFetchArgs) GetReq() *CallbackDelayFetchRequest {
  if !p.IsSetReq() {
    return FavoritesCallbackDelayFetchArgs_Req_DEFAULT
  }
return p.Req
}
func (p *FavoritesCallbackDelayFetchArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *FavoritesCallbackDelayFetchArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *FavoritesCallbackDelayFetchArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &CallbackDelayFetchRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *FavoritesCallbackDelayFetchArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CallbackDelayFetch_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *FavoritesCallbackDelayFetchArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *FavoritesCallbackDelayFetchArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("FavoritesCallbackDelayFetchArgs(%+v)", *p)
}

// Attributes:
//  - Success
type FavoritesCallbackDelayFetchResult struct {
  Success *CallbackDelayFetchResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewFavoritesCallbackDelayFetchResult() *FavoritesCallbackDelayFetchResult {
  return &FavoritesCallbackDelayFetchResult{}
}

var FavoritesCallbackDelayFetchResult_Success_DEFAULT *CallbackDelayFetchResponse
func (p *FavoritesCallbackDelayFetchResult) GetSuccess() *CallbackDelayFetchResponse {
  if !p.IsSetSuccess() {
    return FavoritesCallbackDelayFetchResult_Success_DEFAULT
  }
return p.Success
}
func (p *FavoritesCallbackDelayFetchResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *FavoritesCallbackDelayFetchResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *FavoritesCallbackDelayFetchResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &CallbackDelayFetchResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *FavoritesCallbackDelayFetchResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CallbackDelayFetch_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *FavoritesCallbackDelayFetchResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *FavoritesCallbackDelayFetchResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("FavoritesCallbackDelayFetchResult(%+v)", *p)
}

// Attributes:
//  - Req
type FavoritesCallbackSyncDataArgs struct {
  Req *CallbackSyncDataRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewFavoritesCallbackSyncDataArgs() *FavoritesCallbackSyncDataArgs {
  return &FavoritesCallbackSyncDataArgs{}
}

var FavoritesCallbackSyncDataArgs_Req_DEFAULT *CallbackSyncDataRequest
func (p *FavoritesCallbackSyncDataArgs) GetReq() *CallbackSyncDataRequest {
  if !p.IsSetReq() {
    return FavoritesCallbackSyncDataArgs_Req_DEFAULT
  }
return p.Req
}
func (p *FavoritesCallbackSyncDataArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *FavoritesCallbackSyncDataArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *FavoritesCallbackSyncDataArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &CallbackSyncDataRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *FavoritesCallbackSyncDataArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CallbackSyncData_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *FavoritesCallbackSyncDataArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *FavoritesCallbackSyncDataArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("FavoritesCallbackSyncDataArgs(%+v)", *p)
}

// Attributes:
//  - Success
type FavoritesCallbackSyncDataResult struct {
  Success *CallbackSyncDataResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewFavoritesCallbackSyncDataResult() *FavoritesCallbackSyncDataResult {
  return &FavoritesCallbackSyncDataResult{}
}

var FavoritesCallbackSyncDataResult_Success_DEFAULT *CallbackSyncDataResponse
func (p *FavoritesCallbackSyncDataResult) GetSuccess() *CallbackSyncDataResponse {
  if !p.IsSetSuccess() {
    return FavoritesCallbackSyncDataResult_Success_DEFAULT
  }
return p.Success
}
func (p *FavoritesCallbackSyncDataResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *FavoritesCallbackSyncDataResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *FavoritesCallbackSyncDataResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &CallbackSyncDataResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *FavoritesCallbackSyncDataResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "CallbackSyncData_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *FavoritesCallbackSyncDataResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *FavoritesCallbackSyncDataResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("FavoritesCallbackSyncDataResult(%+v)", *p)
}

// Attributes:
//  - Req
type FavoritesAddToFavoritesArgs struct {
  Req *AddToFavoritesRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewFavoritesAddToFavoritesArgs() *FavoritesAddToFavoritesArgs {
  return &FavoritesAddToFavoritesArgs{}
}

var FavoritesAddToFavoritesArgs_Req_DEFAULT *AddToFavoritesRequest
func (p *FavoritesAddToFavoritesArgs) GetReq() *AddToFavoritesRequest {
  if !p.IsSetReq() {
    return FavoritesAddToFavoritesArgs_Req_DEFAULT
  }
return p.Req
}
func (p *FavoritesAddToFavoritesArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *FavoritesAddToFavoritesArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *FavoritesAddToFavoritesArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &AddToFavoritesRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *FavoritesAddToFavoritesArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AddToFavorites_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *FavoritesAddToFavoritesArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *FavoritesAddToFavoritesArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("FavoritesAddToFavoritesArgs(%+v)", *p)
}

// Attributes:
//  - Success
type FavoritesAddToFavoritesResult struct {
  Success *AddToFavoritesResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewFavoritesAddToFavoritesResult() *FavoritesAddToFavoritesResult {
  return &FavoritesAddToFavoritesResult{}
}

var FavoritesAddToFavoritesResult_Success_DEFAULT *AddToFavoritesResponse
func (p *FavoritesAddToFavoritesResult) GetSuccess() *AddToFavoritesResponse {
  if !p.IsSetSuccess() {
    return FavoritesAddToFavoritesResult_Success_DEFAULT
  }
return p.Success
}
func (p *FavoritesAddToFavoritesResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *FavoritesAddToFavoritesResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *FavoritesAddToFavoritesResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &AddToFavoritesResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *FavoritesAddToFavoritesResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AddToFavorites_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *FavoritesAddToFavoritesResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *FavoritesAddToFavoritesResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("FavoritesAddToFavoritesResult(%+v)", *p)
}

// Attributes:
//  - Req
type FavoritesGetFavoritesPageArgs struct {
  Req *GetFavoritesPageRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewFavoritesGetFavoritesPageArgs() *FavoritesGetFavoritesPageArgs {
  return &FavoritesGetFavoritesPageArgs{}
}

var FavoritesGetFavoritesPageArgs_Req_DEFAULT *GetFavoritesPageRequest
func (p *FavoritesGetFavoritesPageArgs) GetReq() *GetFavoritesPageRequest {
  if !p.IsSetReq() {
    return FavoritesGetFavoritesPageArgs_Req_DEFAULT
  }
return p.Req
}
func (p *FavoritesGetFavoritesPageArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *FavoritesGetFavoritesPageArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *FavoritesGetFavoritesPageArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &GetFavoritesPageRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *FavoritesGetFavoritesPageArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetFavoritesPage_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *FavoritesGetFavoritesPageArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *FavoritesGetFavoritesPageArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("FavoritesGetFavoritesPageArgs(%+v)", *p)
}

// Attributes:
//  - Success
type FavoritesGetFavoritesPageResult struct {
  Success *GetFavoritesPageResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewFavoritesGetFavoritesPageResult() *FavoritesGetFavoritesPageResult {
  return &FavoritesGetFavoritesPageResult{}
}

var FavoritesGetFavoritesPageResult_Success_DEFAULT *GetFavoritesPageResponse
func (p *FavoritesGetFavoritesPageResult) GetSuccess() *GetFavoritesPageResponse {
  if !p.IsSetSuccess() {
    return FavoritesGetFavoritesPageResult_Success_DEFAULT
  }
return p.Success
}
func (p *FavoritesGetFavoritesPageResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *FavoritesGetFavoritesPageResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *FavoritesGetFavoritesPageResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &GetFavoritesPageResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *FavoritesGetFavoritesPageResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetFavoritesPage_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *FavoritesGetFavoritesPageResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *FavoritesGetFavoritesPageResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("FavoritesGetFavoritesPageResult(%+v)", *p)
}

// Attributes:
//  - Req
type FavoritesGetDiskInfoArgs struct {
  Req *GetDiskInfoRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewFavoritesGetDiskInfoArgs() *FavoritesGetDiskInfoArgs {
  return &FavoritesGetDiskInfoArgs{}
}

var FavoritesGetDiskInfoArgs_Req_DEFAULT *GetDiskInfoRequest
func (p *FavoritesGetDiskInfoArgs) GetReq() *GetDiskInfoRequest {
  if !p.IsSetReq() {
    return FavoritesGetDiskInfoArgs_Req_DEFAULT
  }
return p.Req
}
func (p *FavoritesGetDiskInfoArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *FavoritesGetDiskInfoArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *FavoritesGetDiskInfoArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &GetDiskInfoRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *FavoritesGetDiskInfoArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetDiskInfo_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *FavoritesGetDiskInfoArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *FavoritesGetDiskInfoArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("FavoritesGetDiskInfoArgs(%+v)", *p)
}

// Attributes:
//  - Success
type FavoritesGetDiskInfoResult struct {
  Success *GetDiskInfoResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewFavoritesGetDiskInfoResult() *FavoritesGetDiskInfoResult {
  return &FavoritesGetDiskInfoResult{}
}

var FavoritesGetDiskInfoResult_Success_DEFAULT *GetDiskInfoResponse
func (p *FavoritesGetDiskInfoResult) GetSuccess() *GetDiskInfoResponse {
  if !p.IsSetSuccess() {
    return FavoritesGetDiskInfoResult_Success_DEFAULT
  }
return p.Success
}
func (p *FavoritesGetDiskInfoResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *FavoritesGetDiskInfoResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *FavoritesGetDiskInfoResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &GetDiskInfoResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *FavoritesGetDiskInfoResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetDiskInfo_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *FavoritesGetDiskInfoResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *FavoritesGetDiskInfoResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("FavoritesGetDiskInfoResult(%+v)", *p)
}

// Attributes:
//  - Req
type FavoritesDeleteFavoriteArgs struct {
  Req *DeleteFavoriteRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewFavoritesDeleteFavoriteArgs() *FavoritesDeleteFavoriteArgs {
  return &FavoritesDeleteFavoriteArgs{}
}

var FavoritesDeleteFavoriteArgs_Req_DEFAULT *DeleteFavoriteRequest
func (p *FavoritesDeleteFavoriteArgs) GetReq() *DeleteFavoriteRequest {
  if !p.IsSetReq() {
    return FavoritesDeleteFavoriteArgs_Req_DEFAULT
  }
return p.Req
}
func (p *FavoritesDeleteFavoriteArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *FavoritesDeleteFavoriteArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *FavoritesDeleteFavoriteArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &DeleteFavoriteRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *FavoritesDeleteFavoriteArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DeleteFavorite_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *FavoritesDeleteFavoriteArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *FavoritesDeleteFavoriteArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("FavoritesDeleteFavoriteArgs(%+v)", *p)
}

// Attributes:
//  - Success
type FavoritesDeleteFavoriteResult struct {
  Success *DeleteFavoriteResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewFavoritesDeleteFavoriteResult() *FavoritesDeleteFavoriteResult {
  return &FavoritesDeleteFavoriteResult{}
}

var FavoritesDeleteFavoriteResult_Success_DEFAULT *DeleteFavoriteResponse
func (p *FavoritesDeleteFavoriteResult) GetSuccess() *DeleteFavoriteResponse {
  if !p.IsSetSuccess() {
    return FavoritesDeleteFavoriteResult_Success_DEFAULT
  }
return p.Success
}
func (p *FavoritesDeleteFavoriteResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *FavoritesDeleteFavoriteResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *FavoritesDeleteFavoriteResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &DeleteFavoriteResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *FavoritesDeleteFavoriteResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DeleteFavorite_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *FavoritesDeleteFavoriteResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *FavoritesDeleteFavoriteResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("FavoritesDeleteFavoriteResult(%+v)", *p)
}


