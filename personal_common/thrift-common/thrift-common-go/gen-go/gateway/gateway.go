// Code generated by Thrift Compiler (0.16.0). DO NOT EDIT.

package gateway

import (
	"bytes"
	"context"
	"fmt"
	"time"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"thrift-common/gen-go/base"

)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

var _ = base.GoUnusedProtection__
// Attributes:
//  - TaskID
//  - UploadedBytes
//  - TotalBytes
//  - Progress
//  - Base
type UploadProgressNotifyRequest struct {
  TaskID int64 `thrift:"TaskID,1" db:"TaskID" json:"TaskID"`
  UploadedBytes int64 `thrift:"UploadedBytes,2" db:"UploadedBytes" json:"UploadedBytes"`
  TotalBytes int64 `thrift:"TotalBytes,3" db:"TotalBytes" json:"TotalBytes"`
  Progress float64 `thrift:"Progress,4" db:"Progress" json:"Progress"`
  // unused fields # 5 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewUploadProgressNotifyRequest() *UploadProgressNotifyRequest {
  return &UploadProgressNotifyRequest{}
}


func (p *UploadProgressNotifyRequest) GetTaskID() int64 {
  return p.TaskID
}

func (p *UploadProgressNotifyRequest) GetUploadedBytes() int64 {
  return p.UploadedBytes
}

func (p *UploadProgressNotifyRequest) GetTotalBytes() int64 {
  return p.TotalBytes
}

func (p *UploadProgressNotifyRequest) GetProgress() float64 {
  return p.Progress
}
var UploadProgressNotifyRequest_Base_DEFAULT *base.Base
func (p *UploadProgressNotifyRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return UploadProgressNotifyRequest_Base_DEFAULT
  }
return p.Base
}
func (p *UploadProgressNotifyRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *UploadProgressNotifyRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.DOUBLE {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *UploadProgressNotifyRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.TaskID = v
}
  return nil
}

func (p *UploadProgressNotifyRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.UploadedBytes = v
}
  return nil
}

func (p *UploadProgressNotifyRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.TotalBytes = v
}
  return nil
}

func (p *UploadProgressNotifyRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadDouble(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Progress = v
}
  return nil
}

func (p *UploadProgressNotifyRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *UploadProgressNotifyRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UploadProgressNotifyRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UploadProgressNotifyRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "TaskID", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:TaskID: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.TaskID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.TaskID (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:TaskID: ", p), err) }
  return err
}

func (p *UploadProgressNotifyRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "UploadedBytes", thrift.I64, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:UploadedBytes: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.UploadedBytes)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.UploadedBytes (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:UploadedBytes: ", p), err) }
  return err
}

func (p *UploadProgressNotifyRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "TotalBytes", thrift.I64, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:TotalBytes: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.TotalBytes)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.TotalBytes (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:TotalBytes: ", p), err) }
  return err
}

func (p *UploadProgressNotifyRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Progress", thrift.DOUBLE, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:Progress: ", p), err) }
  if err := oprot.WriteDouble(ctx, float64(p.Progress)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Progress (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:Progress: ", p), err) }
  return err
}

func (p *UploadProgressNotifyRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *UploadProgressNotifyRequest) Equals(other *UploadProgressNotifyRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.TaskID != other.TaskID { return false }
  if p.UploadedBytes != other.UploadedBytes { return false }
  if p.TotalBytes != other.TotalBytes { return false }
  if p.Progress != other.Progress { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *UploadProgressNotifyRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UploadProgressNotifyRequest(%+v)", *p)
}

// Attributes:
//  - BaseResp
type UploadProgressNotifyResponse struct {
  // unused fields # 1 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewUploadProgressNotifyResponse() *UploadProgressNotifyResponse {
  return &UploadProgressNotifyResponse{}
}

var UploadProgressNotifyResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *UploadProgressNotifyResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return UploadProgressNotifyResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *UploadProgressNotifyResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *UploadProgressNotifyResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *UploadProgressNotifyResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *UploadProgressNotifyResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UploadProgressNotifyResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UploadProgressNotifyResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *UploadProgressNotifyResponse) Equals(other *UploadProgressNotifyResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *UploadProgressNotifyResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UploadProgressNotifyResponse(%+v)", *p)
}

type Gateway interface {
  // Parameters:
  //  - Req
  UploadProgressNotify(ctx context.Context, req *UploadProgressNotifyRequest) (_r *UploadProgressNotifyResponse, _err error)
}

type GatewayClient struct {
  c thrift.TClient
  meta thrift.ResponseMeta
}

func NewGatewayClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *GatewayClient {
  return &GatewayClient{
    c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
  }
}

func NewGatewayClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *GatewayClient {
  return &GatewayClient{
    c: thrift.NewTStandardClient(iprot, oprot),
  }
}

func NewGatewayClient(c thrift.TClient) *GatewayClient {
  return &GatewayClient{
    c: c,
  }
}

func (p *GatewayClient) Client_() thrift.TClient {
  return p.c
}

func (p *GatewayClient) LastResponseMeta_() thrift.ResponseMeta {
  return p.meta
}

func (p *GatewayClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
  p.meta = meta
}

// Parameters:
//  - Req
func (p *GatewayClient) UploadProgressNotify(ctx context.Context, req *UploadProgressNotifyRequest) (_r *UploadProgressNotifyResponse, _err error) {
  var _args0 GatewayUploadProgressNotifyArgs
  _args0.Req = req
  var _result2 GatewayUploadProgressNotifyResult
  var _meta1 thrift.ResponseMeta
  _meta1, _err = p.Client_().Call(ctx, "UploadProgressNotify", &_args0, &_result2)
  p.SetLastResponseMeta_(_meta1)
  if _err != nil {
    return
  }
  if _ret3 := _result2.GetSuccess(); _ret3 != nil {
    return _ret3, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "UploadProgressNotify failed: unknown result")
}

type GatewayProcessor struct {
  processorMap map[string]thrift.TProcessorFunction
  handler Gateway
}

func (p *GatewayProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
  p.processorMap[key] = processor
}

func (p *GatewayProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
  processor, ok = p.processorMap[key]
  return processor, ok
}

func (p *GatewayProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
  return p.processorMap
}

func NewGatewayProcessor(handler Gateway) *GatewayProcessor {

  self4 := &GatewayProcessor{handler:handler, processorMap:make(map[string]thrift.TProcessorFunction)}
  self4.processorMap["UploadProgressNotify"] = &gatewayProcessorUploadProgressNotify{handler:handler}
return self4
}

func (p *GatewayProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
  if err2 != nil { return false, thrift.WrapTException(err2) }
  if processor, ok := p.GetProcessorFunction(name); ok {
    return processor.Process(ctx, seqId, iprot, oprot)
  }
  iprot.Skip(ctx, thrift.STRUCT)
  iprot.ReadMessageEnd(ctx)
  x5 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function " + name)
  oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
  x5.Write(ctx, oprot)
  oprot.WriteMessageEnd(ctx)
  oprot.Flush(ctx)
  return false, x5

}

type gatewayProcessorUploadProgressNotify struct {
  handler Gateway
}

func (p *gatewayProcessorUploadProgressNotify) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := GatewayUploadProgressNotifyArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "UploadProgressNotify", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := GatewayUploadProgressNotifyResult{}
  var retval *UploadProgressNotifyResponse
  if retval, err2 = p.handler.UploadProgressNotify(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing UploadProgressNotify: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "UploadProgressNotify", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "UploadProgressNotify", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}


// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//  - Req
type GatewayUploadProgressNotifyArgs struct {
  Req *UploadProgressNotifyRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewGatewayUploadProgressNotifyArgs() *GatewayUploadProgressNotifyArgs {
  return &GatewayUploadProgressNotifyArgs{}
}

var GatewayUploadProgressNotifyArgs_Req_DEFAULT *UploadProgressNotifyRequest
func (p *GatewayUploadProgressNotifyArgs) GetReq() *UploadProgressNotifyRequest {
  if !p.IsSetReq() {
    return GatewayUploadProgressNotifyArgs_Req_DEFAULT
  }
return p.Req
}
func (p *GatewayUploadProgressNotifyArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *GatewayUploadProgressNotifyArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GatewayUploadProgressNotifyArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &UploadProgressNotifyRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *GatewayUploadProgressNotifyArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UploadProgressNotify_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GatewayUploadProgressNotifyArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *GatewayUploadProgressNotifyArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GatewayUploadProgressNotifyArgs(%+v)", *p)
}

// Attributes:
//  - Success
type GatewayUploadProgressNotifyResult struct {
  Success *UploadProgressNotifyResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewGatewayUploadProgressNotifyResult() *GatewayUploadProgressNotifyResult {
  return &GatewayUploadProgressNotifyResult{}
}

var GatewayUploadProgressNotifyResult_Success_DEFAULT *UploadProgressNotifyResponse
func (p *GatewayUploadProgressNotifyResult) GetSuccess() *UploadProgressNotifyResponse {
  if !p.IsSetSuccess() {
    return GatewayUploadProgressNotifyResult_Success_DEFAULT
  }
return p.Success
}
func (p *GatewayUploadProgressNotifyResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *GatewayUploadProgressNotifyResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GatewayUploadProgressNotifyResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &UploadProgressNotifyResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *GatewayUploadProgressNotifyResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UploadProgressNotify_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GatewayUploadProgressNotifyResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *GatewayUploadProgressNotifyResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GatewayUploadProgressNotifyResult(%+v)", *p)
}


