// Code generated by Thrift Compiler (0.16.0). DO NOT EDIT.

package notes

import (
	"bytes"
	"context"
	"fmt"
	"time"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"thrift-common/gen-go/base"

)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

var _ = base.GoUnusedProtection__
// Attributes:
//  - Title
//  - Content
//  - NoteType
//  - Category
//  - Tags
//  - Base
type AddNoteRequest struct {
  Title string `thrift:"Title,1" db:"Title" json:"title"`
  Content string `thrift:"Content,2" db:"Content" json:"content"`
  NoteType *string `thrift:"NoteType,3" db:"NoteType" json:"note_type"`
  Category *string `thrift:"Category,4" db:"Category" json:"category"`
  Tags *string `thrift:"Tags,5" db:"Tags" json:"tags"`
  // unused fields # 6 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewAddNoteRequest() *AddNoteRequest {
  return &AddNoteRequest{}
}


func (p *AddNoteRequest) GetTitle() string {
  return p.Title
}

func (p *AddNoteRequest) GetContent() string {
  return p.Content
}
var AddNoteRequest_NoteType_DEFAULT string
func (p *AddNoteRequest) GetNoteType() string {
  if !p.IsSetNoteType() {
    return AddNoteRequest_NoteType_DEFAULT
  }
return *p.NoteType
}
var AddNoteRequest_Category_DEFAULT string
func (p *AddNoteRequest) GetCategory() string {
  if !p.IsSetCategory() {
    return AddNoteRequest_Category_DEFAULT
  }
return *p.Category
}
var AddNoteRequest_Tags_DEFAULT string
func (p *AddNoteRequest) GetTags() string {
  if !p.IsSetTags() {
    return AddNoteRequest_Tags_DEFAULT
  }
return *p.Tags
}
var AddNoteRequest_Base_DEFAULT *base.Base
func (p *AddNoteRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return AddNoteRequest_Base_DEFAULT
  }
return p.Base
}
func (p *AddNoteRequest) IsSetNoteType() bool {
  return p.NoteType != nil
}

func (p *AddNoteRequest) IsSetCategory() bool {
  return p.Category != nil
}

func (p *AddNoteRequest) IsSetTags() bool {
  return p.Tags != nil
}

func (p *AddNoteRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *AddNoteRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *AddNoteRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Title = v
}
  return nil
}

func (p *AddNoteRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Content = v
}
  return nil
}

func (p *AddNoteRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.NoteType = &v
}
  return nil
}

func (p *AddNoteRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Category = &v
}
  return nil
}

func (p *AddNoteRequest)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Tags = &v
}
  return nil
}

func (p *AddNoteRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *AddNoteRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AddNoteRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *AddNoteRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Title", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Title: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Title)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Title (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Title: ", p), err) }
  return err
}

func (p *AddNoteRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Content", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Content: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Content)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Content (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Content: ", p), err) }
  return err
}

func (p *AddNoteRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetNoteType() {
    if err := oprot.WriteFieldBegin(ctx, "NoteType", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:NoteType: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.NoteType)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.NoteType (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:NoteType: ", p), err) }
  }
  return err
}

func (p *AddNoteRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCategory() {
    if err := oprot.WriteFieldBegin(ctx, "Category", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:Category: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Category)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Category (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:Category: ", p), err) }
  }
  return err
}

func (p *AddNoteRequest) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetTags() {
    if err := oprot.WriteFieldBegin(ctx, "Tags", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:Tags: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Tags)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Tags (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:Tags: ", p), err) }
  }
  return err
}

func (p *AddNoteRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *AddNoteRequest) Equals(other *AddNoteRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Title != other.Title { return false }
  if p.Content != other.Content { return false }
  if p.NoteType != other.NoteType {
    if p.NoteType == nil || other.NoteType == nil {
      return false
    }
    if (*p.NoteType) != (*other.NoteType) { return false }
  }
  if p.Category != other.Category {
    if p.Category == nil || other.Category == nil {
      return false
    }
    if (*p.Category) != (*other.Category) { return false }
  }
  if p.Tags != other.Tags {
    if p.Tags == nil || other.Tags == nil {
      return false
    }
    if (*p.Tags) != (*other.Tags) { return false }
  }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *AddNoteRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("AddNoteRequest(%+v)", *p)
}

// Attributes:
//  - NoteId
//  - BaseResp
type AddNoteResponse struct {
  NoteId int64 `thrift:"NoteId,1" db:"NoteId" json:"NoteId"`
  // unused fields # 2 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewAddNoteResponse() *AddNoteResponse {
  return &AddNoteResponse{}
}


func (p *AddNoteResponse) GetNoteId() int64 {
  return p.NoteId
}
var AddNoteResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *AddNoteResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return AddNoteResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *AddNoteResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *AddNoteResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *AddNoteResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.NoteId = v
}
  return nil
}

func (p *AddNoteResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *AddNoteResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AddNoteResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *AddNoteResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "NoteId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:NoteId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.NoteId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.NoteId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:NoteId: ", p), err) }
  return err
}

func (p *AddNoteResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *AddNoteResponse) Equals(other *AddNoteResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.NoteId != other.NoteId { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *AddNoteResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("AddNoteResponse(%+v)", *p)
}

// Attributes:
//  - Offset
//  - Count
//  - Keyword
//  - Category
//  - Base
type GetNotesPageRequest struct {
  Offset int64 `thrift:"Offset,1" db:"Offset" json:"Offset"`
  Count int64 `thrift:"Count,2" db:"Count" json:"Count"`
  Keyword *string `thrift:"Keyword,3" db:"Keyword" json:"Keyword,omitempty"`
  Category *string `thrift:"Category,4" db:"Category" json:"Category,omitempty"`
  // unused fields # 5 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewGetNotesPageRequest() *GetNotesPageRequest {
  return &GetNotesPageRequest{}
}


func (p *GetNotesPageRequest) GetOffset() int64 {
  return p.Offset
}

func (p *GetNotesPageRequest) GetCount() int64 {
  return p.Count
}
var GetNotesPageRequest_Keyword_DEFAULT string
func (p *GetNotesPageRequest) GetKeyword() string {
  if !p.IsSetKeyword() {
    return GetNotesPageRequest_Keyword_DEFAULT
  }
return *p.Keyword
}
var GetNotesPageRequest_Category_DEFAULT string
func (p *GetNotesPageRequest) GetCategory() string {
  if !p.IsSetCategory() {
    return GetNotesPageRequest_Category_DEFAULT
  }
return *p.Category
}
var GetNotesPageRequest_Base_DEFAULT *base.Base
func (p *GetNotesPageRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return GetNotesPageRequest_Base_DEFAULT
  }
return p.Base
}
func (p *GetNotesPageRequest) IsSetKeyword() bool {
  return p.Keyword != nil
}

func (p *GetNotesPageRequest) IsSetCategory() bool {
  return p.Category != nil
}

func (p *GetNotesPageRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *GetNotesPageRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetNotesPageRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Offset = v
}
  return nil
}

func (p *GetNotesPageRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Count = v
}
  return nil
}

func (p *GetNotesPageRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Keyword = &v
}
  return nil
}

func (p *GetNotesPageRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Category = &v
}
  return nil
}

func (p *GetNotesPageRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *GetNotesPageRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetNotesPageRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetNotesPageRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Offset", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Offset: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Offset)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Offset (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Offset: ", p), err) }
  return err
}

func (p *GetNotesPageRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Count", thrift.I64, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Count: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Count)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Count (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Count: ", p), err) }
  return err
}

func (p *GetNotesPageRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetKeyword() {
    if err := oprot.WriteFieldBegin(ctx, "Keyword", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Keyword: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Keyword)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Keyword (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Keyword: ", p), err) }
  }
  return err
}

func (p *GetNotesPageRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCategory() {
    if err := oprot.WriteFieldBegin(ctx, "Category", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:Category: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Category)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Category (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:Category: ", p), err) }
  }
  return err
}

func (p *GetNotesPageRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *GetNotesPageRequest) Equals(other *GetNotesPageRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Offset != other.Offset { return false }
  if p.Count != other.Count { return false }
  if p.Keyword != other.Keyword {
    if p.Keyword == nil || other.Keyword == nil {
      return false
    }
    if (*p.Keyword) != (*other.Keyword) { return false }
  }
  if p.Category != other.Category {
    if p.Category == nil || other.Category == nil {
      return false
    }
    if (*p.Category) != (*other.Category) { return false }
  }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *GetNotesPageRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetNotesPageRequest(%+v)", *p)
}

// Attributes:
//  - ID
//  - Title
//  - Content
//  - NoteType
//  - Category
//  - Tags
//  - CreateTime
//  - UpdateTime
type NoteItem struct {
  ID int64 `thrift:"Id,1" db:"Id" json:"id"`
  Title string `thrift:"Title,2" db:"Title" json:"title"`
  Content string `thrift:"Content,3" db:"Content" json:"content"`
  NoteType string `thrift:"NoteType,4" db:"NoteType" json:"note_type"`
  Category *string `thrift:"Category,5" db:"Category" json:"category"`
  Tags *string `thrift:"Tags,6" db:"Tags" json:"tags"`
  CreateTime string `thrift:"CreateTime,7" db:"CreateTime" json:"create_time"`
  UpdateTime string `thrift:"UpdateTime,8" db:"UpdateTime" json:"update_time"`
}

func NewNoteItem() *NoteItem {
  return &NoteItem{}
}


func (p *NoteItem) GetID() int64 {
  return p.ID
}

func (p *NoteItem) GetTitle() string {
  return p.Title
}

func (p *NoteItem) GetContent() string {
  return p.Content
}

func (p *NoteItem) GetNoteType() string {
  return p.NoteType
}
var NoteItem_Category_DEFAULT string
func (p *NoteItem) GetCategory() string {
  if !p.IsSetCategory() {
    return NoteItem_Category_DEFAULT
  }
return *p.Category
}
var NoteItem_Tags_DEFAULT string
func (p *NoteItem) GetTags() string {
  if !p.IsSetTags() {
    return NoteItem_Tags_DEFAULT
  }
return *p.Tags
}

func (p *NoteItem) GetCreateTime() string {
  return p.CreateTime
}

func (p *NoteItem) GetUpdateTime() string {
  return p.UpdateTime
}
func (p *NoteItem) IsSetCategory() bool {
  return p.Category != nil
}

func (p *NoteItem) IsSetTags() bool {
  return p.Tags != nil
}

func (p *NoteItem) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *NoteItem)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ID = v
}
  return nil
}

func (p *NoteItem)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Title = v
}
  return nil
}

func (p *NoteItem)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Content = v
}
  return nil
}

func (p *NoteItem)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.NoteType = v
}
  return nil
}

func (p *NoteItem)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Category = &v
}
  return nil
}

func (p *NoteItem)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Tags = &v
}
  return nil
}

func (p *NoteItem)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.CreateTime = v
}
  return nil
}

func (p *NoteItem)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.UpdateTime = v
}
  return nil
}

func (p *NoteItem) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "NoteItem"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *NoteItem) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Id", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Id: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.ID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Id (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Id: ", p), err) }
  return err
}

func (p *NoteItem) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Title", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Title: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Title)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Title (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Title: ", p), err) }
  return err
}

func (p *NoteItem) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Content", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Content: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Content)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Content (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Content: ", p), err) }
  return err
}

func (p *NoteItem) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "NoteType", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:NoteType: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.NoteType)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.NoteType (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:NoteType: ", p), err) }
  return err
}

func (p *NoteItem) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCategory() {
    if err := oprot.WriteFieldBegin(ctx, "Category", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:Category: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Category)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Category (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:Category: ", p), err) }
  }
  return err
}

func (p *NoteItem) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetTags() {
    if err := oprot.WriteFieldBegin(ctx, "Tags", thrift.STRING, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:Tags: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Tags)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Tags (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:Tags: ", p), err) }
  }
  return err
}

func (p *NoteItem) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "CreateTime", thrift.STRING, 7); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:CreateTime: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.CreateTime)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.CreateTime (7) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 7:CreateTime: ", p), err) }
  return err
}

func (p *NoteItem) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "UpdateTime", thrift.STRING, 8); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:UpdateTime: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.UpdateTime)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.UpdateTime (8) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 8:UpdateTime: ", p), err) }
  return err
}

func (p *NoteItem) Equals(other *NoteItem) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ID != other.ID { return false }
  if p.Title != other.Title { return false }
  if p.Content != other.Content { return false }
  if p.NoteType != other.NoteType { return false }
  if p.Category != other.Category {
    if p.Category == nil || other.Category == nil {
      return false
    }
    if (*p.Category) != (*other.Category) { return false }
  }
  if p.Tags != other.Tags {
    if p.Tags == nil || other.Tags == nil {
      return false
    }
    if (*p.Tags) != (*other.Tags) { return false }
  }
  if p.CreateTime != other.CreateTime { return false }
  if p.UpdateTime != other.UpdateTime { return false }
  return true
}

func (p *NoteItem) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("NoteItem(%+v)", *p)
}

// Attributes:
//  - NotesList
//  - HasMore
//  - Total
//  - BaseResp
type GetNotesPageResponse struct {
  NotesList []*NoteItem `thrift:"NotesList,1" db:"NotesList" json:"NotesList"`
  HasMore bool `thrift:"HasMore,2" db:"HasMore" json:"HasMore"`
  Total int64 `thrift:"Total,3" db:"Total" json:"Total"`
  // unused fields # 4 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewGetNotesPageResponse() *GetNotesPageResponse {
  return &GetNotesPageResponse{}
}


func (p *GetNotesPageResponse) GetNotesList() []*NoteItem {
  return p.NotesList
}

func (p *GetNotesPageResponse) GetHasMore() bool {
  return p.HasMore
}

func (p *GetNotesPageResponse) GetTotal() int64 {
  return p.Total
}
var GetNotesPageResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *GetNotesPageResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return GetNotesPageResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *GetNotesPageResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *GetNotesPageResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetNotesPageResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*NoteItem, 0, size)
  p.NotesList =  tSlice
  for i := 0; i < size; i ++ {
    _elem0 := &NoteItem{}
    if err := _elem0.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem0), err)
    }
    p.NotesList = append(p.NotesList, _elem0)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *GetNotesPageResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.HasMore = v
}
  return nil
}

func (p *GetNotesPageResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Total = v
}
  return nil
}

func (p *GetNotesPageResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *GetNotesPageResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetNotesPageResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetNotesPageResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "NotesList", thrift.LIST, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:NotesList: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.NotesList)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.NotesList {
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:NotesList: ", p), err) }
  return err
}

func (p *GetNotesPageResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "HasMore", thrift.BOOL, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:HasMore: ", p), err) }
  if err := oprot.WriteBool(ctx, bool(p.HasMore)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.HasMore (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:HasMore: ", p), err) }
  return err
}

func (p *GetNotesPageResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Total", thrift.I64, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Total: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Total)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Total (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Total: ", p), err) }
  return err
}

func (p *GetNotesPageResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *GetNotesPageResponse) Equals(other *GetNotesPageResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if len(p.NotesList) != len(other.NotesList) { return false }
  for i, _tgt := range p.NotesList {
    _src1 := other.NotesList[i]
    if !_tgt.Equals(_src1) { return false }
  }
  if p.HasMore != other.HasMore { return false }
  if p.Total != other.Total { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *GetNotesPageResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetNotesPageResponse(%+v)", *p)
}

// Attributes:
//  - NoteId
//  - Title
//  - Content
//  - NoteType
//  - Category
//  - Tags
//  - Base
type UpdateNoteRequest struct {
  NoteId int64 `thrift:"NoteId,1" db:"NoteId" json:"NoteId"`
  Title *string `thrift:"Title,2" db:"Title" json:"title"`
  Content *string `thrift:"Content,3" db:"Content" json:"content"`
  NoteType *string `thrift:"NoteType,4" db:"NoteType" json:"note_type"`
  Category *string `thrift:"Category,5" db:"Category" json:"category"`
  Tags *string `thrift:"Tags,6" db:"Tags" json:"tags"`
  // unused fields # 7 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewUpdateNoteRequest() *UpdateNoteRequest {
  return &UpdateNoteRequest{}
}


func (p *UpdateNoteRequest) GetNoteId() int64 {
  return p.NoteId
}
var UpdateNoteRequest_Title_DEFAULT string
func (p *UpdateNoteRequest) GetTitle() string {
  if !p.IsSetTitle() {
    return UpdateNoteRequest_Title_DEFAULT
  }
return *p.Title
}
var UpdateNoteRequest_Content_DEFAULT string
func (p *UpdateNoteRequest) GetContent() string {
  if !p.IsSetContent() {
    return UpdateNoteRequest_Content_DEFAULT
  }
return *p.Content
}
var UpdateNoteRequest_NoteType_DEFAULT string
func (p *UpdateNoteRequest) GetNoteType() string {
  if !p.IsSetNoteType() {
    return UpdateNoteRequest_NoteType_DEFAULT
  }
return *p.NoteType
}
var UpdateNoteRequest_Category_DEFAULT string
func (p *UpdateNoteRequest) GetCategory() string {
  if !p.IsSetCategory() {
    return UpdateNoteRequest_Category_DEFAULT
  }
return *p.Category
}
var UpdateNoteRequest_Tags_DEFAULT string
func (p *UpdateNoteRequest) GetTags() string {
  if !p.IsSetTags() {
    return UpdateNoteRequest_Tags_DEFAULT
  }
return *p.Tags
}
var UpdateNoteRequest_Base_DEFAULT *base.Base
func (p *UpdateNoteRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return UpdateNoteRequest_Base_DEFAULT
  }
return p.Base
}
func (p *UpdateNoteRequest) IsSetTitle() bool {
  return p.Title != nil
}

func (p *UpdateNoteRequest) IsSetContent() bool {
  return p.Content != nil
}

func (p *UpdateNoteRequest) IsSetNoteType() bool {
  return p.NoteType != nil
}

func (p *UpdateNoteRequest) IsSetCategory() bool {
  return p.Category != nil
}

func (p *UpdateNoteRequest) IsSetTags() bool {
  return p.Tags != nil
}

func (p *UpdateNoteRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *UpdateNoteRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *UpdateNoteRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.NoteId = v
}
  return nil
}

func (p *UpdateNoteRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Title = &v
}
  return nil
}

func (p *UpdateNoteRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Content = &v
}
  return nil
}

func (p *UpdateNoteRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.NoteType = &v
}
  return nil
}

func (p *UpdateNoteRequest)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Category = &v
}
  return nil
}

func (p *UpdateNoteRequest)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Tags = &v
}
  return nil
}

func (p *UpdateNoteRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *UpdateNoteRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UpdateNoteRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UpdateNoteRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "NoteId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:NoteId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.NoteId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.NoteId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:NoteId: ", p), err) }
  return err
}

func (p *UpdateNoteRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetTitle() {
    if err := oprot.WriteFieldBegin(ctx, "Title", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Title: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Title)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Title (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Title: ", p), err) }
  }
  return err
}

func (p *UpdateNoteRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetContent() {
    if err := oprot.WriteFieldBegin(ctx, "Content", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Content: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Content)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Content (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Content: ", p), err) }
  }
  return err
}

func (p *UpdateNoteRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetNoteType() {
    if err := oprot.WriteFieldBegin(ctx, "NoteType", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:NoteType: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.NoteType)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.NoteType (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:NoteType: ", p), err) }
  }
  return err
}

func (p *UpdateNoteRequest) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetCategory() {
    if err := oprot.WriteFieldBegin(ctx, "Category", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:Category: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Category)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Category (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:Category: ", p), err) }
  }
  return err
}

func (p *UpdateNoteRequest) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetTags() {
    if err := oprot.WriteFieldBegin(ctx, "Tags", thrift.STRING, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:Tags: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Tags)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Tags (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:Tags: ", p), err) }
  }
  return err
}

func (p *UpdateNoteRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *UpdateNoteRequest) Equals(other *UpdateNoteRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.NoteId != other.NoteId { return false }
  if p.Title != other.Title {
    if p.Title == nil || other.Title == nil {
      return false
    }
    if (*p.Title) != (*other.Title) { return false }
  }
  if p.Content != other.Content {
    if p.Content == nil || other.Content == nil {
      return false
    }
    if (*p.Content) != (*other.Content) { return false }
  }
  if p.NoteType != other.NoteType {
    if p.NoteType == nil || other.NoteType == nil {
      return false
    }
    if (*p.NoteType) != (*other.NoteType) { return false }
  }
  if p.Category != other.Category {
    if p.Category == nil || other.Category == nil {
      return false
    }
    if (*p.Category) != (*other.Category) { return false }
  }
  if p.Tags != other.Tags {
    if p.Tags == nil || other.Tags == nil {
      return false
    }
    if (*p.Tags) != (*other.Tags) { return false }
  }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *UpdateNoteRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UpdateNoteRequest(%+v)", *p)
}

// Attributes:
//  - BaseResp
type UpdateNoteResponse struct {
  // unused fields # 1 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewUpdateNoteResponse() *UpdateNoteResponse {
  return &UpdateNoteResponse{}
}

var UpdateNoteResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *UpdateNoteResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return UpdateNoteResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *UpdateNoteResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *UpdateNoteResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *UpdateNoteResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *UpdateNoteResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UpdateNoteResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *UpdateNoteResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *UpdateNoteResponse) Equals(other *UpdateNoteResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *UpdateNoteResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("UpdateNoteResponse(%+v)", *p)
}

// Attributes:
//  - NoteId
//  - Base
type DeleteNoteRequest struct {
  NoteId int64 `thrift:"NoteId,1" db:"NoteId" json:"NoteId"`
  // unused fields # 2 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewDeleteNoteRequest() *DeleteNoteRequest {
  return &DeleteNoteRequest{}
}


func (p *DeleteNoteRequest) GetNoteId() int64 {
  return p.NoteId
}
var DeleteNoteRequest_Base_DEFAULT *base.Base
func (p *DeleteNoteRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return DeleteNoteRequest_Base_DEFAULT
  }
return p.Base
}
func (p *DeleteNoteRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *DeleteNoteRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DeleteNoteRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.NoteId = v
}
  return nil
}

func (p *DeleteNoteRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *DeleteNoteRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DeleteNoteRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DeleteNoteRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "NoteId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:NoteId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.NoteId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.NoteId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:NoteId: ", p), err) }
  return err
}

func (p *DeleteNoteRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *DeleteNoteRequest) Equals(other *DeleteNoteRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.NoteId != other.NoteId { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *DeleteNoteRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DeleteNoteRequest(%+v)", *p)
}

// Attributes:
//  - BaseResp
type DeleteNoteResponse struct {
  // unused fields # 1 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewDeleteNoteResponse() *DeleteNoteResponse {
  return &DeleteNoteResponse{}
}

var DeleteNoteResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *DeleteNoteResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return DeleteNoteResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *DeleteNoteResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *DeleteNoteResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *DeleteNoteResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *DeleteNoteResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DeleteNoteResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *DeleteNoteResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *DeleteNoteResponse) Equals(other *DeleteNoteResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *DeleteNoteResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("DeleteNoteResponse(%+v)", *p)
}

// Attributes:
//  - NoteId
//  - Base
type GetNoteRequest struct {
  NoteId int64 `thrift:"NoteId,1" db:"NoteId" json:"NoteId"`
  // unused fields # 2 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewGetNoteRequest() *GetNoteRequest {
  return &GetNoteRequest{}
}


func (p *GetNoteRequest) GetNoteId() int64 {
  return p.NoteId
}
var GetNoteRequest_Base_DEFAULT *base.Base
func (p *GetNoteRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return GetNoteRequest_Base_DEFAULT
  }
return p.Base
}
func (p *GetNoteRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *GetNoteRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetNoteRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.NoteId = v
}
  return nil
}

func (p *GetNoteRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *GetNoteRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetNoteRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetNoteRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "NoteId", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:NoteId: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.NoteId)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.NoteId (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:NoteId: ", p), err) }
  return err
}

func (p *GetNoteRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *GetNoteRequest) Equals(other *GetNoteRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.NoteId != other.NoteId { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *GetNoteRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetNoteRequest(%+v)", *p)
}

// Attributes:
//  - Note
//  - BaseResp
type GetNoteResponse struct {
  Note *NoteItem `thrift:"Note,1" db:"Note" json:"Note"`
  // unused fields # 2 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewGetNoteResponse() *GetNoteResponse {
  return &GetNoteResponse{}
}

var GetNoteResponse_Note_DEFAULT *NoteItem
func (p *GetNoteResponse) GetNote() *NoteItem {
  if !p.IsSetNote() {
    return GetNoteResponse_Note_DEFAULT
  }
return p.Note
}
var GetNoteResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *GetNoteResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return GetNoteResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *GetNoteResponse) IsSetNote() bool {
  return p.Note != nil
}

func (p *GetNoteResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *GetNoteResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetNoteResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Note = &NoteItem{}
  if err := p.Note.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Note), err)
  }
  return nil
}

func (p *GetNoteResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *GetNoteResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetNoteResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetNoteResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Note", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Note: ", p), err) }
  if err := p.Note.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Note), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Note: ", p), err) }
  return err
}

func (p *GetNoteResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *GetNoteResponse) Equals(other *GetNoteResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.Note.Equals(other.Note) { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *GetNoteResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetNoteResponse(%+v)", *p)
}

type Notes interface {
  // Parameters:
  //  - Req
  AddNote(ctx context.Context, req *AddNoteRequest) (_r *AddNoteResponse, _err error)
  // Parameters:
  //  - Req
  GetNotesPage(ctx context.Context, req *GetNotesPageRequest) (_r *GetNotesPageResponse, _err error)
  // Parameters:
  //  - Req
  UpdateNote(ctx context.Context, req *UpdateNoteRequest) (_r *UpdateNoteResponse, _err error)
  // Parameters:
  //  - Req
  DeleteNote(ctx context.Context, req *DeleteNoteRequest) (_r *DeleteNoteResponse, _err error)
  // Parameters:
  //  - Req
  GetNote(ctx context.Context, req *GetNoteRequest) (_r *GetNoteResponse, _err error)
}

type NotesClient struct {
  c thrift.TClient
  meta thrift.ResponseMeta
}

func NewNotesClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *NotesClient {
  return &NotesClient{
    c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
  }
}

func NewNotesClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *NotesClient {
  return &NotesClient{
    c: thrift.NewTStandardClient(iprot, oprot),
  }
}

func NewNotesClient(c thrift.TClient) *NotesClient {
  return &NotesClient{
    c: c,
  }
}

func (p *NotesClient) Client_() thrift.TClient {
  return p.c
}

func (p *NotesClient) LastResponseMeta_() thrift.ResponseMeta {
  return p.meta
}

func (p *NotesClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
  p.meta = meta
}

// Parameters:
//  - Req
func (p *NotesClient) AddNote(ctx context.Context, req *AddNoteRequest) (_r *AddNoteResponse, _err error) {
  var _args2 NotesAddNoteArgs
  _args2.Req = req
  var _result4 NotesAddNoteResult
  var _meta3 thrift.ResponseMeta
  _meta3, _err = p.Client_().Call(ctx, "AddNote", &_args2, &_result4)
  p.SetLastResponseMeta_(_meta3)
  if _err != nil {
    return
  }
  if _ret5 := _result4.GetSuccess(); _ret5 != nil {
    return _ret5, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "AddNote failed: unknown result")
}

// Parameters:
//  - Req
func (p *NotesClient) GetNotesPage(ctx context.Context, req *GetNotesPageRequest) (_r *GetNotesPageResponse, _err error) {
  var _args6 NotesGetNotesPageArgs
  _args6.Req = req
  var _result8 NotesGetNotesPageResult
  var _meta7 thrift.ResponseMeta
  _meta7, _err = p.Client_().Call(ctx, "GetNotesPage", &_args6, &_result8)
  p.SetLastResponseMeta_(_meta7)
  if _err != nil {
    return
  }
  if _ret9 := _result8.GetSuccess(); _ret9 != nil {
    return _ret9, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "GetNotesPage failed: unknown result")
}

// Parameters:
//  - Req
func (p *NotesClient) UpdateNote(ctx context.Context, req *UpdateNoteRequest) (_r *UpdateNoteResponse, _err error) {
  var _args10 NotesUpdateNoteArgs
  _args10.Req = req
  var _result12 NotesUpdateNoteResult
  var _meta11 thrift.ResponseMeta
  _meta11, _err = p.Client_().Call(ctx, "UpdateNote", &_args10, &_result12)
  p.SetLastResponseMeta_(_meta11)
  if _err != nil {
    return
  }
  if _ret13 := _result12.GetSuccess(); _ret13 != nil {
    return _ret13, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "UpdateNote failed: unknown result")
}

// Parameters:
//  - Req
func (p *NotesClient) DeleteNote(ctx context.Context, req *DeleteNoteRequest) (_r *DeleteNoteResponse, _err error) {
  var _args14 NotesDeleteNoteArgs
  _args14.Req = req
  var _result16 NotesDeleteNoteResult
  var _meta15 thrift.ResponseMeta
  _meta15, _err = p.Client_().Call(ctx, "DeleteNote", &_args14, &_result16)
  p.SetLastResponseMeta_(_meta15)
  if _err != nil {
    return
  }
  if _ret17 := _result16.GetSuccess(); _ret17 != nil {
    return _ret17, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "DeleteNote failed: unknown result")
}

// Parameters:
//  - Req
func (p *NotesClient) GetNote(ctx context.Context, req *GetNoteRequest) (_r *GetNoteResponse, _err error) {
  var _args18 NotesGetNoteArgs
  _args18.Req = req
  var _result20 NotesGetNoteResult
  var _meta19 thrift.ResponseMeta
  _meta19, _err = p.Client_().Call(ctx, "GetNote", &_args18, &_result20)
  p.SetLastResponseMeta_(_meta19)
  if _err != nil {
    return
  }
  if _ret21 := _result20.GetSuccess(); _ret21 != nil {
    return _ret21, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "GetNote failed: unknown result")
}

type NotesProcessor struct {
  processorMap map[string]thrift.TProcessorFunction
  handler Notes
}

func (p *NotesProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
  p.processorMap[key] = processor
}

func (p *NotesProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
  processor, ok = p.processorMap[key]
  return processor, ok
}

func (p *NotesProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
  return p.processorMap
}

func NewNotesProcessor(handler Notes) *NotesProcessor {

  self22 := &NotesProcessor{handler:handler, processorMap:make(map[string]thrift.TProcessorFunction)}
  self22.processorMap["AddNote"] = &notesProcessorAddNote{handler:handler}
  self22.processorMap["GetNotesPage"] = &notesProcessorGetNotesPage{handler:handler}
  self22.processorMap["UpdateNote"] = &notesProcessorUpdateNote{handler:handler}
  self22.processorMap["DeleteNote"] = &notesProcessorDeleteNote{handler:handler}
  self22.processorMap["GetNote"] = &notesProcessorGetNote{handler:handler}
return self22
}

func (p *NotesProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
  if err2 != nil { return false, thrift.WrapTException(err2) }
  if processor, ok := p.GetProcessorFunction(name); ok {
    return processor.Process(ctx, seqId, iprot, oprot)
  }
  iprot.Skip(ctx, thrift.STRUCT)
  iprot.ReadMessageEnd(ctx)
  x23 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function " + name)
  oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
  x23.Write(ctx, oprot)
  oprot.WriteMessageEnd(ctx)
  oprot.Flush(ctx)
  return false, x23

}

type notesProcessorAddNote struct {
  handler Notes
}

func (p *notesProcessorAddNote) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := NotesAddNoteArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "AddNote", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := NotesAddNoteResult{}
  var retval *AddNoteResponse
  if retval, err2 = p.handler.AddNote(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing AddNote: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "AddNote", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "AddNote", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type notesProcessorGetNotesPage struct {
  handler Notes
}

func (p *notesProcessorGetNotesPage) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := NotesGetNotesPageArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "GetNotesPage", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := NotesGetNotesPageResult{}
  var retval *GetNotesPageResponse
  if retval, err2 = p.handler.GetNotesPage(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetNotesPage: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "GetNotesPage", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "GetNotesPage", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type notesProcessorUpdateNote struct {
  handler Notes
}

func (p *notesProcessorUpdateNote) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := NotesUpdateNoteArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "UpdateNote", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := NotesUpdateNoteResult{}
  var retval *UpdateNoteResponse
  if retval, err2 = p.handler.UpdateNote(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing UpdateNote: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "UpdateNote", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "UpdateNote", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type notesProcessorDeleteNote struct {
  handler Notes
}

func (p *notesProcessorDeleteNote) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := NotesDeleteNoteArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "DeleteNote", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := NotesDeleteNoteResult{}
  var retval *DeleteNoteResponse
  if retval, err2 = p.handler.DeleteNote(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing DeleteNote: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "DeleteNote", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "DeleteNote", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type notesProcessorGetNote struct {
  handler Notes
}

func (p *notesProcessorGetNote) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := NotesGetNoteArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "GetNote", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := NotesGetNoteResult{}
  var retval *GetNoteResponse
  if retval, err2 = p.handler.GetNote(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetNote: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "GetNote", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "GetNote", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}


// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//  - Req
type NotesAddNoteArgs struct {
  Req *AddNoteRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewNotesAddNoteArgs() *NotesAddNoteArgs {
  return &NotesAddNoteArgs{}
}

var NotesAddNoteArgs_Req_DEFAULT *AddNoteRequest
func (p *NotesAddNoteArgs) GetReq() *AddNoteRequest {
  if !p.IsSetReq() {
    return NotesAddNoteArgs_Req_DEFAULT
  }
return p.Req
}
func (p *NotesAddNoteArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *NotesAddNoteArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *NotesAddNoteArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &AddNoteRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *NotesAddNoteArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AddNote_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *NotesAddNoteArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *NotesAddNoteArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("NotesAddNoteArgs(%+v)", *p)
}

// Attributes:
//  - Success
type NotesAddNoteResult struct {
  Success *AddNoteResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewNotesAddNoteResult() *NotesAddNoteResult {
  return &NotesAddNoteResult{}
}

var NotesAddNoteResult_Success_DEFAULT *AddNoteResponse
func (p *NotesAddNoteResult) GetSuccess() *AddNoteResponse {
  if !p.IsSetSuccess() {
    return NotesAddNoteResult_Success_DEFAULT
  }
return p.Success
}
func (p *NotesAddNoteResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *NotesAddNoteResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *NotesAddNoteResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &AddNoteResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *NotesAddNoteResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AddNote_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *NotesAddNoteResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *NotesAddNoteResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("NotesAddNoteResult(%+v)", *p)
}

// Attributes:
//  - Req
type NotesGetNotesPageArgs struct {
  Req *GetNotesPageRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewNotesGetNotesPageArgs() *NotesGetNotesPageArgs {
  return &NotesGetNotesPageArgs{}
}

var NotesGetNotesPageArgs_Req_DEFAULT *GetNotesPageRequest
func (p *NotesGetNotesPageArgs) GetReq() *GetNotesPageRequest {
  if !p.IsSetReq() {
    return NotesGetNotesPageArgs_Req_DEFAULT
  }
return p.Req
}
func (p *NotesGetNotesPageArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *NotesGetNotesPageArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *NotesGetNotesPageArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &GetNotesPageRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *NotesGetNotesPageArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetNotesPage_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *NotesGetNotesPageArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *NotesGetNotesPageArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("NotesGetNotesPageArgs(%+v)", *p)
}

// Attributes:
//  - Success
type NotesGetNotesPageResult struct {
  Success *GetNotesPageResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewNotesGetNotesPageResult() *NotesGetNotesPageResult {
  return &NotesGetNotesPageResult{}
}

var NotesGetNotesPageResult_Success_DEFAULT *GetNotesPageResponse
func (p *NotesGetNotesPageResult) GetSuccess() *GetNotesPageResponse {
  if !p.IsSetSuccess() {
    return NotesGetNotesPageResult_Success_DEFAULT
  }
return p.Success
}
func (p *NotesGetNotesPageResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *NotesGetNotesPageResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *NotesGetNotesPageResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &GetNotesPageResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *NotesGetNotesPageResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetNotesPage_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *NotesGetNotesPageResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *NotesGetNotesPageResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("NotesGetNotesPageResult(%+v)", *p)
}

// Attributes:
//  - Req
type NotesUpdateNoteArgs struct {
  Req *UpdateNoteRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewNotesUpdateNoteArgs() *NotesUpdateNoteArgs {
  return &NotesUpdateNoteArgs{}
}

var NotesUpdateNoteArgs_Req_DEFAULT *UpdateNoteRequest
func (p *NotesUpdateNoteArgs) GetReq() *UpdateNoteRequest {
  if !p.IsSetReq() {
    return NotesUpdateNoteArgs_Req_DEFAULT
  }
return p.Req
}
func (p *NotesUpdateNoteArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *NotesUpdateNoteArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *NotesUpdateNoteArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &UpdateNoteRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *NotesUpdateNoteArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UpdateNote_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *NotesUpdateNoteArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *NotesUpdateNoteArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("NotesUpdateNoteArgs(%+v)", *p)
}

// Attributes:
//  - Success
type NotesUpdateNoteResult struct {
  Success *UpdateNoteResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewNotesUpdateNoteResult() *NotesUpdateNoteResult {
  return &NotesUpdateNoteResult{}
}

var NotesUpdateNoteResult_Success_DEFAULT *UpdateNoteResponse
func (p *NotesUpdateNoteResult) GetSuccess() *UpdateNoteResponse {
  if !p.IsSetSuccess() {
    return NotesUpdateNoteResult_Success_DEFAULT
  }
return p.Success
}
func (p *NotesUpdateNoteResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *NotesUpdateNoteResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *NotesUpdateNoteResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &UpdateNoteResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *NotesUpdateNoteResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "UpdateNote_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *NotesUpdateNoteResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *NotesUpdateNoteResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("NotesUpdateNoteResult(%+v)", *p)
}

// Attributes:
//  - Req
type NotesDeleteNoteArgs struct {
  Req *DeleteNoteRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewNotesDeleteNoteArgs() *NotesDeleteNoteArgs {
  return &NotesDeleteNoteArgs{}
}

var NotesDeleteNoteArgs_Req_DEFAULT *DeleteNoteRequest
func (p *NotesDeleteNoteArgs) GetReq() *DeleteNoteRequest {
  if !p.IsSetReq() {
    return NotesDeleteNoteArgs_Req_DEFAULT
  }
return p.Req
}
func (p *NotesDeleteNoteArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *NotesDeleteNoteArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *NotesDeleteNoteArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &DeleteNoteRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *NotesDeleteNoteArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DeleteNote_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *NotesDeleteNoteArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *NotesDeleteNoteArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("NotesDeleteNoteArgs(%+v)", *p)
}

// Attributes:
//  - Success
type NotesDeleteNoteResult struct {
  Success *DeleteNoteResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewNotesDeleteNoteResult() *NotesDeleteNoteResult {
  return &NotesDeleteNoteResult{}
}

var NotesDeleteNoteResult_Success_DEFAULT *DeleteNoteResponse
func (p *NotesDeleteNoteResult) GetSuccess() *DeleteNoteResponse {
  if !p.IsSetSuccess() {
    return NotesDeleteNoteResult_Success_DEFAULT
  }
return p.Success
}
func (p *NotesDeleteNoteResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *NotesDeleteNoteResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *NotesDeleteNoteResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &DeleteNoteResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *NotesDeleteNoteResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "DeleteNote_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *NotesDeleteNoteResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *NotesDeleteNoteResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("NotesDeleteNoteResult(%+v)", *p)
}

// Attributes:
//  - Req
type NotesGetNoteArgs struct {
  Req *GetNoteRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewNotesGetNoteArgs() *NotesGetNoteArgs {
  return &NotesGetNoteArgs{}
}

var NotesGetNoteArgs_Req_DEFAULT *GetNoteRequest
func (p *NotesGetNoteArgs) GetReq() *GetNoteRequest {
  if !p.IsSetReq() {
    return NotesGetNoteArgs_Req_DEFAULT
  }
return p.Req
}
func (p *NotesGetNoteArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *NotesGetNoteArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *NotesGetNoteArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &GetNoteRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *NotesGetNoteArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetNote_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *NotesGetNoteArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *NotesGetNoteArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("NotesGetNoteArgs(%+v)", *p)
}

// Attributes:
//  - Success
type NotesGetNoteResult struct {
  Success *GetNoteResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewNotesGetNoteResult() *NotesGetNoteResult {
  return &NotesGetNoteResult{}
}

var NotesGetNoteResult_Success_DEFAULT *GetNoteResponse
func (p *NotesGetNoteResult) GetSuccess() *GetNoteResponse {
  if !p.IsSetSuccess() {
    return NotesGetNoteResult_Success_DEFAULT
  }
return p.Success
}
func (p *NotesGetNoteResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *NotesGetNoteResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *NotesGetNoteResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &GetNoteResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *NotesGetNoteResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetNote_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *NotesGetNoteResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *NotesGetNoteResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("NotesGetNoteResult(%+v)", *p)
}


