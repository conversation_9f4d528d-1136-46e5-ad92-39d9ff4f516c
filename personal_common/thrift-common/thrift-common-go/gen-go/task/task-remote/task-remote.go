// Code generated by Thrift Compiler (0.16.0). DO NOT EDIT.

package main

import (
	"context"
	"flag"
	"fmt"
	"math"
	"net"
	"net/url"
	"os"
	"strconv"
	"strings"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"thrift-common/gen-go/base"
	"task"
)

var _ = base.GoUnusedProtection__
var _ = task.GoUnusedProtection__

func Usage() {
  fmt.Fprintln(os.<PERSON>, "Usage of ", os.Args[0], " [-h host:port] [-u url] [-f[ramed]] function [arg1 [arg2...]]:")
  flag.PrintDefaults()
  fmt.Fprintln(os.Stderr, "\nFunctions:")
  fmt.Fprintln(os.Stderr, "  AddTaskResponse AddTask(AddTaskRequest req)")
  fmt.Fprintln(os.Stderr, "  EditTaskResponse EditTask(EditTaskRequest req)")
  fmt.Fprintln(os.<PERSON>r, "  ListTaskResponse ListTask(ListTaskRequest req)")
  fmt.Fprintln(os.Stderr, "  GetLastResponse GetLast(GetLastRequest req)")
  fmt.Fprintln(os.Stderr, "  TaskActionResponse TaskAction(TaskActionRequest req)")
  fmt.Fprintln(os.Stderr, "  TaskMvResponse TaskMv(TaskMvRequest req)")
  fmt.Fprintln(os.Stderr, "  GetTaskPageResponse GetTaskPage(GetTaskPageRequest req)")
  fmt.Fprintln(os.Stderr, "  PickTaskResponse PickTask(PickTaskRequest req)")
  fmt.Fprintln(os.Stderr, "  PickDelResponse PickDel(PickDelRequest req)")
  fmt.Fprintln(os.Stderr, "  SearchTaskResponse SearchTask(SearchTaskRequest req)")
  fmt.Fprintln(os.Stderr)
  os.Exit(0)
}

type httpHeaders map[string]string

func (h httpHeaders) String() string {
  var m map[string]string = h
  return fmt.Sprintf("%s", m)
}

func (h httpHeaders) Set(value string) error {
  parts := strings.Split(value, ": ")
  if len(parts) != 2 {
    return fmt.Errorf("header should be of format 'Key: Value'")
  }
  h[parts[0]] = parts[1]
  return nil
}

func main() {
  flag.Usage = Usage
  var host string
  var port int
  var protocol string
  var urlString string
  var framed bool
  var useHttp bool
  headers := make(httpHeaders)
  var parsedUrl *url.URL
  var trans thrift.TTransport
  _ = strconv.Atoi
  _ = math.Abs
  flag.Usage = Usage
  flag.StringVar(&host, "h", "localhost", "Specify host and port")
  flag.IntVar(&port, "p", 9090, "Specify port")
  flag.StringVar(&protocol, "P", "binary", "Specify the protocol (binary, compact, simplejson, json)")
  flag.StringVar(&urlString, "u", "", "Specify the url")
  flag.BoolVar(&framed, "framed", false, "Use framed transport")
  flag.BoolVar(&useHttp, "http", false, "Use http")
  flag.Var(headers, "H", "Headers to set on the http(s) request (e.g. -H \"Key: Value\")")
  flag.Parse()
  
  if len(urlString) > 0 {
    var err error
    parsedUrl, err = url.Parse(urlString)
    if err != nil {
      fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
      flag.Usage()
    }
    host = parsedUrl.Host
    useHttp = len(parsedUrl.Scheme) <= 0 || parsedUrl.Scheme == "http" || parsedUrl.Scheme == "https"
  } else if useHttp {
    _, err := url.Parse(fmt.Sprint("http://", host, ":", port))
    if err != nil {
      fmt.Fprintln(os.Stderr, "Error parsing URL: ", err)
      flag.Usage()
    }
  }
  
  cmd := flag.Arg(0)
  var err error
  var cfg *thrift.TConfiguration = nil
  if useHttp {
    trans, err = thrift.NewTHttpClient(parsedUrl.String())
    if len(headers) > 0 {
      httptrans := trans.(*thrift.THttpClient)
      for key, value := range headers {
        httptrans.SetHeader(key, value)
      }
    }
  } else {
    portStr := fmt.Sprint(port)
    if strings.Contains(host, ":") {
           host, portStr, err = net.SplitHostPort(host)
           if err != nil {
                   fmt.Fprintln(os.Stderr, "error with host:", err)
                   os.Exit(1)
           }
    }
    trans = thrift.NewTSocketConf(net.JoinHostPort(host, portStr), cfg)
    if err != nil {
      fmt.Fprintln(os.Stderr, "error resolving address:", err)
      os.Exit(1)
    }
    if framed {
      trans = thrift.NewTFramedTransportConf(trans, cfg)
    }
  }
  if err != nil {
    fmt.Fprintln(os.Stderr, "Error creating transport", err)
    os.Exit(1)
  }
  defer trans.Close()
  var protocolFactory thrift.TProtocolFactory
  switch protocol {
  case "compact":
    protocolFactory = thrift.NewTCompactProtocolFactoryConf(cfg)
    break
  case "simplejson":
    protocolFactory = thrift.NewTSimpleJSONProtocolFactoryConf(cfg)
    break
  case "json":
    protocolFactory = thrift.NewTJSONProtocolFactory()
    break
  case "binary", "":
    protocolFactory = thrift.NewTBinaryProtocolFactoryConf(cfg)
    break
  default:
    fmt.Fprintln(os.Stderr, "Invalid protocol specified: ", protocol)
    Usage()
    os.Exit(1)
  }
  iprot := protocolFactory.GetProtocol(trans)
  oprot := protocolFactory.GetProtocol(trans)
  client := task.NewTaskClient(thrift.NewTStandardClient(iprot, oprot))
  if err := trans.Open(); err != nil {
    fmt.Fprintln(os.Stderr, "Error opening socket to ", host, ":", port, " ", err)
    os.Exit(1)
  }
  
  switch cmd {
  case "AddTask":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "AddTask requires 1 args")
      flag.Usage()
    }
    arg86 := flag.Arg(1)
    mbTrans87 := thrift.NewTMemoryBufferLen(len(arg86))
    defer mbTrans87.Close()
    _, err88 := mbTrans87.WriteString(arg86)
    if err88 != nil {
      Usage()
      return
    }
    factory89 := thrift.NewTJSONProtocolFactory()
    jsProt90 := factory89.GetProtocol(mbTrans87)
    argvalue0 := task.NewAddTaskRequest()
    err91 := argvalue0.Read(context.Background(), jsProt90)
    if err91 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.AddTask(context.Background(), value0))
    fmt.Print("\n")
    break
  case "EditTask":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "EditTask requires 1 args")
      flag.Usage()
    }
    arg92 := flag.Arg(1)
    mbTrans93 := thrift.NewTMemoryBufferLen(len(arg92))
    defer mbTrans93.Close()
    _, err94 := mbTrans93.WriteString(arg92)
    if err94 != nil {
      Usage()
      return
    }
    factory95 := thrift.NewTJSONProtocolFactory()
    jsProt96 := factory95.GetProtocol(mbTrans93)
    argvalue0 := task.NewEditTaskRequest()
    err97 := argvalue0.Read(context.Background(), jsProt96)
    if err97 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.EditTask(context.Background(), value0))
    fmt.Print("\n")
    break
  case "ListTask":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "ListTask requires 1 args")
      flag.Usage()
    }
    arg98 := flag.Arg(1)
    mbTrans99 := thrift.NewTMemoryBufferLen(len(arg98))
    defer mbTrans99.Close()
    _, err100 := mbTrans99.WriteString(arg98)
    if err100 != nil {
      Usage()
      return
    }
    factory101 := thrift.NewTJSONProtocolFactory()
    jsProt102 := factory101.GetProtocol(mbTrans99)
    argvalue0 := task.NewListTaskRequest()
    err103 := argvalue0.Read(context.Background(), jsProt102)
    if err103 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.ListTask(context.Background(), value0))
    fmt.Print("\n")
    break
  case "GetLast":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetLast requires 1 args")
      flag.Usage()
    }
    arg104 := flag.Arg(1)
    mbTrans105 := thrift.NewTMemoryBufferLen(len(arg104))
    defer mbTrans105.Close()
    _, err106 := mbTrans105.WriteString(arg104)
    if err106 != nil {
      Usage()
      return
    }
    factory107 := thrift.NewTJSONProtocolFactory()
    jsProt108 := factory107.GetProtocol(mbTrans105)
    argvalue0 := task.NewGetLastRequest()
    err109 := argvalue0.Read(context.Background(), jsProt108)
    if err109 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetLast(context.Background(), value0))
    fmt.Print("\n")
    break
  case "TaskAction":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "TaskAction requires 1 args")
      flag.Usage()
    }
    arg110 := flag.Arg(1)
    mbTrans111 := thrift.NewTMemoryBufferLen(len(arg110))
    defer mbTrans111.Close()
    _, err112 := mbTrans111.WriteString(arg110)
    if err112 != nil {
      Usage()
      return
    }
    factory113 := thrift.NewTJSONProtocolFactory()
    jsProt114 := factory113.GetProtocol(mbTrans111)
    argvalue0 := task.NewTaskActionRequest()
    err115 := argvalue0.Read(context.Background(), jsProt114)
    if err115 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.TaskAction(context.Background(), value0))
    fmt.Print("\n")
    break
  case "TaskMv":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "TaskMv requires 1 args")
      flag.Usage()
    }
    arg116 := flag.Arg(1)
    mbTrans117 := thrift.NewTMemoryBufferLen(len(arg116))
    defer mbTrans117.Close()
    _, err118 := mbTrans117.WriteString(arg116)
    if err118 != nil {
      Usage()
      return
    }
    factory119 := thrift.NewTJSONProtocolFactory()
    jsProt120 := factory119.GetProtocol(mbTrans117)
    argvalue0 := task.NewTaskMvRequest()
    err121 := argvalue0.Read(context.Background(), jsProt120)
    if err121 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.TaskMv(context.Background(), value0))
    fmt.Print("\n")
    break
  case "GetTaskPage":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "GetTaskPage requires 1 args")
      flag.Usage()
    }
    arg122 := flag.Arg(1)
    mbTrans123 := thrift.NewTMemoryBufferLen(len(arg122))
    defer mbTrans123.Close()
    _, err124 := mbTrans123.WriteString(arg122)
    if err124 != nil {
      Usage()
      return
    }
    factory125 := thrift.NewTJSONProtocolFactory()
    jsProt126 := factory125.GetProtocol(mbTrans123)
    argvalue0 := task.NewGetTaskPageRequest()
    err127 := argvalue0.Read(context.Background(), jsProt126)
    if err127 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.GetTaskPage(context.Background(), value0))
    fmt.Print("\n")
    break
  case "PickTask":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "PickTask requires 1 args")
      flag.Usage()
    }
    arg128 := flag.Arg(1)
    mbTrans129 := thrift.NewTMemoryBufferLen(len(arg128))
    defer mbTrans129.Close()
    _, err130 := mbTrans129.WriteString(arg128)
    if err130 != nil {
      Usage()
      return
    }
    factory131 := thrift.NewTJSONProtocolFactory()
    jsProt132 := factory131.GetProtocol(mbTrans129)
    argvalue0 := task.NewPickTaskRequest()
    err133 := argvalue0.Read(context.Background(), jsProt132)
    if err133 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.PickTask(context.Background(), value0))
    fmt.Print("\n")
    break
  case "PickDel":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "PickDel requires 1 args")
      flag.Usage()
    }
    arg134 := flag.Arg(1)
    mbTrans135 := thrift.NewTMemoryBufferLen(len(arg134))
    defer mbTrans135.Close()
    _, err136 := mbTrans135.WriteString(arg134)
    if err136 != nil {
      Usage()
      return
    }
    factory137 := thrift.NewTJSONProtocolFactory()
    jsProt138 := factory137.GetProtocol(mbTrans135)
    argvalue0 := task.NewPickDelRequest()
    err139 := argvalue0.Read(context.Background(), jsProt138)
    if err139 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.PickDel(context.Background(), value0))
    fmt.Print("\n")
    break
  case "SearchTask":
    if flag.NArg() - 1 != 1 {
      fmt.Fprintln(os.Stderr, "SearchTask requires 1 args")
      flag.Usage()
    }
    arg140 := flag.Arg(1)
    mbTrans141 := thrift.NewTMemoryBufferLen(len(arg140))
    defer mbTrans141.Close()
    _, err142 := mbTrans141.WriteString(arg140)
    if err142 != nil {
      Usage()
      return
    }
    factory143 := thrift.NewTJSONProtocolFactory()
    jsProt144 := factory143.GetProtocol(mbTrans141)
    argvalue0 := task.NewSearchTaskRequest()
    err145 := argvalue0.Read(context.Background(), jsProt144)
    if err145 != nil {
      Usage()
      return
    }
    value0 := argvalue0
    fmt.Print(client.SearchTask(context.Background(), value0))
    fmt.Print("\n")
    break
  case "":
    Usage()
    break
  default:
    fmt.Fprintln(os.Stderr, "Invalid function ", cmd)
  }
}
