// Code generated by Thrift Compiler (0.16.0). DO NOT EDIT.

package task

import (
	"bytes"
	"context"
	"fmt"
	"time"
	thrift "github.com/apache/thrift/lib/go/thrift"
	"thrift-common/gen-go/base"

)

// (needed to ensure safety because of naive import list construction.)
var _ = thrift.ZERO
var _ = fmt.Printf
var _ = context.Background
var _ = time.Now
var _ = bytes.Equal

var _ = base.GoUnusedProtection__
// Attributes:
//  - Title
//  - Parent
//  - Priority
//  - Mark
//  - ForceID
//  - ForceStatus
//  - Description
//  - DDL
//  - Base
type AddTaskRequest struct {
  Title string `thrift:"Title,1" db:"Title" json:"title"`
  Parent string `thrift:"Parent,2" db:"Parent" json:"parent"`
  Priority *string `thrift:"Priority,3" db:"Priority" json:"priority"`
  Mark *string `thrift:"Mark,4" db:"Mark" json:"mark"`
  ForceID *string `thrift:"ForceID,5" db:"ForceID" json:"forceID"`
  ForceStatus *string `thrift:"ForceStatus,6" db:"ForceStatus" json:"forceStatus"`
  Description *string `thrift:"Description,7" db:"Description" json:"description"`
  DDL *string `thrift:"DDL,8" db:"DDL" json:"ddl"`
  // unused fields # 9 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewAddTaskRequest() *AddTaskRequest {
  return &AddTaskRequest{}
}


func (p *AddTaskRequest) GetTitle() string {
  return p.Title
}

func (p *AddTaskRequest) GetParent() string {
  return p.Parent
}
var AddTaskRequest_Priority_DEFAULT string
func (p *AddTaskRequest) GetPriority() string {
  if !p.IsSetPriority() {
    return AddTaskRequest_Priority_DEFAULT
  }
return *p.Priority
}
var AddTaskRequest_Mark_DEFAULT string
func (p *AddTaskRequest) GetMark() string {
  if !p.IsSetMark() {
    return AddTaskRequest_Mark_DEFAULT
  }
return *p.Mark
}
var AddTaskRequest_ForceID_DEFAULT string
func (p *AddTaskRequest) GetForceID() string {
  if !p.IsSetForceID() {
    return AddTaskRequest_ForceID_DEFAULT
  }
return *p.ForceID
}
var AddTaskRequest_ForceStatus_DEFAULT string
func (p *AddTaskRequest) GetForceStatus() string {
  if !p.IsSetForceStatus() {
    return AddTaskRequest_ForceStatus_DEFAULT
  }
return *p.ForceStatus
}
var AddTaskRequest_Description_DEFAULT string
func (p *AddTaskRequest) GetDescription() string {
  if !p.IsSetDescription() {
    return AddTaskRequest_Description_DEFAULT
  }
return *p.Description
}
var AddTaskRequest_DDL_DEFAULT string
func (p *AddTaskRequest) GetDDL() string {
  if !p.IsSetDDL() {
    return AddTaskRequest_DDL_DEFAULT
  }
return *p.DDL
}
var AddTaskRequest_Base_DEFAULT *base.Base
func (p *AddTaskRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return AddTaskRequest_Base_DEFAULT
  }
return p.Base
}
func (p *AddTaskRequest) IsSetPriority() bool {
  return p.Priority != nil
}

func (p *AddTaskRequest) IsSetMark() bool {
  return p.Mark != nil
}

func (p *AddTaskRequest) IsSetForceID() bool {
  return p.ForceID != nil
}

func (p *AddTaskRequest) IsSetForceStatus() bool {
  return p.ForceStatus != nil
}

func (p *AddTaskRequest) IsSetDescription() bool {
  return p.Description != nil
}

func (p *AddTaskRequest) IsSetDDL() bool {
  return p.DDL != nil
}

func (p *AddTaskRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *AddTaskRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *AddTaskRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Title = v
}
  return nil
}

func (p *AddTaskRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Parent = v
}
  return nil
}

func (p *AddTaskRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Priority = &v
}
  return nil
}

func (p *AddTaskRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Mark = &v
}
  return nil
}

func (p *AddTaskRequest)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.ForceID = &v
}
  return nil
}

func (p *AddTaskRequest)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.ForceStatus = &v
}
  return nil
}

func (p *AddTaskRequest)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.Description = &v
}
  return nil
}

func (p *AddTaskRequest)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.DDL = &v
}
  return nil
}

func (p *AddTaskRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *AddTaskRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AddTaskRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *AddTaskRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Title", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Title: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Title)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Title (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Title: ", p), err) }
  return err
}

func (p *AddTaskRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Parent", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Parent: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Parent)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Parent (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Parent: ", p), err) }
  return err
}

func (p *AddTaskRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetPriority() {
    if err := oprot.WriteFieldBegin(ctx, "Priority", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Priority: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Priority)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Priority (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Priority: ", p), err) }
  }
  return err
}

func (p *AddTaskRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMark() {
    if err := oprot.WriteFieldBegin(ctx, "Mark", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:Mark: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Mark)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Mark (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:Mark: ", p), err) }
  }
  return err
}

func (p *AddTaskRequest) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetForceID() {
    if err := oprot.WriteFieldBegin(ctx, "ForceID", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:ForceID: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ForceID)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ForceID (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:ForceID: ", p), err) }
  }
  return err
}

func (p *AddTaskRequest) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetForceStatus() {
    if err := oprot.WriteFieldBegin(ctx, "ForceStatus", thrift.STRING, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:ForceStatus: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ForceStatus)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ForceStatus (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:ForceStatus: ", p), err) }
  }
  return err
}

func (p *AddTaskRequest) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetDescription() {
    if err := oprot.WriteFieldBegin(ctx, "Description", thrift.STRING, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:Description: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Description)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Description (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:Description: ", p), err) }
  }
  return err
}

func (p *AddTaskRequest) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetDDL() {
    if err := oprot.WriteFieldBegin(ctx, "DDL", thrift.STRING, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:DDL: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.DDL)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.DDL (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:DDL: ", p), err) }
  }
  return err
}

func (p *AddTaskRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *AddTaskRequest) Equals(other *AddTaskRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Title != other.Title { return false }
  if p.Parent != other.Parent { return false }
  if p.Priority != other.Priority {
    if p.Priority == nil || other.Priority == nil {
      return false
    }
    if (*p.Priority) != (*other.Priority) { return false }
  }
  if p.Mark != other.Mark {
    if p.Mark == nil || other.Mark == nil {
      return false
    }
    if (*p.Mark) != (*other.Mark) { return false }
  }
  if p.ForceID != other.ForceID {
    if p.ForceID == nil || other.ForceID == nil {
      return false
    }
    if (*p.ForceID) != (*other.ForceID) { return false }
  }
  if p.ForceStatus != other.ForceStatus {
    if p.ForceStatus == nil || other.ForceStatus == nil {
      return false
    }
    if (*p.ForceStatus) != (*other.ForceStatus) { return false }
  }
  if p.Description != other.Description {
    if p.Description == nil || other.Description == nil {
      return false
    }
    if (*p.Description) != (*other.Description) { return false }
  }
  if p.DDL != other.DDL {
    if p.DDL == nil || other.DDL == nil {
      return false
    }
    if (*p.DDL) != (*other.DDL) { return false }
  }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *AddTaskRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("AddTaskRequest(%+v)", *p)
}

// Attributes:
//  - TreeRoot
//  - Marks
//  - Timelines
//  - BaseResp
type AddTaskResponse struct {
  TreeRoot *TaskItem `thrift:"TreeRoot,1" db:"TreeRoot" json:"treeRoot"`
  Marks []*TaskMarkItem `thrift:"Marks,2" db:"Marks" json:"marks"`
  Timelines [][]string `thrift:"Timelines,3" db:"Timelines" json:"timelines"`
  // unused fields # 4 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewAddTaskResponse() *AddTaskResponse {
  return &AddTaskResponse{}
}

var AddTaskResponse_TreeRoot_DEFAULT *TaskItem
func (p *AddTaskResponse) GetTreeRoot() *TaskItem {
  if !p.IsSetTreeRoot() {
    return AddTaskResponse_TreeRoot_DEFAULT
  }
return p.TreeRoot
}

func (p *AddTaskResponse) GetMarks() []*TaskMarkItem {
  return p.Marks
}

func (p *AddTaskResponse) GetTimelines() [][]string {
  return p.Timelines
}
var AddTaskResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *AddTaskResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return AddTaskResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *AddTaskResponse) IsSetTreeRoot() bool {
  return p.TreeRoot != nil
}

func (p *AddTaskResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *AddTaskResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *AddTaskResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.TreeRoot = &TaskItem{}
  if err := p.TreeRoot.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.TreeRoot), err)
  }
  return nil
}

func (p *AddTaskResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*TaskMarkItem, 0, size)
  p.Marks =  tSlice
  for i := 0; i < size; i ++ {
    _elem0 := &TaskMarkItem{}
    if err := _elem0.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem0), err)
    }
    p.Marks = append(p.Marks, _elem0)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *AddTaskResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([][]string, 0, size)
  p.Timelines =  tSlice
  for i := 0; i < size; i ++ {
    _, size, err := iprot.ReadListBegin(ctx)
    if err != nil {
      return thrift.PrependError("error reading list begin: ", err)
    }
    tSlice := make([]string, 0, size)
    _elem1 :=  tSlice
    for i := 0; i < size; i ++ {
var _elem2 string
      if v, err := iprot.ReadString(ctx); err != nil {
      return thrift.PrependError("error reading field 0: ", err)
} else {
      _elem2 = v
}
      _elem1 = append(_elem1, _elem2)
    }
    if err := iprot.ReadListEnd(ctx); err != nil {
      return thrift.PrependError("error reading list end: ", err)
    }
    p.Timelines = append(p.Timelines, _elem1)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *AddTaskResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *AddTaskResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AddTaskResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *AddTaskResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "TreeRoot", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:TreeRoot: ", p), err) }
  if err := p.TreeRoot.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.TreeRoot), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:TreeRoot: ", p), err) }
  return err
}

func (p *AddTaskResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Marks", thrift.LIST, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Marks: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Marks)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.Marks {
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Marks: ", p), err) }
  return err
}

func (p *AddTaskResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Timelines", thrift.LIST, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Timelines: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.LIST, len(p.Timelines)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.Timelines {
    if err := oprot.WriteListBegin(ctx, thrift.STRING, len(v)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range v {
      if err := oprot.WriteString(ctx, string(v)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Timelines: ", p), err) }
  return err
}

func (p *AddTaskResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *AddTaskResponse) Equals(other *AddTaskResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.TreeRoot.Equals(other.TreeRoot) { return false }
  if len(p.Marks) != len(other.Marks) { return false }
  for i, _tgt := range p.Marks {
    _src3 := other.Marks[i]
    if !_tgt.Equals(_src3) { return false }
  }
  if len(p.Timelines) != len(other.Timelines) { return false }
  for i, _tgt := range p.Timelines {
    _src4 := other.Timelines[i]
    if len(_tgt) != len(_src4) { return false }
    for i, _tgt := range _tgt {
      _src5 := _src4[i]
      if _tgt != _src5 { return false }
    }
  }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *AddTaskResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("AddTaskResponse(%+v)", *p)
}

// Attributes:
//  - IDLabel
//  - Title
//  - Description
//  - DDL
//  - Base
type EditTaskRequest struct {
  IDLabel string `thrift:"IDLabel,1" db:"IDLabel" json:"taskIDLabel"`
  Title *string `thrift:"Title,2" db:"Title" json:"title"`
  Description *string `thrift:"Description,3" db:"Description" json:"description"`
  DDL *string `thrift:"DDL,4" db:"DDL" json:"ddl"`
  // unused fields # 5 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewEditTaskRequest() *EditTaskRequest {
  return &EditTaskRequest{}
}


func (p *EditTaskRequest) GetIDLabel() string {
  return p.IDLabel
}
var EditTaskRequest_Title_DEFAULT string
func (p *EditTaskRequest) GetTitle() string {
  if !p.IsSetTitle() {
    return EditTaskRequest_Title_DEFAULT
  }
return *p.Title
}
var EditTaskRequest_Description_DEFAULT string
func (p *EditTaskRequest) GetDescription() string {
  if !p.IsSetDescription() {
    return EditTaskRequest_Description_DEFAULT
  }
return *p.Description
}
var EditTaskRequest_DDL_DEFAULT string
func (p *EditTaskRequest) GetDDL() string {
  if !p.IsSetDDL() {
    return EditTaskRequest_DDL_DEFAULT
  }
return *p.DDL
}
var EditTaskRequest_Base_DEFAULT *base.Base
func (p *EditTaskRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return EditTaskRequest_Base_DEFAULT
  }
return p.Base
}
func (p *EditTaskRequest) IsSetTitle() bool {
  return p.Title != nil
}

func (p *EditTaskRequest) IsSetDescription() bool {
  return p.Description != nil
}

func (p *EditTaskRequest) IsSetDDL() bool {
  return p.DDL != nil
}

func (p *EditTaskRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *EditTaskRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *EditTaskRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.IDLabel = v
}
  return nil
}

func (p *EditTaskRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Title = &v
}
  return nil
}

func (p *EditTaskRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Description = &v
}
  return nil
}

func (p *EditTaskRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.DDL = &v
}
  return nil
}

func (p *EditTaskRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *EditTaskRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "EditTaskRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *EditTaskRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "IDLabel", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:IDLabel: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.IDLabel)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.IDLabel (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:IDLabel: ", p), err) }
  return err
}

func (p *EditTaskRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetTitle() {
    if err := oprot.WriteFieldBegin(ctx, "Title", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Title: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Title)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Title (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Title: ", p), err) }
  }
  return err
}

func (p *EditTaskRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetDescription() {
    if err := oprot.WriteFieldBegin(ctx, "Description", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Description: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Description)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Description (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Description: ", p), err) }
  }
  return err
}

func (p *EditTaskRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetDDL() {
    if err := oprot.WriteFieldBegin(ctx, "DDL", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:DDL: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.DDL)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.DDL (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:DDL: ", p), err) }
  }
  return err
}

func (p *EditTaskRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *EditTaskRequest) Equals(other *EditTaskRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.IDLabel != other.IDLabel { return false }
  if p.Title != other.Title {
    if p.Title == nil || other.Title == nil {
      return false
    }
    if (*p.Title) != (*other.Title) { return false }
  }
  if p.Description != other.Description {
    if p.Description == nil || other.Description == nil {
      return false
    }
    if (*p.Description) != (*other.Description) { return false }
  }
  if p.DDL != other.DDL {
    if p.DDL == nil || other.DDL == nil {
      return false
    }
    if (*p.DDL) != (*other.DDL) { return false }
  }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *EditTaskRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("EditTaskRequest(%+v)", *p)
}

// Attributes:
//  - TreeRoot
//  - Marks
//  - Timelines
//  - BaseResp
type EditTaskResponse struct {
  TreeRoot *TaskItem `thrift:"TreeRoot,1" db:"TreeRoot" json:"treeRoot"`
  Marks []*TaskMarkItem `thrift:"Marks,2" db:"Marks" json:"marks"`
  Timelines [][]string `thrift:"Timelines,3" db:"Timelines" json:"timelines"`
  // unused fields # 4 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewEditTaskResponse() *EditTaskResponse {
  return &EditTaskResponse{}
}

var EditTaskResponse_TreeRoot_DEFAULT *TaskItem
func (p *EditTaskResponse) GetTreeRoot() *TaskItem {
  if !p.IsSetTreeRoot() {
    return EditTaskResponse_TreeRoot_DEFAULT
  }
return p.TreeRoot
}

func (p *EditTaskResponse) GetMarks() []*TaskMarkItem {
  return p.Marks
}

func (p *EditTaskResponse) GetTimelines() [][]string {
  return p.Timelines
}
var EditTaskResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *EditTaskResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return EditTaskResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *EditTaskResponse) IsSetTreeRoot() bool {
  return p.TreeRoot != nil
}

func (p *EditTaskResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *EditTaskResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *EditTaskResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.TreeRoot = &TaskItem{}
  if err := p.TreeRoot.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.TreeRoot), err)
  }
  return nil
}

func (p *EditTaskResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*TaskMarkItem, 0, size)
  p.Marks =  tSlice
  for i := 0; i < size; i ++ {
    _elem6 := &TaskMarkItem{}
    if err := _elem6.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem6), err)
    }
    p.Marks = append(p.Marks, _elem6)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *EditTaskResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([][]string, 0, size)
  p.Timelines =  tSlice
  for i := 0; i < size; i ++ {
    _, size, err := iprot.ReadListBegin(ctx)
    if err != nil {
      return thrift.PrependError("error reading list begin: ", err)
    }
    tSlice := make([]string, 0, size)
    _elem7 :=  tSlice
    for i := 0; i < size; i ++ {
var _elem8 string
      if v, err := iprot.ReadString(ctx); err != nil {
      return thrift.PrependError("error reading field 0: ", err)
} else {
      _elem8 = v
}
      _elem7 = append(_elem7, _elem8)
    }
    if err := iprot.ReadListEnd(ctx); err != nil {
      return thrift.PrependError("error reading list end: ", err)
    }
    p.Timelines = append(p.Timelines, _elem7)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *EditTaskResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *EditTaskResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "EditTaskResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *EditTaskResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "TreeRoot", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:TreeRoot: ", p), err) }
  if err := p.TreeRoot.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.TreeRoot), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:TreeRoot: ", p), err) }
  return err
}

func (p *EditTaskResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Marks", thrift.LIST, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Marks: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Marks)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.Marks {
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Marks: ", p), err) }
  return err
}

func (p *EditTaskResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Timelines", thrift.LIST, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Timelines: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.LIST, len(p.Timelines)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.Timelines {
    if err := oprot.WriteListBegin(ctx, thrift.STRING, len(v)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range v {
      if err := oprot.WriteString(ctx, string(v)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Timelines: ", p), err) }
  return err
}

func (p *EditTaskResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *EditTaskResponse) Equals(other *EditTaskResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.TreeRoot.Equals(other.TreeRoot) { return false }
  if len(p.Marks) != len(other.Marks) { return false }
  for i, _tgt := range p.Marks {
    _src9 := other.Marks[i]
    if !_tgt.Equals(_src9) { return false }
  }
  if len(p.Timelines) != len(other.Timelines) { return false }
  for i, _tgt := range p.Timelines {
    _src10 := other.Timelines[i]
    if len(_tgt) != len(_src10) { return false }
    for i, _tgt := range _tgt {
      _src11 := _src10[i]
      if _tgt != _src11 { return false }
    }
  }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *EditTaskResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("EditTaskResponse(%+v)", *p)
}

// Attributes:
//  - ID
//  - Content
//  - MarkType
//  - DocLink
//  - Time
type TaskMarkItem struct {
  ID string `thrift:"ID,1" db:"ID" json:"id"`
  Content string `thrift:"Content,2" db:"Content" json:"content"`
  MarkType string `thrift:"MarkType,3" db:"MarkType" json:"markType"`
  DocLink *string `thrift:"DocLink,4" db:"DocLink" json:"docLink"`
  Time string `thrift:"Time,5" db:"Time" json:"time"`
}

func NewTaskMarkItem() *TaskMarkItem {
  return &TaskMarkItem{}
}


func (p *TaskMarkItem) GetID() string {
  return p.ID
}

func (p *TaskMarkItem) GetContent() string {
  return p.Content
}

func (p *TaskMarkItem) GetMarkType() string {
  return p.MarkType
}
var TaskMarkItem_DocLink_DEFAULT string
func (p *TaskMarkItem) GetDocLink() string {
  if !p.IsSetDocLink() {
    return TaskMarkItem_DocLink_DEFAULT
  }
return *p.DocLink
}

func (p *TaskMarkItem) GetTime() string {
  return p.Time
}
func (p *TaskMarkItem) IsSetDocLink() bool {
  return p.DocLink != nil
}

func (p *TaskMarkItem) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskMarkItem)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ID = v
}
  return nil
}

func (p *TaskMarkItem)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Content = v
}
  return nil
}

func (p *TaskMarkItem)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.MarkType = v
}
  return nil
}

func (p *TaskMarkItem)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.DocLink = &v
}
  return nil
}

func (p *TaskMarkItem)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Time = v
}
  return nil
}

func (p *TaskMarkItem) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "TaskMarkItem"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskMarkItem) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ID", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ID: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.ID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ID (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ID: ", p), err) }
  return err
}

func (p *TaskMarkItem) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Content", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Content: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Content)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Content (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Content: ", p), err) }
  return err
}

func (p *TaskMarkItem) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "MarkType", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:MarkType: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.MarkType)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.MarkType (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:MarkType: ", p), err) }
  return err
}

func (p *TaskMarkItem) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetDocLink() {
    if err := oprot.WriteFieldBegin(ctx, "DocLink", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:DocLink: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.DocLink)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.DocLink (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:DocLink: ", p), err) }
  }
  return err
}

func (p *TaskMarkItem) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Time", thrift.STRING, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:Time: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Time)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Time (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:Time: ", p), err) }
  return err
}

func (p *TaskMarkItem) Equals(other *TaskMarkItem) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ID != other.ID { return false }
  if p.Content != other.Content { return false }
  if p.MarkType != other.MarkType { return false }
  if p.DocLink != other.DocLink {
    if p.DocLink == nil || other.DocLink == nil {
      return false
    }
    if (*p.DocLink) != (*other.DocLink) { return false }
  }
  if p.Time != other.Time { return false }
  return true
}

func (p *TaskMarkItem) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskMarkItem(%+v)", *p)
}

// Attributes:
//  - IDLabel
//  - Action
//  - MustLeaf
//  - StartTime
//  - EndTime
//  - Content
//  - MarkTime
//  - Force
//  - LabelName
//  - Base
type TaskActionRequest struct {
  IDLabel *string `thrift:"IDLabel,1" db:"IDLabel" json:"taskIDLabel"`
  Action string `thrift:"Action,2" db:"Action" json:"action"`
  MustLeaf string `thrift:"MustLeaf,3" db:"MustLeaf" json:"mustLeaf"`
  StartTime *string `thrift:"StartTime,4" db:"StartTime" json:"startTime"`
  EndTime *string `thrift:"EndTime,5" db:"EndTime" json:"endTime"`
  Content *string `thrift:"Content,6" db:"Content" json:"content"`
  MarkTime *string `thrift:"MarkTime,7" db:"MarkTime" json:"markTime"`
  Force *string `thrift:"Force,8" db:"Force" json:"force"`
  LabelName *string `thrift:"LabelName,9" db:"LabelName" json:"labelName"`
  // unused fields # 10 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewTaskActionRequest() *TaskActionRequest {
  return &TaskActionRequest{}
}

var TaskActionRequest_IDLabel_DEFAULT string
func (p *TaskActionRequest) GetIDLabel() string {
  if !p.IsSetIDLabel() {
    return TaskActionRequest_IDLabel_DEFAULT
  }
return *p.IDLabel
}

func (p *TaskActionRequest) GetAction() string {
  return p.Action
}

func (p *TaskActionRequest) GetMustLeaf() string {
  return p.MustLeaf
}
var TaskActionRequest_StartTime_DEFAULT string
func (p *TaskActionRequest) GetStartTime() string {
  if !p.IsSetStartTime() {
    return TaskActionRequest_StartTime_DEFAULT
  }
return *p.StartTime
}
var TaskActionRequest_EndTime_DEFAULT string
func (p *TaskActionRequest) GetEndTime() string {
  if !p.IsSetEndTime() {
    return TaskActionRequest_EndTime_DEFAULT
  }
return *p.EndTime
}
var TaskActionRequest_Content_DEFAULT string
func (p *TaskActionRequest) GetContent() string {
  if !p.IsSetContent() {
    return TaskActionRequest_Content_DEFAULT
  }
return *p.Content
}
var TaskActionRequest_MarkTime_DEFAULT string
func (p *TaskActionRequest) GetMarkTime() string {
  if !p.IsSetMarkTime() {
    return TaskActionRequest_MarkTime_DEFAULT
  }
return *p.MarkTime
}
var TaskActionRequest_Force_DEFAULT string
func (p *TaskActionRequest) GetForce() string {
  if !p.IsSetForce() {
    return TaskActionRequest_Force_DEFAULT
  }
return *p.Force
}
var TaskActionRequest_LabelName_DEFAULT string
func (p *TaskActionRequest) GetLabelName() string {
  if !p.IsSetLabelName() {
    return TaskActionRequest_LabelName_DEFAULT
  }
return *p.LabelName
}
var TaskActionRequest_Base_DEFAULT *base.Base
func (p *TaskActionRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return TaskActionRequest_Base_DEFAULT
  }
return p.Base
}
func (p *TaskActionRequest) IsSetIDLabel() bool {
  return p.IDLabel != nil
}

func (p *TaskActionRequest) IsSetStartTime() bool {
  return p.StartTime != nil
}

func (p *TaskActionRequest) IsSetEndTime() bool {
  return p.EndTime != nil
}

func (p *TaskActionRequest) IsSetContent() bool {
  return p.Content != nil
}

func (p *TaskActionRequest) IsSetMarkTime() bool {
  return p.MarkTime != nil
}

func (p *TaskActionRequest) IsSetForce() bool {
  return p.Force != nil
}

func (p *TaskActionRequest) IsSetLabelName() bool {
  return p.LabelName != nil
}

func (p *TaskActionRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *TaskActionRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskActionRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.IDLabel = &v
}
  return nil
}

func (p *TaskActionRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Action = v
}
  return nil
}

func (p *TaskActionRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.MustLeaf = v
}
  return nil
}

func (p *TaskActionRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.StartTime = &v
}
  return nil
}

func (p *TaskActionRequest)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.EndTime = &v
}
  return nil
}

func (p *TaskActionRequest)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Content = &v
}
  return nil
}

func (p *TaskActionRequest)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.MarkTime = &v
}
  return nil
}

func (p *TaskActionRequest)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.Force = &v
}
  return nil
}

func (p *TaskActionRequest)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.LabelName = &v
}
  return nil
}

func (p *TaskActionRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *TaskActionRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "TaskActionRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskActionRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetIDLabel() {
    if err := oprot.WriteFieldBegin(ctx, "IDLabel", thrift.STRING, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:IDLabel: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.IDLabel)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.IDLabel (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:IDLabel: ", p), err) }
  }
  return err
}

func (p *TaskActionRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Action", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Action: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Action)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Action (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Action: ", p), err) }
  return err
}

func (p *TaskActionRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "MustLeaf", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:MustLeaf: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.MustLeaf)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.MustLeaf (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:MustLeaf: ", p), err) }
  return err
}

func (p *TaskActionRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetStartTime() {
    if err := oprot.WriteFieldBegin(ctx, "StartTime", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:StartTime: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.StartTime)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.StartTime (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:StartTime: ", p), err) }
  }
  return err
}

func (p *TaskActionRequest) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetEndTime() {
    if err := oprot.WriteFieldBegin(ctx, "EndTime", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:EndTime: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.EndTime)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.EndTime (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:EndTime: ", p), err) }
  }
  return err
}

func (p *TaskActionRequest) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetContent() {
    if err := oprot.WriteFieldBegin(ctx, "Content", thrift.STRING, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:Content: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Content)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Content (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:Content: ", p), err) }
  }
  return err
}

func (p *TaskActionRequest) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMarkTime() {
    if err := oprot.WriteFieldBegin(ctx, "MarkTime", thrift.STRING, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:MarkTime: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.MarkTime)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.MarkTime (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:MarkTime: ", p), err) }
  }
  return err
}

func (p *TaskActionRequest) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetForce() {
    if err := oprot.WriteFieldBegin(ctx, "Force", thrift.STRING, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:Force: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Force)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Force (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:Force: ", p), err) }
  }
  return err
}

func (p *TaskActionRequest) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetLabelName() {
    if err := oprot.WriteFieldBegin(ctx, "LabelName", thrift.STRING, 9); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:LabelName: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.LabelName)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.LabelName (9) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 9:LabelName: ", p), err) }
  }
  return err
}

func (p *TaskActionRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *TaskActionRequest) Equals(other *TaskActionRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.IDLabel != other.IDLabel {
    if p.IDLabel == nil || other.IDLabel == nil {
      return false
    }
    if (*p.IDLabel) != (*other.IDLabel) { return false }
  }
  if p.Action != other.Action { return false }
  if p.MustLeaf != other.MustLeaf { return false }
  if p.StartTime != other.StartTime {
    if p.StartTime == nil || other.StartTime == nil {
      return false
    }
    if (*p.StartTime) != (*other.StartTime) { return false }
  }
  if p.EndTime != other.EndTime {
    if p.EndTime == nil || other.EndTime == nil {
      return false
    }
    if (*p.EndTime) != (*other.EndTime) { return false }
  }
  if p.Content != other.Content {
    if p.Content == nil || other.Content == nil {
      return false
    }
    if (*p.Content) != (*other.Content) { return false }
  }
  if p.MarkTime != other.MarkTime {
    if p.MarkTime == nil || other.MarkTime == nil {
      return false
    }
    if (*p.MarkTime) != (*other.MarkTime) { return false }
  }
  if p.Force != other.Force {
    if p.Force == nil || other.Force == nil {
      return false
    }
    if (*p.Force) != (*other.Force) { return false }
  }
  if p.LabelName != other.LabelName {
    if p.LabelName == nil || other.LabelName == nil {
      return false
    }
    if (*p.LabelName) != (*other.LabelName) { return false }
  }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *TaskActionRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskActionRequest(%+v)", *p)
}

// Attributes:
//  - TreeRoot
//  - Marks
//  - Timelines
//  - BaseResp
type TaskActionResponse struct {
  TreeRoot *TaskItem `thrift:"TreeRoot,1" db:"TreeRoot" json:"treeRoot"`
  Marks []*TaskMarkItem `thrift:"Marks,2" db:"Marks" json:"marks"`
  Timelines [][]string `thrift:"Timelines,3" db:"Timelines" json:"timelines"`
  // unused fields # 4 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewTaskActionResponse() *TaskActionResponse {
  return &TaskActionResponse{}
}

var TaskActionResponse_TreeRoot_DEFAULT *TaskItem
func (p *TaskActionResponse) GetTreeRoot() *TaskItem {
  if !p.IsSetTreeRoot() {
    return TaskActionResponse_TreeRoot_DEFAULT
  }
return p.TreeRoot
}

func (p *TaskActionResponse) GetMarks() []*TaskMarkItem {
  return p.Marks
}

func (p *TaskActionResponse) GetTimelines() [][]string {
  return p.Timelines
}
var TaskActionResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *TaskActionResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return TaskActionResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *TaskActionResponse) IsSetTreeRoot() bool {
  return p.TreeRoot != nil
}

func (p *TaskActionResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *TaskActionResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskActionResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.TreeRoot = &TaskItem{}
  if err := p.TreeRoot.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.TreeRoot), err)
  }
  return nil
}

func (p *TaskActionResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*TaskMarkItem, 0, size)
  p.Marks =  tSlice
  for i := 0; i < size; i ++ {
    _elem12 := &TaskMarkItem{}
    if err := _elem12.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem12), err)
    }
    p.Marks = append(p.Marks, _elem12)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *TaskActionResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([][]string, 0, size)
  p.Timelines =  tSlice
  for i := 0; i < size; i ++ {
    _, size, err := iprot.ReadListBegin(ctx)
    if err != nil {
      return thrift.PrependError("error reading list begin: ", err)
    }
    tSlice := make([]string, 0, size)
    _elem13 :=  tSlice
    for i := 0; i < size; i ++ {
var _elem14 string
      if v, err := iprot.ReadString(ctx); err != nil {
      return thrift.PrependError("error reading field 0: ", err)
} else {
      _elem14 = v
}
      _elem13 = append(_elem13, _elem14)
    }
    if err := iprot.ReadListEnd(ctx); err != nil {
      return thrift.PrependError("error reading list end: ", err)
    }
    p.Timelines = append(p.Timelines, _elem13)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *TaskActionResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *TaskActionResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "TaskActionResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskActionResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "TreeRoot", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:TreeRoot: ", p), err) }
  if err := p.TreeRoot.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.TreeRoot), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:TreeRoot: ", p), err) }
  return err
}

func (p *TaskActionResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Marks", thrift.LIST, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Marks: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Marks)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.Marks {
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Marks: ", p), err) }
  return err
}

func (p *TaskActionResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Timelines", thrift.LIST, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Timelines: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.LIST, len(p.Timelines)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.Timelines {
    if err := oprot.WriteListBegin(ctx, thrift.STRING, len(v)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range v {
      if err := oprot.WriteString(ctx, string(v)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Timelines: ", p), err) }
  return err
}

func (p *TaskActionResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *TaskActionResponse) Equals(other *TaskActionResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.TreeRoot.Equals(other.TreeRoot) { return false }
  if len(p.Marks) != len(other.Marks) { return false }
  for i, _tgt := range p.Marks {
    _src15 := other.Marks[i]
    if !_tgt.Equals(_src15) { return false }
  }
  if len(p.Timelines) != len(other.Timelines) { return false }
  for i, _tgt := range p.Timelines {
    _src16 := other.Timelines[i]
    if len(_tgt) != len(_src16) { return false }
    for i, _tgt := range _tgt {
      _src17 := _src16[i]
      if _tgt != _src17 { return false }
    }
  }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *TaskActionResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskActionResponse(%+v)", *p)
}

// Attributes:
//  - SrcTask
//  - Parent
//  - DstTaskID
//  - Base
type TaskMvRequest struct {
  SrcTask string `thrift:"SrcTask,1" db:"SrcTask" json:"srcTask"`
  Parent *string `thrift:"Parent,2" db:"Parent" json:"parent"`
  DstTaskID *string `thrift:"DstTaskID,3" db:"DstTaskID" json:"dstTaskID"`
  // unused fields # 4 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewTaskMvRequest() *TaskMvRequest {
  return &TaskMvRequest{}
}


func (p *TaskMvRequest) GetSrcTask() string {
  return p.SrcTask
}
var TaskMvRequest_Parent_DEFAULT string
func (p *TaskMvRequest) GetParent() string {
  if !p.IsSetParent() {
    return TaskMvRequest_Parent_DEFAULT
  }
return *p.Parent
}
var TaskMvRequest_DstTaskID_DEFAULT string
func (p *TaskMvRequest) GetDstTaskID() string {
  if !p.IsSetDstTaskID() {
    return TaskMvRequest_DstTaskID_DEFAULT
  }
return *p.DstTaskID
}
var TaskMvRequest_Base_DEFAULT *base.Base
func (p *TaskMvRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return TaskMvRequest_Base_DEFAULT
  }
return p.Base
}
func (p *TaskMvRequest) IsSetParent() bool {
  return p.Parent != nil
}

func (p *TaskMvRequest) IsSetDstTaskID() bool {
  return p.DstTaskID != nil
}

func (p *TaskMvRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *TaskMvRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskMvRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.SrcTask = v
}
  return nil
}

func (p *TaskMvRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Parent = &v
}
  return nil
}

func (p *TaskMvRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.DstTaskID = &v
}
  return nil
}

func (p *TaskMvRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *TaskMvRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "TaskMvRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskMvRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "SrcTask", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:SrcTask: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.SrcTask)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.SrcTask (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:SrcTask: ", p), err) }
  return err
}

func (p *TaskMvRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetParent() {
    if err := oprot.WriteFieldBegin(ctx, "Parent", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Parent: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Parent)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Parent (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Parent: ", p), err) }
  }
  return err
}

func (p *TaskMvRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetDstTaskID() {
    if err := oprot.WriteFieldBegin(ctx, "DstTaskID", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:DstTaskID: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.DstTaskID)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.DstTaskID (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:DstTaskID: ", p), err) }
  }
  return err
}

func (p *TaskMvRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *TaskMvRequest) Equals(other *TaskMvRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.SrcTask != other.SrcTask { return false }
  if p.Parent != other.Parent {
    if p.Parent == nil || other.Parent == nil {
      return false
    }
    if (*p.Parent) != (*other.Parent) { return false }
  }
  if p.DstTaskID != other.DstTaskID {
    if p.DstTaskID == nil || other.DstTaskID == nil {
      return false
    }
    if (*p.DstTaskID) != (*other.DstTaskID) { return false }
  }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *TaskMvRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskMvRequest(%+v)", *p)
}

// Attributes:
//  - TreeRoot
//  - Marks
//  - Timelines
//  - BaseResp
type TaskMvResponse struct {
  TreeRoot *TaskItem `thrift:"TreeRoot,1" db:"TreeRoot" json:"treeRoot"`
  Marks []*TaskMarkItem `thrift:"Marks,2" db:"Marks" json:"marks"`
  Timelines [][]string `thrift:"Timelines,3" db:"Timelines" json:"timelines"`
  // unused fields # 4 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewTaskMvResponse() *TaskMvResponse {
  return &TaskMvResponse{}
}

var TaskMvResponse_TreeRoot_DEFAULT *TaskItem
func (p *TaskMvResponse) GetTreeRoot() *TaskItem {
  if !p.IsSetTreeRoot() {
    return TaskMvResponse_TreeRoot_DEFAULT
  }
return p.TreeRoot
}

func (p *TaskMvResponse) GetMarks() []*TaskMarkItem {
  return p.Marks
}

func (p *TaskMvResponse) GetTimelines() [][]string {
  return p.Timelines
}
var TaskMvResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *TaskMvResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return TaskMvResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *TaskMvResponse) IsSetTreeRoot() bool {
  return p.TreeRoot != nil
}

func (p *TaskMvResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *TaskMvResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskMvResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.TreeRoot = &TaskItem{}
  if err := p.TreeRoot.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.TreeRoot), err)
  }
  return nil
}

func (p *TaskMvResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*TaskMarkItem, 0, size)
  p.Marks =  tSlice
  for i := 0; i < size; i ++ {
    _elem18 := &TaskMarkItem{}
    if err := _elem18.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem18), err)
    }
    p.Marks = append(p.Marks, _elem18)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *TaskMvResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([][]string, 0, size)
  p.Timelines =  tSlice
  for i := 0; i < size; i ++ {
    _, size, err := iprot.ReadListBegin(ctx)
    if err != nil {
      return thrift.PrependError("error reading list begin: ", err)
    }
    tSlice := make([]string, 0, size)
    _elem19 :=  tSlice
    for i := 0; i < size; i ++ {
var _elem20 string
      if v, err := iprot.ReadString(ctx); err != nil {
      return thrift.PrependError("error reading field 0: ", err)
} else {
      _elem20 = v
}
      _elem19 = append(_elem19, _elem20)
    }
    if err := iprot.ReadListEnd(ctx); err != nil {
      return thrift.PrependError("error reading list end: ", err)
    }
    p.Timelines = append(p.Timelines, _elem19)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *TaskMvResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *TaskMvResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "TaskMvResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskMvResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "TreeRoot", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:TreeRoot: ", p), err) }
  if err := p.TreeRoot.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.TreeRoot), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:TreeRoot: ", p), err) }
  return err
}

func (p *TaskMvResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Marks", thrift.LIST, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Marks: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Marks)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.Marks {
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Marks: ", p), err) }
  return err
}

func (p *TaskMvResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Timelines", thrift.LIST, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Timelines: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.LIST, len(p.Timelines)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.Timelines {
    if err := oprot.WriteListBegin(ctx, thrift.STRING, len(v)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range v {
      if err := oprot.WriteString(ctx, string(v)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Timelines: ", p), err) }
  return err
}

func (p *TaskMvResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *TaskMvResponse) Equals(other *TaskMvResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.TreeRoot.Equals(other.TreeRoot) { return false }
  if len(p.Marks) != len(other.Marks) { return false }
  for i, _tgt := range p.Marks {
    _src21 := other.Marks[i]
    if !_tgt.Equals(_src21) { return false }
  }
  if len(p.Timelines) != len(other.Timelines) { return false }
  for i, _tgt := range p.Timelines {
    _src22 := other.Timelines[i]
    if len(_tgt) != len(_src22) { return false }
    for i, _tgt := range _tgt {
      _src23 := _src22[i]
      if _tgt != _src23 { return false }
    }
  }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *TaskMvResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskMvResponse(%+v)", *p)
}

// Attributes:
//  - Number
//  - Base
type GetLastRequest struct {
  Number *string `thrift:"Number,1" db:"Number" form:"number" json:"number"`
  // unused fields # 2 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewGetLastRequest() *GetLastRequest {
  return &GetLastRequest{}
}

var GetLastRequest_Number_DEFAULT string
func (p *GetLastRequest) GetNumber() string {
  if !p.IsSetNumber() {
    return GetLastRequest_Number_DEFAULT
  }
return *p.Number
}
var GetLastRequest_Base_DEFAULT *base.Base
func (p *GetLastRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return GetLastRequest_Base_DEFAULT
  }
return p.Base
}
func (p *GetLastRequest) IsSetNumber() bool {
  return p.Number != nil
}

func (p *GetLastRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *GetLastRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetLastRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Number = &v
}
  return nil
}

func (p *GetLastRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *GetLastRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetLastRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetLastRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetNumber() {
    if err := oprot.WriteFieldBegin(ctx, "Number", thrift.STRING, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Number: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Number)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Number (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Number: ", p), err) }
  }
  return err
}

func (p *GetLastRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *GetLastRequest) Equals(other *GetLastRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Number != other.Number {
    if p.Number == nil || other.Number == nil {
      return false
    }
    if (*p.Number) != (*other.Number) { return false }
  }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *GetLastRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetLastRequest(%+v)", *p)
}

// Attributes:
//  - Tasks
//  - BaseResp
type GetLastResponse struct {
  Tasks []*TaskItem `thrift:"Tasks,1" db:"Tasks" json:"tasks"`
  // unused fields # 2 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewGetLastResponse() *GetLastResponse {
  return &GetLastResponse{}
}


func (p *GetLastResponse) GetTasks() []*TaskItem {
  return p.Tasks
}
var GetLastResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *GetLastResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return GetLastResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *GetLastResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *GetLastResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetLastResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*TaskItem, 0, size)
  p.Tasks =  tSlice
  for i := 0; i < size; i ++ {
    _elem24 := &TaskItem{}
    if err := _elem24.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem24), err)
    }
    p.Tasks = append(p.Tasks, _elem24)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *GetLastResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *GetLastResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetLastResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetLastResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Tasks", thrift.LIST, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Tasks: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Tasks)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.Tasks {
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Tasks: ", p), err) }
  return err
}

func (p *GetLastResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *GetLastResponse) Equals(other *GetLastResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if len(p.Tasks) != len(other.Tasks) { return false }
  for i, _tgt := range p.Tasks {
    _src25 := other.Tasks[i]
    if !_tgt.Equals(_src25) { return false }
  }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *GetLastResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetLastResponse(%+v)", *p)
}

// Attributes:
//  - IDLabel
//  - Position
//  - Base
type PickTaskRequest struct {
  IDLabel *string `thrift:"IDLabel,1" db:"IDLabel" json:"taskIDLabel"`
  Position *string `thrift:"Position,2" db:"Position" json:"position"`
  // unused fields # 3 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewPickTaskRequest() *PickTaskRequest {
  return &PickTaskRequest{}
}

var PickTaskRequest_IDLabel_DEFAULT string
func (p *PickTaskRequest) GetIDLabel() string {
  if !p.IsSetIDLabel() {
    return PickTaskRequest_IDLabel_DEFAULT
  }
return *p.IDLabel
}
var PickTaskRequest_Position_DEFAULT string
func (p *PickTaskRequest) GetPosition() string {
  if !p.IsSetPosition() {
    return PickTaskRequest_Position_DEFAULT
  }
return *p.Position
}
var PickTaskRequest_Base_DEFAULT *base.Base
func (p *PickTaskRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return PickTaskRequest_Base_DEFAULT
  }
return p.Base
}
func (p *PickTaskRequest) IsSetIDLabel() bool {
  return p.IDLabel != nil
}

func (p *PickTaskRequest) IsSetPosition() bool {
  return p.Position != nil
}

func (p *PickTaskRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *PickTaskRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *PickTaskRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.IDLabel = &v
}
  return nil
}

func (p *PickTaskRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Position = &v
}
  return nil
}

func (p *PickTaskRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *PickTaskRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "PickTaskRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *PickTaskRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetIDLabel() {
    if err := oprot.WriteFieldBegin(ctx, "IDLabel", thrift.STRING, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:IDLabel: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.IDLabel)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.IDLabel (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:IDLabel: ", p), err) }
  }
  return err
}

func (p *PickTaskRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetPosition() {
    if err := oprot.WriteFieldBegin(ctx, "Position", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Position: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Position)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Position (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Position: ", p), err) }
  }
  return err
}

func (p *PickTaskRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *PickTaskRequest) Equals(other *PickTaskRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.IDLabel != other.IDLabel {
    if p.IDLabel == nil || other.IDLabel == nil {
      return false
    }
    if (*p.IDLabel) != (*other.IDLabel) { return false }
  }
  if p.Position != other.Position {
    if p.Position == nil || other.Position == nil {
      return false
    }
    if (*p.Position) != (*other.Position) { return false }
  }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *PickTaskRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("PickTaskRequest(%+v)", *p)
}

// Attributes:
//  - Tasks
//  - BaseResp
type PickTaskResponse struct {
  Tasks []*TaskItem `thrift:"Tasks,1" db:"Tasks" json:"tasks"`
  // unused fields # 2 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewPickTaskResponse() *PickTaskResponse {
  return &PickTaskResponse{}
}


func (p *PickTaskResponse) GetTasks() []*TaskItem {
  return p.Tasks
}
var PickTaskResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *PickTaskResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return PickTaskResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *PickTaskResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *PickTaskResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *PickTaskResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*TaskItem, 0, size)
  p.Tasks =  tSlice
  for i := 0; i < size; i ++ {
    _elem26 := &TaskItem{}
    if err := _elem26.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem26), err)
    }
    p.Tasks = append(p.Tasks, _elem26)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *PickTaskResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *PickTaskResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "PickTaskResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *PickTaskResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Tasks", thrift.LIST, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Tasks: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Tasks)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.Tasks {
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Tasks: ", p), err) }
  return err
}

func (p *PickTaskResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *PickTaskResponse) Equals(other *PickTaskResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if len(p.Tasks) != len(other.Tasks) { return false }
  for i, _tgt := range p.Tasks {
    _src27 := other.Tasks[i]
    if !_tgt.Equals(_src27) { return false }
  }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *PickTaskResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("PickTaskResponse(%+v)", *p)
}

// Attributes:
//  - Position
//  - Base
type PickDelRequest struct {
  Position string `thrift:"Position,1" db:"Position" json:"position"`
  // unused fields # 2 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewPickDelRequest() *PickDelRequest {
  return &PickDelRequest{}
}


func (p *PickDelRequest) GetPosition() string {
  return p.Position
}
var PickDelRequest_Base_DEFAULT *base.Base
func (p *PickDelRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return PickDelRequest_Base_DEFAULT
  }
return p.Base
}
func (p *PickDelRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *PickDelRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *PickDelRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Position = v
}
  return nil
}

func (p *PickDelRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *PickDelRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "PickDelRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *PickDelRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Position", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Position: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Position)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Position (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Position: ", p), err) }
  return err
}

func (p *PickDelRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *PickDelRequest) Equals(other *PickDelRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Position != other.Position { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *PickDelRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("PickDelRequest(%+v)", *p)
}

// Attributes:
//  - Tasks
//  - BaseResp
type PickDelResponse struct {
  Tasks []*TaskItem `thrift:"Tasks,1" db:"Tasks" json:"tasks"`
  // unused fields # 2 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewPickDelResponse() *PickDelResponse {
  return &PickDelResponse{}
}


func (p *PickDelResponse) GetTasks() []*TaskItem {
  return p.Tasks
}
var PickDelResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *PickDelResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return PickDelResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *PickDelResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *PickDelResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *PickDelResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*TaskItem, 0, size)
  p.Tasks =  tSlice
  for i := 0; i < size; i ++ {
    _elem28 := &TaskItem{}
    if err := _elem28.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem28), err)
    }
    p.Tasks = append(p.Tasks, _elem28)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *PickDelResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *PickDelResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "PickDelResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *PickDelResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Tasks", thrift.LIST, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Tasks: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Tasks)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.Tasks {
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Tasks: ", p), err) }
  return err
}

func (p *PickDelResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *PickDelResponse) Equals(other *PickDelResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if len(p.Tasks) != len(other.Tasks) { return false }
  for i, _tgt := range p.Tasks {
    _src29 := other.Tasks[i]
    if !_tgt.Equals(_src29) { return false }
  }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *PickDelResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("PickDelResponse(%+v)", *p)
}

// Attributes:
//  - ID
//  - Title
//  - Description
//  - Parent
//  - Status
//  - Priority
//  - Level
//  - Category
//  - DDL
//  - CreateTime
//  - Subs
//  - TimeInvested
//  - TimeSpan
//  - Label
type TaskItem struct {
  ID string `thrift:"ID,1" db:"ID" json:"id"`
  Title string `thrift:"Title,2" db:"Title" json:"title"`
  Description string `thrift:"Description,3" db:"Description" json:"description"`
  Parent string `thrift:"Parent,4" db:"Parent" json:"parent"`
  Status string `thrift:"Status,5" db:"Status" json:"status"`
  Priority int64 `thrift:"Priority,6" db:"Priority" json:"priority"`
  Level int64 `thrift:"Level,7" db:"Level" json:"level"`
  Category string `thrift:"Category,8" db:"Category" json:"category"`
  DDL string `thrift:"DDL,9" db:"DDL" json:"ddl"`
  CreateTime string `thrift:"CreateTime,10" db:"CreateTime" json:"createTime"`
  Subs []*TaskItem `thrift:"Subs,11" db:"Subs" json:"subs"`
  TimeInvested int64 `thrift:"TimeInvested,12" db:"TimeInvested" json:"timeInvested"`
  TimeSpan []int64 `thrift:"TimeSpan,13" db:"TimeSpan" json:"timeSpan"`
  Label *string `thrift:"Label,14" db:"Label" json:"label"`
}

func NewTaskItem() *TaskItem {
  return &TaskItem{}
}


func (p *TaskItem) GetID() string {
  return p.ID
}

func (p *TaskItem) GetTitle() string {
  return p.Title
}

func (p *TaskItem) GetDescription() string {
  return p.Description
}

func (p *TaskItem) GetParent() string {
  return p.Parent
}

func (p *TaskItem) GetStatus() string {
  return p.Status
}

func (p *TaskItem) GetPriority() int64 {
  return p.Priority
}

func (p *TaskItem) GetLevel() int64 {
  return p.Level
}

func (p *TaskItem) GetCategory() string {
  return p.Category
}

func (p *TaskItem) GetDDL() string {
  return p.DDL
}

func (p *TaskItem) GetCreateTime() string {
  return p.CreateTime
}

func (p *TaskItem) GetSubs() []*TaskItem {
  return p.Subs
}

func (p *TaskItem) GetTimeInvested() int64 {
  return p.TimeInvested
}

func (p *TaskItem) GetTimeSpan() []int64 {
  return p.TimeSpan
}
var TaskItem_Label_DEFAULT string
func (p *TaskItem) GetLabel() string {
  if !p.IsSetLabel() {
    return TaskItem_Label_DEFAULT
  }
return *p.Label
}
func (p *TaskItem) IsSetLabel() bool {
  return p.Label != nil
}

func (p *TaskItem) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 10:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField10(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 11:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField11(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 12:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField12(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 13:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField13(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 14:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField14(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskItem)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.ID = v
}
  return nil
}

func (p *TaskItem)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Title = v
}
  return nil
}

func (p *TaskItem)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Description = v
}
  return nil
}

func (p *TaskItem)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Parent = v
}
  return nil
}

func (p *TaskItem)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Status = v
}
  return nil
}

func (p *TaskItem)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.Priority = v
}
  return nil
}

func (p *TaskItem)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.Level = v
}
  return nil
}

func (p *TaskItem)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.Category = v
}
  return nil
}

func (p *TaskItem)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.DDL = v
}
  return nil
}

func (p *TaskItem)  ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 10: ", err)
} else {
  p.CreateTime = v
}
  return nil
}

func (p *TaskItem)  ReadField11(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*TaskItem, 0, size)
  p.Subs =  tSlice
  for i := 0; i < size; i ++ {
    _elem30 := &TaskItem{}
    if err := _elem30.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem30), err)
    }
    p.Subs = append(p.Subs, _elem30)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *TaskItem)  ReadField12(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 12: ", err)
} else {
  p.TimeInvested = v
}
  return nil
}

func (p *TaskItem)  ReadField13(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]int64, 0, size)
  p.TimeSpan =  tSlice
  for i := 0; i < size; i ++ {
var _elem31 int64
    if v, err := iprot.ReadI64(ctx); err != nil {
    return thrift.PrependError("error reading field 0: ", err)
} else {
    _elem31 = v
}
    p.TimeSpan = append(p.TimeSpan, _elem31)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *TaskItem)  ReadField14(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 14: ", err)
} else {
  p.Label = &v
}
  return nil
}

func (p *TaskItem) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "TaskItem"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
    if err := p.writeField10(ctx, oprot); err != nil { return err }
    if err := p.writeField11(ctx, oprot); err != nil { return err }
    if err := p.writeField12(ctx, oprot); err != nil { return err }
    if err := p.writeField13(ctx, oprot); err != nil { return err }
    if err := p.writeField14(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskItem) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "ID", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:ID: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.ID)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.ID (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:ID: ", p), err) }
  return err
}

func (p *TaskItem) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Title", thrift.STRING, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Title: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Title)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Title (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Title: ", p), err) }
  return err
}

func (p *TaskItem) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Description", thrift.STRING, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Description: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Description)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Description (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Description: ", p), err) }
  return err
}

func (p *TaskItem) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Parent", thrift.STRING, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:Parent: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Parent)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Parent (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:Parent: ", p), err) }
  return err
}

func (p *TaskItem) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Status", thrift.STRING, 5); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:Status: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Status)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Status (5) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 5:Status: ", p), err) }
  return err
}

func (p *TaskItem) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Priority", thrift.I64, 6); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:Priority: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Priority)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Priority (6) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 6:Priority: ", p), err) }
  return err
}

func (p *TaskItem) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Level", thrift.I64, 7); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:Level: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Level)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Level (7) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 7:Level: ", p), err) }
  return err
}

func (p *TaskItem) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Category", thrift.STRING, 8); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:Category: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Category)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Category (8) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 8:Category: ", p), err) }
  return err
}

func (p *TaskItem) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "DDL", thrift.STRING, 9); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:DDL: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.DDL)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.DDL (9) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 9:DDL: ", p), err) }
  return err
}

func (p *TaskItem) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "CreateTime", thrift.STRING, 10); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:CreateTime: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.CreateTime)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.CreateTime (10) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 10:CreateTime: ", p), err) }
  return err
}

func (p *TaskItem) writeField11(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Subs", thrift.LIST, 11); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 11:Subs: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Subs)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.Subs {
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 11:Subs: ", p), err) }
  return err
}

func (p *TaskItem) writeField12(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "TimeInvested", thrift.I64, 12); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 12:TimeInvested: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.TimeInvested)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.TimeInvested (12) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 12:TimeInvested: ", p), err) }
  return err
}

func (p *TaskItem) writeField13(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "TimeSpan", thrift.LIST, 13); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 13:TimeSpan: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.I64, len(p.TimeSpan)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.TimeSpan {
    if err := oprot.WriteI64(ctx, int64(v)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 13:TimeSpan: ", p), err) }
  return err
}

func (p *TaskItem) writeField14(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetLabel() {
    if err := oprot.WriteFieldBegin(ctx, "Label", thrift.STRING, 14); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 14:Label: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Label)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Label (14) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 14:Label: ", p), err) }
  }
  return err
}

func (p *TaskItem) Equals(other *TaskItem) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.ID != other.ID { return false }
  if p.Title != other.Title { return false }
  if p.Description != other.Description { return false }
  if p.Parent != other.Parent { return false }
  if p.Status != other.Status { return false }
  if p.Priority != other.Priority { return false }
  if p.Level != other.Level { return false }
  if p.Category != other.Category { return false }
  if p.DDL != other.DDL { return false }
  if p.CreateTime != other.CreateTime { return false }
  if len(p.Subs) != len(other.Subs) { return false }
  for i, _tgt := range p.Subs {
    _src32 := other.Subs[i]
    if !_tgt.Equals(_src32) { return false }
  }
  if p.TimeInvested != other.TimeInvested { return false }
  if len(p.TimeSpan) != len(other.TimeSpan) { return false }
  for i, _tgt := range p.TimeSpan {
    _src33 := other.TimeSpan[i]
    if _tgt != _src33 { return false }
  }
  if p.Label != other.Label {
    if p.Label == nil || other.Label == nil {
      return false
    }
    if (*p.Label) != (*other.Label) { return false }
  }
  return true
}

func (p *TaskItem) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskItem(%+v)", *p)
}

// Attributes:
//  - Root
//  - Status
//  - Priority
//  - DDL
//  - Grep
//  - DefaultType
//  - ShowTime
//  - FilterTime
//  - InvestedTimeTimes
//  - Level
//  - Base
type ListTaskRequest struct {
  Root *string `thrift:"Root,1" db:"Root" form:"root" json:"root"`
  Status *string `thrift:"Status,2" db:"Status" form:"status" json:"status"`
  Priority *string `thrift:"Priority,3" db:"Priority" form:"priority" json:"priority"`
  DDL *string `thrift:"DDL,4" db:"DDL" form:"ddl" json:"ddl"`
  Grep *string `thrift:"Grep,5" db:"Grep" form:"grep" json:"grep"`
  DefaultType *string `thrift:"DefaultType,6" db:"DefaultType" form:"defaultType" json:"defaultType"`
  ShowTime *string `thrift:"ShowTime,7" db:"ShowTime" form:"showTime" json:"showTime"`
  FilterTime *string `thrift:"FilterTime,8" db:"FilterTime" form:"filterTime" json:"filterTime"`
  InvestedTimeTimes *string `thrift:"InvestedTimeTimes,9" db:"InvestedTimeTimes" form:"investedTimeTimes" json:"investedTimeTimes"`
  Level *string `thrift:"Level,10" db:"Level" form:"level" json:"level"`
  // unused fields # 11 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewListTaskRequest() *ListTaskRequest {
  return &ListTaskRequest{}
}

var ListTaskRequest_Root_DEFAULT string
func (p *ListTaskRequest) GetRoot() string {
  if !p.IsSetRoot() {
    return ListTaskRequest_Root_DEFAULT
  }
return *p.Root
}
var ListTaskRequest_Status_DEFAULT string
func (p *ListTaskRequest) GetStatus() string {
  if !p.IsSetStatus() {
    return ListTaskRequest_Status_DEFAULT
  }
return *p.Status
}
var ListTaskRequest_Priority_DEFAULT string
func (p *ListTaskRequest) GetPriority() string {
  if !p.IsSetPriority() {
    return ListTaskRequest_Priority_DEFAULT
  }
return *p.Priority
}
var ListTaskRequest_DDL_DEFAULT string
func (p *ListTaskRequest) GetDDL() string {
  if !p.IsSetDDL() {
    return ListTaskRequest_DDL_DEFAULT
  }
return *p.DDL
}
var ListTaskRequest_Grep_DEFAULT string
func (p *ListTaskRequest) GetGrep() string {
  if !p.IsSetGrep() {
    return ListTaskRequest_Grep_DEFAULT
  }
return *p.Grep
}
var ListTaskRequest_DefaultType_DEFAULT string
func (p *ListTaskRequest) GetDefaultType() string {
  if !p.IsSetDefaultType() {
    return ListTaskRequest_DefaultType_DEFAULT
  }
return *p.DefaultType
}
var ListTaskRequest_ShowTime_DEFAULT string
func (p *ListTaskRequest) GetShowTime() string {
  if !p.IsSetShowTime() {
    return ListTaskRequest_ShowTime_DEFAULT
  }
return *p.ShowTime
}
var ListTaskRequest_FilterTime_DEFAULT string
func (p *ListTaskRequest) GetFilterTime() string {
  if !p.IsSetFilterTime() {
    return ListTaskRequest_FilterTime_DEFAULT
  }
return *p.FilterTime
}
var ListTaskRequest_InvestedTimeTimes_DEFAULT string
func (p *ListTaskRequest) GetInvestedTimeTimes() string {
  if !p.IsSetInvestedTimeTimes() {
    return ListTaskRequest_InvestedTimeTimes_DEFAULT
  }
return *p.InvestedTimeTimes
}
var ListTaskRequest_Level_DEFAULT string
func (p *ListTaskRequest) GetLevel() string {
  if !p.IsSetLevel() {
    return ListTaskRequest_Level_DEFAULT
  }
return *p.Level
}
var ListTaskRequest_Base_DEFAULT *base.Base
func (p *ListTaskRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return ListTaskRequest_Base_DEFAULT
  }
return p.Base
}
func (p *ListTaskRequest) IsSetRoot() bool {
  return p.Root != nil
}

func (p *ListTaskRequest) IsSetStatus() bool {
  return p.Status != nil
}

func (p *ListTaskRequest) IsSetPriority() bool {
  return p.Priority != nil
}

func (p *ListTaskRequest) IsSetDDL() bool {
  return p.DDL != nil
}

func (p *ListTaskRequest) IsSetGrep() bool {
  return p.Grep != nil
}

func (p *ListTaskRequest) IsSetDefaultType() bool {
  return p.DefaultType != nil
}

func (p *ListTaskRequest) IsSetShowTime() bool {
  return p.ShowTime != nil
}

func (p *ListTaskRequest) IsSetFilterTime() bool {
  return p.FilterTime != nil
}

func (p *ListTaskRequest) IsSetInvestedTimeTimes() bool {
  return p.InvestedTimeTimes != nil
}

func (p *ListTaskRequest) IsSetLevel() bool {
  return p.Level != nil
}

func (p *ListTaskRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *ListTaskRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 5:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField5(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 6:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField6(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 7:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField7(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 8:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField8(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 9:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField9(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 10:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField10(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *ListTaskRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Root = &v
}
  return nil
}

func (p *ListTaskRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Status = &v
}
  return nil
}

func (p *ListTaskRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Priority = &v
}
  return nil
}

func (p *ListTaskRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.DDL = &v
}
  return nil
}

func (p *ListTaskRequest)  ReadField5(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 5: ", err)
} else {
  p.Grep = &v
}
  return nil
}

func (p *ListTaskRequest)  ReadField6(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 6: ", err)
} else {
  p.DefaultType = &v
}
  return nil
}

func (p *ListTaskRequest)  ReadField7(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 7: ", err)
} else {
  p.ShowTime = &v
}
  return nil
}

func (p *ListTaskRequest)  ReadField8(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 8: ", err)
} else {
  p.FilterTime = &v
}
  return nil
}

func (p *ListTaskRequest)  ReadField9(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 9: ", err)
} else {
  p.InvestedTimeTimes = &v
}
  return nil
}

func (p *ListTaskRequest)  ReadField10(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 10: ", err)
} else {
  p.Level = &v
}
  return nil
}

func (p *ListTaskRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *ListTaskRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ListTaskRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField5(ctx, oprot); err != nil { return err }
    if err := p.writeField6(ctx, oprot); err != nil { return err }
    if err := p.writeField7(ctx, oprot); err != nil { return err }
    if err := p.writeField8(ctx, oprot); err != nil { return err }
    if err := p.writeField9(ctx, oprot); err != nil { return err }
    if err := p.writeField10(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ListTaskRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetRoot() {
    if err := oprot.WriteFieldBegin(ctx, "Root", thrift.STRING, 1); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Root: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Root)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Root (1) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Root: ", p), err) }
  }
  return err
}

func (p *ListTaskRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetStatus() {
    if err := oprot.WriteFieldBegin(ctx, "Status", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Status: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Status)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Status (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Status: ", p), err) }
  }
  return err
}

func (p *ListTaskRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetPriority() {
    if err := oprot.WriteFieldBegin(ctx, "Priority", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Priority: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Priority)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Priority (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Priority: ", p), err) }
  }
  return err
}

func (p *ListTaskRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetDDL() {
    if err := oprot.WriteFieldBegin(ctx, "DDL", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:DDL: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.DDL)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.DDL (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:DDL: ", p), err) }
  }
  return err
}

func (p *ListTaskRequest) writeField5(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetGrep() {
    if err := oprot.WriteFieldBegin(ctx, "Grep", thrift.STRING, 5); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 5:Grep: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Grep)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Grep (5) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 5:Grep: ", p), err) }
  }
  return err
}

func (p *ListTaskRequest) writeField6(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetDefaultType() {
    if err := oprot.WriteFieldBegin(ctx, "DefaultType", thrift.STRING, 6); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 6:DefaultType: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.DefaultType)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.DefaultType (6) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 6:DefaultType: ", p), err) }
  }
  return err
}

func (p *ListTaskRequest) writeField7(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetShowTime() {
    if err := oprot.WriteFieldBegin(ctx, "ShowTime", thrift.STRING, 7); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 7:ShowTime: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.ShowTime)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.ShowTime (7) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 7:ShowTime: ", p), err) }
  }
  return err
}

func (p *ListTaskRequest) writeField8(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetFilterTime() {
    if err := oprot.WriteFieldBegin(ctx, "FilterTime", thrift.STRING, 8); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 8:FilterTime: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.FilterTime)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.FilterTime (8) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 8:FilterTime: ", p), err) }
  }
  return err
}

func (p *ListTaskRequest) writeField9(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetInvestedTimeTimes() {
    if err := oprot.WriteFieldBegin(ctx, "InvestedTimeTimes", thrift.STRING, 9); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 9:InvestedTimeTimes: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.InvestedTimeTimes)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.InvestedTimeTimes (9) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 9:InvestedTimeTimes: ", p), err) }
  }
  return err
}

func (p *ListTaskRequest) writeField10(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetLevel() {
    if err := oprot.WriteFieldBegin(ctx, "Level", thrift.STRING, 10); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 10:Level: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Level)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Level (10) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 10:Level: ", p), err) }
  }
  return err
}

func (p *ListTaskRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *ListTaskRequest) Equals(other *ListTaskRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Root != other.Root {
    if p.Root == nil || other.Root == nil {
      return false
    }
    if (*p.Root) != (*other.Root) { return false }
  }
  if p.Status != other.Status {
    if p.Status == nil || other.Status == nil {
      return false
    }
    if (*p.Status) != (*other.Status) { return false }
  }
  if p.Priority != other.Priority {
    if p.Priority == nil || other.Priority == nil {
      return false
    }
    if (*p.Priority) != (*other.Priority) { return false }
  }
  if p.DDL != other.DDL {
    if p.DDL == nil || other.DDL == nil {
      return false
    }
    if (*p.DDL) != (*other.DDL) { return false }
  }
  if p.Grep != other.Grep {
    if p.Grep == nil || other.Grep == nil {
      return false
    }
    if (*p.Grep) != (*other.Grep) { return false }
  }
  if p.DefaultType != other.DefaultType {
    if p.DefaultType == nil || other.DefaultType == nil {
      return false
    }
    if (*p.DefaultType) != (*other.DefaultType) { return false }
  }
  if p.ShowTime != other.ShowTime {
    if p.ShowTime == nil || other.ShowTime == nil {
      return false
    }
    if (*p.ShowTime) != (*other.ShowTime) { return false }
  }
  if p.FilterTime != other.FilterTime {
    if p.FilterTime == nil || other.FilterTime == nil {
      return false
    }
    if (*p.FilterTime) != (*other.FilterTime) { return false }
  }
  if p.InvestedTimeTimes != other.InvestedTimeTimes {
    if p.InvestedTimeTimes == nil || other.InvestedTimeTimes == nil {
      return false
    }
    if (*p.InvestedTimeTimes) != (*other.InvestedTimeTimes) { return false }
  }
  if p.Level != other.Level {
    if p.Level == nil || other.Level == nil {
      return false
    }
    if (*p.Level) != (*other.Level) { return false }
  }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *ListTaskRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ListTaskRequest(%+v)", *p)
}

// Attributes:
//  - TreeRoot
//  - Marks
//  - Timelines
//  - MaxLevel
//  - BaseResp
type ListTaskResponse struct {
  TreeRoot *TaskItem `thrift:"TreeRoot,1" db:"TreeRoot" json:"treeRoot"`
  Marks []*TaskMarkItem `thrift:"Marks,2" db:"Marks" json:"marks"`
  Timelines [][]string `thrift:"Timelines,3" db:"Timelines" json:"timelines"`
  MaxLevel int64 `thrift:"MaxLevel,4" db:"MaxLevel" json:"maxLevel"`
  // unused fields # 5 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewListTaskResponse() *ListTaskResponse {
  return &ListTaskResponse{}
}

var ListTaskResponse_TreeRoot_DEFAULT *TaskItem
func (p *ListTaskResponse) GetTreeRoot() *TaskItem {
  if !p.IsSetTreeRoot() {
    return ListTaskResponse_TreeRoot_DEFAULT
  }
return p.TreeRoot
}

func (p *ListTaskResponse) GetMarks() []*TaskMarkItem {
  return p.Marks
}

func (p *ListTaskResponse) GetTimelines() [][]string {
  return p.Timelines
}

func (p *ListTaskResponse) GetMaxLevel() int64 {
  return p.MaxLevel
}
var ListTaskResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *ListTaskResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return ListTaskResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *ListTaskResponse) IsSetTreeRoot() bool {
  return p.TreeRoot != nil
}

func (p *ListTaskResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *ListTaskResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *ListTaskResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.TreeRoot = &TaskItem{}
  if err := p.TreeRoot.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.TreeRoot), err)
  }
  return nil
}

func (p *ListTaskResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*TaskMarkItem, 0, size)
  p.Marks =  tSlice
  for i := 0; i < size; i ++ {
    _elem34 := &TaskMarkItem{}
    if err := _elem34.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem34), err)
    }
    p.Marks = append(p.Marks, _elem34)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *ListTaskResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([][]string, 0, size)
  p.Timelines =  tSlice
  for i := 0; i < size; i ++ {
    _, size, err := iprot.ReadListBegin(ctx)
    if err != nil {
      return thrift.PrependError("error reading list begin: ", err)
    }
    tSlice := make([]string, 0, size)
    _elem35 :=  tSlice
    for i := 0; i < size; i ++ {
var _elem36 string
      if v, err := iprot.ReadString(ctx); err != nil {
      return thrift.PrependError("error reading field 0: ", err)
} else {
      _elem36 = v
}
      _elem35 = append(_elem35, _elem36)
    }
    if err := iprot.ReadListEnd(ctx); err != nil {
      return thrift.PrependError("error reading list end: ", err)
    }
    p.Timelines = append(p.Timelines, _elem35)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *ListTaskResponse)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.MaxLevel = v
}
  return nil
}

func (p *ListTaskResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *ListTaskResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ListTaskResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *ListTaskResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "TreeRoot", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:TreeRoot: ", p), err) }
  if err := p.TreeRoot.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.TreeRoot), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:TreeRoot: ", p), err) }
  return err
}

func (p *ListTaskResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Marks", thrift.LIST, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Marks: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Marks)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.Marks {
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Marks: ", p), err) }
  return err
}

func (p *ListTaskResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Timelines", thrift.LIST, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Timelines: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.LIST, len(p.Timelines)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.Timelines {
    if err := oprot.WriteListBegin(ctx, thrift.STRING, len(v)); err != nil {
      return thrift.PrependError("error writing list begin: ", err)
    }
    for _, v := range v {
      if err := oprot.WriteString(ctx, string(v)); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T. (0) field write error: ", p), err) }
    }
    if err := oprot.WriteListEnd(ctx); err != nil {
      return thrift.PrependError("error writing list end: ", err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Timelines: ", p), err) }
  return err
}

func (p *ListTaskResponse) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "MaxLevel", thrift.I64, 4); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:MaxLevel: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.MaxLevel)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.MaxLevel (4) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 4:MaxLevel: ", p), err) }
  return err
}

func (p *ListTaskResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *ListTaskResponse) Equals(other *ListTaskResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if !p.TreeRoot.Equals(other.TreeRoot) { return false }
  if len(p.Marks) != len(other.Marks) { return false }
  for i, _tgt := range p.Marks {
    _src37 := other.Marks[i]
    if !_tgt.Equals(_src37) { return false }
  }
  if len(p.Timelines) != len(other.Timelines) { return false }
  for i, _tgt := range p.Timelines {
    _src38 := other.Timelines[i]
    if len(_tgt) != len(_src38) { return false }
    for i, _tgt := range _tgt {
      _src39 := _src38[i]
      if _tgt != _src39 { return false }
    }
  }
  if p.MaxLevel != other.MaxLevel { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *ListTaskResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("ListTaskResponse(%+v)", *p)
}

// Attributes:
//  - Offset
//  - Count
//  - Base
type GetTaskPageRequest struct {
  Offset int64 `thrift:"Offset,1" db:"Offset" json:"Offset"`
  Count int64 `thrift:"Count,2" db:"Count" json:"Count"`
  // unused fields # 3 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewGetTaskPageRequest() *GetTaskPageRequest {
  return &GetTaskPageRequest{}
}


func (p *GetTaskPageRequest) GetOffset() int64 {
  return p.Offset
}

func (p *GetTaskPageRequest) GetCount() int64 {
  return p.Count
}
var GetTaskPageRequest_Base_DEFAULT *base.Base
func (p *GetTaskPageRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return GetTaskPageRequest_Base_DEFAULT
  }
return p.Base
}
func (p *GetTaskPageRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *GetTaskPageRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetTaskPageRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Offset = v
}
  return nil
}

func (p *GetTaskPageRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.Count = v
}
  return nil
}

func (p *GetTaskPageRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *GetTaskPageRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetTaskPageRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetTaskPageRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Offset", thrift.I64, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Offset: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Offset)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Offset (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Offset: ", p), err) }
  return err
}

func (p *GetTaskPageRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Count", thrift.I64, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:Count: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Count)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Count (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:Count: ", p), err) }
  return err
}

func (p *GetTaskPageRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *GetTaskPageRequest) Equals(other *GetTaskPageRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Offset != other.Offset { return false }
  if p.Count != other.Count { return false }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *GetTaskPageRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetTaskPageRequest(%+v)", *p)
}

// Attributes:
//  - TaskList
//  - HasMore
//  - Total
//  - BaseResp
type GetTaskPageResponse struct {
  TaskList []*TaskItem `thrift:"TaskList,1" db:"TaskList" json:"TaskList"`
  HasMore bool `thrift:"HasMore,2" db:"HasMore" json:"HasMore"`
  Total int64 `thrift:"Total,3" db:"Total" json:"Total"`
  // unused fields # 4 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewGetTaskPageResponse() *GetTaskPageResponse {
  return &GetTaskPageResponse{}
}


func (p *GetTaskPageResponse) GetTaskList() []*TaskItem {
  return p.TaskList
}

func (p *GetTaskPageResponse) GetHasMore() bool {
  return p.HasMore
}

func (p *GetTaskPageResponse) GetTotal() int64 {
  return p.Total
}
var GetTaskPageResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *GetTaskPageResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return GetTaskPageResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *GetTaskPageResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *GetTaskPageResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.BOOL {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.I64 {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *GetTaskPageResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*TaskItem, 0, size)
  p.TaskList =  tSlice
  for i := 0; i < size; i ++ {
    _elem40 := &TaskItem{}
    if err := _elem40.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem40), err)
    }
    p.TaskList = append(p.TaskList, _elem40)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *GetTaskPageResponse)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadBool(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.HasMore = v
}
  return nil
}

func (p *GetTaskPageResponse)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadI64(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.Total = v
}
  return nil
}

func (p *GetTaskPageResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *GetTaskPageResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetTaskPageResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *GetTaskPageResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "TaskList", thrift.LIST, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:TaskList: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.TaskList)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.TaskList {
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:TaskList: ", p), err) }
  return err
}

func (p *GetTaskPageResponse) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "HasMore", thrift.BOOL, 2); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:HasMore: ", p), err) }
  if err := oprot.WriteBool(ctx, bool(p.HasMore)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.HasMore (2) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 2:HasMore: ", p), err) }
  return err
}

func (p *GetTaskPageResponse) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Total", thrift.I64, 3); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:Total: ", p), err) }
  if err := oprot.WriteI64(ctx, int64(p.Total)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Total (3) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 3:Total: ", p), err) }
  return err
}

func (p *GetTaskPageResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *GetTaskPageResponse) Equals(other *GetTaskPageResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if len(p.TaskList) != len(other.TaskList) { return false }
  for i, _tgt := range p.TaskList {
    _src41 := other.TaskList[i]
    if !_tgt.Equals(_src41) { return false }
  }
  if p.HasMore != other.HasMore { return false }
  if p.Total != other.Total { return false }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *GetTaskPageResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("GetTaskPageResponse(%+v)", *p)
}

// Attributes:
//  - Keyword
//  - InMarks
//  - MarkType
//  - Limit
//  - Base
type SearchTaskRequest struct {
  Keyword string `thrift:"Keyword,1" db:"Keyword" form:"keyword" json:"keyword"`
  InMarks *string `thrift:"InMarks,2" db:"InMarks" form:"inMarks" json:"inMarks"`
  MarkType *string `thrift:"MarkType,3" db:"MarkType" form:"markType" json:"markType"`
  Limit *string `thrift:"Limit,4" db:"Limit" form:"limit" json:"limit"`
  // unused fields # 5 to 254
  Base *base.Base `thrift:"Base,255" db:"Base" json:"Base"`
}

func NewSearchTaskRequest() *SearchTaskRequest {
  return &SearchTaskRequest{}
}


func (p *SearchTaskRequest) GetKeyword() string {
  return p.Keyword
}
var SearchTaskRequest_InMarks_DEFAULT string
func (p *SearchTaskRequest) GetInMarks() string {
  if !p.IsSetInMarks() {
    return SearchTaskRequest_InMarks_DEFAULT
  }
return *p.InMarks
}
var SearchTaskRequest_MarkType_DEFAULT string
func (p *SearchTaskRequest) GetMarkType() string {
  if !p.IsSetMarkType() {
    return SearchTaskRequest_MarkType_DEFAULT
  }
return *p.MarkType
}
var SearchTaskRequest_Limit_DEFAULT string
func (p *SearchTaskRequest) GetLimit() string {
  if !p.IsSetLimit() {
    return SearchTaskRequest_Limit_DEFAULT
  }
return *p.Limit
}
var SearchTaskRequest_Base_DEFAULT *base.Base
func (p *SearchTaskRequest) GetBase() *base.Base {
  if !p.IsSetBase() {
    return SearchTaskRequest_Base_DEFAULT
  }
return p.Base
}
func (p *SearchTaskRequest) IsSetInMarks() bool {
  return p.InMarks != nil
}

func (p *SearchTaskRequest) IsSetMarkType() bool {
  return p.MarkType != nil
}

func (p *SearchTaskRequest) IsSetLimit() bool {
  return p.Limit != nil
}

func (p *SearchTaskRequest) IsSetBase() bool {
  return p.Base != nil
}

func (p *SearchTaskRequest) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 2:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField2(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 3:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField3(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 4:
      if fieldTypeId == thrift.STRING {
        if err := p.ReadField4(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *SearchTaskRequest)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 1: ", err)
} else {
  p.Keyword = v
}
  return nil
}

func (p *SearchTaskRequest)  ReadField2(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 2: ", err)
} else {
  p.InMarks = &v
}
  return nil
}

func (p *SearchTaskRequest)  ReadField3(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 3: ", err)
} else {
  p.MarkType = &v
}
  return nil
}

func (p *SearchTaskRequest)  ReadField4(ctx context.Context, iprot thrift.TProtocol) error {
  if v, err := iprot.ReadString(ctx); err != nil {
  return thrift.PrependError("error reading field 4: ", err)
} else {
  p.Limit = &v
}
  return nil
}

func (p *SearchTaskRequest)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.Base = &base.Base{}
  if err := p.Base.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Base), err)
  }
  return nil
}

func (p *SearchTaskRequest) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "SearchTaskRequest"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField2(ctx, oprot); err != nil { return err }
    if err := p.writeField3(ctx, oprot); err != nil { return err }
    if err := p.writeField4(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *SearchTaskRequest) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Keyword", thrift.STRING, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Keyword: ", p), err) }
  if err := oprot.WriteString(ctx, string(p.Keyword)); err != nil {
  return thrift.PrependError(fmt.Sprintf("%T.Keyword (1) field write error: ", p), err) }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Keyword: ", p), err) }
  return err
}

func (p *SearchTaskRequest) writeField2(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetInMarks() {
    if err := oprot.WriteFieldBegin(ctx, "InMarks", thrift.STRING, 2); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 2:InMarks: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.InMarks)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.InMarks (2) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 2:InMarks: ", p), err) }
  }
  return err
}

func (p *SearchTaskRequest) writeField3(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetMarkType() {
    if err := oprot.WriteFieldBegin(ctx, "MarkType", thrift.STRING, 3); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 3:MarkType: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.MarkType)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.MarkType (3) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 3:MarkType: ", p), err) }
  }
  return err
}

func (p *SearchTaskRequest) writeField4(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetLimit() {
    if err := oprot.WriteFieldBegin(ctx, "Limit", thrift.STRING, 4); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 4:Limit: ", p), err) }
    if err := oprot.WriteString(ctx, string(*p.Limit)); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T.Limit (4) field write error: ", p), err) }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 4:Limit: ", p), err) }
  }
  return err
}

func (p *SearchTaskRequest) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Base", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:Base: ", p), err) }
  if err := p.Base.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Base), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:Base: ", p), err) }
  return err
}

func (p *SearchTaskRequest) Equals(other *SearchTaskRequest) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if p.Keyword != other.Keyword { return false }
  if p.InMarks != other.InMarks {
    if p.InMarks == nil || other.InMarks == nil {
      return false
    }
    if (*p.InMarks) != (*other.InMarks) { return false }
  }
  if p.MarkType != other.MarkType {
    if p.MarkType == nil || other.MarkType == nil {
      return false
    }
    if (*p.MarkType) != (*other.MarkType) { return false }
  }
  if p.Limit != other.Limit {
    if p.Limit == nil || other.Limit == nil {
      return false
    }
    if (*p.Limit) != (*other.Limit) { return false }
  }
  if !p.Base.Equals(other.Base) { return false }
  return true
}

func (p *SearchTaskRequest) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("SearchTaskRequest(%+v)", *p)
}

// Attributes:
//  - Tasks
//  - BaseResp
type SearchTaskResponse struct {
  Tasks []*TaskItem `thrift:"Tasks,1" db:"Tasks" json:"tasks"`
  // unused fields # 2 to 254
  BaseResp *base.BaseResponse `thrift:"BaseResp,255" db:"BaseResp" json:"BaseResp"`
}

func NewSearchTaskResponse() *SearchTaskResponse {
  return &SearchTaskResponse{}
}


func (p *SearchTaskResponse) GetTasks() []*TaskItem {
  return p.Tasks
}
var SearchTaskResponse_BaseResp_DEFAULT *base.BaseResponse
func (p *SearchTaskResponse) GetBaseResp() *base.BaseResponse {
  if !p.IsSetBaseResp() {
    return SearchTaskResponse_BaseResp_DEFAULT
  }
return p.BaseResp
}
func (p *SearchTaskResponse) IsSetBaseResp() bool {
  return p.BaseResp != nil
}

func (p *SearchTaskResponse) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.LIST {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    case 255:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField255(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *SearchTaskResponse)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  _, size, err := iprot.ReadListBegin(ctx)
  if err != nil {
    return thrift.PrependError("error reading list begin: ", err)
  }
  tSlice := make([]*TaskItem, 0, size)
  p.Tasks =  tSlice
  for i := 0; i < size; i ++ {
    _elem42 := &TaskItem{}
    if err := _elem42.Read(ctx, iprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", _elem42), err)
    }
    p.Tasks = append(p.Tasks, _elem42)
  }
  if err := iprot.ReadListEnd(ctx); err != nil {
    return thrift.PrependError("error reading list end: ", err)
  }
  return nil
}

func (p *SearchTaskResponse)  ReadField255(ctx context.Context, iprot thrift.TProtocol) error {
  p.BaseResp = &base.BaseResponse{}
  if err := p.BaseResp.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.BaseResp), err)
  }
  return nil
}

func (p *SearchTaskResponse) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "SearchTaskResponse"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
    if err := p.writeField255(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *SearchTaskResponse) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "Tasks", thrift.LIST, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:Tasks: ", p), err) }
  if err := oprot.WriteListBegin(ctx, thrift.STRUCT, len(p.Tasks)); err != nil {
    return thrift.PrependError("error writing list begin: ", err)
  }
  for _, v := range p.Tasks {
    if err := v.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", v), err)
    }
  }
  if err := oprot.WriteListEnd(ctx); err != nil {
    return thrift.PrependError("error writing list end: ", err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:Tasks: ", p), err) }
  return err
}

func (p *SearchTaskResponse) writeField255(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "BaseResp", thrift.STRUCT, 255); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 255:BaseResp: ", p), err) }
  if err := p.BaseResp.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.BaseResp), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 255:BaseResp: ", p), err) }
  return err
}

func (p *SearchTaskResponse) Equals(other *SearchTaskResponse) bool {
  if p == other {
    return true
  } else if p == nil || other == nil {
    return false
  }
  if len(p.Tasks) != len(other.Tasks) { return false }
  for i, _tgt := range p.Tasks {
    _src43 := other.Tasks[i]
    if !_tgt.Equals(_src43) { return false }
  }
  if !p.BaseResp.Equals(other.BaseResp) { return false }
  return true
}

func (p *SearchTaskResponse) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("SearchTaskResponse(%+v)", *p)
}

type Task interface {
  // Parameters:
  //  - Req
  AddTask(ctx context.Context, req *AddTaskRequest) (_r *AddTaskResponse, _err error)
  // Parameters:
  //  - Req
  EditTask(ctx context.Context, req *EditTaskRequest) (_r *EditTaskResponse, _err error)
  // Parameters:
  //  - Req
  ListTask(ctx context.Context, req *ListTaskRequest) (_r *ListTaskResponse, _err error)
  // Parameters:
  //  - Req
  GetLast(ctx context.Context, req *GetLastRequest) (_r *GetLastResponse, _err error)
  // Parameters:
  //  - Req
  TaskAction(ctx context.Context, req *TaskActionRequest) (_r *TaskActionResponse, _err error)
  // Parameters:
  //  - Req
  TaskMv(ctx context.Context, req *TaskMvRequest) (_r *TaskMvResponse, _err error)
  // Parameters:
  //  - Req
  GetTaskPage(ctx context.Context, req *GetTaskPageRequest) (_r *GetTaskPageResponse, _err error)
  // Parameters:
  //  - Req
  PickTask(ctx context.Context, req *PickTaskRequest) (_r *PickTaskResponse, _err error)
  // Parameters:
  //  - Req
  PickDel(ctx context.Context, req *PickDelRequest) (_r *PickDelResponse, _err error)
  // Parameters:
  //  - Req
  SearchTask(ctx context.Context, req *SearchTaskRequest) (_r *SearchTaskResponse, _err error)
}

type TaskClient struct {
  c thrift.TClient
  meta thrift.ResponseMeta
}

func NewTaskClientFactory(t thrift.TTransport, f thrift.TProtocolFactory) *TaskClient {
  return &TaskClient{
    c: thrift.NewTStandardClient(f.GetProtocol(t), f.GetProtocol(t)),
  }
}

func NewTaskClientProtocol(t thrift.TTransport, iprot thrift.TProtocol, oprot thrift.TProtocol) *TaskClient {
  return &TaskClient{
    c: thrift.NewTStandardClient(iprot, oprot),
  }
}

func NewTaskClient(c thrift.TClient) *TaskClient {
  return &TaskClient{
    c: c,
  }
}

func (p *TaskClient) Client_() thrift.TClient {
  return p.c
}

func (p *TaskClient) LastResponseMeta_() thrift.ResponseMeta {
  return p.meta
}

func (p *TaskClient) SetLastResponseMeta_(meta thrift.ResponseMeta) {
  p.meta = meta
}

// Parameters:
//  - Req
func (p *TaskClient) AddTask(ctx context.Context, req *AddTaskRequest) (_r *AddTaskResponse, _err error) {
  var _args44 TaskAddTaskArgs
  _args44.Req = req
  var _result46 TaskAddTaskResult
  var _meta45 thrift.ResponseMeta
  _meta45, _err = p.Client_().Call(ctx, "AddTask", &_args44, &_result46)
  p.SetLastResponseMeta_(_meta45)
  if _err != nil {
    return
  }
  if _ret47 := _result46.GetSuccess(); _ret47 != nil {
    return _ret47, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "AddTask failed: unknown result")
}

// Parameters:
//  - Req
func (p *TaskClient) EditTask(ctx context.Context, req *EditTaskRequest) (_r *EditTaskResponse, _err error) {
  var _args48 TaskEditTaskArgs
  _args48.Req = req
  var _result50 TaskEditTaskResult
  var _meta49 thrift.ResponseMeta
  _meta49, _err = p.Client_().Call(ctx, "EditTask", &_args48, &_result50)
  p.SetLastResponseMeta_(_meta49)
  if _err != nil {
    return
  }
  if _ret51 := _result50.GetSuccess(); _ret51 != nil {
    return _ret51, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "EditTask failed: unknown result")
}

// Parameters:
//  - Req
func (p *TaskClient) ListTask(ctx context.Context, req *ListTaskRequest) (_r *ListTaskResponse, _err error) {
  var _args52 TaskListTaskArgs
  _args52.Req = req
  var _result54 TaskListTaskResult
  var _meta53 thrift.ResponseMeta
  _meta53, _err = p.Client_().Call(ctx, "ListTask", &_args52, &_result54)
  p.SetLastResponseMeta_(_meta53)
  if _err != nil {
    return
  }
  if _ret55 := _result54.GetSuccess(); _ret55 != nil {
    return _ret55, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "ListTask failed: unknown result")
}

// Parameters:
//  - Req
func (p *TaskClient) GetLast(ctx context.Context, req *GetLastRequest) (_r *GetLastResponse, _err error) {
  var _args56 TaskGetLastArgs
  _args56.Req = req
  var _result58 TaskGetLastResult
  var _meta57 thrift.ResponseMeta
  _meta57, _err = p.Client_().Call(ctx, "GetLast", &_args56, &_result58)
  p.SetLastResponseMeta_(_meta57)
  if _err != nil {
    return
  }
  if _ret59 := _result58.GetSuccess(); _ret59 != nil {
    return _ret59, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "GetLast failed: unknown result")
}

// Parameters:
//  - Req
func (p *TaskClient) TaskAction(ctx context.Context, req *TaskActionRequest) (_r *TaskActionResponse, _err error) {
  var _args60 TaskTaskActionArgs
  _args60.Req = req
  var _result62 TaskTaskActionResult
  var _meta61 thrift.ResponseMeta
  _meta61, _err = p.Client_().Call(ctx, "TaskAction", &_args60, &_result62)
  p.SetLastResponseMeta_(_meta61)
  if _err != nil {
    return
  }
  if _ret63 := _result62.GetSuccess(); _ret63 != nil {
    return _ret63, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "TaskAction failed: unknown result")
}

// Parameters:
//  - Req
func (p *TaskClient) TaskMv(ctx context.Context, req *TaskMvRequest) (_r *TaskMvResponse, _err error) {
  var _args64 TaskTaskMvArgs
  _args64.Req = req
  var _result66 TaskTaskMvResult
  var _meta65 thrift.ResponseMeta
  _meta65, _err = p.Client_().Call(ctx, "TaskMv", &_args64, &_result66)
  p.SetLastResponseMeta_(_meta65)
  if _err != nil {
    return
  }
  if _ret67 := _result66.GetSuccess(); _ret67 != nil {
    return _ret67, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "TaskMv failed: unknown result")
}

// Parameters:
//  - Req
func (p *TaskClient) GetTaskPage(ctx context.Context, req *GetTaskPageRequest) (_r *GetTaskPageResponse, _err error) {
  var _args68 TaskGetTaskPageArgs
  _args68.Req = req
  var _result70 TaskGetTaskPageResult
  var _meta69 thrift.ResponseMeta
  _meta69, _err = p.Client_().Call(ctx, "GetTaskPage", &_args68, &_result70)
  p.SetLastResponseMeta_(_meta69)
  if _err != nil {
    return
  }
  if _ret71 := _result70.GetSuccess(); _ret71 != nil {
    return _ret71, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "GetTaskPage failed: unknown result")
}

// Parameters:
//  - Req
func (p *TaskClient) PickTask(ctx context.Context, req *PickTaskRequest) (_r *PickTaskResponse, _err error) {
  var _args72 TaskPickTaskArgs
  _args72.Req = req
  var _result74 TaskPickTaskResult
  var _meta73 thrift.ResponseMeta
  _meta73, _err = p.Client_().Call(ctx, "PickTask", &_args72, &_result74)
  p.SetLastResponseMeta_(_meta73)
  if _err != nil {
    return
  }
  if _ret75 := _result74.GetSuccess(); _ret75 != nil {
    return _ret75, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "PickTask failed: unknown result")
}

// Parameters:
//  - Req
func (p *TaskClient) PickDel(ctx context.Context, req *PickDelRequest) (_r *PickDelResponse, _err error) {
  var _args76 TaskPickDelArgs
  _args76.Req = req
  var _result78 TaskPickDelResult
  var _meta77 thrift.ResponseMeta
  _meta77, _err = p.Client_().Call(ctx, "PickDel", &_args76, &_result78)
  p.SetLastResponseMeta_(_meta77)
  if _err != nil {
    return
  }
  if _ret79 := _result78.GetSuccess(); _ret79 != nil {
    return _ret79, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "PickDel failed: unknown result")
}

// Parameters:
//  - Req
func (p *TaskClient) SearchTask(ctx context.Context, req *SearchTaskRequest) (_r *SearchTaskResponse, _err error) {
  var _args80 TaskSearchTaskArgs
  _args80.Req = req
  var _result82 TaskSearchTaskResult
  var _meta81 thrift.ResponseMeta
  _meta81, _err = p.Client_().Call(ctx, "SearchTask", &_args80, &_result82)
  p.SetLastResponseMeta_(_meta81)
  if _err != nil {
    return
  }
  if _ret83 := _result82.GetSuccess(); _ret83 != nil {
    return _ret83, nil
  }
  return nil, thrift.NewTApplicationException(thrift.MISSING_RESULT, "SearchTask failed: unknown result")
}

type TaskProcessor struct {
  processorMap map[string]thrift.TProcessorFunction
  handler Task
}

func (p *TaskProcessor) AddToProcessorMap(key string, processor thrift.TProcessorFunction) {
  p.processorMap[key] = processor
}

func (p *TaskProcessor) GetProcessorFunction(key string) (processor thrift.TProcessorFunction, ok bool) {
  processor, ok = p.processorMap[key]
  return processor, ok
}

func (p *TaskProcessor) ProcessorMap() map[string]thrift.TProcessorFunction {
  return p.processorMap
}

func NewTaskProcessor(handler Task) *TaskProcessor {

  self84 := &TaskProcessor{handler:handler, processorMap:make(map[string]thrift.TProcessorFunction)}
  self84.processorMap["AddTask"] = &taskProcessorAddTask{handler:handler}
  self84.processorMap["EditTask"] = &taskProcessorEditTask{handler:handler}
  self84.processorMap["ListTask"] = &taskProcessorListTask{handler:handler}
  self84.processorMap["GetLast"] = &taskProcessorGetLast{handler:handler}
  self84.processorMap["TaskAction"] = &taskProcessorTaskAction{handler:handler}
  self84.processorMap["TaskMv"] = &taskProcessorTaskMv{handler:handler}
  self84.processorMap["GetTaskPage"] = &taskProcessorGetTaskPage{handler:handler}
  self84.processorMap["PickTask"] = &taskProcessorPickTask{handler:handler}
  self84.processorMap["PickDel"] = &taskProcessorPickDel{handler:handler}
  self84.processorMap["SearchTask"] = &taskProcessorSearchTask{handler:handler}
return self84
}

func (p *TaskProcessor) Process(ctx context.Context, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  name, _, seqId, err2 := iprot.ReadMessageBegin(ctx)
  if err2 != nil { return false, thrift.WrapTException(err2) }
  if processor, ok := p.GetProcessorFunction(name); ok {
    return processor.Process(ctx, seqId, iprot, oprot)
  }
  iprot.Skip(ctx, thrift.STRUCT)
  iprot.ReadMessageEnd(ctx)
  x85 := thrift.NewTApplicationException(thrift.UNKNOWN_METHOD, "Unknown function " + name)
  oprot.WriteMessageBegin(ctx, name, thrift.EXCEPTION, seqId)
  x85.Write(ctx, oprot)
  oprot.WriteMessageEnd(ctx)
  oprot.Flush(ctx)
  return false, x85

}

type taskProcessorAddTask struct {
  handler Task
}

func (p *taskProcessorAddTask) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := TaskAddTaskArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "AddTask", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := TaskAddTaskResult{}
  var retval *AddTaskResponse
  if retval, err2 = p.handler.AddTask(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing AddTask: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "AddTask", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "AddTask", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type taskProcessorEditTask struct {
  handler Task
}

func (p *taskProcessorEditTask) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := TaskEditTaskArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "EditTask", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := TaskEditTaskResult{}
  var retval *EditTaskResponse
  if retval, err2 = p.handler.EditTask(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing EditTask: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "EditTask", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "EditTask", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type taskProcessorListTask struct {
  handler Task
}

func (p *taskProcessorListTask) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := TaskListTaskArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "ListTask", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := TaskListTaskResult{}
  var retval *ListTaskResponse
  if retval, err2 = p.handler.ListTask(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing ListTask: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "ListTask", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "ListTask", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type taskProcessorGetLast struct {
  handler Task
}

func (p *taskProcessorGetLast) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := TaskGetLastArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "GetLast", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := TaskGetLastResult{}
  var retval *GetLastResponse
  if retval, err2 = p.handler.GetLast(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetLast: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "GetLast", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "GetLast", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type taskProcessorTaskAction struct {
  handler Task
}

func (p *taskProcessorTaskAction) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := TaskTaskActionArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "TaskAction", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := TaskTaskActionResult{}
  var retval *TaskActionResponse
  if retval, err2 = p.handler.TaskAction(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing TaskAction: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "TaskAction", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "TaskAction", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type taskProcessorTaskMv struct {
  handler Task
}

func (p *taskProcessorTaskMv) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := TaskTaskMvArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "TaskMv", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := TaskTaskMvResult{}
  var retval *TaskMvResponse
  if retval, err2 = p.handler.TaskMv(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing TaskMv: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "TaskMv", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "TaskMv", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type taskProcessorGetTaskPage struct {
  handler Task
}

func (p *taskProcessorGetTaskPage) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := TaskGetTaskPageArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "GetTaskPage", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := TaskGetTaskPageResult{}
  var retval *GetTaskPageResponse
  if retval, err2 = p.handler.GetTaskPage(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing GetTaskPage: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "GetTaskPage", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "GetTaskPage", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type taskProcessorPickTask struct {
  handler Task
}

func (p *taskProcessorPickTask) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := TaskPickTaskArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "PickTask", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := TaskPickTaskResult{}
  var retval *PickTaskResponse
  if retval, err2 = p.handler.PickTask(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing PickTask: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "PickTask", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "PickTask", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type taskProcessorPickDel struct {
  handler Task
}

func (p *taskProcessorPickDel) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := TaskPickDelArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "PickDel", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := TaskPickDelResult{}
  var retval *PickDelResponse
  if retval, err2 = p.handler.PickDel(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing PickDel: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "PickDel", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "PickDel", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}

type taskProcessorSearchTask struct {
  handler Task
}

func (p *taskProcessorSearchTask) Process(ctx context.Context, seqId int32, iprot, oprot thrift.TProtocol) (success bool, err thrift.TException) {
  args := TaskSearchTaskArgs{}
  var err2 error
  if err2 = args.Read(ctx, iprot); err2 != nil {
    iprot.ReadMessageEnd(ctx)
    x := thrift.NewTApplicationException(thrift.PROTOCOL_ERROR, err2.Error())
    oprot.WriteMessageBegin(ctx, "SearchTask", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return false, thrift.WrapTException(err2)
  }
  iprot.ReadMessageEnd(ctx)

  tickerCancel := func() {}
  // Start a goroutine to do server side connectivity check.
  if thrift.ServerConnectivityCheckInterval > 0 {
    var cancel context.CancelFunc
    ctx, cancel = context.WithCancel(ctx)
    defer cancel()
    var tickerCtx context.Context
    tickerCtx, tickerCancel = context.WithCancel(context.Background())
    defer tickerCancel()
    go func(ctx context.Context, cancel context.CancelFunc) {
      ticker := time.NewTicker(thrift.ServerConnectivityCheckInterval)
      defer ticker.Stop()
      for {
        select {
        case <-ctx.Done():
          return
        case <-ticker.C:
          if !iprot.Transport().IsOpen() {
            cancel()
            return
          }
        }
      }
    }(tickerCtx, cancel)
  }

  result := TaskSearchTaskResult{}
  var retval *SearchTaskResponse
  if retval, err2 = p.handler.SearchTask(ctx, args.Req); err2 != nil {
    tickerCancel()
    if err2 == thrift.ErrAbandonRequest {
      return false, thrift.WrapTException(err2)
    }
    x := thrift.NewTApplicationException(thrift.INTERNAL_ERROR, "Internal error processing SearchTask: " + err2.Error())
    oprot.WriteMessageBegin(ctx, "SearchTask", thrift.EXCEPTION, seqId)
    x.Write(ctx, oprot)
    oprot.WriteMessageEnd(ctx)
    oprot.Flush(ctx)
    return true, thrift.WrapTException(err2)
  } else {
    result.Success = retval
  }
  tickerCancel()
  if err2 = oprot.WriteMessageBegin(ctx, "SearchTask", thrift.REPLY, seqId); err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = result.Write(ctx, oprot); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.WriteMessageEnd(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err2 = oprot.Flush(ctx); err == nil && err2 != nil {
    err = thrift.WrapTException(err2)
  }
  if err != nil {
    return
  }
  return true, err
}


// HELPER FUNCTIONS AND STRUCTURES

// Attributes:
//  - Req
type TaskAddTaskArgs struct {
  Req *AddTaskRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewTaskAddTaskArgs() *TaskAddTaskArgs {
  return &TaskAddTaskArgs{}
}

var TaskAddTaskArgs_Req_DEFAULT *AddTaskRequest
func (p *TaskAddTaskArgs) GetReq() *AddTaskRequest {
  if !p.IsSetReq() {
    return TaskAddTaskArgs_Req_DEFAULT
  }
return p.Req
}
func (p *TaskAddTaskArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *TaskAddTaskArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskAddTaskArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &AddTaskRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *TaskAddTaskArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AddTask_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskAddTaskArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *TaskAddTaskArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskAddTaskArgs(%+v)", *p)
}

// Attributes:
//  - Success
type TaskAddTaskResult struct {
  Success *AddTaskResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewTaskAddTaskResult() *TaskAddTaskResult {
  return &TaskAddTaskResult{}
}

var TaskAddTaskResult_Success_DEFAULT *AddTaskResponse
func (p *TaskAddTaskResult) GetSuccess() *AddTaskResponse {
  if !p.IsSetSuccess() {
    return TaskAddTaskResult_Success_DEFAULT
  }
return p.Success
}
func (p *TaskAddTaskResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *TaskAddTaskResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskAddTaskResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &AddTaskResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *TaskAddTaskResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "AddTask_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskAddTaskResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *TaskAddTaskResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskAddTaskResult(%+v)", *p)
}

// Attributes:
//  - Req
type TaskEditTaskArgs struct {
  Req *EditTaskRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewTaskEditTaskArgs() *TaskEditTaskArgs {
  return &TaskEditTaskArgs{}
}

var TaskEditTaskArgs_Req_DEFAULT *EditTaskRequest
func (p *TaskEditTaskArgs) GetReq() *EditTaskRequest {
  if !p.IsSetReq() {
    return TaskEditTaskArgs_Req_DEFAULT
  }
return p.Req
}
func (p *TaskEditTaskArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *TaskEditTaskArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskEditTaskArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &EditTaskRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *TaskEditTaskArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "EditTask_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskEditTaskArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *TaskEditTaskArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskEditTaskArgs(%+v)", *p)
}

// Attributes:
//  - Success
type TaskEditTaskResult struct {
  Success *EditTaskResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewTaskEditTaskResult() *TaskEditTaskResult {
  return &TaskEditTaskResult{}
}

var TaskEditTaskResult_Success_DEFAULT *EditTaskResponse
func (p *TaskEditTaskResult) GetSuccess() *EditTaskResponse {
  if !p.IsSetSuccess() {
    return TaskEditTaskResult_Success_DEFAULT
  }
return p.Success
}
func (p *TaskEditTaskResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *TaskEditTaskResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskEditTaskResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &EditTaskResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *TaskEditTaskResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "EditTask_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskEditTaskResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *TaskEditTaskResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskEditTaskResult(%+v)", *p)
}

// Attributes:
//  - Req
type TaskListTaskArgs struct {
  Req *ListTaskRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewTaskListTaskArgs() *TaskListTaskArgs {
  return &TaskListTaskArgs{}
}

var TaskListTaskArgs_Req_DEFAULT *ListTaskRequest
func (p *TaskListTaskArgs) GetReq() *ListTaskRequest {
  if !p.IsSetReq() {
    return TaskListTaskArgs_Req_DEFAULT
  }
return p.Req
}
func (p *TaskListTaskArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *TaskListTaskArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskListTaskArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &ListTaskRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *TaskListTaskArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ListTask_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskListTaskArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *TaskListTaskArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskListTaskArgs(%+v)", *p)
}

// Attributes:
//  - Success
type TaskListTaskResult struct {
  Success *ListTaskResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewTaskListTaskResult() *TaskListTaskResult {
  return &TaskListTaskResult{}
}

var TaskListTaskResult_Success_DEFAULT *ListTaskResponse
func (p *TaskListTaskResult) GetSuccess() *ListTaskResponse {
  if !p.IsSetSuccess() {
    return TaskListTaskResult_Success_DEFAULT
  }
return p.Success
}
func (p *TaskListTaskResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *TaskListTaskResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskListTaskResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &ListTaskResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *TaskListTaskResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "ListTask_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskListTaskResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *TaskListTaskResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskListTaskResult(%+v)", *p)
}

// Attributes:
//  - Req
type TaskGetLastArgs struct {
  Req *GetLastRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewTaskGetLastArgs() *TaskGetLastArgs {
  return &TaskGetLastArgs{}
}

var TaskGetLastArgs_Req_DEFAULT *GetLastRequest
func (p *TaskGetLastArgs) GetReq() *GetLastRequest {
  if !p.IsSetReq() {
    return TaskGetLastArgs_Req_DEFAULT
  }
return p.Req
}
func (p *TaskGetLastArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *TaskGetLastArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskGetLastArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &GetLastRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *TaskGetLastArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetLast_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskGetLastArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *TaskGetLastArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskGetLastArgs(%+v)", *p)
}

// Attributes:
//  - Success
type TaskGetLastResult struct {
  Success *GetLastResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewTaskGetLastResult() *TaskGetLastResult {
  return &TaskGetLastResult{}
}

var TaskGetLastResult_Success_DEFAULT *GetLastResponse
func (p *TaskGetLastResult) GetSuccess() *GetLastResponse {
  if !p.IsSetSuccess() {
    return TaskGetLastResult_Success_DEFAULT
  }
return p.Success
}
func (p *TaskGetLastResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *TaskGetLastResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskGetLastResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &GetLastResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *TaskGetLastResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetLast_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskGetLastResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *TaskGetLastResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskGetLastResult(%+v)", *p)
}

// Attributes:
//  - Req
type TaskTaskActionArgs struct {
  Req *TaskActionRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewTaskTaskActionArgs() *TaskTaskActionArgs {
  return &TaskTaskActionArgs{}
}

var TaskTaskActionArgs_Req_DEFAULT *TaskActionRequest
func (p *TaskTaskActionArgs) GetReq() *TaskActionRequest {
  if !p.IsSetReq() {
    return TaskTaskActionArgs_Req_DEFAULT
  }
return p.Req
}
func (p *TaskTaskActionArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *TaskTaskActionArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskTaskActionArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &TaskActionRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *TaskTaskActionArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "TaskAction_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskTaskActionArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *TaskTaskActionArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskTaskActionArgs(%+v)", *p)
}

// Attributes:
//  - Success
type TaskTaskActionResult struct {
  Success *TaskActionResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewTaskTaskActionResult() *TaskTaskActionResult {
  return &TaskTaskActionResult{}
}

var TaskTaskActionResult_Success_DEFAULT *TaskActionResponse
func (p *TaskTaskActionResult) GetSuccess() *TaskActionResponse {
  if !p.IsSetSuccess() {
    return TaskTaskActionResult_Success_DEFAULT
  }
return p.Success
}
func (p *TaskTaskActionResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *TaskTaskActionResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskTaskActionResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &TaskActionResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *TaskTaskActionResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "TaskAction_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskTaskActionResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *TaskTaskActionResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskTaskActionResult(%+v)", *p)
}

// Attributes:
//  - Req
type TaskTaskMvArgs struct {
  Req *TaskMvRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewTaskTaskMvArgs() *TaskTaskMvArgs {
  return &TaskTaskMvArgs{}
}

var TaskTaskMvArgs_Req_DEFAULT *TaskMvRequest
func (p *TaskTaskMvArgs) GetReq() *TaskMvRequest {
  if !p.IsSetReq() {
    return TaskTaskMvArgs_Req_DEFAULT
  }
return p.Req
}
func (p *TaskTaskMvArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *TaskTaskMvArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskTaskMvArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &TaskMvRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *TaskTaskMvArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "TaskMv_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskTaskMvArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *TaskTaskMvArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskTaskMvArgs(%+v)", *p)
}

// Attributes:
//  - Success
type TaskTaskMvResult struct {
  Success *TaskMvResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewTaskTaskMvResult() *TaskTaskMvResult {
  return &TaskTaskMvResult{}
}

var TaskTaskMvResult_Success_DEFAULT *TaskMvResponse
func (p *TaskTaskMvResult) GetSuccess() *TaskMvResponse {
  if !p.IsSetSuccess() {
    return TaskTaskMvResult_Success_DEFAULT
  }
return p.Success
}
func (p *TaskTaskMvResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *TaskTaskMvResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskTaskMvResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &TaskMvResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *TaskTaskMvResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "TaskMv_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskTaskMvResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *TaskTaskMvResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskTaskMvResult(%+v)", *p)
}

// Attributes:
//  - Req
type TaskGetTaskPageArgs struct {
  Req *GetTaskPageRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewTaskGetTaskPageArgs() *TaskGetTaskPageArgs {
  return &TaskGetTaskPageArgs{}
}

var TaskGetTaskPageArgs_Req_DEFAULT *GetTaskPageRequest
func (p *TaskGetTaskPageArgs) GetReq() *GetTaskPageRequest {
  if !p.IsSetReq() {
    return TaskGetTaskPageArgs_Req_DEFAULT
  }
return p.Req
}
func (p *TaskGetTaskPageArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *TaskGetTaskPageArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskGetTaskPageArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &GetTaskPageRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *TaskGetTaskPageArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetTaskPage_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskGetTaskPageArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *TaskGetTaskPageArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskGetTaskPageArgs(%+v)", *p)
}

// Attributes:
//  - Success
type TaskGetTaskPageResult struct {
  Success *GetTaskPageResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewTaskGetTaskPageResult() *TaskGetTaskPageResult {
  return &TaskGetTaskPageResult{}
}

var TaskGetTaskPageResult_Success_DEFAULT *GetTaskPageResponse
func (p *TaskGetTaskPageResult) GetSuccess() *GetTaskPageResponse {
  if !p.IsSetSuccess() {
    return TaskGetTaskPageResult_Success_DEFAULT
  }
return p.Success
}
func (p *TaskGetTaskPageResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *TaskGetTaskPageResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskGetTaskPageResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &GetTaskPageResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *TaskGetTaskPageResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "GetTaskPage_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskGetTaskPageResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *TaskGetTaskPageResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskGetTaskPageResult(%+v)", *p)
}

// Attributes:
//  - Req
type TaskPickTaskArgs struct {
  Req *PickTaskRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewTaskPickTaskArgs() *TaskPickTaskArgs {
  return &TaskPickTaskArgs{}
}

var TaskPickTaskArgs_Req_DEFAULT *PickTaskRequest
func (p *TaskPickTaskArgs) GetReq() *PickTaskRequest {
  if !p.IsSetReq() {
    return TaskPickTaskArgs_Req_DEFAULT
  }
return p.Req
}
func (p *TaskPickTaskArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *TaskPickTaskArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskPickTaskArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &PickTaskRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *TaskPickTaskArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "PickTask_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskPickTaskArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *TaskPickTaskArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskPickTaskArgs(%+v)", *p)
}

// Attributes:
//  - Success
type TaskPickTaskResult struct {
  Success *PickTaskResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewTaskPickTaskResult() *TaskPickTaskResult {
  return &TaskPickTaskResult{}
}

var TaskPickTaskResult_Success_DEFAULT *PickTaskResponse
func (p *TaskPickTaskResult) GetSuccess() *PickTaskResponse {
  if !p.IsSetSuccess() {
    return TaskPickTaskResult_Success_DEFAULT
  }
return p.Success
}
func (p *TaskPickTaskResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *TaskPickTaskResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskPickTaskResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &PickTaskResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *TaskPickTaskResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "PickTask_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskPickTaskResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *TaskPickTaskResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskPickTaskResult(%+v)", *p)
}

// Attributes:
//  - Req
type TaskPickDelArgs struct {
  Req *PickDelRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewTaskPickDelArgs() *TaskPickDelArgs {
  return &TaskPickDelArgs{}
}

var TaskPickDelArgs_Req_DEFAULT *PickDelRequest
func (p *TaskPickDelArgs) GetReq() *PickDelRequest {
  if !p.IsSetReq() {
    return TaskPickDelArgs_Req_DEFAULT
  }
return p.Req
}
func (p *TaskPickDelArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *TaskPickDelArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskPickDelArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &PickDelRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *TaskPickDelArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "PickDel_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskPickDelArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *TaskPickDelArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskPickDelArgs(%+v)", *p)
}

// Attributes:
//  - Success
type TaskPickDelResult struct {
  Success *PickDelResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewTaskPickDelResult() *TaskPickDelResult {
  return &TaskPickDelResult{}
}

var TaskPickDelResult_Success_DEFAULT *PickDelResponse
func (p *TaskPickDelResult) GetSuccess() *PickDelResponse {
  if !p.IsSetSuccess() {
    return TaskPickDelResult_Success_DEFAULT
  }
return p.Success
}
func (p *TaskPickDelResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *TaskPickDelResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskPickDelResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &PickDelResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *TaskPickDelResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "PickDel_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskPickDelResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *TaskPickDelResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskPickDelResult(%+v)", *p)
}

// Attributes:
//  - Req
type TaskSearchTaskArgs struct {
  Req *SearchTaskRequest `thrift:"req,1" db:"req" json:"req"`
}

func NewTaskSearchTaskArgs() *TaskSearchTaskArgs {
  return &TaskSearchTaskArgs{}
}

var TaskSearchTaskArgs_Req_DEFAULT *SearchTaskRequest
func (p *TaskSearchTaskArgs) GetReq() *SearchTaskRequest {
  if !p.IsSetReq() {
    return TaskSearchTaskArgs_Req_DEFAULT
  }
return p.Req
}
func (p *TaskSearchTaskArgs) IsSetReq() bool {
  return p.Req != nil
}

func (p *TaskSearchTaskArgs) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 1:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField1(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskSearchTaskArgs)  ReadField1(ctx context.Context, iprot thrift.TProtocol) error {
  p.Req = &SearchTaskRequest{}
  if err := p.Req.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Req), err)
  }
  return nil
}

func (p *TaskSearchTaskArgs) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "SearchTask_args"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField1(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskSearchTaskArgs) writeField1(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if err := oprot.WriteFieldBegin(ctx, "req", thrift.STRUCT, 1); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field begin error 1:req: ", p), err) }
  if err := p.Req.Write(ctx, oprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Req), err)
  }
  if err := oprot.WriteFieldEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write field end error 1:req: ", p), err) }
  return err
}

func (p *TaskSearchTaskArgs) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskSearchTaskArgs(%+v)", *p)
}

// Attributes:
//  - Success
type TaskSearchTaskResult struct {
  Success *SearchTaskResponse `thrift:"success,0" db:"success" json:"success,omitempty"`
}

func NewTaskSearchTaskResult() *TaskSearchTaskResult {
  return &TaskSearchTaskResult{}
}

var TaskSearchTaskResult_Success_DEFAULT *SearchTaskResponse
func (p *TaskSearchTaskResult) GetSuccess() *SearchTaskResponse {
  if !p.IsSetSuccess() {
    return TaskSearchTaskResult_Success_DEFAULT
  }
return p.Success
}
func (p *TaskSearchTaskResult) IsSetSuccess() bool {
  return p.Success != nil
}

func (p *TaskSearchTaskResult) Read(ctx context.Context, iprot thrift.TProtocol) error {
  if _, err := iprot.ReadStructBegin(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read error: ", p), err)
  }


  for {
    _, fieldTypeId, fieldId, err := iprot.ReadFieldBegin(ctx)
    if err != nil {
      return thrift.PrependError(fmt.Sprintf("%T field %d read error: ", p, fieldId), err)
    }
    if fieldTypeId == thrift.STOP { break; }
    switch fieldId {
    case 0:
      if fieldTypeId == thrift.STRUCT {
        if err := p.ReadField0(ctx, iprot); err != nil {
          return err
        }
      } else {
        if err := iprot.Skip(ctx, fieldTypeId); err != nil {
          return err
        }
      }
    default:
      if err := iprot.Skip(ctx, fieldTypeId); err != nil {
        return err
      }
    }
    if err := iprot.ReadFieldEnd(ctx); err != nil {
      return err
    }
  }
  if err := iprot.ReadStructEnd(ctx); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T read struct end error: ", p), err)
  }
  return nil
}

func (p *TaskSearchTaskResult)  ReadField0(ctx context.Context, iprot thrift.TProtocol) error {
  p.Success = &SearchTaskResponse{}
  if err := p.Success.Read(ctx, iprot); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T error reading struct: ", p.Success), err)
  }
  return nil
}

func (p *TaskSearchTaskResult) Write(ctx context.Context, oprot thrift.TProtocol) error {
  if err := oprot.WriteStructBegin(ctx, "SearchTask_result"); err != nil {
    return thrift.PrependError(fmt.Sprintf("%T write struct begin error: ", p), err) }
  if p != nil {
    if err := p.writeField0(ctx, oprot); err != nil { return err }
  }
  if err := oprot.WriteFieldStop(ctx); err != nil {
    return thrift.PrependError("write field stop error: ", err) }
  if err := oprot.WriteStructEnd(ctx); err != nil {
    return thrift.PrependError("write struct stop error: ", err) }
  return nil
}

func (p *TaskSearchTaskResult) writeField0(ctx context.Context, oprot thrift.TProtocol) (err error) {
  if p.IsSetSuccess() {
    if err := oprot.WriteFieldBegin(ctx, "success", thrift.STRUCT, 0); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field begin error 0:success: ", p), err) }
    if err := p.Success.Write(ctx, oprot); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T error writing struct: ", p.Success), err)
    }
    if err := oprot.WriteFieldEnd(ctx); err != nil {
      return thrift.PrependError(fmt.Sprintf("%T write field end error 0:success: ", p), err) }
  }
  return err
}

func (p *TaskSearchTaskResult) String() string {
  if p == nil {
    return "<nil>"
  }
  return fmt.Sprintf("TaskSearchTaskResult(%+v)", *p)
}


