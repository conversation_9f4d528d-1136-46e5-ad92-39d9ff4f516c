package favorites

import (
	"context"
	"net"
	"thrift-common/gen-go/favorites"

	"github.com/apache/thrift/lib/go/thrift"
	wErr "github.com/pkg/errors"
)

func Server(ctx context.Context, handler favorites.Favorites) error {
	HOST := "0.0.0.0"
	PORT := "18081"
	transportFactory := thrift.NewTBufferedTransportFactory(8192)
	protocolFactory := thrift.NewTBinaryProtocolFactoryDefault()
	serverTransport, err := thrift.NewTServerSocket(net.JoinHostPort(HOST, PORT))
	if err != nil {
		return wErr.Wrap(err, "")
	}

	processor := favorites.NewFavoritesProcessor(handler)
	server := thrift.NewTSimpleServer4(processor, serverTransport, transportFactory, protocolFactory)
	err = server.Serve()

	if err != nil {
		return wErr.Wrap(err, "running server error")
	}
	return nil
}
