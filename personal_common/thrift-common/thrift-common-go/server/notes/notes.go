package notes

import (
	"context"
	"net"
	"thrift-common/gen-go/notes"

	"github.com/apache/thrift/lib/go/thrift"
	wErr "github.com/pkg/errors"
)

func Server(ctx context.Context, handler notes.Notes) error {
	HOST := "0.0.0.0"
	PORT := "18083"
	transportFactory := thrift.NewTBufferedTransportFactory(8192)
	protocolFactory := thrift.NewTBinaryProtocolFactoryDefault()
	serverTransport, err := thrift.NewTServerSocket(net.JoinHostPort(HOST, PORT))
	if err != nil {
		return wErr.Wrap(err, "")
	}

	processor := notes.NewNotesProcessor(handler)
	server := thrift.NewTSimpleServer4(processor, serverTransport, transportFactory, protocolFactory)
	err = server.Serve()

	if err != nil {
		return wErr.Wrap(err, "running server error")
	}
	return nil
}
