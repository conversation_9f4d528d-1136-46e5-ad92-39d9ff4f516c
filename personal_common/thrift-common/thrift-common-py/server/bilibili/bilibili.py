
import importlib
bilibili = importlib.import_module("thrift-common.gen-py.bilibili.bilibili")

from thrift.transport import TSocket
from thrift.transport import TTransport
from thrift.protocol import TBinaryProtocol
from thrift.server import TServer


def Server(handler):
    processor = bilibili.Processor(handler)
    transport = TSocket.TServerSocket("0.0.0.0", "18080")
    tfactory = TTransport.TBufferedTransportFactory()
    pfactory = TBinaryProtocol.TBinaryProtocolFactory()

	# TSimpleServer请求多几次会卡住。。。
    # server = TServer.TSimpleServer(processor, transport, tfactory, pfactory)
    server = TServer.TThreadPoolServer(processor, transport, tfactory, pfactory)
    server.serve()
