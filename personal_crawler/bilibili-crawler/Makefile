
prepare:
	pip3 install numpy you-get bilix thrift pika -i https://mirrors.aliyun.com/pypi/simple/

run:
	PYTHONUNBUFFERED="1" ENV=dev python3 main.py

test:
	rm -rf $(shell pwd)/output/savepath
	mkdir -p $(shell pwd)/output/savepath
	# SAVE_PATH=$(shell pwd)/output/savepath python3 -m unittest -v mq/mq_test.py
	# SAVE_PATH=$(shell pwd)/output/savepath python3 -m unittest -vvv mq.mq_test.TestMqPullResourceFiles.testPullResourceFiles1
	SAVE_PATH=$(shell pwd)/output/savepath python3 -m unittest -vvv mq.mq_test.TestMqPullResourceFiles.testPullResourceFiles3
	# SAVE_PATH=$(shell pwd)/output/savepath python3 -m unittest -vvv mq.mq_test.TestMqCheckResourceFilesStatus.testCheckResourceFilesStatus

deploy:
	./scripts/deploy.sh

logs:
	tail -n 100 /data/docker-volumes/bilibili-crawler/runtime/bilibili-crawler-logs/stdout.log -n 100
	tail -n 100 /data/docker-volumes/bilibili-crawler/runtime/bilibili-crawler-logs/stderr.log -n 100
