import os

globalConfig = {}
def loadConfigs():
    if 'ENV' in os.environ:
        env = os.environ['ENV']
    else:
        env = "dev"

    if env == "deploy":
        globalConfig["StorePath"] = "/store_root"
        globalConfig["AmqpUrl"] = "amqp://admin:adminuser@rabbit-mq:5672/?heartbeat=0"
    else:
        globalConfig["StorePath"] = "/data/docker-volumes/public_root/file_resources/bilibili"
        globalConfig["AmqpUrl"] = "amqp://admin:adminuser@127.0.0.1:5672/?heartbeat=0"
