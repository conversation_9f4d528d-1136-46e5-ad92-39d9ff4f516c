FROM python:3.10
WORKDIR .
EXPOSE 9998
RUN chmod 1777 /tmp && cat /etc/resolv.conf && apt-get update && apt-get install -y nload ffmpeg
# 独立拷贝Makefile，把make prepare放到COPY . .之前，避免缓存层失效
COPY Makefile .
RUN make prepare
COPY . .
# tmp/来自deploy.sh的临时创建
COPY tmp/ /usr/local/lib/python3.10/site-packages/
CMD [ "/bin/sh", "-c", "python3 main.py >/runtime/bilibili-crawler-logs/stdout.log 2>/runtime/bilibili-crawler-logs/stderr.log" ]
# CMD [ "sleep", "3600" ]
