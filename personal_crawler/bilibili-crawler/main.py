import logging
# 默认是level是warning
logging.basicConfig(level=logging.INFO, format='%(levelname)s [%(asctime)s] [:%(lineno)s:%(funcName)s] %(message)s')
import importlib
bilibili = importlib.import_module("thrift-common.server.bilibili")
import service
from config import loadConfigs
from mq import initMq

loadConfigs()
initMq()

handler = service.BilibiliService()
logging.info("Server Started!")
bilibili.Server(handler)
