import os
import re
import time
import json
import logging
import traceback
import subprocess
import threading
import pika
import numpy as np
from subprocess import Popen, PIPE
from threading import Thread
from queue import Queue
from config import globalConfig
from .receive_msg import *

def initMq():
    connection = pika.BlockingConnection(
        pika.URLParameters(globalConfig["AmqpUrl"]))
    channel = connection.channel()
    channel.queue_declare(queue='favorites_pull_resource_bilibili_start')
    channel.queue_declare(queue='favorites_pull_resource_status')
    channel.basic_consume('favorites_pull_resource_bilibili_start', auto_ack=True, on_message_callback=ReceivePullResourceStartMsg)

    consume_thread = threading.Thread(target=start_consumer, args=(channel,))
    consume_thread.start()
    # channel.basic_publish(exchange='', routing_key='favorites_pull_resource_status', body='Hello World!')
    # logging.info(" [x] Sent 'Hello World!'")
    # connection.close()

def start_consumer(channel):
    channel.start_consuming()
