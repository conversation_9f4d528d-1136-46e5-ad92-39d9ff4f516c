import os
import json
import mock
from mock import call
import unittest
from unittest.async_case import IsolatedAsyncioTestCase
# import aiounittest
import mq

import logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s [%(asctime)s] [:%(lineno)s:%(funcName)s] %(message)s')

savePath=os.environ["SAVE_PATH"]
taskSource = mq.TaskSourceTypeUser

def checkPullMeta(self, channel, title, collectRecordID, argChannel, argStatusMsg, fileNum):
    self.assertEqual(argChannel, channel)
    self.assertEqual(argStatusMsg["StatusCode"], 101)
    self.assertEqual(argStatusMsg["StatusMessage"], "pull meta")
    self.assertEqual(argStatusMsg["CollectRecordID"], collectRecordID)
    self.assertEqual(argStatusMsg["Title"], title)
    self.assertEqual(argStatusMsg["FilesNum"], fileNum)
    self.assertEqual(argStatusMsg["FilesMissingIds"], "[]")
    argFilesStatus = json.loads(argStatusMsg["FilesStatus"])
    self.assertEqual(len(argFilesStatus), fileNum)
    for i in range(fileNum):
        self.assertEqual(argFilesStatus[i]["id"], "%s/%s"%(i+1, fileNum))
        self.assertIn("title", argFilesStatus[i])
        self.assertIn("format", argFilesStatus[i])
        self.assertIn("container", argFilesStatus[i])
        self.assertIn("quality", argFilesStatus[i])
        self.assertIn("size", argFilesStatus[i])
    argDuration = json.loads(argStatusMsg["DurationDetails"])
    self.assertIn("pull_meta", argDuration)

def checkDownload(self, channel, collectRecordID, argChannel, argStatusMsg, fileNum):
    self.assertEqual(argChannel, channel)
    self.assertEqual(argStatusMsg["StatusCode"], 102)
    self.assertEqual(argStatusMsg["StatusMessage"], "download files")
    self.assertEqual(argStatusMsg["CollectRecordID"], collectRecordID)
    argDuration = json.loads(argStatusMsg["DurationDetails"])
    for i in range(fileNum):
        self.assertIn("%s/%s_waiting"%(i+1, fileNum), argDuration)
        self.assertIn("%s/%s_finish"%(i+1, fileNum), argDuration)
    return argDuration


@mock.patch("mq.mq.SendPullResourceStatusMsg")
class TestMqPullResourceFiles(IsolatedAsyncioTestCase):
    async def testPullResourceFiles1(self, mockSendPullResourceStatusMsg):
        """单视频/无Extracting/有Merging"""
        # mockSendPullResourceStatusMsg.return_value = 'mocked'
        channel = "test_channel"
        collectRecordID = "1234"
        url = "https://www.bilibili.com/video/BV1NM411C7ix"
        files = await mq.pullResourceMetaInfo(channel, collectRecordID, url, savePath, taskSource)
        print("files: ", files)
        return
        mockSendPullResourceStatusMsg.assert_called_once()
        argChannel = mockSendPullResourceStatusMsg.call_args[0][0]
        argStatusMsg = mockSendPullResourceStatusMsg.call_args[0][1]
        title = "【小提琴】迎着日落在宿舍演奏起风了-“你的眼中，明暗交杂，一笑生花”"
        checkPullMeta(self, channel, title, collectRecordID, argChannel, argStatusMsg, 1)

        mq.pullResourceFiles(channel, collectRecordID, url, files, savePath)
        self.assertEqual(mockSendPullResourceStatusMsg.call_count, 2)
        argChannel = mockSendPullResourceStatusMsg.call_args[0][0]
        argStatusMsg = mockSendPullResourceStatusMsg.call_args[0][1]
        argDuration = checkDownload(self, channel, collectRecordID, argChannel, argStatusMsg, 1)
        self.assertIn("1/1_merge", argDuration)

    def testPullResourceFiles2(self, mockSendPullResourceStatusMsg):
        """单视频/无Extracting/Skiping"""
        # mockSendPullResourceStatusMsg.return_value = 'mocked'
        channel = "test_channel"
        collectRecordID = "1234"
        url = "https://www.bilibili.com/video/BV1NM411C7ix"
        files = mq.pullResourceMetaInfo(channel, collectRecordID, url, savePath, taskSource)
        mockSendPullResourceStatusMsg.assert_called_once()
        argChannel = mockSendPullResourceStatusMsg.call_args[0][0]
        argStatusMsg = mockSendPullResourceStatusMsg.call_args[0][1]
        title = "【小提琴】迎着日落在宿舍演奏起风了-“你的眼中，明暗交杂，一笑生花”"
        checkPullMeta(self, channel, title, collectRecordID, argChannel, argStatusMsg, 1)

        mq.pullResourceFiles(channel, collectRecordID, url, files, savePath)
        self.assertEqual(mockSendPullResourceStatusMsg.call_count, 2)
        argChannel = mockSendPullResourceStatusMsg.call_args[0][0]
        argStatusMsg = mockSendPullResourceStatusMsg.call_args[0][1]
        argDuration = checkDownload(self, channel, collectRecordID, argChannel, argStatusMsg, 1)
        self.assertIn("1/1_skip", argDuration)

    async def testPullResourceFiles3(self, mockSendPullResourceStatusMsg):
        """多视频/无Extracting/有Merging"""
        # mockSendPullResourceStatusMsg.return_value = 'mocked'
        channel = "test_channel"
        collectRecordID = "1234"
        # url = "https://www.bilibili.com/video/BV1Ge4y1K7Bf?p=3"
        # 短链接似乎无法通过p=xx直接拉取。api.get_video_info生成的pages里面的url是有问题的
        url = "https://b23.tv/M5ejGO8?p=3"
        fileNum = 6
        title, total, totalSize, files, missIds, statusCode, statusMessage, shouldConintue = await mq.fetchMetaInfo(url, savePath, taskSource)
        print("files: ", files)
        # argChannel = mockSendPullResourceStatusMsg.call_args[0][0]
        # argStatusMsg = mockSendPullResourceStatusMsg.call_args[0][1]
        # title = "【中国人民大学】《时代经济》主讲：翟东升（全6讲） (P"
        # checkPullMeta(self, channel, title, collectRecordID, argChannel, argStatusMsg, fileNum)

        # mq.pullResourceFiles(channel, collectRecordID, url, files, savePath)
        await mq.downloadFile(url, files, savePath)
        argChannel = mockSendPullResourceStatusMsg.call_args[0][0]
        argStatusMsg = mockSendPullResourceStatusMsg.call_args[0][1]
        argDuration = checkDownload(self, channel, collectRecordID, argChannel, argStatusMsg, fileNum)
        for i in range(fileNum):
            self.assertIn("%s/%s_merge"%(i+1, fileNum), argDuration)
            self.assertNotIn("%s/%s_skip"%(i+1, fileNum), argDuration)


    async def testPullResourceFiles3_all(self, mockSendPullResourceStatusMsg):
        """多视频/无Extracting/有Merging"""
        # mockSendPullResourceStatusMsg.return_value = 'mocked'
        channel = "test_channel"
        collectRecordID = "1234"
        url = "https://www.bilibili.com/video/BV1Ge4y1K7Bf?p=2"
        fileNum = 6
        title, total, totalSize, files, missIds, statusCode, statusMessage, shouldConintue = await mq.fetchMetaInfo(url, savePath, taskSource)
        print("files: ", files)
        mockSendPullResourceStatusMsg.assert_called_once()
        argChannel = mockSendPullResourceStatusMsg.call_args[0][0]
        argStatusMsg = mockSendPullResourceStatusMsg.call_args[0][1]
        title = "【中国人民大学】《时代经济》主讲：翟东升（全6讲） (P"
        checkPullMeta(self, channel, title, collectRecordID, argChannel, argStatusMsg, fileNum)

        mq.pullResourceFiles(channel, collectRecordID, url, files, savePath)
        argChannel = mockSendPullResourceStatusMsg.call_args[0][0]
        argStatusMsg = mockSendPullResourceStatusMsg.call_args[0][1]
        argDuration = checkDownload(self, channel, collectRecordID, argChannel, argStatusMsg, fileNum)
        for i in range(fileNum):
            self.assertIn("%s/%s_merge"%(i+1, fileNum), argDuration)
            self.assertNotIn("%s/%s_skip"%(i+1, fileNum), argDuration)

    def testPullResourceFiles4(self, mockSendPullResourceStatusMsg):
        """多视频/无Extracting/Skiping"""
        # mockSendPullResourceStatusMsg.return_value = 'mocked'
        channel = "test_channel"
        collectRecordID = "1234"
        url = "https://www.bilibili.com/video/BV1Ge4y1K7Bf"
        fileNum = 6
        files = mq.pullResourceMetaInfo(channel, collectRecordID, url, savePath, taskSource)
        mockSendPullResourceStatusMsg.assert_called_once()
        argChannel = mockSendPullResourceStatusMsg.call_args[0][0]
        argStatusMsg = mockSendPullResourceStatusMsg.call_args[0][1]
        title = "【中国人民大学】《时代经济》主讲：翟东升（全6讲） (P"
        checkPullMeta(self, channel, title, collectRecordID, argChannel, argStatusMsg, fileNum)

        mq.pullResourceFiles(channel, collectRecordID, url, files, savePath)
        self.assertEqual(mockSendPullResourceStatusMsg.call_count, 2)
        argChannel = mockSendPullResourceStatusMsg.call_args[0][0]
        argStatusMsg = mockSendPullResourceStatusMsg.call_args[0][1]
        argDuration = checkDownload(self, channel, collectRecordID, argChannel, argStatusMsg, fileNum)
        for i in range(fileNum):
            self.assertIn("%s/%s_skip"%(i+1, fileNum), argDuration)

    def testPullResourceFiles5(self, mockSendPullResourceStatusMsg):
        """多视频/无Extracting/无Merging"""
        # mockSendPullResourceStatusMsg.return_value = 'mocked'
        channel = "test_channel"
        collectRecordID = "1234"
        url = "https://www.bilibili.com/bangumi/play/ss39865"
        fileNum = 4
        files = mq.pullResourceMetaInfo(channel, collectRecordID, url, savePath, taskSource)
        mockSendPullResourceStatusMsg.assert_called_once()
        argChannel = mockSendPullResourceStatusMsg.call_args[0][0]
        argStatusMsg = mockSendPullResourceStatusMsg.call_args[0][1]
        title = "众神之地：第"
        checkPullMeta(self, channel, title, collectRecordID, argChannel, argStatusMsg, fileNum)

        mq.pullResourceFiles(channel, collectRecordID, url, files, savePath)
        self.assertEqual(mockSendPullResourceStatusMsg.call_count, 2)
        argChannel = mockSendPullResourceStatusMsg.call_args[0][0]
        argStatusMsg = mockSendPullResourceStatusMsg.call_args[0][1]
        argDuration = checkDownload(self, channel, collectRecordID, argChannel, argStatusMsg, fileNum)
        for i in range(fileNum):
            self.assertNotIn("%s/%s_merge"%(i+1, fileNum), argDuration)
            self.assertNotIn("%s/%s_skip"%(i+1, fileNum), argDuration)

    def testPullResourceFiles6(self, mockSendPullResourceStatusMsg):
        """多视频/无Extracting/无Merging/Skiping"""
        # mockSendPullResourceStatusMsg.return_value = 'mocked'
        channel = "test_channel"
        collectRecordID = "1234"
        url = "https://www.bilibili.com/bangumi/play/ss39865"
        fileNum = 4
        files = mq.pullResourceMetaInfo(channel, collectRecordID, url, savePath, taskSource)
        mockSendPullResourceStatusMsg.assert_called_once()
        argChannel = mockSendPullResourceStatusMsg.call_args[0][0]
        argStatusMsg = mockSendPullResourceStatusMsg.call_args[0][1]
        title = "众神之地：第"
        checkPullMeta(self, channel, title, collectRecordID, argChannel, argStatusMsg, fileNum)

        mq.pullResourceFiles(channel, collectRecordID, url, files, savePath)
        self.assertEqual(mockSendPullResourceStatusMsg.call_count, 2)
        argChannel = mockSendPullResourceStatusMsg.call_args[0][0]
        argStatusMsg = mockSendPullResourceStatusMsg.call_args[0][1]
        argDuration = checkDownload(self, channel, collectRecordID, argChannel, argStatusMsg, fileNum)
        for i in range(fileNum):
            self.assertIn("%s/%s_skip"%(i+1, fileNum), argDuration)


@mock.patch("mq.mq.SendPullResourceStatusMsg")
class TestMqCheckResourceFilesStatus(unittest.TestCase):
    def testCheckResourceFilesStatus(self, mockSendPullResourceStatusMsg):
        """视频文件不存在"""
        # mockSendPullResourceStatusMsg.return_value = 'mocked'
        channel = "test_channel"
        collectRecordID = "1234"
        files = []
        files.append({
            "id": "1/4",
            "title": "测试文件", 
            "format": "dash-flv480",
            "container": "mp4",
            "quality": "清晰 480P",
            "size": "6.6 MiB (6881953 bytes)"
        })
        savePath = "/"
        mq.checkResourceFilesStatus(channel, collectRecordID, files, savePath)
        self.assertEqual(mockSendPullResourceStatusMsg.call_count, 1)
        argChannel = mockSendPullResourceStatusMsg.call_args[0][0]
        argStatusMsg = mockSendPullResourceStatusMsg.call_args[0][1]
        files = json.loads(argStatusMsg["FilesStatus"])

        self.assertEqual(argStatusMsg["StatusCode"], 103)
        self.assertEqual(argStatusMsg["StatusMessage"], "partial")
        self.assertEqual(len(files), 1)

        f = files[0]
        self.assertEqual(f["realsize"], "")


if __name__ == '__main__':
    unittest.main(verbosity=2)
