import os
import re
import time
import json
import logging
import traceback
import subprocess
import threading
import asyncio
import pika
import numpy as np
from subprocess import Popen, PIPE
from threading import Thread
from queue import Queue
from config import globalConfig
from .step_pull_resource_meta_info import *
from .step_pull_resource_files import *
from .step_check_resource_files_status import *
from .send_msg import *

async def downloadTask(channel, method, properties, body):
    logging.info("receive msg %r" % body)
    req = json.loads(body.decode())

    CollectRecordID = req["CollectRecordID"]
    url = req["Url"]
    isMultiple = req["IsMultiple"]
    logging.info("SavePath: %r" % req["SavePath"])
    if req["SavePath"] != "" and req["SavePath"][0] == '/':
        req["SavePath"] = req["SavePath"][1:]

    savePath = os.path.join(globalConfig["StorePath"], req["SavePath"])
    logging.info("savePath: %s", savePath)
    os.makedirs(savePath, exist_ok=True)
    start = time.time()

    try:
        files, shouldConintue = await pullResourceMetaInfo(channel, CollectRecordID, url, savePath, req["TaskSource"])
        if shouldConintue:
            await pullResourceFiles(channel, CollectRecordID, url, files, savePath, req["TaskSource"])
            checkResourceFilesStatus(channel, CollectRecordID, files, savePath, req["TaskSource"])
        # you_get.any_download(url, output_dir=savePath, merge=True)

        # not working
        # you_get.main(input_file=url, output_dir=savePath)

        # not working
        # you_get.download_main(
        #     you_get.any_download, you_get.any_download_playlist, url, None
        # )

        # subprocess.run(["you-get", "--playlist", url, "-o", savePath])
    except Exception as err:
        duration=int(1000*(time.time()-start))
        # logging.error("Catch error: %s", err)
        logging.error("Catch error: %s", traceback.format_exc())
        res = {"CollectRecordID": CollectRecordID, "StatusCode": 500, "StatusMessage": str(err), "Duration": duration}
        SendPullResourceStatusMsg(channel, res, req["TaskSource"])
        return

    duration=int(1000*(time.time()-start))
    res = {"CollectRecordID": CollectRecordID, "StatusCode": 200, "StatusMessage": "success", "Duration": duration}
    # SendPullResourceStatusMsg(channel, res, req["TaskSource"])
    return

def ReceivePullResourceStartMsg(channel, method, properties, body):
    # loop = asyncio.get_event_loop()
    # loop.create_task(downloadTask(channel, method, properties, body))
    asyncio.run(downloadTask(channel, method, properties, body))
