import os
import re
import time
import json
import logging
import traceback
import subprocess
import threading
import pika
import numpy as np
from subprocess import Popen, PIPE
from threading import Thread
from queue import Queue
from config import globalConfig
from .send_msg import *

def findHighQuality(savePath, files):
    maxSize = 0
    highQualityFile = files[0]
    for f in files:
        cmd = "du -s '%s' | cut -f1" % (os.path.join(savePath, f))
        logging.info("exec cmd[%s]" % (cmd))
        p = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, bufsize=0, text=True, shell=True)
        size = int(p.communicate()[0].strip())
        logging.info("file[%s] size[%r]" % (f, size))
        if size > maxSize:
            maxSize = size
            highQualityFile = f

    for f in files:
        if f != highQualityFile:
            cmd = "mkdir -p %s/low_quality && mv '%s' %s/low_quality" % (savePath, os.path.join(savePath, f), savePath)
            subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, bufsize=0, text=True, shell=True)

    return highQualityFile

def convertTitle2Name(title):
	# 获得的title信息，到通过find正则匹配查找文件名时，需要做的转化。比如文件title包含[5]，在正则命令行种要转成\[5\]才行
    return title.replace("'", "*").replace("/", "-").replace("[", "\[").replace("]", "\]")[0:64]

def checkResourceFilesStatus(channel, collectRecordID, files, savePath, taskSource):
    allFinished = True
    status = "success"
    statusCode = 105
    logging.info("checkResourceFilesStatus files: %r", files)
    for f in files:
        logging.info("range f: %r", f)
        # cmd = "ls -al | grep \"%s\" | grep -v cmt | awk '{printf $5}'" % f['title']
        # output = p.communicate()[0]
        # logging.info("file[%s] output[%r]" % (f['title'], output))

        # 这种写法可以避免ls带来的末尾\n，先提取文件名
        # NOTE 这里有个bug。。如果路径名也命中了正则匹配，那么出来的结果是多个拼接到一起。。就会导致拼接的filename有问题。这里暂时加上-type f来限制file类型。但是如果file超过一个，也会导致拼接的地方有bug，这里是需要额外判断find命令查出来的item项是不是只有一个的。
        # 原命令: find '/data/docker-volumes/public_root/file_resources/bilibili/' -maxdepth 1 ! -name '*cmt.xml' -name '重锤老板良心（四）*' -type f -printf '%f'
        catFilename = convertTitle2Name(f['title'])
        logging.info("catFilename: %r", catFilename)
        cmd = "find %s -maxdepth 1 ! -name '*cmt.xml' ! -name '*.download' -name '*%s*' -type f -printf '%%f'" % (savePath, catFilename)
        logging.info("exec cmd[%s]" % (cmd))
        p = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, bufsize=0, text=True, shell=True)
        filename = p.communicate()[0]
        logging.info("file[%s] filename[%r]" % (f['title'], filename))
        if filename == '':
            # 没找到文件
            allFinished = False
            status = "partial"
            statusCode = 103
            continue
        # 需要二次判断该文件是否真的存在，否则有可能是find出来多个记录拼接出来有问题的文件
        # 1. 判断文件数是否超过1个
        cmd = "find %s -maxdepth 1 ! -name '*cmt.xml' ! -name '*.download'  -name '*%s*' -type f -printf '%%f\n'" % (savePath, catFilename)
        logging.info("exec cmd[%s]" % (cmd))
        p = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, bufsize=0, text=True, shell=True)
        filenamesMulti = p.communicate()[0].strip().split("\n")
        count = len(filenamesMulti)
        logging.error("file[%s] files[%r]" % (f['title'], filenamesMulti))
        logging.error("file[%s] count[%r]" % (f['title'], count))
        if int(count) == 0:
            # 没找到文件
            allFinished = False
            status = "partial"
            continue

        if int(count) > 1:
            # 多个文件，一般存在flv和mp4等
            # 找一个size最大的
            filename = findHighQuality(savePath, filenamesMulti)
            logging.info("findHighQuality[%r]" % filename)

        # 2. TODO 判断文件是否存在
        f['filename'] = filename

		# 文件名不能用单引号括起来，否则空格会导致截断报错
        # cmd = "file -b --mime-type '%s'" % (os.path.join(savePath, filename))
        cmd = "file -b --mime-type \"%s\"" % (os.path.join(savePath, filename))
        logging.info("exec cmd[%s]" % (cmd))
        p = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, bufsize=0, text=True, shell=True)
        filetype = p.communicate()[0].strip()
        logging.info("file[%s] filetype[%r]" % (filename, filetype))
        f['filetype'] = filetype.strip()

        cmd = "du -sb \"%s\" | awk '{printf $1}'" % os.path.join(savePath, filename)
        logging.info("exec cmd[%s]" % (cmd))
        p = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, bufsize=0, text=True, shell=True)
        realsize = p.communicate()[0]
        logging.info("file[%s] realsize[%r]" % (filename, realsize))
        f['realsize'] = realsize

        cmd = "sha256sum \"%s\" | awk '{printf $1}'" % os.path.join(savePath, filename)
        logging.info("exec cmd[%s]" % (cmd))
        p = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, bufsize=0, text=True, shell=True)
        sha256 = p.communicate()[0].strip()
        logging.info("file[%s] sha256[%r]" % (filename, sha256))
        f['sha256'] = sha256
        f['sync_status'] = "102"


    statusMsg = {
        "StatusCode": statusCode,
        "StatusMessage": status,
        "CollectRecordID": collectRecordID,
        "FilesStatus": json.dumps(files, ensure_ascii=False), # 没有ensure_ascii=False会导致title中文被转成\uxxxx
    }
    SendPullResourceStatusMsg(channel, statusMsg, taskSource)


if __name__ == "__main__":
    title = convertTitle2Name("'dfdf's/sfdsf")
    print("title: ", title)
