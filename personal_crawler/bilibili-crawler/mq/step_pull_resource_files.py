import os
import re
import time
import json
import logging
import traceback
import subprocess
import threading
import pika
import numpy as np
from subprocess import Popen, PIPE
from threading import Thread
from queue import Queue
from config import globalConfig
from .send_msg import *
from bilix.sites.bilibili import DownloaderBilibili
from bilix.sites.bilibili import api

# TODO 没用。。。一样一大堆http日志
logging.getLogger("requests").setLevel(logging.WARNING)
logging.getLogger("urllib3").setLevel(logging.WARNING)

## 为了让代码单测性更加良好，这里就不应该在you-get/bilix下载视频的方法里，又SendPullResourceStatusMsg发送mq消息。所以需要细分方法代码

async def downloadFile(url, files, savePath):
    # reHeader = r'you-get:\sExtracting\s(\d+)\sof\s(\d+)\svideos'
    reHeader = r'title\s*(.*)\s*'
    reSkip = r'Skipping(.*)file\salready\sexists'
    reDownload = r'Merging\svideo\sparts'
    reMerge = r'Merged\sinto\s(.*)'
    reFinish = r'Downloading\s(.*)cmt.xml'

    duration = {}
    start = time.time()
    last = start

    # 前置检验savepath的写权限
    check = os.access(savePath, os.W_OK)
    if not check:
        raise ValueError("savePath no permission")

    async with DownloaderBilibili() as d:
        # res = await d.get_series(url)
        logging.info("files: %r", files)
        for p in files:
            logging.info("get_video p: %r", p)
            await d.get_video(p['url'], savePath)
            duration["%s_finish"%p['id']] = int(1000*(time.time()-last))
            last = time.time()

    logging.info("duration:")
    logging.info(duration)
    logging.info("duration: %r", duration)
    return duration


async def pullResourceFiles(channel, collectRecordID, url, files, savePath, taskSource):
    duration = await downloadFile(url, files, savePath)
    statusCode = 102 # cloudserver下载完成，未转储；
    if taskSource == 3:
        statusCode = 203 # 直接来自801记录下载到localserver。
    statusMsg = {
        "StatusCode": statusCode,
        "StatusMessage": "download files",
        "CollectRecordID": collectRecordID,
        "DurationDetails": json.dumps(duration)
    }
    SendPullResourceStatusMsg(channel, statusMsg, taskSource)

if __name__ == "__main__":
    duration = downloadFile("https://www.bilibili.com/video/BV1Dt42187BZ", files)
    print("duration: ", duration)
