import os
import re
import time
import json
import logging
import traceback
import subprocess
import threading
import pika
import numpy as np
from subprocess import Popen, PIPE
from threading import Thread
from queue import Queue
from config import globalConfig
from .send_msg import *

## 为了让代码单测性更加良好，这里就不应该在you-get/bilix下载视频的方法里，又SendPullResourceStatusMsg发送mq消息。所以需要细分方法代码

def downloadFile():
	# reHeader = r'you-get:\sExtracting\s(\d+)\sof\s(\d+)\svideos'
    reHeader = r'title\s*(.*)\s*'
    reSkip = r'Skipping(.*)file\salready\sexists'
    reDownload = r'Merging\svideo\sparts'
    reMerge = r'Merged\sinto\s(.*)'
    reFinish = r'Downloading\s(.*)cmt.xml'

    duration = {}
    start = time.time()

    # 前置检验savepath的写权限
    check = os.access(savePath, os.W_OK)
    if not check:
        raise ValueError("savePath no permission")


    # 这里用-i而不用--json来解析，是因为--json更麻烦，不是严格意义返回json字符串，一大堆换行符、空格之类的。还有颜色控制符。。
    p = subprocess.Popen(["you-get", "--playlist", url, "-o", savePath], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, bufsize=0, text=True, close_fds=True)
    # 用cur_id来记录还是不能保证下述匹配的顺序。可能cur_id=2的reHeader先出来，然后cur_id=1的reFinish再出来，所以还是得正则提取title，来匹配具体的id
    # 这样的话，也不能简单的线性time.time()-start来计算间距了，各个id都得线性保存。
    # cur_id = ""
    simCurId = "" # 只能针对一些Merging之类的不包含title的字符串，做一些比较模糊的cur_id记录
    f2idMap = {}
    for f in files:
        f2idMap[f["title"]] = f["id"]

    def parseID(simTitle):
        matchTitle = ""
        for title in f2idMap:
            if title in simTitle:
                matchTitle = title
        if matchTitle == "":
            logging.info("simTitle[%s] simCurId[%s]" % (simTitle, simCurId))
            return simCurId
        return f2idMap[matchTitle]

    for line in iter(p.stdout.readline, b''):
        if line == '':
            break
        # logging.info("line %s" % line)

        # Merging video parts...经常跟下一个视频的Extracting x of xx在同一行出现。。所以这里放到reHeader前，并且不加continue
        # 又踩坑。。。Merging除了跟下一视频的Extracting相连，还可能跟自身视频的Merged into相连。总之就是没有换行，不卡这个点了
        # output = re.findall(reDownload, line)
        # if len(output) > 0:
        #     logging.info("output[%s] line[%s]" % (output, line))
        #     title = output[0]
        #     cur_id = parseID(title)
        #     logging.info("title[%s] cur_id[%s]" % (title, cur_id))

        #     last = duration["%s_waiting"%cur_id]
        #     duration["%s_download"%cur_id] = int(1000*(time.time()-start)-last)
        #     logging.info("duration: %r" % duration)
        #     continue

        output = re.findall(reHeader, line)
        if len(output) > 0:
            logging.info("output[%s] line[%s]" % (output, line))
            # cur_id="%s/%s"%output[0]
            cur_id=parseID(output[0])
            logging.info("cur_id: %s" % cur_id)

            duration["%s_waiting"%cur_id] = int(1000*(time.time()-start))
            simCurId = cur_id
            logging.info("duration: %r" % duration)
            continue

        output = re.findall(reSkip, line)
        if len(output) > 0:
            logging.info("output[%s] line[%s]" % (output, line))
            title = output[0]
            cur_id = parseID(title)
            logging.info("title[%s] cur_id[%s]" % (title, cur_id))

            if "%s_waiting"%cur_id in duration:
                # 系列视频时，针对当前视频识别reHeader记录的waiting至今的时间
                last = duration["%s_waiting"%cur_id]
            else:
                # 单个视频
                last = 0
            duration["%s_skip"%cur_id] = int(1000*(time.time()-start)-last)
            logging.info("duration: %r" % duration)
            continue

        output = re.findall(reMerge, line)
        if len(output) > 0:
            logging.info("output[%s] line[%s]" % (output, line))
            title = output[0]
            cur_id = parseID(title)
            logging.info("title[%s] cur_id[%s]" % (title, cur_id))

            if "%s_waiting"%cur_id in duration:
                # 系列视频时，针对上个视频的waiting
                last = duration["%s_waiting"%cur_id]
            else:
                # 单个视频
                last = 0
            duration["%s_merge"%cur_id] = int(1000*(time.time()-start)-last)
            logging.info("duration: %r" % duration)
            continue


        output = re.findall(reFinish, line)
        if len(output) > 0:
            logging.info("output[%s] line[%s]" % (output, line))
            title = output[0]
            cur_id = parseID(title)
            logging.info("title[%s] cur_id[%s]" % (title, cur_id))

            if "%s_merge"%cur_id in duration:
                # 系列视频，上个视频merge完的节点
                last = duration["%s_merge"%cur_id]
            elif "%s_skip"%cur_id in duration:
                # 系列视频，上个视频存在且skip的节点
                last = duration["%s_skip"%cur_id]
            else:
                # 单个视频
                last = 0
            duration["%s_finish"%cur_id] = int(1000*(time.time()-start)-last)
            logging.info("duration: %r" % duration)
            continue

    p.stdout.close()
    p.kill()
    p.wait()

    logging.info("duration:")
    logging.info(duration)
    logging.info("duration: %r", duration)
	return duration


def pullResourceFiles(channel, collectRecordID, url, files, savePath, taskSource):
    duration = downloadFile(url, files)
    statusMsg = {
        "StatusCode": 102,
        "StatusMessage": "download files",
        "CollectRecordID": collectRecordID,
        "DurationDetails": json.dumps(duration)
    }
    SendPullResourceStatusMsg(channel, statusMsg, taskSource)

# 踩了一堆垃圾python的stderr flush的坑。
# def pullResourceFiles(channel, collectRecordID, url):
#     start = time.time()
#     # 这里用-i而不用--json来解析，是因为--json更麻烦，不是严格意义返回json字符串，一大堆换行符、空格之类的。还有颜色控制符。。
#     # command = "you-get --playlist " + url
#     # cmd = subprocess.Popen(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE, bufsize=0, text=True, shell=True)
#     cmd = subprocess.Popen(["you-get", "--playlist", url], stdout=subprocess.PIPE, stderr=subprocess.PIPE, bufsize=0, text=True)
#     # https://stackoverflow.com/questions/1822237/asynchronously-read-stdout-from-subprocess-popen
#     # for line in iter(cmd.stdout.readline, ""):
#     #     logging.info("stdout : %r", line.rstrip("\n"))
# 
#     # https://stackoverflow.com/questions/31833897/python-read-from-subprocess-stdout-and-stderr-separately-while-preserving-order
#     def reader(pipe, queue):
#         logging.info("pipe %s" % pipe)
#         # while True:
#         #     time.sleep(0.1)
#         #     pipe.flush()
#         #     line = pipe.readline()
#         #     logging.info("pipe %s: %s" % (pipe, line))
# 
#         try:
#             with pipe:
#                 pipe.flush()
#                 for line in iter(pipe.readline, b''):
#                     if line != '':
#                         # logging.info("%s: %s" %(pipe, line))
#                         queue.put((pipe, line))
#         finally:
#             queue.put(None)
#     
#     q = Queue()
#     Thread(target=reader, args=[cmd.stderr, q]).start()
#     Thread(target=reader, args=[cmd.stdout, q]).start()
# #     for _ in range(2):
# #         for source, line in iter(q.get, None):
# #             logging.info("%s: %s" % (source, line))
# # 

