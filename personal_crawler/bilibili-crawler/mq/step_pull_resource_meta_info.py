import os
import re
import time
import json
import logging
import traceback
import subprocess
import threading
import pika
import numpy as np
from subprocess import Popen, PIPE
from threading import Thread
from queue import Queue
from config import globalConfig
from .send_msg import *
from os import path
from bilix.sites.bilibili import DownloaderBilibili
from bilix.sites.bilibili import api

def escape_ansi(line):
    # 参考https://stackoverflow.com/questions/14693701/how-can-i-remove-the-ansi-escape-sequences-from-a-string-in-python
    ansi_escape = re.compile(r'(?:\x1B[@-_]|[\x80-\x9F])[0-?]*[ -/]*[@-~]')
    return ansi_escape.sub('', line)

def getCommonStr(strList):
    common = strList[0]
    for i in range(1, len(strList)):
        common = os.path.commonprefix([common, strList[i]])
    return common

def getFreeSpaceSize(path):
    """获取文件路径的剩余空间字节数"""
    result=os.statvfs(path)
    block_size=result.f_frsize
    total_blocks=result.f_blocks
    free_blocks=result.f_bfree
    # giga=1024*1024*1024
    total_size=total_blocks*block_size
    free_size=free_blocks*block_size
    print('total_size = %s' % total_size)
    print('free_size = %s' % free_size)
    return free_size

TaskSourceTypeUser       = 1 # 用户出发下载任务
TaskSourceTypeBackground = 2 # 定时任务
TaskSourceTypeDelayFetch = 3 # 来自delayFetch离线任务

# 是否延迟拉取的逻辑
def checkShouldContinue(total, totalSize, savePath, taskSource):
    # 目前bilix暂时无法获取totalSize，保险期间全部lazy down
    return True

    # DEBUG
    # return False
    # 超过10个也延迟拉取？还是只以size为准
    # if total > 10
    #     return False

    # 超过512MB就延迟拉取（如果是delayFetch，不受512MB限制）
    if totalSize > 512*1024*1024 and taskSource != TaskSourceTypeDelayFetch:
        return False

    # 剩余空间不足，也要lazy down
    freeSize = getFreeSpaceSize(savePath)
    if totalSize > freeSize:
        return False

    return True

async def fetchMetaInfo(url, savePath, taskSource):
    logging.info("pullResourceFiles start pull... %r", url)
    title = "" # 总标题
    total = 0 # 文件数
    totalSize = 0 # 所有文件总size，bilix目前似乎没看到怎么获取的方式，先跳过置0
    files = [] # 单个文件详细信息
    missIds = [] # 缺失的文件id

    async with DownloaderBilibili() as d:
        # 短链接似乎无法通过p=xx直接拉取。api.get_video_info生成的pages里面的url是有问题的。要先解析成长链接
        # 也无法通过get_series获取全部视频，只能拉到第一个
        res = await api.get_video_info(d.client, url)
        title = res.title
        total = len(res.pages)
        logging.info("title: %r", res.title)
        logging.info("aid: %r", res.aid)  # article id或者av id，即视频id
        logging.info("cid: %r", res.cid)  # page id，即分页id
        logging.info("ep_id: %r", res.ep_id) # 指番剧/电影/电视剧/纪录片id 
        logging.info("p: %r", res.p)
        logging.info("desc: %r", res.desc)
        logging.info("tags: %r", res.tags)
        logging.info("other: %r", res.other)
        logging.info("bvid: %r", res.bvid)  # 专栏bv号
        logging.info("status: %r", res.status)
        logging.info("img_url: %r", res.img_url)
        logging.info("dash.duration: %r", res.dash.duration)

        # 这里dash.videos的长度跟视频个数没有关系，视频个数在pages里
        # i = 0
        #for f in res.dash.videos:
        #    logging.info("i = %r", i)
        #    logging.info("dash.videos: %r", res.dash.videos[i])
        #    i += 1

        i = 0
        logging.info("pages.len: %r", len(res.pages))
        for d in res.pages:
            logging.info("i = %r", i)
            logging.info("pages: %r", d)
            files.append({
                "id": "%s/%s"%(i+1, total),
				"url": d.p_url,
                "title": title if d.p_name == "" else d.p_name,
                "format": '',
                "container": '',
                "quality": '',
                "size": '0',
                "sync_status": "101",
                "sync_unit_type": "1",
            })
            i += 1
        logging.info("files: %r", files)

    logging.info("files: %r", files)

    statusCode = 101
    statusMessage = "pull meta"
    shouldConintue = checkShouldContinue(total, totalSize, savePath, taskSource)
    if not shouldConintue:
        statusCode = 104
        statusMessage = "lazy down"
        for i in range(total):
            logging.info("files[i] %r", files[i])
            logging.info("files[i]sync %r", files[i]["sync_status"])
            files[i]["sync_status"] = "801"
    return title, total, totalSize, files, missIds, statusCode, statusMessage, shouldConintue

    

# 先拉取文件数、文件title等信息
async def pullResourceMetaInfo(channel, collectRecordID, url, savePath, taskSource):
    start = time.time()

    title, total, totalSize, files, missIds, statusCode, statusMessage, shouldConintue = await fetchMetaInfo(url, savePath, taskSource)

    duration=int(1000*(time.time()-start))
    logging.info("type %r", type(duration))
    logging.info("files: %r", files)
    statusMsg = {
        "StatusCode": statusCode,
        "StatusMessage": statusMessage,
        "ShouldConintue": shouldConintue,
        "CollectRecordID": collectRecordID,
        "Title": title,
        "FilesNum": total,
        "TotalSize": totalSize,
        "FilesStatus": json.dumps(files, ensure_ascii=False), # 没有ensure_ascii=False会导致title中文被转成\uxxxx
        "FilesMissingIds": json.dumps(missIds),
        "DurationDetails": json.dumps({"pull_meta": duration})
    }
    SendPullResourceStatusMsg(channel, statusMsg, taskSource)
    return files, shouldConintue
