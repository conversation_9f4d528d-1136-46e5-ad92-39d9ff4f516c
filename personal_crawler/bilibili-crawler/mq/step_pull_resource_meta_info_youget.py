import os
import re
import time
import json
import logging
import traceback
import subprocess
import threading
import pika
import numpy as np
from subprocess import Popen, PIPE
from threading import Thread
from queue import Queue
from config import globalConfig
from .send_msg import *
from os import path

def escape_ansi(line):
    # 参考https://stackoverflow.com/questions/14693701/how-can-i-remove-the-ansi-escape-sequences-from-a-string-in-python
    ansi_escape = re.compile(r'(?:\x1B[@-_]|[\x80-\x9F])[0-?]*[ -/]*[@-~]')
    return ansi_escape.sub('', line)

def getCommonStr(strList):
    common = strList[0]
    for i in range(1, len(strList)):
        common = os.path.commonprefix([common, strList[i]])
    return common

def getFreeSpaceSize(path):
	"""获取文件路径的剩余空间字节数"""
	result=os.statvfs(path)
	block_size=result.f_frsize
	total_blocks=result.f_blocks
	free_blocks=result.f_bfree
	# giga=1024*1024*1024
	total_size=total_blocks*block_size
	free_size=free_blocks*block_size
	print('total_size = %s' % total_size)
	print('free_size = %s' % free_size)
	return free_size

TaskSourceTypeUser       = 1 # 用户出发下载任务
TaskSourceTypeBackground = 2 # 定时任务
TaskSourceTypeDelayFetch = 3 # 来自delayFetch离线任务

# 是否延迟拉取的逻辑
def checkShouldContinue(total, totalSize, savePath, taskSource):
    # DEBUG
    # return False
    # 超过10个也延迟拉取？还是只以size为准
    # if total > 10
    #     return False

    # 超过512MB就延迟拉取（如果是delayFetch，不受512MB限制）
    if totalSize > 512*1024*1024 and taskSource != TaskSourceTypeDelayFetch:
        return False

    # 剩余空间不足，也要lazy down
    freeSize = getFreeSpaceSize(savePath)
    if totalSize > freeSize:
        return False

    return True


# 先拉取文件数、文件title等信息
def pullResourceMetaInfo(channel, collectRecordID, url, savePath, taskSource):
    start = time.time()
    # 这里用-i而不用--json来解析，是因为--json更麻烦，不是严格意义返回json字符串，一大堆换行符、空格之类的。还有颜色控制符。。
    logging.info("pullResourceFiles start pull... %r", url)
    result = subprocess.run(["you-get", "--playlist", "-i", url], stdout=subprocess.PIPE, stderr=subprocess.STDOUT, bufsize=0, text=True)
    logging.info("stdout type: %r", type(result.stdout))
    stdout = result.stdout
    logging.info("stdout: %s", stdout)

    reHeader = r'you-get:\sExtracting\s(\d+)\sof\s(\d+)\svideos'
    reTitle = r'[\s\S]*?title:\s*(.*?)\n[\s\S]*?'
    # 这里只拿到第一个最高质量的streams
    reStreams = r'format:\s*?(\S*?)\s*?container:\s*?(\S*)\s*?quality:\s*(.*?)\n\s*size:\s*(.*?)\n'
    # 这里stdout和stderr不会按顺序，而是错位的。stderr会接在stdout之后，导致reHeader要独立解析
    # 这里设置了export PYTHONUNBUFFERED="1"之后，stdout和stderr就不错位了
    output = re.findall(reTitle + reStreams, stdout)
    logging.info("output: %r", output)

    files = []
    missIds = []
    # if len(output) > 0:
    # 先提取重复部分的title
    titles = np.array(output)[:,0]
    logging.info("titles: %r", titles)
    title = getCommonStr(titles)
    logging.info("title: %r", title)
    reSize = r'(\d+)\sbytes'

    # 有系列视频的才有Extracting 1 of 4 videos之类的解析模式
    total = len(output)
    totalSize = 0
    for i in range(total):
        f = output[i]
        logging.info("f: %r", f)
        sizeOut = re.findall(reSize, f[4])
        size = sizeOut[0]
        logging.info("size: %r sizeOut: %r", size, sizeOut)

        totalSize += int(size)
        files.append({
            "id": "%s/%s"%(i+1, total),
            "title": f[0],
            "format": escape_ansi(f[1]),
            "container": f[2],
            "quality": f[3],
            "size": size,
            "sync_status": "101",
            "sync_unit_type": "1",
        })
    logging.info("files: %r", files)

    #     # 这里不通过Extracting直接根据title之后不需要missingIds的检测了，因为本身就根据检测数量来作为total了
    #     # output = re.findall(reHeader, stdout)
    #     # total = len(output)
    #     # logging.info("total: %r, files: %r", total, len(files))
    #     # if total != len(files):
    #     #     missIds = [*range(1, total+1)]
    #     #     logging.info("missing: %r", missIds)
    #     #     for i in output:
    #     #         cur = int(i[0])
    #     #         logging.info("cur: %r", cur)
    #     #         missIds.remove(cur)
    #     #     logging.error("missing: %r", missIds)
    # else:
    #     # 表示单个视频
    #     output = re.findall(reTitle + reStreams, stdout)
    #     logging.info("single output: %r", output)
    #     f = output[0]
    #     total = 1
    #     files.append({
    #         "id": "1/1",
    #         "title": f[0],
    #         "format": escape_ansi(f[1]),
    #         "container": f[2],
    #         "quality": f[3],
    #         "size": f[4]
    #     })

    statusCode = 101
    statusMessage = "pull meta"
    shouldConintue = checkShouldContinue(total, totalSize, savePath, taskSource)
    if not shouldConintue:
        statusCode = 104
        statusMessage = "lazy down"
        for i in range(total):
            logging.info("files[i] %r", files[i])
            logging.info("files[i]sync %r", files[i]["sync_status"])
            files[i]["sync_status"] = "801"

    duration=int(1000*(time.time()-start))
    logging.info("type %r", type(duration))
    logging.info("files: %r", files)
    statusMsg = {
        "StatusCode": statusCode,
        "StatusMessage": statusMessage,
        "ShouldConintue": shouldConintue,
        "CollectRecordID": collectRecordID,
        "Title": title,
        "FilesNum": total,
        "TotalSize": totalSize,
        "FilesStatus": json.dumps(files, ensure_ascii=False), # 没有ensure_ascii=False会导致title中文被转成\uxxxx
        "FilesMissingIds": json.dumps(missIds),
        "DurationDetails": json.dumps({"pull_meta": duration})
    }
    SendPullResourceStatusMsg(channel, statusMsg, taskSource)
    return files, shouldConintue

