#!/bin/bash
cur_dir=$(dirname $(readlink -f $0))
root_dir=$(dirname ${cur_dir})
if [[ ! -f ${root_dir}/.version ]]; then
  echo "Wrong Root Directory"
  exit
fi

# 先拉取最新代码（避免每次.version冲突）
# git pull

source ${root_dir}/config/config.ini
version_file=${root_dir}/.version
image_version=$(UpdateVersionPatch ${version_file})

image_name=bilibili-crawler:${image_version}
# mkdir -p tmp && chmod l777 tmp
mkdir -p tmp && chmod 777 tmp
# 可能是/usr/local/lib/python3.7/dist-packages，也可能是python3.8，使用python_lib_path替代
python_lib_path=$(python3 -c "import pip; print(pip.__path__[0].rstrip('/pip'))")
if [[ ! -d ${python_lib_path}/thrift-common ]]; then
  echo "找不到${python_lib_path}/thrift-common，可能重新安装过python，需要去personal_common/thrift-common仓库执行make gen"
  exit
fi

cp -R ${python_lib_path}/thrift-common/ tmp/

docker build -t ${image_name} -f ${root_dir}/image/Dockerfile ${root_dir}/
docker image ls -a | grep bilibili-crawler
rm -rf tmp/

path=/data/docker-volumes/${container_name}
if [[ -d ${path} ]]; then
  echo "Already Exist Path: ${path}"
else
  mkdir -p ${path}
fi

docker container rm -f ${container_name}
docker run -d  --restart unless-stopped --net admin-net -e ENV='deploy' \
  --name=${container_name} \
  -v ${path}/runtime/bilibili-crawler-logs:/runtime/bilibili-crawler-logs \
  -v ${store_root}:/store_root \
  ${image_name}
# docker container ls -a

# docker exec -it ${container_name} bash

# deploy用root权限执行，会报错
# git add .version
# git commit -m "update version"
# git push
