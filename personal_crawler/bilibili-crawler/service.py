import os
import time
import logging
import traceback
import subprocess
from you_get import common as you_get
from config import globalConfig
import importlib
ttypes = importlib.import_module("thrift-common.gen-py.bilibili.ttypes")
base_ttypes = importlib.import_module("thrift-common.gen-py.base.ttypes")

class BilibiliService:
    def showCurrentTimestamp(self):
        timestamp = time.time()
        logging.info("timestamp: %s", timestamp)
        return str(timestamp)

    # 参考: https://pypi.org/project/you-get/0.3.8/
    def PullVideo(self, req):
        # 改成mq模式
        pass
    #     url = req.Url
    #     isMultiple = req.IsMultiple
    #     if req.SavePath[0] == '/':
    #         req.SavePath = req.SavePath[1:]
    #     savePath = os.path.join(globalConfig["StorePath"], req.SavePath)
    #     logging.info("savePath: %s", savePath)
    #     os.makedirs(savePath, exist_ok=True)
    #     start = time.time()
    #     try:
    #         logging.info("call pullVideo, req: %s", req)
    #         # you_get.any_download(url, output_dir=savePath, merge=True)

    #         # not working
    #         # you_get.main(input_file=url, output_dir=savePath)

    #         # not working
    #         # you_get.download_main(
    #         #     you_get.any_download, you_get.any_download_playlist, url, None
    #         # )

    #         subprocess.run(["you-get", "--playlist", url, "-o", savePath])
    #     except Exception as err:
    #         # logging.error("Catch error: %s", err)
    #         logging.error("Catch error: %s", traceback.format_exc())
    #         return ttypes.PullVideoResponse(
    #             duration=int(1000*(time.time()-start)),
    #             BaseResp=base_ttypes.BaseResponse(500, str(err))
    #         )

    #     res = ttypes.PullVideoResponse(
    #         duration=int(1000*(time.time()-start)),
    #         BaseResp=base_ttypes.BaseResponse(200, "success")
    #     )
    #     logging.info("call pullVideo, res: %s", res)
    #   return res
