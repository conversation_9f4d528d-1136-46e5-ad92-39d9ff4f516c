import os
import re
import time
import json
import logging
import traceback
import subprocess
import threading
import pika

stdout= """
you-get: Extracting 3 of 4 videos ...
site:                Bilibili
title:               众神之地：第3集 寻找故土
streams:             # Available quality and codecs
    [ DASH ] ____________________________________
    - format:        dash-flv
      container:     mp4
      quality:       高清 1080P
      size:          786.4 MiB (824596472 bytes)
    # download-with: you-get --format=dash-flv [URL]

    - format:        dash-flv720
      container:     mp4
      quality:       高清 720P
      size:          455.2 MiB (477264128 bytes)
    # download-with: you-get --format=dash-flv720 [URL]

    - format:        dash-flv480
      container:     mp4
      quality:       清晰 480P
      size:          308.2 MiB (323119463 bytes)
    # download-with: you-get --format=dash-flv480 [URL]

    - format:        dash-flv360
      container:     mp4
      quality:       流畅 360P
      size:          159.9 MiB (167644081 bytes)
    # download-with: you-get --format=dash-flv360 [URL]


you-get: Extracting 4 of 4 videos ...
site:                Bilibili
title:               众神之地：第4集 山神归来
streams:             # Available quality and codecs
    [ DEFAULT ] _________________________________
    - format:        flv
      container:     flv
      quality:       高清 1080P
      size:          124.9 MiB (130993743 bytes)
    # download-with: you-get --format=flv [URL]

    - format:        flv720
      container:     flv
      quality:       高清 720P
      size:          88.1 MiB (92370191 bytes)
    # download-with: you-get --format=flv720 [URL]

    - format:        flv480
      container:     flv
      quality:       清晰 480P
      size:          39.3 MiB (41191830 bytes)
    # download-with: you-get --format=flv480 [URL]

    - format:        flv360
      container:     flv
      quality:       流畅 360P
      size:          18.0 MiB (18839672 bytes)
    # download-with: you-get --format=flv360 [URL]
"""
reHeader = r'you-get:\sExtracting\s(\d+)\sof\s(\d+)\svideos'
reTitle = r'[\s\S]*?title:\s*(.*?)\n[\s\S]*?'
# reStreams = r'format:\s*?(\S*?)\s*?container:\s*?(\S*)\s*?quality:\s*(.*?)\n\s*size:\s*(.*?)\n'
reStreams = r'format:\s*?(\S*?)\s*?container:\s*?(\S*)\s*?quality:\s*(.*?)\n\s*size:\s*(.*?)\n'
output = re.findall(reHeader + reTitle + reStreams, stdout)
print("output: ", output)
