SHELL = /bin/bash

prepare:
	# sudo apt-get install -y pkg-config default-libmysqlclient-dev libssl-dev liblzma-dev
	# cd src/nodejs && npm i -S -E bluebird mysql2 request-promise sequelize commander wcwidth echarts
	source /data/deploy/treeenv/bin/activate && \
	cd src/python && pip install -r requirements.txt

dailycrawler:
	# node src/nodejs/index.js dailycrawler -d -D 2022-11-21 688298
	# node src/nodejs/index.js dailycrawler -dd 300750
	node src/nodejs/index.js dailycrawler -dd 600651
	node src/nodejs/index.js dailycrawler -dd 600652
	node src/nodejs/index.js dailycrawler -dd 600653
	node src/nodejs/index.js dailycrawler -dd 600654
	node src/nodejs/index.js dailycrawler -dd 600655
	node src/nodejs/index.js dailycrawler -dd 600656
	node src/nodejs/index.js dailycrawler -dd 600601
	node src/nodejs/index.js dailycrawler -dd 600602
	node src/nodejs/index.js dailycrawler -dd 600603
	node src/nodejs/index.js dailycrawler -dd 600604
	node src/nodejs/index.js dailycrawler -dd 600605
	node src/nodejs/index.js dailycrawler -dd 600606

info_stock:
	https_proxy=http://127.0.0.1:38899 http_proxy=http://127.0.0.1:38899 node src/nodejs/index.js info -dd --type=stock
	# node src/nodejs/index.js info -dd --type=stock

cron_daily:
	# https_proxy=http://127.0.0.1:8899 http_proxy=http://127.0.0.1:8899 node src/nodejs/index.js cron_daily -dd --type=stock
	node src/nodejs/index.js cron_daily -dd --type=stock

info_index:
	node src/nodejs/index.js info -d --type=index

stat_total:
	node src/nodejs/index.js stat -dd -t stock

stat_stocks:
	node src/nodejs/index.js stat -dd -t stock -c 600656
	node src/nodejs/index.js stat -dd -t stock -c 600601
	node src/nodejs/index.js stat -dd -t stock -c 600602
	node src/nodejs/index.js stat -dd -t stock -c 600651
	node src/nodejs/index.js stat -dd -t stock -c 600652
	node src/nodejs/index.js stat -dd -t stock -c 600653
	node src/nodejs/index.js stat -dd -t stock -c 600654
	node src/nodejs/index.js stat -dd -t stock -c 600655

stat_single:
	node src/nodejs/index.js stat -dd -t stock -c 600656
	# node src/nodejs/index.js stat -dd -t stock -c 300750

stat_index_399808:
	node src/nodejs/index.js stat -dd -t index -c 399808

stat_index_000985:
	node src/nodejs/index.js stat -dd -t index -c 000985

stat_index_000001:
	# 上证综指
	node src/nodejs/index.js stat -dd -t index -c 000001

stat_index_000016:
	# 上证50
	node src/nodejs/index.js stat -dd -t index -c 000016

# download_pankou: info_stock
# download_pankou:
# 	# node src/nodejs/index.js dump -t stock -f output/stock_list.txt
# 	# https_proxy=http://127.0.0.1:8899 http_proxy=http://127.0.0.1:8899 ./scripts/download_pankou.sh output/stock_list.txt output/pankou_data
# 	./scripts/download_pankou.sh output/stock_list.txt output/pankou_data
# 
# download_fenshi:
# 	./scripts/download_fenshi.sh output/stock_list.txt output/fenshi_data

# Makefile中无法继承父进程（bash）的环境变量，无法使用pyac alias，需要显示source ~/.bashrc，参考: https://yuanbao.tencent.com/chat/naQivTmsDa/77a8e4c2-0706-4daa-a70d-5da856e88e5d
# 要么自己先pyac进入环境，要么直接显示source
download_pankou:
	# export https_proxy=http://127.0.0.1:8899 http_proxy=http://127.0.0.1:8899 && 
	source /data/deploy/treeenv/bin/activate && \
	source conf/env && \
	python src/python/download_pankou.py --concurrency=10 /data2/stock-crawler-data/pankou_data
	# python src/python/download_pankou.py output/pankou_data --max-line=100

download_fenshi:
	# export https_proxy=http://127.0.0.1:8899 http_proxy=http://127.0.0.1:8899 && 
	source /data/deploy/treeenv/bin/activate && \
	source conf/env && \
	python src/python/download_fenshi.py --concurrency=10 /data2/stock-crawler-data/fenshi_data
	# python src/python/download_fenshi.py output/fenshi_data --max-line=100

decompress_fenshi:
	source /data/deploy/treeenv/bin/activate && \
	source conf/env && \
	python src/python/decompress.py --type=fenshi /data2/stock-crawler-data/decompress_fenshi

decompress_pankou:
	source /data/deploy/treeenv/bin/activate && \
	source conf/env && \
	python src/python/decompress.py --type=pankou /data2/stock-crawler-data/decompress_pankou

debug:
	# source /data/deploy/treeenv/bin/activate && \
	# python3 /data/projects/personal_tool/crack-eastmoney-dat/fenshi/fetch_fenshi2.py 300466 20240603 output/test2.dat
	# python3 /data/projects/personal_tool/crack-eastmoney-dat/fenshi/decompress_detect.py output/test.dat -v --max-offset=64
	# dd bs=1 skip=25 if=output/test.dat of=output/test.shift.dat
	# python3 /data/projects/personal_tool/crack-eastmoney-dat/decompress.py output/test.shift.dat output/test.plain.dat
	python3 /data/projects/personal_tool/crack-eastmoney-dat/fenshi/decompress_detect.py output/test.plain.dat -v --max-offset=1024

migrate:
	source /data/deploy/treeenv/bin/activate && \
	source conf/env && \
	python src/python/migrate_mysql_clickhouse.py
	# oproxy && pip install clickhouse_driver && cproxy && \ 
	# python src/python/migrate_mysql_clickhouse.py
