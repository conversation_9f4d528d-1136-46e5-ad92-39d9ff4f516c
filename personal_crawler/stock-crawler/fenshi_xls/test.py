import tushare as ts
# ts.set_token('2a5154aab951e29b2e52dd795f3e42763596ef1f0f4097de17cdd046')

# data = ts.get_hist_data('600848', ktype='5') #一次性获取全部日k线数据
# pro = ts.pro_api()
# df = pro.daily(ts_code='000001.SZ', start_date='20180701', end_date='20180718')
#多个股票
# df = pro.daily(ts_code='000001.SZ,600000.SH', start_date='20180701', end_date='20180718')
# df = ts.get_tick_data('301010',date='2022-06-09', src='tt')
# 
# print(df)

df = ts.get_stock_basics()
date = df.ix['600848']['timeToMarket'] #上市日期YYYYMMDD
