#!/bin/bash

# 分时数据下载函数（请根据实际需求补充具体逻辑）
download_fenshi() {
    local stock_code=$1
    local date=$2
	local output_dir=$3
    if [[ ! -d ${output_dir} ]]; then
        mkdir -p ${output_dir}
    fi
    local dat_file="$output_dir/${stock_code}.${date}.dat"
    # 示例调用: 您的下载逻辑
    
    if [[ -f ${dat_file} ]]; then
        echo "  Skip $stock_code.$date.dat ..."
        return
    fi

    echo "  Download $stock_code.$date.dat Start..."
    # TODO 调用python脚本下载TCP dat数据
    local code=$(echo "$stock_code" | grep -oE '[0-9]{6}$')
    python3 /data/projects/personal_tool/crack-eastmoney-dat/fenshi/fetch_fenshi.py ${code} ${date} ${dat_file}
	
    echo "  Download $stock_code.$date.dat Finish."
}


# 检查参数数量
if [ $# -ne 2 ]; then
    echo "错误：需要2个参数"
    echo "用法：$0 <输入文件> <输出目录>"
    exit 1
fi

input_file=$1
output_dir=$2

# 创建输出目录（如果不存在）
mkdir -p "$output_dir" || {
    echo "错误：无法创建输出目录 $output_dir"
    exit 1
}

# 检查文件是否存在
if [ ! -f "$input_file" ]; then
    echo "错误：文件 $input_file 不存在"
    exit 1
fi

# 逐行读取文件
while IFS= read -r line || [[ -n "$line" ]]; do
    # 跳过空行和注释行
    if [[ -z "$line" || "$line" == \#* ]]; then
        continue
    fi

    # 分割股票代码和日期列表
    IFS=':' read -r stock_code dates_str <<< "$line"

    # 临时fix 错误格式的stock code
    if [[ "$stock_code" =~ "null" ]]; then
        echo "Skip Code: ${stock_code}"
        continue
    fi

    # 分割日期字符串为数组
    IFS=',' read -ra dates <<< "$dates_str"

    for date in "${dates[@]}"; do
        # 通过索引获取日期，并去除可能存在的空格
        # date="${dates[i]}"          # 修复关键错误：添加 $ 符号
        date=$(echo "$date" | xargs)
        echo "date: ${date}"

        # 调用下载函数
        if [[ -n "$stock_code" && -n "$date" ]]; then
            download_fenshi "$stock_code" "$date" "${output_dir}/${stock_code}"
            # sleep 1
        else
            echo "警告: 无效数据行 - 股票代码: '$stock_code', 日期: '$date'"
        fi
    done
done < "$input_file"

echo "处理完成"
