#!/bin/bash

warnings.filterwarnings("ignore", category=InsecureRequestWarning)
# 盘口数据下载函数（请根据实际需求补充具体逻辑）
download_pankou() {
    local stock_code=$1
    local date=$2
	local output_dir=$3
    if [[ ! -d ${output_dir} ]]; then
        mkdir -p ${output_dir}
    fi
    local dat_file="$output_dir/${stock_code}.${date}.dat"
    # 示例调用: 您的下载逻辑
    
    if [[ -f ${dat_file} ]]; then
        echo "  Skip $stock_code.$date.dat ..."
        return
    fi

    echo "  Download $stock_code.$date.dat Start..."
    # 这里可以添加curl/wget请求或其他下载逻辑
	curl -ksSf -o ${dat_file} -X GET "https://applookback.eastmoney.com/free/pankou_detail_new/1/${date}/${stock_code}.dat" \
		-H "host: applookback.eastmoney.com" \
		-H "em-gt: ACAA8AB554CC4799B454B7C98165CBc8" \
		-H "em-hqdelay: " \
		-H "em-ct: " \
		-H "accept: */*" \
		-H "em-ver: 10.12.5" \
		-H "em-ut: " \
		-H "em-pa: 1" \
		-H "em-sl: 0" \
		-H "accept-language: zh-Hans-CN;q=1" \
		-H "em-os: iOS" \
		-H "user-agent: rls.com.eastmoney.ipad/10.12.5 (iPad; iOS 18.3.1; Scale/2.00)" \
		-H "em-pkg: com.eastmoney.ipad" \
		-H "em-hqshiel: " \
		-H "em-md: MTBCNDc1QTItMENCMC00ODkyLUJBMDAtRTQ0NEU5NzM5QTM2" \
		-H "em-ab: test_1L9;"
    echo "  Download $stock_code.$date.dat Finish."
}


# 检查参数数量
if [ $# -ne 2 ]; then
    echo "错误：需要2个参数"
    echo "用法：$0 <输入文件> <输出目录>"
    exit 1
fi

input_file=$1
output_dir=$2

# 创建输出目录（如果不存在）
mkdir -p "$output_dir" || {
    echo "错误：无法创建输出目录 $output_dir"
    exit 1
}

# 检查文件是否存在
if [ ! -f "$input_file" ]; then
    echo "错误：文件 $input_file 不存在"
    exit 1
fi

# 逐行读取文件
while IFS= read -r line || [[ -n "$line" ]]; do
    # 跳过空行和注释行
    if [[ -z "$line" || "$line" == \#* ]]; then
        continue
    fi

    # 分割股票代码和日期列表
    IFS=':' read -r stock_code dates_str <<< "$line"

    # 临时fix 错误格式的stock code
    if [[ "$stock_code" =~ "null" ]]; then
        echo "Skip Code: ${stock_code}"
        continue
    fi

    # 分割日期字符串为数组
    IFS=',' read -ra dates <<< "$dates_str"

    # 逆序遍历所有日期
    for date in "${dates[@]}"; do
    # for ((i = ${#dates[@]} - 1; i >= 0; i--)); do
        # 通过索引获取日期，并去除可能存在的空格
        # date="${dates[i]}"          # 修复关键错误：添加 $ 符号
        date=$(echo "$date" | xargs)
        echo "date: ${date}"

		# 检查日期是否早于或等于20180914，大概率404
		if [[ $date -le 20180914 ]]; then
		    echo "跳过日期：$date"
		    continue
		fi

        # 调用下载函数
        if [[ -n "$stock_code" && -n "$date" ]]; then
            download_pankou "$stock_code" "$date" "${output_dir}/${stock_code}"
            # sleep 1
        else
            echo "警告: 无效数据行 - 股票代码: '$stock_code', 日期: '$date'"
        fi
    done
done < "$input_file"

echo "处理完成"
