#!/bin/bash

# 检查参数是否存在
if [ $# -ne 1 ]; then
    echo "用法: $0 <目标目录>"
    exit 1
fi

target_dir="$1"

# 检查目标目录是否存在
if [ ! -d "$target_dir" ]; then
    echo "错误：目录不存在 '$target_dir'"
    exit 1
fi

# 遍历目标目录下的SH/SZ前缀子目录
cd "$target_dir" || exit 1

# 使用数组避免通配符展开问题
dirs=( SH* SZ* )

for old_dir in "${dirs[@]}"; do
    # 跳过非目录和非匹配项
    if [ ! -d "$old_dir" ]; then
        continue
    fi

    # 生成新目录名（去掉前2个字符）
    new_dir="${old_dir:2}"

    # 重命名目录
    echo "重命名目录: $old_dir -> $new_dir"
    mv -v -- "$old_dir" "$new_dir"

    # 处理目录内文件
    if cd "$new_dir"; then
        # 查找所有以原目录名开头的文件
        files=( "${old_dir}"* )
        for old_file in "${files[@]}"; do
            # 跳过不匹配的glob结果和非文件项
            [ -e "$old_file" ] || continue
            [ -f "$old_file" ] || continue

            # 生成新文件名
            new_file="${new_dir}${old_file#$old_dir}"

            # 重命名文件
            echo "重命名文件: $old_file -> $new_file"
            mv -v -- "$old_file" "$new_file"
        done
        cd ..
    else
        echo "错误：无法进入目录 $new_dir"
        exit 1
    fi
done

echo "操作完成"
