
const { Init } = require('../init.js');
const CrawlerOutput = require('../display/crawler_output.js');
const service = require('../service');

async function runStepCheckMeta(output, code) {
  let type = await service.GetCodeMetaFromDB(output, code);
  if (type != consts.CODE_TYPE_UNKNOWN) {
    // 本地db获取到
    output.setHasMeta(true);
    output.setType(type);
    return;
  }

  output.setHasMeta(false);
  type = await service.CrawlerCodeMetaFromThirdClient(output, code);
  if (type != consts.CODE_TYPE_UNKNOWN) {
    // 本地没有，第三方拉取meta获取到
    output.setType(type);
    return;
  }

  output.setType(consts.CODE_TYPE_UNKNOWN);
}

async function runStepCheckDailyData(output, code, specifyDate) {
  let type = output.getType();
  await service.CrawlerCodeDaily(output, code, type, specifyDate);
}

async function RunDailyCrawler(code, isDebug, specifyDate) {
  let output = new CrawlerOutput(isDebug);
  output.setCode(code);

  await Init(output);
  await runStepCheckMeta(output, code);
  await runStepCheckDailyData(output, code, specifyDate);

  output.print();
}

module.exports = {
  RunDailyCrawler
}
