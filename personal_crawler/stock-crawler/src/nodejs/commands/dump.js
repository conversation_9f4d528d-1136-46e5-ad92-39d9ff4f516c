const { Init } = require('../init.js');
const BaseOutput = require('../display/base.js');
const service = require('../service');
const fs = require('fs').promises;

async function RunDump(isDebug, type, dumpFile) {
  let output = new BaseOutput(isDebug);
  // output.setType(type);
  await Init(output);

  await fs.writeFile(dumpFile, '');

  let stocks = await service.GetAllStocksFromDB(output);
  for (let s of stocks) {
	let code = s.stockCode;
    // 每个stock写入一行
	let line = `${s.stockCity}${code}:`;
    let dates = await service.GetAllDatesFromDB(output, code);
	// date从最新日期开始
    dates = dates.map(d => d.replaceAll('-', '')).reverse();
    line += dates.join(',');
    await fs.appendFile(dumpFile, line + '\n');
  }

  output.print();
}

module.exports = {
  RunDump,
}
