const { Init } = require('../init.js');
const InfoOutput = require('../display/info_output.js');
const service = require('../service');

async function runStepCheckMeta(output, type) {
  output.debug(`runStepCheckMeta type=${type}`)
  if (type == consts.CODE_TYPE_STOCK) {
    await service.CrawlerAllStockMetaFromThirdClient(output);
  } else if (type == consts.CODE_TYPE_INDEX) {
    await service.CrawlerAllIndexFromThirdClient(output);
  }
}

async function runStepCheckDailyData(output, type) {
  if (type == consts.CODE_TYPE_STOCK) {
    await service.CrawlerAllStockDailyFromThirdClient(output);
  } else if (type == consts.CODE_TYPE_INDEX) {
    await service.CrawlerAllIndexDailyFromThirdClient(output);
  }
}

async function runStepCheckComponentAdjustment(output, type) {
  if (type == consts.CODE_TYPE_STOCK) {
    // do nothing
  } else if (type == consts.CODE_TYPE_INDEX) {
    await service.CrawlerAllIndexComponentAdjustmentFromThirdClient(output);
  }
}

async function runStepCheckFundamentalData(output, type) {
  if (type == consts.CODE_TYPE_STOCK) {
    await service.CrawlerAllStockFundamentalFromThirdClient(output);
  } // else do nothing
}

async function runStepCheckHistoryValuations(output, type) {
  if (type == consts.CODE_TYPE_STOCK) {
    await service.CrawlerAllStockValuationsFromThirdClient(output);
  } // else do nothing
}

async function runStepCheckIPORecords(output, type) {
  if (type == consts.CODE_TYPE_STOCK) {
    await service.CrawlerAllIPOSPORecordsFromThirdClient(output);
  } // else do nothing
}

async function RunInfo(isDebug, type) {
  let output = new InfoOutput(isDebug);
  output.setType(type);
  await Init(output);

  try {
    // 拉取股票/指数/基金元数据
    await runStepCheckMeta(output, type);
    // 拉取股票/指数/基金日K数据
    await runStepCheckDailyData(output, type);
    // 拉取股票基本面数据
    await runStepCheckFundamentalData(output, type);
    // 拉取指数/基金成分股变动记录
    await runStepCheckComponentAdjustment(output, type);
    await service.CrawlerSingleIndexComponentAdjustmentFromThirdClient(output, '000001');
    // 拉取股票历史估值
    await runStepCheckHistoryValuations(output, type);
    // 拉取股票历史IPO/SPO记录
    await runStepCheckIPORecords(output, type);
    output.SetTaskSuccess();
  } catch (error) {
    output.error(`RunInfo encountered an error`, error);
    output.SetTaskFailed(error.message);
  } finally {
    output.print();
    output.notify("即时爬取任务");
  }
}

async function RunCronDaily(isDebug, type) {
  let output = new InfoOutput(isDebug);
  output.setType(type);
  await Init(output);

  try {
    // 拉取股票/指数/基金元数据：为了更新新上市的stocks
    await runStepCheckMeta(output, type);
    // 拉取股票/指数/基金日K数据
    await runStepCheckDailyData(output, type);
    output.SetTaskSuccess();
  } catch (error) {
    output.error(`RunCronDaily encountered an error: `, error);
    output.SetTaskFailed(error.message);
  } finally {
    output.print();
    output.notify("每日拉取任务");
  }
}

module.exports = {
  RunInfo,
  RunCronDaily,
}
