
const { Init } = require('../init.js');
const StatIndexOutput = require('../display/stat_index_output.js');
const StatStockOutput = require('../display/stat_stock_output.js');
const service = require('../service');

async function RunStatSingleIndex(output, code) {
  let index = await service.GetIndexFromDB(output, code);
  if (!index) {
    throw new Error(`db不存在的indexCode: ${code}`);
  }
  output.setIndexInfo(index);

  // step1: 补全股票基础数据
  // await service.CrawlerAllStockFromThirdClient(output);

  // step2: 补全股票的大事提醒（名称变动/ST、新股上市）
  // await service.CrawlerAllStockCompanyEventsFromThirdClient(output);

  // step3: 补全单个index的sina/sse成份股记录
  // await service.CrawlerSingleIndexComponentAdjustmentFromThirdClient(output, code);

  let stocks = await service.GetAllStocksFromDB();
  output.setAllStocks(stocks);

  // step1: 补全所有股票日K数据

  // step: 验证日K数据
  await service.CheckSingleIndexDataEntry(output, code)
}

async function RunStatSingleStock(output, code) {
  let stock= await service.GetStockFromDB(output, code);
  if (!stock) {
    throw new Error(`db不存在的stockCode: ${code}`);
  }
  output.setStockInfo(stock);

  // // step1: 补全IPO记录
  // await service.CrawlerSingleStockIPORecordsFromThirdClient(output, code);

  // // - 补全SPO
  // await service.CrawlerAllSPORecordsFromThirdClient(output);

  // 补全历史股本变动
  // await service.CrawlerSingleStockSharesHistoryFromThirdClient(output, code);

  // // step2: 补全限售解禁
  // await service.CrawlerSingleStockUnlimitedFromThirdClient(output, code);

  // // step3: 补全质押记录
  // await service.CrawlerSingleStockPledgesFromThirdClient(output, code);

  // // step4: 补全分红送转
  // await service.CrawlerSingleStockBonusFromThirdClient(output, code);

  // // step3: 补全公告记录
  // await service.CrawlerSingleStockNoticesFromThirdClient(output, code);

  // // step4: 补全每日估值数据
  await service.CrawlerSingleStockValuationsFromThirdClient(output, code);


  // step: 验证日K数据
  // await runStepCheckIndexComponents(output, code)
  await service.CheckSingleStockDataEntry(output, code);
}

async function RunStatSingle(code, isDebug, type) {
  if (type == consts.CODE_TYPE_INDEX) {
    let output = new StatIndexOutput(isDebug);
    output.setType(type);
    await Init(output);
    await RunStatSingleIndex(output, code)
    output.print();

  } else if (type == consts.CODE_TYPE_STOCK) {
    let output = new StatStockOutput(isDebug);
    output.setType(type);
    await Init(output);
    await RunStatSingleStock(output, code)
    output.print();
  } else {
    console.log("Do Nothing");
  }

}

module.exports = {
  RunStatSingle
}
