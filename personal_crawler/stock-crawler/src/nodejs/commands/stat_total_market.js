const { Init } = require('../init.js');
const StatTotalOutput = require('../display/stat_total_output.js');
const service = require('../service');
const {CheckSingleStockDataEntry} = require('../service/stock/check_data.js');

async function statOneDay() {
  
}

async function runStepStatMarketCap(output, type) {
  if (type == consts.CODE_TYPE_STOCK) {
    // await service.CrawlerAllStockFromThirdClient(output);
    // await service.CrawlerAllStockDailyFromThirdClient(output);
    //
    // let ret = await service.GetExistDailyInfoFromDB();
    // output.setDailyCount(ret[0]);
    // output.setDailyDateRange(ret[1], ret[2]);

    // await service.CrawlerMarketValuationFromThirdClient(output);
    // await service.CrawlerIPORecordFromThirdClient(output, '300750');

    await CheckSingleStockDataEntry(output, '300750');

    // await service.StatChart(output);

    // // 按日期维度，进行K线数据统计，进行校验。但是日K数据没有股票份额
    // let startDate = new Date(ret[1]);
    // let endDate = new Date(ret[2]);
    // for (let date = startDate; date <= endDate; date.setDate(date.getDate() + 1)) {
    //   await service.
    //   output.debug2(date);
    // }
  }
}

async function RunStatTotalMarket(isDebug, type) {
  let output = new StatTotalOutput(isDebug);
  output.setType(type);

  await Init(output);
  // 统计总市值
  await runStepStatMarketCap(output, type);

  output.print();
}

module.exports = {
  RunStatTotalMarket
}
