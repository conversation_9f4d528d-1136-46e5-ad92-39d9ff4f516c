const path = require('path');
const fs = require('fs');
const utils = require('../nodejs-common/utils.js');
const color = require('../nodejs-common/color.js');

class BaseOutput {
  constructor(debugLevel) {
    this._debug = debugLevel;
    this._outputMap = [];
    // 图表、文件等生成的输出路径
    this._outputPath = "/tmp/stock-crawler/";
    if (!fs.existsSync(this._outputPath)) {
      fs.mkdirSync(this._outputPath);
    }
  }

  getFilePath(file) {
    return path.join(this._outputPath, file);
  }

  _printConvert(k, v) {
    // 通用convert
    if (v === false) {
      return '否';
    }
    if (v == true) {
      return '是';
    }

    if (v == consts.CODE_TYPE_FUND) {
      return '基金';
    }
    if (v == consts.CODE_TYPE_STOCK) {
      return '股票';
    }
    if (v == consts.CODE_TYPE_INDEX) {
      return '指数';
    }

    // 特殊key的convert
    // if (k == '类型/Type: '
    
    return v;
  }

  print() {
    let outputMap = [];
    for (let i of this._outputMap) {
      let k = i[0];
        let v = this._printConvert(k, i[1]);
        outputMap.push([k, v]);
    }

    let maxHeader = utils.GetMaxWcwidth(outputMap.map(i => i[0]));

    let header = color.bred;
    for (let i of outputMap) {
      let h = utils.PadEnd(i[0], maxHeader, ' ');

      if (i[1] != undefined) {
        // 如果是k,v形式
        logger.raw(header(h), i[1]);
      } else {
        // 只有一行title文本形式
        logger.raw(header(h));
      }
    }
  }

  info() {
    logger.info.apply(logger, arguments);
  }

  warn() {
    logger.warn.apply(logger, arguments);
  }

  error() {
    logger.error.apply(logger, arguments);
  }

  debug() {
    if (this._debug) {
      logger.debug.apply(logger, arguments);
    }
  }

  debug2() {
    if (this._debug && this._debug >= 2) {
      logger.debug.apply(logger, arguments);
    }
  }

  // 上证50 - 调整股本/自由流通比例
  appendAdjustShares(lastDateShares, todayShares, freeRatio) {
    // 乘上自由流通比例得到的调整股本中，可能出现小数
    if (!isNaN(lastDateShares)) {
      lastDateShares = Math.ceil(lastDateShares);
    }
    if (!isNaN(todayShares)) {
      todayShares = Math.ceil(todayShares);
    }

    if (freeRatio) {
      if (!isNaN(freeRatio)) {
        freeRatio = freeRatio.toFixed(3);
      }
    } else {
      freeRatio = '';
    }
    freeRatio = utils.PadStart(freeRatio, 6, ' ');

    let sharesInfo = '';
    if (lastDateShares) {
      lastDateShares = '' + lastDateShares;
      todayShares = '' + todayShares;
      if (lastDateShares == todayShares) {
        sharesInfo = `[${utils.PadStart(lastDateShares, 35, ' ')}/${freeRatio}]`;
      } else {
        sharesInfo = `[${utils.PadStart(lastDateShares, 16, ' ')} / ${utils.PadStart(todayShares, 16, ' ')}/${freeRatio}]`;
      }
    }
    return sharesInfo
  }

  // 上证综指 - 总股本
  appendShares(lastDateShares, todayShares) {
    let sharesInfo = '';
    if (lastDateShares) {
      lastDateShares = '' + lastDateShares;
      todayShares = '' + todayShares;
      if (lastDateShares == todayShares) {
        sharesInfo = `[${utils.PadStart(lastDateShares, 16, ' ')}]`;
      } else {
        sharesInfo = `[${utils.PadStart(lastDateShares, 16, ' ')} => ${utils.PadStart(todayShares, 16, ' ')}]`;
      }
    }
    return sharesInfo
  }

  // 上证综指/上证50 - 总流通市值
  appendUnlimitCap(unlimitCap) {
    let unlimitCapInfo = '';
    if (unlimitCap) {
      if (typeof unlimitCap == 'number') {
        unlimitCap = unlimitCap.toFixed(2);
      }
      unlimitCapInfo = `[${utils.PadStart(unlimitCap, 20, ' ')}]`;
    }
    return unlimitCapInfo;
  }

  debugKlineDiff(code, stockName, lastDate, date, lastPrice, todayPrice, lastDateCap, todayCap, appends, fenshiCount) {
    if (!this._debug) {
      return;
    }

    let diff = (todayCap - lastDateCap).toFixed(2);
    if (diff != 'NaN') {
      if (diff > 0) {
        diff = '+' + diff;
      } else {
        diff = '' + diff;
      }
    } else {
      diff = '';
    }

    if (typeof lastDateCap == 'number') {
      lastDateCap = lastDateCap.toFixed(2);
    }
    if (typeof(todayCap) == 'number') {
      todayCap = todayCap.toFixed(2);
    }

    let rate = (diff/lastDateCap*100).toFixed(2);
    if (rate != 'NaN') {
      rate = rate + '%';
      if (rate[0] != '-') {
        rate = '+' + rate;
      }
    } else {
      rate = '';
    }

    if (!todayPrice) {
      todayPrice = '';
    } else if (typeof(todayPrice) == 'number') {
      todayPrice = (+todayPrice).toFixed(2);
    }
    if (!lastPrice) {
      lastPrice = '';
    } else if (typeof(lastPrice) == 'number') {
      lastPrice = (+lastPrice).toFixed(2);
    }

    if (!lastDateCap) {
      lastDateCap = '';
    }
    if (!todayCap) {
      todayCap = '';
    }

    let parts = [
      utils.PadStart(code, 6, ' '),
      '[',
        utils.PadStart(stockName, 8, ' '),
      '] ',
      utils.PadStart(lastDate, 10, ' '),
      '[',
        utils.PadStart(lastDateCap, 20, ' '),
        utils.PadStart(lastPrice, 8, ' '),
      '] => ',
      utils.PadStart(date, 10, ' '),
      '[',
        utils.PadStart(todayCap, 20, ' '),
        utils.PadStart(todayPrice, 8, ' '),
      ']/',
      utils.PadStart(rate, 7, ' '),
      utils.PadStart(diff, 20, ' '),
    ];

    let str = '';
    for (let i of parts) {
      str = `${str}${i}`;
    }
    if (appends) {
      for (let a of appends) {
        str = `${str} ${a}`
      }
    }

    if (fenshiCount !== undefined) {
      str = `${str}/${fenshiCount}`
    }

    logger.raw(str);
    // logger.raw(utils.PadStart(code, 8, ' '), ` \
    //   ${lastDate} \
    //   [${}/${}] => ${date}[${utils.PadStart(todayCap, 17, ' ')}/${utils.PadStart(todayPrice, 8, ' ')}]/${rate} ${} ${} ${unlimitCapInfo}`)
  }

  formatExpected(value, expected, accuracy) {
    if (value.toFixed(accuracy) != expected) {
      return color.bred(value.toFixed(accuracy));
    }
    return color.bgreen(value.toFixed(accuracy));
  }
}

module.exports = BaseOutput;
