
const utils = require('../nodejs-common/utils.js');
const BaseOutput = require('./base.js');

class CrawlerOutput extends BaseOutput {
  constructor(...args) {
    super(...args);
    this._code = "";
    this._name = "";
    this._type = "";
    this._hasMeta = "";
    this._crawlerType = '';
    this._existDate = '';
    this._existNumber = '';
    this._crawlerDate = '';
    this._crawlerNumber = '';
    this._conflictNumber = '';
    this._dataType = '不复权';
    this._market = '';
  }

  setCode(code) {
    this._code = code;
  }

  setName(name) {
    this._name = name;
  }

  setType(type) {
    this._type = type;
  }

  getType(type) {
    return this._type;
  }

  setHasMeta(hasMeta) {
    this._hasMeta = hasMeta;
  }

  setFillStockMeta(oldCount, pullCount, newCount, threeACountSum) {
    this._fillStockMeta = `old:${oldCount} + pull:${pullCount} -> new:${newCount}`;
    this._fillStockMetaThreeASum = `new:${threeACountSum}`;
  }

  setFillStockBMeta(oldCount, pullCount, newCount) {
    this._fillStockBMeta = `old:${oldCount} + pull:${pullCount} -> new:${newCount}`;
  }

  setFillStockTMeta(oldCount, pullCount, newCount) {
    this._fillStockTMeta = `old:${oldCount} + pull:${pullCount} -> new:${newCount}`;
  }

  _commonFill(flag, conflictCount, partCount, existCount, missCount, confictDetails, missDetails) {
    if (partCount > 0) {
      partCount = color.red(partCount);
    }
    if (conflictCount > 0) {
      conflictCount = color.red(conflictCount);
      if (Array.isArray(confictDetails) && confictDetails.length) {
        conflictCount += '<' + confictDetails.join('|') + '>';
      }
    }
    if (missCount> 0) {
      missCount= color.red(missCount);
      if (Array.isArray(missDetails) && missDetails.length) {
        missCount += '<' + missDetails.join('|') + '>';
      }
    }
    let str = `dup:${existCount},part:${partCount},conflict:${conflictCount},dbmiss:${missCount}`
    if (['B', 'gem', 'st', 'kcb'].indexOf(flag) >= 0) {
      str = `dup:${existCount},conflict:${conflictCount},dbmiss:${missCount}`
    }
    return str
  }

  setFillStockMetaByFlag(flag, oldCount, pullCount, newCount, conflictCount, partCount, existCount, missCount, confictDetails, missDetails, stockTypeCountMap) {
    let warnStr = this._commonFill(flag, conflictCount, partCount, existCount, missCount, confictDetails, missDetails);
    if (flag == 'shA') {
      this._fillStockMetaSh = `old:${oldCount} + pull:${pullCount}(${warnStr}) -> new:${newCount}`;
    } else if (flag == 'szA') {
      this._fillStockMetaSz = `old:${oldCount} + pull:${pullCount}(${warnStr}) -> new:${newCount}`;
    } else if (flag == 'bjA') {
      this._fillStockMetaBj = `old:${oldCount} + pull:${pullCount}(${warnStr}) -> new:${newCount}`;
    } else if (flag == 'B') {
      this._fillStockMetaB = `old:${oldCount} + pull:${pullCount}(${warnStr}) -> new:${newCount}`;
    } else if (flag == 'gem') {
      this._fillStockMetaGem = `old:${oldCount} + pull:${pullCount}(${warnStr}) -> new:${newCount}`;
    } else if (flag == 'kcb') {
      this._fillStockMetaKcb = `old:${oldCount} + pull:${pullCount}(${warnStr}) -> new:${newCount}`;
    } else if (flag == 'st') {
      let wrongStr = '';
      if (stockTypeCountMap.noTypeCount) {
        wrongStr = color.red(` wrong:${stockTypeCountMap.noTypeCount}`);
      }
      let typeStr = `A股:${stockTypeCountMap.aCount} B股:${stockTypeCountMap.bCount}${wrongStr}`;
      this._fillStockMetaSt = `old:${oldCount} + pull:${pullCount}(${warnStr}) -> new:${newCount}[${typeStr}]`;
    } else if (flag == 'staq_net') {
      this._fillStockMetaStaq = `old:${oldCount} + pull:${pullCount}(${warnStr}) -> new:${newCount}`;
    }
  }

  setFillIndexMeta(oldCount, pullCount, newCount) {
    this._fillIndexMeta = `old:${oldCount} + pull:${pullCount} -> new:${newCount}`;
  }

  setFillFundMeta(oldCount, pullCount, newCount) {
    this._fillFundMeta = `old:${oldCount} + pull:${pullCount} -> new:${newCount}`;
  }

  setFillDaily(oldCount, pullCount, newCount) {
    this._fillDaily = `old:${oldCount} + pull:${pullCount} -> new:${newCount}`;
  }

  setExistDate(startDate, endDate) {
    startDate = utils.FormatDate(startDate);
    endDate = utils.FormatDate(endDate);
    this._existDate = `${startDate}/${endDate}`;
  }

  setCrawlerDate(startDate, endDate) {
    startDate = utils.FormatDate(startDate);
    endDate = utils.FormatDate(endDate);
    this._crawlerDate = `${startDate}/${endDate}`;
  }

  setSpecifyDateDate(specifyData) {
    this._specifyData = specifyData;
  }

  print() {
    let outputMap = [];
    outputMap.push(['代码/Code: ', this._code]);
    outputMap.push(['类型/Type: ', this._type]);
    outputMap.push(['名称: ', this._name]);
    outputMap.push(['db存在元数据: ', this._hasMeta]);
    if (!this._hasMeta) {
      outputMap.push(['  补充A股股票: ', this._fillStockMeta]);
      outputMap.push(['  补充B股股票: ', this._fillStockBMeta]);
      outputMap.push(['  补充两网/退市股票: ', this._fillStockTMeta]);
      outputMap.push(['  补充指数: ', this._fillIndexMeta]);
      outputMap.push(['  补充基金: ', this._fillFundMeta]);
    }
    outputMap.push(['  更新上证A股: ', this._fillStockMetaSh]);
    outputMap.push(['  更新深证A股: ', this._fillStockMetaSz]);
    outputMap.push(['  更新北证A股: ', this._fillStockMetaBj]);
    outputMap.push(['    上证A+深证A+北证A: ', this._fillStockMetaThreeASum]);
    outputMap.push(['  更新创业板: ', this._fillStockMetaGem]);
    outputMap.push(['  更新科创板: ', this._fillStockMetaKcb]);
    outputMap.push(['  更新沪股通: ', this._fillStockMeta]);
    outputMap.push(['  更新深股通: ', this._fillStockMeta]);
    outputMap.push(['  更新B股: ', this._fillStockMetaB]);
    outputMap.push(['  更新风险警示板: ', this._fillStockMetaSt]);
    outputMap.push(['  更新两网及退市: ', this._fillStockMetaStaq]);

    outputMap.push(['爬取类型: ', this._crawlerType]);
    outputMap.push(['  已存在日期: ', this._existDate]);
    outputMap.push(['  爬取日期: ', this._crawlerDate]);
    outputMap.push(['  补充日K数: ', this._fillDaily]);
    outputMap.push(['  冲突条数: ', this._conflictNumber]);
    outputMap.push(['数据类型: ', this._dataType]);
    outputMap.push(['市场: ', this._market]);
    if (this._specifyData) {
      outputMap.push(['指定日期数据: ', this._specifyData.date]);
      outputMap.push(['  开盘价: ', this._specifyData.openingPrice]);
      outputMap.push(['  收盘价: ', this._specifyData.closingPrice]);
      outputMap.push(['  最高价: ', this._specifyData.highestPrice]);
      outputMap.push(['  最低价: ', this._specifyData.lowestPrice]);
      outputMap.push(['  成交量: ', this._specifyData.tradingVolume]);
      outputMap.push(['  成交额: ', this._specifyData.turnover]);
      outputMap.push(['  振幅: ', this._specifyData.amplitude]);
      outputMap.push(['  涨跌幅: ', this._specifyData.changePercent]);
      outputMap.push(['  涨跌额: ', this._specifyData.change]);
      outputMap.push(['  换手率: ', this._specifyData.turnoverRate]);
    }

    this._outputMap = outputMap;

    super.print();
  }
}

module.exports = CrawlerOutput;
