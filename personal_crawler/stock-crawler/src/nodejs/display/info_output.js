const utils = require('../nodejs-common/utils.js');
const stock = require('../service/stock/index.js');
const BaseOutput = require('./base.js');
const { Notify } = require('../nodejs-common/report/notify.js');
const logger = require('../nodejs-common/logger/index.js');

class InfoOutput extends BaseOutput {
  constructor(...args) {
    super(...args);
    this._type = "";
    this._name = "";
    this._type = "";
    this._fillMeta = '';
    this._fillDaily = '';
    this._fillFenshi = '';
    this._fillValutions = '';
    this._fillComponents = '';
    this._uniqueCodeCount = '';
    this._fillIPOCount = '';
    this._existIPOCount = '';
    this._uniqueIPOCodeCount = '';
    this._fillSPOCount = '';
    this._uniqueSPOCodeCount = '';

    this._delistCount = '';
    this._stCount = '';
    this._statCodeNumber = '';
    this._statMarketCap = '';
    this._taskStatus = 0; // 0: 成功，1：失败
    this._notifyErrorMsg = "";
  }

  setType(type) {
    this._type = type;
  }

  getType(type) {
    return this._type;
  }

  setFillMeta(oldCount, pullCount, newCount) {
    this._fillMeta = `old:${oldCount} + pull:${pullCount} -> new:${newCount}`;
  }

  setFillDaily(oldCount, pullCount, newCount) {
    this._fillDaily = `old:${oldCount} + pull:${pullCount} -> new:${newCount}`;
  }

  setFillFenshi(oldCount, pullCount, newCount) {
    this._fillFenshi = `old:${oldCount} + pull:${pullCount} -> new:${newCount}`;
  }

  setFillValuations(oldCount, pullCount, newCount) {
    this._fillValutions = `[valuations] old:${oldCount} + pull:${pullCount} -> new:${newCount}`;
  }

  setFillIPO(count, uniqueCode, faildCount, stocksLength, exist) {
    this._fillIPOCount = count;
    this._existIPOCount = exist;
    this._uniqueIPOCodeCount = uniqueCode;
    this._fillIPOFailedCount = faildCount;
    this._fillIPOTotalStocksCount = stocksLength;
  }

  setFillSPO(count, uniqueCode) {
    this._fillSPOCount = count;
    this._uniqueSPOCodeCount = uniqueCode;
  }

  setFillValuationsFailed(failedCount) {
    this._fillValuationFailed = `${failedCount}`;
  }

  setFillComponents(oldCount, pullCount, newCount) {
    this._fillComponents = `old:${oldCount} + pull:${pullCount} -> new:${newCount}`;
  }

  setUniqueCodeCount(count) {
    this._uniqueCodeCount = count;
  }

  setDelistCount(count) {
    this._delistCount = count;
  }

  setStCount(count) {
    this._stCount = count;
  }

  setStatCodeNumber(statCodeNumber) {
    this._statCodeNumber = statCodeNumber;
  }

  setStatMarketCap(statMarketCap) {
    this._statMarketCap = statMarketCap;
  }

  SetTaskFailed(errmsg) {
    this._taskStatus = 1;
    this._notifyErrorMsg = errmsg;
  }

  SetTaskSuccess() {
    this._taskStatus = 0;
    this._notifyErrorMsg = "";
  }

  getFormattedOutputForNotify() {
    // Convert terminal color codes to Markdown-style formatting
    let formattedOutput = "\n" + this._outputMap.map(([key, value]) => {
      key = key.replace(/\x1b\[.*?m/g, ''); // Remove color codes from keys
      if (!!value && value.replace) {
        value = value.replace(/\x1b\[0;31m/g, '<font color="red">')
          .replace(/\x1b\[0;32m/g, '<font color="green">')
          .replace(/\x1b\[0;33m/g, '<font color="yellow">')
          .replace(/\x1b\[0;34m/g, '<font color="blue">')
          .replace(/\x1b\[0;35m/g, '<font color="purple">')
          .replace(/\x1b\[0;36m/g, '<font color="cyan">')
          .replace(/\x1b\[0;37m/g, '<font color="white">')
          .replace(/\x1b\[1;31m/g, '<font color="red"><b>')
          .replace(/\x1b\[1;32m/g, '<font color="green"><b>')
          .replace(/\x1b\[1;33m/g, '<font color="yellow"><b>')
          .replace(/\x1b\[1;34m/g, '<font color="blue"><b>')
          .replace(/\x1b\[1;35m/g, '<font color="purple"><b>')
          .replace(/\x1b\[1;36m/g, '<font color="cyan"><b>')
          .replace(/\x1b\[1;37m/g, '<font color="white"><b>')
          .replace(/\x1b\[0m/g, '</font>'); // Close font tags
      }
      return `    ${key}: ${value}`;
    }).join('\n');
    return formattedOutput;
  }

  print() {
    let outputMap = [];
    outputMap.push(['类型/Type: ', this._type]);
    outputMap.push(['db元数据数: ', this._fillMeta]);
    outputMap.push(['日K条数: ', this._fillDaily]);
    outputMap.push(['  独立代码数: ', this._uniqueCodeCount]);
    outputMap.push(['  日K条数均值: ', this._uniqueCodeCount]);
    outputMap.push(['分时条数: ', this._fillFenshi]);
    outputMap.push(['日市值条数: ', this._fillValutions]);
    outputMap.push(['  其中拉取失败个股数: ', this._fillValuationFailed]);
    outputMap.push(['拉取IPO记录数: ', this._fillIPOCount]);
    outputMap.push(['  之前存在IPO记录数: ', this._existIPOCount]);
    outputMap.push(['  其中独立股票数: ', this._uniqueIPOCodeCount]);
    outputMap.push(['  另外拉取IPO失败股票数: ', this._fillIPOFailedCount]);
    outputMap.push(['  所有股票数: ', this._fillIPOTotalStocksCount]);
    outputMap.push(['拉取SPO记录数: ', this._fillSPOCount]);
    outputMap.push(['  其中独立股票数: ', this._uniqueSPOCodeCount]);

    outputMap.push(['成份股调仓记录: ', this._fillComponents]);
    outputMap.push(['被退市股票数: ', this._delistCount]);
    outputMap.push(['被ST股票数: ', this._stCount]);
    outputMap.push(['上退市统计: ', this._statCodeNumber]);
    outputMap.push(['市值统计: ', this._statMarketCap]);

    this._outputMap = outputMap;
    super.print();
  }

  notify(task_name) {
    let task_status = "";
    let detail;

    if (this._taskStatus === 0) {
      task_status = "<font color=\"green\">成功</font>";
      detail = this.getFormattedOutputForNotify();
    } else {
      task_status = "<font color=\"red\">失败</font>";
      detail = this._notifyErrorMsg ? this._notifyErrorMsg : "未知错误";
    }

    Notify(task_name, task_status, detail);
  }
}

module.exports = InfoOutput;
