const utils = require('../nodejs-common/utils.js');
const BaseOutput = require('./base.js');

class DiffCheckInfo {
  constructor(checkTime) {
    this._checkTime = checkTime;
  }

  setLeftData(components) {
    // 股票code的array
    this._leftData = components;
  }

  setRightData(components) {
    // 股票code的array
    this._rightData = components;
  }

  printDiff(outputMap, stockMap) {
    outputMap.push(['时间点1: ', utils.FormatTimeLocal(this._checkTime)]);
    let leftMap = {};
    for (let s of this._leftData) {
      leftMap[s] = stockMap[s].stockName;
    }
    // outputMap.push(['left', leftMap]);
  }
}

class StatIndexOutput extends BaseOutput {
  constructor(...args) {
    super(...args);
    this.diffSlices = [];

    this._type = "";
    this._fillMeta = '';
    // this._dailyCount = '';
    // this._dailyDateRange = '';

    // this._statSvgLink = '';
    this._index = null;
    this._stocks = [];
    this._stockMap = {};
  }

  // 补全基础股票
  setFillMeta(oldCount, pullCount, newCount) {
    this._fillMeta = `old:${oldCount} + pull:${pullCount} -> new:${newCount}`;
  }

  // 补全所有股票-大事提醒
  setFillCompanyEvents(stocksCount, oldCount, pullCount, newCount) {
    this._fillEvents = `stocks[${stocksCount}] events[old:${oldCount} + pull:${pullCount} -> new:${newCount}]`;
  }

  setFillSTRecords(count) {
    this._fillStRecords = count;
  }

  setFillListingRecords(count) {
    this._fillListingRecords = count;
  }


  setType(type) {
    this._type = type;
  }

  getType(type) {
    return this._type;
  }

  setIndexInfo(index) {
    this._index = index;
  }

  setAllStocks(stocks) {
    this._stocks = stocks;
    for (let s of stocks) {
      this._stockMap[s.stockCode] = s;
    }
  }

  // setDailyCount(count) {
  //   this._dailyCount = count;
  // }

  // setDailyDateRange(start, end) {
  //   this._dailyDateRange = [start, end];
  // }

  // setExistLastTradeDateMarketCap(date) {
  //   this._existLastTradeDateMarketCap = date;
  // }

  // setLastTradeDate(date) {
  //   this._lastTradeDate = date;
  // }

  // setFetchNewMarketCap(count) {
  //   this._fetchMarketCapCount = count;
  // }


  // setStatSvgLink(link) {
  //   this._statSvgLink = link;
  // }

  // 添加一个时间点的diff数据分片
  startDiffCheck(time) {
    this.diffSlices.push(new DiffCheckInfo(time));
  }

  // 方式1: db存储的指数成分股
  setTraceComponentsByDb(components) {
    let currentDiff = this.diffSlices[this.diffSlices.length - 1];
    currentDiff.setLeftData(components);
  }

  // 方式2: 指数编制规则的指数成份股
  setTraceComponentsByRule(components) {
    let currentDiff = this.diffSlices[this.diffSlices.length - 1];
    currentDiff.setRightData(components);
  }

  printBaseSection(outputMap) {
    outputMap.push(['基础信息']);
    outputMap.push(['  指数Code: ', this._index.indexCode]);
    outputMap.push(['  指数名称: ', this._index.indexName]);
  }

  // printMarketCapSection(outputMap) {
  //   outputMap.push(['\n']);
  //   outputMap.push(['市值统计']);
  //   outputMap.push(['  最后交易日: ', this._lastTradeDate]);
  //   outputMap.push(['  DB已存最后交易日: ', this._existLastTradeDateMarketCap]);
  //   outputMap.push(['  新增记录: ', this._fetchMarketCapCount]);
  //   outputMap.push(['  生成图表链接: ', this._statSvgLink]);
  // }

  // printIPOSection() {
  //   outputMap.push(['\n']);
  //   outputMap.push(['IPO/增发/分红/配股/减持']);
  // }

  printDiff(outputMap) {
    outputMap.push(['diff结果: ']);
    for (let d of this.diffSlices) {
      d.printDiff(outputMap, this._stockMap);
    }
  }

  print() {
    this.printBaseSection(this._outputMap);
    // this.printMarketCapSection(this._outputMap);
    // this.printDiff(this._outputMap);

    let outputMap = [];
    // outputMap.push(['补齐股票db元数据数: ', this._fillMeta]);
    // outputMap.push(['补齐股票大事提醒: ', this._fillEvents]);
    // outputMap.push(['  - 其中生成ST记录: ', this._fillStRecords]);
    // outputMap.push(['  - 其中生成新股上市/恢复上市/暂停上市记录: ', this._fillListingRecords]);

    this._outputMap = outputMap;
    super.print();
  }
}

module.exports = StatIndexOutput;
