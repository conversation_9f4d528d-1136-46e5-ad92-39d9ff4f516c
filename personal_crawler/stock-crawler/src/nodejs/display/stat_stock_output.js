const utils = require('../nodejs-common/utils.js');
const BaseOutput = require('./base.js');

class StatStockOutput extends BaseOutput {
  constructor(...args) {
    super(...args);
    this._type = "";
    this._stock = null;
  }

  setType(type) {
    this._type = type;
  }

  getType(type) {
    return this._type;
  }

  setStockInfo(stock) {
    this._stock = stock;
  }

  setFillCompanyEvents(count, lastDate, timeStr) {
    this._eventCount = count;
    this._eventFirstDate = lastDate;
    this._eventLoop = timeStr;
  }

  setFillUnlimted(count) {
    this._unlimtedCount = count;
  }

  setFillPledges(count) {
    this._pledgeCount = count;
  }

  setFillSharesHistoryCount(count) {
    this._sharesHistoryCount = count;
  }

  setFillBonus(oldCount, pullCount, newCount) {
    this._bonusCount = `${oldCount}(pull:${pullCount}) => ${newCount}`;
  }

  setFillValuations(oldCount, pullCount, newCount) {
    this._valuationCount = `${oldCount}(pull:${pullCount}) => ${newCount}`;
  }

  setFillNotices(count, lastDate, timeStr) {
    this._noticeCount = count;
    this._noticeFirstDate = lastDate;
    this._noticeLoop = timeStr;
  }

  setFillSPO(count, uniqueCode) {
    this._fillSPOCount = count;
    this._uniqueSPOCodeCount = uniqueCode;
  }


  printBaseSection(outputMap) {
    outputMap.push(['基础信息']);
    outputMap.push(['  股票Code: ', this._stock.stockCode]);
    outputMap.push(['  股票名称: ', this._stock.stockName]);

    outputMap.push(['公司大事记录: ', this._eventCount]);
    outputMap.push(['', '不一定准确，bulkCreate不能返回affectedRows，重复性也会被累加']);
    outputMap.push(['  最早日期: ', this._eventFirstDate]);
    outputMap.push(['  循环次数/上限次数: ', this._eventLoop]);

    outputMap.push(['公告: ', this._noticeCount]);
    outputMap.push(['  最早日期: ', this._noticeFirstDate]);
    outputMap.push(['  循环次数/上限次数: ', this._noticeLoop]);

    outputMap.push(['限售解禁记录: ', this._unlimtedCount]);
    outputMap.push(['每日估值记录: ', this._valuationCount]);
    outputMap.push(['质押记录: ', this._pledgeCount]);
    outputMap.push(['股本变动记录: ', this._sharesHistoryCount]);

    outputMap.push(['拉取SPO记录数: ', this._fillSPOCount]);
    outputMap.push(['  其中独立股票数: ', this._uniqueSPOCodeCount]);

    outputMap.push(['分红送转记录: ', this._bonusCount]);
  }

  print() {
    this.printBaseSection(this._outputMap);
    // this.printMarketCapSection(this._outputMap);

    super.print();
  }
}

module.exports = StatStockOutput;
