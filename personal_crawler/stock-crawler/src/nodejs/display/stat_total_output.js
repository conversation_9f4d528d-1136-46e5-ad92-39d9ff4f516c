const utils = require('../nodejs-common/utils.js');
const BaseOutput = require('./base.js');

class StatTotalOutput extends BaseOutput {
  constructor(...args) {
    super(...args);
    this._type = "";
    this._dailyCount = '';
    this._dailyDateRange = '';

    this._statSvgLink = '';
  }

  setType(type) {
    this._type = type;
  }

  getType(type) {
    return this._type;
  }

  setDailyCount(count) {
    this._dailyCount = count;
  }

  setDailyDateRange(start, end) {
    this._dailyDateRange = [start, end];
  }

  setExistLastTradeDateMarketCap(date) {
    this._existLastTradeDateMarketCap = date;
  }

  setLastTradeDate(date) {
    this._lastTradeDate = date;
  }

  setFetchNewMarketCap(count) {
    this._fetchMarketCapCount = count;
  }


  setStatSvgLink(link) {
    this._statSvgLink = link;
  }

  printBaseSection(outputMap) {
    outputMap.push(['基础信息']);
    outputMap.push(['  日K记录数: ', this._dailyCount]);
    outputMap.push(['  日K日期范围: ', this._dailyDateRange]);
  }

  printMarketCapSection(outputMap) {
    outputMap.push(['\n']);
    outputMap.push(['市值统计']);
    outputMap.push(['  最后交易日: ', this._lastTradeDate]);
    outputMap.push(['  DB已存最后交易日: ', this._existLastTradeDateMarketCap]);
    outputMap.push(['  新增记录: ', this._fetchMarketCapCount]);
    outputMap.push(['  生成图表链接: ', this._statSvgLink]);
  }

  printIPOSection() {
    outputMap.push(['\n']);
    outputMap.push(['IPO/增发/分红/配股/减持']);
  }

  print() {
    this.printBaseSection(this._outputMap);
    this.printMarketCapSection(this._outputMap);

    super.print();
  }
}

module.exports = StatTotalOutput;
