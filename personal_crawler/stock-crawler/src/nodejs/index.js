'use strict';

const { program, Option } = require('commander');
const commands = require('./commands');
const assert = require('assert');

function convertOptionType(optionType) {
  let type;
  if (optionType == 'stock') {
    type = consts.CODE_TYPE_STOCK;
  } else if (optionType == 'index') {
    type = consts.CODE_TYPE_INDEX;
  } else if (optionType == 'fund') {
    type = consts.CODE_TYPE_FUND;
  } else {
    logger.error(`错误的type: ${optionType}`);
    return;
  }
  return type
}

function convertDebugLevel(options) {
  let debugLevel = 0;
  if (options.debug) {
    debugLevel = 1;
  }
  if (options.debug2) {
    debugLevel = 2;
  }
  return debugLevel;
}

function generateCommand(name) {

  return program.command(name, {tree: true})
    .option('-d, --debug', 'debug log')
    .option('-dd, --debug2', 'debug log');
}

async function main() {
  program.name('stock-crawler')
    .description('Crawler for stock')
    .version('0.0.1');
  
  generateCommand('dailycrawler')
    .option('-D, --date <date>', 'specify date')
    .argument('<code>', 'stockCode, or indexCode, or fundCode')
    .action((code, options) => {
      assert.equal(options.__proto__.__proto__, null);

      let debugLevel = convertDebugLevel(options);
      commands.RunDailyCrawler(code, debugLevel, options.date);
    });

  generateCommand('stat')
    .option('-t, --type <char>', 'codeType: stock, index, fund')
    .option('-c, --code <char>', 'stockCode, or indexCode, or fundCode')
    .action((options) => {
      assert.equal(options.__proto__.__proto__, null);

      let type = convertOptionType(options.type);
      if (!type) {
        return;
      }
      let debugLevel = convertDebugLevel(options);

      if (options.code) {
        // 单只股票统计
        commands.RunStatSingle(options.code, debugLevel, type);
      } else {
        // 汇总统计
        commands.RunStatTotalMarket(debugLevel, type);
      }
    });

  generateCommand('info')
    // .argument('<args>', 'args...')
    .option('-t, --type <char>', 'stock, or index, or fund')
    .action((options) => {
      let type = convertOptionType(options.type);
      if (!type) {
        return;
      }

      let debugLevel = convertDebugLevel(options);
      commands.RunInfo(debugLevel, type);
    });

  generateCommand('cron_daily')
    // .argument('<args>', 'args...')
    .option('-t, --type <char>', 'stock, or index, or fund')
    .action((options) => {
      let type = convertOptionType(options.type);
      if (!type) {
        return;
      }

      let debugLevel = convertDebugLevel(options);
      commands.RunCronDaily(debugLevel, type);
    });

  generateCommand('dump')
    .option('-t, --type <char>', 'stock, or index, or fund')
    .option('-f, --file <char>', 'dump file path')
    .action((options) => {
      let type = convertOptionType(options.type);
      if (!type) {
        return;
      }

      let debugLevel = convertDebugLevel(options);
      commands.RunDump(debugLevel, type, options.file);
    });

  program.parse();
}

try {
  main()
  .catch((err) => {
    logger.error('[main Promise Error]', err);
  });
} catch(err) {
  logger.error('[main Error]', err);
}
