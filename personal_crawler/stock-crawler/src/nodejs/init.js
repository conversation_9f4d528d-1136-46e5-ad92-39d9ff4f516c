
const Stock = require('./stock.js');
global.Promise = require('bluebird');
global.logger = require('./nodejs-common/logger/index.js');
global.color = require('./nodejs-common/color.js');
require('./consts.js');

const config = {
  db: {
    database: 'db_financial',
    username: 'financial',
    password: 'financialpass',
    // 容器上
    // host: 'financial-mysql',
    // port: 3306
    // 容器外
    host: 'localhost',
    port: 13312
  }
};

const modelsInit = require('./models');
const eastmoney_v2 = require('./third_client/eastmoney_v2.js');
global.db = {};

async function Init(logger) {
  try {
    await eastmoney_v2.init(logger);
    logger.debug('[Init]', 'start modelsInit()...');
    await modelsInit(db, config.db);
    logger.debug('[Init]', 'end modelsInit()');

    // let stock = new Stock({
    //   code: '603876'
    // });
    // logger.info('[main]', 'start stock.getInfo()...');
    // await stock.getInfo();
    // logger.info('[main]', 'end stock.getInfo()');
  } catch (err) {
    logger.error("[main sync error]", err)
  }
}

module.exports = {
  Init
}
