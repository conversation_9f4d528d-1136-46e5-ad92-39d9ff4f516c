'use strict';

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('AssetSize', {
    fundCode: Sequelize.STRING,
    date: {
      type: Sequelize.DATE,
      comment: '净值日期'
    },
    purchaseShares: {
      type: Sequelize.STRING,
      comment: '期间申购（亿份）'
    },
    redemptionShares: {
      type: Sequelize.STRING,
      comment: '期间赎回（亿份）'
    },
    totalShares: {
      type: Sequelize.STRING,
      comment: '期末总份额（亿份）'
    },
    NAV: {
      type: Sequelize.STRING,
      comment: '期末净资产（亿元）/net asset value'
    },
    NAVROC: {
      type: Sequelize.STRING,
      comment: '净资产变动率/net asset value rate of change'
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'assetsize_unique_index_fundcode_date',
      unique: true,
      method: 'BTREE',
      fields: ['fundCode', 'date']
    }]
  });
}
