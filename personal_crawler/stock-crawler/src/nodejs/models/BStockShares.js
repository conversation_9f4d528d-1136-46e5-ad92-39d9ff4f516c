'use strict';

// 临时存放B股股本数据的临时表。为了验证上证综指
module.exports = function (Sequelize, sequelize) {
  return sequelize.define('BStockShares', {
    stockCode: {
      type: Sequelize.STRING,
      comment: '代码'
    },
    stockName: {
      type: Sequelize.STRING,
      comment: '名称'
    },
    totalShares: {
      type: Sequelize.INTEGER,
      comment: '总股本'
    },
    nonFreeShares: {
      type: Sequelize.INTEGER,
      comment: '未流通股份'
    },
    unlimitedShares: {
      type: Sequelize.INTEGER,
      comment: '已流通股份'
    },
    bFreeShares: {
      type: Sequelize.INTEGER,
      comment: '已流通B股'
    },
    listedAShares: {
      type: Sequelize.INTEGER,
      comment: '已上市A股'
    },
    crawlerDate: {
      type: Sequelize.STRING,
      comment: '爬取日期'
    },
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'b_stock_shares_unique',
      unique: true,
      method: 'BTREE',
      fields: ['stockCode', 'crawlerDate']
    }]
  });
}
