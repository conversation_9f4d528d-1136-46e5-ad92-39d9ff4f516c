'use strict';

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('CompanyEvent', {
    stockCode: {
      type: Sequelize.STRING,
      comment: '代码'
    },
    eventType: {
      type: Sequelize.STRING,
      comment: '事件类型'
    },
    specificEventType: {
      type: Sequelize.STRING,
      comment: '事件类型'
    },
    level1ContentMd5: {
      type: Sequelize.STRING(64),
      comment: 'text类型不能做unique索引，只能用md5'
    },
    level1Content: {
      type: Sequelize.TEXT,
      comment: '内容'
    },
    level2Content: {
      type: Sequelize.TEXT,
      comment: '内容'
    },
    noticeDate: {
      type: Sequelize.STRING,
      comment: '公告日期'
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'events_index_stockcode',
      unique: false,
      method: 'BTREE',
      fields: ['stockCode']
    }, {
      name: 'events_unique_index_code_content',
      unique: true,
      method: 'BTREE',
      fields: ['stockCode', 'level1ContentMd5']
    }]
  });
}
