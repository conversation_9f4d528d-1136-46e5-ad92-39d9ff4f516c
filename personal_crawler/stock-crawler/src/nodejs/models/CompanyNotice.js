'use strict';

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('CompanyNotice', {
    stockCode: {
      type: Sequelize.STRING,
      comment: '代码'
    },
    title: {
      type: Sequelize.STRING,
      comment: '公告标题'
    },
    noticeType1: {
      type: Sequelize.STRING,
      comment: '公告类型'
    },
    noticeType2: {
      type: Sequelize.STRING,
      comment: '公告类型'
    },
    noticeType3: {
      type: Sequelize.STRING,
      comment: '公告类型'
    },
    noticeDate: {
      type: Sequelize.STRING,
      comment: '公告日期'
    },
    artCode: {
      type: Sequelize.STRING,
      comment: '文章链接id',
      primaryKey: true,
    },
    text: {
      type: Sequelize.TEXT('long'),
      comment: '公告正文文本',
    },
    textMd5: {
      type: Sequelize.STRING(64),
      comment: 'text类型不能做unique索引，只能用md5'
    },
    textPageSize: {
      type: Sequelize.INTEGER,
      comment: '公告正文页数',
    },
    pdfUrl: {
      type: Sequelize.STRING,
      comment: 'pdf附件链接',
    },
    pdfSize: {
      type: Sequelize.STRING,
      comment: 'pdf附件大小',
    },
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: []
  });
}
