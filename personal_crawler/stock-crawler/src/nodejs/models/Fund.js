'use strict';

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('Fund', {
    fundCode: Sequelize.STRING,
    fundName: Sequelize.STRING,
    fundAbbrName: Sequelize.STRING,
    fundFullName: Sequelize.STRING,
    fundType: {
      type: Sequelize.STRING
    },
    fundSeries : {
      type: Sequelize.STRING,
      comment: '级数'
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'funds_unique_index_fundcode',
      unique: true,
      method: 'BTREE',
      fields: ['fundCode']
    }]
  });
}
