'use strict';

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('FundKLineDaily', {
    fundCode: {
      type: Sequelize.STRING,
      comment: '基金代码'
    },
    date: {
      type: Sequelize.STRING,
      comment: 'K线日期'
    },
    openingPrice: {
      type: Sequelize.STRING,
      comment: '开盘价'
    },
    closingPrice: {
      type: Sequelize.STRING,
      comment: '收盘价'
    },
    highestPrice: {
      type: Sequelize.STRING,
      comment: '最高价'
    },
    lowestPrice: {
      type: Sequelize.STRING,
      comment: '最低价'
    },
    tradingVolume: {
      type: Sequelize.STRING,
      comment: '成交量'
    },
    turnover: {
      type: Sequelize.STRING,
      comment: '成交额'
    },
    amplitude: {
      type: Sequelize.STRING,
      comment: '振幅'
    },
    changePercent: {
      type: Sequelize.STRING,
      comment: '涨跌幅'
    },
    change: {
      type: Sequelize.STRING,
      comment: '涨跌额'
    },
    turnoverRate: {
      type: Sequelize.STRING,
      comment: '换手率'
    },
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'funds_unique_index_fundcode_date',
      unique: true,
      method: 'BTREE',
      fields: ['fundCode', 'date']
    }, {
      name: 'funds_index_date',
      unique: false,
      method: 'BTREE',
      fields: ['date']
    }]
  });
}
