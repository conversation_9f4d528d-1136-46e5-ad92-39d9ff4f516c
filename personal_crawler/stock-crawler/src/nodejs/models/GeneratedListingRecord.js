'use strict';

// 从CompanyEvent表提取生成的ST记录
module.exports = function (Sequelize, sequelize) {
  return sequelize.define('GeneratedListingRecord', {
    stockCode: {
      type: Sequelize.STRING,
      comment: '代码'
    },
    listingType: {
      type: Sequelize.STRING,
      comment: '1:新股上市; 2:暂停上市; 3:恢复上市; 4:转板上市',
    },
    listingDate: {
      type: Sequelize.STRING,
      comment: '上市日期 / 暂停上市日期 / 恢复上市日期'
    },
    market: {
      type: Sequelize.STRING,
      comment: '交易市场。listingType=1/4 时有值',
      allowNull: true,
    },
    reason: {
      type: Sequelize.STRING,
      comment: '变更原因。listingType=2/3 时有值',
      allowNull: true,
    },
    beforeMarket: {
      type: Sequelize.STRING,
      comment: '转板前交易市场。listingType=4 时有值',
      allowNull: true,
    },
    noticeDate: {
      type: Sequelize.STRING,
      comment: '公告日期'
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'listing_unique_index',
      unique: true,
      method: 'BTREE',
      fields: ['stockCode', 'listingDate']
    }]
  });
}
