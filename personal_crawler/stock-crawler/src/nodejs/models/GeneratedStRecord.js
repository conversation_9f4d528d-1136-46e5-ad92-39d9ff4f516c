'use strict';

// 从CompanyEvent表提取生成的ST记录
module.exports = function (Sequelize, sequelize) {
  return sequelize.define('GeneratedStRecord', {
    stockCode: {
      type: Sequelize.STRING,
      comment: '代码'
    },
    stDirection: {
      type: Sequelize.STRING,
      comment: '1: 从非ST到ST；0: 从ST到非ST'
    },
    beforeName: {
      type: Sequelize.STRING,
      comment: ''
    },
    afterName: {
      type: Sequelize.STRING,
      comment: ''
    },
    noticeDate: {
      type: Sequelize.STRING,
      comment: '公告日期'
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'st_unique_index',
      unique: true,
      method: 'BTREE',
      fields: ['stockCode', 'noticeDate']
    }]
  });
}
