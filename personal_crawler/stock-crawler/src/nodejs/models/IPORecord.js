module.exports = function (Sequelize, sequelize) {
  return sequelize.define('IPORecord', {
    stockCode: {
      type: Sequelize.STRING,
      comment: '股票代码'
    },
    issuePrice: {
      type: Sequelize.STRING,
      comment: '发行价格（元/股）',
    },
    issueNum: {
      type: Sequelize.STRING,
      comment: '发行数量（股）',
    },
    totalRaiseFunds: {
      type: Sequelize.STRING,
      comment: '发行数额',
    },
    freeMarketCap: {
      type: Sequelize.STRING,
      comment: '发行后流通市值',
    },
    totalMarketCap: {
      type: Sequelize.STRING,
      comment: '发行后总市值',
    },
    recordType: {
      type: Sequelize.STRING,
      comment: '发行类型，0:IPO；1:SPO(定向增发)；2:SPO(公开增发)',
    },
    issueDate: {
      type: Sequelize.STRING,
      allowNull: false, // 唯一索引建不允许为NULL，否则容易导致唯一索引不生效、大批重复数据
      comment: '发行日期',
    },
    issueListingDate: {
      type: Sequelize.STRING,
      comment: '增发上市日期',
    },
    issueWay: {
      type: Sequelize.STRING,
      comment: '发行方式',
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'ipo_unique_index_code_date',
      unique: true,
      method: 'BTREE',
      fields: ['stockCode', 'issueDate']
    }, {
      name: 'ipo_index_stock',
      unique: false,
      method: 'BTREE',
      fields: ['stockCode'],
    }]
  });
}
