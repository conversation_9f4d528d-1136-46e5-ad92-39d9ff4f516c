'use strict';

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('Index', {
    indexType: {
      type: Sequelize.ENUM,
      values: ['sh', 'sz', 'zz'],
      comment: '指数类型: 上证系列指数, 深圳系列指数, 中证系列指数'
    },
    isImportant: {
      type: Sequelize.BOOLEAN,
      values: [true, false],
      defaultValue: false,
      comment: '是否沪深重要指数'
    },
    indexCode: {
      type: Sequelize.STRING,
      comment: '代码'
    },
    indexName: {
      type: Sequelize.STRING,
      comment: '名称'
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'indices_unique_index_indexcode',
      unique: true,
      method: 'BTREE',
      fields: ['indexCode']
    }]
  });
}
