'use strict';

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('IndexComponent', {
    indexCode: {
      type: Sequelize.STRING,
      comment: '指数代码'
    },
    indexName: {
      type: Sequelize.STRING,
      comment: '指数名称'
    },
    stockCode: {
      type: Sequelize.STRING,
      comment: '成份股股票代码'
    },
    stockName: {
      type: Sequelize.STRING,
      comment: '成份股股票名称'
    },
    addTime: {
      type: Sequelize.STRING,
      comment: '成份股调入时间'
    },
    delTime: {
      type: Sequelize.STRING,
      comment: '成份股调出时间'
    },
    weight: {
      type: Sequelize.STRING,
      comment: '权重股权重'
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'component_unique_indexcode_stockcode',
      unique: true,
      method: 'BTREE',
      fields: ['indexCode', 'stockCode', 'addTime', 'delTime']
    }]
  });
}
