'use strict';

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('IndexComponentSse', {
    indexCode: {
      type: Sequelize.STRING,
      comment: '指数代码'
    },
    indexName: {
      type: Sequelize.STRING,
      comment: '指数名称'
    },
    stockCode: {
      type: Sequelize.STRING,
      comment: '成份股股票代码'
    },
    stockName: {
      type: Sequelize.STRING,
      comment: '成份股股票名称'
    },
    inDate: {
      type: Sequelize.STRING,
      comment: '官方展示的发布日期。这里一般是爬取时间的下一个开盘日期。这里后续用来维护成份股加进指数的实际时间。如果历史数据被修正、并且每日定时任务在跑，那就是准确的。修正后的inDate维护到addTime'
    },
    addTime: {
      type: Sequelize.STRING,
      comment: '添加日期',
    },
    delTime: {
      type: Sequelize.STRING,
      comment: '移除日期',
    },
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'component_unique_indexcode_stockcode',
      unique: true,
      method: 'BTREE',
      fields: ['indexCode', 'stockCode', 'inDate']
    }]
  });
}
