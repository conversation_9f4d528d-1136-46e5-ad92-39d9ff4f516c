'use strict';

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('IndexValueDaily', {
    indexCode: {
      type: Sequelize.STRING,
      comment: '代码'
    },
    indexName: {
      type: Sequelize.STRING,
      comment: '名称'
    },
    date: {
      type: Sequelize.DATE,
      comment: '数据日期'
    },
    openingPrice: {
      type: Sequelize.INTEGER,
      comment: '开盘价，单位厘'
    },
    closingPrice: {
      type: Sequelize.INTEGER,
      comment: '收盘价，单位厘'
    },
    highestPrice: {
      type: Sequelize.INTEGER,
      comment: '最高价，单位厘'
    },
    lowestPrice: {
      type: Sequelize.INTEGER,
      comment: '最低价，单位厘'
    },
    totalPrice: {
      type: Sequelize.INTEGER,
      comment: '分钟内成交额，单位厘'
    },
    volume: {
      type: Sequelize.INTEGER,
      comment: '成交量，单位手'
    },
    percentChange: {
      type: Sequelize.INTEGER,
      comment: '涨跌幅，百分比值'
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'indicesvaluedaily_index_indexcode',
      unique: false,
      method: 'BTREE',
      fields: ['indexCode']
    }]
  });
}
