'use strict';

// 日内分时数据

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('IndexValueDetail', {
    indexCode: {
      type: Sequelize.STRING,
      comment: '代码'
    },
    date: {
      type: Sequelize.STRING,
      comment: '数据日期'
    },
    time: {
      type: Sequelize.STRING,
      comment: '数据时间'
    },
    price: {
      type: Sequelize.STRING,
      comment: '即时价格，单位厘'
    },
    avgPrice: {
      type: Sequelize.STRING,
      comment: '平均价格，单位厘'
    },
    highestPrice: {
      type: Sequelize.INTEGER,
      comment: '最高价，单位厘'
    },
    lowestPrice: {
      type: Sequelize.INTEGER,
      comment: '最低价，单位厘'
    },
    volume: {
      type: Sequelize.STRING,
      comment: '成交量，单位手'
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'indicesvaluedetail_index_indexcode',
      unique: true,
      method: 'BTREE',
      fields: ['indexCode', 'date', 'time']
    }]
  });
}
