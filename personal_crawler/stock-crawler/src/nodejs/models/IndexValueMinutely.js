'use strict';

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('IndexValueMinutely', {
    indexCode: {
      type: Sequelize.STRING,
      comment: '代码'
    },
    indexName: {
      type: Sequelize.STRING,
      comment: '名称'
    },
    time: {
      type: Sequelize.STRING,
      comment: '数据时间'
    },
    price: {
      type: Sequelize.STRING,
      comment: '分钟价格（指数），单位厘，分钟现价(指数值)，聚合方式为取details最新记录的price值'
    },
    avgPrice: {
      type: Sequelize.STRING,
      comment: '平均价格，单位厘'
    },
    highestPrice: {
      type: Sequelize.STRING,
      comment: '最高价，单位厘'
    },
    lowestPrice: {
      type: Sequelize.STRING,
      comment: '最低价，单位厘'
    },
    totalPrice: {
      type: Sequelize.STRING,
      comment: '分钟内成交额，单位厘'
    },
    volume: {
      type: Sequelize.STRING,
      comment: '现手，来自detail表中，上一分钟左闭右开区间的成交量总和。比如09:37的volume，是[09:36, 09:37)之间的detail的volume求和'
    },
    totalVolume: {
      type: Sequelize.STRING,
      comment: '当天累计成交量，单位手'
    },
    percentChange: {
      type: Sequelize.STRING,
      comment: '涨跌幅，百分比值'
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'indicesvalueminutely_index_indexcode',
      unique: false,
      method: 'BTREE',
      fields: ['indexCode']
    }]
  });
}
