'use strict';

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('MarketValuation', {
    changeRate: {
      type: Sequelize.STRING,
      comment: '涨跌幅',
    },
    closePrice: {
      type: Sequelize.STRING,
      comment: '收盘指数，根据tradeMarketCode有不同的指数含义',
    },
    tradeMarketCode: {
      type: Sequelize.STRING,
      allowNull: false,
      comment: '交易市场代码: 000300/沪深两市/沪深300?; 000001/沪市主板/上证指数; 000688/科创板/科创50; 399001/深市主板/深证指数; 399006/创业板/创业板指',
    },
    freeShares: {
      type: Sequelize.STRING,
      comment: '流通股本（股）',
    },
    freeMarketCap: {
      type: Sequelize.STRING,
      comment: '流通市值（元）',
    },
    totalShares: {
      type: Sequelize.STRING,
      comment: '总股本（股）',
    },
    totalMarketCap: {
      type: Sequelize.STRING,
      comment: '总市值（元）',
    },
    listingOrgNum: {
      type: Sequelize.STRING,
      comment: '个股总数',
    },
    peTtmAvg: {
      type: Sequelize.STRING,
      comment: '平均市盈率（动态）',
    },
    totalMarketCap: {
      type: Sequelize.STRING,
      comment: '总市值（元）',
    },
    tradeDate: {
      type: Sequelize.STRING,
      comment: '交易日期',
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'market_valuation_unique_index_code_date',
      unique: true,
      method: 'BTREE',
      fields: ['tradeMarketCode', 'tradeDate']
    }]
  });
}
