'use strict';

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('NetValue', {
    fundCode: {
      type: Sequelize.STRING,
      comment: '基金代码'
    },
    date: {
      type: Sequelize.DATE,
      comment: '净值日期'
    },
    DGR: {
      type: Sequelize.STRING,
      comment: '基金日增长率/daily growth rate'
    },
    NAVPS: {
      type: Sequelize.STRING, 
      comment: '基金单位净值/net asset value per share'
    },
    ACCNAV: {
      type: Sequelize.STRING,
      comment: '基金累计净值/accumulative net asset value'
    },
    pStatus: {
      type: Sequelize.STRING,
      comment: '申购状态/purchasing status'
    },
    rStatus: {
      type: Sequelize.STRING,
      comment: '赎回状态/redeming status'
    },
    dividend: {
      type: Sequelize.STRING,
      comment: '分红配送'
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'netvalue_unique_index_fundcode_date',
      unique: true,
      method: 'BTREE',
      fields: ['fundCode', 'date']
    }]
  });
}
