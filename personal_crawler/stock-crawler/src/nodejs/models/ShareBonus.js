'use strict';

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('ShareBonus', {
    stockCode: {
      type: Sequelize.STRING,
      comment: '代码'
    },
    reportDate: {
      type: Sequelize.STRING,
      comment: '报告期'
    },
    publishDate: {
      type: Sequelize.STRING,
      comment: '业绩披露日'
    },
    planNoticeDate: {
      type: Sequelize.STRING,
      comment: '预案公告日'
    },
    equityRecordDate: {
      type: Sequelize.STRING,
      comment: '股权登记日'
    },
    exDividendDate: {
      type: Sequelize.STRING,
      comment: '除权除息日'
    },
    noticeDate: {
      type: Sequelize.STRING,
      comment: '最新公告日'
    },
    assignProgress: {
      type: Sequelize.STRING,
      comment: '方案进度'
    },
    implPlanProfile: {
      type: Sequelize.STRING,
      comment: '方案描述'
    },
    basicEps: {
      type: Sequelize.STRING,
      comment: '每股收益（元）'
    },
    bvps: {
      type: Sequelize.STRING,
      comment: '每股净资产（元）'
    },
    perCapitalReserve: {
      type: Sequelize.STRING,
      comment: '每股公积金（元）',
    },
    perUnassignProfit: {
      type: Sequelize.STRING,
      comment: '每股未分配利润（元）'
    },
    totalShares: {
      type: Sequelize.INTEGER,
      comment: '总股本'
    },
    bonusItRatio: {
      type: Sequelize.STRING,
      comment: '送转股份-每10股送转多少'
    },
    bonusRatio: {
      type: Sequelize.STRING,
      comment: '送转股份-每10股送多少'
    },
    itRatio: {
      type: Sequelize.STRING,
      comment: '送转股份-每10股转多少'
    },
    pretaxBonusRmb: {
      type: Sequelize.STRING,
      comment: '现金分红-每10股派多少（税前）'
    },
    dividentRatio: {
      type: Sequelize.STRING,
      comment: '股息率'
    },
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'bonus_unique_index',
      unique: true,
      method: 'BTREE',
      fields: ['stockCode', 'implPlanProfile']
    }]
  });
}
