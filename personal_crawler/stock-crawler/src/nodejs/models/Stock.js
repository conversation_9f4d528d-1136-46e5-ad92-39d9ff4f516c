'use strict';

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('Stock', {
    stockCode: {
      type: Sequelize.STRING,
      comment: '代码'
    },
    stockName: {
      type: Sequelize.STRING,
      comment: '名称'
    },
    cmd: {
      type: Sequelize.STRING
    },
    stockType: {
      type: Sequelize.STRING,
      comment: '股票类型, A:A股, B:B股, T:两网及退市, NEEQ:新三板'
    },
    /**
     * 以下是针对沪深个股
     */
    stockCity: {
      type: Sequelize.ENUM,
      values: ['SH', 'SZ', 'BJ'],
      comment: '交易所: 上证, 深圳, 北京'
    },
    stockState: {
      type: Sequelize.STRING,
      comment: '目前保存退市状态；st/delist'
    },
    isNew: {
      type: Sequelize.BOOLEAN,
      comment: '是否新股'
    },
    isSme: {
      type: Sequelize.BOOLEAN,
      comment: '是否中小板(SME board)'
    },
    isGem: {
      type: Sequelize.BOOLEAN,
      comment: '是否创业板(growth enterprise market)'
    },
    isKcb: {
      type: Sequelize.BOOLEAN,
      comment: '是否科创板'
    },
    isDanger: {
      type: Sequelize.BOOLEAN,
      comment: '是否风险警示版'
    },
    /**
     * 以下是针对新三板
     */
    isInnovate: {
      type: Sequelize.BOOLEAN,
      comment: '是否新三板->创新层'
    },
    isBasic: {
      type: Sequelize.BOOLEAN,
      comment: '是否新三板->基础层'
    },
    isAgreement: {
      type: Sequelize.BOOLEAN,
      comment: '是否新三板->协议'
    },
    isMarketMaking: {
      type: Sequelize.BOOLEAN,
      comment: '是否新三板->做市'
    },
    isBidding: {
      type: Sequelize.BOOLEAN,
      comment: '是否新三板->竞价'
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'stocks_unique_index_stockcode',
      unique: true,
      method: 'BTREE',
      fields: ['stockCode']
    }]
  });
}
