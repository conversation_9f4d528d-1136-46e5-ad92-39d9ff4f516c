'use strict';

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('StockFundamental', {
    stockCode: {
      type: Sequelize.STRING,
      comment: '代码'
    },
    stockName: {
      type: Sequelize.STRING,
      comment: '名称'
    },
    businessRaw: {
      type: Sequelize.STRING,
      comment: '主营范围（东财原始文本描述）'
    },
    business: {
      type: Sequelize.STRING,
      comment: '主营范围（解析后的规则化描述，json序列化）'
    },
    // 单独起一个表存储结构化数据
    // businessComponents: {
    //   type: Sequelize.STRING,
    //   comment: '主营构成'
    // },
    crawlerTime: {
      type: Sequelize.DATE,
      comment: '相关数据爬取时间'
    },
    
    // 一些特殊分析字段
    isCleanEnergy: {
      type: Sequelize.BOOLEAN,
      comment: '是否新能源相关业务'
    },
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'fundamental_unique_index_bussiness',
      unique: true,
      method: 'BTREE',
      fields: ['stockCode', 'businessRaw']
    }]
  });
}
