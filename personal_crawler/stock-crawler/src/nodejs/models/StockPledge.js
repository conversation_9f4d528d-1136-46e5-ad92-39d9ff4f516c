'use strict';

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('StockPledge', {
    stockCode: {
      type: Sequelize.STRING,
      comment: '股票代码',
      allowNull: false
    },
    holderName: {
      type: Sequelize.STRING,
      comment: '股东名称'
    },
    pledgeNum: {
      type: Sequelize.STRING,
      comment: '质押股份数量（股）'
    },
    pledgeMarketCap: {
      type: Sequelize.STRING,
      comment: '质押股份市值（元）'
    },
    holdRatio: {
      type: Sequelize.STRING,
      comment: '占所持股份比例（%）'
    },
    pfTsr: {
      type: Sequelize.STRING,
      comment: '占总股本比例（%）'
    },
    pfOrg: {
      type: Sequelize.STRING,
      comment: '质押机构'
    },
    pfReason: {
      type: Sequelize.STRING,
      comment: '质押原因'
    },
    pfPurpose: {
      type: Sequelize.STRING,
      comment: '质押目的'
    },
    closePrice: {
      type: Sequelize.STRING,
      comment: '质押日收盘价（元）'
    },
    warningLine: {
      type: Sequelize.STRING,
      comment: '预警线（估算）'
    },
    openLine: {
      type: Sequelize.STRING,
      comment: '平仓线'
    },
    pledgeStartDate: {
      type: Sequelize.STRING,
      comment: '质押开始日期'
    },
    pledgeUnfreezeDate: {
      type: Sequelize.STRING,
      comment: '质押解除日期'
    },
    unfreezeState: {
      type: Sequelize.STRING,
      comment: '质押解除状态'
    },
    noticeDate: {
      type: Sequelize.STRING,
      comment: '公告日期'
    },
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'pledges_index_stock',
      unique: false,
      method: 'BTREE',
      fields: ['stockCode']
    }]
  });
}
