'use strict';

// 存放股本变动历史数据记录
module.exports = function (Sequelize, sequelize) {
  return sequelize.define('StockShares', {
    stockCode: {
      type: Sequelize.STRING,
      comment: '代码'
    },
    stockName: {
      type: Sequelize.STRING,
      comment: '名称'
    },
    totalShares: {
      type: Sequelize.STRING,
      comment: '总股本'
    },
    nonFreeShares: {
      type: Sequelize.STRING,
      comment: '未流通股份'
    },
    limitedShares: {
      type: Sequelize.STRING,
      comment: '流通受限股'
    },
    unlimitedShares: {
      type: Sequelize.STRING,
      comment: '已流通股份'
    },
    listedAShares: {
      type: Sequelize.STRING,
      comment: '已上市A股'
    },
    limitedAShares: {
      type: Sequelize.STRING,
      comment: '限售A股'
    },
    bFreeShares: {
      type: Sequelize.STRING,
      comment: '已流通B股'
    },
    hFreeShares: {
      type: Sequelize.STRING,
      comment: '已流通H股'
    },
    otherFreeShares: {
      type: Sequelize.STRING,
      comment: '其他流通股本，目前看到有D股上市',
    },
    limitedSharesChange: {
      type: Sequelize.STRING,
      comment: '流通受限股变动份额'
    },
    unlimitedSharesChange: {
      type: Sequelize.STRING,
      comment: '已流通股变动份额'
    },
    listedASharesChange: {
      type: Sequelize.STRING,
      comment: '流通A股变动份额'
    },
    limitedASharesChange: {
      type: Sequelize.STRING,
      comment: '限售A股变动份额'
    },
    changeReason: {
      type: Sequelize.STRING,
      allowNull: false,
      comment: '股本变动原因'
    },
    changeReasonExplain: {
      type: Sequelize.STRING,
      comment: '变更原因解释'
    },
    endDate: {
      type: Sequelize.STRING,
      allowNull: false,
      comment: 'web/app上展示的日期'
    },
    noticeDate: {
      type: Sequelize.STRING,
      comment: '公告日期'
    },
    listingDate: {
      type: Sequelize.STRING,
      comment: '股本变动上市日期'
    },
    limitedStateShares: {
      type: Sequelize.STRING,
      comment: '国家持股（受限）'
    },
    limitedDomesticNostate: {
      type: Sequelize.STRING,
      comment: '境内法人持股（受限）'
    },
    limitedOthars: {
      type: Sequelize.STRING,
      comment: '其他内资持股（受限）'
    },
    crawlerDate: {
      type: Sequelize.STRING,
      comment: '爬取日期'
    },
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'stock_shares_unique',
      unique: true,
      method: 'BTREE',
      fields: ['stockCode', 'endDate', 'changeReason']
    }]
  });
}
