'use strict';

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('StockValuation', {
    stockCode: {
      type: Sequelize.STRING,
      allowNull: false,
      comment: '股票代码',
    },
    boardCode: {
      type: Sequelize.STRING,
      comment: '所属板块代码',
    },
    boardName: {
      type: Sequelize.STRING,
      comment: '所属板块名称',
    },
    totalMarketCap: {
      type: Sequelize.STRING,
      comment: '总市值（元）',
    },
    notlimitedMarketCapA: {
      type: Sequelize.STRING,
      comment: '流通市值（元）',
    },
    closePrice: {
      type: Sequelize.STRING,
      comment: '收盘价格',
    },
    changeRate: {
      type: Sequelize.STRING,
      comment: '涨跌幅',
    },
    totalShares: {
      type: Sequelize.STRING,
      comment: '总股本（股）',
    },
    freeSharesA: {
      type: Sequelize.STRING,
      comment: '流通股本（股），不包含境外，仅A股',
    },
    peTtm: {
      type: Sequelize.STRING,
      comment: '市盈率（TTM）',
    },
    peLar: {
      type: Sequelize.STRING,
      comment: '市盈率（静态）',
    },
    pbMrq: {
      type: Sequelize.STRING,
      comment: '市净率',
    },
    pegCar: {
      type: Sequelize.STRING,
      comment: 'PEG值',
    },
    pcfOcfLar: {
      type: Sequelize.STRING,
      comment: '',
    },
    pcfOcfTtm: {
      type: Sequelize.STRING,
      comment: '市现率',
    },
    psTtm: {
      type: Sequelize.STRING,
      comment: '市销率',
    },
    tradeDate: {
      type: Sequelize.STRING,
      comment: '交易日期',
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'stock_valuation_unique_index_code_date',
      unique: true,
      method: 'BTREE',
      fields: ['stockCode', 'tradeDate']
    }]
  });
}
