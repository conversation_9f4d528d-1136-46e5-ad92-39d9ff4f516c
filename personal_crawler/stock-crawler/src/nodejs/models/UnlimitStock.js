'use strict';

module.exports = function (Sequelize, sequelize) {
  return sequelize.define('UnlimitStock', {
    stockCode: {
      type: Sequelize.STRING,
      comment: '代码'
    },
    addListingShares: {
      type: Sequelize.INTEGER,
      comment: '新增可上市股份（股）'
    },
    freeDate: {
      type: Sequelize.STRING,
      comment: '解除限售日期',
    },
    freeSharesType: {
      type: Sequelize.STRING,
      comment: '限售股类型'
    },
    liftHolderAll: {
      type: Sequelize.INTEGER,
      comment: '本次解禁股东多少家，看着像是同个日期作为一次？',
    },
    liftSharesAll: {
      type: Sequelize.INTEGER,
      comment: '本次解禁多少股，看着像是同个日期作为一次？',
    },
    limitedHolderName: {
      type: Sequelize.STRING,
      comment: '解禁股东名称'
    },
    limitedType: {
      type: Sequelize.STRING,
      comment: '解禁类型'
    },
    planFeature: {
      type: Sequelize.STRING,
      comment: '信息来源'
    },
    orgCode: {
      type: Sequelize.STRING,
      comment: '组织代码'
    }
  }, {
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    indexes: [{
      name: 'unlimit_index_stockcode',
      unique: false,
      method: 'BTREE',
      fields: ['stockCode']
    }, {
      name: 'unlimit_unique_index',
      unique: true,
      method: 'BTREE',
      fields: ['stockCode', 'freeDate', 'limitedHolderName']
    }]
  });
}
