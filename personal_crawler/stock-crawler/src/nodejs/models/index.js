'use strict';

const Sequelize = require('sequelize');

// async function dataInit(db) {
//   await dataInitIndices(db);
//   // await dataInitIndexValueDaily(db);
//   await dataInitIndexValueMinutely(db);
// 
//   // await dataInitStocks(db);
// }

async function modelsInit(db, config) {
  const sequelize = new Sequelize(
    config.database, 
    config.username,
    config.password, {
      host: config.host,
      port: config.port,
      dialect: 'mysql',
      pool: {
        max: 100
      },
      timezone: "+08:00",
      operatorsAliases: false,
      logging: false
  });

  db.Index = require('./Index.js')(Sequelize, sequelize);
  db.IndexKLineDaily = require('./IndexKLineDaily')(Sequelize, sequelize);
  db.IndexComponent = require('./IndexComponent')(Sequelize, sequelize);
  db.IndexComponentSse = require('./IndexComponentSse')(Sequelize, sequelize);

  db.IndexValueDaily = require('./IndexValueDaily.js')(Sequelize, sequelize);
  db.IndexValueMinutely = require('./IndexValueMinutely.js')(Sequelize, sequelize);
  db.IndexValueDetail = require('./IndexValueDetail.js')(Sequelize, sequelize);

  db.Stock = require('./Stock.js')(Sequelize, sequelize);
  db.StockKLineDaily = require('./StockKLineDaily.js')(Sequelize, sequelize);
  db.StockFundamental= require('./StockFundamental')(Sequelize, sequelize);
  db.StockValueDetail = require('./StockValueDetail.js')(Sequelize, sequelize);

  db.Fund = require('./Fund.js')(Sequelize, sequelize);
  db.FundKLineDaily = require('./FundKLineDaily.js')(Sequelize, sequelize);

  db.MarketValuation = require('./MarketValuation.js')(Sequelize, sequelize);
  db.StockValuation = require('./StockValuation.js')(Sequelize, sequelize);
  db.BStockShares = require('./BStockShares.js')(Sequelize, sequelize);
  db.StockShares = require('./StockShares.js')(Sequelize, sequelize);
  db.UnlimitStock = require('./UnlimitStock.js')(Sequelize, sequelize);
  db.CompanyEvent = require('./CompanyEvent.js')(Sequelize, sequelize);
  db.GeneratedStRecord = require('./GeneratedStRecord.js')(Sequelize, sequelize);
  db.GeneratedListingRecord = require('./GeneratedListingRecord.js')(Sequelize, sequelize);
  db.CompanyNotice = require('./CompanyNotice.js')(Sequelize, sequelize);
  db.IPORecord = require('./IPORecord.js')(Sequelize, sequelize);
  db.StockPledge = require('./StockPledge.js')(Sequelize, sequelize);
  db.ShareBonus = require('./ShareBonus.js')(Sequelize, sequelize);

  await sequelize.sync();
  // await db.StockShares.sync({force: true});
  // await db.IndexComponentSse.sync({force: true});
  // await db.ShareBonus.sync({force: true});
  // await db.UnlimitStock.sync({force: true});
  // await db.CompanyNotice.sync({force: true});
  // await db.CompanyEvent.sync({force: true});
  // await db.IPORecord.sync({force: true});
  // await db.Index.sync({force: true});
  // await db.IndexValueMinutely.sync({force: true});
  // await db.IndexValueDetail.sync({force: true});
  // await db.StockValueDetail.sync({force: true});
  // await db.Stock.sync({force: true});
  // await db.StockKLineDaily.sync({force: true});
  // await db.Fund.sync({force: true});
  // await db.FundKLineDaily.sync({force: true});

  // await dataInit(db);
}

module.exports = modelsInit;
