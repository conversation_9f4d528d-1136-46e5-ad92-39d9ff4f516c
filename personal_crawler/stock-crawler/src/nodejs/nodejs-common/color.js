const wcwidth = require('wcwidth');
class Color {
  constructor() {
    // 注意，此处不能用\033，只能用\x1b，否则一直报错：SyntaxError: Octal escape sequences are not allowed in strict mode.
    this._map = {
      black: '\x1b[0;30m',
      red: '\x1b[0;31m',
      green: '\x1b[0;32m',
      yellow: '\x1b[0;33m',
      blue: '\x1b[0;34m',
      purple: '\x1b[0;35m',
      cyan: '\x1b[0;36m',
      white: '\x1b[0;37m',

      bblack: '\x1b[1;30m',
      bred: '\x1b[1;31m',
      bgreen: '\x1b[1;32m',
      byellow: '\x1b[1;33m',
      bblue: '\x1b[1;34m',
      bpurple: '\x1b[1;35m',
      bcyan: '\x1b[1;36m',
      bwhite: '\x1b[1;37m',
      nocolor: '\x1b[0m',
    };

    for (let c in this._map) {
      this[c] = (str) => {
        return `${this._map[c]}${str}${this._map['nocolor']}`;
      }
    }
  }

  array(data, color) {
    for (let i = 0; i < data.length; i++) {
      data[i] = `${this._map[color]}${data[i]}${this._map['nocolor']}`
    }
    return data;
  }

  width(str) {
    str = str + '';
  // 关于wcwidth去除颜色字符的影响，看起来并没有通用的方案。只能自己手动处理。
  // https://stackoverflow.com/questions/27892047/how-might-i-calculate-the-terminal-column-width-of-various-characters
    let width = 0;
    let rawstr = str;
    for (let c in this._map) {
      // console.log('before', c, wcwidth(rawstr));
      rawstr = rawstr.replaceAll(this._map[c], '');
      // console.log('after', c, wcwidth(rawstr));
    }
    // return wcwidth(str) - wcwidth(rawstr);
    return wcwidth(rawstr);
  }
}

const color = new Color();

module.exports = color;
