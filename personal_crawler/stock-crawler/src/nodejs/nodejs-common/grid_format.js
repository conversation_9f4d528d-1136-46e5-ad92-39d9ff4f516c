const color = require('./color.js');
const utils = require('./utils.js');

class GridFormat {
  constructor(cols) {
    this.cols = cols;
    this.data = [];

    this._maxColWidthMap = [];
    for (let i in this.cols) {
      this._maxColWidthMap[i] = 0;
    }
  }

  _append(data, line) {
    for (let i in line) {
      let width = color.width(line[i]);
      if (width > this._maxColWidthMap[i]) {
        this._maxColWidthMap[i] = width;
      }
    }
    data.push(line);
  }

  push(line) {
    this._append(this.data, line)
  }

  format(opts) {
    let data = [];
    if (opts && opts.title) {
      this._append(data, color.array(this.cols, 'bgreen'))
    }
    data = data.concat(this.data);

    let lines = [];
    for (let record of data) {
      let str = '';
      for (let i in record) {
        let col = record[i];
        let width = this._maxColWidthMap[i];
        str += ' ' + utils.PadStart(col, width, ' ');
      }
      lines.push(str);
    }
    return lines;
  }

  print() {
    let lines = this.format({title: true});
    for (let l of lines) {
      console.log(l);
    }
  }
}



module.exports = GridFormat;
