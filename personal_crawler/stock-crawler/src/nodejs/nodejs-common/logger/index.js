'use strict';
const util = require('util');
const utils = require('../utils.js');
const color = require('../color.js');

class Logger {
  constructor() {
  }

  _time() {
    return "["+utils.FormatTime(new Date())+"]"
  }

  _getCallerLocation() {
    const error = new Error();
    const stack = error.stack.split('\n');
    // Get the caller of the logging method (index 4 since we need to skip internal calls)
    const callerLine = stack[4];
    if (!callerLine) return '';
    const match = callerLine.match(/\s+at\s.+\s\((.+):(\d+):\d+\)/);
    return match ? ` [${match[1]}:${match[2]}]` : '';
  }

  raw() {
    console.log(...arguments)
  }

  info() {
    console.log(color.green(this._time() + this._getCallerLocation()), ...arguments)
  }

  debug() {
    let all = [];
    for (let i of arguments) {
      if (Array.isArray(i)) {
        all.push(util.inspect(i, {maxArrayLength: null}));
      } else {
        all.push(i);
      }
    }
    console.debug(color.cyan(this._time() + this._getCallerLocation()), ...all)
  }

  warn() {
    console.warn(color.yellow(this._time() + this._getCallerLocation()), ...arguments)
  }

  error() {
    console.error(color.red(this._time() + this._getCallerLocation()), ...arguments)
  }
}
const logger = new Logger();

function _test() {
  logger.info("test");
}
_test();

module.exports = logger;
