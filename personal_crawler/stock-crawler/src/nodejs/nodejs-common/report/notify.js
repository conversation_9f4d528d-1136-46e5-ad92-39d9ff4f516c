const { httpRequestWithRetry } = require('../request/index');

async function Notify(name, status, detail) {
    const url = 'https://www.feishu.cn/flow/api/trigger-webhook/2f00ca77f9a2d60b2cae852fc6e54ced';
    const body = {
        task_name: name,
        task_status: status,
        task_detail: detail
    };

    const options = {
        method: 'POST',
        uri: url,
        body: body,
        json: true
    };

    try {
        await httpRequestWithRetry(options);
        console.log('Notification sent successfully: ');
    } catch (error) {
        console.error('Failed to send notification:', error);
    }
}

module.exports = {
    Notify
};