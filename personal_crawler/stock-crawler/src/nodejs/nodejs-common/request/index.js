const rp = require('request-promise');
const zlib = require('zlib');

const defaultHeaders = {
    "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36",
};

async function makeRequest(logger, options, retries = 3, delay = 1000) {
    // Add proxy support from environment variables
    let proxy = process.env.http_proxy || process.env.HTTP_PROXY;
    if (options.proxy) {
        // Ignore SSL issues when using proxy
        options.rejectUnauthorized = false;
        proxy = options.proxy;
    }

    // Check proxy format and warn if needed
    if (proxy) {
        // Check for whistle proxy ports
        const whistlePorts = [8899, 18899, 28899, 38899, 48899];
        const proxyPort = parseInt(proxy.split(':').pop());
        // if (whistlePorts.includes(proxyPort)) {
        //     logger?.warn(`Detected whistle proxy port (${proxyPort}), make sure you have whistle running`);
        // }
        
        // Check if proxy is missing protocol
        if (!proxy.startsWith('http://') && !proxy.startsWith('https://')) {
            logger?.warn(`Proxy URL "${proxy}" is missing protocol (http:// or https://), this may cause issues`);
        }
    }
    logger?.debug(`Using options.proxy: ${proxy}, retries=${retries}, delay=${delay}`);

    options.headers = { ...defaultHeaders, ...options.headers };
    
    for (let attempt = 1; attempt <= retries; attempt++) {
        try {
            logger.debug(`Request ${JSON.stringify(options)}`)
            let response = await rp(options);
            
            if (options.encoding === null && !options.json) {
                response = zlib.gunzipSync(response).toString('utf8');
                if (options.parseJson) {
                    response = JSON.parse(response);
                }
            }
            
            return response;
        } catch (error) {
            const proxyError = error.cause?.code === 'ECONNREFUSED' && options.proxy ? 
                ' (Proxy connection failed)' : '';
            logger?.warn(`[Attempt ${attempt}] Request failed for ${options.uri}: ${error.message}${proxyError}`);
            if (attempt === retries) {
                logger?.error(`[Failed] Max retries reached for URL: ${options.uri}`);
                throw error; 
            }
            await new Promise((resolve) => setTimeout(resolve, delay * attempt));
        }
    }
}

async function httpRequestWithRetry(options, retryCount = 3) {
    return makeRequest(console, options, retryCount, 1000);
}

module.exports = {
    makeRequest,
    httpRequestWithRetry
};
