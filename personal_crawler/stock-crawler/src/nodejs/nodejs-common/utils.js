const color = require('./color.js');

// function formatTime(d_t) {
//   if (!(d_t instanceof Date)) {
//     d_t = new Date(d_t);
//   }
//   let year = d_t.getFullYear();
//   let month = ("0" + (d_t.getMonth() + 1)).slice(-2);
//   let day = ("0" + d_t.getDate()).slice(-2);
//   let hour = d_t.getHours();
//   let minute = d_t.getMinutes();
//   let seconds = d_t.getSeconds();
//   let format = year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + seconds;
//   // prints date & time in YYYY-MM-DD HH:MM:SS format
//   // console.log(format);
//   return format;
// }

function FormatTime(time) {
  if (typeof(time) == 'string') {
    time = new Date(time);
  }
  return time.toISOString();
}

function FormatTimeLocal(time) {
  if (typeof(time) == 'string') {
    time = new Date(time);
  }
  const offset = time.getTimezoneOffset()
  time = new Date(time.getTime() - (offset*60*1000))
  const str = time.toISOString();
  return str.slice(0, 10) + ' ' + str.slice(11, 19);
}

function FormatDate(time) {
  if (!time) {
    return time;
  }
  if (typeof(time) == 'string') {
    time = new Date(time);
  }
  const offset = time.getTimezoneOffset()
  time = new Date(time.getTime() - (offset*60*1000))
  const str = time.toISOString();
  return str.slice(0, 10);
}

function AddDays(dateStr, daysCount) {
  let date = new Date(dateStr);
  date.setDate(date.getDate() + daysCount);
  return FormatDate(date);
}

async function Sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

function SortObjectsByKey(unordered) {
  const ordered = Object.keys(unordered).sort().reduce(
    (obj, key) => { 
      obj[key] = unordered[key]; 
      return obj;
      }, 
    {}
  );
  return ordered;
}

function SortObjectsByValue(unordered) {
  const ordered = Object.keys(unordered).sort((a, b) => {
  	  return unordered[a] - unordered[b];
  	}).reduce((prev, curr, i) => {
  	  prev[i] = unordered[curr];
  	  return prev;
  	}, {});
  return ordered;
}

function Assert(value) {
  if (!value) {
    throw new Error("assert error")
  }
}

function AssertEqual(left, right) {
  if (left != right) {
    console.log(`${left} != ${right}`)
  }
  Assert(left == right)
}

function ParseNumberText(desc) {
  let match = desc.match(/([0-9.]+)(.*)/i);
  // console.log(match);
  let num = +match[1];
  let unit = match[2];
  if (unit == '万') {
    num *= 10000;
  } else if (unit == '亿') {
    num *= 100000000;
  }
  return num.toFixed(0);
}

function ParseNumberComma(desc) {
  return +desc.split(',').join('');
}

function ParseChineseDate(date) {
  let match = date.match(/(\d+)年(\d+)月(\d+)日/i);
  if (!match) {
    return null;
  }
  return new Date(match[1], match[2]-1, (+match[3])+1).toISOString().slice(0, 10);
}

// 从某个起始日期开始，生成每一个date
function GenAllDatesFrom(start, end) {
  if (!end) {
    end = FormatDate(new Date())
  }
  let ret = [];
  for (let i = start; i <= end; i = AddDays(i, 1)) {
    ret.push(i);
  }
  return ret;
}

function ArrayEqual(arr1, arr2) {
  if (arr1.length != arr2.length) {
    return false;
  }

  arr1 = arr1.sort();
  arr2 = arr2.sort();
  for (let i = 0; i < arr1.length; ++i) {
    if (arr1[i] != arr2[i]) {
      return false;
    }
    return true;
  }

  return true;
}

function ArrayMinus(arr1, arr2) {
  let res = [];
  for (let i of arr1) {
    if (arr2.indexOf(i) < 0) {
      // arr1有，而arr2没有
      res.push(i);
    }
  }
  return res;
}

const isNumeric = (num) => (typeof(num) === 'number' || typeof(num) === "string" && num.trim() !== '') && !isNaN(num);

function PrettyNumber(num) {
  if (isNumeric(num) && num.length > 7) {
    let dotIndex = num.indexOf('.');
    let basePos = num.length - dotIndex;
    if (dotIndex == -1) {
      basePos = 0;
    }

    basePos = -basePos - 4;
    num = num.slice(0, basePos) + ',' + num.slice(basePos);

    if (num.length > (-basePos + 5)) {
      basePos = basePos - 5;
      num = num.slice(0, basePos) + ',' + num.slice(basePos);
    }

    if (num.length > (-basePos + 5)) {
      basePos = basePos - 5;
      num = num.slice(0, basePos) + ',' + num.slice(basePos);
    }

    // 处理首字符正负号的num
    if (num.slice(0, 2) == "+,") {
      num = "+" + num.slice(2)
    }
    if (num.slice(0, 2) == "-,") {
      num = "-" + num.slice(2)
    }
  }
  return num;
}

function GetMaxWcwidth(array) {
  let maxHeader = 0;
  for (let i of array) {
    let width = color.width(i);
    if (width > maxHeader) {
      maxHeader = width;
    }
  }

  if (maxHeader % 2 == 1) {
    maxHeader += 1;
  }
  return maxHeader;
}

function PadStart(str, num, ch) {
  str = str + '';
  str = PrettyNumber(str);
  let gap = num - color.width(str);
  let h = str.padStart(gap + str.length, ch);
  return h
}

function PadEnd(str, num, ch) {
  str = str + '';
  str = PrettyNumber(str);
  let gap = num - color.width(str);
  let h = str.padStart(gap + str.length, ch);
  return h
}

function test() {
  let res = PadStart('片仔癀', 8, ' ');
  console.log(wcwidth('片仔癀'));
  console.log(wcwidth('片仔癀a'));
  let str = color.bred(color.bred('片仔癀a'));
  console.log(wcwidth(str));
  console.log(color.width(str));
  console.log(`[${res}]`);
}
// test();

module.exports = {
  FormatTime,
  FormatTimeLocal,
  FormatDate,
  AddDays,
  SortObjectsByKey,
  SortObjectsByValue,
  Sleep,
  Assert,
  AssertEqual,
  ParseNumberText,
  ParseChineseDate,
  ParseNumberComma,
  GenAllDatesFrom,
  ArrayEqual,
  ArrayMinus,
  PadStart,
  PadEnd,
  GetMaxWcwidth,
};
