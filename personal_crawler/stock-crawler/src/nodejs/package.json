{"name": "stockmarket", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "node index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "ssh://git@*************:34786/gitsrv/stockmarket"}, "author": "luzeshu <<EMAIL>>", "license": "ISC", "dependencies": {"bluebird": "3.7.2", "cheerio": "1.0.0", "commander": "12.1.0", "echarts": "5.5.1", "mathjs": "14.0.0", "mysql2": "3.11.5", "request-promise": "4.2.6", "sequelize": "6.37.5", "wcwidth": "1.0.1"}}