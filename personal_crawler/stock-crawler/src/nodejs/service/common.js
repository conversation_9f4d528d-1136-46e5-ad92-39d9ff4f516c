
/**
 * 获取K线的日期范围
 */
async function GetExistDailyInfoFromDB(stockCode) {
  let where = {};
  if (stockCode) {
    where = {stockCode: stockCode};
  }

  let count = await db.StockKLineDaily.count({
    where: where
  });
  console.log('db.StockKLineDaily find count: ', count);
  if (count > 0) {
    let first = await db.StockKLineDaily.findOne({
      where: where,
      order: [['date', 'ASC']]
    });
    let last = await db.StockKLineDaily.findOne({
      where: where,
      order: [['date', 'DESC']]
    });
    return [count, first.date, last.date];
  }

  return [count, null, null];
}

/**
 * 获取某只股票的估值数据的日期范围
 */
async function GetExistValuationFromDB(stockCode) {
  let where = {};
  if (stockCode) {
    where = {stockCode: stockCode};
  }

  let count = await db.StockValuation.count({
    where: where
  });
  console.log('db.StockValuation find count: ', count);
  if (count > 0) {
    let first = await db.StockValuation.findOne({
      where: where,
      order: [['tradeDate', 'ASC']]
    });
    let last = await db.StockValuation.findOne({
      where: where,
      order: [['tradeDate', 'DESC']]
    });
    return [count, first.tradeDate.slice(0, 10), last.tradeDate.slice(0, 10)];
  }

  return [count, null, null];
}

/**
 * 获取K线的日期范围
 */
async function GetExistIndexKLineFromDB(indexCode) {
  let where = {};
  if (indexCode) {
    where = {indexCode: indexCode};
  }

  let count = await db.IndexKLineDaily.count({
    where: where
  });
  console.log('db.IndexKLineDaily find count: ', count);
  if (count > 0) {
    let first = await db.IndexKLineDaily.findOne({
      where: where,
      order: [['date', 'ASC']]
    });
    let last = await db.IndexKLineDaily.findOne({
      where: where,
      order: [['date', 'DESC']]
    });
    return [count, first.date, last.date];
  }

  return [count, null, null];
}

module.exports = {
  GetExistDailyInfoFromDB,
  GetExistValuationFromDB,
  GetExistIndexKLineFromDB,
}
