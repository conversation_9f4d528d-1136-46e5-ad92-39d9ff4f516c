
const eastmoney_v2 = require('../../third_client/eastmoney_v2.js');

async function GetFundFromDB(logger, code) {
  const fund = await db.Fund.findOne({
    where: {fundCode: code},
  });
  return fund;
}

async function GetFundFromThirdClient(output, fundCode) {
  let oldCount = await db.Fund.count();
  let fund;
  let funds = [];
  let body = await eastmoney_v2.getAllEtf();
  for(let r of body.rows) {
    funds.push(r);
    if (r.fundCode == fundCode) {
      fund = r;
    }
  }

  await db.Fund.bulkCreate(funds, {ignoreDuplicates: true});

  let newCount = await db.Fund.count();
  output.setFillFundMeta(oldCount, funds.length, newCount);

  return fund;
}

async function GetFundDailyFromThirdClient(output, fundCode, specifyDate) {
  let oldCount = await db.FundKLineDaily.count({
    where: {fundCode: fundCode}
  });
  if (oldCount > 0) {
    let first = await db.FundKLineDaily.findOne({
      where: {fundCode: fundCode},
      order: [['date', 'ASC']]
    });
    let last = await db.FundKLineDaily.findOne({
      where: {fundCode: fundCode},
      order: [['date', 'DESC']]
    });
    output.setExistDate(first.date, last.date)
  }


  let body = await eastmoney_v2.getEtfKLine(output, fundCode);
  await db.FundKLineDaily.bulkCreate(body.rows, {ignoreDuplicates: true});
  let newCount = await db.FundKLineDaily.count({
    where: {fundCode: fundCode}
  });
  output.setFillDaily(oldCount, body.total, newCount);

  let first;
  let last;
  for (let i of body.rows) {
    let r = new Date(i.date);
    if (!first) {
      first = r;
    }
    if (!last) {
      last = r;
    }
    if (r.getTime() < first.getTime()) {
      first = r;
    }
    if (r.getTime() > last.getTime()) {
      last = r;
    }
  }
  output.setCrawlerDate(first, last);

  let specifyDateData;
  for (let i of body.rows) {
    if (i.date == specifyDate) {
      specifyDateData = i;
      break;
    }
  }

  if (!specifyDateData) {
    specifyDateData = await db.FundKLineDaily.findOne({
      where: {
        fundCode: fundCode,
        date: specifyDate
      }
    });
  }
  output.setSpecifyDateDate(specifyDateData)
}

module.exports = {
  GetFundFromDB,
  GetFundFromThirdClient,
  GetFundDailyFromThirdClient,
};
