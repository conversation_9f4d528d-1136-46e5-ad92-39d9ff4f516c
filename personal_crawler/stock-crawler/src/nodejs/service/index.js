const indexService = require('./indices');
const stockService = require('./stock');
const fundService = require('./fund');
const statService = require('./statistics');
const common = require('./common');

async function GetCodeMetaFromDB(output, code) {
  let index = await indexService.GetIndexFromDB(output, code);
  if (index) {
    output.setName(index.indexName);
    return consts.CODE_TYPE_INDEX;
  }

  let stock = await stockService.GetStockFromDB(output, code);
  if (stock) {
    output.setName(stock.stockName);
    return consts.CODE_TYPE_STOCK;
  }

  let fund = await fundService.GetFundFromDB(output, code);
  if (fund) {
    output.setName(fund.fundName);
    return consts.CODE_TYPE_FUND;
  }

  return consts.CODE_TYPE_UNKNOWN;
}

async function CrawlerCodeMetaFromThirdClient(output, code) {
  let index = await indexService.GetIndexFromThirdClient(output, code);
  if (index) {
    output.setName(index.indexName);
    return consts.CODE_TYPE_INDEX;
  }

  stock = await stockService.GetStockFromThirdClient(output, code);
  if (stock) {
    output.setName(stock.stockName);
    return consts.CODE_TYPE_STOCK;
  }

  fund = await fundService.GetFundFromThirdClient(output, code);
  if (fund) {
    output.setName(fund.fundName);
    return consts.CODE_TYPE_FUND;
  }

  return consts.CODE_TYPE_UNKNOWN
}

async function CrawlerCodeDaily(output, code, type, specifyDate) {
  if (type == consts.CODE_TYPE_STOCK) {
    await stockService.GetStockDailyFromThirdClient(output, code, specifyDate);
  } else if (type == consts.CODE_TYPE_FUND) {
    await fundService.GetFundDailyFromThirdClient(output, code, specifyDate);
  }
}

module.exports = {
  GetCodeMetaFromDB,
  CrawlerCodeMetaFromThirdClient,
  CrawlerCodeDaily,

  // stock
  GetAllStockCodesFromDB: stockService.GetAllStockCodesFromDB,
  GetAllDatesFromDB: stockService.GetAllDatesFromDB,
  GetStockFromDB: stockService.GetStockFromDB,
  GetAllStocksFromDB: stockService.GetAllStocksFromDB,
  CrawlerAllStockMetaFromThirdClient: stockService.CrawlerAllStockMetaFromThirdClient,
  CrawlerAllStockDailyFromThirdClient: stockService.CrawlerAllStockDailyFromThirdClient,
  CrawlerAllStockFundamentalFromThirdClient: stockService.CrawlerAllStockFundamentalFromThirdClient,
  CrawlerAllStockValuationsFromThirdClient: stockService.CrawlerAllStockValuationsFromThirdClient,
  CrawlerAllStockCompanyEventsFromThirdClient: stockService.CrawlerAllStockCompanyEventsFromThirdClient,
  CrawlerSingleStockValuationsFromThirdClient: stockService.CrawlerSingleStockValuationsFromThirdClient,
  CrawlerAllIPOSPORecordsFromThirdClient: stockService.CrawlerAllIPOSPORecordsFromThirdClient,
  CrawlerAllSPORecordsFromThirdClient: stockService.CrawlerAllSPORecordsFromThirdClient,
  CrawlerSingleStockIPORecordsFromThirdClient: stockService.CrawlerSingleStockIPORecordsFromThirdClient,
  // CrawlerSingleStockIPORecords: stockService.CrawlerSingleStockIPORecords,
  CrawlerSingleStockSharesHistoryFromThirdClient: stockService.CrawlerSingleStockSharesHistoryFromThirdClient,
  CrawlerSingleStockNoticesFromThirdClient: stockService.CrawlerSingleStockNoticesFromThirdClient,
  CheckSingleStockDataEntry: stockService.CheckSingleStockDataEntry,
  CrawlerSingleStockUnlimitedFromThirdClient: stockService.CrawlerSingleStockUnlimitedFromThirdClient,
  CrawlerSingleStockPledgesFromThirdClient: stockService.CrawlerSingleStockPledgesFromThirdClient,
  CrawlerSingleStockBonusFromThirdClient: stockService.CrawlerSingleStockBonusFromThirdClient,

  // index
  GetIndexFromDB: indexService.GetIndexFromDB,
  CrawlerAllIndexFromThirdClient: indexService.CrawlerAllIndexFromThirdClient,
  CrawlerAllIndexDailyFromThirdClient: indexService.CrawlerAllIndexDailyFromThirdClient,
  CrawlerAllIndexComponentAdjustmentFromThirdClient: indexService.CrawlerAllIndexComponentAdjustmentFromThirdClient,
  CrawlerSingleIndexComponentAdjustmentFromThirdClient: indexService.CrawlerSingleIndexComponentAdjustmentFromThirdClient,
  CheckSingleIndexDataEntry: indexService.CheckSingleIndexDataEntry,

  CrawlerMarketValuationFromThirdClient: statService.CrawlerMarketValuationFromThirdClient,
  CrawlerIPORecordFromThirdClient: statService.CrawlerIPORecordFromThirdClient,

  StatChart: statService.StatChart,

  GetExistDailyInfoFromDB: common.GetExistDailyInfoFromDB,
};
