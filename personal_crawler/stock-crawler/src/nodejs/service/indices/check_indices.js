
const utils = require('../../nodejs-common/utils.js');
const Sequelize = require('sequelize');
const Op = Sequelize.Op;
const stockService = require('../stock/index.js');
const indexService = require('./indices.js');
const helper = require('./helper.js');
const math = require('mathjs');


// 为了复用对象，不用每个date都调用方法内部去重新读db、重新构造，采用class封装，直接一次初始化
class DBIndexComponents {
  constructor(output, code) {
    this._output = output;
    this._code = code;
    // this._timePoints = timePoints;
    this._actions = [];
  }

  async Init() {
    let indexCode = this._code;
    let output = this._output;

    // 步骤1：先db找出所有添加、移除的调整记录
    let records = await db.IndexComponent.findAll({
      where: {indexCode: indexCode},
      attributes: ['indexCode', 'stockCode', 'addTime', 'delTime'],
      raw: true,
    });
    output.debug('记录记录列表长度: records.length=', records.length);

    // 步骤2：从记录里面复原某个时间点切片
    //  具体逻辑: 
    //    1. 先把所有记录的addTime、delTime等信息拆分成对应的action列表
    //    2. 把action列表按照时间排序，然后维持一个指数成分列表
    //    3. 按时间顺序把addTime的加进去，delTime的移出去
    let actions = [];
    let addTimes = 0;
    let delTimes = 0;
    let addTimeMap = {}; // 看看集中添加、移除的时间
    let delTimeMap = {};
    for (let r of records) {
      if (r.addTime != null && r.addTime != '') {
        actions.push({
          stockCode: r.stockCode, 
          operateTime: new Date(r.addTime),
          type: 'add',
        });
        addTimes += 1;

        if (!addTimeMap[utils.FormatDate(r.addTime)]) {
          addTimeMap[utils.FormatDate(r.addTime)] = {count: 0, stocks: []};
        }
        addTimeMap[utils.FormatDate(r.addTime)].count += 1;
        addTimeMap[utils.FormatDate(r.addTime)].stocks.push(r);
      }

      // TODO 上证50先过滤delTime不详的记录。然后用sse来验证最近时间段的指数
      if (r.delTime != null && r.delTime != '' && r.delTime != '--') {
        let delTime;
        if (r.delTime == '--') {
          // 事实上这个stock在最近记录已经被删除了。但是移除日期不详。。
        } else {
          delTime = new Date(r.delTime);
        }
        actions.push({
          stockCode: r.stockCode, 
          operateTime: delTime,
          type: 'del',
        });
        delTimes += 1;

        if (!delTimeMap[utils.FormatDate(delTime)]) {
          delTimeMap[utils.FormatDate(delTime)] = {count: 0, stocks: []};
        }
        delTimeMap[utils.FormatDate(delTime)].count += 1;
        delTimeMap[utils.FormatDate(delTime)].stocks.push(r);
      }
    }

    // 验证是否跟adjustDates的时间一致。（有调整周期的特殊指数才需要校验这个）
    //   - 但是多了几个新增股数为1的date，这些特殊原因需要进一步人工分析。看起来是新股上市的日期，随机就被加进指数
    // output.debug('集中新增成份股的时间: ', Object.keys(addTimeMap).sort());
    // output.debug('集中移除成份股的时间: ', Object.keys(delTimeMap).sort());
    // output.debug('集中新增成份股的时间: ', utils.SortObjectsByKey(addTimeMap));
    // output.debug('集中移除成份股的时间: ', utils.SortObjectsByKey(delTimeMap));

    // output.debug('specical: ', Object.keys(addTimeMap).reduce((filtered, date) => {
    //   if (addTimeMap[date].count == 1) {
    //     filtered.push(addTimeMap[date].stocks[0]);
    //   }
    //   return filtered;
    // }, []));

    actions.sort((a, b) => a.operateTime.getTime() - b.operateTime.getTime());

    output.debug('构建操作列表长度：actions.length=', actions.length)
    output.debug('其中添加次数: ', addTimes,  '移除次数: ', delTimes);
    this._actions = actions;

    // 构建每日stocks列表的map
    let date2stocksMap = {};
    let lastStocks = [];
    let allDates = Object.keys(addTimeMap).concat(Object.keys(delTimeMap)).sort();
    let startDate = allDates[0];
    // let endDate = allDates[allDates.length - 1];

    // 这里要生成到今日的记录才行
    let timePoints = await getTimePoints(output, indexCode, startDate);
    for (let d of timePoints) {
      if (addTimeMap[d]) {
        lastStocks = lastStocks.concat(addTimeMap[d].stocks.map(r => r.stockCode));
      }

      if (delTimeMap[d]) {
        // 为了确保无误，需要保证移除的每个stock原本就在里面
        for (let s of delTimeMap[d].stocks) {
          let idx = lastStocks.indexOf(s.stockCode);
          utils.Assert(idx >= 0);
          // NOTE: 不能用splice，这是在上个date的原lastStocks上进行修改的
          lastStocks = lastStocks.slice(0, idx).concat(lastStocks.slice(idx+1));
        }
      }
      // output.debug('d: ', d, lastStocks);

      date2stocksMap[d] = lastStocks;
    }

    output.debug('date2stocksMap dates count: ', Object.keys(date2stocksMap).length);
    output.debug('addTimeMap count: ', Object.keys(addTimeMap).length);
    output.debug('delTimeMap count: ', Object.keys(delTimeMap).length);
    output.debug(`generate timepoints: [${timePoints[0]}, ${timePoints[timePoints.length - 1]}]`);

    // output.debug('BuildBySina:');
    // for (let d of this._timePoints) {
    //   let extra = '';
    //   if (addTimeMap[d]) {
    //     extra += `add[${addTimeMap[d].stocks.length}/${addTimeMap[d].stocks.map(r => r.stockCode)}]`;
    //   }
    //   if (delTimeMap[d]) {
    //     extra += `               del[${delTimeMap[d].stocks.length}/${delTimeMap[d].stocks.map(r => r.stockCode)}]`;
    //   }
    //   output.debug(d, date2stocksMap[d].length, extra);
    // }
    // output.debug('date2stocksMap: ', date2stocksMap);

    this._addTimeMap = addTimeMap;
    this._delTimeMap = delTimeMap;
    this.date2stocksMap = date2stocksMap;

    // output.debug('\n\n截至时间: ', utils.FormatTimeLocal(timePoint));
   
    // let components = [];
    // let realActionTimes = 0;

    // output.debug(`截至时间（${utils.FormatDate(endDate)}）实际有效的action次数: `, realActionTimes);
    // // output.debug(actions);

    // output.debug('最终的长度：components.length=', components.length);
    // 
    // output.setTraceComponentsByDb(components);
  }

  async Trace(timePoint) {
    let output = this._output;
    let addTimeMap = this._addTimeMap;
    let delTimeMap = this._delTimeMap;
    let date2stocksMap = this.date2stocksMap;
    let d = timePoint;

    if (!date2stocksMap[d]) {
      output.error(`Trace超出date边界[${timePoint}]`)
    }

    let adds = [];
    if (addTimeMap[d]) {
      adds = addTimeMap[d].stocks.map(r => r.stockCode);
    }
    let dels = [];
    if (delTimeMap[d]) {
      dels = delTimeMap[d].stocks.map(r => r.stockCode);
    }
    // adds: 表示这一天加进去的
    // dels: 表示这一天移除的
    // date2stocksMap[d]: 表示这一天的所有成份股
    return [adds, dels, date2stocksMap[d]];
  }

  // 直接从每日估值表获取，用于手动计算数据的逻辑是否正确
  async _stepCheckValuations(output, date, stocks, shares, valuationMap) {
    // StockValuation表数据最早只有2018/01/02
    let valuations = await db.StockValuation.findAll({
      where: {
        tradeDate: date,
        stockCode: {[Op.in]: stocks},
      },
      raw: true,
    });

    let valuationMapCheck = {};
    for (let v of valuations) {
      valuationMapCheck[v.stockCode] = v;
    }

    // 校验
    for (let s of shares) {
      if (!valuationMapCheck[s.stockCode]) {
        output.info(`[CheckShares] db.StockValuation not exist[${date}/${s.stockCode}]`);
        continue
      }

      let checkV = valuationMapCheck[s.stockCode];
      if (checkV.totalShares != s.totalShares) {
        output.warn(`[CheckShares] Err[${date}/${s.stockCode}/totalShares/${checkV.totalShares} != ${s.totalShares}]`);
        continue;
      }

      if (checkV.freeSharesA != s.listedAShares) {
        output.warn(`[CheckShares] Err[${date}/${s.stockCode}/freeSharesA/${checkV.freeSharesA} != ${s.listedAShares}]`);
        continue;
      }

      let v = valuationMap[s.stockCode];
      if (!v) {
        output.error(`[CheckShares] valuationMap not exist[${date}/${s.stockCode}]`);
        continue;
      }

      // 这里市值checkV.totalMarketCap应该是不止A股。 而计算的totalMarketCapA是单单只有A股的
      // TODO 后续可以验证totalMarketCap - v.totalMarketCapA 之后，剩下的跟B/H/D股股本的关系
      // if (checkV.totalMarketCap != v.totalMarketCapA) {
      //   output.warn(`[CheckShares] Err[${date}/${s.stockCode}/totalMarketCap/${checkV.totalMarketCap} != ${v.totalMarketCapA}]`);
      //   continue;
      // }

      if ((+checkV.notlimitedMarketCapA).toFixed(2) != v.notlimitedMarketCapA.toFixed(2)) {
        output.warn(`[CheckShares] Err[${date}/${s.stockCode}/notlimitedMarketCapA/${checkV.notlimitedMarketCapA} != ${v.notlimitedMarketCapA}]`);
        continue;
      }

      // output.debug(`[CheckShares] StockValuation success[${date}/${s.stockCode}]`);
    }
    return shares;
  }

  // 步骤：获取某个date、某批stocks的shares
  async _stepGetShares(output, date, stocks) {
    // // 正向验证老八股时的指数，先临时拿上市时的shares当成${date}的shares
    // let shares = await db.StockShares.findAll({
    //   where: {
    //     stockCode: {[Op.in]: stocks},
    //     // changeReason: {[Op.like]: '首发%'},
    //     changeReason: {[Op.like]: '首发A股%'},
    //   },
    //   raw: true,
    // });
    // output.debug('CrawlerSingleStockSharesHistoryFromThirdClient...')
    // for (let s of stocks) {
    //   await stockService.CrawlerSingleStockSharesHistoryFromThirdClient(output, s);
    // }
    // output.debug('CrawlerSingleStockSharesHistoryFromThirdClient finish')

    // 上面的方式在验证1991-07-15日期的时候已经不适用了。中间经过一些拆分，所以应该取每个stocks的最近变动记录
    let shares = await db.StockShares.findAll({
      where: {
        stockCode: {[Op.in]: stocks},
        listingDate: {
          [Op.lte]: date + ' 00:00:00',
        }
      },
      order: [['listingDate', 'DESC']],
      raw: true,
    });

    let sharesMap = {};
    for (let s of shares) {
      if (sharesMap[s.stockCode]) {
        continue;
      }
      sharesMap[s.stockCode] = s;
    }
    // sharesMap['600655'] = this.getFixShares('600655', date);

    shares = Object.keys(sharesMap).map(k => sharesMap[k]);

    // output.debug(`shares[${date}]: `, shares);
    // output.debug(`sharesMap[${date}]: `, sharesMap);
    return shares;
  }

  getFixShares(stockCode, date) {
    if (stockCode == '600655') {
      return {
        stockCode: stockCode,
        totalShares: 10000000,
        unlimitedShares: 10000000,
        listingDate: '1990-12-19',
      };
    }
  }
  
  getFixPrice(stockCode, date) {
    if (stockCode == '600655') {
      if (date == '1991-07-15') {
        return '11.57';
      }
      if (date == '1991-07-16') {
        return '11.69';
      }
      if (date == '1991-07-17') {
        return '11.81';
      }
      if (date == '1991-07-18') {
        return '11.92';
      }
      if (date == '1991-07-19') {
        return '12.04';
      }
    }
  }

  // 步骤：获取某个date、某批stocks的市值
  async _stepGetValuations(output, date, stocks) {
    let valuationMap = {};
    for (let s of stocks) {
      valuationMap[s] = {stockCode: s};
    }

    // 方法1：直接获取每日估值记录
    // StockValuation表数据最早只有2018/01/02
    // let valuations = await db.StockValuation.findAll({
    //   where: {
    //     tradeDate: date,
    //     stockCode: {[Op.in]: stocks},
    //   },
    //   raw: true,
    // });
    // for (let v of valuations) {
    //   valuationMap[v.stockCode] = v;
    // }
    // // output.debug('valuations.length: ', valuations.length);

    let rawDate = date;
    if (date == '1990-12-18') {
      date = '1990-12-19';
    }

    // 方法2：通过每日日K估价 * shares
    // NOTE 存在问题：shares的复现计算目前还不稳妥，这个环节可能出现数据出错（需要人工定位）
    let klines = await db.StockKLineDaily.findAll({
      where: {
        date: date,
        stockCode: {[Op.in]: stocks},
      },
      raw: true,
    });

    let klineMap = {};
    for (let k of klines) {
      klineMap[k.stockCode] = k;

      valuationMap[k.stockCode].price = k.closingPrice;
      // if (rawDate == '1990-12-18') {
      //   valuationMap[k.stockCode].price = k.openingPrice;
      // }
      // 搜狐证券修正，修完误差更大。
      if (k.stockCode == '600656' || k.stockCode == '600654'|| k.stockCode == '600602' ) {
        valuationMap[k.stockCode].price = '' + (k.closingPrice / 100);
      }
      if (k.stockCode == '600653' || k.stockCode == '600652' || k.stockCode == '600651' || k.stockCode == '600601') {
        valuationMap[k.stockCode].price = '' + (k.closingPrice / 10);
      }
    }
    valuationMap['600655'] = {
      price: this.getFixPrice('600655', date),
      // shares: this.getFixShares('600655', date),
    };

    // output.debug(`klineMap[${date}]:`, klineMap)
    // 手动修复
    if (date == '1990-12-19' && valuationMap['600652']) {
      valuationMap['600652'].price = '193.00';
    }
    if (date == '1990-12-20'&& valuationMap['600651']) {
      valuationMap['600651'].price = '320.30';
    }

    // 获取sharse
    let shares = await this._stepGetShares(output, date, stocks)
    
    // let sharesMap = {};
    for (let s of shares) {
      // output.info('before: ', s);
      s.nonFreeShares = +s.nonFreeShares;
      s.unlimitedShares = +s.unlimitedShares;
      s.limitedShares = +s.limitedShares;
      s.totalShares = +s.totalShares;
      s.listedAShares = +s.listedAShares;
      s.bFreeShares = +s.bFreeShares;
      s.hFreeShares = +s.hFreeShares;
      s.otherFreeShares = +s.otherFreeShares;
      // output.info(s);

      // 断言几个等式
      utils.Assert(s.nonFreeShares == 0);
      utils.Assert(s.totalShares == s.unlimitedShares + s.limitedShares); // 总股本（包括境内境外） = 已流通股本 + 流通受限股
      utils.Assert(s.unlimitedShares == s.listedAShares + s.bFreeShares + s.hFreeShares + s.otherFreeShares); // 已流通股本 = 已上市流通A股 + B股 + H股
      utils.Assert(s.bFreeShares == 0 || s.hFreeShares == 0);  // TODO 找到B、H股同时存在的情况，来验证上面的等式
      // utils.Assert(s.otherFreeShares == 0); 

      // sharesMap[s.stockCode] = s.totalShares;

      // if (s.stockCode == '600602') {
      //   s.totalShares = s.totalShares * 4.2;
      // }
      valuationMap[s.stockCode].shares = s.totalShares;
      // TODO 
      valuationMap[s.stockCode].unlimitedShares = s.unlimitedShares;
      valuationMap[s.stockCode].listedAShares = s.listedAShares;
      // 这里只能手动计算A股总市值。能获取到的数据：通过totalShares - bFreeShares - hFreeShares;
      valuationMap[s.stockCode].totalSharesA = s.totalShares - s.bFreeShares - s.hFreeShares - s.otherFreeShares;
      valuationMap[s.stockCode].ipoDate = s.listingDate;

      valuationMap[s.stockCode].totalMarketCap = valuationMap[s.stockCode].shares * valuationMap[s.stockCode].price;
      // 这里下面两个值都没有考虑境外B股/H股有没有非流通限售股的情况。目前也没这个数据
      valuationMap[s.stockCode].notlimitedMarketCapA = valuationMap[s.stockCode].listedAShares * valuationMap[s.stockCode].price;
      valuationMap[s.stockCode].totalMarketCapA = valuationMap[s.stockCode].totalSharesA * valuationMap[s.stockCode].price;
    }
    // output.debug(`valuationMap:`, valuationMap)
 
    await this._stepCheckValuations(output, date, stocks, shares, valuationMap);
    
    return valuationMap;
  }


  // indexCode: 计算的指数代码
  // lastDate: 上一个交易日的日期（临时传入。后续构建专门方法来判断date是否交易日、是否特殊节日）
  // date: 要计算指数的日期
  // lastDateIndex: 上一交易日的基期值
  // expected: 这一天实际的指数值（作为断言判断）
  // basicStocks: 由于sina和sse两边数据不一致。这里传参来控制，看看哪个更接近
  async Calculate(output, indexCode, lastDate, date, lastDateIndex, expected, basicStocks) {
    // // step1: 获取股票日K数据 - 股价
    let stocks = await db.Stock.findAll({
      raw: true,
    });
    let stockMap = {};
    for (let s of stocks) {
      stockMap[s.stockCode] = s;
    }

    // step2: 获取股票每日总市值
    let valuationMap = {};
    valuationMap[lastDate] = await this._stepGetValuations(output, lastDate, basicStocks)
    valuationMap[date] = await this._stepGetValuations(output, date, basicStocks)
    // output.debug('basicStocks: ', basicStocks);
    // output.debug('valuationMap: ', valuationMap);
    // return;
    

    // step3: 获取成份股
    let allBStockCode = [];
    // 直接使用basicStocks
    // let comps = await db.IndexComponentSse.findAll();
    for (let c of basicStocks) {
      // if (!klineMap[c]) {
      //   throw new Error('Not Found Kline ' + c);
      // }

      if (!valuationMap[lastDate][c] || !valuationMap[date][c]) {
        // throw new Error('Not Found Valuation ' + c);
        // output.error('Not Found Valuation ' + c);
        allBStockCode.push(c);
      }
    }
    // output.debug('comps.length: ', comps.length);


    // step3: 构建B股的总市值
    ////   let shares = await db.BStockShares.findAll({
    ////     where: {stockCode: {[Op.in]: allBStockCode}},
    ////     raw: true,
    ////   });
    ////   let sharesMap = {};
    ////   for (let s of shares) {
    ////     sharesMap[s.stockCode] = s;
    ////   }

    ////   // output.debug('sharesMap: ', sharesMap);
    ////   // output.debug('leng: ', Object.keys(sharesMap).length, allBStockCode.length);

    ////   output.debug('BStocks: ', allBStockCode);
    ////   let klines = await db.StockKLineDaily.findAll({
    ////     where: {
    ////       stockCode: {[Op.in]: allBStockCode},
    ////       date: lastDate,
    ////     },
    ////     raw: true,
    ////   });

    ////   for (let k of klines) {
    ////     if (!sharesMap[k.stockCode]) {
    ////       output.debug('not found K :', k.stockCode, sharesMap[k.stockCode]);
    ////     }
    ////     let p = +k.closingPrice;
    ////     let s = +sharesMap[k.stockCode].unlimitedShares;
    ////     let t = p * s;
    ////     // output.debug('p: ', p);
    ////     // output.debug('s: ', s);
    ////     // output.debug('t: ', t);

    ////     valuationMap[lastDate][k.stockCode] = {notlimitedMarketCapA: t, unlimitedShares: s};
    ////     // output.debug('cal date1: ', valuationMap[lastDate][k.stockCode].totalMarketCap);
    ////   }

    ////   let klines2 = await db.StockKLineDaily.findAll({
    ////     where: {
    ////       stockCode: {[Op.in]: allBStockCode},
    ////       date: date,
    ////     },
    ////     raw: true,
    ////   });

    ////   for (let k of klines2) {
    ////     let p = +k.closingPrice;
    ////     let s = +sharesMap[k.stockCode].unlimitedShares;
    ////     let t = p * s;
    ////     // output.debug('p: ', p);
    ////     // output.debug('s: ', s);
    ////     // output.debug('t: ', t);

    ////     valuationMap[date][k.stockCode] = {notlimitedMarketCapA: t, unlimitedShares: s};
    ////     // output.debug('cal date1: ', valuationMap[date][k.stockCode].totalMarketCap);
    ////   }


    let calcIndex, lastValue, todayValue; // lastValue/todayValue表示公式中的分母。上证综指表示市值，上证50表示调整市值等
    // step5: 正式计算验证。根据不同的indexCode做逻辑分发
    if (indexCode == "000001") {
      // 上证综指
      [calcIndex, lastValue, todayValue] = this.calculate000001(output, basicStocks, stockMap, valuationMap, lastDate, date, lastDateIndex, expected);
    } else if (indexCode == "000016") {
      // 上证50
      [calcIndex, lastValue, todayValue] = await this.calculate000016(output, basicStocks, stockMap, valuationMap, lastDate, date, lastDateIndex, expected);
    }

    // output.debugKlineDiff('calc', '', lastDate, date, lastDateIndex, expected, lastValue, todayValue);
    output.debugKlineDiff('calc', '', lastDate, date, lastDateIndex, output.formatExpected(calcIndex, expected, 2));
    // output.debug(`[${date}] calculate(6)[${calcIndex.toFixed(6)}] calculate(2)[${calcIndex.toFixed(2)}] expected[${expected}]`);

    return calcIndex;
  }


  async calculate000016(output, basicStocks, stockMap, valuationMap, lastDate, date, lastDateIndex, expected) {
    let appends = [
      // output.appendAdjustShares('调整股本', '总股本', '流通比')
      output.appendAdjustShares('调整股本', '流通股本', '流通比')
    ];
    output.debugKlineDiff('代码', '名称', '昨日', '今日', '昨价', '今价', '上日调整市值', '今日调整市值', appends, '分时');

    let totalAdjustShares = 0;
    let totalAdjustCap = 0;
    let ratio = 1;
    let i = 0;
    let filter = 0;


    // 前置计算校验
    let baseValueMap = {};
    let curValueMap = {};
    for (let c of basicStocks) {
      let s = stockMap[c];
      if (!valuationMap[lastDate][c] || !valuationMap[date][c]) {
        ++ filter;
        if (!s) {
          output.debug('no meta!: ', c);
        } else {
          output.debug('filter: ', c, s.stockName, s.stockCity, s.stockType);
        }
        output.error('miss record: ', c);
        return;
      }

      baseValueMap[c] = valuationMap[lastDate][c];
      curValueMap[c] = valuationMap[date][c];
    }

    let [orderStocks, lastAdjustCap, adjustCap, totalShares] = helper.calculate(basicStocks, stockMap, lastDate, date, baseValueMap, curValueMap);
    for (let s of orderStocks) {
      // 先打印一遍日K
      // 日志记录累计值的差异没意义，应该记录单只股票的差异，以及最终加总的差异
      let appends = [
        // output.appendAdjustShares(adjustShares, todayShares, freeRatio)
        output.appendAdjustShares(s.adjustShares, s.freeShares, s.freeRatio)
      ];
      output.debugKlineDiff(s.code, s.stockName, s.lastDate, s.date, s.lastPrice, s.price,
        s.lastDateAdjustCap, s.adjustCap, appends, '');
    }


    /*

    // 打印分时价格
    let fenshiSeries = helper.getEmptyFenshiMap();
    for (let s of orderStocks) {
      let fenshi = await db.StockValueDetail.findAll({
        where: {
          stockCode: s.code,
          date: date,
        },
        order: [['time', 'ASC']],
        raw: true,
      })
      let fenshiMap = {};
      let lastPrice;
      for (let f of fenshi) {
        fenshiMap[f.time] = f.price;
      }
      // output.debug("fenshiMap: ", s.code, fenshiMap)
      // output.debug("fenshiSeries: ", fenshiSeries)

      for (let t of fenshiSeries) {
        if (fenshiMap[t.time]) {
          lastPrice = fenshiMap[t.time];
        }
        t.stocks[s.code] = lastPrice;
      }


      // for (let f of fenshi) {
      //   // output.debugKlineDiff(s.code, s.stockName, s.lastDate, f.time, s.lastPrice, f.price, '', '', '');
      // }
    }

    for (let t of fenshiSeries) {
      output.debug('t: ', t.time, t.stocks['688599']);
    }

    // // 过滤fenshiMap存在缺点数据的Stock
    // let filterCount = 0;
    // let orderTimes = Object.keys(fenshiMap);
    // orderTimes.sort((a, b) => a.replaceAll(":", "") - b.replaceAll(":", ""));
    // for (let time of orderTimes) {
    //   if (fenshiMap[time].length != orderStocks.length) {
    //     output.debug('filter: ', time, fenshiMap[time]);
    //     // delete fenshiMap[time];
    //     filterCount += 1;
    //   }
    // }
    // output.debug('filter count: ', filterCount);
    // output.debug("fenshiMap: ", Object.keys(fenshiMap))


    // index分时数据
    let indexFenshi = await db.IndexValueDetail.findAll({
      where: {
        indexCode: "000016",
        date: date,
      },
      order: [['time', 'ASC']],
      raw: true,
    });
    let fMap = [];
    for (let f of indexFenshi) {
      let stocks = [];
      for (let s of orderStocks) {
        stocks.push({
          stockCode: s.code,
          price: fenshiSeries[f.time][s.code],
        });
      }

      fMap.push({
        time: time,
        stocks: stocks,
      });
    }

*/

    appends = [
      output.appendAdjustShares(totalShares, totalShares)
    ];
    output.debugKlineDiff('expect', '', lastDate, date, lastDateIndex, expected, lastAdjustCap, adjustCap, appends);

    ratio = (adjustCap-lastAdjustCap) / lastAdjustCap;
    let calcIndex = lastDateIndex * (1+ratio);
    return [calcIndex, lastAdjustCap, adjustCap];
  }

  calculate000001(output, basicStocks, stockMap, valuationMap, lastDate, date, lastDateIndex, expected) {
    let appends = [
      output.appendShares('总股本', '总股本'),
      output.appendUnlimitCap('流通市值')
    ];
    output.debugKlineDiff('代码', '名称', '昨日', '今日', '昨价', '今价', '上日总市值', '今日总市值', appends);

    let ratio = 1;
    let totalValuations = 0; // 上一日，所有成份股的总市值。作为分母
    let totalA = 0;
    let totalB = 0;
    let diffValuations = 0; // 所有成份股的今日较上一日的差值总和。作为分子
    let diffA = 0;
    let diffB = 0;
    let i = 0;
    let filter = 0;
    let totalShares = 0;
    let totalUnlimitCap = 0;
    for (let c of basicStocks) {
      let s = stockMap[c];
      if (!valuationMap[lastDate][c] || !valuationMap[date][c]) {
        ++ filter;
        if (!s) {
          output.debug('no meta!: ', c);
        } else {
          output.debug('filter: ', c, s.stockName, s.stockCity, s.stockType);
        }
        continue;
      }

      let lastDateCap = (+valuationMap[lastDate][c].totalMarketCapA);
      let lastDateShares = (+valuationMap[lastDate][c].shares);
      let todayCap = (+valuationMap[date][c].totalMarketCapA);
      let todayShares = (+valuationMap[date][c].shares);
      let lastPrice = valuationMap[lastDate][c].price;
      let price = valuationMap[date][c].price;
      let unlimitCap = (+valuationMap[date][c].notlimitedMarketCapA);

      let addDiff = todayCap - lastDateCap;

      if (s.stockType == 'B') {
        totalB += lastDateCap;
        diffB += addDiff;
      } else {
        totalA += lastDateCap;
        diffA += addDiff;
      }

      totalValuations += lastDateCap;
      diffValuations += addDiff;
      totalShares += todayShares;
      totalUnlimitCap += unlimitCap;
      // 日志记录累计值的差异没意义，应该记录单只股票的差异，以及最终加总的差异
      // output.debug(i++, c, `diffValuations=${diffValuations}`, `totalValuations=${totalValuations}`);
      // output.debug('A:', totalA, diffA);
      // output.debug('B:', totalB, diffB);
      appends = [
        output.appendShares(lastDateShares, todayShares),
        output.appendUnlimitCap(unlimitCap),
      ];
      output.debugKlineDiff(c, s.stockName, lastDate, date, lastPrice, price, lastDateCap, todayCap, appends);
    }
    appends = [
      output.appendShares(totalShares, totalShares),
      output.appendUnlimitCap(totalUnlimitCap),
    ];
    output.debugKlineDiff('sum', '', lastDate, date, lastDateIndex, expected, totalValuations, totalValuations+diffValuations, appends);

    ratio = diffValuations / totalValuations;
    // output.debug('filter: ', filter, 'total: ', basicStocks.length);
    // output.debug('\nfinal ratio: ', ratio);
    // output.debug(`suppose divider(totalMarketCap/currentIndex*100): `, ((totalValuations+diffValuations)/expected*100).toFixed(10));
    // output.debug(`fomular calc: `, ((totalValuations+diffValuations)/1458469100*100).toFixed(10));

    let calcIndex = lastDateIndex * (1+ratio);
    return [calcIndex, totalValuations, totalValuations+diffValuations];
  }
}

// 常见的中证指数样本调整周期: 每半年调整一次，样本调整实施时间分别为每年6月和12月的第二个星期五的下一交易日。
// NOTE 人工对照规则填充日期
const adjustDates = [
  // new Date('2015-06-15'),
  new Date('2015-12-14'),
  new Date('2016-06-13'),
  new Date('2016-12-12'),
  new Date('2017-06-12'),
  new Date('2017-12-11'),
  new Date('2018-06-11'),
  new Date('2018-12-17'),
  new Date('2019-06-17'),
  new Date('2019-12-16'),
  new Date('2020-06-15'),
  new Date('2020-12-14'),
  new Date('2021-06-15'),
  new Date('2021-12-13'),
  new Date('2022-06-13'),
  new Date('2022-12-12'),
  new Date('2023-06-12'),
  new Date('2023-12-11'),
];

// 临时调整规则: 特殊情况下将对指数进行临时调整。当样本退市时，将其从指数样本中剔除。
// 样本公司发生收购、合并、分拆等情形的处理，参照计算与维护细则处理。

class RuleIndexComponents {
  constructor(output, code, timePoints) {
    this._output = output;
    this._code = code;
    this._timePoints = timePoints;
    this._actions = [];
  }

  async Init() {
    // 步骤1: 样本空间
    await this.stepGetSampleSpace();
    await this.stepGetSampleMethod();
  }

  async Trace(timePoint) {
    let output = this._output;
    let listingMap = this.listingMap;
    let stMap = this.stMap;
    let date2stocksMap = this.date2stocksMap;
    let d = timePoint;

    let adds = [];
    if (listingMap[d]) {
      adds = listingMap[d].stocks;
    }
    let dels = [];
    if (stMap[d]) {
      dels = stMap[d].stocks;
    }
    return [adds, dels, date2stocksMap[d]];
  }
}

class RuleIndexComponents000001 extends RuleIndexComponents {
  constructor(...args) {
    super(...args);
  }

  // 步骤1（样本空间）: 上证综合指数的样本空间由在上海证券交易所上市的股票和红筹企业发行的存托凭证组成。ST、*ST 证券除外。
  async stepGetSampleSpace() {
    let output = this._output;

    // step1: 首先，获取上海证券交易所上市的所有股票
    let stocks = await db.Stock.findAll({
      where: {
        stockCity: 'SH',
        stockType: 'A',
      },
      attributes: ['stockCode', 'stockName', 'isDanger'],
      raw: true,
    });
    output.debug('stocks count:', stocks.length);
    // 构造样本空间map
    let stockSpaceMap = {};
    stocks.map(s => {
      stockSpaceMap[s.stockCode] = s;
    });

    // step2: 获取所有股票的上市、st记录 => 构造以date为key，action(st/listing)为value的时间切片记录
    let listingMap = {};
    let listings = await db.GeneratedListingRecord.findAll({
      where: {
        listingType: {
          [Op.or]: ['1', '3', '4'], // 新股上市、恢复上市、转板上市
        },
      },
      raw: true,
    });
    output.debug('listing count: ', listings.length);
    listings.map(l => {
      if (!stockSpaceMap[l.stockCode]) {
        // 过滤不在样本空间内的
        return;
      }

      if (!listingMap[l.listingDate]) {
        listingMap[l.listingDate] = {count: 0, stocks: []};
      }
      listingMap[l.listingDate].count += 1;
      listingMap[l.listingDate].stocks.push(l.stockCode);
    });

    // step3: 同上，构造st对于的map
    let stMap = {};
    let sts = await db.GeneratedStRecord.findAll({
      where: {stDirection: '1'},
      raw: true,
    });
    output.debug('st count: ', sts.length);
    sts.map(l => {
      if (!stockSpaceMap[l.stockCode]) {
        // 过滤不在样本空间内的
        return;
      }

      if (!stMap[l.noticeDate]) {
        stMap[l.noticeDate] = {count: 0, stocks: []};
      }
      stMap[l.noticeDate].count += 1;
      stMap[l.noticeDate].stocks.push(l.stockCode);
    });

    // 构建每日stocks列表的map
    let date2stocksMap = {};
    let lastStocks = [];
    for (let d of this._timePoints) {
      if (listingMap[d]) {
        lastStocks = lastStocks.concat(listingMap[d].stocks.map(r => r.stockCode));
      }

      if (stMap[d]) {
        // 为了确保无误，需要保证移除的每个stock原本就在里面
        for (let s of stMap[d].stocks) {
          let idx = lastStocks.indexOf(s.stockCode);
          utils.Assert(idx >= 0);
          // NOTE: 不能用splice，这是在上个date的原lastStocks上进行修改的
          lastStocks = lastStocks.slice(0, idx).concat(lastStocks.slice(idx+1));
        }
      }

      date2stocksMap[d] = lastStocks;
    }

    // output.debug('BuildByRule:');
    // for (let d of this._timePoints) {
    //   let extra = '';
    //   if (listingMap[d]) {
    //     extra += `add[${listingMap[d].stocks.length}/${listingMap[d].stocks}]`;
    //   }
    //   if (stMap[d]) {
    //     extra += `               del[${stMap[d].stocks.length}/${stMap[d].stocks}]`;
    //   }
    //   output.debug(d, date2stocksMap[d].length, extra);
    // }

    this.listingMap = listingMap;
    this.stMap = stMap;
    this.date2stocksMap = date2stocksMap;
  }

  // 步骤2（选样方法）: 上证综合指数选取所有样本空间内证券作为指数样本。
  async stepGetSampleMethod() {

  }

  // 步骤3：计算指数公式
  async stepCalcIndex() {
    // 报告期指数 = (报告期样本总市值 / 除数) * 100
    // 其中，
    //    - 总市值 = ∑(证券价格×发行股本数)。
    //    - 除数修正方法参见指数计算与维护细则。http://www.sse.com.cn/market/sseindex/calculation/
  }
}

function newRuleIndexComponents(output, code, timePoints) {
  if (code == '000001') {
    return new RuleIndexComponents000001(output, code, timePoints)
  }
}

const traceComponentsFuncMap = {
'000001': async function(output, timePoint) {
  // 上证综合指数（000001）
  // https://www.csindex.com.cn/#/indices/family/detail?indexCode=000001
  // 实现编制规则:
  // 基日和基点: 该指数以1990年12月19日为基日，以100点为基点。

  // 步骤3（指数样本调整）: 
  //   - 调整周期: 既然是全样本，好像就没这个概念了。
  //   - 上市以来日均总市值排名在沪市前 10 位的证券于上市满三个月后计入指数
  //     - 其他证券于上市满一年后计入指数。
  //   - 样本被实施风险警示的，从被实施风险警示措施次月的第二个星期五的下一交易日起将其从指数样本中剔除；
  //     - 被撤销风险警示措施的证券，从被撤销风险警示措施次月的第二个星期五的下一交易日起将其计入指数。
  //   - 当样本退市时，将其从指数样本中剔除。样本公司发生收购、合并、分拆、停牌等情形的处理，参照指数计算与维护细则处理。
},

'000985':  async function(output, timePoint) {
  // 中证全指指数（000985）
  // https://www.csindex.com.cn/#/indices/family/detail?indexCode=000985
  // 实现编制规则
},

'399808': async function(output, timePoint) {
  // 中证新能源指数（399808）
  // https://www.csindex.com.cn/#/indices/family/detail?indexCode=399808
  // 实现编制规则: 
  // 步骤1（样本空间）: 是中证全指指数的样本空间
  // TODO

  // 步骤2: 样本空间内证券，按照过去一年的日均成交金额，由高到低排名，剔除排名靠后的20%证券
  //   - 这里需要以timePoint往前找到最近的一个调整日期，以调整日期为起始点，往前回溯的一年作为「过去一年的日均成交金额」的参考时间
  //   - 指数创建起始日期：
  //   - 指数调整周期：指数样本每半年调整一次，样本调整实施时间分别为每年6月和12月的第二个星期五的下一交易日。
  //   - 指数临时调整时间：
  // TODO

  // 步骤3: 步骤2剩余的证券中，选取涉及可再生能源生产、新能源应用、新能源存储以及新能源交互设备等业务的上市公司证券进入新能源主题
  // TODO
  
  // 步骤4: 步骤3选出的证券，再按照过去一年日均总市值由高到低排名，选取排名在前80的证券作为指数样本
  //   - 时间点的设置规则同步骤2
  // TODO
  let data = {};
  output.setTraceComponentsByRule(data);
}

};

async function TraceIndexComponentsByRule(output, indexCode, timePoint) {
  if (Object.keys(traceComponentsFuncMap).indexOf(indexCode) >= 0) {
    return traceComponentsFuncMap[indexCode](output, timePoint);
  }

  throw new Error(`Unsupported IndexCode[${indexCode}]`);
}

// 根据支持的code，返回调整周期时间节点
async function getTimePoints(output, code, startDate, endDate) {
  // if (code == '000001') {
  //   // 上证综合指数
  //   // 以1990/12/19为基日，没有调整周期，等于每个date都生成校验
  //   return utils.GenAllDatesFrom('1990-12-19');
  // }
  // return adjustDates.concat(new Date());

  let allDates = utils.GenAllDatesFrom(startDate, endDate);
  let indices = await db.IndexKLineDaily.findAll({
      where: {
        indexCode: code,
        date: {[Op.in]: allDates},
      },
      // attributes: ['indexCode', 'closingPrice', 'changePercent', 'date'],
      attributes: ['date'],
      raw: true,
  });

  let checks = [];
  for (let i of indices) {
    // 经过indices过滤，保留交易日期的部分
    checks.push(i.date);
  }

  return checks
}

async function CheckIndex(output, code) {

}


// step1: 校验sina爬取指数成分数据，与指数编制规则构建的成分数据。是否一致。
async function stepCheckComponentsBySinaAndRule(output, timePoints, dbComponents, ruleComponents) {
  output.debug('StartTrace:');
  for (let t of timePoints) {
    output.startDiffCheck(t);
    let hasChange = false;

    // 方式1: 从db.IndexComponent表，回溯任一时刻的指数股票构成
    // await TraceIndexComponentsByDb(output, code, t);
    let [adds, dels, all] = await dbComponents.Trace(t);
    let extra = '';
    if (adds.length) {
      hasChange = true;
      extra += `add[${adds.length}/${adds.sort()}]`
    }
    if (dels.length) {
      hasChange = true;
      extra += `    del[${dels.length}/${dels.sort()}]`
    }

    // 方式2: 根据指数编制方案实现的规则，回溯任一时刻的指数股票构成
    // await TraceIndexComponentsByRule(output, code, t);
    let [adds2, dels2, all2] = await ruleComponents.Trace(t);
    let extra2 = '';
    if (adds2.length) {
      hasChange = true;
      extra2 += `add[${adds2.length}/${adds2.sort()}]`
    }
    if (dels2.length) {
      hasChange = true;
      extra2 += `    del[${dels2.length}/${dels2.sort()}]`
    }

    // 简化日志
    // if (!hasChange) {
    //   continue;
    // }
    // if (utils.ArrayEqual(adds, adds2) && utils.ArrayEqual(dels, dels2)) {
    //   continue;
    // }

    output.debug(t);
    output.debug(`   > sina[${all.length}] ${extra}`);
    output.debug(`   < rule[${all2.length}] ${extra2}`);
  }
}

async function stepCheckComponentsBySinaAndSse(output, code, dbComponents, date) {
  // 校验最后一天的sina和sse差别
  // let date = '2024-03-05';
  let [adds, dels, all] = await dbComponents.Trace(date);
  output.debug('date:', date);
  output.debug('sina adds:', adds.length);
  output.debug('sina dels:', dels.length);
  output.debug('sina all:', all.length);
  let sinaStocks = all;
  let sses = await db.IndexComponentSse.findAll({
    where: {indexCode: code},
    raw: true,
  });

  // TODO hardcode
  // sinaStocks = ['600601', '600602', '600651', '600652', '600653', '600654', '600656', '600655'];
  // sinaStocks = ['600601', '600602', '600651', '600652', '600653', '600654', '600656'];
  // sinaStocks = ['600601', '600602', '600651', '600652', '600656'];
  // sinaStocks = ['600601', '600602', '600651', '600656'];
  // sinaStocks = ['600601', '600602', '600652', '600656'];
  // sinaStocks = ['600601', '600602', '600656'];
  
  let sseStocks = sses.map(r => r.stockCode);
  output.debug(`sina(${date})/${sinaStocks.length}: `, sinaStocks);
  // output.debug(`sse(最新)/${sseStocks.length}: `, sseStocks);
  output.debug(`sina(${date})-sse(最新)`, utils.ArrayMinus(sinaStocks, sseStocks));
  // output.debug(`sse(最新)-sina(${date})`, utils.ArrayMinus(sseStocks, sinaStocks));

  return [sinaStocks, sseStocks];
}

async function CheckSingleIndexDataEntry(output, code) {
  // return CheckIndex(output, code);
  // let bounds = ['2024-03-28', '2024-03-29'];
  // let bounds = ['2024-04-18', '2024-04-23'];
  let bounds = ['2024-05-30', '2024-05-31'];
  let startDate = bounds[0];
  let endDate = bounds[1];

  // 按照指数的（起始时间，调整周期，临时调整时间点）等信息，挑选出几个关键的回溯时间点
  let timePoints = await getTimePoints(output, code, startDate, endDate)
  let dbComponents = new DBIndexComponents(output, code, timePoints);
  await dbComponents.Init();

  // let ruleComponents = newRuleIndexComponents(output, code, timePoints);
  // await ruleComponents.Init();

  // 基础日期、指数数据
  // 1990-12-19为基期，100点为基点
  // 首日600602收盘价365.70，股本只有1992-12-01的3w股
  // - sina数据似乎也不准。。从日K表捞出来的1990-12-19当天还有600601、600651、600656
  //  1990-12-19 上交所上市的应该有8只股票（老八股）: https://xueqiu.com/1497330209/148729253
  //    其中600652等到1990-12-20才有第一天日K记录
  //    其中600654等到1990-12-21才有第一天日K记录
  //    其中600653等到1991-01-08才有第一天日K记录
  //    其中600655等到1992-09-02才有第一天日K记录
  //  但实际上市要更早。。: https://xueqiu.com/4399540310/224843787
  // let list = [
  //   ['1990-12-19', 99.98],
  //   ['1990-12-20', 104.39],
  //   ['1990-12-21', 109.13],

  //   // ['1991-07-15', 133.144],
  //   // ['1991-07-16', 134.466],
  //   // ['1991-07-17', 135.81],

  //   // ['2024-01-25', 2906.11],
  //   // ['2024-01-26', 2910.22],
  //   // ['2024-01-29', 2883.36],
  //   // ['2024-01-30', 2830.53],
  //   // ['2024-01-31', 2788.55],
  //   // ['2024-02-01', 2770.74],
  //   // ['2024-02-02', 2730.15],
  //   // ['2024-02-05', 2702.19],
  //   // ['2024-02-06', 2789.49],
  //   // ['2024-02-07', 2829.70],
  //   // ['2024-02-08', 2865.90],
  //   // ['2024-03-04', 3039.31],
  //   // ['2024-03-05', 3047.79],
  // ];

  // step1: 校验sina爬取指数成分数据，与指数编制规则构建的成分数据。是否一致。
  // await stepCheckComponentsBySinaAndRule(output, timePoints, dbComponents, ruleComponents);

  // step2: 校验sina爬取的指数成分数据，跟上证官方sse爬取的指数数据是否有差异
  let [sinaStocks, sseStocks] = await stepCheckComponentsBySinaAndSse(output, code, dbComponents, startDate);

  // 确认最终的basicStocks
  let basicStocks = sseStocks;


  // step3: 补充index/stocks分时数据
  // await indexService.CrawlerSingleIndexFenshiDetailFromThirdClient(output, code);
  // for (let s of basicStocks) {
  //   await stockService.CrawlerSingleStockFenshiDetailFromThirdClient(output, s);
  // }


  // step4: 指数计算校验
  // for (let i = 0; i < list.length - 1; ++i) {
  //   let lastDate = list[i][0];
  //   let lastDateIndex = list[i][1];
  //   let date = list[i+1][0];
  //   let expected = list[i+1][1];
  //   await dbComponents.Calculate(output, lastDate, date, lastDateIndex, expected, sinaStocks);
  // }

  let checks = timePoints;
  
  let indices = await db.IndexKLineDaily.findAll({
      where: {
        indexCode: code,
        date: {[Op.in]: checks},
      },
      // attributes: ['indexCode', 'closingPrice', 'changePercent', 'date'],
      attributes: ['closingPrice', 'changePercent', 'date'],
      raw: true,
  });
  let indicesMap = {};
  for (let i of indices) {
    indicesMap[i.date] = i.closingPrice;
  }
  output.info('date: ', checks.length);

  // 修改验证流程，串起整个流程
  // let calcs = [{
  //   // date: '1990-12-18',
  //   // val: 100,

  //   // date: '1990-12-19',
  //   // val: 99.98,
  //   //
  //   // date: '1991-07-15',
  //   // val: 133.143,
  //   //
  //   date: '1991-07-19',
  //   val: 136.70,
  // }];

  let calcs = [{
    date: checks[0],
    val: indicesMap[checks[0]],
  }];

  for (let i = 0; i < checks.length - 1; ++i) {
    let lastDate = checks[i];
    // let lastDateIndex = calcs[i].val;
    let lastDateIndex = indicesMap[lastDate];
    let date = checks[i+1];
    // TODO 先用sse验证2024年的上证50
    let res = await dbComponents.Calculate(output, code, lastDate, date, lastDateIndex, indicesMap[date], basicStocks);
    calcs.push({
      date: date,
      val: res,
    });
  }

  output.debug('calcs: ', calcs);
  output.debug('expected: ', indices);
  output.debug('basicStocks: ', basicStocks);
  // matrix.solve();
}


module.exports = {
  CheckSingleIndexDataEntry,
};
