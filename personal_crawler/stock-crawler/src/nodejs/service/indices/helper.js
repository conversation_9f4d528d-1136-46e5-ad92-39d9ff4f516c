

/****
 * 一个纯逻辑计算、无side-effect的辅助计算方法文件
 * 1. 不读取db
 * 2. 不调用eastmoney等三方接口
 * 3. 不打印日志
 */

function getEmptyFenshiMap() {
  let fenshiMap = [];
  let e = new Date("2000-01-01 11:30:00");
  for (let s = new Date("2000-01-01 09:30:00"); s.getTime() <= e.getTime(); s.setSeconds(s.getSeconds() + 1)) {
    let str = utils.FormatTimeLocal(s).slice(11);
    fenshiMap.push({time: str, stocks: {}});
  }

  e = new Date("2000-01-01 15:10:00");
  for (let s = new Date("2000-01-01 13:00:00"); s.getTime() <= e.getTime(); s.setSeconds(s.getSeconds() + 1)) {
    let str = utils.FormatTimeLocal(s).slice(11);
    fenshiMap.push({time: str, stocks: {}});
  }
  return fenshiMap;
}



  // 计算调整股本数
function calcAdjustShares(valuation) {
  // 1. 确定自由流通量
  //   - A股总股本
  let totalShares = valuation.totalSharesA;
  //   - 自由流通股本
  let freeShares = valuation.listedAShares;
  //   - 非自由流通股本
  // let nonFreeShares = totalShares - valuation.unlimitedShares;
  //   - 自由流通比例
  let freeRatio = freeShares / totalShares;

  // 2. 确定分级靠档
  let weight = 0;
  if (isIn(freeRatio, 0, 0.10)) {
    weight = Math.ceil(freeRatio*100)/100;
  } else if (isIn(freeRatio, 0.10, 0.2)) {
    weight = 0.2;
  } else if (isIn(freeRatio, 0.2, 0.3)) {
    weight = 0.3;
  } else if (isIn(freeRatio, 0.3, 0.4)) {
    weight = 0.4;
  } else if (isIn(freeRatio, 0.4, 0.5)) {
    weight = 0.5;
  } else if (isIn(freeRatio, 0.5, 0.6)) {
    weight = 0.6;
  } else if (isIn(freeRatio, 0.6, 0.7)) {
    weight = 0.7;
  } else if (isIn(freeRatio, 0.7, 0.8)) {
    weight = 0.8;
  } else if (isIn(freeRatio, 0.8, 1)) {
    weight = 1;
  }
  return [totalShares * weight, freeRatio];


  // 左开右闭
  function isIn(val, left, right) {
    if (val > left && val <= right) {
      return true;
    }
    return false;
  }
}

// 输入:
//  1. 成分stocks列表
//  2. 某个时间点和基期的price信息：基期/valuationMap[lastDate]  待计算时间点/valuationMap[date]
//      - 可以计算date
//      - 可以计算分时timepoint
//
// 输出:
//  当前时间点下的计算值
//  
function calculate(basicStocks, stockMap, lastDate, date, baseValueMap, curValueMap) {
    let totalShares = 0;
    let diffValuations = 0; // 所有成份股的今日较上一日的差值总和。作为分子
    let totalValuations = 0; // 上一日，所有成份股的总市值。作为分母
    let orderStocks = [];
    for (let c of basicStocks) {
      let s = stockMap[c];

      // 计算调整市值: lastDate
      let lastPrice = baseValueMap[c].price;
      // let lastDateCap = (+baseValueMap[c].totalMarketCapA);
      // let lastDateShares = (+baseValueMap[c].totalSharesA);
      // let [lastDateAdjustShares, lastFreeRatio] = calcAdjustShares(baseValueMap[c]);


      // 计算调整市值：today
      let price = curValueMap[c].price;
      // let todayCap = (+curValueMap[c].totalMarketCapA);
      // let todayShares = (+curValueMap[c].totalSharesA);
      let freeShares = (+curValueMap[c].listedAShares);
      let [adjustShares, freeRatio] = calcAdjustShares(curValueMap[c]);
      let lastDateAdjustCap = adjustShares * lastPrice;
      let adjustCap = adjustShares * price;

      // 计算解禁市值（去掉限售）: tody
      let unlimitCap = (+curValueMap[c].notlimitedMarketCapA);

      let addDiff = adjustCap - lastDateAdjustCap;


      // 计算累计值
      totalValuations += lastDateAdjustCap;
      diffValuations += addDiff;
      totalShares += adjustShares;
      // totalUnlimitCap += unlimitCap;

      orderStocks.push({
        code: c,
        stockName: s.stockName,
        lastDate: lastDate,
        date: date,
        lastPrice: lastPrice,
        price: price,
        lastDateAdjustCap: lastDateAdjustCap,
        adjustCap: adjustCap,
        adjustShares: adjustShares,
        freeShares: freeShares,
        freeRatio: freeRatio,
      });
    }

    // 排序: 今日市值倒序排
    orderStocks.sort((a, b) => b.adjustCap - a.adjustCap);

  return [orderStocks, totalValuations, totalValuations+diffValuations, totalShares]
}

// 用于快速验证未知系数的求解矩阵
class Matrix {
  constructor() {
    this.config = {
      // epsilon: 0.001,
      matrix: 'Matrix',
      notation: 'fixed',
      precision: 5,
    };
    this.A = []; // 二维矩阵，每一行表示每个date的value序列，每个value序列表示股票按顺序排列的已经计算好的调整市值
    this.x = []; // 表示未知系数序列，顺序跟value序列的股票顺序一一对应，表示对应股票的未知系数
    this.y = []; // 表示已知指数序列，值按照A的date顺序排列
  }

  format(v) {
    return math.format(v, this.config)
  }

  solve() {
    // 等式关系就是 Ax = y，A和y已知，求解x
    // Step1：先求A的逆矩阵B
    // x = BAx = By
    console.log('start solve:');
    // console.log('A: ', this.A);
    
    // let B = math.inv(this.A);
    // console.log('B: ', this.format(B));
    // let r = math.multiply(B, this.A)
    // console.log('r: ', this.format(r));
    // r = math.multiply(this.A, B)
    // console.log('r: ', this.format(r));

    // let r = math.multiply(B, this.y);
    // console.log('r:', this.format(r));
    //

	// 方式二：已知AX=0，求X。
	//   1. 即求解A的右零因子向量（zero divisor）
    //   2. 因为矩阵为非交换环，所以左零因子和右零因子是不等价的
    //   3. 零因子同时蕴含了因子本身是非零元素

    // let y = math.zeros(this.A.length);
    this.A = math.matrix(this.A);
    this.y = math.matrix(this.y);
    // let eig = math.eigs(this.A);
    // console.log('eig: ', eig);
    // let eigenvalues = eig.values;
    // console.log('eigenvalues: ', eigenvalues);
    // const zeroMatrix = math.zeros(this.A.size()[0], this.A.size()[1]);

	// for (let i = 0; i < eigenvalues.length; i++) {
	//   if (math.abs(eigenvalues[i]) < 1e-2) { // 使用一个很小的阈值来判断特征值是否为零
	//     zeroMatrix.set([i, i], 1);
	//   }
	// }
    //
    // console.log('zeroMatrix: ', zeroMatrix.toString());

    console.log('this.A: ', this.A);
    console.log('y: ', this.y);
    let x = math.lusolve(this.A, this.y);
    console.log('solve: ', this.format(x));
    // console.log('L: ', x.L);
    // console.log('U: ', x.U);

    // let svd = math.svd(this.A);
   
	// const singularValues = svd.S;
	// 
	// // 提取右奇异向量矩阵
	// const rightSingularVectors = svd.V;
	// 
	// // 找到对应于零奇异值的右奇异向量
	// const zeroSingularIndex = singularValues.findIndex(value => math.abs(value) < 1e-10); // 使用一个很小的阈值来判断特征值是否为零
	// const zeroFactorVector = rightSingularVectors.subset(math.index(math.range(0, rightSingularVectors.size()[0]), zeroSingularIndex));
	// console.log('零因子向量:');
	// console.log(zeroFactorVector.toString()); 
  }

  addLine(line) {
    this.A.push(line);
  }

  addY(v) {
    this.y.push(v);
  }
  
  test() {
    // this.A = [
    //   // [1, 2],
    //   // [3, 4]
    //   [1, 2, 3],
    //   [4, 92, 33],
    //   [5, 22, 3]
    // ];
    // this.y = [];
    // console.log('format: ', math.format(1e+27, this.config));
    // this.solve();

    this.A = [
      [4, 3],
      [6, 2]
    ];
    this.y = [1, 2]
    this.solve();
    // X: 
    // [1, 1],
    // [-1, -1]
    //
    // 左零因子:
    // [-2, 1]
    // [-2, 1]
  }
}
// let matrix = new Matrix();
// matrix.test();


module.exports = {
  calculate,
};
