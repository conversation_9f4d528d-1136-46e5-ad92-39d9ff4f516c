const eastmoney = require('../../third_client/eastmoney.js');
const eastmoney_v2 = require('../../third_client/eastmoney_v2.js');
const sina = require('../../third_client/sina.js');
const sse = require('../../third_client/sse.js');
const common = require('../common');

async function dataInitIndices() {
  const indices = await indexService.GetIndicies();
  logger.info('[modelsInit]', 'db.Index.bulkCreate', indices);
  await db.Index.bulkCreate(indices);
}

async function dataInitIndexValueMinutely(db) {
  let indices = await db.Index.findAll({
    attributes: ['indexCode', 'indexType', 'indexName'],
    raw: true
  });
  logger.info('[dataInitIndexValueMinutely]', indices);
  for(let i of indices) {
    let values = await indexService.GetIndexFenshiTrends(i);
    if (values.length > 0) {
      await db.IndexValueMinutely.bulkCreate(values);
    }

    for (let v of values) {
      if ((new Date(v.time)).getTime() == new Date('2022-08-26 10:37').getTime()) {
        logger.info("++++ v: ", v);
      }
    }

    values = await indexService.GetIndexFenshiDetails(i);
    if (values.length > 0) {
      await db.IndexValueDetail.bulkCreate(values);
    }

    let total = 0;
    let totalPrice = 0.0;
    let avgPrice = 0;
    for (let v of values) {
      let time = new Date(v.time);
      if (time.getTime() < (new Date('2022-08-26 10:37')).getTime() 
        && time.getTime() >= (new Date('2022-08-26 10:36')).getTime()) {
        logger.info("=== v: ", v);
        total += (+v.volume);
        totalPrice += v.volume * v.price;
      }
      avgPrice = totalPrice * 1.0 / total;
    }
    logger.info('total: ', total);
    logger.info('totalPrice: ', totalPrice);
    logger.info('avgPrice: ', avgPrice);
  }
}

async function _getIndicesFromApi() {
  let indices = [];
  // 上证系列指数
  let body = await eastmoney_v2.getIndexSh();
  for(let r of body.rows) {
    let index = {
      indexType: 'sh',
      isImportant: false,
      indexCode: r.indexCode,
      indexName: r.indexName
    };
    indices.push(index);
  }

  // 深圳系列指数
  body = await eastmoney_v2.getIndexSz();
  for(let r of body.rows) {
    let index = {
      indexType: 'sz',
      isImportant: false,
      indexCode: r.indexCode,
      indexName: r.indexName
    };
    indices.push(index);
  }
  return indices;
}

async function _getIndicesFromInit() {
  let indices = [
    // 沪深重要指数
    // {indexType: 'sh', indexCode: '000001', indexName: '上证指数'},
    // {indexType: 'sh', indexCode: '000002', indexName: 'A股指数'},
    // {indexType: 'sh', indexCode: '000003', indexName: 'B股指数'},
    // {indexType: 'sh', indexCode: '000016', indexName: '上证50'},
    {indexType: 'sh', indexCode: '000300', indexName: '沪深300'},
    // {indexType: 'sz', indexCode: '399001', indexName: '深证成指'},
    // {indexType: 'sz', indexCode: '399002', indexName: '深成指R'},
    // {indexType: 'sz', indexCode: '399003', indexName: '成份B指'},
    // {indexType: 'sz', indexCode: '399005', indexName: '中小板指'},
    // {indexType: 'sz', indexCode: '399006', indexName: '创业板指'},
    // {indexType: 'sz', indexCode: '399106', indexName: '深证综指'},
    // {indexType: 'sz', indexCode: '399550', indexName: '央视50'},
  ];
  return indices;
}

async function GetIndicies() {
  let indices = await _getIndicesFromInit();
  // let indices = await _getIndicesFromApi();

  const shImportant = ['000001', '000002', '000003', '000016', '000300'];
  const szImportant = ['399001', '399002', '399003', '399005', '399006', '399106', '399550'];
  for (let i of indices) {
    if (shImportant.indexOf(i.indexCode) > -1) {
      i.isImportant = true;
    }

    if (szImportant.indexOf(i.indexCode) > -1) {
      i.isImportant = true;
    }
  }
  return indices;
}

async function GetIndexFenshiTrends(i) {
  let values = [];
  let body = await eastmoney_v2.getIndexFenshiTrends(i.indexCode);
  body.rows.map(v => v.indexName = i.indexName);
  values = values.concat(body.rows);
  return values;
}

async function GetIndexFenshiDetails(i) {
  let values = [];
  let body = await eastmoney_v2.getIndexFenshiDetails(i.indexCode, i.indexType);
  body.rows.map(v => v.indexName = i.indexName);
  values = values.concat(body.rows);
  return values;
}

async function GetIndexFromDB(logger, code) {
  const index = await db.Index.findOne({
    where: {indexCode: code},
  });
  return index;
}

async function GetIndexFromThirdClient(logger, code) {

}


function generateGetAllIndicesAndSave(flag, indexType) {
  return async function(output) {
    let oldCount = await db.Index.count({where: {indexType: indexType}});
    let indices = [];
    let body = await eastmoney_v2.getAllStockByFlag(output, flag);
    output.debug(`getAllIndexByFlag[flag=${flag} indexType=${indexType}] result.count=${body.total}`);
    for(let r of body.rows) {
      r.indexType = indexType;
      indices.push(r);
    }
    await db.Index.bulkCreate(indices, {ignoreDuplicates: true});
  
    let newCount = await db.Index.count({where: {indexType: indexType}});
  
    return [oldCount, indices, newCount];
  }
}

var getAllSzIndicesAndSave = generateGetAllIndicesAndSave('index_sz', 'sz');
var getAllShIndicesAndSave = generateGetAllIndicesAndSave('index_sh', 'sh');
var getAllZzIndicesAndSave = generateGetAllIndicesAndSave('index_zz', 'zz');


async function CrawlerAllIndexFromThirdClient(output) {
  const [oldCount1, indices1, newCount1] = await getAllSzIndicesAndSave(output);
  const [oldCount2, indices2, newCount2] = await getAllShIndicesAndSave(output);
  const [oldCount3, indices3, newCount3] = await getAllZzIndicesAndSave(output);

  const oldCount = oldCount1 + oldCount2 + oldCount3;
  const newCount = newCount1 + newCount2 + oldCount3;
  const indices = indices1.concat(indices2).concat(indices3);

  output.setFillMeta(oldCount, indices.length, newCount);
}

async function CrawlerSingleIndexFenshiDetailFromThirdClient(output, indexCode, indexType) {
  let ret = await eastmoney_v2.getIndexFenshiDetails(output, indexCode, indexType);
  await db.IndexValueDetail.bulkCreate(ret.rows, {ignoreDuplicates: true});
  output.debug(`index[${indexCode}] fenshi count: ${ret.total}`)
}

async function CrawlerAllIndexDailyFromThirdClient(output) {
  let oldCount = await db.IndexKLineDaily.count();

  let indices = await db.Index.findAll({
    attributes: ['indexCode', 'indexType'],
    // order: [['indexCode', 'ASC']], // 调试用，顺序获取方便定位
    raw: true
  });

  let total = 0;
  let i = -1;
  for (let s of indices) {
    i += 1;
    // 判断是否存在最新日K
    let [sOldCount, sFirstDate, sLastDate] = await common.GetExistIndexKLineFromDB(s.indexCode);
    if (sOldCount > 0) {
      // 如果已经有最近日期的K数据，则跳过当前stock的爬取
      let lastTradeDate = await eastmoney_v2.GetLastTradeDate();
      if (lastTradeDate == sLastDate) {
        output.debug(`i=${i}/index=${s.indexCode}/exist(count=${sOldCount} date=[${sFirstDate}, ${sLastDate}]) 最新`);
        continue
      } else {
        output.debug(`i=${i}/index=${s.indexCode}/exist(count=${sOldCount} date=[${sFirstDate}, ${sLastDate}]) ADD Incremental(lastTradeDate=${lastTradeDate} lastDate=${sLastDate})`);
      }
    }

    let body = await eastmoney_v2.getIndexKLine(output, s.indexCode, s.indexType);
    await db.IndexKLineDaily.bulkCreate(body.rows, {ignoreDuplicates: true});
    total += body.total;

    output.debug(`i=${i}/index=${s.indexCode}/total + ${body.total}=>${total}`);

    // 拉取分时数据
    await CrawlerSingleIndexFenshiDetailFromThirdClient(output, s.indexCode, s.indexType);
  }

  let newCount = await db.IndexKLineDaily.count();
  output.setFillDaily(oldCount, total, newCount);
}

async function _crawlerIndexComponentAdjustmentFromThirdClient(output, s) {
  // 获取当前指数已有的调仓记录
  let records = await db.IndexComponent.findAll({
    attributes: ['indexCode', 'stockCode', 'addTime', 'delTime'],
    where: {
      indexCode: s.indexCode,
    },
    raw: true
  });

  // // 判断是否存在最新日K
  // let [sOldCount, sFirstDate, sLastDate] = await common.GetExistIndexKLineFromDB(s.indexCode);
  // if (sOldCount > 0) {
  //   // 如果已经有最近日期的K数据，则跳过当前stock的爬取
  //   let lastTradeDate = await eastmoney_v2.GetLastTradeDate();
  //   if (lastTradeDate == sLastDate) {
  //     output.debug(`i=${i}/index=${s.indexCode}/exist(count=${sOldCount} date=[${sFirstDate}, ${sLastDate}]) 最新`);
  //     continue
  //   } else {
  //     output.debug(`i=${i}/index=${s.indexCode}/exist(count=${sOldCount} date=[${sFirstDate}, ${sLastDate}]) ADD Incremental(lastTradeDate=${lastTradeDate} lastDate=${sLastDate})`);
  //   }
  // }

  // 从sina获取记录
  output.debug("从sina拉取，写进db.IndexComponent...");
  let body = await sina.getIndexComponents(output, s.indexCode, s.indexName);
  await db.IndexComponent.bulkCreate(body.rows, {ignoreDuplicates: true});

  let toAdds = [];
  let sseRecords = await db.IndexComponentSse.findAll({
    where: {
      indexCode: s.indexCode,
    },
    order: [['inDate', 'DESC']],
    raw: true
  });
  let sseMap = {};
  for (let r of sseRecords) {
    if (!sseMap[r.stockCode]) {
      sseMap[r.stockCode] = [];
    }
    // 构建每个股票的记录
    sseMap[r.stockCode].push(r);
  }
  let now = new Date();

  // 从sse获取记录
  output.debug("从sse拉取，写进db.IndexComponentSse...");
  let sseBody = await sse.getIndexComponents(output, s.indexCode, s.indexName);
  for (let s of sseBody.rows) {
    if (sseMap[s.stockCode]) {
      // 如果股票已经存在记录，取出最新日期的一条记录，看看是添加还是移除
      let record = sseMap[s.stockCode][0];
      if (record.addTime) {
        // 添加时间，目前处于「已加进」状态，不重复添加
      } else if (record.delTime) {
        // 移除时间，目前处于「移除」状态，重新添加
        toAdds.push({
          indexCode: s.indexCode,
          indexName: s.indexName,
          stockCode: s.stockCode,
          stockName: s.stockName,
          inDate: s.inDate,
          addTime: s.inDate,
        });
      } else {
        // TODO 异常情况。同为空、或者同为非空
      }
    } else {
      // 股票未存在过。那就当做新增加入
      toAdds.push({
        indexCode: s.indexCode,
        indexName: s.indexName,
        stockCode: s.stockCode,
        stockName: s.stockName,
        inDate: s.inDate,
        addTime: s.inDate,
      });
    }
  }
  logger.debug('sse toAdds: ', toAdds);
  await db.IndexComponentSse.bulkCreate(toAdds, {ignoreDuplicates: true});

  return body;
}

async function CrawlerSingleIndexComponentAdjustmentFromThirdClient(output, indexCode) {
  let index = await db.Index.findOne({
    attributes: ['indexCode', 'indexType', 'indexName'],
    where: {
      indexCode: indexCode,
    },
    order: [['indexCode', 'ASC']], // 调试用，顺序获取方便定位
    raw: true
  });
  await _crawlerIndexComponentAdjustmentFromThirdClient(output, index)
}

async function CrawlerAllIndexComponentAdjustmentFromThirdClient(output) {
  let oldCount = await db.IndexComponent.count();

  let indices = await db.Index.findAll({
    attributes: ['indexCode', 'indexType', 'indexName'],
    order: [['indexCode', 'ASC']], // 调试用，顺序获取方便定位
    raw: true
  });

  let total = 0;
  let i = -1;
  for (let s of indices) {
    i += 1;
    
    let body = await _crawlerIndexComponentAdjustmentFromThirdClient(output, s)

    total += body.total;
    output.debug(`[components] i=${i}/index=${s.indexCode}/total + ${body.total}=>${total}`);
  }

  let newCount = await db.IndexComponent.count();
  output.setFillComponents(oldCount, total, newCount);
}

module.exports = {
  GetIndicies: GetIndicies,
  GetIndexFenshiTrends: GetIndexFenshiTrends,
  GetIndexFenshiDetails: GetIndexFenshiDetails,

  GetIndexFromDB,
  GetIndexFromThirdClient,

  CrawlerAllIndexFromThirdClient, // 从第三方拉取所有指数的元信息
  CrawlerAllIndexDailyFromThirdClient, // 从第三方拉取所有指数的日K记录
  CrawlerSingleIndexComponentAdjustmentFromThirdClient,
  CrawlerAllIndexComponentAdjustmentFromThirdClient,  // 从第三方拉取所有指数的成份股调整记录
  CrawlerSingleIndexFenshiDetailFromThirdClient,  // 从第三方拉取单个指数的分时数据（只能最新date）
};
