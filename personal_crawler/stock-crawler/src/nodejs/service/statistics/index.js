const fs = require('fs');
const eastmoney_v2 = require('../../third_client/eastmoney_v2.js');
const echarts = require('echarts');

async function CrawlerMarketValuationFromThirdClient(output) {
  const tradeMarketCode = "000300";

  let lastTradeDate = await eastmoney_v2.GetLastTradeDate();
  output.setLastTradeDate(lastTradeDate);

  let dbExistLast = await db.MarketValuation.findOne({
    whedailyCountr: {
      tradeMarketCode: tradeMarketCode,
    },
    order: [['tradeDate', 'DESC']],
    limit: 1,
    raw: true
  });
  let dbExistLastDate = dbExistLast.tradeDate;
  output.setExistLastTradeDateMarketCap(dbExistLastDate);

  if (new Date(dbExistLastDate).getTime() == new Date(lastTradeDate).getTime()) {
    return
  }

  let body = await eastmoney_v2.GetMarketValuation(output, tradeMarketCode, dbExistLastDate);
  output.setFetchNewMarketCap(body.total);
  await db.MarketValuation.bulkCreate(body.rows, {ignoreDuplicates: true});
}


async function CrawlerIPORecordFromThirdClient(output, stockCode) {
  let lastTradeDate = await eastmoney_v2.GetLastTradeDate();
  output.setLastTradeDate(lastTradeDate);

  let dbExistLast = await db.IPORecord.findOne({
    whedailyCountr: {
      stockCode: stockCode,
    },
    order: [['tradeDate', 'DESC']],
    limit: 1,
    raw: true
  });
  let dbExistLastDate = null;
  if (dbExistLast) {
    dbExistLastDate = dbExistLast.tradeDate;
  }
  output.setExistLastTradeDateMarketCap(dbExistLastDate);

  if (dbExistLastDate && new Date(dbExistLastDate).getTime() == new Date(lastTradeDate).getTime()) {
    return
  }

  // let body = await eastmoney_v2.GetMarketValuation(output, tradeMarketCode, dbExistLastDate);
  // output.setFetchNewMarketCap(body.total);
  // await db.MarketValuation.bulkCreate(body.rows, {ignoreDuplicates: true});
}



/**
 * 生成统计图表
 */
async function StatChart(output) {
  const tradeMarketCode = "000300";
  let result = await db.MarketValuation.findAll({
    where: {
      "tradeMarketCode": tradeMarketCode,
    },
    order: [['tradeDate', 'ASC']],
    raw: true,
  });

  // for (let d of result) {
  //   output.debug2(d.tradeDate, d.totalMarketCap);
  // }

  let option = {
    xAxis: [
      {
        type: 'category',
        axisTick: {
          alignWithLabel: true,
        },
        data: result.map((v) => v['tradeDate']),
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '总市值（元）',
        min: 0,
        max: 10093511893,
        position: 'left',
        axisLabel: {
          formatter: '{value} Y'
        }
      }
    ],
    series: [
      {
        name: '总市值（元）',
        type: 'line',
        yAxisIndex: 0,
        data: result.map((v) => v['totalMarketCap']),
      }
    ],
  };

  const chart = echarts.init(null, null, {
    renderer: 'svg',
    ssr: true,
    width: 1080,
    height: 1080,
  })
  chart.setOption(option)
  fs.writeFileSync(output.getFilePath('test.svg'), chart.renderToSVGString(), 'utf-8');
  output.setStatSvgLink(`http://*************:9999/test.svg`);
}

module.exports = {
  CrawlerMarketValuationFromThirdClient,
  CrawlerIPORecordFromThirdClient,
  StatChart,
}
