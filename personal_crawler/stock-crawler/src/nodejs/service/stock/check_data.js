
const { Init } = require('../../init.js');
const utils = require('../../nodejs-common/utils.js');
const eastmoney_v2 = require('../../third_client/eastmoney_v2.js');
const Sequelize = require('sequelize');
const Op = Sequelize.Op;


// 构造以date为key，从IPO起始日期开始，每日的totalShares为value的map
// params: changes，保存所有IPO/SPO记录的数组，每个元素保存{date, shares}
function buildSharesMap(logger, changes) {
  let sharesMap = {};
  let startDate = changes[0].date;
  let now = utils.FormatDate(new Date());
  logger.info('buildSharesMap:', changes);

  let idx = 0;
  for (let date = startDate; date < now; date = utils.AddDays(date, 1)) {
    if (idx < changes.length - 1 && date >= changes[idx+1].date) {
      // 日期增加到了新的IPO/SPO记录之后，用新的shares数量更新
      logger.info('change date:', date, 'idx: ', idx, '=>', idx+1);
      idx += 1
    }
    sharesMap[date] = +changes[idx].shares;
  }
  return sharesMap;
}

function isAcceptAccuracy(logger, approximate, real) {
  let diff = real - approximate;
  // 假如是73.35万股，即在百位数是准确的。
  // 加入是73.35亿股，即在百万的位置是准确的。
  // 实际上表达的就是在0.0001的误差范围内是可以接受的
  let accuracy = diff / real;
  if (accuracy < 0.0005 && accuracy > -0.0005) {
    return true;
  }
  logger.debug(`appro[${approximate}] real[${real}] diff[${diff}] accuracy[${accuracy}]`);
  return false;
}

function parseLimitEvent(logger, companyEvent) {
  // eg: 2020-09-21解禁数量524.1万股，占总股本比例0.23%，股份类型：股权激励限售股份
  let match = companyEvent.level1Content.match(/(\d+-\d+-\d+).*解禁数量(.*?)股.*股份类型：(.*)/i);
  let ret = {
    date: match[1],
    sharesDesc: match[2],
    shares: utils.ParseNumberText(match[2]),
    stockType: match[3],
    noticeDate: companyEvent.noticeDate,

  };
  return ret;
}

function parseESOPEvent(logger, companyEvent) {
  
}

function aggrAndCheckUnlimitStocks(logger, unlimits) {
  let dateMap = {};
  for (let r of unlimits) {
    if (!dateMap[r.freeDate]) {
      dateMap[r.freeDate] = {
        date: r.freeDate,
        shares: 0,
        stockType: r.freeSharesType,
        raws: [],
      };
    }
    dateMap[r.freeDate].shares += r.addListingShares;
    dateMap[r.freeDate].raws.push(r);
  }

  for (let r of unlimits) {
    // logger.debug('r: ', r);
    // logger.debug('dateMap[date]: ', dateMap[r.freeDate]);
    utils.Assert(r.liftSharesAll == dateMap[r.freeDate].shares);
    utils.Assert(r.freeSharesType == dateMap[r.freeDate].stockType);
  }
  // logger.debug('dateMap:', dateMap);

  return Object.values(dateMap).sort((a, b) => a.date < b.date);
}

// 把限售解禁的数据，做一个辅助关联数据，对于每次限售解禁的数量中，有多少是处于质押状态的
function findoutPledgeMap(logger, unlimitMap, pledges) {
  let audixMap = {};
  for (let date in unlimitMap) {
    // 对于每次限售解禁的日期，先找出处于质押状态的数据
    // 即date处于<pledgeStartDate, pledgeUnfreezeDate>区间的部分
    let targets = [];
    let targetMap = {}; // 以股东名字为key
    for (let p of pledges) {
      if (date > p.pledgeStartDate && (!p.pledgeUnfreezeDate || p.pledgeUnfreezeDate > date)) {
        // if (date == '2021-06-11') {
        //   logger.debug('P/true: ', p);
        // }

        // date大于质押起始时间，且date小于解押时间（解押时间为null表示无穷大）
        targets.push(p);
        // targets.push(`${p.limitedHolderName}:${p.addListingShares}`);
        if (!targetMap[p.holderName]) {
          targetMap[p.holderName] = 0;
        }
        targetMap[p.holderName] += (+p.pledgeNum);
        // if (date == '2021-06-11') {
        //   logger.debug('targetMap: ', targetMap);
        // }
      // } else {
      //   if (date == '2021-06-11') {
      //     logger.debug('cond1: ', date > p.pledgeStartDate);
      //     logger.debug('cond2: ', !p.pledgeUnfreezeDate);
      //     logger.debug('cond3: ', p.pledgeUnfreezeDate > date);
      //     logger.debug('P/false: ', p);
      //   }
      }
    }

    // 再从unlimitMap[date]里的股东限售解禁明细原始记录raws里面，找到是否有对于targets里的人
    for (let r of unlimitMap[date].raws) { // r表示限售解禁记录
      let pledgeNum = targetMap[r.limitedHolderName]; // 质押记录
      if (pledgeNum) {
        // 如果限售解禁、质押记录对应到同一个股东，那么对限售解禁数据构建修正的辅助map
        if (!audixMap[date]) {
          audixMap[date] = 0;
        }
        // 把质押的股份数量记录下来
        audixMap[date] += pledgeNum;
      }
    }

    // if (targets.length > 0) {
    // if (date == '2021-06-11') {
    //   logger.debug('date: ', date);
    //   logger.debug('unlimit: ', unlimitMap[date]);
    //   logger.debug('targets: ', targets);
    // }
  }
  // logger.debug('final audixMap: ', audixMap);
  return audixMap;
}

// 获取前一个交易日的记录
function getLastTradeDayValuation(date, valuationMap) {
  let maxLoopTimes = 2000;
  let d = utils.AddDays(date, -1);
  for (let i = 0; i < maxLoopTimes; ++i) {
    let r = valuationMap[d];
    if (r) {
      return r;
    }

    d = utils.AddDays(d, -1);
  }
  return null
}

// 怀疑东财数据有问题，hardCode修正
// 以及各种待确认的猫腻数据case
function hardCodeFixValuations(logger, r) {
  if (r.stockCode == '300750') {
    // case1
    // 宁德时代，2021/06/11-2021/06/18，
    //   - 东财记录: 2309379382，解禁限售后没有去掉质押的部分。
    //        - 限售解禁: 952381254
    //        - 实际流通: 672743092（这个数据会导致050、051编号的公告最终freeShares不一致。这里实际流通的四舍五入法当做向上取整，所以这里当做672743094来处理了。）
    //        - 东财记录比实际多包含的: 952381254-672743094 = 279638160
    //   - 实际: freeShares应该是2309379382-279638160 = 2029741222
    if (r.tradeDate >= "2021-06-11" && r.tradeDate <= "2021-06-18") {
      r.freeShares -= 279638160;
      // 或者直接r.freeShares = 2029741222
      logger.debug('r.freeShares: ', r.freeShares);
    }
  }
  return r;
}

// TODO 各种待确认的猫腻数据变动
function toCheckFixFreeShares(stockCode, date) {
  if (stockCode == '300750') {
    if (date == '2022-08-18') {
      // 宁德时代2022/08/18找不到原因的freeShares少了63225183，6千万股: 2038656510 => 1975431327
      return 1975431327 - 2038656510;
    }
    if (date == '2023-03-10') {
      // 未知：高管股份变动
      return 2157191463 - 2092466281; // 增加了64725182，6千万股
    }

    if (date == '2023-06-30') {
      // 不知道什么情况。。
      return 1
    }

    if (date == '2023-09-21') {
      // 未知：高管股份变动
      return 3889712351 - 3889738366; // 减少26015股
    }
  }
  return 0;
}

function fixFreeSharesCheckMap(logger, stockCode, freeSharesCheckMap, unlimitMap, pledgeMap, esopMap, additionalMap) {
  let addShares = 0; // 用来记录循环到哪个日期时，限售解禁带来的新增股份数

  let debugDate = '2022-11-04';
  logger.debug('fixFreeSharesCheckMap debug:', freeSharesCheckMap[debugDate],
    unlimitMap[debugDate], esopMap[debugDate]);

  debugDate = '2023-01-04';
  logger.debug('fixFreeSharesCheckMap debug:', freeSharesCheckMap[debugDate],
    unlimitMap[debugDate], esopMap[debugDate]);

  // 遍历freeSharesCheckMap的所有日期
  for (let d in freeSharesCheckMap) {
    if (unlimitMap[d]) {
      // TODO 这里目前质押先不计算，优先用规则来计算，比如上市流通占25%（按照招股说明书来，可能不同股票得case by case处理）
      // https://pdf.dfcfw.com/pdf/H2_AN202106081496776288_1.pdf?1623181265000.pdf
      // 按整数截断，不四舍五入

      // 如果某个日期存在限售解禁记录，就按照一定规则累计到addShares
      // logger.debug('unlimitMap :', d, unlimitMap[d].stockType, unlimitMap[d].shares);
      // if (unlimitMap[d].stockType == "首发原股东限售股份") {
      if (d == "2021-06-11") {
        // TODO 临时修复，后面看看怎么区分。25%流通比例的信息来自招股说明书和公告
        // 而且不能直接*0.25.。还要只取其中两个人的。由于不清楚明确逻辑，不用做过多处理，直接hardCode这两个人先。
        let shares = 0;
        for (let r of unlimitMap[d].raws) {
          if (r.limitedHolderName == '黄世霖' || r.limitedHolderName == '李平') {
            // shares += Math.floor(r.addListingShares * 0.25);
            shares += Math.ceil(r.addListingShares * 0.25);
          } else {
            shares += r.addListingShares;
          }
        }
        addShares += shares;
      } else {
        addShares += unlimitMap[d].shares;
      }
    }

    // 遇到股权激励归属记录，也需要增加freeShares
    if (esopMap[d]) {
      // TODO esopMap和unlimitMap可能有重叠。。
      if (unlimitMap[d]) {
        utils.Assert(esopMap[d].shares == unlimitMap[d].shares);
      } else {
        addShares += esopMap[d].shares;
      }
    }

    // 遇到转增股
    if (additionalMap[d]) {
      let lastDate = utils.AddDays(d, -1);
      // TODO 先四舍五入取法，后面看看是不是Math.floor
      let add = Math.round(freeSharesCheckMap[lastDate] * additionalMap[d] / 10);
      addShares += add
    }

    addShares += toCheckFixFreeShares(stockCode, d)

    // 每一天的freeSharesCheckMap都用addShares来做增加修正
    freeSharesCheckMap[d] += addShares;
    // logger.debug('d: ', d, addShares, '->', freeSharesCheckMap[d]);
  }
  return freeSharesCheckMap;
}

function fixTotalSharesCheckMap(logger, totalSharesCheckMap, unlimitMapForTotal, esopMap, additionalMap, buyBackMap, givenMap) {
  let addShares = 0; // 用来记录循环到哪个日期时，回购注销带来的总股份数回收
  logger.info('fixTotalSharesCheckMap: esopMap: ', esopMap);
  logger.info('fixTotalSharesCheckMap: unlimitMapForTotal: ', unlimitMapForTotal);

  // 遍历totalSharesCheckMap的所有日期
  for (let d in totalSharesCheckMap) {
    // // 目前发现这里的数据都被esopMap包含了。。
    // if (unlimitMapForTotal[d]) {
    //   // 影响总股本的限售解禁类型
    //   addShares += unlimitMapForTotal[d].shares;
    // }

    if (givenMap[d]) {
      // 股权激励（授予、暂未解禁）
      addShares += givenMap[d].shares;
    }

    if (esopMap[d]) {
      // 一般性股权激励（授予、而且对于freeShares/totalShares同时增加）
      addShares += esopMap[d].shares;
    }

    if (buyBackMap[d]) {
      // 如果某个日期存在回购注销记录，就反向累计到addShares
      addShares -= buyBackMap[d].shares;
      // logger.info('回购注销', buyBackMap[d]);
    }

    // 遇到转增股
    if (additionalMap[d]) {
      let lastDate = utils.AddDays(d, -1);
      // TODO 先四舍五入取法，后面看看是不是Math.floor
      let add = Math.round(totalSharesCheckMap[lastDate] * additionalMap[d] / 10);
      addShares += add
    }

    // 每一天的totalSharesCheckMap都用addShares来做增加修正
    totalSharesCheckMap[d] += addShares;
    // logger.debug('d: ', d, addShares, '->', totalSharesCheckMap[d]);
  }
  return totalSharesCheckMap;
}

function parseBuybackNotice(logger, notice) {
  let content = notice.text;
  // logger.info('html: ', content);
  // let match = content.match(/回购注销.*共计([\d|,|\.|万|亿]+)股[\S\s]*公司已于(\d+年\d+月\d+日).*完成.*?([\d,]+)股(减少|变更)为([\d,]+)股/i);
  // let match = content.match(/回购注销.*共计([\d,.万亿]+)股/i);
  let match = content.match(/.*公司已于(\d+年\d+月\d+日).*完成.*?([\d,]+)股(减少|变更)为([\d,]+)股/i);
  if (!match) {
    logger.debug(content);
    throw new Error(`CrawlerNoticePageAndParseBuyBack failed ${artCode}`);
  }
  // console.log('found: ', match[1], match[2], match[4]);
  let before = utils.ParseNumberComma(match[2]);
  let after = utils.ParseNumberComma(match[4]);
  let obj = {
    shares: before - after,
    // date: utils.AddDays(utils.ParseChineseDate(match[1]), -1), // NOTE 份额变动应该在前一天
    date: utils.ParseChineseDate(match[1]), // 发现2019-07-09没有在前一天
    beforeShares: before,
    afterShares: after,
    artCode: notice.artCode,
  };
  // console.log('回购: ', obj);
  return obj;
}

/**
 * 获取回购注销限售股记录
 */
async function getSingleStockBuyBacks(output, stockCode) {
  let buyBacks = await db.CompanyNotice.findAll({
    where: {
      stockCode: stockCode,
      title: {[Op.like]: '%回购注销完成%'},
    },
    raw: true,
  });
  // output.info('buyBacks: ', buyBacks.length);
  let buyBackMap = {};
  for (let d of buyBacks) {
    // output.info('d :', d);
    let r = parseBuybackNotice(output, d);
    output.info('[getSingleStockBuyBacks]', r);
    buyBackMap[r.date] = r;
  }
  return buyBackMap;
}

function parseESOPGivenNotice(logger, notice) {
  let match = notice.text.match(/股份总数由\s*([\d,]+)\s*股增加至\s*([\d,]+)\s*股/i);
  if (!match) {
    // logger.debug(notice.text);
    return null;
    throw new Error(`parseESOPGivenNotice match shares failed ${notice.artCode}`);
  }
  let matchDate  = notice.text.match(/上市日期为.*?(\d+年\d+月\d+日)/i);
  if (!matchDate) {
    throw new Error(`parseESOPGivenNotice match date failed ${notice.artCode}`);
  }

  let before = utils.ParseNumberComma(match[1]);
  let after = utils.ParseNumberComma(match[2]);
  let date = utils.ParseChineseDate(matchDate[1]);
  let obj = {
    before: before,
    after: after,
    shares: after - before,
    date: date,
  };
  logger.info('obj:', obj);
  return obj;
}

  // 公告页面-股权激励归属结果页面解析
function parseESOPNotice(logger, notice) {
  let content = notice.text;
  // let match = content.match(/本次归属股[票|份]数量.*?([\d,]+)股[\s\S]*上市流通日.*?(\d+年\d+月\d+日)/i);
  let matchStock = content.match(/本次归属股[票|份]数量.*?([\d,]+)股/i);
  if (!matchStock) {
    logger.debug(content);
    throw new Error(`CrawlerNoticePageAndParseBuyBack failed ${artCode}`);
  }
  let shares = utils.ParseNumberComma(matchStock[1]);

  let matchDate = content.match(/上市流通日.*?(\d+年\d+月\d+日)/i);
  if (!matchDate) {
    logger.debug(content);
    throw new Error(`CrawlerNoticePageAndParseBuyBack failed ${artCode}`);
  }
  let date = utils.ParseChineseDate(matchDate[1]);

  let obj = {
    shares: shares,
    date: date,
  };
  logger.debug('[parseESOPNotice] obj: ', obj);
  return obj;
}

/**
 * 获取限制性股权激励授予计划
 */
async function getSingleStockESOPGivenRecords(output, stockCode) {
  let notices = await db.CompanyNotice.findAll({
    where: {
      stockCode: stockCode,
      title: {[Op.like]: '%登记完成%'},
    },
    raw: true,
  });
  let givenMap = {};
  for (let d of notices) {
    let r = parseESOPGivenNotice(output, d)
    if (!r) {
      continue;
    }
    let key = r.date;
    givenMap[key] = r;
  }
  output.info('givenMap:', givenMap);
  return givenMap;
}

/**
 * 获取股权激励相关的信息（限售解禁）
 */
async function getSingleStockESOPRecords(output, stockCode) {
  let notices = await db.CompanyNotice.findAll({
    where: {
      stockCode: stockCode,
      title: {[Op.like]: '%归属结果%'},
    },
    raw: true,
  });
  let esopMap = {};
  for (let d of notices) {
    let r = parseESOPNotice(output, d)
    output.info('[getESOPRecords]', 'r: ', r, 'd.noticeDate ', d.noticeDate, d.artCode);
    // NOTE: 东财的数据有问题，valuations在公告日就加上了，而不是上市流通日。这里为了构建CheckMap，就先以东财为准做对齐。实际上自己能够回溯真实数据即可。（2021/11/12的公告，2021/11/16上市流通）
    // let key = r.date;
    // let key = d.noticeDate;
    // 这里在2022-11-01的公告又踩坑，freeShares加在了上市流通日2022-11-04
    let key = r.date;
    if (stockCode == '300750' && r.date == '2021-11-16') {
      key = '2021-11-12'; // 强行fix
    }

    esopMap[key] = r;
  }
  return esopMap
}

function parseAdditionalNotice(logger, notice) {
  // 空格换行太麻烦，干脆全部合并掉
  let text = notice.text.replace(/\s/g, '');
  let match = text.match(/现有总股本.*?([\d,]*)股.*?合计转增.*?([\d,]*)股.*?转增后.*?为([\d,]*)股/i);
  if (!match) {
    logger.debug('text: ', text);
    throw new Error(`parseAdditionalNotice ${notice.artCode}`);
  }
  // logger.debug('match: ', match);
  let obj = {
    before: utils.ParseNumberComma(match[1]),
    shares: utils.ParseNumberComma(match[2]),
    after: utils.ParseNumberComma(match[3]),
  };
  return obj;
}

// NOTE: 有专门的分红送转数据来构建了
// /**
//  * 获取转增股相关公告
//  */
// async function getSingleStockAdditionals(output, stockCode) {
//   let notices = await db.CompanyNotice.findAll({
//     where: {
//       stockCode: stockCode,
//       title: {[Op.like]: '%转增%'},
//     },
//     raw: true,
//   });
//   let additionalMap = {};
//   for (let d of notices) {
//     let r = parseAdditionalNotice(output, d)
//     output.info('[getSingleStockAdditionals]', 'r: ', r, 'd.noticeDate ', d.noticeDate, d.artCode);
// 
//     additionalMap[r.date] = r;
//   }
//   return additionalMap;
// }
async function getSingleStockAdditionals(output, stockCode) {
  let bonus = await db.ShareBonus.findAll({
     where: {
       stockCode: stockCode,
     },
  });
  let additionalMap = {};
  for (let b of bonus) {
    if (b.itRatio) {
      // 表示有转增股
      // 以除权除息日来计算
      additionalMap[b.exDividendDate] = +(b.itRatio);
    }
  }
  return additionalMap;
}


// async function test() {
//   await Init(console);
//   getSingleStockBuyBacks(console, '300750')
// }
// test();


/**
 * 获取IPO首日相关的信息，主要用于checkStockShares的构建shares checkMap的起点。（不能使用valuations记录，因为是check目标。另外valuations最早数据只有2018/01/02，这也是要实现checkStockShares构建逻辑来回溯更早数据的原因）
 *   totalShares: 上市时总股本
 *   freeShares: 上市时流通股本
 *   valuations: 临时方案的传入变量。完善逻辑后删除
 */
async function getIPOInfo(output, stockCode, valuations) {
  let ipoInfo = await db.StockShares.findOne({
    where: {
      stockCode: stockCode,
      changeReason: '首发A股上市',
    },
  });
  return {
    totalShares: ipoInfo.totalShares,
    freeShares: ipoInfo.freeShares,
  };

  // // TODO 为了快速验证，封装复杂的、差异化的底层实现细节。这里针对要验证的老八股，人工hardcode，串起来整个执行流程先
  // // if (stockCode == '600656') {
  // //   return {
  // //     freeShares: // 45.01w
  // //     totalShares: , // 246.65w
  // //   };
  // // }


  // // TODO delele，IPO首日的totalShares目前不好获取，先用valuations hardcode。但实际上就类似于IPO只有第一条记录，只用到freeShares。而其他的都是SPO，只用到totalShares.
  // // 对于2018/01/02之前上市的，valuations会缺失数据。所以还是得想办法解决

  // let totalShares = 0;
  // if (valuations.length > 0) {
  //   totalShares = valuations[0].totalShares;
  // } else {
  //   throw new Error('getIPOInfo failed');
  // }

  // return {
  //   totalShares: totalShares,
  // }
}


/**
 * 根据IPO/SPO/解禁/回购等等记录，重建每日的股份数，跟StockValuations.totalShares记录比对，是否一致
 */
async function checkStockShares(output, stockCode) {
  // Step1: 获取db里爬取的每日估值记录（只有2018.01.02起始的）
  let valuations = await db.StockValuation.findAll({
    attributes: ['stockCode', 'totalShares', 'freeShares', 'tradeDate', 'totalMarketCap'],
    where: {stockCode: stockCode},
    order: [['tradeDate', 'ASC']],
    raw: true,
  });
  // 构建valuations的map
  let valuationMap = {};
  for (let r of valuations) {
    r.freeShares = +r.freeShares;
    r.totalShares = +r.totalShares;
    r.totalMarketCap = +r.totalMarketCap;
    r.tradeDate = r.tradeDate.slice(0, 10);
    let date = r.tradeDate;
    valuationMap[date] = hardCodeFixValuations(output, r);
  }


  // Step2: 获取IPO/SPO记录，并构建freeSharesCheckMap
  let records = await db.IPORecord.findAll({
    attributes: ['stockCode', 'issueNum', 'issuePrice', 'issueDate', 'issueListingDate', 'recordType'],
    where: {
      stockCode: stockCode,
      // recordType: '0', // TODO 目前看起来SPO好像不影响freeShares，只影响totalShares
    },
    order: [['issueListingDate', 'ASC']],
    raw: true,
  });
  logger.debug(records);
  // 临时fix date格式。。
  for (let r of records) {
    r.issueListingDate = r.issueListingDate.slice(0, 10);
    r.issueDate = r.issueDate.slice(0, 10);
  }

  let freeChanges = [];
  let totalChanges = [];
  let currentFreeShares = 0;
  let currentTotalShares = 0;
  for (let i = 0; i < records.length; i++) {
    // assert: 是否只有第一条IPO记录的recordType是0:IPO，其他的都是1/2(SPO)
    // output.debug(`${i} -- ${JSON.stringify(records[i])}`);
    if (i == 0) {
      utils.Assert(records[i].recordType == '0')
    } else {
      utils.Assert(records[i].recordType != '0')
    }

    // 构建shares梳理变更的时间点数组
    logger.debug('issueNum:', records[i].issueNum);
    // currentShares += (+records[i].issueNum)
    logger.debug('currentShares:', records[i].issueNum);


    if (records[i].recordType == '0') {
      // 目前看起来SPO好像不影响freeShares，只影响totalShares
      currentFreeShares += (+records[i].issueNum)
      freeChanges.push({
        date: records[i].issueListingDate,
        shares: currentFreeShares,
      });

      let ipoInfo = await getIPOInfo(output, stockCode, valuations);
      currentTotalShares = ipoInfo.totalShares;

      totalChanges.push({
        date: records[i].issueListingDate,
        shares: currentTotalShares,
      });
    } else {
      currentTotalShares += (+records[i].issueNum)
      totalChanges.push({
        date: records[i].issueListingDate,
        shares: currentTotalShares,
      });
    }
  }
  let freeSharesCheckMap = buildSharesMap(output, freeChanges);
  logger.debug('freeChanges: ', freeChanges);
  logger.debug('freeSharesCheckMap: ', freeSharesCheckMap);
  let totalSharesCheckMap = buildSharesMap(output, totalChanges);
  logger.debug('totalChanges: ', totalChanges);
  logger.debug('totalSharesCheckMap: ', totalSharesCheckMap);


  // step3: 获取限制性股票激励
  let givenMap = await getSingleStockESOPGivenRecords(output, stockCode)
  logger.info('givens: ', givenMap);


  let uniqueStockTypeMap = {};
  let type1TotalDiff = 0;
  let type2TotalDiff = 0;
  let unlimitMap = {};
  let unlimitMapForTotal = {};
  // step3: 获取限售解禁记录，再往freeSharesCheckMap里面添加变动记录
  ///////         let events = await db.CompanyEvent.findAll({
  ///////           where: {stockCode: stockCode, eventType: '限售解禁'},
  ///////           raw: true
  ///////         });
  ///////         for (let e of events) {
  ///////           let r = parseLimitEvent(output, e)
  ///////           // logger.debug('r: ', r);
  ///////           uniqueStockTypeMap[r.stockType] = r;
  ///////           if (new Date(r.date) > new Date()) {
  ///////             // 未来的解禁公告，先跳过
  ///////             continue
  ///////           }
  //////            // 只能解析出来变更股份的概数，用valuationMap来check一个精确值
  //////            // logger.debug('\n\n限售解禁记录:', r);
  //////            // 解禁前一天的记录
  //////            let before = getLastTradeDayValuation(r.date, valuationMap);
  //////            if (before == null) {
  //////              // TODO 一般是上市当天
  //////              // logger.debug(records[0]);
  //////              // utils.Assert(r.date == records[0].issueListingDate);
  //////              continue;
  //////            }
  //////            // 解禁当天的记录
  //////            let after = valuationMap[r.date];
  //////            // logger.debug('0: ', before);
  //////            // logger.debug('1: ', after);
  //////            let realDiff = after.freeShares - before.freeShares;
  //////            let distance = (realDiff - r.shares) / realDiff;
  //////            // logger.debug('diffFrees: ', realDiff, '误差: ', distance);
  //////            // logger.debug('diffTotals: ', after.totalShares - before.totalShares);
  //////            // 检测点1: 前后两天的totalShares是否相等的。这里发现也是不一定的。。解禁限售还能同时把totalShares也家上来。。
  //////            if (r.stockType != '股权激励限售股份'
  //////              && r.stockType != '首发原股东限售股份'
  //////              && r.stockType != '定向增发机构配售股份') {
  //////              utils.Assert(before.totalShares + realDiff == after.totalShares);
  //////              type1TotalDiff += realDiff;
  //////            } else {
  //////              utils.Assert(before.totalShares == after.totalShares);
  //////              type2TotalDiff += realDiff;
  //////            }
  //////            // if (r.date == "2021-06-11") {
  //////              logger.debug('before: ', before);
  //////              logger.debug('after: ', after);
  //////              logger.debug('r: ', r);
  //////              logger.debug('realDiff: ', realDiff);
  //////              let accuracy = (realDiff - r.shares) / realDiff;
  //////              logger.debug('accuracy: ', accuracy);
  //////            // }
  //////            
  //////            // 检测点2: 前后两天的freeShares差额是不是大致等于公告里的份额（落在合理比例内）
  //////            utils.Assert(isAcceptAccuracy(output, r.shares, realDiff));
  //////            r.shares = realDiff;
  //////            unlimitMap[r.date] = r;
  //////          }
  //////          // logger.debug('uniqueStockTypeMap: ', uniqueStockTypeMap);
  //////          logger.debug('type1TotalDiff ', type1TotalDiff);
  //////          logger.debug('type2TotalDiff ', type2TotalDiff);
  //////          logger.debug('unlimitMap: ', unlimitMap);

  // step3: 获取限售解禁记录，再往freeSharesCheckMap里面添加变动记录
  // NOTE: 上面CompanyEvent里的限售解禁不准确，需要额外拉取限售解禁记录
  let unlimits = await db.UnlimitStock.findAll({
    where: {
      stockCode: stockCode,
    },
    raw: true
  });

  let aggrUnlimits = aggrAndCheckUnlimitStocks(output, unlimits)
  // output.debug('aggrAndCheckUnlimitStocks: ', aggrUnlimits);
  for (let r of aggrUnlimits) {
    uniqueStockTypeMap[r.stockType] = r;
    if (new Date(r.date) > new Date()) {
      // 未来的解禁公告，先跳过
      continue
    }
    // 解禁前一天的记录
    let before = getLastTradeDayValuation(r.date, valuationMap);
    if (before == null) {
      // TODO 一般是上市当天
      // logger.debug(records[0]);
      // utils.Assert(r.date == records[0].issueListingDate);
      continue;
    }
    // 解禁当天的记录
    let after = valuationMap[r.date];
    // if (after == null ) {
    //   // TODO 新数据还没拉取，先跳过，后面删掉
    //   continue;
    // }
    // logger.debug('r: ', r);
    // logger.debug('0: ', before);
    // logger.debug('1: ', after);
    let realDiff = after.freeShares - before.freeShares;
    // logger.debug('diffTotals: ', after.totalShares - before.totalShares);
    // 检测点1: 前后两天的totalShares是否相等的。这里发现也是不一定的。。解禁限售还能同时把totalShares也家上来。。
    if (r.stockType != '股权激励限售股份'
      && r.stockType != '首发原股东限售股份'
      && r.stockType != '定向增发机构配售股份') {
      utils.Assert(before.totalShares + realDiff == after.totalShares);
      unlimitMapForTotal[r.date] = r;
      type1TotalDiff += realDiff;
    } else {
      utils.Assert(before.totalShares == after.totalShares);
      type2TotalDiff += realDiff;
    }
    
    // 检测点2: 前后两天的freeShares差额等于realDiff
    utils.Assert(before.freeShares + realDiff == after.freeShares);
    unlimitMap[r.date] = r;
  }
  logger.debug('uniqueStockTypeMap: ', uniqueStockTypeMap);
  logger.debug('type1TotalDiff ', type1TotalDiff);
  logger.debug('type2TotalDiff ', type2TotalDiff);
  logger.debug('unlimitMap: ', unlimitMap);
  logger.debug('unlimitMapForTotal: ', unlimitMapForTotal);

  // step4: 获取质押数据
  let pledges = await db.StockPledge.findAll({
    where: {
      stockCode: stockCode,
    },
    raw: true,
  });
  // 把限售解禁的数据，做一个辅助关联数据，对于每次限售解禁的数量中，有多少是处于质押状态的
  let pledgeMap = findoutPledgeMap(output, unlimitMap, pledges);
  // logger.debug('pledgeMap: ', pledgeMap);


  // step5: 获取股权/股票激励计划的记录
  ///////      // NOTE 虽然step3获取限售解禁时，CompanyEvent记录不对，但是没办法了。股权激励没找到专门的数据记录
  ///////      let events = await db.CompanyEvent.findAll({
  ///////        where: {stockCode: stockCode, eventType: '股权激励'},
  ///////        raw: true
  ///////      });
  ///////      for (let e of events) {
  ///////        let r = parseESOPEvent(output, e)
  ///////        // logger.debug('r: ', r);
  ///////      }
  // 确实不能用CompanyEvent。。没有详细信息
  let esopMap = await getSingleStockESOPRecords(output, stockCode);
  output.debug('esopMap: ', esopMap);

  // step6: 获取回购注销限售股公告
  let buyBackMap = await getSingleStockBuyBacks(output, stockCode)  ;
  output.debug('buyBackMap: ', buyBackMap);

  // step7: 获取转增股公告
  let additionalMap = await getSingleStockAdditionals(output, stockCode);
  output.debug('additional: ', additionalMap);

  // Step6: Step1的freeSharesCheckMap与Step2的每日估值记录做比较

  // 先把导致新增流通股的限售解禁、质押数据(unlimitMap/pledgeMap)，融入到freeSharesCheckMap
  freeSharesCheckMap = fixFreeSharesCheckMap(logger, stockCode, freeSharesCheckMap, unlimitMap, pledgeMap, esopMap, additionalMap);

  // 把导致总股本减少、流通股不变的回购注销限售股(buyBackMap)，融入到totalSharesCheckMap
  totalSharesCheckMap = fixTotalSharesCheckMap(logger, totalSharesCheckMap, unlimitMapForTotal, esopMap, additionalMap, buyBackMap, givenMap)

  for (let r of valuations) {
    // 这里从valuationMap取出来覆盖，因为有做过hardCodeFixValuations修正
    let date = r.tradeDate.slice(0, 10);
    // freeShares 流通股本跟IPO/SPO的记录要对上。
    // totalShares包括所有非流通股，总量很大
    logger.debug(`${date} CrawlerTotal[${r.totalShares}] CheckerTotal[${totalSharesCheckMap[date]}] CrawlerFree[${r.freeShares}] CheckerFree[${freeSharesCheckMap[date]}]`);
    // logger.debug(r);
    // logger.debug('pledgeMap: ', pledgeMap);
    // 检测点1: 流通股本的变化，要对得上IPO/限售解禁/
    utils.Assert((+r.freeShares) == freeSharesCheckMap[date])

    // 检测点2: 总股本的变化，要对得上股权激励增发/
    utils.Assert((+r.totalShares) == totalSharesCheckMap[date])
  }

  return [freeSharesCheckMap, valuations];
}


async function checkStockValuations(output, stockCode, sharesMap, valuations) {
  // Step1: 获取日K数据
  let klines = await db.StockKLineDaily.findAll({
    attributes: ['stockCode', 'date', 'closingPrice'],
    where: {stockCode: stockCode},
    order: [['date', 'ASC']],
    raw: true,
  });

  // Step2: 构建klineMap
  let klineMap = {};
  for (let k of klines) {
    klineMap[k.date] = +k.closeingPrice;
  }


  // Step3: 日K收盘价，与sharesMap做向量逐元素乘积，跟valuations做比对
  for (let r of valuations){
    assert(r.totalMarketCap == sharesMap[r.date] * klineMap[r.date])
  }
}

/**
 * 校验单只个股的各方面数据是否有不符合预期的发现
 */
async function CheckSingleStockDataEntry(output, stockCode) {
  // 提取公告里的股本变动信息
  // let notices = await db.CompanyNotice.findAll({
  //   where: {
  //     'stockCode': stockCode,
  //     'noticeDate': {
  //       [Op.gte]: '2022-07-04',
  //       [Op.lte]: '2022-08-22',
  //     },
  //   },
  //   order: [['noticeDate', 'ASC']],
  //   raw: true,
  // });
  // let i = 0;
  // let j = 0;
  // for (let n of notices) {
  //   i++;
  //   if (!n.text) {
  //     continue;
  //   }
  //   // if (n.noticeDate == '2021-11-12') {
  //   //   output.debug(n);
  //   // }

  //   // let match = n.text.match(/(无限[\s\S]*?售[\s\S]*?股[\s\S]*四舍五入)/i);
  //   // let match = n.text.match(/(无[\s\S]*?限[\s\S]*?售[\s\S]*?股[\s\S]*?四舍五入)/i);
  //   let match = n.text.match(/(高[\s\S]*?锁定)/i);
  //   if (match) {
  //     output.debug(`${i}/${j++}`, n.noticeDate, n.artCode, match[1]);
  //   }
  // }

  // step1: 校验每日totalShares
  let [sharesMap, valuations] = await checkStockShares(output, stockCode);

  // step2: 校验每日市值
  // await checkStockValuations(output, stockCode, sharesMap, valuations);

  // step3: 校验
}

module.exports = {
  CheckSingleStockDataEntry,
};
