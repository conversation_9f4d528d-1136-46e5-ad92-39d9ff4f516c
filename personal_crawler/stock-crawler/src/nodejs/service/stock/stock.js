const eastmoney_v2 = require('../../third_client/eastmoney_v2.js');
const common = require('../common');
const utils = require('../../nodejs-common/utils.js');
const Sequelize = require('sequelize');
const Op = Sequelize.Op;

async function dataInitStocks() {
  return dataInitStockDetail("603606")
  // let stocks = [];
  // // let body = await eastmoney_v2.getStockA();
  // let body = await eastmoney_v2.getAllStock();
  // logger.info('[dataInitStocks]', body);
  // for(let r of body.rows) {
  //   stocks.push(r);
  // }
  // logger.info('[dataInitStocks]', 'bulkCreate', stocks.length);
  // await db.Stock.bulkCreate(stocks);

  let exists = await db.StockKLineDaily.findAll({
    attributes: [Sequelize.literal('DISTINCT stockCode')],
    raw: true
  })
  exists = exists.map(x => x.stockCode)
  logger.info('[exist kline stockCodes]', exists.length)
  for (let s of stocks) {
    if (exists.indexOf(s.stockCode) >= 0) {
      continue
    }
    try {
      await dataInitStockDetail(logger, s.stockCode)
    } catch(err) {
      // 跳过失败的部分打印日志先
      logger.error(err)
    }
  }
}

async function CrawlerSingleStockFenshiDetailFromThirdClient(logger, stockCode) {
  // let body = await eastmoney_v2.getStockFenshi(stockCode);
  // logger.info('[dataInitStockFenshi] stockCode', stockCode, 'body', body)

  let ret = await eastmoney_v2.getStockFenshiDetails(logger, stockCode);
  await db.StockValueDetail.bulkCreate(ret.rows, {ignoreDuplicates: true});
  logger.debug(`CrawlerSingleStockFenshiDetailFromThirdClient stock[${stockCode}] fenshi count: ${ret.total}`)
  return ret.total;
}

async function dataInitStockDetail(logger, stockCode) {
  // await dataInitStockKLine(stockCode)
  await dataInitStockFenshi(logger, stockCode)
}


function generateGetAllTypeStocksAndSave(flag, stockType) {
  return async function(output) {
    let oldCount = await db.Stock.count({where: {stockType: stockType}});
    let stocks = [];
    let body = await eastmoney_v2.getAllStockByFlag(output, flag);
    output.debug(`getAllStockByFlag[flag=${flag} stockType=${stockType}] result.count=${body.total}`);
    for(let r of body.rows) {
      r.stockType = stockType;
      stocks.push(r);
    }
    await db.Stock.bulkCreate(stocks, {ignoreDuplicates: true});
  
    let newCount = await db.Stock.count({where: {stockType: stockType}});
  
    return [oldCount, stocks, newCount];
  }
}

var getAllTypeAStocksAndSave = generateGetAllTypeStocksAndSave('A', 'A');
var getAllTypeBStocksAndSave = generateGetAllTypeStocksAndSave('B', 'B');
var getAllTypeTStocksAndSave = generateGetAllTypeStocksAndSave('staq_net', 'T');

async function GetAllStockCodesFromDB(logger) {
  const result = await db.Stock.findAll({
    raw: true,
    attributes: ['stockCode'],
  });
  let stockCodes = result.map(obj => obj.stockCode);
  logger.info('stockCodes: ', stockCodes);
  return stockCodes;
}

async function GetAllDatesFromDB(logger, code) {
  const result = await db.StockKLineDaily.findAll({
    attributes: ['date'],
    where: {stockCode: code},
    raw: true,
  });
  let dates = result.map(obj => obj.date);
  return dates;
}

async function GetAllStocksFromDB(logger) {
  const stocks = await db.Stock.findAll({
    raw: true,
  });
  return stocks;
}

async function GetStockFromDB(logger, code) {
  const stock = await db.Stock.findOne({
    where: {stockCode: code},
    raw: true,
  });
  return stock;
}

function generateUpdateByStockType(flag, stockType) {
  return async function updateByType(output) {
    let oldCount = await db.Stock.count({
      where: {stockType: stockType}
    });
  
    let conflictCount = 0;
    let existCount = 0;
    let missCount = 0;
    let body = await eastmoney_v2.getAllStockByFlag(output, flag);
    for (let r of body.rows) {
      let stock = await db.Stock.findOne({
        where: {stockCode: r.stockCode}
      })
  
      if (!stock) {
        // db缺少meta记录
        missCount += 1;
        continue;
      }
  
      if (!stock.stockType) {
        // 没值，直接save
        stock.stockType = stockType;
        await stock.save();
      } else {
        if (stock.stockType != stockType) {
          conflictCount += 1;
        } else {
          existCount += 1;
        }
      }
    }
  
    let newCount = await db.Stock.count({
      where: {stockType: stockType}
    });
  
    output.setFillStockMetaByFlag(flag, oldCount, body.total, newCount, conflictCount, 0, existCount, missCount);
    return newCount
  }
}

var updateByTypeB = generateUpdateByStockType('B', 'B');
var updateByTypeStaq = generateUpdateByStockType('staq_net', 'T');

function generateUpdateByBoolValueMethod(dbKey, flag) {
  return async function(output) {
    let oldCount = await db.Stock.count({
      where: {[dbKey]: true}
    });

    let conflictCount = 0;
    let existCount = 0;
    let missCount = 0;
    let body = await eastmoney_v2.getAllStockByFlag(output, flag);
    let checkMap = {};
    let confictDetails = [];
    let missDetails = [];
    let stockTypeCountMap = {aCount: 0, bCount: 0, noTypeCount: 0};

    for (let r of body.rows) {
      let stock = await db.Stock.findOne({
        where: {stockCode: r.stockCode}
      })

      checkMap[r.stockCode] = true;

      if (!stock) {
        // db缺少meta记录
        missDetails.push(`${r.stockCode}:${r.stockName}`);
        missCount += 1;
        continue;
      }

      if (stock.stockType == 'A') {
        stockTypeCountMap.aCount += 1;
      } else if (stock.stockType == 'B') {
        stockTypeCountMap.bCount += 1;
      } else {
        stockTypeCountMap.noType += 1;
      }

      if (stock[dbKey] === null) {
        // 没值，直接save
        stock[dbKey] = true;
        await stock.save();
      } else {
        if (stock[dbKey] === false) {
          // 检测db记录为isGem=false，但是实际拉取为true的冲突
          conflictCount += 1;
          confictDetails.push(`${stock.stockCode}:${stock.stockName}:db[false]:pull[true]`)
        } else {
          existCount += 1;
        }
      }
    }

    let newCount = await db.Stock.count({
      where: {[dbKey]: true}
    });

    // 把未设置isGem的stocks，全部设置为false，方便后续变动检测出冲突
    let unsetStocks = await db.Stock.findAll({
      where: {[dbKey]: null}
    });
    for (let s of unsetStocks) {
      s[dbKey] = false;
      await s.save();
    }

    let setStocks = await db.Stock.findAll({
      where: {[dbKey]: true}
    });
    for (let s of setStocks) {
      // 检测db记录为isGem=true，但是实际拉取为false的冲突
      if (!checkMap[s.stockCode]) {
        conflictCount += 1;
        confictDetails.push(`${s.stockCode}:${s.stockName}:db[true]:pull[false]`)
      }
    }

    output.setFillStockMetaByFlag(flag, oldCount, body.total, newCount, conflictCount, 0, existCount, missCount, confictDetails, missDetails, stockTypeCountMap);
    return newCount
  }
}

var updateByGem = generateUpdateByBoolValueMethod('isGem', 'gem');
var updateByKcb = generateUpdateByBoolValueMethod('isKcb', 'kcb');
var updateByDanger = generateUpdateByBoolValueMethod('isDanger', 'st');


async function updateTypeCityAndType(output, flag, stockCity, stockType) {
  let oldCount = await db.Stock.count({
    where: {stockCity: stockCity, stockType: stockType}
  });

  let conflictCount = 0;
  let existCount = 0;
  let missCount = 0;
  let partCount = 0;
  let body = await eastmoney_v2.getAllStockByFlag(output, flag);
  for (let r of body.rows) {
    let stock = await db.Stock.findOne({
      where: {stockCode: r.stockCode}
    })

    if (!stock) {
      // db缺少meta记录
      missCount += 1;
      continue;
    }

    if (!stock.stockCity && !stock.stockType) {
      // 两个字段都没值，直接save
      stock.stockCity = stockCity;
      stock.stockType = stockType;
      await stock.save();
      continue;
    }

    if (stock.stockCity && stock.stockCity != stockCity) {
        // stockCity 非Null，并且不为sh，值冲突
        conflictCount += 1;
    } else if (stock.stockType && stock.stockType != stockType) {
        conflictCount += 1;
    } else {
      if (stock.stockCity && stock.stockType) {
        existCount += 1;
      } else {
        // 部分字段缺失，也需要save
        partCount += 1;
        stock.stockCity = stockCity;
        stock.stockType = stockType;
        await stock.save();
      }
    }
  }

  let newCount = await db.Stock.count({
    where: {stockCity: stockCity, stockType: stockType}
  });

  output.setFillStockMetaByFlag(flag, oldCount, body.total, newCount, conflictCount, partCount, existCount, missCount);
  return newCount
}

async function GetStockFromThirdClient(output, stockCode) {
  let stock;
  let [oldCount, stocks, newCount] = await getAllTypeAStocksAndSave(output);
  for(let r of stocks) {
    if (r.stockCode == stockCode) {
      stock = r;
    }
  }

  const [oldCountB, stocksB, newCountB] = await getAllTypeBStocksAndSave(output);
  output.setFillStockBMeta(oldCountB, stocksB.length, newCountB);
  const [oldCountT, stocksT, newCountT] = await getAllTypeTStocksAndSave(output);
  output.setFillStockTMeta(oldCountT, stocksT.length, newCountT);

  // 更新上证类型
  let shCount = await updateTypeCityAndType(output, 'shA', 'sh', 'A');

  // 更新深证类型
  let szCount = await updateTypeCityAndType(output, 'szA', 'sz', 'A');

  // 更新北证类型
  let bjCount = await updateTypeCityAndType(output, 'bjA', 'bj', 'A');

  await updateByGem(output);
  await updateByKcb(output);
  await updateByDanger(output);

  // 更新B股类型
  let bCount = await updateByTypeB(output);
  let tCount = await updateByTypeStaq(output);

  // newCount = await db.Stock.count();
  output.setFillStockMeta(oldCount, stocks.length, newCount, shCount+szCount+bjCount);

  return stock;
}


async function GetStockDailyFromThirdClient(output, stockCode, specifyDate) {
  let [oldCount, firstDate, lastDate] = await common.GetExistDailyInfoFromDB(stockCode);

  if (oldCount > 0) {
    output.setExistDate(firstDate, lastDate)
  }

  let body = await eastmoney_v2.getStockKLine(output, stockCode);
  await db.StockKLineDaily.bulkCreate(body.rows, {ignoreDuplicates: true});
  let newCount = await db.StockKLineDaily.count({
    where: {stockCode: stockCode}
  });
  output.setFillDaily(oldCount, body.total, newCount);

  let first;
  let last;
  for (let i of body.rows) {
    let r = new Date(i.date);
    if (!first) {
      first = r;
    }
    if (!last) {
      last = r;
    }
    if (r.getTime() < first.getTime()) {
      first = r;
    }
    if (r.getTime() > last.getTime()) {
      last = r;
    }
  }
  output.setCrawlerDate(first, last);

  let specifyDateData;
  for (let i of body.rows) {
    if (i.date == specifyDate) {
      specifyDateData = i;
      break;
    }
  }

  if (!specifyDateData) {
    specifyDateData = await db.StockKLineDaily.findOne({
      where: {
        stockCode: stockCode,
        date: specifyDate
      }
    });
  }
  output.setSpecifyDateDate(specifyDateData)
}

// 只拉股票本身1对1的meta信息。不拉以日期为维度的daily数据（比如日K、分时、历史股本shares等）
async function CrawlerAllStockMetaFromThirdClient(output) {
  // stockType=A，表示A股
  // stockType=B，表示B股
  // stockType=T，表示两网及退市
  // const pullFuncs = [getAllTypeAStocksAndSave, getAllTypeBStocksAndSave, getAllTypeTStocksAndSave];
  const pullFuncs = [getAllTypeTStocksAndSave];
  let oldCount = 0;
  let newCount = 0;
  let stocks = [];
  for (let f of pullFuncs) {
    const [oc, s, nc] = await f(output);
    oldCount += oc;
    newCount += nc;
    stocks = stocks.concat(s);
  }

  output.setFillMeta(oldCount, stocks.length, newCount);

  // // 顺便拉一下分时数据 => 在daily数据里，不归属到meta数据
  // for (let s of stocks) {
  //   await CrawlerSingleStockFenshiDetailFromThirdClient(output, s.stockCode);
  // }

  // // 顺便临时拉一下SharesInfo
  // for (let s of stocks) {
  //   await CrawlerSingleStockSharesHistoryFromThirdClient(output, s.stockCode);
  // }

  // // 顺便临时拉一下SharesInfo
  // for (let s of stocks2) {
  //   let shareInfo = await eastmoney_v2.GetStockShareInfo(output, s.stockCode);
  //   shareInfo.stockName = s.stockName;
  //   await db.BStockShares.create(shareInfo, {ignoreDuplicates: true});
  //   output.debug('BStockShares pull: ', s.stockCode, shareInfo);
  // }
}

// 每日分时数据（不包含日K，这个可以很多天一起聚合拉取）
async function CrawlerAllStockDailyFromThirdClient(output) {
  let oldCount = await db.StockKLineDaily.count();
  let fenshiCount = await db.StockValueDetail.count();

  let stocks = await db.Stock.findAll({
    attributes: ['stockCode', 'stockState', 'stockName'],
    // where: {stockType: 'B'},
    raw: true
  });

  let total = 0;
  let delistCount = 0;
  let stCount = 0;
  let i = -1;
  let fenshiTotal = 0;
  
  for (let s of stocks) {
    if (s.stockState == "st") {
      stCount += 1;
    } else if (s.stockState == "delist") {
      delistCount += 1;
    }

    // output.debug('s: ', s)
    i += 1;
    // 判断是否存在最新日K
    let [sOldCount, sFirstDate, sLastDate] = await common.GetExistDailyInfoFromDB(s.stockCode);
    if (sOldCount > 10) {
      // 如果已经有最近日期的K数据，则跳过当前stock的爬取
      let lastTradeDate = await eastmoney_v2.GetLastTradeDate();
      if (lastTradeDate == sLastDate) {
        output.debug(`[日K] i=${i}/stock=${s.stockCode}/exist(count=${sOldCount} date=[${sFirstDate}, ${sLastDate}]) 最新`);
        continue
      } else {
        // output.info("s.stockState:", s.stockState)
        if (s.stockState == "delist") {
          // 已经退市
          output.debug(`[日K] i=${i}/stock=${s.stockCode}/exist(count=${sOldCount} date=[${sFirstDate}, ${sLastDate}]) 已退市`);
        } else {
          output.debug(`[日K] i=${i}/stock=${s.stockCode}/exist(count=${sOldCount} date=[${sFirstDate}, ${sLastDate}]) ADD Incremental(lastTradeDate=${lastTradeDate} lastDate=${sLastDate})`);
        }
      }
    }

    let before = total;
    let args = {};
    if (sOldCount < 10) {
      // 数量少的（新上市）或者没拉拉取过的（sOldCount==0）的情况，不介意lmt传大一些
      args.lmt = 1000000;
      output.debug2(`${s.stockCode} : sOldCount=${sOldCount} : set lmt=${args.lmt}`);
    }
    let body = await eastmoney_v2.getStockKLine(output, s.stockCode, args);
    await db.StockKLineDaily.bulkCreate(body.rows, {ignoreDuplicates: true});
    total += body.total;
    output.debug(`[日K] i=${i}/stock=${s.stockCode}/total(${before} + pull:${body.total} => ${total})`);

    // 拉取分时数据
    fenshiTotal += await CrawlerSingleStockFenshiDetailFromThirdClient(output, s.stockCode);
  }

  let newCount = await db.StockKLineDaily.count();
  output.setFillDaily(oldCount, total, newCount);
  output.setDelistCount(delistCount);
  output.setStCount(stCount);

  let newFenshiCount = await db.StockValueDetail.count();
  output.setFillFenshi(fenshiCount, fenshiTotal, newFenshiCount);
}

async function _crawlerSingleStockValuationsFromThirdClient(output, i, total, s) {
  try {
    // output.debug('s: ', s)
    // 判断是否存在最新日K
    let [sOldCount, sFirstDate, sLastDate] = await common.GetExistValuationFromDB(s.stockCode);
    if (sOldCount > 0) {
      // 如果已经有最近日期的估值数据，则跳过当前stock的爬取
      let lastTradeDate = await eastmoney_v2.GetLastTradeDate();
      if (lastTradeDate == sLastDate) {
        output.debug(`[估值] i=${i}/stock=${s.stockCode}/exist(count=${sOldCount} date=[${sFirstDate}, ${sLastDate}]) 最新`);
        return
      } else {
        // output.info("s.stockState:", s.stockState)
        if (s.stockState == "delist") {
          // 已经退市
          output.debug(`[估值] i=${i}/stock=${s.stockCode}/exist(count=${sOldCount} date=[${sFirstDate}, ${sLastDate}]) 已退市`);
        } else {
          output.debug(`[估值] i=${i}/stock=${s.stockCode}/exist(count=${sOldCount} date=[${sFirstDate}, ${sLastDate}]) ADD Incremental(lastTradeDate=${lastTradeDate} lastDate=${sLastDate})`);
        }
      }
    }

    output.debug(`[估值] i=${i}/stock=${s.stockCode}/total=${total}`);
    let body = await eastmoney_v2.GetStockValuation(output, s.stockCode, sLastDate);
    await db.StockValuation.bulkCreate(body.rows, {ignoreDuplicates: true});
    return body.total;
  } catch(err) {
    // TODO 暂时不知道为什么有一些股票获取不到数据。先跳过
    logger.error(err);
    logger.warn("code failed: ", s.stockCode, s.stockName);
    return -1;
  }
}

async function CrawlerSingleStockValuationsFromThirdClient(output, stockCode) {
  let oldCount = await db.StockValuation.count({where: {'stockCode': stockCode}});
  let stock = await db.Stock.findOne({
    attributes: ['stockCode', 'stockState'],
    where: {'stockCode': stockCode},
    raw: true
  });
  let pullCount = await _crawlerSingleStockValuationsFromThirdClient(output, 0, 0, stock)

  let newCount = await db.StockValuation.count({where: {'stockCode': stockCode}});
  output.setFillValuations(oldCount, pullCount, newCount);
}

async function CrawlerAllStockValuationsFromThirdClient(output) {
  let oldCount = await db.StockValuation.count();

  let stocks = await db.Stock.findAll({
    attributes: ['stockCode', 'stockState'],
    where: {'stockType': 'A'}, // 不知道为什么B股的Valuations获取方式全部失败
    raw: true
  });

  let total = 0;
  let failedCount = 0;
  let i = -1;
  for (let s of stocks) {
    i += 1;
    let count = await _crawlerSingleStockValuationsFromThirdClient(output, i, total, s)
    if (count == -1) {
      failedCount += 1;
      continue;
    }
    total += count;
  }

  let newCount = await db.StockValuation.count();
  output.setFillValuations(oldCount, total, newCount);
  output.setFillValuationsFailed(failedCount);
}


// 判断是否新能源业务
function _decideIsCleanEnergy(businessScope) {
  // 包含电池
  if (businessScope.indexOf("电池") >= 0) {
    return true;
  }

  return false;
}

async function CrawlerAllStockFundamentalFromThirdClient(output) {
  let stocks = await db.Stock.findAll({
    attributes: ['stockCode', 'stockName'],
    raw: true
  });

  let now = new Date();
  let i = 0;
  let existCount = 0;
  for (let s of stocks) {
    let exist = await db.StockFundamental.findOne({
      where: {stockCode: s.stockCode},
    })
    if (exist && now.getTime() - exist.crawlerTime.getTime() < 30*24*3600000) {
      // 一个月内爬过的记录就不更新了。
      // TODO 后面再新增强制拉取的逻辑
      existCount += 1;
      continue
    }

    let businessScope = await eastmoney_v2.GetStockBusiness(output, s.stockCode);
    if (!businessScope) {
      output.warn("[CrawlerAllStockFundamentalFromThirdClient] GetStockBusiness failed: ", s.stockCode, s.stockName);
      continue;
    }
    let isCleanEnergy = null; // 默认不存false，存空值
    if (_decideIsCleanEnergy(businessScope)) {
      isCleanEnergy = true;
    }
    await db.StockFundamental.create({
      stockCode: s.stockCode,
      stockName: s.stockName,
      businessRaw: businessScope,
      crawlerTime: now,
      isCleanEnergy: isCleanEnergy,
    }, {ignoreDuplicates: true});

    i += 1;
    if (i % 40 == 0) {
      output.debug(`get business: new[${i}] exist[${existCount}]`);
    }

    // 停1ms
    await utils.Sleep(1);
  }
  output.debug(`[finish] get business total: new[${i}] exist[${existCount}]`);
}

async function crawlerAllSPORecords(output) {
  let spoBody = await eastmoney_v2.GetSPO(output)
  await db.IPORecord.bulkCreate(spoBody.rows, {ignoreDuplicates: true});
  uniqueMap = {};
  for (let r of spoBody.rows) {
    uniqueMap[r.stockCode] = true;
  }
  output.setFillSPO(spoBody.total, Object.keys(uniqueMap).length);
}

async function CrawlerSingleStockNoticesFromThirdClient(output, code) {
  let page = 1;
  let maxLoopTimes = 200; // 避免死循环的最大次数
  let lastDate = '';
  // let uniq = {};
  // let uniq1 = {};
  // let uniq2 = {};
  // let uniq3 = {};
  let notices = [];
  for (page = 1; page < maxLoopTimes; page += 1) {
    let ret = await eastmoney_v2.GetCompanyNotices(output, code, page);
    if (!ret) {
      break;
    }
    notices = notices.concat(ret.rows);
  }

  //   - 保留原始数据
  await db.CompanyNotice.bulkCreate(notices, {ignoreDuplicates: true});
  lastDate = notices[notices.length-1].noticeDate;
  output.setFillNotices(notices.length, lastDate, `loop[p:${page}/max:${maxLoopTimes}]`);
  // for (let n of notices) {
  //   uniq[n.NoticeType1] = true;
  //   uniq[n.NoticeType2] = true;
  //   uniq[n.NoticeType3] = true;
  //   uniq1[n.NoticeType1] = true;
  //   uniq2[n.NoticeType2] = true;
  //   uniq3[n.NoticeType3] = true;
  // }
  // console.log(Object.keys(uniq));
  // console.log(Object.keys(uniq1));
  // console.log(Object.keys(uniq2));
  // console.log(Object.keys(uniq3));
  // console.log(Object.keys(uniq).length);
  // console.log(Object.keys(uniq1).length);
  // console.log(Object.keys(uniq2).length);
  // console.log(Object.keys(uniq3).length);


  // Step2 拉取公告正文并保存
  notices = await db.CompanyNotice.findAll({
    attributes: ['artCode'],
    where: {
      stockCode: code,
      text: null
    },
  });
  output.debug('empty text: ', notices.length);
  let i = 0;
  for (let n of notices) {
    let content = await eastmoney_v2.GetNoticePage(output, n.artCode)
    for (let k in content) {
      n[k] = content[k];
    }
    await n.save();
    output.debug('save: ', i++, n.artCode);
  }
}

async function _crawlerSingleStockCompanyEventsFromThirdClient(output, stock, tag) {
  let page = 1;
  let total = 0;
  let maxLoopTimes = 200; // 避免死循环的最大次数
  let lastDate = '';
  for (page = 1; page < maxLoopTimes; page += 1) {
    let ret = await eastmoney_v2.GetCompanyEvents(output, stock, tag, page);
    if (!ret) {
      break;
    }
    let events = ret.rows;
    //   - 保留原始数据
    let res = await db.CompanyEvent.bulkCreate(events, {ignoreDuplicates: true});
    // logger.debug('result: ', res, 'lengh: ', events.length);
    total += events.length;
    // logger.debug('last: ', events[events.length-1]);
    lastDate = events[events.length-1].noticeDate;
  }

  return [total, lastDate, page, maxLoopTimes];
}

async function CrawlerSingleStockCompanyEventsFromThirdClient(output, stockCode) {
  let stock = await db.Stock.findOne({
    attributes: ['stockCode', 'stockName', 'stockCity'],
    where: {stockCode: stockCode},
    raw: true
  });
  if (!stock || !stock.stockCity) {
    throw new Error(`CrawlerSingleStockCompanyEventsFromThirdClient Not Found`);
  }

  let tag = `${stock.stockCode}.${stock.stockCity}`;
  let [total, lastDate, page, maxLoopTimes] = await _crawlerSingleStockCompanyEventsFromThirdClient(output, stock.stockCode, tag);
  output.setFillCompanyEvents(total, lastDate, `loop[p:${page}/max:${maxLoopTimes}]`);
}

function isSTName(name) {
  if (name.indexOf('ST') >= 0) {
    return true
  }
  if (name.indexOf('PT') >= 0) {
    return true;
  }
  return false;
}

async function CrawlerAllStockCompanyEventsFromThirdClient(output) {
  let stocks = await db.Stock.findAll({
    attributes: ['stockCode', 'stockName', 'stockCity'],
    raw: true
  });

  let oldCount = await db.CompanyEvent.count();

  let total = 0;
  // let i = 0;
  // for (let s of stocks) {
  //   let tag = `${s.stockCode}.${s.stockCity}`;
  //   let [t, l, p, m] = await _crawlerSingleStockCompanyEventsFromThirdClient(output, s.stockCode, tag);
  //   output.debug(`${s.stockCode}[${++i}/${stocks.length}]: total=${t} lastDate=${l} loop[p:${p}/max:${m}]`);
  //   total += t;
  // }
  let newCount = await db.CompanyEvent.count();
  output.setFillCompanyEvents(stocks.length, oldCount, total, newCount);

  let sts = [];
  // 从公告里面提取生成st记录
  for (let s of stocks) {
    let records = await db.CompanyEvent.findAll({
      attributes: ['stockCode', 'level1Content', 'noticeDate'],
      where: {
        stockCode: s.stockCode,
        eventType: '名称变动',
      },
      raw: true
    });

    for (let r of records) {
      let match = r.level1Content.match(/(.*)→(.*)/i);
      if (!match) {
        throw new Error(`Wrong Format: ` + r);
      }
      let before = match[1];
      let after = match[2];
      if (!isSTName(before) && isSTName(after)) {
        // 非ST => ST
        // output.debug(s.stockCode, 'before: ', before, 'after: ', after, r.noticeDate);
        sts.push({
          stockCode: s.stockCode,
          beforeName: before,
          afterName: after,
          noticeDate: r.noticeDate,
          stDirection: '1',
        });
      }
      if (isSTName(before) && !isSTName(after)) {
        // 从ST => 非ST
        // output.debug(s.stockCode, 'before: ', before, 'after: ', after, r.noticeDate);
        sts.push({
          stockCode: s.stockCode,
          beforeName: before,
          afterName: after,
          noticeDate: r.noticeDate,
          stDirection: '0',
        });
      }
    }
    // output.debug(`genST:${s.stockCode}[${++i}/${stocks.length}]`);
  }
  await db.GeneratedStRecord.bulkCreate(sts, {ignoreDuplicates: true});
  output.debug(`genST:${sts.length}`);
  output.setFillSTRecords(sts.length);

  // 从公告里面提取上市记录
  let listings = [];
  let records = await db.CompanyEvent.findAll({
    attributes: ['stockCode', 'eventType', 'specificEventType', 'level1Content', 'level2Content', 'noticeDate'],
    where: {
      eventType: {[Op.like]: '%上市%'}
    },
    raw: true,
  });
  for (let r of records) {
    utils.Assert(r.eventType == '上市状态变动');
    utils.Assert(['新股上市', '暂停上市', '恢复上市', '转板上市'].indexOf(r.specificEventType) >= 0);
    if (r.specificEventType == '新股上市') {
      let match = r.level1Content.match(/上市日期：([\d-]+)，交易市场：(\S+)/i);
      if (!match) {
        throw new Error(`Wrong Format: ` + r);
      }
      listings.push({
        stockCode: r.stockCode,
        listingType: '1', // 新股上市
        listingDate: match[1],
        market: match[2],
        noticeDate: r.noticeDate,
      });
    } else if (r.specifyEventType == '暂停上市') {
      let match = r.level1Content.match(/将于([\d-]+)暂停上市/i);
      if (!match) {
        throw new Error(`Wrong Format: ` + r);
      }
      listings.push({
        stockCode: r.stockCode,
        listingType: '2', // 暂停上市
        listingDate: match[1],
        reason: r.level2Content,
        noticeDate: r.noticeDate,
      });
    } else if (r.specifyEventType == '恢复上市') {
      let match = r.level1Content.match(/将于([\d-]+)恢复上市/i);
      if (!match) {
        throw new Error(`Wrong Format: ` + r);
      }
      listings.push({
        stockCode: r.stockCode,
        listingType: '3', // 恢复上市
        listingDate: match[1],
        reason: r.level2Content,
        noticeDate: r.noticeDate,
      });
    } else if (r.specifyEventType == '转板上市') {
      let match = r.level1Content.match(/上市日期：([\d-]+)，交易市场：(\S+)，转板前交易市场：(\S+)/i);
      if (!match) {
        throw new Error(`Wrong Format: ` + r);
      }
      listings.push({
        stockCode: r.stockCode,
        listingType: '4', // 转板上市
        listingDate: match[1],
        market: match[2],
        reason: match[3],
        noticeDate: r.noticeDate,
      });
    }
  }

  await db.GeneratedListingRecord.bulkCreate(listings, {ignoreDuplicates: true});
  output.debug(`genLising:${listings.length}`);
  output.setFillListingRecords(listings.length);
}

async function CrawlerSingleStockUnlimitedFromThirdClient(output, stock) {
  let ret = await eastmoney_v2.GetUnlimitStocks(logger, stock)
  await db.UnlimitStock.bulkCreate(ret.rows, {ignoreDuplicates: true});
  output.setFillUnlimted(ret.total);
}

async function CrawlerSingleStockPledgesFromThirdClient(output, stock) {
  let ret = await eastmoney_v2.GetSingleStockPledges(output, stock)
  await db.StockPledge.bulkCreate(ret.rows, {ignoreDuplicates: true});
  output.setFillPledges(ret.total);
}


function convertEvent2IPOs(output, events) {
  let records = [];
  output.debug('events: ', events);
  output.debug('length: ', events.length);

  return records;
}

async function CrawlerSingleStockIPORecordsFromThirdClient(output, stock) {
  let rows = [];
  // step1: IPO记录
  logger.debug('[Step1] GetIPO Start...');
  let ipoRecord = await eastmoney_v2.GetIPO(output, stock)
  if (!ipoRecord) {
    throw new Error('CrawlerSingleStockIPORecordsFromThirdClient failed');
  }
  rows.push(ipoRecord);
  logger.debug('[Step1] GetIPO Success');

  // // step2: 公司大事-限售解禁
  // logger.debug('[Step2] GetCompanyEvents Start...');
  // await CrawlerSingleStockCompanyEventsFromThirdClient(output, stock)
  // //   - 提取限售解禁相关的事件
  // // 或者不把限售解禁加到IPO记录了。要用来check的时候取出来解析就好了。大事公告也没有详细的数量，只有概数xxx万股、亿股
  // // let events = await db.CompanyEvent.findAll({
  // //   where: {stockCode: stock, eventType: '限售解禁'},
  // //   raw: true
  // // });
  // // let convertedRecords = convertEvent2IPOs(output, events)
  // // rows = rows.concat(convertedRecords);
  // logger.debug('[Step2] GetCompanyEvents Success');

  // step3: 公司公告-股本变动（股票激励）

  // await db.IPORecord.bulkCreate(rows, {ignoreDuplicates: true});
  await db.IPORecord.bulkCreate(rows, {
    updateOnDuplicate: ['issueDate'] // 替换为需要更新的字段
  });
}

async function crawlerAllIPORecords(output) {
  let stocks = await db.Stock.findAll({
    attributes: ['stockCode', 'stockName'],
    raw: true
  });

  let faildIpoCount = 0;
  let exist = 0;
  let rows = [];
  for (let s of stocks) {
    let ipo = await db.IPORecord.findOne({
      where: {stockCode: s.stockCode, recordType: '0'},
      raw: true,
    });
    if (ipo) {
      ++exist;
      // 合法性校验，如果有非法字段，那么也要继续往下走，从eastmoney获取后更新
      if (new Date(ipo.issueDate) == 'Invalid Date') {
        // 往下走
      } else {
        // 没有异常情况才continue
        continue;
      }
    }
    
    try {
      await CrawlerSingleStockIPORecordsFromThirdClient(output, s.stockCode)
    } catch(err) {
      logger.error(err);
      ++faildIpoCount;
    }
  }

  let uniqueMap = {};
  for (let r of rows) {
    uniqueMap[r.stockCode] = true;
  }
  output.setFillIPO(rows.length, Object.keys(uniqueMap).length, faildIpoCount, stocks.length, exist);
}

async function CrawlerAllIPOSPORecordsFromThirdClient(output) {
  await crawlerAllIPORecords(output)
  await crawlerAllSPORecords(output)
}

async function CrawlerAllSPORecordsFromThirdClient(output) {
  await crawlerAllSPORecords(output)
}

async function CrawlerSingleStockSharesHistoryFromThirdClient(output, stock) {
  let sharesInfos = await eastmoney_v2.GetStockShareList(output, stock)
  await db.StockShares.bulkCreate(sharesInfos.rows, {ignoreDuplicates: true});
  if (output.setFillSharesHistoryCount) {
    output.setFillSharesHistoryCount(sharesInfos.total);
  }
}

async function CrawlerSingleStockBonusFromThirdClient(output, stock) {
  let oldCount = await db.ShareBonus.count({where: {stockCode: stock}});

  let bonus = await eastmoney_v2.GetStockBonus(output, stock)
  await db.ShareBonus.bulkCreate(bonus.rows, {ignoreDuplicates: true});

  let newCount = await db.ShareBonus.count({where: {stockCode: stock}});
  output.setFillBonus(oldCount, bonus.total, newCount);
}

module.exports = {
  GetAllStockCodesFromDB,
  GetAllDatesFromDB,
  GetAllStocksFromDB,
  GetStockFromDB,
  GetStockFromThirdClient,
  GetStockDailyFromThirdClient,
  CrawlerAllStockMetaFromThirdClient,
  CrawlerAllStockDailyFromThirdClient,
  CrawlerAllStockFundamentalFromThirdClient,
  CrawlerAllStockValuationsFromThirdClient,
  CrawlerSingleStockValuationsFromThirdClient,
  CrawlerSingleStockIPORecordsFromThirdClient,
  CrawlerSingleStockCompanyEventsFromThirdClient,
  CrawlerAllStockCompanyEventsFromThirdClient,
  CrawlerSingleStockNoticesFromThirdClient,
  CrawlerSingleStockUnlimitedFromThirdClient,
  CrawlerSingleStockPledgesFromThirdClient,
  CrawlerAllSPORecordsFromThirdClient,
  CrawlerAllIPOSPORecordsFromThirdClient,
  CrawlerSingleStockBonusFromThirdClient,
  CrawlerSingleStockSharesHistoryFromThirdClient,
  CrawlerSingleStockFenshiDetailFromThirdClient,
};
