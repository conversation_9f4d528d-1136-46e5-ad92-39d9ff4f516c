'use strict';

const request = require('request-promise');

class Stock {
  constructor(params) {
    this._code = params.code;
  }

  async getInfo() {
    let url = 'http://pdfm.eastmoney.com/EM_UBG_PDTI_Fast/api/js?rtntype=5&id=6038761&type=r&iscr=false';

    let body = await request.get(url);
    try {
      body = body.slice(1, -1);
      body = JSON.parse(body);
      logger.info(body);
    }catch(err) {
      logger.info(body);
    }
  }
};

module.exports = Stock;
