'use strict';
const request = require('../../../nodejs-common/request');

async function getDelistedStocks(logger) {
    // 页面：https://www.cninfo.com.cn/new/commonUrl?url=data/person-stock-data-tables#zzts
    let options = {
        url: "https://www.cninfo.com.cn/data20/listedCompany/stop",
        method: "POST",
        json: true
    };

    let body = await request.makeRequest(logger, options, 1);
    logger.info("body: ", body.data);
    return {
        total: body.data.total,
        rows: body.data.records,
    };
}

// async function test() {
//     await getDelistedStocks(console).then((data) => {
//         console.log(data);
//     });
// }
// test()

module.exports = {
    getDelistedStocks,
}