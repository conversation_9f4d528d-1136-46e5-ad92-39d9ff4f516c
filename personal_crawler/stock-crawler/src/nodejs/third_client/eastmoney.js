'use strict';

const qs = require('querystring');
const request = require('request-promise');

class EastMoney {
  constructor() {
    this._token = '4f1862fc3b5e77c150a2b985b12db0fd';
  }

  async getIndexValueMinutely(indexCode, indexCity) {
    let url = 'http://pdfm.eastmoney.com/EM_UBG_PDTI_Fast/api/js';
    let id = indexCode; // +'1'表示沪，+'2'表示深
    if (indexCity == 'sh') {
      id += '1';
    } else if (indexCity == 'sz') {
      id += '2';
    } else {
      throw new Error('wrong indexCity', indexCity);
    }
    let params = {
      rtntype: 5,
      token: this._token,
      cb: '',
      id: id,
      type: 'r',
      iscr: false,
      _: Date.now() - 24*3600000
    };
    url += '?' + qs.stringify(params);
    
    let body = await request.get(url);
    // logger.info('[_getIndexHistoryApi]', params, url);
    try {
      body = body.slice(1, -1);
      // 拿到的body是一个relaxed json，字段名没有双引号，无法JSON.parse()
      eval('body = '+body);
      body.total = body.recordsFiltered;
      delete body.recordsFiltered;
      // logger.info(body);
      let rows = [];
      for(let d of body.data) {
        d = d.split(',');
        rows.push({
          time: new Date(d[0]),
          indexCode: indexCode,
          price: +d[1]*1000, // 白线(蓝线)，实际分时价格
          avgPrice: +d[3]*1000, // 黄线，均价
          volume: +d[2] // 成交量,单位(手)
        });
      }
      body.rows = rows;
      delete body.data;
    }catch(err) {
      logger.error(err);
      debugger;
      // logger.info(body);
    }
    return body;
  }

  /**
   * 获取沪深指数或者沪深个股的api，具体含义和功能暂不清楚
   * @url 'http://nufm.dfcfw.com/EM_Finance2014NumericApplication/JS.aspx'
   * @params.cmd 板块(指数或者基金)
   *    C.1:上证指数, 
   *    C.5:深圳指数, 
   *
   *    C._A:沪深A股, 
   *      C.2:上证A股, 
   *      C._SZAME:深证A股, 
   *      C.BK05011:新股, 
   *      C.13:中小板, 
   *      C.80:创业板 
   *      C.BK07071:沪股通 
   *      C.BK08041:深股通 
   *    C._B:B股
   *    C._AB_FXJS:风险警示版
   *    R.__40|__42:两网及退市
   *    
   *    C.81:新三板-全部
   *      C._81.32:新三板-创新层
   *      C._81.16:新三板-基础层
   *      C._81.PRTCL:新三板-协议
   *      C._81.MM:新三板-做市
   *      R._81.CA|_81.CM:新三板-竞价
   *
   *    C.__20
   * @params.p 页码
   * @params.ps 每页大小
   * @params.sr 1: -1: 
   * @params.sty  FPGBKI: FCOIATC:  E1II:  MPNSBAS:
   */ 
  async _getIndexOrStockApi(params) {
    let url = 'http://nufm.dfcfw.com/EM_Finance2014NumericApplication/JS.aspx';
    params = params ? params : {};
    params.type = 'CT';
    params.token = this._token;
    params.sty = 'FCOIATC';
    params.cb = '';
    params.js = '({data:[(x)],recordsFiltered:(tot)})';
    params.st = '(ChangePercent)';
    params.sr = -1;
    params.p = 1;
    params.ps = 20000; // 新三板超过一万条记录
    params._ = Date.now();
    if (Object.keys(params).length) {
      url += '?' + qs.stringify(params);
    }

    let body = await request.get(url);
    // logger.info('_getIndexOrStockApi', params, url);
    if (!body.length) {
      throw new Error('EastMoney:_getIndexOrStockApi Error, maybe token expired');
    }
    try {
      body = body.slice(1, -1);
      // 拿到的body是一个relaxed json，字段名没有双引号，无法JSON.parse()
      eval('body = '+body);
      body.total = body.recordsFiltered;
      delete body.recordsFiltered;
      // logger.info(body);
      body.rows = body.data;
      delete body.data;
    }catch(err) {
      logger.error(err);
      debugger;
      // logger.info(body);
    }
    return body;
  }

  // async _getStockApi(params) {
  //   let ret = await this._getIndexOrStockApi(params);
  //   let rows = [];
  //   for(let d of ret.rows) {
  //     try {
  //     d = d.split(',');
  //     rows.push({
  //       cmd: params.cmd,
  //       stockCode: d[1],
  //       stockName: d[2]
  //     });
  //     } catch(err) {
  //       logger.error(err);
  //       logger.error(d);
  //     }
  //   }
  //   ret.rows = rows;
  //   return ret;
  // }

  async _getIndexApi(params) {
    let ret = await this._getIndexOrStockApi(params);
    let rows = [];
    for(let d of ret.rows) {
      d = d.split(',');
      rows.push({
        indexCode: d[1],
        indexName: d[2]
      });
    }
    ret.rows = rows;
    return ret;
  }

  /**
   * 爬取: 沪深指数->深圳系列指数
   * 位与页面: http://quote.eastmoney.com/center/gridlist.html#index_sz
   */
  async getIndexSz() {
    let params = {
      cmd: 'C.5'
    };
    let body = await this._getIndexApi(params);
    return body;
  }

  /**
   * 爬取: 沪深指数->上证系列指数
   * 位与页面: http://quote.eastmoney.com/center/gridlist.html#index_sh
   */
  async getIndexSh() {
    let params = {
      cmd: 'C.1'
    };
    let body = await this._getIndexApi(params);
    return body;
  }

  // /**
  //  * 爬取: 沪深A股
  //  * 位与页面: http://quote.eastmoney.com/center/gridlist.html#hs_a_board
  //  */
  // async getStockA() {
  //   let params = {
  //     cmd: 'C._A'
  //   };
  //   let body = await this._getStockApi(params);
  //   return body;
  // }

  // // 获取个股页面所有类型标签的股票，可能导致重复
  // async getAllStock() {
  //   let rows = [];
  //   let map = {};
  //   let count = 0;
  //   // 沪深A股
  //   let body = await this._getStockApi({cmd: 'C._A'});
  //   logger.info('[getAllStock]', 'A股', body.rows.length);
  //   for(let r of body.rows) {
  //     if (!map[r.stockCode]) {
  //       r.stockType = 'A';
  //       map[r.stockCode] = r;
  //     }
  //   }

  //   // 上证A股
  //   body = await this._getStockApi({cmd: 'C.2'});
  //   logger.info('[getAllStock]', '上证A股', body.rows.length);
  //   for(let r of body.rows) {
  //     if (!map[r.stockCode]) {
  //       throw new Error('未添加的上证A股');
  //     }
  //     map[r.stockCode]['stockCity'] = 'sh';
  //   }

  //   // 深圳A股
  //   body = await this._getStockApi({cmd: 'C._SZAME'});
  //   logger.info('[getAllStock]', '深证A股', body.rows.length);
  //   for(let r of body.rows) {
  //     if (!map[r.stockCode]) {
  //       throw new Error('未添加的深证A股');
  //     }
  //     map[r.stockCode]['stockCity'] = 'sz';
  //   }

  //   // A股新股
  //   body = await this._getStockApi({cmd: 'C.BK05011'});
  //   logger.info('[getAllStock]', '新股', body.rows.length);
  //   for(let r of body.rows) {
  //     if (!map[r.stockCode]) {
  //       throw new Error('未添加的新股');
  //     }
  //     map[r.stockCode]['isNew'] = true;
  //   }

  //   // 中小板
  //   body = await this._getStockApi({cmd: 'C.13'});
  //   logger.info('[getAllStock]', '中小板', body.rows.length);
  //   for(let r of body.rows) {
  //     if (!map[r.stockCode]) {
  //       throw new Error('未添加的中小板');
  //     }
  //     map[r.stockCode]['isSme'] = true;
  //   }

  //   // 创业板
  //   body = await this._getStockApi({cmd: 'C.80'});
  //   logger.info('[getAllStock]', '创业板', body.rows.length);
  //   for(let r of body.rows) {
  //     if (!map[r.stockCode]) {
  //       throw new Error('未添加的创业板');
  //     }
  //     if (map[r.stockCode]['isSme']) {
  //       throw new Error('板块冲突');
  //     }
  //     map[r.stockCode]['isGem'] = true;
  //   }

  //   // 沪股通
  //   body = await this._getStockApi({cmd: 'C.BK07071'});
  //   logger.info('[getAllStock]', '沪股通', body.rows.length);
  //   for(let r of body.rows) {
  //     if (!map[r.stockCode]) {
  //       throw new Error('未添加的沪股通');
  //     }
  //   }

  //   // 深股通
  //   body = await this._getStockApi({cmd: 'C.BK08041'});
  //   logger.info('[getAllStock]', '深股通', body.rows.length);
  //   for(let r of body.rows) {
  //     if (!map[r.stockCode]) {
  //       throw new Error('未添加的深股通');
  //     }
  //   }

  //   // B股
  //   body = await this._getStockApi({cmd: 'C._B'});
  //   logger.info('[getAllStock]', 'B股', body.rows.length);
  //   count = 0;
  //   for(let r of body.rows) {
  //     if (!map[r.stockCode]) {
  //       r.stockType = 'B';
  //       map[r.stockCode] = r;
  //       ++ count;
  //     }
  //   }
  //   logger.info('[getAllStock]', '新增', count);

  //   // 风险警示版
  //   body = await this._getStockApi({cmd: 'C._AB_FXJS'});
  //   logger.info('[getAllStock]', '风险警示版', body.rows.length);
  //   for(let r of body.rows) {
  //     if (!map[r.stockCode]) {
  //       throw new Error('未添加的风险警示板');
  //     }
  //     map[r.stockCode]['isDanger'] = true;
  //   }

  //   // 两网及退市
  //   body = await this._getStockApi({cmd: 'R.__40|__42'});
  //   logger.info('[getAllStock]', '两网及退市', body.rows.length);
  //   count = 0;
  //   for(let r of body.rows) {
  //     if (!map[r.stockCode]) {
  //       r.stockType = 'T';
  //       map[r.stockCode] = r;
  //       ++ count;
  //     }
  //   }
  //   logger.info('[getAllStock]', '新增', count);

  //   // 新三板
  //   body = await this._getStockApi({cmd: 'C.81'});
  //   logger.info('[getAllStock]', '新三板', body.rows.length);
  //   count = 0;
  //   for(let r of body.rows) {
  //     if (!map[r.stockCode]) {
  //       r.stockType = 'NEEQ';
  //       map[r.stockCode] = r;
  //       ++ count;
  //     }
  //   }
  //   logger.info('[getAllStock]', '新增', count);

  //   // 新三板创新层
  //   body = await this._getStockApi({cmd: 'C._81.32'});
  //   logger.info('[getAllStock]', '新三板创新层', body.rows.length);
  //   for(let r of body.rows) {
  //     if (!map[r.stockCode]) {
  //       throw new Error('未添加的新三板创新层');
  //     }
  //     map[r.stockCode]['isInnovate'] = true;
  //   }

  //   // 新三板基础层
  //   body = await this._getStockApi({cmd: 'C._81.16'});
  //   logger.info('[getAllStock]', '新三板基础层', body.rows.length);
  //   for(let r of body.rows) {
  //     if (!map[r.stockCode]) {
  //       throw new Error('未添加的新三板基础层');
  //     }
  //     map[r.stockCode]['isBasic'] = true;
  //   }

  //   // 新三板协议
  //   body = await this._getStockApi({cmd: 'C._81.PRTCL'});
  //   logger.info('[getAllStock]', '新三板协议', body.rows.length);
  //   for(let r of body.rows) {
  //     if (!map[r.stockCode]) {
  //       throw new Error('未添加的新三板协议');
  //     }
  //     map[r.stockCode]['isAgreement'] = true;
  //   }

  //   // 新三板做市
  //   body = await this._getStockApi({cmd: 'C._81.MM'});
  //   logger.info('[getAllStock]', '新三板做市', body.rows.length);
  //   for(let r of body.rows) {
  //     if (!map[r.stockCode]) {
  //       throw new Error('未添加的新三板做市');
  //     }
  //     map[r.stockCode]['isMarketMaking'] = true;
  //   }

  //   // 新三板竞价
  //   body = await this._getStockApi({cmd: 'R._81.CA|_81.CM'});
  //   logger.info('[getAllStock]', '新三板竞价', body.rows.length);
  //   for(let r of body.rows) {
  //     if (!map[r.stockCode]) {
  //       throw new Error('未添加的新三板竞价');
  //     }
  //     map[r.stockCode]['isBidding'] = true;
  //   }

  //   rows = Object.values(map);
  //   // let allParams = [
  //   //   'C._A',
  //   //   'C.2',
  //   //   'C._SZAME',
  //   //   'C.BK05011',
  //   //   'C.13',
  //   //   'C.80',
  //   //   'C.BK07071',
  //   //   'C.BK08041',
  //   //   'C._B',
  //   //   'C._AB_FXJS',
  //   //   'R.__40%7C__42'
  //   // ];
  //   // let rows = [];
  //   // let map = {};
  //   // for(let c of allParams) {
  //   //   let body = await this._getStockApi({cmd: c});
  //   //   rows = rows.concat(body.rows);
  //   // }
  //   return {total: rows.length, rows: rows};
  // }
};

module.exports = new EastMoney();
