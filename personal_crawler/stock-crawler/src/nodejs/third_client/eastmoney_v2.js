'use strict';

const apis = require('./eastmoney_v2/apis');

class EastMoney {
  constructor() {}

  async init(logger) {
    // 为了避免多次请求kline获取lastTradeDate，直接client初始化时获取一次
    this._lastTradeDate = await apis.getLastTradeDate(logger);
  }

  async getKline(logger, args) {
    return apis.getKline(logger, args);
  }

  async getStockKLine(logger, stockCode, args) {
    return apis.getStockKLine(logger, stockCode, args); 
  }

  async getIndexKLine(logger, indexCode, indexType) {
    return apis.getIndexKLine(logger, indexCode, indexType);
  }

  async getEtfKLine(logger, fundCode) {
    return apis.getEtfKLine(logger, fundCode);
  }

  async getAllEtf(logger) {
    return apis.getAllEtf(logger);
  }

  async getAllStockByFlag(logger, flag) {
    logger.debug("getAllStocByFlag: ", flag)
    return apis.getAllStockByFlag(logger, flag);
  }

  async getStockFenshiDetails(logger, stockCode) {
    return apis.getStockFenshiDetails(logger, this._lastTradeDate, stockCode);
  }

  async getIndexFenshiDetails(logger, indexCode, indexType) {
    return apis.getIndexFenshiDetails(logger, this._lastTradeDate, indexCode, indexType);
  }

  async GetLastTradeDate(logger) {
    // return apis.getLastTradeDate(logger);
    return this._lastTradeDate;
  }

  async GetIPO(logger, stockCode) {
    return apis.getIPO(logger, stockCode);
  }

  async GetSPO(logger) {
    return apis.getSPO(logger);
  }

  async GetCompanyNotices(logger, stockCode, page) {
    return apis.getCompanyNotices(logger, stockCode, page);
  }

  async GetNoticePage(logger, artCode) {
    return apis.getNoticePage(logger, artCode);
  }
  
  async GetCompanyEvents(logger, stockCode, tag, page) {
    return apis.getCompanyEvents(logger, stockCode, tag, page);
  }

  async GetStockBusiness(logger, stockCode) {
    return apis.getStockBusiness(logger, stockCode);
  }
  
  async GetStockBusinessComponents(logger, stockCode) {
    return apis.getStockBusinessComponents(logger, stockCode);
  }

  async GetStockBonus(logger, stockCode) {
    return apis.getStockBonus(logger, stockCode); 
  }

  async GetStockShareList(logger, stockCode) {
    return apis.getStockShareList(logger, stockCode);
  }

  async GetStockShareInfo(logger, stockCode) {
    let ret = await this.GetStockShareList(logger, stockCode);
    return ret.rows[0];
  }

  async GetSingleStockPledges(logger, stockCode) {
    return apis.getSingleStockPledges(logger, stockCode);
  }

  async GetUnlimitStocks(logger, stockCode) {
    return apis.getUnlimitStocks(logger, stockCode);
  }

  async GetMarketValuation(logger, tradeMarketCode, existDate) {
    return apis.getMarketValuation(logger, tradeMarketCode, existDate);
  }

  async GetStockValuation(logger, stockCode, existDate) {
    return apis.getStockValuation(logger, stockCode, existDate);
  }

  async GetIndustryValuation(logger, industryCode, existDate) {
    return apis.getIndustryValuation(logger, industryCode, existDate);
  }
}

async function test() {
  let east = new EastMoney();
  console.debug2 = console.info;
  console.debug = console.info;
  // let res = await east.GetSPO(console);
  // let res = await east.GetIPO(console, "300530");
  // let res = await east.GetIPO(console, "600416");
  // let res = await east.GetIPO(console, "001358");
  // let res = await east.GetIPO(console, "300750");
  // let res = await east.GetSingleStockPledges(console, "300750");
  // let res = await east.GetUnlimitStocks(console, "300750");
  east.GetIPO(console, "688287");
  // east.GetLastTradeDate(console);
  // east.GetStockBusiness(console, "300750")
  // let res = await east.getStockKLine(console, "600602")
  // let res = await east.getStockKLine(console, "600656")
  // east.GetMarketValuation(console, '300750', '2023-12-20')
  // let res = await east.GetMarketValuation(console, '000300', '2023-12-20')
  // let res = await east.GetStockValuation(console, '300750', '2023-12-20')
  // let res = await east.GetStockBonus(console, '300750');
  // let res = await east.getStockKLine(console, "900957");
  // let res = await east.GetStockShareInfo(console, "900957");
  // let res = await east.GetStockShareList(console, "600656");
  // let res = await east.GetStockShareList(console, "600602");
  // let res = await east.GetStockShareList(console, "600309");
  //
  // let res = await east.getIndexFenshiTrends(console, '000016');
  // let res = await east.getStockFenshiDetails(console, '600028');
  // console.log(res);
  // east.GetLastTradeDate();
  // east.GetStockShareList(console, '600690');
}
// test();

module.exports = new EastMoney();
