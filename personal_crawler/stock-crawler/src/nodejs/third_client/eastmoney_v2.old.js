'use strict';

const crypto = require('crypto');
const fs = require('fs');
const qs = require('querystring');
const request = require('request-promise');
const zlib = require('zlib');
const iconv = require('iconv-lite');
const utils = require('../nodejs-common/utils.js');

class EastMoney {
  constructor() {}

  /**
   * 通用网络请求方法，支持重试机制
   * @param {Object} logger - 日志对象
   * @param {Object} options - 请求选项
   * @param {number} retries - 最大重试次数
   * @param {number} delay - 每次重试的延迟时间（毫秒）
   * @returns {Promise<any>} - 请求结果
   */
  async _makeRequest(logger, options, retries = 3, delay = 1000) {
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        return await request(options);
      } catch (error) {
        logger.warn(`[Attempt ${attempt}] Request failed: ${error.message}`);
        if (attempt === retries) {
          logger.error(`[Failed] Max retries reached for URL: ${options.uri}`);
          throw error;
        }
        await new Promise((resolve) => setTimeout(resolve, delay));
      }
    }
  }

  _parseKLine(d) {
    let data = d.split(',');
    return {
      date: data[0],
      openingPrice: data[1], // 开盘价
      closingPrice: data[2], // 收盘价
      highestPrice: data[3], // 最高价
      lowestPrice: data[4], // 最低价
      tradingVolume: data[5], // 成交量
      turnover: data[6], // 成交额
      amplitude: data[7], // 振幅
      changePercent: data[8], // 涨跌幅
      change: data[9], // 涨跌额
      turnoverRate: data[10], // 换手率
      tradingVolumeAfterTest: data[11], // 盘后成交额（猜测）
      tradingVolumeAfterTest: data[12], // 盘后成交量（猜测）
    };
  }

  _parseStockTrends(indexCode, d) {
    let data = d.split(',');
    return {
      indexCode: indexCode,
      time: data[0],
      avgPrice: data[1], // 分钟内平均价？瞬时价格？怎么聚合？
      price: data[2], // 分钟现价(指数值)，聚合方式为取details最新记录的price值
      highestPrice: data[3], // 分钟内最高成交价
      lowestPrice: data[4], // 分钟内最低成交价
      volume: data[5], // 现手
      totalPrice: data[6], // 分钟内成交额

      totalVolume: data[11], // 当日累计成交手数
    };
  }

  _parseIndexDetail(indexCode, d) {
    let data = d.split(',');
    return {
      indexCode: indexCode,
      time: data[0],
      price: data[1],
      volume: data[2],
    };
  }

  _parseStockDetail(stockCode, d) {
    let data = d.split(',');
    return {
      stockCode: stockCode,
      time: data[0],
      price: data[1],
      volume: data[2],
    };
  }

  // 市场估值数据
  _parseMarketValuation(d) {
    return {
      changeRate: d.CHANGE_RATE, // 涨跌幅
      closePrice: d.CLOSE_PRICE, // 沪深300
      freeMarketCap: d.FREE_MARKET_CAP, // 流通市值（元）
      freeShares: d.FREE_SHARES, // 流通股本（股）
      listingOrgNum: d.LISTING_ORG_NUM, // 个股总数
      peTtmAvg: d.PE_TTM_AVG, // 平均市盈率
      // : d.SECURITY_INNER_CODE, // "1000157500"
      totalMarketCap: d.TOTAL_MARKET_CAP, // 总市值（元）
      totalShares: d.TOTAL_SHARES, // 总股本（股）
      tradeDate: d.TRADE_DATE.slice(0, 10), // 交易日期
      tradeMarketCode: d.TRADE_MARKET_CODE, // 交易市场代码:
        // 1. 000300/沪深两市(沪深300？)；
        // 2. 000001/沪市主板（上证指数）
        // 3. 000688/科创板（科创50）
        // 4. 399001/深市主板（深证指数）
        // 5. 399006/创业板（创业板指）
    };
  }

  // TODO 行业估值数据
  _parseIndustryValuation(d) {
    return {
      tradeDate: d.TRADE_DATE.slice(0, 10), // 交易日期
      boardCode: d.BOARD_CODE, // 行业/板块代码
      boardName: d.BOARD_NAME, // 行业/板块名称
      freeSharesVag: d.FREE_SHARES_VAG, // 平均流通股本（股）
      lossCount: d.LOSS_COUNT, // 亏损家数
      avgMarketCap: d.MARKET_CAP_VAG, // 平均市值
      avgNoMarketCap: d.NOMARKETCAP_A_VAG, // 平均流通市值（元）
      // nolimitedMarketCap: d.NOTLIMITED_MARKETCAP_A, // 
    };
  }

  // 个股估值数据
  _parseStockValuation(d) {
    return {
      stockCode: d.SECURITY_CODE,
      boardCode: d.BOARD_CODE,
      boardName: d.BOARD_NAME,
      totalMarketCap: d.TOTAL_MARKET_CAP,
      notlimitedMarketCapA: d.NOTLIMITED_MARKETCAP_A,
      closePrice: d.CLOSE_PRICE,
      changeRate: d.CHANGE_RATE,
      totalShares: d.TOTAL_SHARES,
      freeSharesA: d.FREE_SHARES_A,
      peTtm: d.PE_TTM,
      peLar: d.PE_LAR,
      pbMrq: d.PB_MRQ,
      pegCar: d.PEG_CAR,
      pcfOcfLar: d.PCF_OCF_LAR,
      pcfOcfTtm: d.PCF_OCF_TTM,
      psTtm: d.PS_TTM,
      tradeDate: d.TRADE_DATE.slice(0, 10), // 交易日期
    };
  }

  _parseIPO(d, pageType) {
    let issueDate = d.ISSUE_DATE;
    if (d.ISSUE_DATE) {
      issueDate = d.ISSUE_DATE.slice(0, 10);
    }
    let listingDate = d.LISTING_DATE;
    if (d.LISTING_DATE) {
      listingDate = d.LISTING_DATE.slice(0, 10);
    }

    return {
      fromPageType: pageType,
      stockCode: d.SECURITY_CODE,
      recordType: '0',
      issueNum: d.TOTAL_ISSUE_NUM,  // 总发行梳理（股）
      onlineIssueNum: d.ONLINE_ISSUE_NUM, // 网上发行数量（股）
      offlinePlacingNum: d.OFFLINE_PLACING_NUM, // 网下配售数量（股）
      issuePrice: d.ISSUE_PRICE,
      issueDate: issueDate,
      issueListingDate: listingDate,
      lockinPeriod: '',
      issueWay: d.ISSUE_WAY,
      // recordType: '0',
      // issueNum: d
    };
  }

  _parseSPO(d) {
    let issueDate = d.ISSUE_DATE;
    if (d.ISSUE_DATE) {
      issueDate = d.ISSUE_DATE.slice(0, 10);
    }
    let listingDate = d.ISSUE_LISTING_DATE;
    if (d.LISTING_DATE) {
      listingDate = d.ISSUE_LISTING_DATE.slice(0, 10);
    }
    return {
      stockCode: d.SECURITY_CODE,
      recordType: d.SEO_TYPE, // 1:定向增发；2:公开增发
      issueNum: d.ISSUE_NUM, // 发行总数（股）
      issuePrice: d.ISSUE_PRICE, // 发行价格（元）
      newPrice: d.NEW_PRICE, // 最新价
      issueDate: issueDate, // 发行日期。不知道为什么发现一些是没有上市日期的
      issueListingDate: listingDate, // 增发上市日期。这里同样也有一堆空值。而且issueDate和listingDate都不是同时为空。
      lockinPeriod: d.LOCKIN_PERIOD, // 锁定期
      corRecode: d.CORRECODE, // 增发代码
      otherOnlineVss: d.OTHER_ONLINE_VSS, // 网上发行（股）

      // https://data.eastmoney.com/other/gkzf.html
      // 表格里面没有展示的信息字段
      issueWay: d.ISSUE_WAY, // 发行方式: 网上优先配置；

      // // 其他返回字段，暂时没有对应
      //          : d.BALLOT_NUM
      //          : d.BVPS_AFTER
      //          : d.BVPS_BEFORE
      //          : d.CHANGE_RATE
      //          : d.CLOSE_PRICE
      //          : d.COLEAD_UNDER_WRITER
      //          : d.CORRECODE_NAME_ABBR
      //          : d.EQUITY_RECORD_DATE
      //          : d.FINANCE_CODE
      //          : d.FROZEN_CAPITAL
      //          : d.FUND_FOR
      //          : d.ISSUE_OBJECT
      //          : d.ISSUE_OFF_DATE
      //          : d.ISSUE_ON_DATE
      //          : d.ISSUE_RATE
      //          : d.ISSUE_SHARE_AFTER
      //          : d.ISSUE_SHARE_BEFORE
      //          : d.LEAD_UNDER_WRITER
      //          : d.LOT_DATE
      //          : d.MAIN_BUSINESS
      //          : d.NET_RAISE_FUNDS
      //          : d.OFFLINE_PLACING_NUM
      //          : d.ONLINE_ISSUE_LWR
      //          : d.ONLINE_ISSUE_NUM
      //          : d.OPEN_PRICE
      //          : d.PER_CAPITAL
      //          : d.PLACING_RATIO
      //          : d.PRICE_PRINCIPLE
      //          : d.TOTAL_RAISE_FUNDS
      //          : d.TRADE_MARKET
      //          : d.TRADE_MARKET_CODE
      //          : d.UNDER_WRITING
      //          : d.UNDER_WRITING_PERIOD
    };
  }


  /**
   * 前端js抠出来的代码
   *            switch (e) {
            case 1:
                t.txt = "开盘竞价",
                t.isopen = !0;
                break;
            case 2:
                t.txt = "交易中",
                t.isopen = !0;
                break;
            case 3:
                t.txt = "盘中休市";
                break;
            case 4:
                t.txt = "收盘竞价";
                break;
            case 5:
                t.txt = "已收盘";
                break;
            case 6:
                t.txt = "停牌";
                break;
            case 7:
                t.txt = "退市";
                break;
            case 8:
                t.txt = "暂停上市";
                break;
            case 9:
                t.txt = "未上市";
                break;
            case 10:
                t.txt = "未开盘";
                break;
            case 11:
                t.txt = "盘前",
                t.isopen = !0;
                break;
            case 12:
                t.txt = "盘后",
                t.isopen = !0;
                break;
            case 13:
                t.txt = "休市";
                break;
            case 14:
                t.txt = "盘中停牌";
                break;
            case 15:
                t.txt = "非交易代码";
                break;
            case 16:
                t.txt = "波动性中断";
                break;
            case 17:
                t.txt = "盘后交易启动",
                t.isopen = !0;
                break;
            case 18:
                t.txt = "盘后集中撮合交易",
                t.isopen = !0;
                break;
            case 19:
                t.txt = "盘后固定价格交易",
                t.isopen = !0
            }
   */

  async _getClistPage(logger, args, pn) {
    let url = 'http://89.push2.eastmoney.com/api/qt/clist/get';
    let params = {
      // cb: '',
      pn: pn, // page number
      pz: 100, // page size，最大只支持100了
      po: 1,
      np: 1,
      ut: 'bd1d9ddb04089700cf9c27f6f7426281',
      fltt: 2,
      invt: 2,
      fid: 'f3', // 默认值，可能args有其他值
      fields: 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152', // fileds表示前端页面展示的字段位置，表示返回哪些字段。其中f1未知(但是发现股票代码90开头的是3，00/20/68/60/30开头的是2)，f2最新价，f3涨跌幅，f4涨跌额，f5成交量，f6成交额，f7振幅，f8换手率，f9市盈率(动态)，f10量比，f11未知，f12代码(股票/指数)，f13未知，f14名称(股票/指数)，f15最高价，f16最低价，f17今开，f18昨收，f20-f22未知，f23市净率。未知的field可以都保存原始字段，到时入库后再提取分析，找规律
      _: new Date().getTime()
    };
    for (let k in args) {
      params[k] = args[k];
    }

    let options = {
      uri: url + '?' + qs.stringify(params),
      json: true
    };

    let body = await this._makeRequest(logger, options);
    logger.debug(`[GET] ${options.uri}`);

    let rows = [];
    if (!body.data) {
      return rows;
    }

    for (let d of body.data.diff) {
      if (d.f2 == '-') {
        // TODO 没有最新价，确认是否为退市状态。暂时先当做退市吧。还有st状态。
        d.state = 'delist';
      }
      rows.push({
        code: d.f12,
        name: d.f14,
        state: d.state,
      });
    }
    return rows;
  }

  async _getClist(logger, args) {
    let ret = [];
    // 最大100页循环
    for (let i = 1; i < 100; i++) {
      let list = await this._getClistPage(logger, args, i);
      if (list.length == 0) {
        break;
      }
      ret = ret.concat(list)
    }
    return ret;
  }

  // 页面: http://quote.eastmoney.com/center/gridlist.html#fund_etf
  async getAllEtf() {
    // let url = 'http://89.push2.eastmoney.com/api/qt/clist/get?cb=jQuery112402571412670923954_1668930530442&pn=1&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=4027386437815592|0|1|0|web&fid=f3&fs=b:MK0021,b:MK0022,b:MK0023,b:MK0024&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152&_=1668930530443'
    let body = await this._getClist(logger, {
      wbp2u: '4027386437815592|0|1|0:web', // 没发现差别
      fs: 'b:MK0021,b:MK0022,b:MK0023,b:MK0024', // 获取etf还是股票等的关键区别参数
    });

    let rows = [];
    for (let d of body) {
      rows.push({
        fundCode: d.code,
        fundName: d.name
      });
    }
    
    return {total: rows.length, rows: rows};
  }

  _convertBody2StockResp(body) {
    let rows = [];
    for (let d of body) {
      rows.push({
        stockCode: d.code,
        stockName: d.name,
        stockState: d.state,
      })
    }
    return {total: rows.length, rows: rows};
  }

  _convertBody2IndexResp(body) {
    let rows = [];
    for (let d of body) {
      rows.push({
        indexCode: d.code,
        indexName: d.name,
      })
    }
    return {total: rows.length, rows: rows};
  }

  async getAllStockByFlag(flag) {
    if (flag == 'A') {
      // 页面(沪深京A股): http://quote.eastmoney.com/center/gridlist.html#hs_a_board
      // let url = 'http://17.push2.eastmoney.com/api/qt/clist/get?cb=jQuery1124023164828994737796_1638631233243&pn=1&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&fid=f3&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23,m:0+t:81+s:2048&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152&_=1638631233318';
      let body = await this._getClist(logger, {
        fs: 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23,m:0+t:81+s:2048',
      });
      // 为了获得stockCity的信息。这里扩散成三个城市tab去获取
      let all = this._convertBody2StockResp(body);
      let shStocks = await this.getAllStockByFlag('shA')
      let szStocks = await this.getAllStockByFlag('szA')
      let bjStocks = await this.getAllStockByFlag('bjA')
      utils.AssertEqual(all.total,  shStocks.total + szStocks.total + bjStocks.total);
      let cityMap = {};
      shStocks.rows.forEach((v) => cityMap[v.stockCode] = 'SH');
      szStocks.rows.forEach((v) => cityMap[v.stockCode] = 'SZ');
      bjStocks.rows.forEach((v) => cityMap[v.stockCode] = 'BJ');
      for (let v of all.rows) {
        v.stockCity = cityMap[v.stockCode];
      }
      return all;

    } else if (flag == 'shA') {
      // 页面(上证A股tab页面): http://quote.eastmoney.com/center/gridlist.html#sh_a_board
      // let url = 'http://81.push2.eastmoney.com/api/qt/clist/get?cb=jQuery112407321699085160172_1669048297399&pn=1&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=4027386437815592|0|1|0|web&fid=f3&fs=m:1+t:2,m:1+t:23&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152&_=1669048297712';
      let body = await this._getClist(logger, {
        wbp2u: '4027386437815592|0|1|0:web', // 没发现差别
        fs: 'm:1+t:2,m:1+t:23',
      });
      return this._convertBody2StockResp(body);
    } else if (flag == 'szA') {
      // 页面(深证A股tab页面): http://quote.eastmoney.com/center/gridlist.html#sz_a_board
      // let url = 'http://81.push2.eastmoney.com/api/qt/clist/get?cb=jQuery112407321699085160172_1669048297397&pn=1&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=4027386437815592|0|1|0|web&fid=f3&fs=m:0+t:6,m:0+t:80&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152&_=1669048298060';
      let body = await this._getClist(logger, {
        wbp2u: '4027386437815592|0|1|0:web', // 没发现差别
        fs: 'm:0+t:6,m:0+t:80',
      });
      return this._convertBody2StockResp(body);
    } else if (flag == 'bjA') {
      // 页面(北证A股tab页面): http://quote.eastmoney.com/center/gridlist.html#bj_a_board
      // let url = 'http://81.push2.eastmoney.com/api/qt/clist/get?cb=jQuery112407321699085160172_1669048297397&pn=1&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=4027386437815592|0|1|0|web&fid=f3&fs=m:0+t:81+s:2048&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152&_=1669048298078';
      let body = await this._getClist(logger, {
        wbp2u: '4027386437815592|0|1|0:web', // 没发现差别
        fs: 'm:0+t:81+s:2048',
      });
      return this._convertBody2StockResp(body);
    } else if (flag == 'B') {
      // 页面(B股): http://quote.eastmoney.com/center/gridlist.html#b_board
      // let url = 'http://81.push2.eastmoney.com/api/qt/clist/get?cb=jQuery112407321699085160172_1669048297397&pn=1&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=4027386437815592|0|1|0|web&fid=f3&fs=m:0+t:7,m:1+t:3&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152&_=1669048298155'
      let body = await this._getClist(logger, {
        wbp2u: '4027386437815592|0|1|0:web', // 没发现差别
        fs: 'm:0+t:7,m:1+t:3',
      });
      return this._convertBody2StockResp(body);
    } else if (flag == 'gem') {
      // 页面(创业板): http://quote.eastmoney.com/center/gridlist.html#gem_board
      // let url = 'http://28.push2.eastmoney.com/api/qt/clist/get?cb=jQuery112409275104334730571_1669137224977&pn=1&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=4027386437815592|0|1|0|web&fid=f3&fs=m:0+t:80&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152&_=1669137225026';
      let body = await this._getClist(logger, {
        wbp2u: '4027386437815592|0|1|0:web', // 没发现差别
        fs: 'm:0+t:80',
      });
      return this._convertBody2StockResp(body);
    } else if (flag == 'kcb') {
      // 科创板: http://quote.eastmoney.com/center/gridlist.html#kcb_board
      let body = await this._getClist(logger, {
        wbp2u: '4027386437815592|0|1|0:web', // 没发现差别
        fs: 'm:1+t:23',
      });
      return this._convertBody2StockResp(body);
    } else if (flag == 'sh_hk') {
      // 沪股通: http://quote.eastmoney.com/center/gridlist.html#sh_hk_board
      let body = await this._getClist(logger, {
        wbp2u: '4027386437815592|0|1|0:web', // 没发现差别
        fs: 'b:BK0707',
        fid: 'f26',
      });
      return this._convertBody2StockResp(body);
    } else if (flag == 'sz_hk') {
      // 深股通: http://quote.eastmoney.com/center/gridlist.html#sz_hk_board
      let body = await this._getClist(logger, {
        wbp2u: '4027386437815592|0|1|0:web', // 没发现差别
        fs: 'b:BK0804',
        fid: 'f26',
      });
      return this._convertBody2StockResp(body);
    } else if (flag == 'st') {
      // 风险警示板(ST): http://quote.eastmoney.com/center/gridlist.html#st_board
      let body = await this._getClist(logger, {
        wbp2u: '4027386437815592|0|1|0:web', // 没发现差别
        fs: 'm:0+f:4,m:1+f:4',
      });
      return this._convertBody2StockResp(body);
    } else if (flag == 'staq_net') {
      // 两网及退市: http://quote.eastmoney.com/center/gridlist.html#staq_net_board
      let body = await this._getClist(logger, {
        wbp2u: '4027386437815592|0|1|0:web', // 没发现差别
        fs: 'm:0+s:3',
      });
      return this._convertBody2StockResp(body);
    } else if (flag == 'sh_ab') {
      // 上证AB股比价: http://quote.eastmoney.com/center/gridlist.html#ab_comparison_sh
      // let url = 'http://28.push2.eastmoney.com/api/qt/clist/get?cb=jQuery112409275104334730571_1669137224971&pn=1&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=4027386437815592|0|1|0|web&fid=f199&fs=m:1+b:BK0498&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152,f201,f202,f203,f196,f197,f199,f195,f200&_=1669137225105'
      let body = await this._getClist(logger, {
        wbp2u: '4027386437815592|0|1|0:web', // 没发现差别
        fs: 'm:1+b:BK0498',
        fid: 'f199',
      });
      return this._convertBody2StockResp(body);
    } else if (flag == 'sz_ab') {
      // 深证AB股比价: http://quote.eastmoney.com/center/gridlist.html#ab_comparison_sz
      // let url = 'http://28.push2.eastmoney.com/api/qt/clist/get?cb=jQuery112409275104334730571_1669137224971&pn=1&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=4027386437815592|0|1|0|web&fid=f199&fs=m:0+b:BK0498&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152,f201,f202,f203,f196,f197,f199,f195,f200&_=1669137225113'
      let body = await this._getClist(logger, {
        wbp2u: '4027386437815592|0|1|0:web', // 没发现差别
        fs: 'm:0+s:3',
        fid: 'f199',
      });
      return this._convertBody2StockResp(body);
    } else if (flag == 'index_sh') {
      // 页面（上证系列指数）:https://quote.eastmoney.com/center/gridlist.html#index_sh
      // let url = 'https://65.push2.eastmoney.com/api/qt/clist/get?cb=jQuery1124048235124377782523_1703250616096&pn=2&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=|0|0|0|web&fid=f3&fs=m:1+s:2&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f26,f22,f33,f11,f62,f128,f136,f115,f152&_=1703250616133'
      let body = await this._getClist(logger, {
        wbp2u: '|0|0|0|web',
        fs: 'm:1+s:2',
      });
      return this._convertBody2IndexResp(body);
    } else if (flag == 'index_sz') {
      // 页面（深证系列指数）:https://quote.eastmoney.com/center/gridlist.html#index_sz
      // let url = 'https://46.push2.eastmoney.com/api/qt/clist/get?cb=jQuery1124002654497177392723_1703250624870&pn=2&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=|0|0|0|web&fid=f3&fs=m:0+t:5&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f26,f22,f33,f11,f62,f128,f136,f115,f152&_=1703250625085'
      let body = await this._getClist(logger, {
        wbp2u: '|0|0|0|web',
        fs: 'm:0+t:5',
      });
      return this._convertBody2IndexResp(body);
    } else if (flag == 'index_zz') {
      // 页面（中证系列指数）:https://quote.eastmoney.com/center/gridlist.html#index_zzzs
      // let url = 'https://91.push2.eastmoney.com/api/qt/clist/get?cb=jQuery1124071692446961072_1703253808945&pn=2&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=|0|0|0|web&fid=f3&fs=m:2&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f26,f22,f33,f11,f62,f128,f136,f115,f152&_=1703253808951'
      let body = await this._getClist(logger, {
        wbp2u: '|0|0|0|web',
        fs: 'm:2',
      });
      return this._convertBody2IndexResp(body);
    }
  }

  async _getKline(logger, args) {
    let url = 'http://67.push2his.eastmoney.com/api/qt/stock/kline/get';

    let params = {
      // cb: 'jQuery11240954883264978859_1643562836212',
      // ut: 
      fields1: 'f1,f2,f3,f4,f5,f6',
      // fields1: 'f1',
      fields2: 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63,f64,f65,f66,f67,f68',
      // fields2: 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
      // fields2: 'f1',
      klt: 101,
      fqt: 0, // 如果是1，表示前复权
      end: 20500101,
      lmt: 1000000,
      // lmt: 50,
      _: new Date().getTime()
    };
    // NOTE!!! 这段逻辑加了之后，会影响获取到的fields的实际含义，导致_parseKLine的字段映射错误
    // for (let i = 2; i < 300; i++) {
    //   params.fields1 += `,f${i}`;
    //   params.fields2 += `,f${i}`;
    // }
    for (let k in args) {
      params[k] = args[k];
    }

    let options = {
      uri: url + '?' + qs.stringify(params),
      json: true
    };

    let body = await this._makeRequest(logger, options);
    logger.debug2('url: ', options.uri);

    let rows = [];
    if (!body.data) {
      logger.error('_getKline: ', body);
      // throw new Error(body);
    } else {
      let i = 0;
      for (let d of body.data.klines) {
        let parsed = this._parseKLine(d);
        logger.debug2(params.fields2);
        // logger.debug2(`[eastmoney][${i++}]`, d.split(',').length, d.split(','));
        logger.debug2(`[eastmoney][${i++}] ${d}`, parsed);
        rows.push(parsed)
      }
    }
    return rows;
  }

  async getStockKLine(logger, stockCode) {
    // 页面（宁德时代）http://quote.eastmoney.com/sz300750.html
    // let url = 'http://67.push2his.eastmoney.com/api/qt/stock/kline/get?cb=jQuery11240954883264978859_1643562836212&secid=0.300750&ut=fa5fd1943c7b386f172d6893dbfba10b&fields1=f1%2Cf2%2Cf3%2Cf4%2Cf5%2Cf6&fields2=f51%2Cf52%2Cf53%2Cf54%2Cf55%2Cf56%2Cf57%2Cf58%2Cf59%2Cf60%2Cf61&klt=101&fqt=0&end=20500101&lmt=1000000&_=1643562836325'
    let secid = '';
    let head = stockCode.slice(0, 2)
    // 这个secid的参数值，很像分时接口的market参数。0表示00、30、20等开头，1表示68、60开头
    if (head == '00' || head == '30') {
      secid = '0.' + stockCode; 
    } else if (head == '68' || head == '60' || head == '90') {
      secid = '1.' + stockCode;
    } else {
      // 兜底值先用0.
      secid = '0.' + stockCode; 
    }

    let rows = await this._getKline(logger, {
      'secid': secid
    });
    let i = 0;
    for (let r of rows) {
      r.stockCode = stockCode;
      // logger.debug2(i++, r);
    }
    return {
      total: rows.length,
      rows: rows
    }
  }

  async getIndexKLine(logger, indexCode, indexType) {
    // 页面（深证成指）https://quote.eastmoney.com/zs399001.html
    // let url = 'https://push2his.eastmoney.com/api/qt/stock/kline/get?cb=jQuery351004863304295803239_1703255417934&secid=0.399001&ut=fa5fd1943c7b386f172d6893dbfba10b&fields1=f1%2Cf2%2Cf3%2Cf4%2Cf5%2Cf6&fields2=f51%2Cf52%2Cf53%2Cf54%2Cf55%2Cf56%2Cf57%2Cf58%2Cf59%2Cf60%2Cf61&klt=101&fqt=1&beg=0&end=20500101&smplmt=460&lmt=1000000&_=1703255417935'
    let secid = '';
    if (indexType == 'zz') {
      // 中证系列指数
      secid = '2.' + indexCode;
    } else if (indexType == 'sz') {
      // 深证系列指数
      secid = '0.' + indexCode;
    } else if (indexType == 'sh') {
      // 上证系列指数
      secid = '1.' + indexCode;
    } else {
      // 兜底
      secid = '1.' + indexCode;
    }

    let rows = await this._getKline(logger, {
      'secid': secid
    });
    let i = 0;
    for (let r of rows) {
      r.indexCode = indexCode;
      // logger.debug2(i++, r);
    }
    return {
      total: rows.length,
      rows: rows
    }
  }
 

  // 页面http://quote.eastmoney.com/sh513360.html
  // 但是该页面没用到对应接口，而是获取kline的图片。接口参考上面stock的kline接口
  async getEtfKLine(logger, fundCode) {
    let secid = '';
    let head = fundCode.slice(0, 1)
    if (head == '5') {
      // 表示沪市
      secid = '1.' + fundCode; 
    } else if (head == '1') {
      // 表示深市
      secid = '0.' + fundCode;
    } else {
      // 兜底值先用0.
      secid = '0.' + fundCode; 
    }

    let rows = await this._getKline(logger, {
      'secid': secid
    });
    for (let r of rows) {
      r.fundCode = fundCode;
    }
    return {
      total: rows.length,
      rows: rows
    }
  }

  // 页面http://quote.eastmoney.com/f1.html?code=002487&market=2
  // async getStockFenShi(logger, stockCode) {
  //   logger.info('[getStockFenShi] stockCode=', stockCode)
  //   // let url = 'http://push2ex.eastmoney.com/getStockFenShi?pagesize=144&ut=7eea3edcaed734bea9cbfc24409ed989&dpt=wzfscj&cb=jQuery1124034075517163279034_1643695047437&pageindex=0&id=0024872&sort=1&ft=1&code=002487&market=0&_=1643695047438'
  //   let url = 'http://push2ex.eastmoney.com/getStockFenShi';
  //   let params = {
  //     cb: 'jQuery1124034075517163279034_1641621447438',
  //     ut: '7eea3edcaed734bea9cbfc24409ed989', // 这个参数必须加上，而且目前看着是固定的.
  //     pagesize: 144,
  //     dpt: 'wzfscj',
  //     pageindex: 0,
  //     id: stockCode,
  //     sort: 1,
  //     ft: 1,
  //     code: stockCode,
  //     market: 1,  // 这个market参数值，很像kline接口的secid参数前缀。0表示00、30、20等开头，1表示68、60开头
  //     _: '1641621447438'
  //   };
  //   if (Object.keys(params).length) {
  //     url += '?' + qs.stringify(params);
  //   }
  //   let body = await (url);
  //   body = body.slice('jQuery1124034075517163279034_1641621447438'.length + 1, -2)
  //   body = JSON.parse(body);

  //   let rows = [];
  //   for (let d of body.data.data) {
  //     rows.push(d)
  //   }

  //   return {
  //     total: rows.length,
  //     rows: rows
  //   }
  // }

  // async getIndexFenshiTrends(logger, indexCode) {
  //   logger.info('[getIndexFenshiTrends] indexCode=', indexCode)

  //   // secid的逻辑有待研究，跟stockCode不太一样，这里上证指数是1.开头
  //   let secid = '1.' + indexCode;
  //   // let head = stockCode.slice(0, 2)
  //   // // 这个secid的参数值，很像分时接口的market参数。0表示00、30、20等开头，1表示68、60开头
  //   // if (head == '00' || head == '30') {
  //   //   secid = '0.' + stockCode; 
  //   // } else if (head == '68' || head == '60') {
  //   //   secid = '1.' + stockCode;
  //   // } else {
  //   //   // 兜底值先用0.
  //   //   secid = '0.' + stockCode; 
  //   // }

  //   // http://push2his.eastmoney.com/api/qt/stock/trends2/get?fields1=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13&fields2=f51,f52,f53,f54,f55,f56,f57,f58&ut=fa5fd1943c7b386f172d6893dbfba10b&iscr=0&ndays=1&secid=1.000001&cb=jQuery35103487176572911206_1661594997864&_=1661594997865
  //   let url = 'http://push2his.eastmoney.com/api/qt/stock/trends2/get';
  //   // fileds表示前端页面展示的字段位置，表示返回哪些字段。未知的field可以都保存原始字段，到时入库后再提取分析，找规律
  //   // f51: 时间
  //   // f53: 分钟内最低价，比如09:31表示30-31分之间，下同
  //   // f54: 分钟内最高价
  //   // f56: 分钟内成交量，比如09:31表示30-31分之间
  //   // f61: 当日累计成交量
  //   let params = {
  //     // cb: 'jQuery35103487176572911206_1661594997864',
  //     secid: secid,
  //     // ut: 
  //     fields1: 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f11,f12,f13',
  //     fields2: 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61',
  //     // iscr: 0,
  //     // ndays: 1,
  //     _: new Date('2022-08-25 16:00:00').getTime()
  //   };

  //   if (Object.keys(params).length) {
  //     url += '?' + qs.stringify(params);
  //   }
  //   let body = await (url);
  //   body = JSON.parse(body);

  //   let rows = [];
  //   for (let d of body.data.trends) {
  //     rows.push(this._parseStockTrends(indexCode, d))
  //   }
  //   // logger.info('rows: ', rows)
  //   // logger.info('body: ', body.data.trends)
  //   return {
  //     total: rows.length,
  //     rows: rows
  //   }
  // }

  async GetLastTradeDate(logger) {
    return "2025-04-22"
    // 直接以获取000001的最新记录日期
    let body = await this.getIndexKLine(logger, '000001', 'sh');
    logger.debug2("GetLastTradeDate: ", body)
    let lastRecord = body.rows[body.rows.length - 1];
    return lastRecord.date;
  }

  /**
   * 这里details接口似乎比trends2接口更好，trends2一分钟只有一个数据点，details似乎3s钟就有一条记录。这里可以两边一起记录后做对账
   */
  async getStockFenshiDetails(logger, stockCode) {
    logger.info('[getStockFenshiDetails] stockCode=', stockCode)

    // secid的逻辑有待研究，跟stockCode不太一样，这里上证指数是1.开头
    // let secid = '1.' + stockCode;
    let secid = '';
    let head = stockCode.slice(0, 2)
    // 这个secid的参数值，很像分时接口的market参数。0表示00、30、20等开头，1表示68、60开头
    if (head == '00' || head == '30') {
      secid = '0.' + stockCode; 
    } else if (head == '68' || head == '60') {
      secid = '1.' + stockCode;
    } else {
      // 兜底值先用0.
      secid = '0.' + stockCode; 
    }

    // http://push2.eastmoney.com/api/qt/stock/details/get?ut=fa5fd1943c7b386f172d6893dbfba10b&fields1=f1,f2,f3,f4&fields2=f51,f52,f53,f54,f55&secid=1.000300&pos=-16&cb=jQuery3510721251876002502_1661604054581&_=1661604054582
    let url = 'http://push2.eastmoney.com/api/qt/stock/details/get';

    // fileds表示前端页面展示的字段位置，表示返回哪些字段。未知的field可以都保存原始字段，到时入库后再提取分析，找规律
    // f51: 时间
    // f52: 价格
    // f53: 成交数量
    // f54: 似乎都是0，不知道干啥
    // f55: 似乎都是1或者2
    let params = {
      // cb: 'jQuery35103487176572911206_1661594997864',
      secid: secid,
      // ut: 'fa5fd1943c7b386f172d6893dbfba10b',
      fields1: 'f1,f2,f3,f4',
      fields2: 'f51,f52,f53,f54,f55',
      pos: 0,
      _: new Date().getTime()
    };

    if (Object.keys(params).length) {
      url += '?' + qs.stringify(params);
    }

    let options = {
      uri: url,
      json: true
    };

    let body = await this._makeRequest(logger, options);
    logger.debug2('url: ', options.uri);

    let lastTradeDate = await this.GetLastTradeDate(logger);
    logger.info("lastTradeDate: ", lastTradeDate);
    if (!body.data) {
      logger.error("getStockFenshiDetails error:", body, 'param: ', params)
      return {
        total: 0,
        rows: []
      }
    }

    let rows = [];
    for (let d of body.data.details) {
      let item = this._parseStockDetail(stockCode, d);
      // detail接口只有时间点，需要手动获取上一个交易日date
      item.date = lastTradeDate;
      rows.push(item);
    }
    // logger.info(body)
    // logger.info('rows: ', rows)
    // logger.info('body: ', body.data.details)
    // logger.info('body: ', body.data.details)
    return {
      total: rows.length,
      rows: rows
    }
  }

  /**
   * 这里details接口似乎比trends2接口更好，trends2一分钟只有一个数据点，details似乎3s钟就有一条记录。这里可以两边一起记录后做对账
   */
  async getIndexFenshiDetails(logger, indexCode, indexType) {
    // logger.info('[getIndexFenshiDetails] indexCode=', indexCode)

    // secid的逻辑有待研究，跟stockCode不太一样，这里上证指数是1.开头
    // let secid = '1.' + indexCode;
    let secid = '';
    if (indexType == 'zz') {
      // 中证系列指数
      secid = '2.' + indexCode;
    } else if (indexType == 'sz') {
      // 深证系列指数
      secid = '0.' + indexCode;
    } else if (indexType == 'sh') {
      // 上证系列指数
      secid = '1.' + indexCode;
    } else {
      // 兜底
      secid = '1.' + indexCode;
    }

    // http://push2.eastmoney.com/api/qt/stock/details/get?ut=fa5fd1943c7b386f172d6893dbfba10b&fields1=f1,f2,f3,f4&fields2=f51,f52,f53,f54,f55&secid=1.000300&pos=-16&cb=jQuery3510721251876002502_1661604054581&_=1661604054582
    let url = 'http://push2.eastmoney.com/api/qt/stock/details/get';

    // fileds表示前端页面展示的字段位置，表示返回哪些字段。未知的field可以都保存原始字段，到时入库后再提取分析，找规律
    // f51: 时间
    // f52: 价格
    // f53: 成交数量
    // f54: 似乎都是0，不知道干啥
    // f55: 似乎都是1或者2
    let params = {
      // cb: 'jQuery35103487176572911206_1661594997864',
      secid: secid,
      // ut: 'fa5fd1943c7b386f172d6893dbfba10b',
      fields1: 'f1,f2,f3,f4',
      fields2: 'f51,f52,f53,f54,f55',
      pos: 0,
      _: new Date().getTime()
    };

    if (Object.keys(params).length) {
      url += '?' + qs.stringify(params);
    }

    let options = {
      uri: url,
      json: true
    };

    let body = await this._makeRequest(logger, options);
    logger.debug2('url: ', options.uri);

    let lastTradeDate = await this.GetLastTradeDate(logger);
    logger.info("lastTradeDate: ", lastTradeDate);
    if (!body.data) {
      logger.error("getIndexFenshiDetails error:", body, 'param: ', params)
      return {
        total: 0,
        rows: []
      }
    }

    let rows = [];
    for (let d of body.data.details) {
      let item = this._parseIndexDetail(indexCode, d);
      // detail接口只有时间点，需要手动获取上一个交易日date
      item.date = lastTradeDate;
      rows.push(item);
    }
    // logger.info(body)
    // logger.info('rows: ', rows)
    // logger.info('body: ', body.data.details)
    // logger.info('body: ', body.data.details)
    return {
      total: rows.length,
      rows: rows
    }
  }

  parseShareInfo(stockCode, obj) {
    // console.log('-- obj: ', obj);
    // utils.Assert(obj.END_DATE == obj.LISTING_DATE); // 加了断言有时并不满足。但是web页面展示的日期看起来是END_DATE

    // 暂时只用到股本
    return {
      stockCode: stockCode,
      stockName: obj.SECURITY_NAME_ABBR,
      // app上展示的字段（资料=>股东股本=>股本变动）
      limitedShares: obj.LIMITED_SHARES, // 流通受限股
      unlimitedShares: obj.UNLIMITED_SHARES, // 已流通股
      totalShares: obj.TOTAL_SHARES, // 总股本
      listedAShares: obj.LISTED_A_SHARES, // 流通A股（已上市A股）
      limitedAShares: obj.LIMITED_A_SHARES, // 限售A股

      // B股相关
      bFreeShares: obj.B_FREE_SHARE, // 已流通B股
      hFreeShares: obj.H_FREE_SHARE, // 已流通H股

      // 变动数额（趋势展示）
      limitedSharesChange: obj.LIMITED_SHARES_CHANGE, // 流通受限股变动
      unlimitedSharesChange: obj.UNLIMITED_SHARES_CHANGE, // 已流通股变动
      listedASharesChange: obj.LISTED_ASHARES_CHANGE, // 流通A股变动
      limitedASharesChange: obj.LIMITED_ASHARES_CHANGE, // 限售A股变动

      endDate: obj.END_DATE.slice(0, 10), // web上展示的日期
      noticeDate: obj.NOTICE_DATE.slice(0, 10), // 公告日期
      listingDate: obj.LISTING_DATE.slice(0, 10), // 股本变更的上市日期
      changeReason: obj.CHANGE_REASON, // 股本变动原因
      changeReasonExplain: obj.CHANGE_REASON_EXPLAIN, // 变更原因解释

      // 其他
      nonFreeShares: obj.NON_FREE_SHARES, // 未流通股份
      limitedStateShares: obj.LIMITED_STATE_SHARES, // 国家持股（受限）；比如600656，web页面股本结构=>历年股本变动
      limitedOthars: obj.LIMITED_OTHARS, // 其他内资持股（受限）；同上
      limitedDomesticNostate: obj.LIMITED_DOMESTIC_NOSTATE, // 境内法人持股（受限）

      otherFreeShares: obj.OTHER_FREE_SHARES, // 已流通股 = 流通A股+境外流通股+其他流通股；目前定位到是D股上市

      crawlerDate: utils.FormatDate(new Date()),
      // .... 还有很多字段，需要再取
    };
  }

  // 背景: B股没有valuations，为了验证上证综指，快速拉取B股日K与shares验证。
  //  背景2: valuations最早记录只有(2018/01/02)，再往前的只能这里获取。而且老八股的IPO记录也只能通过这里获取了。
  // 页面（股本结构）：https://emweb.securities.eastmoney.com/pc_hsf10/pages/index.html?type=web&code=SH900957&color=b#/gbjg
  async GetStockShareList(logger, stockCode) {
    // url: https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_F10_EH_EQUITY&columns=SECUCODE%2CSECURITY_CODE%2CNON_FREE_SHARES%2CLIMITED_SHARES%2CUNLIMITED_SHARES%2CTOTAL_SHARES%2CLISTED_A_SHARES%2CB_FREE_SHARE%2CH_FREE_SHARE%2COTHER_FREE_SHARES%2CNON_FREESHARES_RATIO%2CLIMITED_SHARES_RATIO%2CLISTED_SHARES_RATIO%2CTOTAL_SHARES_RATIO%2CLISTED_A_RATIOPC%2CLISTED_B_RATIOPC%2CLISTED_H_RATIOPC%2CLISTED_OTHER_RATIOPC%2CLISTED_SUM_RATIOPC&quoteColumns=&filter=(SECUCODE%3D%22900957.SH%22)&pageNumber=1&pageSize=1&sortTypes=-1&sortColumns=END_DATE&source=HSF10&client=PC&v=09809152956537941
    let url = 'https://datacenter.eastmoney.com/securities/api/data/v1/get';
    let params = {
      reportName: 'RPT_F10_EH_EQUITY',
      columns: 'ALL',
      quoteColumns: '',
      filter: `(SECURITY_CODE="${stockCode}")`,
      pageNumber: 1,
      pageSize: 10000,
      sortTypes: -1,
      sortColumns: 'END_DATE',
      source: 'HSF10',
      client: 'PC',
      v: '09809152956537941',
    };
    url += '?' + qs.stringify(params);

    let options = {
      uri: url,
      json: true
    };

    let body = await this._makeRequest(logger, options);
    logger.debug2('url: ', options.uri);

    if (body.code != 0) {
      logger.error('GetSharesInfo: ', body);
      throw new Error(body);
    }

    let rows = [];
    for (let d of body.result.data) {
      rows.push(this.parseShareInfo(stockCode, d));
    }
    return {
      rows: rows,
      total: rows.length,
    };
    return this.parseShareInfo(stockCode, body.result.data[4]);
  }

  async GetStockShareInfo(logger, stockCode) {
    // NOTE: 这个接口实际上获取的是历年股本变动的list。这里先取最后一个元素来作为最新的。假定B股的股本变动不大
    let ret = await this.GetStockShareList(logger, stockCode);
    return ret.rows[0];
  }

  // 东方财富网 > 数据中心 > 宁德时代个股数据 => 发行分配 > 发行与分红
  // https://data.eastmoney.com/stockdata/300750.html
  async GetIPORecord() {
    // 发行与分红（新股、增发、分红记录）
    // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112308405709889060404_1692617756257&reportName=RPT_ISSUEBONUS_DET&columns=ALL&quoteColumns=&pageNumber=1&source=WEB&client=WEB&filter=(SECURITY_CODE%3D%22300750%22)(START_DATE_NUM%3D%221%22)&_=1692617756258
    //

    // 派现募资（分红、新股、增发、配股）
    // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112308405709889060404_1692617756255&reportName=RPT_ISSUEBONUS_STA&columns=ALL&quoteColumns=&pageNumber=1&source=WEB&client=WEB&filter=(SECURITY_CODE%3D%22300750%22)&_=1692617756256
  }

  // 东方财富网 > 数据中心 > 新股数据 > 宁德时代
  // https://data.eastmoney.com/xg/xg/detail/300750.html
  // 个股新股发行详细资料页面，包括财务数据等
  async GetIPO(logger, stockCode) {
    // 几个不同内容的请求例子: 
    // 其中发现的几个字段数值含义: 
    //   TOTAL_RAISE_FUNDS = FREE_MARKET_CAP，可以assert一下看看有没有不等的情况
    //   FREE_MARKET_CAP: 发行后市值，应该就是IPO新股和发行价的乘积。流通市值（宁德时代54.62亿）
    //   TOTAL_MARKET_CAP: 总市值，包括非流通市值（比如宁德时代546.15亿）
    //   PE_RATIO_AFTER: 发行市盈率
    //   INDUSTRY_PE_RATIO: 参考行业市盈率（最新）
    //   PE_RATIO_NEWEST: 该股的动态市盈率（最新）
    //
    //   FREECAP_AVG_LOWER: 网下申购需配市值
    //   OFFLINE_PLACING_DATE: 网上申购日（网上发行日期）
    //   OFFLINE_VAP_RATIO: 网下配售中签率(%)
    //   FREECAP_AVG_DATE: 网下申购市值确认日？融资首次公告日？
    //   BALLOT_NOTICE_DATE: 中签缴款日？中签号公布日期？中签结果公告日期？断言是否不相等情况
    //   OFFLINE_VAS_MULTIPLE: 网下配售认购倍数
    //   ONLINE_VA_NUM: 网上有效申购户数（户）
    //   ONLINE_VA_SHARES: 网上有效申购股数（股）
    //   OFFLINE_VAP_OBJECT: 网下有效申购户数（户）
    //   OFFLINE_VATS: 网下有效申购股数（股）
    //   ISSUE_WAY: 发行方式类型
    //   ISSUE_WAY_EXPLAIN: 发行方式说明
    //   TOTAL_APPLY_NUM: 初步询价累计报价股数（万股）
    //
    //   ISSUE_PRICE: 发行价格（元/股）
    //   ISSUE_NUM? : 先用TOTAL_RAISE_FUNDS / ISSUE_PRICE，断言是否整数
    //     - 发现一部分信息在html的info数组里面。。。见getIPO_Type1
    //
    //   PLACING_NUM: 网下配售数量（股）
    //
    //
    //   页面的几部分划分：发行状况；发行方式；申购状况；行业可对比情况；中签号；承销商；首日表现；
    //
    //
    // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112308807501610495192_1692621946567&filter=(SECURITY_CODE="300750")&columns=ISSUE_DATE,ENQUIRY_OBJECT_NUM,EP_OBJECT_NUM,MEDIAN_PRICE,EXCLUDE_MEDIAN_PRICE,WEIGHTAVG_PRICE,EXCLUDE_WEIGHTAVG_PRICE,EFUND_MEDIAN_PRICE,FUND_MEDIAN_PRICE,FUND_WEIGHTAVG_PRICE,EFUND_WEIGHTAVG_PRICE,ISSUE_PRICE,ISSUE_PE_RATIO,PLACING_OBJECT_NUM,PLACING_NUM&source=WEB&client=WEB&reportName=RPT_ENQUIRY&_=1692621946568
    // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112308807501610495192_1692621946549&filter=(SECURITY_CODE="300750")&columns=INDUSTRY,PAR_VALUE,TOTAL_RAISE_FUNDS,OFFLINE_PLACING_DATE,OFFLINE_VAP_RATIO,FREECAP_AVG_LOWER,FREECAP_AVG_DATE,BALLOT_NOTICE_DATE,OFFLINE_VAS_MULTIPLE,ONLINE_VA_NUM,OFFLINE_VAP_OBJECT,ONLINE_VA_SHARES,OFFLINE_VATS,ISSUE_WAY,ISSUE_WAY_EXPLAIN,ORIG_TRANSFER_NUM,TOTAL_APPLY_NUM&source=WEB&client=WEB&reportName=RPT_IPO_OTHERDETAILS&_=1692621946550
    //
    // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112308807501610495192_1692621946553&filter=(SECURITY_CODE="300750")(%40REFERENCE_SECURITY_CODE="NULL")&columns=SECUCODE,SECURITY_CODE,SECURITY_INNER_CODE,INDUSTRY,INDUSTRY_PE_RATIO,TOTAL_MARKET_CAP,FREE_MARKET_CAP,PE_RATIO_AFTER,REFERENCE_AVG_MARKETCAP,AVG_PE_P1AR,AVG_Q3R,PE_RATIO_NEWEST&source=WEB&client=WEB&reportName=RPT_IPO_COMPARE&_=1692621946554
    //
    // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112308807501610495192_1692621946557&filter=(SECURITY_CODE="300750")&columns=ITEM_NUM,ITEM_NAME,PLAN_INVEST_AMT,TOTAL_PLAN_INVEST,TOTAL_RAISE_FUNDS,INVEST_RAISE_GAP,INVEST_RAISE_RATIO&source=WEB&client=WEB&reportName=RPT_RFINVESTITEMIPO&sortColumns=ITEM_NUM&sortTypes=1&_=1692621946558
    //
    // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112308807501610495192_1692621946567&filter=(SECURITY_CODE="300750")&columns=ISSUE_DATE,ENQUIRY_OBJECT_NUM,EP_OBJECT_NUM,MEDIAN_PRICE,EXCLUDE_MEDIAN_PRICE,WEIGHTAVG_PRICE,EXCLUDE_WEIGHTAVG_PRICE,EFUND_MEDIAN_PRICE,FUND_MEDIAN_PRICE,FUND_WEIGHTAVG_PRICE,EFUND_WEIGHTAVG_PRICE,ISSUE_PRICE,ISSUE_PE_RATIO,PLACING_OBJECT_NUM,PLACING_NUM&source=WEB&client=WEB&reportName=RPT_ENQUIRY&_=1692621946568
    //
    // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112309400764932331884_1705074252913&columns=ORG_CODE%2CSECURITY_CODE%2CSECUCODE%2CSECURITY_NAME_ABBR%2CAPPLY_CODE%2CTRADE_MARKET%2CISSUE_PRICE%2CAFTER_ISSUE_PE%2CINDUSTRY%2CISSUE_WAY%2CONLINE_ISSUE_NUM%2COFFLINE_ISSUE_NUM%2CONLINE_ISSUE_DATE%2COFFLINE_PLACING_DATE%2CINDUSTRY_PE%2CTOTAL_RAISE_FUNDS%2CSTRATEGIC_PLACING_NUM%2CTOTAL_ISSUE_NUM%2CAPPLY_NUM_UPPER%2CBALLOT_PAY_DATE%2CONLINE_PREDICT_TAM%2CONLINE_FREEZE_STARTDATE%2CONLINE_FREEZE_ENDDATE%2COFFLINE_REFUND_DATE%2CONLINE_REFUND_DATE%2COFFLINE_START_DATE%2COFFLINE_END_DATE%2CPRICE_WAY&token=894050c76af8597a853f5b408b759f5d&source=NEEQSELECT&reportName=RPT_ISSUE_BASICINFO&filter=(SECURITY_CODE%3D%22836961%22)&client=WEB&pageSize=1&_=1705074252919

    logger.debug(`stock: ${stockCode}`);
    let data = await this.getIPO_Type2(logger, stockCode);
    if (data == null) {
      data = await this.getIPO_Type1(logger, stockCode); 
    }
    if (data == null) {
      data = await this.getIPO_Type3(logger, stockCode); 
    }

    if (data == null) {
      logger.error("GetIPO failed, stock=", stockCode);
      return null;
    }

    // logger.debug('success: ', data);
    return data;
  }

  async getIPO_Type2(logger, stockCode) {
    let rows = [];
    let url = 'https://datacenter-web.eastmoney.com/api/data/v1/get';
    let params = {
      filter: `(SECURITY_CODE="${stockCode}")`,
      // columns: 'ISSUE_DATE,ENQUIRY_OBJECT_NUM,EP_OBJECT_NUM,MEDIAN_PRICE,EXCLUDE_MEDIAN_PRICE,WEIGHTAVG_PRICE,EXCLUDE_WEIGHTAVG_PRICE,EFUND_MEDIAN_PRICE,FUND_MEDIAN_PRICE,FUND_WEIGHTAVG_PRICE,EFUND_WEIGHTAVG_PRICE,ISSUE_PRICE,ISSUE_PE_RATIO,PLACING_OBJECT_NUM,PLACING_NUM',
      // columns: 'ISSUE_NUM',
      // columns: 'ISSUE_DATE',
      columns: 'ALL',
      source: 'WEB',
      client: 'WEB',
      reportName: 'RPT_ISSUE_BASICINFO', // 基本资料 - 发行状况
      // reportName: 'RPT_NEEQ_IPOAPPLY', // 基本资料 - 申购状况
      // reportName: 'RPT_IPO_COMPARE', // 基本资料 - 「行业可对比情况（截止申购日期）」
      // reportName: 'RPT_IPO_OTHERDETAILS', // 基本资料 - 「发行状况/发行方式」
      // reportName: 'RPT_RFINVESTITEMIPO', // 基本资料 - 「募集资金将用于的项目」
    };

    if (Object.keys(params).length) {
      url += '?' + qs.stringify(params);
    }

    let options = {
      uri: url,
      json: true
    };

    let body = await this._makeRequest(logger, options);
    logger.debug2('url: ', options.uri);

    if (body.code != 0) {
      // logger.error('url: ', url, '\nbody: ', body, '\nstock: ', stockCode);
      logger.debug('[getIPO_Type2] step1 failed: ', stockCode);
      return null;
    }

    // 申购状况
    params.reportName = 'RPT_NEEQ_IPOAPPLY';
    url = 'https://datacenter-web.eastmoney.com/api/data/v1/get';
    if (Object.keys(params).length) {
      url += '?' + qs.stringify(params);
    }

    options = {
      uri: url,
      json: true
    };

    let body2 = await this._makeRequest(logger, options);
    logger.debug2('url: ', options.uri);

    if (body2.code != 0) {
      // logger.error('url: ', url, '\nbody: ', body2, '\nstock: ', stockCode);
      logger.debug2('[getIPO_Type2] step2 failed: ', stockCode);
      return null;
    }

    let obj = {...body.result.data[0], ...body2.result.data[0]};
    // 暂时没找到ISSUE_DATE
    obj.ISSUE_DATE = obj.ONLINE_ISSUE_DATE;
    return this._parseIPO(obj, "getIPO_Type2");
  }

  async getIPO_Type3(logger, stockCode) {
    // 第三种页面: https://emweb.securities.eastmoney.com/pc_hsf10/pages/index.html?type=web&code=SH600416&color=b#/gsgk/fxxg
    //
    // https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_PCF10_ORG_ISSUEINFO&columns=SECUCODE%2CSECURITY_CODE%2CFOUND_DATE%2CLISTING_DATE%2CAFTER_ISSUE_PE%2CONLINE_ISSUE_DATE%2CISSUE_WAY%2CPAR_VALUE%2CTOTAL_ISSUE_NUM%2CISSUE_PRICE%2CDEC_SUMISSUEFEE%2CTOTAL_FUNDS%2CNET_RAISE_FUNDS%2COPEN_PRICE%2CCLOSE_PRICE%2CTURNOVERRATE%2CHIGH_PRICE%2COFFLINE_VAP_RATIO%2CONLINE_ISSUE_LWR&quoteColumns=&filter=(SECUCODE%3D%22600416.SH%22)&pageNumber=1&pageSize=1&sortTypes=&sortColumns=&source=HSF10&client=PC&v=08013393768249644
    let url = 'https://datacenter.eastmoney.com/securities/api/data/v1/get';
    let params = {
      filter: `(SECURITY_CODE="${stockCode}")`,
      // columns: 'ISSUE_DATE,ENQUIRY_OBJECT_NUM,EP_OBJECT_NUM,MEDIAN_PRICE,EXCLUDE_MEDIAN_PRICE,WEIGHTAVG_PRICE,EXCLUDE_WEIGHTAVG_PRICE,EFUND_MEDIAN_PRICE,FUND_MEDIAN_PRICE,FUND_WEIGHTAVG_PRICE,EFUND_WEIGHTAVG_PRICE,ISSUE_PRICE,ISSUE_PE_RATIO,PLACING_OBJECT_NUM,PLACING_NUM',
      // columns: 'ISSUE_NUM',
      // columns: 'ISSUE_DATE',
      columns: 'ALL',
      source: 'HSF10',
      client: 'PC',
      pageSize: 1,
      pageNumber: 1,
      reportName: 'RPT_PCF10_ORG_ISSUEINFO',
    };
    if (Object.keys(params).length) {
      url += '?' + qs.stringify(params);
    }

    let options = {
      uri: url,
      json: true
    };

    let body = await this._makeRequest(logger, options);
    logger.debug2('url: ', options.uri);

    if (body.code != 0) {
      // logger.error('url: ', url, '\nbody: ', body, '\nstock: ', stockCode);
      logger.debug('[getIPO_Type3] failed: ', stockCode);
      return null;
    }

    let obj = body.result.data[0];
    // 暂时没找到ISSUE_DATE
    obj.ISSUE_DATE = obj.ONLINE_ISSUE_DATE;

    return this._parseIPO(obj, "getIPO_Type3")
  }

  async getIPO_Type1(logger, stockCode) {
    // 页面类型1： https://data.eastmoney.com/xg/xg/detail/300750.html
    ////      let rows = [];
    ////      let url = 'https://datacenter-web.eastmoney.com/api/data/v1/get';
    ////      let params = {
    ////        filter: `(SECURITY_CODE="${stockCode}")`,
    ////        columns: 'ALL',
    ////        source: 'WEB',
    ////        client: 'WEB',
    ////        reportName: 'RPT_ENQUIRY',
    ////      };

    ////      if (Object.keys(params).length) {
    ////        url += '?' + qs.stringify(params);
    ////      }

    ////      // logger.debug('url: ', url);
    ////      let body = await (url);
    ////      body = JSON.parse(body);
    ////      // logger.debug('body: ', body.result.data);

    ////      if (body.code != 0) {
    ////        logger.error('url: ', url, '\nbody: ', body, '\nstock: ', stockCode);
    ////        logger.debug('[getIPO_Type1] step1 failed: ', stockCode);
    ////        return null;
    ////      }


    ////      // 获取发行状况/申购状况的一些字段，比如总市值
    ////      params.reportName = 'RPT_IPO_OTHERDETAILS';
    ////      url = 'https://datacenter-web.eastmoney.com/api/data/v1/get';
    ////      if (Object.keys(params).length) {
    ////        url += '?' + qs.stringify(params);
    ////      }

    ////      // logger.debug('url: ', url);
    ////      let body2 = await (url);
    ////      body2 = JSON.parse(body2);
    ////      // logger.debug('body2: ', body2.result.data);
    ////      if (body2.code != 0) {
    ////        logger.error('url: ', url, '\nbody: ', body2, '\nstock: ', stockCode);
    ////        logger.debug('[getIPO_Type1] step2 failed: ', stockCode);
    ////        return null;
    ////      }

    ////      let obj = {...body.result.data[0], ...body2.result.data[0]};

    ////      // 这种页面的接口找不到「总发行数量」，甚至找不到「网上发行数量」的字段在哪里
    ////      //   ISSUE_NUM? : 先用TOTAL_RAISE_FUNDS / ISSUE_PRICE，断言是否整数
    ////      obj.TOTAL_ISSUE_NUM = obj.TOTAL_RAISE_FUNDS / obj.ISSUE_PRICE;
    ////      // logger.debug(`${stockCode} ${obj.TOTAL_RAISE_FUNDS} / ${obj.ISSUE_PRICE}=`, obj.TOTAL_ISSUE_NUM);
    ////      utils.Assert(Number.isInteger(obj.TOTAL_ISSUE_NUM));
    ////      // 暂时没找到ISSUE_DATE
    ////      obj.ISSUE_DATE = obj.ONLINE_ISSUE_DATE;

    // 最终发现重要信息数组是在html里面。。。
    let url = `https://data.eastmoney.com/xg/xg/detail/${stockCode}.html`;
    let options = {
      uri: url,
      json: true
    };

    let body = await this._makeRequest(logger, options);
    logger.debug2('url: ', options.uri);
    // logger.debug('body: ', body);
    let found = body.match(/var\s*info\s*=\s*({.*?})/g); // g参数无法捕获括号里面的内容并返回
    found = found[0].match(/var\s*info\s*=\s*(\{.*?\})/i);
    let obj = JSON.parse(found[1]);
    logger.debug2('[getIPO_Type1] obj: ', obj);
    if (obj.ISSUE_PRICE == undefined) {
      // logger.debug('[getIPO_Type1] failed: ', stockCode);
      return null
    }

    obj.ISSUE_DATE = obj.APPLY_DATE;
    obj.TOTAL_ISSUE_NUM = obj.ISSUE_NUM * 10000;
    
    // logger.debug('obj: ', obj);
    return this._parseIPO(obj, "getIPO_Type1");
  }

  // 东方财富网 > 数据中心 > 新股数据 > 增发
  // https://data.eastmoney.com/other/gkzf.html
  // Get Secondary Public Offering
  async _getSPO(logger, page, size) {
    // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112304234307033142197_1692618966463&sortColumns=ISSUE_DATE&sortTypes=-1&pageSize=50&pageNumber=2&reportName=RPT_SEO_DETAIL&columns=ALL&quoteColumns=f2~01~SECURITY_CODE~NEW_PRICE&quoteType=0&source=WEB&client=WEB
    let filter = ''; // 没法用SECUCODE或者SECURITY_CODE做过滤
    // filter = '(SEO_TYPE="1")'; // 定向增发
    // filter = '(SEO_TYPE="2")'; // 公开增发
    let url = 'https://datacenter-web.eastmoney.com/api/data/v1/get';
    let params = {
      sortColumns: 'ISSUE_DATE',
      sortTypes: -1,
      pageSize: size,
      pageNumber: page,
      reportName: 'RPT_SEO_DETAIL',
      columns: 'ALL',
      quoteColumns: 'f2~01~SECURITY_CODE~NEW_PRICE',
      quoteType: '0',
      filter: filter,
      source: 'WEB',
      client: 'WEB',
    };

    if (Object.keys(params).length) {
      url += '?' + qs.stringify(params);
    }

    let options = {
      uri: url,
      json: true
    };

    let body = await this._makeRequest(logger, options);
    logger.debug2('url: ', options.uri);

    if (body.code != 0) {
      logger.error('url: ', url, '\nbody: ', body);
    }


    // let i = page*size;
    // for (let d of body.result.data) {
    //   // logger.info(i++, d);
    //   if (d.SECUCODE == '300750.SZ') {
    //     logger.info(i++, d);
    //   }
    //   // logger.info(i++, d.SECUCODE, d.ISSUE_DATE);
    // }
    // logger.debug2('body: ', body);
    return body;
  }

  // 因为没法过滤stockCode，全量记录大概5617条
  // 页面: https://data.eastmoney.com/other/gkzf.html
  async GetSPO(logger) {
    let page = 1;
    let size = 50;
    let rows = [];
    let total = 0;
    let maxLoopTimes = 200; // 避免死循环的最大次数
    for (let page = 1; page < maxLoopTimes; page += 1) {
      // logger.debug(`loop: i=${page} count=${rows.length}`);
      // if (page * size >= total + size) {
      //   logger.debug('break: ', page * size);
      //   break;
      // }

      let body = await this._getSPO(logger, page, size);
      total += body.result.data.length;

      for (let d of body.result.data) {
        let item = this._parseSPO(d);
        rows.push(item);
      }
      logger.debug(`[GetSPO] ${total} records...`);

      if (total > body.result.count || page > body.result.pages) {
        logger.warn(`[GetSPO] overflow page[${page}] total[${total}] body[${body.result}]`);
        break;
      } else if (total == body.result.count) {
        // 正常break
        break;
      }
    }
    logger.debug(`[GetSPO] ${total} records finish.`);

    return {
      total: rows.length,
      rows: rows,
    };
  }

  _parseNotice(r) {
    utils.Assert(r.title == r.title_ch);
    // 对于columns超过1个元素的，把里面'其他'类型的的记录删掉
    // if (r.columns.length > 1) {
    //   r.columns = r.columns.filter(r => r.column_name != '其他');
    // }

    if (r.columns.length > 3) {
      console.warn(r);
    }
    // utils.Assert(r.columns.length <= 3 && r.columns.length > 0);
    // utils.Assert(r.columns.length <= 4 && r.columns.length > 0); // 600656
    // utils.Assert(r.columns[0].column_code == '001003001001003');
    utils.Assert(r.codes.length == 1);

    let type2 = null;
    let type3 = null;
    if (r.columns.length > 1) {
      type2 = r.columns[1].column_name;
    }
    if (r.columns.length > 2) {
      type3 = r.columns[2].column_name;
    }

    return {
      title: r.title,
      artCode: r.art_code,
      noticeType1: r.columns[0].column_name,
      noticeType2: type2,
      noticeType3: type3,
      noticeDate: r.notice_date.slice(0, 10),
      stockCode: r.codes[0].stock_code,
    };
  }

  _parseNoticeContent(d) {
    utils.Assert(d.attach_url == d.attach_url_web);
    utils.Assert(d.attach_list.length == 1);

    return {
      text: d.notice_content,
      pdfUrl: d.attach_url,
      pdfSize: d.attach_size,
      textPageSize: d.page_size,
    };
  }

  async GetNoticePage(logger, artCode) {
    // https://data.eastmoney.com/notices/detail/300750/${artCode}.html
    // 公告内容也不在对应的页面html里面，而是在另外的http接口
    // https://np-cnotice-stock.eastmoney.com/api/content/ann?cb=jQuery112305863073085797124_1705329373397&art_code=AN202304141585474627&client_source=web&page_index=1&_=1705329373398
    let url = 'https://np-cnotice-stock.eastmoney.com/api/content/ann';
    let params = {
      art_code: artCode,
      client_source: 'web',
      page_index: 1,
      _: new Date().getTime()
    };

    let options = {
      uri: url + '?' + qs.stringify(params),
      json: true
    };

    let body = await this._makeRequest(logger, options);
    if (body.success != 1) {
      logger.error(body);
      throw new Error('GetNoticePage failed');
    } 

    let content = this._parseNoticeContent(body.data);

    for (let p = 2; p <= content.textPageSize; ++p) {
      params.page_index = p;
      options.uri = url + '?' + qs.stringify(params);

      body = await this._makeRequest(logger, options);
      if (body.success != 1) {
        logger.error(body);
        throw new Error(`GetNoticePage failed[p=${p} artCode=${artCode}`);
      }
      let more = this._parseNoticeContent(body.data);
      utils.Assert(more.textPageSize == content.textPageSize);
      utils.Assert(more.pdfUrl == content.pdfUrl);
      content.text += more.text;
    }

    if (content.text) {
      const md5 = crypto.createHash('md5');
      content.textMd5 = md5.update(content.text).digest('hex');
    } else {
      // 为了避免混淆是没有数据、还是没有拉取，这里用空文本，表示没有数据。
      content.text = '';
      content.textMd5 = '';
    }

    return content;
  }

  //   // 公告页面-回购注销页面解析
  //   async CrawlerNoticePageAndParseBuyBack(logger, artCode) {
  //     let content = await this.GetNoticePage(logger, artCode)
  //     content = content.text;
  //     // logger.info('html: ', content);
  //     // let match = content.match(/回购注销.*共计([\d|,|\.|万|亿]+)股[\S\s]*公司已于(\d+年\d+月\d+日).*完成.*?([\d,]+)股(减少|变更)为([\d,]+)股/i);
  //     // let match = content.match(/回购注销.*共计([\d,.万亿]+)股/i);
  //     let match = content.match(/.*公司已于(\d+年\d+月\d+日).*完成.*?([\d,]+)股(减少|变更)为([\d,]+)股/i);
  //     if (!match) {
  //       logger.debug(content);
  //       throw new Error(`CrawlerNoticePageAndParseBuyBack failed ${artCode}`);
  //     }
  //     // console.log('found: ', match[1], match[2], match[4]);
  //     let before = utils.ParseNumberComma(match[2]);
  //     let after = utils.ParseNumberComma(match[4]);
  //     let obj = {
  //       shares: before - after,
  //       date: utils.AddDays(utils.ParseChineseDate(match[1]), -1), // NOTE 份额变动应该在前一天
  //       beforeShares: before,
  //       afterShares: after,
  //     };
  //     // console.log('obj: ', obj);
  //     return obj;
  //   }
  // 
  // 移到service层 // 公告页面-股权激励归属结果页面解析
  // async CrawlerNoticePageAndParseESOP(logger, artCode) {
  //   let content = await this.GetNoticePage(logger, artCode)
  //   content = content.text;
  //   // let match = content.match(/本次归属股[票|份]数量.*?([\d,]+)股[\s\S]*上市流通日.*?(\d+年\d+月\d+日)/i);
  //   let matchStock = content.match(/本次归属股[票|份]数量.*?([\d,]+)股/i);
  //   if (!matchStock) {
  //     logger.debug(content);
  //     throw new Error(`CrawlerNoticePageAndParseBuyBack failed ${artCode}`);
  //   }
  //   let shares = utils.ParseNumberComma(matchStock[1]);

  //   let matchDate = content.match(/上市流通日.*?(\d+年\d+月\d+日)/i);
  //   if (!matchDate) {
  //     logger.debug(content);
  //     throw new Error(`CrawlerNoticePageAndParseBuyBack failed ${artCode}`);
  //   }
  //   let date = utils.ParseChineseDate(matchDate[1]);

  //   let obj = {
  //     shares: shares,
  //     date: date,
  //   };
  //   // logger.debug('obj: ', obj);
  //   return obj;
  // }

  // https://data.eastmoney.com/notices/stock/300750.html
  // 东方财富网 > 数据中心 > 公告大全 > 宁德时代 
  async GetCompanyNotices(logger, stockCode, page) {
// https://np-anotice-stock.eastmoney.com/api/security/ann?cb=jQuery112300792944395156574_1705297970396&sr=-1&page_size=50&page_index=2&ann_type=A&client_source=web&stock_list=300750&f_node=0&s_node=0
    let url = 'https://np-anotice-stock.eastmoney.com/api/security/ann';
    let params = {
      sr: -1,
      page_size: 50,
      page_index: page,
      ann_type: 'A',
      client_source: 'web',
      stock_list: stockCode,
      f_node: 0,
      s_node: 0
    };

    let options = {
      uri: url + '?' + qs.stringify(params),
      json: true
    };

    let body = await this._makeRequest(logger, options);
    if (body.success != 1) {
      logger.error('url: ', options.uri, '\nbody: ', body);
      return null;
    }
    if (body.data.list.length == 0) {
      return null;
    }

    let rows = [];
    for (let r of body.data.list) {
      rows.push(this._parseNotice(r));
    }

    return {
      rows: rows,
      total: rows.length,
    };
  }

  // https://data.eastmoney.com/gphg/hglist.html
  // 特色数据 => 股票回购
  async GetStockBuyBacks() {
    // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112306523881833215057_1705296924847&sortColumns=UPD&sortTypes=-1&pageSize=50&pageNumber=2&reportName=RPTA_WEB_GETHGLIST_NEW&columns=ALL&source=WEB
    let url = 'https://datacenter-web.eastmoney.com/api/data/v1/get';
    let params = {
      sortTypes: -1,
      sortColumns: 'UPD',
      pageSize: 50,
      pageNumber: 1,
      columns: 'ALL',
      reportName: 'RPTA_WEB_GETHGLIST_NEW',
      params: '300750.SZ',
      p: page,
      source: 'WEB',
    };

  }

  _parseCompanyEvent(logger, stockCode, d) {
    const md5 = crypto.createHash('md5');
    let level2Content = '';
    if (d.LEVEL2_CONTENT) {
      level2Content = JSON.stringify(d.LEVEL2_CONTENT);
    }
    return {
      stockCode: stockCode,
      eventType: d.EVENT_TYPE,
      specificEventType: d.SPECIFIC_EVENTTYPE,
      noticeDate: d.NOTICE_DATE,
      level1ContentMd5: md5.update(d.LEVEL1_CONTENT).digest('hex'),
      level1Content: d.LEVEL1_CONTENT,
      level2Content: level2Content,
    };
  }

  // https://emweb.securities.eastmoney.com/pc_hsf10/pages/index.html?type=web&code=SZ300750&color=b#/gsds/xsjj
  // 个股 => 公司大事
  async GetCompanyEvents(logger, stockCode, tag, page) {
    // https://datacenter.eastmoney.com/securities/api/data/get?type=RTP_F10_DETAIL&params=300750.SZ&p=2&source=HSF10&client=PC&v=046885534553548913
    let url = 'https://datacenter.eastmoney.com/securities/api/data/get';
    let params = {
      type: 'RTP_F10_DETAIL',
      params: tag,
      p: page,
      source: 'HSF10',
      client: 'PC',
      v: '046885534553548913',
    };
 
    if (Object.keys(params).length) {
      url += '?' + qs.stringify(params);
    }
    // logger.debug('url: ', url);

    // await request({uri: url, encoding: null, resolveWithFullResponse: true})
    //   .then((response) => {
    //     logger.debug('data: ', response);
    //     logger.debug('body: ', response.data);
    //     fs.writeFileSync('/tmp/testdata', response.data);
    //   });
    // NOTE 发现返回的数据存在Content-Encoding:gzip的响应头。需要pipe解压缩。否则直接打印body是乱码。并且encoding需要先用null，不能utf8解码成字符串再解压。
    let data = await request({uri: url, headaers: {'accept-encoding': 'deflate'}, encoding: null});
    let body = zlib.gunzipSync(data).toString('utf8');
    // logger.debug('body: ', body);
    body = JSON.parse(body);

    if (body.code != 0) {
      if (page == 1) {
        logger.warn('url: ', url, '\nbody: ', body);
      }
      return null;
    }

    let rows = [];
    for (let d of body.data) {
      rows.push(this._parseCompanyEvent(logger, stockCode, d[0]));
    }
    // logger.debug('[GetCompanyEvents] rows: ',rows);
    return {
      total: rows.length,
      rows: rows,
    };
  }

  _parseUnlimitStock(d) {
    return {
      stockCode: d.SECURITY_CODE,
      addListingShares: d.ADD_LISTING_SHARES,
      freeDate: d.FREE_DATE.slice(0, 10),
      freeSharesType: d.FREE_SHARES_TYPE,
      liftHolderAll: d.LIFT_HOLDER_ALL,
      liftSharesAll: d.LIFT_SHARES_ALL,
      limitedHolderName: d.LIMITED_HOLDER_NAME,
      limitedType: d.LIMITED_TYPE,
      orgCode: d.ORG_CODE,
      planFeature: d.PLAN_FEATURE,
    };
  }

  // https://datapc.eastmoney.com/emdatacenter/jjg/detail?color=w&code=300750&name=
  async GetUnlimitStocks(logger, stockCode) {
    let url = 'https://datapc.eastmoney.com/emdatacenter/jjg/detaillist2';
    let params = {
      code: stockCode, // 暂时不知道page、size的参数。后面防御一下count不等的情况throw err
      date: '',
    };
    url += '?' + qs.stringify(params);

    let options = {
      uri: url,
      json: true
    };

    let body = await this._makeRequest(logger, options);
    logger.debug2('url: ', options.uri);

    if (body.code != 0) {
      logger.error('url: ', url, '\nbody: ', body);
      // return null;
      return {
        rows: [],
        total: 0,
      };
    }
    let data = body.result.data;
    if (data.length < body.result.count || body.result.page > 1) {
      throw new Error('意想不到的数据，需要改造page/size');
    }

    let rows = [];
    for (let d of data) {
      rows.push(this._parseUnlimitStock(d));
    }

    return {
      rows: rows,
      total: rows.length,
    };
  }

  _parseStockBonus(d) {
    return {
      stockCode: d.SECURITY_CODE,
      reportDate: d.REPORT_DATE.slice(0, 10), // 报告期
      publishDate: d.PUBLISH_DATE.slice(0, 10), // 业绩披露日
      planNoticeDate: d.PLAN_NOTICE_DATE.slice(0, 10), // 预案公告日
      equityRecordDate: d.EQUITY_RECORD_DATE.slice(0, 10), // 股权登记日
      exDividendDate: d.EX_DIVIDEND_DATE.slice(0, 10), // 除权除息日
      noticeDate: d.NOTICE_DATE.slice(0, 10), // 最新公告日期

      assignProgress: d.ASSIGN_PROGRESS, // 方案进度
      implPlanProfile: d.IMPL_PLAN_PROFILE, // 方案描述

      bonusItRatio: d.BONUS_IT_RATIO, // 送转总比例（每10股）
      bonusRatio: d.BONUS_RATIO, // 送股比例（每10股）
      itRatio: d.IT_RATIO, // 转股比例（每10股）
      pretaxBonusRmb: d.PRETAX_BONUS_RMB, // 税前现金分红比例（每10股）
      dividentRatio: d.DIVIDENT_RATIO, // 股息率

      basicEps: d.BASIC_EPS, // 每股收益（元）
      bvps: d.BVPS, // 每股净资产（元）
      perCapitalReserve: d.PER_CAPITAL_RESERVE, // 每股公积金（元）
      perUnassignProfit: d.PER_UNASSIGN_PROFIT, // 每股未分配利润（元）
      totalShares: d.TOTAL_SHARES, // 总股本（股）
      exDividendDays: d.EX_DIVIDEND_DAYS, // 表示今天距离该次除权除息（exDividendDate）的天数
    };
  }

  // https://data.eastmoney.com/yjfp/detail/300750.html
  // 分红送配页面
  async GetStockBonus(logger, stockCode) {
    // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery1123017299310159595072_1705832742841&sortColumns=REPORT_DATE&sortTypes=-1&pageSize=50&pageNumber=1&reportName=RPT_SHAREBONUS_DET&columns=ALL&quoteColumns=&js=%7B%22data%22%3A(x)%2C%22pages%22%3A(tp)%7D&source=WEB&client=WEB&filter=(SECURITY_CODE%3D%22300750%22)
    let url = 'https://datacenter-web.eastmoney.com/api/data/v1/get';
    let params = {
      sortColumns: 'REPORT_DATE',
      sortType: 1,
      pageSize: 500,
      pageNumber: 1,
      reportName: 'RPT_SHAREBONUS_DET',
      columns: 'ALL',
      quoteColumns: '',
      source: 'WEB',
      client: 'WEB',
      filter: `(SECURITY_CODE="${stockCode}")`,
    };
    url += '?' + qs.stringify(params);

    let options = {
      uri: url,
      json: true
    };

    let body = await this._makeRequest(logger, options);
    logger.debug2('url: ', options.uri);

    if (body.code != 0) {
      logger.error('url: ', url, '\nbody: ', body);
      // return null;
      return {
        rows: [],
        total: 0
      };
    }

    let data = body.result.data;
    if (data.length < body.result.count || body.result.page > 1) {
      throw new Error('GetStockBonus 意向不到的数据，需要改造page/size');
    }

    // logger.debug('data: ', data);
    let rows = [];
    for (let d of data) {
      rows.push(this._parseStockBonus(d));
    }
    return {
      rows: rows,
      total: rows.length,
    };
  }

  // 个股数据页: https://data.eastmoney.com/stockdata/300750.html
  // 个股数据 => 财务数据 > 主要业务构成 > 经营分析 > 主营分析
  // https://emweb.securities.eastmoney.com/pc_hsf10/pages/index.html?type=web&code=SZ300750&color=b#/jyfx/zyfw
  async GetStockBusiness(logger, stockCode) {
    // https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HSF9_BASIC_ORGINFO&columns=SECUCODE%2CSECURITY_CODE%2CBUSINESS_SCOPE&quoteColumns=&filter=(SECUCODE%3D%22300750.SZ%22)&pageNumber=1&pageSize=1&sortTypes=&sortColumns=&source=HSF10&client=PC&v=004673720798959646
    let url = 'https://datacenter.eastmoney.com/securities/api/data/v1/get';
    let params = {
      reportName: 'RPT_HSF9_BASIC_ORGINFO',
      // columns: 'SECUCODE%2CSECURITY_CODE%2CBUSINESS_SCOPE',
      columns: 'SECUCODE,SECURITY_CODE,BUSINESS_SCOPE',
      quoteColumns: '',
      // filter: '(SECUCODE%3D%22300750.SZ%22)',
      // filter: '(SECUCODE="300750.SZ")',
      filter: `(SECURITY_CODE="${stockCode}")`,
      pageNumber: 1,
      pageSize: 1,
      sortTypes: '',
      sortColumns: '',
      source: 'HSF10',
      client: 'PC',
      v: '004673720798959646'
    };

    // 根据参数coloums: 'SECUCODE,SECURITY_CODE,BUSINESS_SCOPE'  返回结果举例: 
    // SECUCODE: "300750.SZ"
    // SECURITY_CODE: "300750"
    // BUSINESS_SCOPE: "锂离子电池、锂聚合物电池、燃料电池、动力电池、超大容量储能电池、超级电容器、电池管理系统及可充电电池包……"
    //
    
    if (Object.keys(params).length) {
      url += '?' + qs.stringify(params);
    }

    let options = {
      uri: url,
      json: true
    };

    let body = await this._makeRequest(logger, options);
    logger.debug2('url: ', options.uri);

    if (body.code != 0) {
      logger.error('url: ', url, '\nbody: ', body);
    }

    let data = body.result.data[0];
    if (!data || data.SECURITY_CODE != stockCode || !data.BUSINESS_SCOPE) {
      logger.error('url: ', url, '\ndata: ', data);
    }
    // logger.debug2('data: ', data);
    return data.BUSINESS_SCOPE;
  }

  // 个股数据页: https://data.eastmoney.com/stockdata/300750.html
  // 个股数据 => 财务数据 > 主要业务构成 > 经营分析 > 主营构成分析
  // https://emweb.securities.eastmoney.com/pc_hsf10/pages/index.html?type=web&code=SZ300750&color=b#/jyfx/zygcfx
  async GetStockBusinessComponents(logger, stockCode) {
    // https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_F10_FN_MAINOP&columns=SECUCODE%2CSECURITY_CODE%2CREPORT_DATE%2CMAINOP_TYPE%2CITEM_NAME%2CMAIN_BUSINESS_INCOME%2CMBI_RATIO%2CMAIN_BUSINESS_COST%2CMBC_RATIO%2CMAIN_BUSINESS_RPOFIT%2CMBR_RATIO%2CGROSS_RPOFIT_RATIO%2CRANK&quoteColumns=&filter=(SECUCODE%3D%22300750.SZ%22)&pageNumber=1&pageSize=200&sortTypes=-1%2C1%2C1&sortColumns=REPORT_DATE%2CMAINOP_TYPE%2CRANK&source=HSF10&client=PC&v=08111520166464605
    let url = 'https://datacenter.eastmoney.com/securities/api/data/v1/get';
    let params = {
      reportName: 'RPT_F10_FN_MAINOP',
      columns: 'SECUCODE,SECURITY_CODE,REPORT_DATE,MAINOP_TYPE,ITEM_NAME,MAIN_BUSINESS_INCOME,MBI_RATIO,MAIN_BUSINESS_COST,MBC_RATIO,MAIN_BUSINESS_RPOFIT,MBR_RATIO,GROSS_RPOFIT_RATIO,RANK',
      quoteColumns: '',
      // filter: '(SECUCODE%3D%22300750.SZ%22)',
      // filter: '(SECUCODE="300750.SZ")',
      filter: `(SECURITY_CODE="${stockCode}")`,
      pageNumber: 1,
      pageSize: 200,
      sortTypes: '-1,1,1',
      sortColumns: 'REPORT_DATE,MAINOP_TYPE,RANK',
      source: 'HSF10',
      client: 'PC',
      v: '08111520166464605'
    };
  }


  // 个股数据页: https://data.eastmoney.com/stockdata/300750.html
  // 个股数据 => 财务数据 > 主要业务构成 > 经营分析 > 经营评述
  // https://emweb.securities.eastmoney.com/pc_hsf10/pages/index.html?type=web&code=SZ300750&color=b#/jyfx/jyps
  // 经营评述，暂不实现
  // https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_F10_OP_BUSINESSANALYSIS&columns=SECUCODE%2CSECURITY_CODE%2CREPORT_DATE%2CBUSINESS_REVIEW&quoteColumns=&filter=(SECUCODE%3D%22300750.SZ%22)&pageNumber=1&pageSize=1&sortTypes=&sortColumns=&source=HSF10&client=PC&v=07833304465046542


  async _getValuation(logger, type, code, page, size) {
    // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112304693424450623289_1692440786784&sortColumns=TRADE_DATE&sortTypes=-1&pageSize=50&pageNumber=2&reportName=RPT_VALUEMARKET&columns=ALL&quoteColumns=&filter=(TRADE_DATE%3E%272019-08-19%27)(TRADE_MARKET_CODE%3D%22000300%22)&source=WEB&client=WEB
    let url = 'https://datacenter-web.eastmoney.com/api/data/v1/get';
    let reportName = "";
    let filter = "";
    if (type == "stock") {
      // 个股估值
      // https://data.eastmoney.com/gzfx/detail/300750.html
      reportName = "RPT_VALUEANALYSIS_DET";
      filter = `(SECURITY_CODE="${code}")`;
    } else if (type == "market") {
      // 整个市场估值，一般就沪深两市、沪市主板、科创板、深市主板、创业板
      // https://data.eastmoney.com/gzfx/scgk.html
      reportName = "RPT_VALUEMARKET";
      filter = `(TRADE_DATE>'1990-12-18')(TRADE_MARKET_CODE="${code}")`;
    } else if (type == "industry") {
      // 行业估值
      reportName = "RPT_VALUEINDUSTRY_DET";
      filter = `BOARD_CODE="${code}"`;
    } else {
      throw new Error(`_getValuation Wrong type: ${type}`);
    }

    let params = {
      sortColumns: 'TRADE_DATE',
      sortTypes: -1, // 正排，这样可以只获取增量数据
      pageSize: size,
      pageNumber: page,
      reportName: reportName,
      columns: 'ALL',
      quoteColumns: '',
      filter: filter,
      source: 'WEB',
      client: 'WEB',
    };

    if (Object.keys(params).length) {
      url += '?' + qs.stringify(params);
    }

    let options = {
      uri: url,
      json: true
    };

    let body = await this._makeRequest(logger, options);
    logger.debug2('url: ', options.uri);

    if (body.code != 0) {
      logger.error('url: ', url, '\nbody: ', body);
      throw new Error(`[failed] ${code}`);
    } else {
      // logger.debug2('body: ', {
      //   pages: body.result.pages,
      //   count: body.result.count,
      //   data: '[...]',
      // });
    }
    return body;
  }

  async _getAllValuation(logger, type, code, existDate) {
    let page = 1;
    let size = 50;
    let rows = [];
    let total = 9999;
    let maxLoopTimes = 1000; // 避免死循环的最大次数
    let exitLoop = false;
    for (let page = 1; page < maxLoopTimes; page += 1) {
      logger.debug(`${code} loop: i=${page} count=${rows.length}`);
      if (page * size >= total + size) {
        logger.debug(`${code} break: `, page * size);
        break;
      }

      let body = await this._getValuation(logger, type, code, page, size);
      total = body.result.count;

      for (let d of body.result.data) {
        if (new Date(d.TRADE_DATE).getTime() <= new Date(existDate).getTime()) {
          logger.debug(`${code} exit loop, count=${rows.length}, tradeDate(${d.TRADE_DATE}) <= existDate(${existDate})`);
          exitLoop = true
          break;
        }
        rows.push(d);
      }

      if (exitLoop) {
        break;
      }
    }

    return rows;
  }

  // 东方财富网 > 数据中心 > 估值分析 > 市场估值概况
  // https://data.eastmoney.com/gzfx/scgk.html
  // 个股估值分析页面: https://data.eastmoney.com/gzfx/detail/300750.html
  // 行业估值分析页面: https://data.eastmoney.com/gzfx/hy/016029.html
  async GetMarketValuation(logger, tradeMarketCode, existDate) {
    let rows = [];

    let data = await this._getAllValuation(logger, "market", tradeMarketCode, existDate)
    for (let d of data) {
      let item = this._parseMarketValuation(d);
      logger.debug(d);
      rows.push(item);
    }

    return {
      total: rows.length,
      rows: rows,
    };
  }

  async GetStockValuation(logger, stockCode, existDate) {
    let rows = [];

    let data = await this._getAllValuation(logger, "stock", stockCode, existDate)
    for (let d of data) {
      let item = this._parseStockValuation(d);
      // logger.debug(d);
      rows.push(item);
    }

    return {
      total: rows.length,
      rows: rows,
    };
  }

  async GetIndustryValuation(logger, industryCode, existDate) {
    let rows = [];

    let data = await this._getAllValuation(logger, "industry", industryCode, existDate)
    for (let d of data) {
      let item = this._parseIndustryValuation(d);
      logger.debug(d);
      rows.push(item);
    }

    return {
      total: rows.length,
      rows: rows,
    };
  }

  // TODO 专门获取限售解禁详情的记录页
  // https://datapc.eastmoney.com/emdatacenter/jjg/detail?color=w&code=300750&name=%u5B81%u5FB7%u65F6%u4EE3
  //
  //


  _parsePledge(stockCode, d) {
    let pledgeUnfreezeDate = null;
    if (d.ACTUAL_UNFREEZE_DATE) {
      pledgeUnfreezeDate = d.ACTUAL_UNFREEZE_DATE.slice(0, 10);
    }
    return {
      stockCode: stockCode,
      holderName: d.HOLDER_NAME, // 股东名称
      pledgeNum: d.PF_NUM, // 质押股份数量（股）
      pledgeMarketCap: d.MARKET_CAP, // 质押股份市值（元）
      holdRatio: d.PF_HOLD_RATIO, // 占所持股份比例（%）
      pfTsr: d.PF_TSR, // 占总股本比例（%）
      pfOrg: d.PF_ORG, // 质押机构
      pfReason: d.PF_REASON, // 质押原因
      pfPurpose: d.PF_PURPOSE, // 质押目的
      closePrice: d.CLOSE_FORWARD_ADJPRICE, // 质押日收盘价（元）
      warningLine: d.WARNING_LINE, // 预警线（估算）
      openLine: d.OPENLINE, // 平仓线
      pledgeStartDate: d.PF_START_DATE.slice(0, 10), // 质押开始日期
      pledgeUnfreezeDate: pledgeUnfreezeDate, // 质押解除日期
      unfreezeState: d.UNFREEZE_STATE, // 状态
      noticeDate: d.NOTICE_DATE.slice(0, 10), // 公告日期
    };
  }
  // 质押明细页面: https://data.eastmoney.com/gpzy/detail/300750.html
  async GetSingleStockPledges(logger, stockCode) {
    // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112305562515807193058_1705409129319&sortColumns=NOTICE_DATE&sortTypes=-1&pageSize=50&pageNumber=1&reportName=RPTA_APP_ACCUMDETAILS&columns=ALL&quoteColumns=&source=WEB&client=WEB&filter=(SECURITY_CODE%3D%22300750%22)&t=zymx
    let url = 'https://datacenter-web.eastmoney.com/api/data/v1/get';
    let params = {
      sortColumns: 'NOTICE_DATE',
      sortType: -1,
      pageSize: 500,
      pageNumber: 1,
      reportName: 'RPTA_APP_ACCUMDETAILS',
      columns: 'ALL',
      quoteColumns: '',
      source: 'WEB',
      client: 'WEB',
      filter: `(SECURITY_CODE="${stockCode}")`,
    };
    url += '?' + qs.stringify(params);

    let options = {
      uri: url,
      json: true
    };

    let body = await this._makeRequest(logger, options);
    logger.debug2('url: ', options.uri);

    if (body.code != 0) {
      logger.error('url: ', url, '\nbody: ', body);
    }
 
    let rows = [];
    if (body.result) {
      for (let r of body.result.data) {
        rows.push(this._parsePledge(stockCode, r));
      }
    }
 
    logger.debug('rows: ', rows);
    return {
      rows: rows,
      total: rows.length,
    };
  }

  // // 质押比例页面：https://data.eastmoney.com/gpzy/stock/300750.html
  // // 这个接口质押股数、质押市值都没有准确数字。先弃用
  // async GetSingleStockPledgesRatio(logger, stockCode) {
  //   // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112305724348943865796_1705406582200&sortColumns=TRADE_DATE&sortTypes=-1&pageSize=50&pageNumber=2&reportName=RPT_CSDC_LIST&columns=ALL&quoteColumns=&source=WEB&client=WEB&filter=(SECURITY_CODE%3D%22300750%22)
  //   let url = 'https://datacenter-web.eastmoney.com/api/data/v1/get';
  //   let params = {
  //     sortColumns: 'TRADE_DATE',
  //     sortTypes: -1,
  //     pageSize: 500,
  //     pageNumber: 1,
  //     reportName: 'RPT_CSDC_LIST',
  //     columns: 'ALL',
  //     quoteColumns: '',
  //     source: 'WEB',
  //     client: 'WEB',
  //     filter: `(SECURITY_CODE="${stockCode}")`,
  //   };
  //   url += '?' + qs.stringify(params);

  //   let body = await (url);
  //   body = JSON.parse(body);

  //   if (body.code != 0) {
  //     logger.error('url: ', url, '\nbody: ', body);
  //   }

  //   let rows = [];
  //   for (let r of body.result.data) {
  //     rows.push(r);
  //   }

  //   logger.debug('rows: ', rows);
  //   return {
  //     rows: rows,
  //     total: rows.length,
  //   };
  // }
}

async function test() {
  let east = new EastMoney();
  console.debug2 = console.info;
  // let res = await east.GetSPO(console);
  // let res = await east.GetIPO(console, "300530");
  // let res = await east.GetIPO(console, "600416");
  // let res = await east.GetIPO(console, "001358");
  // let res = await east.GetIPO(console, "300750");
  // let res = await east.GetSingleStockPledges(console, "300750");
  // let res = await east.GetUnlimitStocks(console, "300750");
  // east.GetIPO(console);
  // east.GetLastTradeDate(console);
  // east.GetStockBusiness(console, "300750")
  // let res = await east.getStockKLine(console, "600602")
  // let res = await east.getStockKLine(console, "600656")
  // east.GetMarketValuation(console, '300750', '2023-12-20')
  // let res = await east.GetMarketValuation(console, '000300', '2023-12-20')
  // let res = await east.GetStockValuation(console, '300750', '2023-12-20')
  // let res = await east.GetStockBonus(console, '300750');
  // let res = await east.getStockKLine(console, "900957");
  // let res = await east.GetStockShareInfo(console, "900957");
  // let res = await east.GetStockShareList(console, "600656");
  // let res = await east.GetStockShareList(console, "600602");
  // let res = await east.GetStockShareList(console, "600309");
  //
  // let res = await east.getIndexFenshiTrends(console, '000016');
  let res = await east.getStockFenshiDetails(console, '600028');
  console.log(res);
  // east.GetLastTradeDate();
  // east.GetStockShareList(console, '600690');
}
// test();

module.exports = new EastMoney();
