'use strict';

const qs = require('querystring');
const utils = require('../utils');
const parsers = require('../parsers');

// https://data.eastmoney.com/yjfp/detail/300750.html
// 分红送配页面
async function getStockBonus(logger, stockCode) {
  // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery1123017299310159595072_1705832742841&sortColumns=REPORT_DATE&sortTypes=-1&pageSize=50&pageNumber=1&reportName=RPT_SHAREBONUS_DET&columns=ALL&quoteColumns=&js=%7B%22data%22%3A(x)%2C%22pages%22%3A(tp)%7D&source=WEB&client=WEB&filter=(SECURITY_CODE%3D%22300750%22)
  let url = 'https://datacenter-web.eastmoney.com/api/data/v1/get';
  let params = {
    sortColumns: 'REPORT_DATE',
    sortType: 1,
    pageSize: 500,
    pageNumber: 1,
    reportName: 'RPT_SHAREBONUS_DET',
    columns: 'ALL',
    quoteColumns: '',
    source: 'WEB',
    client: 'WEB',
    filter: `(SECURITY_CODE="${stockCode}")`,
  };
  url += '?' + qs.stringify(params);

  let options = {
    uri: url,
    json: true
  };

  let body = await utils.makeRequest(logger, options);
  logger.debug2('url: ', options.uri);

  if (body.code != 0) {
    logger.error('url: ', url, '\nbody: ', body);
    return {
      rows: [],
      total: 0
    };
  }

  let data = body.result.data;
  if (data.length < body.result.count || body.result.page > 1) {
    throw new Error('GetStockBonus 意向不到的数据，需要改造page/size');
  }

  let rows = [];
  for (let d of data) {
    rows.push(parsers.bonus.parseStockBonus(d));
  }
  return {
    rows: rows,
    total: rows.length,
  };
}

module.exports = {
  getStockBonus
};
