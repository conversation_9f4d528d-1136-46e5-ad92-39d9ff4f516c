'use strict';

const qs = require('querystring');
const utils = require('../utils');
const parsers = require('../parsers');

// 个股数据页: https://data.eastmoney.com/stockdata/300750.html
// 个股数据 => 财务数据 > 主要业务构成 > 经营分析 > 主营分析
// https://emweb.securities.eastmoney.com/pc_hsf10/pages/index.html?type=web&code=SZ300750&color=b#/jyfx/zyfw
async function getStockBusiness(logger, stockCode) {
  // https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HSF9_BASIC_ORGINFO&columns=SECUCODE%2CSECURITY_CODE%2CBUSINESS_SCOPE&quoteColumns=&filter=(SECUCODE%3D%22300750.SZ%22)&pageNumber=1&pageSize=1&sortTypes=&sortColumns=&source=HSF10&client=PC&v=004673720798959646
  let url = 'https://datacenter.eastmoney.com/securities/api/data/v1/get';
  let params = {
    reportName: 'RPT_HSF9_BASIC_ORGINFO',
    columns: 'SECUCODE,SECURITY_CODE,BUSINESS_SCOPE',
    quoteColumns: '',
    filter: `(SECURITY_CODE="${stockCode}")`,
    pageNumber: 1,
    pageSize: 1,
    sortTypes: '',
    sortColumns: '',
    source: 'HSF10',
    client: 'PC',
    v: '004673720798959646'
  };

  // 根据参数coloums: 'SECUCODE,SECURITY_CODE,BUSINESS_SCOPE'  返回结果举例: 
  // SECUCODE: "300750.SZ"
  // SECURITY_CODE: "300750"
  // BUSINESS_SCOPE: "锂离子电池、锂聚合物电池、燃料电池、动力电池、超大容量储能电池、超级电容器、电池管理系统及可充电电池包……"

  let options = {
    uri: url + '?' + qs.stringify(params),
    json: true
  };

  let body = await utils.makeRequest(logger, options);
  logger.debug2('url: ', options.uri);

  if (body.code != 0) {
    logger.error('url: ', url, '\nbody: ', body);
  }

  let data = body.result.data[0];
  if (!data || data.SECURITY_CODE != stockCode || !data.BUSINESS_SCOPE) {
    logger.error('url: ', url, '\ndata: ', data);
  }
  return data.BUSINESS_SCOPE;
}

// 个股数据页: https://data.eastmoney.com/stockdata/300750.html
// 个股数据 => 财务数据 > 主要业务构成 > 经营分析 > 主营构成分析
// https://emweb.securities.eastmoney.com/pc_hsf10/pages/index.html?type=web&code=SZ300750&color=b#/jyfx/zygcfx
async function getStockBusinessComponents(logger, stockCode) {
  // https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_F10_FN_MAINOP&columns=SECUCODE%2CSECURITY_CODE%2CREPORT_DATE%2CMAINOP_TYPE%2CITEM_NAME%2CMAIN_BUSINESS_INCOME%2CMBI_RATIO%2CMAIN_BUSINESS_COST%2CMBC_RATIO%2CMAIN_BUSINESS_RPOFIT%2CMBR_RATIO%2CGROSS_RPOFIT_RATIO%2CRANK&quoteColumns=&filter=(SECUCODE%3D%22300750.SZ%22)&pageNumber=1&pageSize=200&sortTypes=-1%2C1%2C1&sortColumns=REPORT_DATE%2CMAINOP_TYPE%2CRANK&source=HSF10&client=PC&v=08111520166464605
  let url = 'https://datacenter.eastmoney.com/securities/api/data/v1/get';
  let params = {
    reportName: 'RPT_F10_FN_MAINOP',
    columns: 'SECUCODE,SECURITY_CODE,REPORT_DATE,MAINOP_TYPE,ITEM_NAME,MAIN_BUSINESS_INCOME,MBI_RATIO,MAIN_BUSINESS_COST,MBC_RATIO,MAIN_BUSINESS_RPOFIT,MBR_RATIO,GROSS_RPOFIT_RATIO,RANK',
    quoteColumns: '',
    filter: `(SECURITY_CODE="${stockCode}")`,
    pageNumber: 1,
    pageSize: 200,
    sortTypes: '-1,1,1',
    sortColumns: 'REPORT_DATE,MAINOP_TYPE,RANK',
    source: 'HSF10',
    client: 'PC',
    v: '08111520166464605'
  };

  let options = {
    uri: url + '?' + qs.stringify(params),
    json: true
  };

  let body = await utils.makeRequest(logger, options);
  logger.debug2('url: ', options.uri);

  if (body.code != 0) {
    logger.error('url: ', url, '\nbody: ', body);
    return null;
  }

  return body.result.data;
}

module.exports = {
  getStockBusiness,
  getStockBusinessComponents
};
