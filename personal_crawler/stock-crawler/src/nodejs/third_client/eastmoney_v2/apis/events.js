'use strict';

const qs = require('querystring');
const utils = require('../utils');
const parsers = require('../parsers');
const zlib = require('zlib');

// https://emweb.securities.eastmoney.com/pc_hsf10/pages/index.html?type=web&code=SZ300750&color=b#/gsds/xsjj
// 个股 => 公司大事
async function getCompanyEvents(logger, stockCode, tag, page) {
  // https://datacenter.eastmoney.com/securities/api/data/get?type=RTP_F10_DETAIL&params=300750.SZ&p=2&source=HSF10&client=PC&v=046885534553548913
  let url = 'https://datacenter.eastmoney.com/securities/api/data/get';
  let params = {
    type: 'RTP_F10_DETAIL',
    params: tag,
    p: page,
    source: 'HSF10',
    client: 'PC',
    v: '046885534553548913',
  };
 
  // NOTE 发现返回的数据存在Content-Encoding:gzip的响应头。需要pipe解压缩。否则直接打印body是乱码。并且encoding需要先用null，不能utf8解码成字符串再解压。
  let data = await utils.makeRequest({uri: url, headaers: {'accept-encoding': 'deflate'}, encoding: null});
  let body = zlib.gunzipSync(data).toString('utf8');
  body = JSON.parse(body);

  if (body.code != 0) {
    if (page == 1) {
      logger.warn('url: ', url, '\nbody: ', body);
    }
    return null;
  }

  let rows = [];
  for (let d of body.data) {
    rows.push(parsers.events.parseCompanyEvent(logger, stockCode, d[0]));
  }

  return {
    total: rows.length,
    rows: rows,
  };
}

module.exports = {
  getCompanyEvents
};
