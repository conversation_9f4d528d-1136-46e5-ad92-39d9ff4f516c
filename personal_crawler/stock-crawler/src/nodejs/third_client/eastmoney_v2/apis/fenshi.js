'use strict';

const qs = require('querystring');
const request = require('../../../nodejs-common/request');
const parsers = require('../parsers');

// 页面http://quote.eastmoney.com/f1.html?code=002487&market=2
// async getStockFenShi(logger, stockCode) {
//   logger.info('[getStockFenShi] stockCode=', stockCode)
//   // let url = 'http://push2ex.eastmoney.com/getStockFenShi?pagesize=144&ut=7eea3edcaed734bea9cbfc24409ed989&dpt=wzfscj&cb=jQuery1124034075517163279034_1643695047437&pageindex=0&id=0024872&sort=1&ft=1&code=002487&market=0&_=1643695047438'
//   let url = 'http://push2ex.eastmoney.com/getStockFenShi';
//   let params = {
//     cb: 'jQuery1124034075517163279034_1641621447438',
//     ut: '7eea3edcaed734bea9cbfc24409ed989', // 这个参数必须加上，而且目前看着是固定的.
//     pagesize: 144,
//     dpt: 'wzfscj',
//     pageindex: 0,
//     id: stockCode,
//     sort: 1,
//     ft: 1,
//     code: stockCode,
//     market: 1,  // 这个market参数值，很像kline接口的secid参数前缀。0表示00、30、20等开头，1表示68、60开头
//     _: '1641621447438'
//   };
//   if (Object.keys(params).length) {
//     url += '?' + qs.stringify(params);
//   }
//   let body = await (url);
//   body = body.slice('jQuery1124034075517163279034_1641621447438'.length + 1, -2)
//   body = JSON.parse(body);

//   let rows = [];
//   for (let d of body.data.data) {
//     rows.push(d)
//   }

//   return {
//     total: rows.length,
//     rows: rows
//   }
// }

/**
 * 这里details接口似乎比trends2接口更好，trends2一分钟只有一个数据点，details似乎3s钟就有一条记录。这里可以两边一起记录后做对账
 */
async function getStockFenshiDetails(logger, lastTradeDate, stockCode) {
  logger.info('[getStockFenshiDetails] stockCode=', stockCode)

  // secid的逻辑有待研究，跟stockCode不太一样，这里上证指数是1.开头
  // let secid = '1.' + stockCode;
  let secid = '';
  let head = stockCode.slice(0, 2)
  // 这个secid的参数值，很像分时接口的market参数。0表示00、30、20等开头，1表示68、60开头
  if (head == '00' || head == '30') {
    secid = '0.' + stockCode; 
  } else if (head == '68' || head == '60') {
    secid = '1.' + stockCode;
  } else {
    // 兜底值先用0.
    secid = '0.' + stockCode; 
  }

  // http://push2.eastmoney.com/api/qt/stock/details/get?ut=fa5fd1943c7b386f172d6893dbfba10b&fields1=f1,f2,f3,f4&fields2=f51,f52,f53,f54,f55&secid=1.000300&pos=-16&cb=jQuery3510721251876002502_1661604054581&_=1661604054582
  let url = 'http://push2.eastmoney.com/api/qt/stock/details/get';

  // fileds表示前端页面展示的字段位置，表示返回哪些字段。未知的field可以都保存原始字段，到时入库后再提取分析，找规律
  // f51: 时间
  // f52: 价格
  // f53: 成交数量
  // f54: 似乎都是0，不知道干啥
  // f55: 似乎都是1或者2
  let params = {
    // cb: 'jQuery35103487176572911206_1661594997864',
    secid: secid,
    // ut: 'fa5fd1943c7b386f172d6893dbfba10b',
    fields1: 'f1,f2,f3,f4',
    fields2: 'f51,f52,f53,f54,f55',
    pos: 0,
    _: new Date().getTime()
  };

  if (Object.keys(params).length) {
    url += '?' + qs.stringify(params);
  }

  let options = {
    uri: url,
    json: true
  };

  let body = await request.makeRequest(logger, options);
  logger.debug2('url: ', options.uri);

  logger.info("lastTradeDate: ", lastTradeDate);
  if (!body.data) {
    logger.error("getStockFenshiDetails error:", body, 'param: ', params)
    return {
      total: 0,
      rows: []
    }
  }

  let rows = [];
  for (let d of body.data.details) {
    let item = parsers.fenshi.parseStockDetail(stockCode, d);
    // detail接口只有时间点，需要手动获取上一个交易日date
    item.date = lastTradeDate;
    rows.push(item);
  }
  // logger.info(body)
  // logger.info('rows: ', rows)
  // logger.info('body: ', body.data.details)
  // logger.info('body: ', body.data.details)
  return {
    total: rows.length,
    rows: rows
  }
}

/**
 * 这里details接口似乎比trends2接口更好，trends2一分钟只有一个数据点，details似乎3s钟就有一条记录。这里可以两边一起记录后做对账
 */
async function getIndexFenshiDetails(logger, lastTradeDate, indexCode, indexType) {
  // logger.info('[getIndexFenshiDetails] indexCode=', indexCode)

  // secid的逻辑有待研究，跟stockCode不太一样，这里上证指数是1.开头
  // let secid = '1.' + indexCode;
  let secid = '';
  if (indexType == 'zz') {
    // 中证系列指数
    secid = '2.' + indexCode;
  } else if (indexType == 'sz') {
    // 深证系列指数
    secid = '0.' + indexCode;
  } else if (indexType == 'sh') {
    // 上证系列指数
    secid = '1.' + indexCode;
  } else {
    // 兜底
    secid = '1.' + indexCode;
  }

  // http://push2.eastmoney.com/api/qt/stock/details/get?ut=fa5fd1943c7b386f172d6893dbfba10b&fields1=f1,f2,f3,f4&fields2=f51,f52,f53,f54,f55&secid=1.000300&pos=-16&cb=jQuery3510721251876002502_1661604054581&_=1661604054582
  let url = 'http://push2.eastmoney.com/api/qt/stock/details/get';

  // fileds表示前端页面展示的字段位置，表示返回哪些字段。未知的field可以都保存原始字段，到时入库后再提取分析，找规律
  // f51: 时间
  // f52: 价格
  // f53: 成交数量
  // f54: 似乎都是0，不知道干啥
  // f55: 似乎都是1或者2
  let params = {
    // cb: 'jQuery35103487176572911206_1661594997864',
    secid: secid,
    // ut: 'fa5fd1943c7b386f172d6893dbfba10b',
    fields1: 'f1,f2,f3,f4',
    fields2: 'f51,f52,f53,f54,f55',
    pos: 0,
    _: new Date().getTime()
  };

  if (Object.keys(params).length) {
    url += '?' + qs.stringify(params);
  }

  let options = {
    uri: url,
    json: true
  };

  let body = await request.makeRequest(logger, options);
  logger.debug2('url: ', options.uri);

  logger.info("lastTradeDate: ", lastTradeDate);
  if (!body.data) {
    logger.error("getIndexFenshiDetails error:", body, 'param: ', params)
    return {
      total: 0,
      rows: []
    }
  }

  let rows = [];
  for (let d of body.data.details) {
    let item = parsers.fenshi.parseIndexDetail(indexCode, d);
    // detail接口只有时间点，需要手动获取上一个交易日date
    item.date = lastTradeDate;
    rows.push(item);
  }
  // logger.info(body)
  // logger.info('rows: ', rows)
  // logger.info('body: ', body.data.details)
  // logger.info('body: ', body.data.details)
  return {
    total: rows.length,
    rows: rows
  }
}

// async getIndexFenshiTrends(logger, indexCode) {
//   logger.info('[getIndexFenshiTrends] indexCode=', indexCode)
//   // let url = 'http://push2ex.eastmoney.com/getIndexFenshiTrends?pagesize=144&ut=7eea3edcaed734bea9cbfc24409ed989&dpt=wzfscj&cb=jQuery1124034075517163279034_1643695047437&pageindex=0&id=0024872&sort=1&ft=1&code=002487&market=0&_=1643695047438'
//   let url = 'http://push2ex.eastmoney.com/getIndexFenshiTrends';
//   let params = {
//     cb: 'jQuery1124034075517163279034_1641621447438',
//     ut: '7eea3edcaed734bea9cbfc24409ed989', // 这个参数必须加上，而且目前看着是固定的.
//     pagesize: 144,
//     dpt: 'wzfscj',
//     pageindex: 0,
//     id: indexCode,
//     sort: 1,
//     ft: 1,
//     code: indexCode,
//     market: 1,  // 这个market参数值，很像kline接口的secid参数前缀。0表示00、30、20等开头，1表示68、60开头
//     _: '1641621447438'
//   };
//   if (Object.keys(params).length) {
//     url += '?' + qs.stringify(params);
//   }
//   let body = await (url);
//   body = body.slice('jQuery1124034075517163279034_1641621447438'.length + 1, -2)
//   body = JSON.parse(body);

//   let rows = [];
//   for (let d of body.data.data) {
//     rows.push(d)
//   }

//   return {
//     total: rows.length,
//     rows: rows
//   }
// }

module.exports = {
  // getStockFenShi, // 暂时注释掉未使用的方法
  getStockFenshiDetails,
  getIndexFenshiDetails,
  // getIndexFenshiTrends, // 暂时注释掉未使用的方法
};
