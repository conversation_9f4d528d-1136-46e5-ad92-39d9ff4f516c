'use strict';

const qs = require('querystring');
const utils = require('../utils');
const request = require('../../../nodejs-common/request');

const utils2 = require('../../../nodejs-common/utils');
const parsers = require('../parsers');

async function getKline(logger, args) {
  // 页面（宁德时代）http://quote.eastmoney.com/sz300750.html
  // let url = 'http://67.push2his.eastmoney.com/api/qt/stock/kline/get?cb=jQuery11240954883264978859_1643562836212&secid=0.300750&ut=fa5fd1943c7b386f172d6893dbfba10b&fields1=f1%2Cf2%2Cf3%2Cf4%2Cf5%2Cf6&fields2=f51%2Cf52%2Cf53%2Cf54%2Cf55%2Cf56%2Cf57%2Cf58%2Cf59%2Cf60%2Cf61&klt=101&fqt=0&end=20500101&lmt=1000000&_=1643562836325'
  let url = 'http://67.push2his.eastmoney.com/api/qt/stock/kline/get';
  let params = {
    // cb: 'jQuery11240954883264978859_1643562836212',
    // ut: 
    fields1: 'f1,f2,f3,f4,f5,f6',
    // fields1: 'f1',
    fields2: 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63,f64,f65,f66,f67,f68',
    // fields2: 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61', 
    // fields2: 'f1',
    klt: 101,
    fqt: 0, // 如果是1，表示前复权
    end: 20500101,
    // lmt: 1000000,
    // lmt: 50,
    lmt: 5, // 默认值控制在可能没有人为干预的、中断daily_cron的阈值之内就可以了，避免遗漏中间数据
    _: new Date().getTime()
  };
  // NOTE!!! 这段逻辑加了之后，会影响获取到的fields的实际含义，导致parseKLine的字段映射错误
  // for (let i = 2; i < 300; i++) {
  //   params.fields1 += `,f${i}`;
  //   params.fields2 += `,f${i}`; 
  // }

  for (let k in args) {
    params[k] = args[k];
  }

  let options = {
    uri: url + '?' + qs.stringify(params),
    proxy: "http://127.0.0.1:28899",
    json: true
  };

  let body = await request.makeRequest(logger, options);
  logger.debug2('url: ', options.uri);

  await utils2.Sleep(2000);

  let rows = [];
  if (!body.data) {
    logger.error('getKline: ', body);
  } else {
    for (let d of body.data.klines) {
      let parsed = parsers.kline.parseKLine(d);
      // logger.debug2(params.fields2);
      // logger.debug2(`[eastmoney] ${d}`, parsed);
      rows.push(parsed);
    }
  }
  return rows;
}

async function getStockKLine(logger, stockCode, args) {
  // 页面（宁德时代）http://quote.eastmoney.com/sz300750.html
  // let url = 'http://67.push2his.eastmoney.com/api/qt/stock/kline/get?cb=jQuery11240954883264978859_1643562836212&secid=0.300750&ut=fa5fd1943c7b386f172d6893dbfba10b&fields1=f1%2Cf2%2Cf3%2Cf4%2Cf5%2Cf6&fields2=f51%2Cf52%2Cf53%2Cf54%2Cf55%2Cf56%2Cf57%2Cf58%2Cf59%2Cf60%2Cf61&klt=101&fqt=0&end=20500101&lmt=1000000&_=1643562836325'
  let secid = '';
  let head = stockCode.slice(0, 2);
  // 这个secid的参数值，很像分时接口的market参数。0表示00、30、20等开头，1表示68、60开头
  if (head == '00' || head == '30' || head == '20') {
    secid = '0.' + stockCode;
  } else if (head == '68' || head == '60' || head == '90') {
    secid = '1.' + stockCode;
  } else {
    // 兜底值先用0. （比如40开头的退市股）
    secid = '0.' + stockCode;
  }

  args = args ? args : {};
  args.secid = secid;
  let rows = await getKline(logger, args);

  for (let r of rows) {
    r.stockCode = stockCode;
  }

  return {
    total: rows.length,
    rows: rows
  };
}

async function getIndexKLine(logger, indexCode, indexType, limit) {
  // 页面（深证成指）https://quote.eastmoney.com/zs399001.html
  // let url = 'https://push2his.eastmoney.com/api/qt/stock/kline/get?cb=jQuery351004863304295803239_1703255417934&secid=0.399001&ut=fa5fd1943c7b386f172d6893dbfba10b&fields1=f1%2Cf2%2Cf3%2Cf4%2Cf5%2Cf6&fields2=f51%2Cf52%2Cf53%2Cf54%2Cf55%2Cf56%2Cf57%2Cf58%2Cf59%2Cf60%2Cf61&klt=101&fqt=1&beg=0&end=20500101&smplmt=460&lmt=1000000&_=1703255417935'
  let secid = '';
  if (indexType == 'zz') {
    // 中证系列指数
    secid = '2.' + indexCode;
  } else if (indexType == 'sz') {
    // 深证系列指数
    secid = '0.' + indexCode;
  } else if (indexType == 'sh') {
    // 上证系列指数
    secid = '1.' + indexCode;
  } else {
    // 兜底
    secid = '1.' + indexCode;
  }

  let args = {
    'secid': secid
  };
  if (limit) {
    args['lmt'] = limit;
  }
  let rows = await getKline(logger, args);

  for (let r of rows) {
    r.indexCode = indexCode;
  }

  return {
    total: rows.length,
    rows: rows
  };
}

async function getEtfKLine(logger, fundCode) {
  // 页面http://quote.eastmoney.com/sh513360.html
  // 但是该页面没用到对应接口，而是获取kline的图片。接口参考上面stock的kline接口
  let secid = '';
  let head = fundCode.slice(0, 1);
  if (head == '5') {
    // 表示沪市
    secid = '1.' + fundCode;
  } else if (head == '1') {
    // 表示深市
    secid = '0.' + fundCode;
  } else {
    // 兜底值先用0.
    secid = '0.' + fundCode;
  }

  let rows = await getKline(logger, {
    'secid': secid
  });

  for (let r of rows) {
    r.fundCode = fundCode;
  }

  return {
    total: rows.length,
    rows: rows
  };
}

module.exports = {
  getKline,
  getStockKLine,
  getIndexKLine,
  getEtfKLine
};
