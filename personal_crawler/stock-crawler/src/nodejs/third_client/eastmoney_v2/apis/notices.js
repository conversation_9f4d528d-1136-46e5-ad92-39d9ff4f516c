'use strict';

const qs = require('querystring');
const utils = require('../utils');
const parsers = require('../parsers');
const crypto = require('crypto');

// https://data.eastmoney.com/notices/stock/300750.html
// 东方财富网 > 数据中心 > 公告大全 > 宁德时代 
async function getCompanyNotices(logger, stockCode, page) {
  // https://np-anotice-stock.eastmoney.com/api/security/ann?cb=jQuery112300792944395156574_1705297970396&sr=-1&page_size=50&page_index=2&ann_type=A&client_source=web&stock_list=300750&f_node=0&s_node=0
  let url = 'https://np-anotice-stock.eastmoney.com/api/security/ann';
  let params = {
    sr: -1,
    page_size: 50,
    page_index: page,
    ann_type: 'A',
    client_source: 'web', 
    stock_list: stockCode,
    f_node: 0,
    s_node: 0
  };

  let options = {
    uri: url + '?' + qs.stringify(params),
    json: true
  };

  let body = await utils.makeRequest(logger, options);
  if (body.success != 1) {
    logger.error('url: ', options.uri, '\nbody: ', body);
    return null;
  }
  if (body.data.list.length == 0) {
    return null;
  }

  let rows = [];
  for (let r of body.data.list) {
    rows.push(parsers.notices.parseNotice(r));
  }

  return {
    rows: rows, 
    total: rows.length,
  };
}

async function getNoticePage(logger, artCode) {
  // https://data.eastmoney.com/notices/detail/300750/${artCode}.html
  // 公告内容也不在对应的页面html里面，而是在另外的http接口
  // https://np-cnotice-stock.eastmoney.com/api/content/ann?cb=jQuery112305863073085797124_1705329373397&art_code=AN202304141585474627&client_source=web&page_index=1&_=1705329373398
  let url = 'https://np-cnotice-stock.eastmoney.com/api/content/ann';
  let params = {
    art_code: artCode,
    client_source: 'web',
    page_index: 1,
    _: new Date().getTime()
  };

  let options = {
    uri: url + '?' + qs.stringify(params),
    json: true
  };

  let body = await utils.makeRequest(logger, options);
  if (body.success != 1) {
    logger.error(body);
    throw new Error('GetNoticePage failed');
  }

  let content = parsers.notices.parseNoticeContent(body.data);

  for (let p = 2; p <= content.textPageSize; ++p) {
    params.page_index = p;
    options.uri = url + '?' + qs.stringify(params);

    body = await utils.makeRequest(logger, options);
    if (body.success != 1) {
      logger.error(body);
      throw new Error(`GetNoticePage failed[p=${p} artCode=${artCode}`);
    }
    let more = parsers.notices.parseNoticeContent(body.data);
    utils.Assert(more.textPageSize == content.textPageSize);
    utils.Assert(more.pdfUrl == content.pdfUrl);
    content.text += more.text;
  }

  if (content.text) {
    const md5 = crypto.createHash('md5');
    content.textMd5 = md5.update(content.text).digest('hex');
  } else {
    // 为了避免混淆是没有数据、还是没有拉取，这里用空文本，表示没有数据。
    content.text = '';
    content.textMd5 = '';
  }

  return content;
}

module.exports = {
  getCompanyNotices,
  getNoticePage
};
