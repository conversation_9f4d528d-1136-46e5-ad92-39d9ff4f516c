'use strict';

const qs = require('querystring');
const utils = require('../utils');
const parsers = require('../parsers');

// 东方财富网 > 数据中心 > 新股数据 > 宁德时代
// https://data.eastmoney.com/xg/xg/detail/300750.html
// 个股新股发行详细资料页面，包括财务数据等
async function getIPO(logger, stockCode) {
  // 几个不同内容的请求例子: 
  // 其中发现的几个字段数值含义: 
  //   TOTAL_RAISE_FUNDS = FREE_MARKET_CAP，可以assert一下看看有没有不等的情况
  //   FREE_MARKET_CAP: 发行后市值，应该就是IPO新股和发行价的乘积。流通市值（宁德时代54.62亿）
  //   TOTAL_MARKET_CAP: 总市值，包括非流通市值（比如宁德时代546.15亿）
  //   PE_RATIO_AFTER: 发行市盈率
  //   INDUSTRY_PE_RATIO: 参考行业市盈率（最新）
  //   PE_RATIO_NEWEST: 该股的动态市盈率（最新）
  //
  //   FREECAP_AVG_LOWER: 网下申购需配市值
  //   OFFLINE_PLACING_DATE: 网上申购日（网上发行日期）
  //   OFFLINE_VAP_RATIO: 网下配售中签率(%)
  //   FREECAP_AVG_DATE: 网下申购市值确认日？融资首次公告日？
  //   BALLOT_NOTICE_DATE: 中签缴款日？中签号公布日期？中签结果公告日期？断言是否不相等情况
  //   OFFLINE_VAS_MULTIPLE: 网下配售认购倍数
  //   ONLINE_VA_NUM: 网上有效申购户数（户）
  //   ONLINE_VA_SHARES: 网上有效申购股数（股）
  //   OFFLINE_VAP_OBJECT: 网下有效申购户数（户）
  //   OFFLINE_VATS: 网下有效申购股数（股）
  //   ISSUE_WAY: 发行方式类型
  //   ISSUE_WAY_EXPLAIN: 发行方式说明
  //   TOTAL_APPLY_NUM: 初步询价累计报价股数（万股）
  //
  //   ISSUE_PRICE: 发行价格（元/股）
  //   ISSUE_NUM? : 先用TOTAL_RAISE_FUNDS / ISSUE_PRICE，断言是否整数
  //     - 发现一部分信息在html的info数组里面。。。见getIPO_Type2
  //
  //   PLACING_NUM: 网下配售数量（股）
  //
  //
  //   页面的几部分划分：发行状况；发行方式；申购状况；行业可对比情况；中签号；承销商；首日表现；
  //
  //
  // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112308807501610495192_1692621946567&filter=(SECURITY_CODE="300750")&columns=ISSUE_DATE,ENQUIRY_OBJECT_NUM,EP_OBJECT_NUM,MEDIAN_PRICE,EXCLUDE_MEDIAN_PRICE,WEIGHTAVG_PRICE,EXCLUDE_WEIGHTAVG_PRICE,EFUND_MEDIAN_PRICE,FUND_MEDIAN_PRICE,FUND_WEIGHTAVG_PRICE,EFUND_WEIGHTAVG_PRICE,ISSUE_PRICE,ISSUE_PE_RATIO,PLACING_OBJECT_NUM,PLACING_NUM&source=WEB&client=WEB&reportName=RPT_ENQUIRY&_=1692621946568
  // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112308807501610495192_1692621946549&filter=(SECURITY_CODE="300750")&columns=INDUSTRY,PAR_VALUE,TOTAL_RAISE_FUNDS,OFFLINE_PLACING_DATE,OFFLINE_VAP_RATIO,FREECAP_AVG_LOWER,FREECAP_AVG_DATE,BALLOT_NOTICE_DATE,OFFLINE_VAS_MULTIPLE,ONLINE_VA_NUM,OFFLINE_VAP_OBJECT,ONLINE_VA_SHARES,OFFLINE_VATS,ISSUE_WAY,ISSUE_WAY_EXPLAIN,ORIG_TRANSFER_NUM,TOTAL_APPLY_NUM&source=WEB&client=WEB&reportName=RPT_IPO_OTHERDETAILS&_=1692621946550
  //
  // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112308807501610495192_1692621946553&filter=(SECURITY_CODE="300750")(%40REFERENCE_SECURITY_CODE="NULL")&columns=SECUCODE,SECURITY_CODE,SECURITY_INNER_CODE,INDUSTRY,INDUSTRY_PE_RATIO,TOTAL_MARKET_CAP,FREE_MARKET_CAP,PE_RATIO_AFTER,REFERENCE_AVG_MARKETCAP,AVG_PE_P1AR,AVG_Q3R,PE_RATIO_NEWEST&source=WEB&client=WEB&reportName=RPT_IPO_COMPARE&_=1692621946554
  //
  // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112308807501610495192_1692621946557&filter=(SECURITY_CODE="300750")&columns=ITEM_NUM,ITEM_NAME,PLAN_INVEST_AMT,TOTAL_PLAN_INVEST,TOTAL_RAISE_FUNDS,INVEST_RAISE_GAP,INVEST_RAISE_RATIO&source=WEB&client=WEB&reportName=RPT_RFINVESTITEMIPO&sortColumns=ITEM_NUM&sortTypes=1&_=1692621946558
  //
  // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112308807501610495192_1692621946567&filter=(SECURITY_CODE="300750")&columns=ISSUE_DATE,ENQUIRY_OBJECT_NUM,EP_OBJECT_NUM,MEDIAN_PRICE,EXCLUDE_MEDIAN_PRICE,WEIGHTAVG_PRICE,EXCLUDE_WEIGHTAVG_PRICE,EFUND_MEDIAN_PRICE,FUND_MEDIAN_PRICE,FUND_WEIGHTAVG_PRICE,EFUND_WEIGHTAVG_PRICE,ISSUE_PRICE,ISSUE_PE_RATIO,PLACING_OBJECT_NUM,PLACING_NUM&source=WEB&client=WEB&reportName=RPT_ENQUIRY&_=1692621946568
  //
  // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112309400764932331884_1705074252913&columns=ORG_CODE%2CSECURITY_CODE%2CSECUCODE%2CSECURITY_NAME_ABBR%2CAPPLY_CODE%2CTRADE_MARKET%2CISSUE_PRICE%2CAFTER_ISSUE_PE%2CINDUSTRY%2CISSUE_WAY%2CONLINE_ISSUE_NUM%2COFFLINE_ISSUE_NUM%2CONLINE_ISSUE_DATE%2COFFLINE_PLACING_DATE%2CINDUSTRY_PE%2CTOTAL_RAISE_FUNDS%2CSTRATEGIC_PLACING_NUM%2CTOTAL_ISSUE_NUM%2CAPPLY_NUM_UPPER%2CBALLOT_PAY_DATE%2CONLINE_PREDICT_TAM%2CONLINE_FREEZE_STARTDATE%2CONLINE_FREEZE_ENDDATE%2COFFLINE_REFUND_DATE%2CONLINE_REFUND_DATE%2COFFLINE_START_DATE%2COFFLINE_END_DATE%2CPRICE_WAY&token=894050c76af8597a853f5b408b759f5d&source=NEEQSELECT&reportName=RPT_ISSUE_BASICINFO&filter=(SECURITY_CODE%3D%22836961%22)&client=WEB&pageSize=1&_=1705074252919

  logger.debug(`stock: ${stockCode}`);
  let data = await getIPO_Type1(logger, stockCode);
  if (data == null) {
    data = await getIPO_Type2(logger, stockCode); 
  }
  if (data == null) {
    data = await getIPO_Type3(logger, stockCode); 
  }

  if (data == null) {
    logger.error("GetIPO failed, stock=", stockCode);
    return null;
  }

  return data;
}

async function getIPO_Type1(logger, stockCode) {
  let rows = [];
  let url = 'https://datacenter-web.eastmoney.com/api/data/v1/get';
  let params = {
    filter: `(SECURITY_CODE="${stockCode}")`,
    // columns: 'ISSUE_DATE,ENQUIRY_OBJECT_NUM,EP_OBJECT_NUM,MEDIAN_PRICE,EXCLUDE_MEDIAN_PRICE,WEIGHTAVG_PRICE,EXCLUDE_WEIGHTAVG_PRICE,EFUND_MEDIAN_PRICE,FUND_MEDIAN_PRICE,FUND_WEIGHTAVG_PRICE,EFUND_WEIGHTAVG_PRICE,ISSUE_PRICE,ISSUE_PE_RATIO,PLACING_OBJECT_NUM,PLACING_NUM',
    // columns: 'ISSUE_NUM',
    // columns: 'ISSUE_DATE',
    columns: 'ALL',
    source: 'WEB',
    client: 'WEB',
    reportName: 'RPT_ISSUE_BASICINFO', // 基本资料 - 发行状况
    // reportName: 'RPT_NEEQ_IPOAPPLY', // 基本资料 - 申购状况
    // reportName: 'RPT_IPO_COMPARE', // 基本资料 - 「行业可对比情况（截止申购日期）」
    // reportName: 'RPT_IPO_OTHERDETAILS', // 基本资料 - 「发行状况/发行方式」
    // reportName: 'RPT_RFINVESTITEMIPO', // 基本资料 - 「募集资金将用于的项目」
  };

  if (Object.keys(params).length) {
    url += '?' + qs.stringify(params);
  }

  let options = {
    uri: url,
    json: true
  };

  let body = await utils.makeRequest(logger, options);
  logger.debug2('url: ', options.uri);

  if (body.code != 0) {
    // logger.error('url: ', url, '\nbody: ', body, '\nstock: ', stockCode);
    logger.debug('[getIPO_Type1] step1 failed: ', stockCode);
    return null;
  }

  // 申购状况
  params.reportName = 'RPT_NEEQ_IPOAPPLY';
  url = 'https://datacenter-web.eastmoney.com/api/data/v1/get';
  if (Object.keys(params).length) {
    url += '?' + qs.stringify(params);
  }

  options = {
    uri: url,
    json: true
  };

  let body2 = await utils.makeRequest(logger, options);
  logger.debug2('url: ', options.uri);

  if (body2.code != 0) {
    // logger.error('url: ', url, '\nbody: ', body2, '\nstock: ', stockCode);
    logger.debug2('[getIPO_Type1] step2 failed: ', stockCode);
    return null;
  }

  let obj = {...body.result.data[0], ...body2.result.data[0]};
  // 暂时没找到ISSUE_DATE
  obj.ISSUE_DATE = obj.ONLINE_ISSUE_DATE;
  return parsers.offering.parseIPO(obj, "getIPO_Type1");
}

async function getIPO_Type3(logger, stockCode) {
  // 第三种页面: https://emweb.securities.eastmoney.com/pc_hsf10/pages/index.html?type=web&code=SH600416&color=b#/gsgk/fxxg
  //
  // https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_PCF10_ORG_ISSUEINFO&columns=SECUCODE%2CSECURITY_CODE%2CFOUND_DATE%2CLISTING_DATE%2CAFTER_ISSUE_PE%2CONLINE_ISSUE_DATE%2CISSUE_WAY%2CPAR_VALUE%2CTOTAL_ISSUE_NUM%2CISSUE_PRICE%2CDEC_SUMISSUEFEE%2CTOTAL_FUNDS%2CNET_RAISE_FUNDS%2COPEN_PRICE%2CCLOSE_PRICE%2CTURNOVERRATE%2CHIGH_PRICE%2COFFLINE_VAP_RATIO%2CONLINE_ISSUE_LWR&quoteColumns=&filter=(SECUCODE%3D%22600416.SH%22)&pageNumber=1&pageSize=1&sortTypes=&sortColumns=&source=HSF10&client=PC&v=08013393768249644
  let url = 'https://datacenter.eastmoney.com/securities/api/data/v1/get';
  let params = {
    filter: `(SECURITY_CODE="${stockCode}")`,
    columns: 'ALL',
    source: 'HSF10',
    client: 'PC',
    pageSize: 1,
    pageNumber: 1,
    reportName: 'RPT_PCF10_ORG_ISSUEINFO',
  };
  if (Object.keys(params).length) {
    url += '?' + qs.stringify(params);
  }

  let options = {
    uri: url,
    json: true
  };

  let body = await utils.makeRequest(logger, options);
  logger.debug2('url: ', options.uri);

  if (body.code != 0) {
    logger.debug('[getIPO_Type3] failed: ', stockCode);
    return null;
  }

  let obj = body.result.data[0];
  // 暂时没找到ISSUE_DATE
  obj.ISSUE_DATE = obj.ONLINE_ISSUE_DATE;

  return parsers.offering.parseIPO(obj, "getIPO_Type3")
}

async function getIPO_Type2(logger, stockCode) {
  // 页面类型1： https://data.eastmoney.com/xg/xg/detail/300750.html
  // 最终发现重要信息数组是在html里面。。。
  let url = `https://data.eastmoney.com/xg/xg/detail/${stockCode}.html`;
  let options = {
    uri: url,
    json: true
  };

  let body = await utils.makeRequest(logger, options);
  logger.debug2('url: ', options.uri);

  let found = body.match(/var\s*info\s*=\s*({.*?})/g); // g参数无法捕获括号里面的内容并返回
  found = found[0].match(/var\s*info\s*=\s*(\{.*?\})/i);
  let obj = JSON.parse(found[1]);
  logger.debug2('[getIPO_Type2] obj: ', obj);
  if (obj.ISSUE_PRICE == undefined) {
    return null
  }

  obj.ISSUE_DATE = obj.APPLY_DATE;
  obj.TOTAL_ISSUE_NUM = obj.ISSUE_NUM * 10000;
  
  return parsers.offering.parseIPO(obj, "getIPO_Type2");
}

// 东方财富网 > 数据中心 > 新股数据 > 增发
// https://data.eastmoney.com/other/gkzf.html
// Get Secondary Public Offering
async function getSPO(logger) {
  let page = 1;
  let size = 50;
  let rows = [];
  let total = 0;
  let maxLoopTimes = 200; // 避免死循环的最大次数
  for (let page = 1; page < maxLoopTimes; page += 1) {
    let body = await _getSPO(logger, page, size);
    total += body.result.data.length;

    for (let d of body.result.data) {
      let item = parsers.offering.parseSPO(d);
      rows.push(item);
    }
    logger.debug(`[GetSPO] ${total} records...`);

    if (total > body.result.count || page > body.result.pages) {
      logger.warn(`[GetSPO] overflow page[${page}] total[${total}] body[${body.result}]`);
      break;
    } else if (total == body.result.count) {
      // 正常break
      break;
    }
  }
  logger.debug(`[GetSPO] ${total} records finish.`);

  return {
    total: rows.length,
    rows: rows,
  };
}

async function _getSPO(logger, page, size) {
  // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112304234307033142197_1692618966463&sortColumns=ISSUE_DATE&sortTypes=-1&pageSize=50&pageNumber=2&reportName=RPT_SEO_DETAIL&columns=ALL&quoteColumns=f2~01~SECURITY_CODE~NEW_PRICE&quoteType=0&source=WEB&client=WEB
  let filter = ''; // 没法用SECUCODE或者SECURITY_CODE做过滤
  // filter = '(SEO_TYPE="1")'; // 定向增发
  // filter = '(SEO_TYPE="2")'; // 公开增发
  let url = 'https://datacenter-web.eastmoney.com/api/data/v1/get';
  let params = {
    sortColumns: 'ISSUE_DATE',
    sortTypes: -1,
    pageSize: size,
    pageNumber: page,
    reportName: 'RPT_SEO_DETAIL',
    columns: 'ALL',
    quoteColumns: 'f2~01~SECURITY_CODE~NEW_PRICE',
    quoteType: '0',
    filter: filter,
    source: 'WEB',
    client: 'WEB',
  };

  let options = {
    uri: url + '?' + qs.stringify(params),
    json: true
  };

  let body = await utils.makeRequest(logger, options);
  logger.debug2('url: ', options.uri);

  if (body.code != 0) {
    logger.error('url: ', url, '\nbody: ', body);
  }

  return body;
}

module.exports = {
  getIPO,
  getSPO,
  _getSPO
};
