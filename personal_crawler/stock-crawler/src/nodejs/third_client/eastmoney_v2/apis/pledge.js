'use strict';

const qs = require('querystring');
const utils = require('../utils');
const parsers = require('../parsers');

// 质押明细页面: https://data.eastmoney.com/gpzy/detail/300750.html
async function getSingleStockPledges(logger, stockCode) {
  // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112305562515807193058_1705409129319&sortColumns=NOTICE_DATE&sortTypes=-1&pageSize=50&pageNumber=1&reportName=RPTA_APP_ACCUMDETAILS&columns=ALL&quoteColumns=&source=WEB&client=WEB&filter=(SECURITY_CODE%3D%22300750%22)&t=zymx
  let url = 'https://datacenter-web.eastmoney.com/api/data/v1/get';
  let params = {
    sortColumns: 'NOTICE_DATE',
    sortType: -1,
    pageSize: 500,
    pageNumber: 1,
    reportName: 'RPTA_APP_ACCUMDETAILS',
    columns: 'ALL',
    quoteColumns: '',
    source: 'WEB',
    client: 'WEB',
    filter: `(SECURITY_CODE="${stockCode}")`,
  };
  url += '?' + qs.stringify(params);

  let options = {
    uri: url,
    json: true
  };

  let body = await utils.makeRequest(logger, options);
  logger.debug2('url: ', options.uri);

  if (body.code != 0) {
    logger.error('url: ', url, '\nbody: ', body);
  }

  let rows = [];
  if (body.result) {
    for (let r of body.result.data) {
      rows.push(parsers.pledge.parsePledge(stockCode, r));
    }
  }

  return {
    rows: rows,
    total: rows.length,
  };
}

// // 质押比例页面：https://data.eastmoney.com/gpzy/stock/300750.html
// // 这个接口质押股数、质押市值都没有准确数字。先弃用
// async function getSingleStockPledgesRatio(logger, stockCode) {
//   // https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112305724348943865796_1705406582200&sortColumns=TRADE_DATE&sortTypes=-1&pageSize=50&pageNumber=2&reportName=RPT_CSDC_LIST&columns=ALL&quoteColumns=&source=WEB&client=WEB&filter=(SECURITY_CODE%3D%22300750%22)
//   let url = 'https://datacenter-web.eastmoney.com/api/data/v1/get';
//   let params = {
//     sortColumns: 'TRADE_DATE',
//     sortTypes: -1,
//     pageSize: 500,
//     pageNumber: 1,
//     reportName: 'RPT_CSDC_LIST',
//     columns: 'ALL',
//     quoteColumns: '',
//     source: 'WEB',
//     client: 'WEB',
//     filter: `(SECURITY_CODE="${stockCode}")`,
//   };
//   url += '?' + qs.stringify(params);

//   let body = await (url);
//   body = JSON.parse(body);

//   if (body.code != 0) {
//     logger.error('url: ', url, '\nbody: ', body);
//   }

//   let rows = [];
//   for (let r of body.result.data) {
//     rows.push(r);
//   }

//   return {
//     rows: rows,
//     total: rows.length,
//   };
// }

module.exports = {
  getSingleStockPledges
};
