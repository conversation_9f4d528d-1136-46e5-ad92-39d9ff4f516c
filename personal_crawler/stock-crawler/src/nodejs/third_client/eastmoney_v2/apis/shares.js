'use strict';

const qs = require('querystring');
const utils = require('../utils');
const parsers = require('../parsers');

// 背景: B股没有valuations，为了验证上证综指，快速拉取B股日K与shares验证。
//  背景2: valuations最早记录只有(2018/01/02)，再往前的只能这里获取。而且老八股的IPO记录也只能通过这里获取了。
// 页面（股本结构）：https://emweb.securities.eastmoney.com/pc_hsf10/pages/index.html?type=web&code=SH900957&color=b#/gbjg
async function getStockShareList(logger, stockCode) {
  // url: https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_F10_EH_EQUITY&columns=SECUCODE%2CSECURITY_CODE%2CNON_FREE_SHARES%2CLIMITED_SHARES%2CUNLIMITED_SHARES%2CTOTAL_SHARES%2CLISTED_A_SHARES%2CB_FREE_SHARE%2CH_FREE_SHARE%2COTHER_FREE_SHARES%2CNON_FREESHARES_RATIO%2CLIMITED_SHARES_RATIO%2CLISTED_SHARES_RATIO%2CTOTAL_SHARES_RATIO%2CLISTED_A_RATIOPC%2CLISTED_B_RATIOPC%2CLISTED_H_RATIOPC%2CLISTED_OTHER_RATIOPC%2CLISTED_SUM_RATIOPC&quoteColumns=&filter=(SECUCODE%3D%22900957.SH%22)&pageNumber=1&pageSize=1&sortTypes=-1&sortColumns=END_DATE&source=HSF10&client=PC&v=09809152956537941
  let url = 'https://datacenter.eastmoney.com/securities/api/data/v1/get';
  let params = {
    reportName: 'RPT_F10_EH_EQUITY',
    columns: 'ALL',
    quoteColumns: '',
    filter: `(SECURITY_CODE="${stockCode}")`,
    pageNumber: 1,
    pageSize: 10000,
    sortTypes: -1,
    sortColumns: 'END_DATE',
    source: 'HSF10',
    client: 'PC',
    v: '09809152956537941',
  };

  let options = {
    uri: url + '?' + qs.stringify(params),
    json: true
  };

  let body = await utils.makeRequest(logger, options);
  logger.debug2('url: ', options.uri);

  if (body.code != 0) {
    logger.error('GetSharesInfo: ', body);
    throw new Error(body);
  }

  let rows = [];
  for (let d of body.result.data) {
    rows.push(parsers.shares.parseShareInfo(stockCode, d));
  }
  return {
    rows: rows,
    total: rows.length,
  };
}

async function getStockShareInfo(logger, stockCode) {
  // NOTE: 这个接口实际上获取的是历年股本变动的list。这里先取最后一个元素来作为最新的。假定B股的股本变动不大
  let ret = await getStockShareList(logger, stockCode);
  return ret.rows[0];
}

module.exports = {
  getStockShareList,
  getStockShareInfo
};
