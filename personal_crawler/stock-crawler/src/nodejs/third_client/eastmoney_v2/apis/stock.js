'use strict';

const qs = require('querystring');
const request = require('../../../nodejs-common/request');
const parsers = require('../parsers');
const zlib = require('zlib');

async function getClist(logger, args) {
  let ret = [];
  // 最大100页循环
  for (let i = 1; i < 100; i++) {
    let list = await getClistPage(logger, args, i);
    if (list.length == 0) {
      break;
    }
    ret = ret.concat(list)
  }
  return ret;
}

/**
 * 获取单页股票列表数据
 * fileds表示前端页面展示的字段位置，表示返回哪些字段:
 * f1未知(但是发现股票代码90开头的是3，00/20/68/60/30开头的是2)
 * f2最新价，f3涨跌幅，f4涨跌额，f5成交量，f6成交额，f7振幅
 * f8换手率，f9市盈率(动态)，f10量比，f11未知，f12代码(股票/指数)
 * f13未知，f14名称(股票/指数)，f15最高价，f16最低价，f17今开
 * f18昨收，f20-f22未知，f23市净率
 * 未知的field可以都保存原始字段，到时入库后再提取分析，找规律
 */
async function getClistPage(logger, args, pn) {
  let url = 'http://89.push2.eastmoney.com/api/qt/clist/get';
  // url查询示例: http://89.push2.eastmoney.com/api/qt/clist/get?cb=jQuery112402571412670923954_1668930530442&pn=1&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=4027386437815592|0|1|0|web&fid=f3&fs=b:MK0021,b:MK0022,b:MK0023,b:MK0024&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152&_=1668930530443
  
  let params = {
    // cb: '', // jQuery回调函数名,直接请求不需要
    pn: pn, // page number
    pz: 100, // page size，最大只支持100了
    po: 1,
    np: 1,
    ut: 'bd1d9ddb04089700cf9c27f6f7426281',
    fltt: 2,
    invt: 2,
    fid: 'f3', // 默认值，可能args有其他值
    // fileds表示前端页面展示的字段位置，表示返回哪些字段。
    // 其中f1未知(但是发现股票代码90开头的是3，00/20/68/60/30开头的是2)
    // f2最新价，f3涨跌幅，f4涨跌额，f5成交量，f6成交额，f7振幅
    // f8换手率，f9市盈率(动态)，f10量比，f11未知，f12代码(股票/指数)
    // f13未知，f14名称(股票/指数)，f15最高价，f16最低价，f17今开
    // f18昨收，f20-f22未知，f23市净率
    // 未知的field可以都保存原始字段，到时入库后再提取分析，找规律
    fields: 'f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152',
    _: new Date().getTime()
  };
  
  for (let k in args) {
    params[k] = args[k];
  }

  let options = {
    uri: url + '?' + qs.stringify(params),
    json: true
  };

  let body = await request.makeRequest(logger, options);
  logger.debug(`[GET] ${options.uri}`);

  let rows = [];
  if (!body.data) {
    return rows;
  }

  for (let d of body.data.diff) {
    if (d.f2 == '-') {
      // TODO 没有最新价，确认是否为退市状态。暂时先当做退市吧。还有st状态。
      d.state = 'delist';
    }
    rows.push({
      code: d.f12,
      name: d.f14,
      state: d.state,
    });
  }
  return rows;
}

async function getAllStockByFlag(logger, flag) {
  if (flag == 'A') {
    // 页面(沪深京A股): http://quote.eastmoney.com/center/gridlist.html#hs_a_board
    // let url = 'http://17.push2.eastmoney.com/api/qt/clist/get?cb=jQuery1124023164828994737796_1638631233243&pn=1&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&fid=f3&fs=m:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23,m:0+t:81+s:2048&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152&_=1638631233318'
    let body = await getClist(logger, {
      fs: 'm:0+t:6,m:0+t:80,m:1+t:2,m:1+t:23,m:0+t:81+s:2048',
    });
    // 为了获得stockCity的信息。这里扩散成三个城市tab去获取
    let all = parsers.stock.convertToStockResp(body);
    let shStocks = await getAllStockByFlag(logger, 'shA') 
    let szStocks = await getAllStockByFlag(logger, 'szA')
    let bjStocks = await getAllStockByFlag(logger, 'bjA')
    utils.AssertEqual(all.total, shStocks.total + szStocks.total + bjStocks.total);
    let cityMap = {};
    shStocks.rows.forEach((v) => cityMap[v.stockCode] = 'SH');
    szStocks.rows.forEach((v) => cityMap[v.stockCode] = 'SZ');
    bjStocks.rows.forEach((v) => cityMap[v.stockCode] = 'BJ');
    for (let v of all.rows) {
      v.stockCity = cityMap[v.stockCode];
    }
    return all;

  } else if (flag == 'shA') {
    // 页面(上证A股tab页面): http://quote.eastmoney.com/center/gridlist.html#sh_a_board
    // let url = 'http://81.push2.eastmoney.com/api/qt/clist/get?cb=jQuery112407321699085160172_1669048297399&pn=1&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=4027386437815592|0|1|0|web&fid=f3&fs=m:1+t:2,m:1+t:23&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152&_=1669048297712';
    let body = await getClist(logger, {
      wbp2u: '4027386437815592|0|1|0:web', // 没发现差别
      fs: 'm:1+t:2,m:1+t:23',
    });
    return _convertBody2StockResp(body);

  } else if (flag == 'szA') {
    // 页面(深证A股tab页面): http://quote.eastmoney.com/center/gridlist.html#sz_a_board
    // let url = 'http://81.push2.eastmoney.com/api/qt/clist/get?cb=jQuery112407321699085160172_1669048297397&pn=1&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=4027386437815592|0|1|0|web&fid=f3&fs=m:0+t:6,m:0+t:80&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152&_=1669048298060';
    let body = await getClist(logger, {
      wbp2u: '4027386437815592|0|1|0:web',
      fs: 'm:0+t:6,m:0+t:80',
    });
    return _convertBody2StockResp(body);

  } else if (flag == 'bjA') {
    // 页面(北证A股tab页面): http://quote.eastmoney.com/center/gridlist.html#bj_a_board
    let body = await getClist(logger, {
      wbp2u: '4027386437815592|0|1|0:web',
      fs: 'm:0+t:81+s:2048',
    });
    return _convertBody2StockResp(body);

  } else if (flag == 'B') {
    // 页面(B股): http://quote.eastmoney.com/center/gridlist.html#b_board
    let body = await getClist(logger, {
      wbp2u: '4027386437815592|0|1|0:web',
      fs: 'm:0+t:7,m:1+t:3',
    });
    return _convertBody2StockResp(body);

  } else if (flag == 'gem') {
    // 页面(创业板): http://quote.eastmoney.com/center/gridlist.html#gem_board
    let body = await getClist(logger, {
      wbp2u: '4027386437815592|0|1|0:web',
      fs: 'm:0+t:80',
    });
    return _convertBody2StockResp(body);

  } else if (flag == 'kcb') {
    // 科创板: http://quote.eastmoney.com/center/gridlist.html#kcb_board
    let body = await getClist(logger, {
      wbp2u: '4027386437815592|0|1|0:web',
      fs: 'm:1+t:23',
    });
    return _convertBody2StockResp(body);

  } else if (flag == 'sh_hk') {
    // 沪股通: http://quote.eastmoney.com/center/gridlist.html#sh_hk_board
    let body = await getClist(logger, {
      wbp2u: '4027386437815592|0|1|0:web',
      fs: 'b:BK0707',
      fid: 'f26',
    });
    return _convertBody2StockResp(body);

  } else if (flag == 'sz_hk') {
    // 深股通: http://quote.eastmoney.com/center/gridlist.html#sz_hk_board
    let body = await getClist(logger, {
      wbp2u: '4027386437815592|0|1|0:web',
      fs: 'b:BK0804',
      fid: 'f26',
    });
    return _convertBody2StockResp(body);

  } else if (flag == 'st') {
    // 风险警示板(ST): http://quote.eastmoney.com/center/gridlist.html#st_board
    let body = await getClist(logger, {
      wbp2u: '4027386437815592|0|1|0:web',
      fs: 'm:0+f:4,m:1+f:4',
    });
    return _convertBody2StockResp(body);

  } else if (flag == 'staq_net') {
    // 两网及退市: http://quote.eastmoney.com/center/gridlist.html#staq_net_board
    let body = await getClist(logger, {
      wbp2u: '4027386437815592|0|1|0:web',
      fs: 'm:0+s:3',
    });
    return _convertBody2StockResp(body);
  } else if (flag == 'sh_ab') {
    // 上证AB股比价: http://quote.eastmoney.com/center/gridlist.html#ab_comparison_sh
    // let url = 'http://28.push2.eastmoney.com/api/qt/clist/get?cb=jQuery112409275104334730571_1669137224971&pn=1&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=4027386437815592|0|1|0|web&fid=f199&fs=m:1+b:BK0498&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152,f201,f202,f203,f196,f197,f199,f195,f200&_=1669137225105'
    let body = await getClist(logger, {
      wbp2u: '4027386437815592|0|1|0:web', // 没发现差别
      fs: 'm:1+b:BK0498',
      fid: 'f199',
    });
    return _convertBody2StockResp(body);
  } else if (flag == 'sz_ab') {
    // 深证AB股比价: http://quote.eastmoney.com/center/gridlist.html#ab_comparison_sz
    // let url = 'http://28.push2.eastmoney.com/api/qt/clist/get?cb=jQuery112409275104334730571_1669137224971&pn=1&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=4027386437815592|0|1|0|web&fid=f199&fs=m:0+b:BK0498&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152,f201,f202,f203,f196,f197,f199,f195,f200&_=1669137225113'
    let body = await getClist(logger, {
      wbp2u: '4027386437815592|0|1|0:web', // 没发现差别
      fs: 'm:0+s:3',
      fid: 'f199',
    });
    return _convertBody2StockResp(body);
  } else if (flag == 'index_sh') {
    // 页面（上证系列指数）:https://quote.eastmoney.com/center/gridlist.html#index_sh
    // let url = 'https://65.push2.eastmoney.com/api/qt/clist/get?cb=jQuery1124048235124377782523_1703250616096&pn=2&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=|0|0|0|web&fid=f3&fs=m:1+s:2&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f26,f22,f33,f11,f62,f128,f136,f115,f152&_=1703250616133'
    let body = await getClist(logger, {
      wbp2u: '|0|0|0|web',
      fs: 'm:1+s:2',
    });
    return parsers.stock.convertToIndexResp(body);
  } else if (flag == 'index_sz') {
    // 页面（深证系列指数）:https://quote.eastmoney.com/center/gridlist.html#index_sz
    // let url = 'https://46.push2.eastmoney.com/api/qt/clist/get?cb=jQuery1124002654497177392723_1703250624870&pn=2&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=|0|0|0|web&fid=f3&fs=m:0+t:5&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f26,f22,f33,f11,f62,f128,f136,f115,f152&_=1703250625085'
    let body = await getClist(logger, {
      wbp2u: '|0|0|0|web', 
      fs: 'm:0+t:5',
    });
    return parsers.stock.convertToIndexResp(body);
  } else if (flag == 'index_zz') {
    // 页面（中证系列指数）:https://quote.eastmoney.com/center/gridlist.html#index_zzzs
    // let url = 'https://91.push2.eastmoney.com/api/qt/clist/get?cb=jQuery1124071692446961072_1703253808945&pn=2&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=|0|0|0|web&fid=f3&fs=m:2&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f26,f22,f33,f11,f62,f128,f136,f115,f152&_=1703253808951'
    let body = await getClist(logger, {
      wbp2u: '|0|0|0|web',
      fs: 'm:2',
    });
    return parsers.stock.convertToIndexResp(body);
  }
}

// 转换body为统一的股票列表返回格式
// NOTE: 注意新三板的state字段处理可能不一样，需要进一步验证
function _convertBody2StockResp(body) {
  let rows = [];
  for (let d of body) {
    rows.push({
      stockCode: d.code,
      stockName: d.name,
      stockState: d.state,
    })
  }
  return {total: rows.length, rows: rows};
}

/**
 * 页面: http://quote.eastmoney.com/center/gridlist.html#fund_etf
 * url参考: http://89.push2.eastmoney.com/api/qt/clist/get?cb=jQuery112402571412670923954_1668930530442&pn=1&pz=20&po=1&np=1&ut=bd1d9ddb04089700cf9c27f6f7426281&fltt=2&invt=2&wbp2u=4027386437815592|0|1|0|web&fid=f3&fs=b:MK0021,b:MK0022,b:MK0023,b:MK0024&fields=f1,f2,f3,f4,f5,f6,f7,f8,f9,f10,f12,f13,f14,f15,f16,f17,f18,f20,f21,f23,f24,f25,f22,f11,f62,f128,f136,f115,f152&_=1668930530443
 * 
 * 发行与分红（新股、增发、分红记录）
 * https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112308405709889060404_1692617756257&reportName=RPT_ISSUEBONUS_DET&columns=ALL&quoteColumns=&pageNumber=1&source=WEB&client=WEB&filter=(SECURITY_CODE%3D%22300750%22)(START_DATE_NUM%3D%221%22)&_=1692617756258
 *
 * 派现募资（分红、新股、增发、配股）
 * https://datacenter-web.eastmoney.com/api/data/v1/get?callback=jQuery112308405709889060404_1692617756255&reportName=RPT_ISSUEBONUS_STA&columns=ALL&quoteColumns=&pageNumber=1&source=WEB&client=WEB&filter=(SECURITY_CODE%3D%22300750%22)&_=1692617756256
 * 
 * NOTE 目前的接口不稳定，建议参考:
 * 1. http://fund.eastmoney.com/ETFN_jzzzl.html 基金净值估值表现，获取增长率相关数据
 * 2. http://quote.eastmoney.com/center/gridlist.html#fund_etf ETF行情中心，获取报价相关
 * 3. http://fund.eastmoney.com/data/etfbn_jzzzl.html 场内ETF净值排行
 * TODO:
 * - 需要统计ETF基金总数(约800+)，对比下多个页面获取的数据是否一致
 * - 考虑引入新的数据源来补充，比如http://fund.eastmoney.com/data/etfbn_jzzzl.html
 */
async function getAllEtf(logger) {
  let body = await getClist(logger, {
    wbp2u: '4027386437815592|0|1|0:web', // 没发现差别
    fs: 'b:MK0021,b:MK0022,b:MK0023,b:MK0024', // 获取etf还是股票等的关键区别参数
  });

  let rows = [];
  for (let d of body) {
    rows.push({
      fundCode: d.code,
      fundName: d.name
    });
  }
  
  return {total: rows.length, rows: rows};
}

module.exports = {
  getClist,
  getClistPage, 
  getAllStockByFlag,
  getAllEtf,
  _convertBody2StockResp
};