'use strict';

const qs = require('querystring');
const utils = require('../utils');
const parsers = require('../parsers');

// https://datapc.eastmoney.com/emdatacenter/jjg/detail?color=w&code=300750&name=
async function getUnlimitStocks(logger, stockCode) {
  let url = 'https://datapc.eastmoney.com/emdatacenter/jjg/detaillist2';
  let params = {
    code: stockCode, // 暂时不知道page、size的参数。后面防御一下count不等的情况throw err
    date: '',
  };

  let options = {
    uri: url + '?' + qs.stringify(params),
    json: true
  };

  let body = await utils.makeRequest(logger, options);
  logger.debug2('url: ', options.uri);

  if (body.code != 0) {
    logger.error('url: ', url, '\nbody: ', body);
    return {
      rows: [],
      total: 0,
    };
  }
  let data = body.result.data;
  if (data.length < body.result.count || body.result.page > 1) {
    throw new Error('意想不到的数据，需要改造page/size');
  }

  let rows = [];
  for (let d of data) {
    rows.push(parsers.unlimit.parseUnlimitStock(d));
  }

  return {
    rows: rows,
    total: rows.length,
  };
}

module.exports = {
  getUnlimitStocks
};
