'use strict';

const qs = require('querystring'); 
const utils = require('../utils');
const parsers = require('../parsers');

// 页面: https://data.eastmoney.com/gzfx/scgk.html
// 个股估值分析页面: https://data.eastmoney.com/gzfx/detail/300750.html
// 行业估值分析页面: https://data.eastmoney.com/gzfx/hy/016029.html

async function getValuation(logger, type, code, page, size) {
  let url = 'https://datacenter-web.eastmoney.com/api/data/v1/get';
  let reportName = "";
  let filter = "";
  if (type == "stock") {
    reportName = "RPT_VALUEANALYSIS_DET";
    filter = `(SECURITY_CODE="${code}")`;
  } else if (type == "market") {
    reportName = "RPT_VALUEMARKET";
    filter = `(TRADE_DATE>'1990-12-18')(TRADE_MARKET_CODE="${code}")`;
  } else if (type == "industry") {
    reportName = "RPT_VALUEINDUSTRY_DET";
    filter = `BOARD_CODE="${code}"`;
  } else {
    throw new Error(`_getValuation Wrong type: ${type}`);
  }

  let params = {
    sortColumns: 'TRADE_DATE',
    sortTypes: -1, // 正排，这样可以只获取增量数据
    pageSize: size,
    pageNumber: page,
    reportName: reportName,
    columns: 'ALL',
    quoteColumns: '',
    filter: filter,
    source: 'WEB',
    client: 'WEB',
  };

  let options = {
    uri: url + '?' + qs.stringify(params),
    json: true
  };

  let body = await utils.makeRequest(logger, options);
  logger.debug2('url: ', options.uri);

  if (body.code != 0) {
    logger.error('url: ', url, '\nbody: ', body);
    throw new Error(`[failed] ${code}`);
  }

  return body;
}

async function getAllValuation(logger, type, code, existDate) {
  let page = 1;
  let size = 50;
  let rows = [];
  let total = 9999;
  let maxLoopTimes = 1000; // 避免死循环的最大次数
  let exitLoop = false;
  
  for (let page = 1; page < maxLoopTimes; page += 1) {
    logger.debug(`${code} loop: i=${page} count=${rows.length}`);
    if (page * size >= total + size) {
      logger.debug(`${code} break: `, page * size);
      break;
    }

    let body = await getValuation(logger, type, code, page, size);
    total = body.result.count;

    for (let d of body.result.data) {
      if (new Date(d.TRADE_DATE).getTime() <= new Date(existDate).getTime()) {
        logger.debug(`${code} exit loop, count=${rows.length}, tradeDate(${d.TRADE_DATE}) <= existDate(${existDate})`);
        exitLoop = true;
        break;
      }
      rows.push(d);
    }

    if (exitLoop) {
      break;
    }
  }

  return rows;
}

async function getMarketValuation(logger, tradeMarketCode, existDate) {
  let data = await getAllValuation(logger, "market", tradeMarketCode, existDate);
  let rows = [];

  for (let d of data) {
    rows.push(parsers.valuation.parseMarketValuation(d));
  }

  return {
    total: rows.length,
    rows: rows,
  };
}

async function getStockValuation(logger, stockCode, existDate) {
  let data = await getAllValuation(logger, "stock", stockCode, existDate);
  let rows = [];

  for (let d of data) {
    rows.push(parsers.valuation.parseStockValuation(d)); 
  }

  return {
    total: rows.length,
    rows: rows,
  };
}

async function getIndustryValuation(logger, industryCode, existDate) {
  let data = await getAllValuation(logger, "industry", industryCode, existDate);
  let rows = [];

  for (let d of data) {
    rows.push(parsers.valuation.parseIndustryValuation(d));
  }

  return {
    total: rows.length,
    rows: rows,
  };
}

module.exports = {
  getValuation,
  getAllValuation,
  getMarketValuation,
  getStockValuation, 
  getIndustryValuation
};
