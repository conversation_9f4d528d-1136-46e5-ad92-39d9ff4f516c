'use strict';

const apis = require('./apis');
const parsers = require('./parsers');

class EastMoney {
  constructor() {
    // Copy all API methods
    for (const [key, method] of Object.entries(apis)) {
      this[key] = method;
    }
    // Copy all parser methods 
    for (const [key, parser] of Object.entries(parsers)) {
      this[`_parse${key}`] = parser;
    }
  }

  async _makeRequest(logger, options, retries = 3, delay = 1000) {
    // ...existing _makeRequest code...
  }
}

module.exports = new EastMoney();
