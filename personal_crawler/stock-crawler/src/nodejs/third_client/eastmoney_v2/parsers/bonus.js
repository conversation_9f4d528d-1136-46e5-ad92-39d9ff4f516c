'use strict';

function parseStockBonus(d) {
  return {
    stockCode: d.SECURITY_CODE,
    reportDate: d.REPORT_DATE.slice(0, 10), // 报告期
    publishDate: d.PUBLISH_DATE.slice(0, 10), // 业绩披露日
    planNoticeDate: d.PLAN_NOTICE_DATE.slice(0, 10), // 预案公告日
    equityRecordDate: d.EQUITY_RECORD_DATE.slice(0, 10), // 股权登记日
    exDividendDate: d.EX_DIVIDEND_DATE.slice(0, 10), // 除权除息日
    noticeDate: d.NOTICE_DATE.slice(0, 10), // 最新公告日期

    assignProgress: d.ASSIGN_PROGRESS, // 方案进度
    implPlanProfile: d.IMPL_PLAN_PROFILE, // 方案描述

    bonusItRatio: d.BONUS_IT_RATIO, // 送转总比例（每10股）
    bonusRatio: d.BONUS_RATIO, // 送股比例（每10股）
    itRatio: d.IT_RATIO, // 转股比例（每10股）
    pretaxBonusRmb: d.PRETAX_BONUS_RMB, // 税前现金分红比例（每10股）
    dividentRatio: d.DIVIDENT_RATIO, // 股息率

    basicEps: d.BASIC_EPS, // 每股收益（元）
    bvps: d.BVPS, // 每股净资产（元）
    perCapitalReserve: d.PER_CAPITAL_RESERVE, // 每股公积金（元）
    perUnassignProfit: d.PER_UNASSIGN_PROFIT, // 每股未分配利润（元）
    totalShares: d.TOTAL_SHARES, // 总股本（股）
    exDividendDays: d.EX_DIVIDEND_DAYS, // 表示今天距离该次除权除息（exDividendDate）的天数
  };
}

module.exports = {
  parseStockBonus
};
