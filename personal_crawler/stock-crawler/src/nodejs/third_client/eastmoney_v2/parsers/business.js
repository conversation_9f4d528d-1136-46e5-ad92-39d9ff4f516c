'use strict';

function parseBusinessComponent(d) {
  return {
    stockCode: d.SECURITY_CODE,
    reportDate: d.REPORT_DATE.slice(0, 10),
    mainopType: d.MAINOP_TYPE,
    itemName: d.ITEM_NAME,
    mainBusinessIncome: d.MAIN_BUSINESS_INCOME,
    mbiRatio: d.MBI_RATIO,
    mainBusinessCost: d.MAIN_BUSINESS_COST,
    mbcRatio: d.MBC_RATIO,
    mainBusinessProfit: d.MAIN_BUSINESS_RPOFIT,
    mbrRatio: d.MBR_RATIO,
    grossProfitRatio: d.GROSS_RPOFIT_RATIO,
    rank: d.RANK
  };
}

module.exports = {
  parseBusinessComponent
};
