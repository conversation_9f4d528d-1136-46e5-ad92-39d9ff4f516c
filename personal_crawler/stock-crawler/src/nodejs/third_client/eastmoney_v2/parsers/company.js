'use strict';

const crypto = require('crypto');

function parseCompanyEvent(logger, stockCode, d) {
  const md5 = crypto.createHash('md5');
  let level2Content = '';
  if (d.LEVEL2_CONTENT) {
    level2Content = JSON.stringify(d.LEVEL2_CONTENT);
  }
  return {
    stockCode: stockCode,
    eventType: d.EVENT_TYPE,
    specificEventType: d.SPECIFIC_EVENTTYPE,
    noticeDate: d.NOTICE_DATE,
    level1ContentMd5: md5.update(d.LEVEL1_CONTENT).digest('hex'),
    level1Content: d.LEVEL1_CONTENT,
    level2Content: level2Content,
  };
}

function parseNotice(r) {
  let type2 = null;
  let type3 = null;
  if (r.columns.length > 1) {
    type2 = r.columns[1].column_name;
  }
  if (r.columns.length > 2) {
    type3 = r.columns[2].column_name;
  }

  return {
    title: r.title,
    artCode: r.art_code,
    noticeType1: r.columns[0].column_name,
    noticeType2: type2,
    noticeType3: type3,
    noticeDate: r.notice_date.slice(0, 10),
    stockCode: r.codes[0].stock_code,
  };
}

function parseNoticeContent(d) {
  return {
    text: d.notice_content,
    pdfUrl: d.attach_url,
    pdfSize: d.attach_size,
    textPageSize: d.page_size,
  };
}

module.exports = {
  parseCompanyEvent,
  parseNotice,
  parseNoticeContent
};
