'use strict';

function parseStockDetail(stockCode, d) {
  let data = d.split(',');
  return {
    stockCode: stockCode,
    time: data[0],
    price: data[1],
    volume: data[2],
  };
}

function parseIndexDetail(indexCode, d) {
  let data = d.split(',');
  return {
    indexCode: indexCode,
    time: data[0],
    price: data[1], 
    volume: data[2],
  };
}

module.exports = {
  parseStockDetail,
  parseIndexDetail
};
