'use strict';

function parseKLine(d) {
  let data = d.split(',');
  return {
    date: data[0],
    openingPrice: data[1], // 开盘价
    closingPrice: data[2], // 收盘价
    highestPrice: data[3], // 最高价
    lowestPrice: data[4], // 最低价
    tradingVolume: data[5], // 成交量
    turnover: data[6], // 成交额
    amplitude: data[7], // 振幅
    changePercent: data[8], // 涨跌幅
    change: data[9], // 涨跌额
    turnoverRate: data[10], // 换手率
    tradingTurnoverAfterTest: data[11], // 盘后成交额（猜测）
    tradingVolumeAfterTest: data[12], // 盘后成交量（猜测）
  };
}

function parseStockTrends(indexCode, d) {
  let data = d.split(',');
  return {
    indexCode: indexCode,
    time: data[0],
    avgPrice: data[1], // 分钟内平均价？瞬时价格？怎么聚合？
    price: data[2], // 分钟现价(指数值)，聚合方式为取details最新记录的price值
    highestPrice: data[3], // 分钟内最高成交价
    lowestPrice: data[4], // 分钟内最低成交价
    volume: data[5], // 现手
    totalPrice: data[6], // 分钟内成交额
    totalVolume: data[11], // 当日累计成交手数
  };
}

function parseIndexDetail(indexCode, d) {
  let data = d.split(',');
  return {
    indexCode: indexCode,
    time: data[0],
    price: data[1],
    volume: data[2],
  };
}

function parseStockDetail(stockCode, d) {
  let data = d.split(',');
  return {
    stockCode: stockCode,
    time: data[0],
    price: data[1],
    volume: data[2],
  };
}

module.exports = {
  parseKLine,
  parseStockTrends,
  parseIndexDetail,
  parseStockDetail
};
