'use strict';

const utils = require('../utils');
const crypto = require('crypto');

function parseNotice(r) {
  utils.Assert(r.title == r.title_ch);
  // 对于columns超过1个元素的，把里面'其他'类型的的记录删掉
  // if (r.columns.length > 1) {
  //   r.columns = r.columns.filter(r => r.column_name != '其他');
  // }

  if (r.columns.length > 3) {
    console.warn(r);
  }
  // utils.Assert(r.columns.length <= 3 && r.columns.length > 0);
  // utils.Assert(r.columns.length <= 4 && r.columns.length > 0); // 600656
  // utils.Assert(r.columns[0].column_code == '001003001001003');
  utils.Assert(r.codes.length == 1);

  let type2 = null;
  let type3 = null;
  if (r.columns.length > 1) {
    type2 = r.columns[1].column_name;
  }
  if (r.columns.length > 2) {
    type3 = r.columns[2].column_name;
  }

  return {
    title: r.title,
    artCode: r.art_code,
    noticeType1: r.columns[0].column_name,
    noticeType2: type2,
    noticeType3: type3,
    noticeDate: r.notice_date.slice(0, 10),
    stockCode: r.codes[0].stock_code,
  };
}

function parseNoticeContent(d) {
  utils.Assert(d.attach_url == d.attach_url_web);
  utils.Assert(d.attach_list.length == 1);

  return {
    text: d.notice_content,
    pdfUrl: d.attach_url,
    pdfSize: d.attach_size,
    textPageSize: d.page_size,
  };
}

module.exports = {
  parseNotice,
  parseNoticeContent
};
