'use strict';

// 东方财富网 > 数据中心 > 新股数据 > 宁德时代
// https://data.eastmoney.com/xg/xg/detail/300750.html
// 个股新股发行详细资料页面，包括财务数据等
// 几个不同内容的请求例子:
// 其中发现的几个字段数值含义:
//   TOTAL_RAISE_FUNDS = FREE_MARKET_CAP，可以assert一下看看有没有不等的情况
//   FREE_MARKET_CAP: 发行后市值，应该就是IPO新股和发行价的乘积。流通市值（宁德时代54.62亿）
//   TOTAL_MARKET_CAP: 总市值，包括非流通市值（比如宁德时代546.15亿）
//   PE_RATIO_AFTER: 发行市盈率
//   INDUSTRY_PE_RATIO: 参考行业市盈率（最新）
//   PE_RATIO_NEWEST: 该股的动态市盈率（最新）
//
//   FREECAP_AVG_LOWER: 网下申购需配市值
//   OFFLINE_PLACING_DATE: 网上申购日（网上发行日期）
//   OFFLINE_VAP_RATIO: 网下配售中签率(%)
//   FREECAP_AVG_DATE: 网下申购市值确认日？融资首次公告日？
//   BALLOT_NOTICE_DATE: 中签缴款日？中签号公布日期？中签结果公告日期？断言是否不相等情况
//   OFFLINE_VAS_MULTIPLE: 网下配售认购倍数
//   ONLINE_VA_NUM: 网上有效申购户数（户）
//   ONLINE_VA_SHARES: 网上有效申购股数（股）
//   OFFLINE_VAP_OBJECT: 网下有效申购户数（户）
//   OFFLINE_VATS: 网下有效申购股数（股）
//   ISSUE_WAY: 发行方式类型
//   ISSUE_WAY_EXPLAIN: 发行方式说明
//   TOTAL_APPLY_NUM: 初步询价累计报价股数（万股）
//
//   ISSUE_PRICE: 发行价格（元/股）
//   ISSUE_NUM? : 先用TOTAL_RAISE_FUNDS / ISSUE_PRICE，断言是否整数
//     - 发现一部分信息在html的info数组里面。。。见getIPO_Type1
//
//   PLACING_NUM: 网下配售数量（股）
//
//   页面的几部分划分：发行状况；发行方式；申购状况；行业可对比情况；中签号；承销商；首日表现；
function parseIPO(d, pageType) {
  let issueDate = d.ISSUE_DATE;
  if (d.ISSUE_DATE) {
    issueDate = d.ISSUE_DATE.slice(0, 10);
  }
  let listingDate = d.LISTING_DATE;
  if (d.LISTING_DATE) {
    listingDate = d.LISTING_DATE.slice(0, 10);
  }

  return {
    fromPageType: pageType,
    stockCode: d.SECURITY_CODE,
    recordType: '0',
    issueNum: d.TOTAL_ISSUE_NUM,  // 总发行梳理（股）
    onlineIssueNum: d.ONLINE_ISSUE_NUM, // 网上发行数量（股）
    offlinePlacingNum: d.OFFLINE_PLACING_NUM, // 网下配售数量（股）
    issuePrice: d.ISSUE_PRICE,
    issueDate: issueDate,
    issueListingDate: listingDate,
    lockinPeriod: '',
    issueWay: d.ISSUE_WAY,
  };
}

// 东方财富网 > 数据中心 > 新股数据 > 增发  
// https://data.eastmoney.com/other/gkzf.html
// Get Secondary Public Offering
// NOTE: 因为没法过滤stockCode，全量记录大概5617条
function parseSPO(d) {
  let issueDate = d.ISSUE_DATE;
  if (d.ISSUE_DATE) {
    issueDate = d.ISSUE_DATE.slice(0, 10);
  }
  let listingDate = d.ISSUE_LISTING_DATE;
  if (d.LISTING_DATE) {
    listingDate = d.ISSUE_LISTING_DATE.slice(0, 10);
  }
  return {
    stockCode: d.SECURITY_CODE,
    recordType: d.SEO_TYPE, // 1:定向增发；2:公开增发
    issueNum: d.ISSUE_NUM, // 发行总数（股）
    issuePrice: d.ISSUE_PRICE, // 发行价格（元）
    newPrice: d.NEW_PRICE, // 最新价
    issueDate: issueDate, // 发行日期。不知道为什么发现一些是没有上市日期的
    issueListingDate: listingDate, // 增发上市日期。这里同样也有一堆空值。而且issueDate和listingDate都不是同时为空。
    lockinPeriod: d.LOCKIN_PERIOD, // 锁定期
    corRecode: d.CORRECODE, // 增发代码
    otherOnlineVss: d.OTHER_ONLINE_VSS, // 网上发行（股）

    // https://data.eastmoney.com/other/gkzf.html
    // 表格里面没有展示的信息字段
    issueWay: d.ISSUE_WAY, // 发行方式: 网上优先配置；
  };
}

module.exports = {
  parseIPO,
  parseSPO
};
