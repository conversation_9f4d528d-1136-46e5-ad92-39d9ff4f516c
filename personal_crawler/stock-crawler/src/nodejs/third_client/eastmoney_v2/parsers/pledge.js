'use strict';

function parsePledge(stockCode, d) {
  let pledgeUnfreezeDate = null;
  if (d.ACTUAL_UNFREEZE_DATE) {
    pledgeUnfreezeDate = d.ACTUAL_UNFREEZE_DATE.slice(0, 10);
  }
  return {
    stockCode: stockCode,
    holderName: d.HOLDER_NAME, // 股东名称
    pledgeNum: d.PF_NUM, // 质押股份数量（股）
    pledgeMarketCap: d.MARKET_CAP, // 质押股份市值（元）
    holdRatio: d.PF_HOLD_RATIO, // 占所持股份比例（%）
    pfTsr: d.PF_TSR, // 占总股本比例（%）
    pfOrg: d.PF_ORG, // 质押机构
    pfReason: d.PF_REASON, // 质押原因 
    pfPurpose: d.PF_PURPOSE, // 质押目的
    closePrice: d.CLOSE_FORWARD_ADJPRICE, // 质押日收盘价（元）
    warningLine: d.WARNING_LINE, // 预警线（估算）
    openLine: d.OPENLINE, // 平仓线
    pledgeStartDate: d.PF_START_DATE.slice(0, 10), // 质押开始日期
    pledgeUnfreezeDate: pledgeUnfreezeDate, // 质押解除日期
    unfreezeState: d.UNFREEZE_STATE, // 状态
    noticeDate: d.NOTICE_DATE.slice(0, 10), // 公告日期
  };
}

module.exports = {
  parsePledge 
};
