'use strict';

function parseShareInfo(stockCode, obj) {
  // console.log('-- obj: ', obj);
  // utils.Assert(obj.END_DATE == obj.LISTING_DATE); // 加了断言有时并不满足。但是web页面展示的日期看起来是END_DATE

  // 暂时只用到股本
  return {
    stockCode: stockCode,
    stockName: obj.SECURITY_NAME_ABBR,
    // app上展示的字段（资料=>股东股本=>股本变动）
    limitedShares: obj.LIMITED_SHARES, // 流通受限股
    unlimitedShares: obj.UNLIMITED_SHARES, // 已流通股
    totalShares: obj.TOTAL_SHARES, // 总股本
    listedAShares: obj.LISTED_A_SHARES, // 流通A股（已上市A股）
    limitedAShares: obj.LIMITED_A_SHARES, // 限售A股

    // B股相关
    bFreeShares: obj.B_FREE_SHARE, // 已流通B股
    hFreeShares: obj.H_FREE_SHARE, // 已流通H股

    // 变动数额（趋势展示）
    limitedSharesChange: obj.LIMITED_SHARES_CHANGE, // 流通受限股变动
    unlimitedSharesChange: obj.UNLIMITED_SHARES_CHANGE, // 已流通股变动
    listedASharesChange: obj.LISTED_ASHARES_CHANGE, // 流通A股变动
    limitedASharesChange: obj.LIMITED_ASHARES_CHANGE, // 限售A股变动

    endDate: obj.END_DATE.slice(0, 10), // web上展示的日期
    noticeDate: obj.NOTICE_DATE.slice(0, 10), // 公告日期
    listingDate: obj.LISTING_DATE.slice(0, 10), // 股本变更的上市日期
    changeReason: obj.CHANGE_REASON, // 股本变动原因
    changeReasonExplain: obj.CHANGE_REASON_EXPLAIN, // 变更原因解释

    // 其他
    nonFreeShares: obj.NON_FREE_SHARES, // 未流通股份
    limitedStateShares: obj.LIMITED_STATE_SHARES, // 国家持股（受限）；比如600656，web页面股本结构=>历年股本变动
    limitedOthars: obj.LIMITED_OTHARS, // 其他内资持股（受限）；同上
    limitedDomesticNostate: obj.LIMITED_DOMESTIC_NOSTATE, // 境内法人持股（受限）

    otherFreeShares: obj.OTHER_FREE_SHARES, // 已流通股 = 流通A股+境外流通股+其他流通股；目前定位到是D股上市

    crawlerDate: utils.FormatDate(new Date()),
    // .... 还有很多字段，需要再取
  };
}

module.exports = {
  parseShareInfo
};
