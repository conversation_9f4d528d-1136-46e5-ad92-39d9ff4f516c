'use strict';

function parseCompanyEvent(logger, stockCode, d) {
  const md5 = crypto.createHash('md5');
  let level2Content = '';
  if (d.LEVEL2_CONTENT) {
    level2Content = JSON.stringify(d.LEVEL2_CONTENT);
  }
  return {
    stockCode: stockCode,
    eventType: d.EVENT_TYPE,
    specificEventType: d.SPECIFIC_EVENTTYPE,
    noticeDate: d.NOTICE_DATE,
    level1ContentMd5: md5.update(d.LEVEL1_CONTENT).digest('hex'),
    level1Content: d.LEVEL1_CONTENT,
    level2Content: level2Content,
  };
}

function convertToStockResp(body) {
  let rows = [];
  for (let d of body) {
    rows.push({
      stockCode: d.code,
      stockName: d.name, 
      stockState: d.state,
    })
  }
  return {total: rows.length, rows: rows};
}

function convertToIndexResp(body) {
  let rows = [];
  for (let d of body) {
    rows.push({
      indexCode: d.code,
      indexName: d.name,
    })
  }
  return {total: rows.length, rows: rows};
}

module.exports = {
  parseCompanyEvent,
  convertToStockResp, 
  convertToIndexResp
};
