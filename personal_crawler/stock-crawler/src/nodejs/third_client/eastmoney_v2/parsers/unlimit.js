'use strict';

function parseUnlimitStock(d) {
  return {
    stockCode: d.SECURITY_CODE,
    addListingShares: d.ADD_LISTING_SHARES,
    freeDate: d.FREE_DATE.slice(0, 10),
    freeSharesType: d.FREE_SHARES_TYPE,
    liftHolderAll: d.LIFT_HOLDER_ALL,
    liftSharesAll: d.LIFT_SHARES_ALL,
    limitedHolderName: d.LIMITED_HOLDER_NAME,
    limitedType: d.LIMITED_TYPE,
    orgCode: d.ORG_CODE,
    planFeature: d.PLAN_FEATURE,
  };
}

module.exports = {
  parseUnlimitStock
};
