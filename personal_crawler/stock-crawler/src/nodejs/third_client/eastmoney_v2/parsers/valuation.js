'use strict';

/**
 * 页面: https://data.eastmoney.com/gzfx/scgk.html
 * 解析市场整体估值数据，包括:
 * 1. 000300/沪深两市(沪深300）
 * 2. 000001/沪市主板（上证指数）
 * 3. 000688/科创板（科创50）
 * 4. 399001/深市主板（深证指数）
 * 5. 399006/创业板（创业板指）
 */ 
function parseMarketValuation(d) {
  return {
    changeRate: d.CHANGE_RATE, // 涨跌幅
    closePrice: d.CLOSE_PRICE, // 沪深300
    freeMarketCap: d.FREE_MARKET_CAP, // 流通市值（元）
    freeShares: d.FREE_SHARES, // 流通股本（股）
    listingOrgNum: d.LISTING_ORG_NUM, // 个股总数
    peTtmAvg: d.PE_TTM_AVG, // 平均市盈率
    // : d.SECURITY_INNER_CODE, // "1000157500"
    totalMarketCap: d.TOTAL_MARKET_CAP, // 总市值（元）
    totalShares: d.TOTAL_SHARES, // 总股本（股）
    tradeDate: d.TRADE_DATE.slice(0, 10), // 交易日期
    tradeMarketCode: d.TRADE_MARKET_CODE, // 交易市场代码:
      // 1. 000300/沪深两市(沪深300？)；
      // 2. 000001/沪市主板（上证指数）
      // 3. 000688/科创板（科创50）
      // 4. 399001/深市主板（深证指数）
      // 5. 399006/创业板（创业板指）
  };
}

/**
 * 页面: https://data.eastmoney.com/gzfx/hy.html
 * 解析行业估值数据，包括:
 * - BOARD_CODE/BOARD_NAME: 行业板块ID和名称
 * - MARKET_CAP_VAG: 平均市值
 * - NOMARKETCAP_A_VAG: 平均流通市值
 * - FREE_SHARES_VAG: 平均流通股数
 * - LOSS_COUNT: 亏损家数
 * 
 * 其他返回字段:
 * - AVG_MARKET_CAP_FREE: 
 * - AVG_MARKET_CAP_TOTAL:
 * - AVG_PE_DYNAMIC:
 * - AVG_PE_STATIC:
 * - AVG_PB:
 * - TRADE_DATE:
 * - INDUSTRY_CODE:
 * - REPORT_TYPE:
 */
function parseIndustryValuation(d) {
  return {
    tradeDate: d.TRADE_DATE.slice(0, 10), // 交易日期
    boardCode: d.BOARD_CODE, // 行业/板块代码
    boardName: d.BOARD_NAME, // 行业/板块名称
    freeSharesVag: d.FREE_SHARES_VAG, // 平均流通股本（股）
    lossCount: d.LOSS_COUNT, // 亏损家数
    avgMarketCap: d.MARKET_CAP_VAG, // 平均市值
    avgNoMarketCap: d.NOMARKETCAP_A_VAG, // 平均流通市值（元）
    // nolimitedMarketCap: d.NOTLIMITED_MARKETCAP_A, // 
  };
}

/**
 * 页面: https://data.eastmoney.com/gzfx/detail/300750.html
 * 解析个股估值数据，包括:
 * - SECURITY_CODE/BOARD_CODE/BOARD_NAME: 代码、板块相关
 * - TOTAL_MARKET_CAP/NOTLIMITED_MARKETCAP_A: 总市值/流通市值
 * - TOTAL_SHARES/FREE_SHARES_A: 总股本/流通股本
 * - PE_TTM/PE_LAR: 动态市盈率/静态市盈率
 * - PB_MRQ: 市净率
 * - PEG_CAR: PEG估值
 * - PCF_OCF_LAR/PCF_OCF_TTM: 市现率(经营/动态)
 * - PS_TTM: 市销率
 * 
 * 其他重要返回字段:
 * - TRADE_MARKET: 
 * - LISTING_DATE:
 * - END_DATE:
 * - SECURITY_TYPE_CODE:
 * - SECURITY_TYPE:
 */
function parseStockValuation(d) {
  return {
    stockCode: d.SECURITY_CODE,
    boardCode: d.BOARD_CODE,
    boardName: d.BOARD_NAME,
    totalMarketCap: d.TOTAL_MARKET_CAP,
    notlimitedMarketCapA: d.NOTLIMITED_MARKETCAP_A,
    closePrice: d.CLOSE_PRICE, 
    changeRate: d.CHANGE_RATE, // 涨跌幅
    totalShares: d.TOTAL_SHARES,
    freeSharesA: d.FREE_SHARES_A,
    peTtm: d.PE_TTM, // 动态市盈率
    peLar: d.PE_LAR, // 静态市盈率
    pbMrq: d.PB_MRQ, // 市净率
    pegCar: d.PEG_CAR, // PEG估值
    pcfOcfLar: d.PCF_OCF_LAR, // 市现率(经营)
    pcfOcfTtm: d.PCF_OCF_TTM, // 市现率(动态)
    psTtm: d.PS_TTM, // 市销率
    tradeDate: d.TRADE_DATE.slice(0, 10), // 交易日期

    // 其他未使用字段:
    // TRADE_MARKET:
    // LISTING_DATE: 
    // END_DATE:
    // SECURITY_TYPE_CODE:
    // SECURITY_TYPE:
    // AVG_MARKET_CAP:
    // AVG_MARKET_CAP_FREE:
    // AVG_TOTAL_MARKET_VALUE:
    // TRADE_MARKET_CODE: 
  };
}

module.exports = {
  parseMarketValuation,
  parseIndustryValuation,
  parseStockValuation
};
