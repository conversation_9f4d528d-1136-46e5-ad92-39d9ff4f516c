'use strict';

const crypto = require('crypto');
const utils = require('../../../nodejs-common/utils.js');

/**
 * 生成MD5哈希值
 * NOTE: 主要用于生存公告内容、历史K线等大文本的哈希值，用于比较内容是否变化
 */
function generateHash(data) {
  const md5 = crypto.createHash('md5');
  return md5.update(data).digest('hex');
}

/**
 * 格式化日期为YYYY-MM-DD
 * NOTE: 处理各种不规范的日期格式:
 * 1. 带时间的日期字符串需要截取前10位
 * 2. 中文日期格式需要转换
 * 3. 支持Date对象入参
 */
function formatDate(date) {
  if (typeof date === 'string') {
    if (date.length > 10) {
      date = date.slice(0, 10);
    }
    if (date.includes('年')) {
      return utils.ParseChineseDate(date);
    }
    return date;
  }
  return utils.FormatDate(date);
}

function assert(condition, message) {
  if (!condition) {
    throw new Error(message || "Assertion failed");  
  }
}

function assertEqual(a, b, message) {
  assert(a === b, message || `${a} !== ${b}`);
}

function parseNumberComma(str) {
  return parseInt(str.replace(/,/g, ''));
}

function addDays(date, days) {
  let result = new Date(date);
  result.setDate(result.getDate() + days);
  return formatDate(result);
}

function parseChineseDate(str) {
  const match = str.match(/(\d+)年(\d+)月(\d+)日/);
  if (!match) {
    throw new Error(`Invalid date format: ${str}`);
  }
  return `${match[1]}-${String(match[2]).padStart(2, '0')}-${String(match[3]).padStart(2, '0')}`;
}

/**
 * 解析数字，处理各种情况:
 * 1. 带逗号分隔的数字字符串 '1,234.56'
 * 2. 百分比字符串 '12.34%'
 * 3. 带单位的数字 '123万'
 * 4. 科学计数法 '1.23e+4'
 */
function parseNumber(str) {
  if (!str || typeof str !== 'string') {
    return null;
  }
  
  if (str.endsWith('%')) {
    str = str.slice(0, -1);
    let num = parseFloat(str);
    return isNaN(num) ? null : num / 100;
  }
  
  if (str.endsWith('万')) {
    str = str.slice(0, -1);
    let num = parseFloat(str);
    return isNaN(num) ? null : num * 10000;
  }
  if (str.endsWith('亿')) {
    str = str.slice(0, -1); 
    let num = parseFloat(str);
    return isNaN(num) ? null : num * 100000000;
  }
  
  str = str.replace(/,/g, '');
  let num = parseFloat(str);
  return isNaN(num) ? null : num;
}

// 导出所有工具方法
module.exports = {
  // getLastTradeDate, // 删除此方法
  generateHash,
  formatDate,
  parseNumber,
  assert,
  assertEqual,
  parseNumberComma,
  addDays, 
  parseChineseDate,
  
  // 保持与nodejs-common兼容的方法名
  AssertEqual: utils.AssertEqual,
  ParseNumberComma: utils.ParseNumberComma,
  ParseChineseDate: utils.ParseChineseDate,
  AddDays: utils.AddDays,
  FormatDate: utils.FormatDate,
  
  // 常用的正则表达式
  RegexPattern: {
    NUMBER_COMMA: /^[0-9,]+$/,
    CHINESE_DATE: /^(\d{4})年(\d{1,2})月(\d{1,2})日$/,
    DATE_DASH: /^\d{4}-\d{1,2}-\d{1,2}$/,
    NUMBER_UNIT: /^-?\d+\.?\d*[万亿]?$/,
    NUMBER_PERCENT: /^-?\d+\.?\d*%$/,
    
    STOCK_CODE_SH: /^6\d{5}$/,
    STOCK_CODE_SZ: /^(00|30|20)\d{4}$/,
    STOCK_CODE_BJ: /^(83|87|43)\d{4}$/,
  }
};
