'use strict';

const qs = require('querystring');
const request = require('request-promise');
const cheerio = require('cheerio');
const iconv = require('iconv-lite');

class Sina {
  constructor() {
  }

  // 最新成分页面
  genNewestUrl(code, page) {
    if (page <= 1) {
      return `https://vip.stock.finance.sina.com.cn/corp/go.php/vII_NewestComponent/indexid/${code}.phtml`;
    } else {
      // 其实page也适用于首页，page=1
      return `https://vip.stock.finance.sina.com.cn/corp/view/vII_NewestComponent.php?page=${page}&indexid=${code}`;
    }
  }

  // 历史成分页面
  genHistoryUrl(code, page) {
    if (page <= 1) {
      return `https://vip.stock.finance.sina.com.cn/corp/go.php/vII_HistoryComponent/indexid/${code}.phtml`;
    } else {
      // 其实page也适用于首页，page=1
      return `https://vip.stock.finance.sina.com.cn/corp/view/vII_HistoryComponent.php?page=${page}&indexid=${code}`;
    }
  }

  async getIndexNewComponentsByPage(logger, indexCode, page) {
    let rows = [];

    let url = this.genNewestUrl(indexCode, page);
    let body = await request.get(url, {encoding: null});
    body = iconv.decode(body, 'gb2312');
    const $ = cheerio.load(body);
    let data = $('tbody tr');
    data.map((i, item) => {
      let tds = $(item).find("td");
      if (tds.length != 3) {
        return
      }
      let stockCode = $(tds.get(0)).text();
      if (stockCode == '品种代码') {
        return
      }

      rows.push({
        stockCode: stockCode,
        stockName: $(tds.get(1)).text(),
        addTime: $(tds.get(2)).text(),
        delTime: '', // 不设空值，没法作为unique key
      });
    });

    // logger.debug(rows, rows.length);
    return rows;
  }

  async getIndexHistoryComponentsByPage(logger, indexCode, page) {
    let rows = [];

    let url = this.genHistoryUrl(indexCode, page);
    let body = await request.get(url, {encoding: null});
    body = iconv.decode(body, 'gb2312');
    const $ = cheerio.load(body);
    let data = $('tbody tr');
    data.map((i, item) => {
      let tds = $(item).find("td");
      if (tds.length != 4) {
        return
      }
      let stockCode = $(tds.get(0)).text();
      if (stockCode == '品种代码') {
        return
      }

      rows.push({
        stockCode: stockCode,
        stockName: $(tds.get(1)).text(),
        addTime: $(tds.get(2)).text(),
        delTime: $(tds.get(3)).text(),
      });
    });

    // logger.debug(rows, rows.length);
    return rows;
  }

  isSameRecord(oldRecord, newRecord) {
    if (oldRecord.stockName != newRecord.stockName) {
      return false;
    }
    if (oldRecord.addTime != newRecord.addTime) {
      return false;
    }
    if (oldRecord.delTime != newRecord.delTime){
      return false;
    }
    return true;
  }

  isSamePage(oldPage, newPage) {
    // 这里的oldMap不能用stockCode，因为可能同时一个stockCode、不同addTime出现在一页里。比如399928指数里的002267
    let oldMap = {};
    oldPage.forEach(item => oldMap[item.stockCode+item.addTime] = item);

    for (let i = 0; i < newPage.length; ++i) {
      let item = newPage[i];
      let key = item.stockCode + item.addTime;
      if (!oldMap[key]) {
        // 存在不一样的项
        return false;
      }

      if (!this.isSameRecord(oldMap[key], item)) {
        // 值不一样
        return false;
      }
    }
    return true;
  }

  async _getAllPageRecords(logger, indexCode, type) {
    let allRows = [];

    let fn;
    if (type == 'new') {
      fn = this.getIndexNewComponentsByPage;
    } else if (type == 'history') {
      fn = this.getIndexHistoryComponentsByPage;
    } else {
      throw new Error(`_getAllPageRecords 错误的type: ${type}`);
    }

    let lastPage = [];
    let i = 1; // 从第1页开始尝试获取
    for (;;) {
      let rows = await fn.call(this, logger, indexCode, i)
      logger.debug(`[sina] getPages[type=${type}] code[${indexCode}] page[${i}], get ${rows.length} records.`);
      if (!rows || rows.length == 0) {
        break;
      }
      // 如果只有一页数据的时候，page传什么参数都能拿到一样的数据，所以需要比对页面数据是否更新
      if (this.isSamePage(lastPage, rows)) {
        break;
      }

      allRows = allRows.concat(rows);
      lastPage = rows;
      i += 1;
      if (i > 200) {
        // 一页40，200页就是8000个记录，超出正常预期了
        throw new Error('_getAllPageRecords something wrong');
      }
    }

    // logger.debug(allRows, allRows.length);
    return allRows;
  }

  async getIndexNewComponents(logger, indexCode) {
    return this._getAllPageRecords(logger, indexCode, 'new')
  }

  async getIndexHistoryComponents(logger, indexCode) {
    return this._getAllPageRecords(logger, indexCode, 'history')
  }

  async getIndexComponents(logger, indexCode, indexName) {
    let finalRows = [];
    let newRecords = await this.getIndexNewComponents(logger, indexCode);
    let historyRecords = await this.getIndexHistoryComponents(logger, indexCode);

    // logger.debug('before', newRecords.length + historyRecords.length);

    // 合并与重复校验
    let uniqueMap = {};
    newRecords.concat(historyRecords).forEach(item => {
      item.indexCode = indexCode;
      item.indexName = indexName;

      if (!uniqueMap[item.stockCode]) {
        // 首次加入map
        uniqueMap[item.stockCode] = item;
        finalRows.push(item);
        return;
      }
      // 重复key
      let old = uniqueMap[item.stockCode];
      if (!this.isSameRecord(old, item)) {
        logger.warn(indexCode, 'old: ', old);
        logger.warn(indexCode, 'new: ', item);
        // throw new Error("getIndexComponents check failed");

        // NOTE 确实存在先后两次加入指数的情况，而且还不少
        // 比如000300指数成份股000100：TCL集团
        // 2005-04-08加入，2007-06-29剔除，2009-01-05又加入
        finalRows.push(item);
      }
    });

    // logger.debug('after', finalRows.length);
    // logger.debug(finalRows);
    return {
      rows: finalRows,
      total: finalRows.length,
    }
  }
};

let sina = new Sina();
// sina.getIndexNewComponents(console, '931151');
// sina.getIndexNewComponents(console, '000300');
// sina.getIndexHistoryComponents(console, '000300');
// sina.getIndexComponents(console, '000300');
// sina.getIndexComponents(console, '399973');
// sina.getIndexComponents(console, '399928');

module.exports = sina;
