
const qs = require('querystring');
const request = require('request-promise');
const cheerio = require('cheerio');
const iconv = require('iconv-lite');

/**
 * Shanghai Stock Exchange
 * 上交所接口数据: http://www.sse.com.cn/market/sseindex/indexlist/basic/index.shtml?COMPANY_CODE=000001&INDEX_Code=000001&type=1
 */
class Sse {
  constructor() {
  }

  async request(url) {
    let options = {
      url: url,
      method: 'GET',
      headers: {
        'Referer': 'http://www.sse.com.cn/'
      },
    };
    return request(options);
  }

  // 成份股数据
  async getIndexComponents(logger, indexCode, indexName) {
    // 页面: http://www.sse.com.cn/market/sseindex/indexlist/basic/index.shtml?COMPANY_CODE=000001&INDEX_Code=000001&type=12
    // 接口: http://query.sse.com.cn/commonSoaQuery.do?&jsonCallBack=jsonpCallback65344654&sqlId=DB_SZZSLB_CFGLB&indexCode=000001&isPagination=true&pageHelp.pageSize=60&pageHelp.beginPage=2&pageHelp.cacheSize=1&pageHelp.pageNo=2&pageHelp.endPage=2&_=1706444587044
    let url = 'http://query.sse.com.cn/commonSoaQuery.do';
    // let params = {
    //   sqlId: 'DB_SZZSLB_CFGLB',
    //   indexCode: indexCode,
    //   isPagination: true,
    //   'pageHelp.pageSize': 60,
    //   'pageHelp.beginPage': 2,
    //   'pageHelp.cacheSize': 1,
    //   'pageHelp.pageNo': 2,
    //   'pageHelp.endPage': 2,
    //   _: new Date().getTime(),
    // };
    let params = {
      sqlId: 'DB_SZZSLB_CFGLB',
      indexCode: indexCode,
      isPagination: false,
      _: new Date().getTime(),
    };

    url += '?' + qs.stringify(params);
    let body = await this.request(url);
    // logger.debug(`[GET] ${url}`)
    body = JSON.parse(body);
    logger.debug(`[sse] getPages code[${indexCode}], get ${body.result.length} records.`);

    // logger.debug(`body.result.length: ${body.result.length}`)
    // logger.debug(body.result);
    let rows = [];
    for (let r of body.result) {
      rows.push({
        indexCode, indexCode,
        indexName: indexName,
        stockCode: r.securityCode,
        stockName: r.securityAbbr,
        inDate: r.inDate,
      });
    }
    return {
      rows: rows,
      total: rows.length
    };
  }
}

let sse = new Sse();
async function test() {
  await sse.getIndexComponents(console, '000001')
}
// test();

module.exports = sse;
