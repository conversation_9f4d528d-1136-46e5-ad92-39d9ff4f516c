# base_downloader.py
import os
import re
import sys
import asyncio
import math
import time
import queue
import hashlib
import argparse
import threading
import MySQLdb
from MySQLdb import cursors, Error as DBError
from abc import ABC, abstractmethod
from typing import Iterator, Dict, List  
from python_common.report import notify

class BaseDownloader(ABC):
    def __init__(self, record_table: str):
        self.record_table = record_table
        self.batch_size = 1000  # 每批处理1000条记录
        self.max_retries = 3     # 最大重试次数
        self.retry_delay = 5     # 重试延迟(秒)
        
        # 数据库配置
        self.db_config = {
            'host': os.getenv('db_host'),
            'port': int(os.getenv('db_port', '3306')),
            'user': os.getenv('db_user'),
            'passwd': os.getenv('db_pass'),
            'db': os.getenv('db_name'),
            'charset': 'utf8mb4',
            'cursorclass': cursors.DictCursor  # 确保返回字典格式
        }
        
        # 字段白名单校验
        self.required_fields = {'id', 'stockCode', 'date'}

    def _get_connection(self):
        """获取数据库连接（带重试机制）"""
        for attempt in range(1, self.max_retries+1):
            try:
                return MySQLdb.connect(**self.db_config)
            except DBError as e:
                if attempt == self.max_retries:
                    raise
                print(f"数据库连接失败，第{attempt}次重试... ({e})")
                time.sleep(self.retry_delay * attempt)
    
    def _get_batch(self, last_id: int) -> List[Dict]:
        """安全获取批次数据"""
        conn = None
        try:
            conn = self._get_connection()
            with conn.cursor() as cursor:
                query = f"""
                    SELECT s.id, s.stockCode, s.date
                    FROM StockKLineDailies s
                    LEFT JOIN {self.record_table} r 
                        ON s.stockCode = r.stockCode 
                        AND s.date = r.date
                    WHERE r.stockCode IS NULL
                        AND s.id > %s
                    ORDER BY s.id ASC
                    LIMIT %s
                """
                cursor.execute(query, (last_id, self.batch_size))
                return cursor.fetchall()
        finally:
            if conn: conn.close()
 
    def _get_last_record(self) -> Dict:
        """安全获取批次数据"""
        conn = None
        try:
            conn = self._get_connection()
            with conn.cursor() as cursor:
                query = f"""
                    SELECT r.id, r.stockCode, r.date
                    FROM {self.record_table} r
                    ORDER BY r.id DESC
                    LIMIT 1
                """
                cursor.execute(query)
                return cursor.fetchone()
        finally:
            if conn: conn.close()

    def _get_start_id(self, record) -> Dict:
        """安全获取批次数据"""
        conn = None
        try:
            conn = self._get_connection()
            with conn.cursor() as cursor:
                print(f"last record: {record}")
                date=record['date']
                date=f"{date[0:4]}-{date[4:6]}-{date[6:8]}"
                print(f"date: {date}")
                stock=record['stockCode']

                query = f"""
                    SELECT s.id, s.stockCode, s.date
                    FROM StockKLineDailies s
                    WHERE s.stockCode = '{stock}'
                    AND s.date = '{date}'
                    LIMIT 1
                """
                cursor.execute(query)
                return cursor.fetchone()
        finally:
            if conn: conn.close()


    def _validate_row(self, row: Dict) -> bool:
        """验证数据行完整性"""
        if not isinstance(row, dict):
            print(f"无效数据格式: {type(row)}")
            return False
        missing = self.required_fields - row.keys()
        if missing:
            print(f"缺失必要字段: {missing}")
            return False
        return True

    def _clean_stock_code(self, raw_code: str) -> str:
        """清洗股票代码为6位数字"""
        try:
            cleaned = re.sub(r'\D', '', str(raw_code))[-6:].lstrip('0')
            return cleaned.zfill(6)  # 补零到6位
        except:
            return '000000'  # 异常情况返回默认值

    def _clean_date(self, raw_date: str) -> str:
        """清洗日期为8位数字"""
        try:
            return re.sub(r'\D', '', str(raw_date))[:8]
        except:
            return '19700101'  # 异常返回默认日期

    def fetch_tasks(self, max_line: int = None, start_id: int = 0) -> Iterator[Dict]:
        """生成器：分批次获取有效任务"""
        last_id = start_id
        total_yield = 0
        
        while True:
            try:
                batch = self._get_batch(last_id)
            except DBError as e:
                print(f"获取批次失败: {e}")
                break

            if not batch:
                break
                
            for row in batch:
                if not self._validate_row(row):
                    continue
                    
                # 数据清洗
                try:
                    task = {
                        'id': row['id'],
                        'stockCode': self._clean_stock_code(row['stockCode']),
                        'date': self._clean_date(row['date'])
                    }
                except Exception as e:
                    print(f"数据清洗失败: {e}\n原始数据: {row}")
                
                    continue
                
                    
                # 子类过滤（如盘口数据日期过滤）
                if self._should_skip_task(task):
                    continue
                    
                # 数量控制
                if max_line and total_yield >= max_line:
                    return
                    
                yield task
                total_yield += 1
                
            last_id = batch[-1]['id']
            
            # 提前终止条件
            if len(batch) < self.batch_size:
                break

    def _should_skip_task(self, task: Dict) -> bool:
        """供子类重写的任务过滤方法"""
        return False

    def get_output_path(self, stock_code: str, date: str, output_root: str) -> tuple:
        """生成标准存储路径"""
        stock_dir = os.path.join(output_root, stock_code)
        filename = f"{stock_code}.{date}.dat"
        return stock_dir, os.path.join(stock_dir, filename)

    def _calculate_md5(self, file_path: str) -> str:
                
        """计算MD5校验和"""
                
        md5 = hashlib.md5()
                
        with open(file_path, 'rb') as f:
            while chunk := f.read(8192):
                md5.update(chunk)
        return md5.hexdigest()

    def _calculate_entropy(self, file_path: str) -> float:
        """计算文件熵值（0-8）"""
        try:
            freq = [0] * 256
            total = 0
            
            with open(file_path, 'rb') as f:
                while chunk := f.read(4096):
                    for byte in chunk:
                        freq[byte] += 1
                    total += len(chunk)
            
            if total == 0:
                return 0.0
                
            entropy = 0.0
            for count in freq:
                if count == 0:
                    continue
                p = count / total
                entropy -= p * math.log2(p)
                
            return round(entropy, 4)
        except Exception as e:
            raise RuntimeError(f"熵值计算失败: {e}")

    def _calculate_file_properties(self, file_path: str) -> dict:
        """计算文件属性并解析数据"""
        try:
            props = {
                'size': os.path.getsize(file_path),
                'md5': self._calculate_md5(file_path),
                'entropy': self._calculate_entropy(file_path)
            }
            # 新增解析逻辑
            parsed_data = self.parse_dat_file(file_path)
            if parsed_data['parsedSize'] != props['size']:
                parsed_date['downloadStatus'] = 'PASRED_SIZE_ERROR'

            props.update(parsed_data)
            return props
        except Exception as e:
            print(f"文件属性计算失败: {e}")
            return {'size': 0, 'md5': None, 'entropy': None}

    @abstractmethod
    def parse_dat_file(self, file_path: str) -> dict:
        """解析dat文件的具体实现"""
        return {}

    def save_record(self, record_data: dict):
        """更新插入记录的完整SQL语句"""
        upsert_sql = f"""
        INSERT INTO {self.record_table} (
            stockCode, date, downloadPath, downloadStatus,
            datSize, md5, datEntropy, parsedSize, parsedDate, decompressAlgo
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE
            downloadPath = VALUES(downloadPath),
            downloadStatus = VALUES(downloadStatus),
            datSize = VALUES(datSize),
            md5 = VALUES(md5),
            datEntropy = VALUES(datEntropy),
            parsedSize = VALUES(parsedSize),
            parsedDate = VALUES(parsedDate),
            decompressAlgo = VALUES(decompressAlgo),
            updated_at = CURRENT_TIMESTAMP
        """  # 删除占位符文本
        
        # 调试日志：打印传入的record_data
        # print(f"DEBUG: record_data={record_data}")

        params = (
            record_data['stockCode'],
            record_data['date'],
            record_data.get('downloadPath', ''),
            record_data.get('downloadStatus', 'PENDING'),
            record_data.get('datSize', 0),
            record_data.get('md5'),
            record_data.get('datEntropy'),
            record_data.get('parsedSize', 0),
            record_data.get('parsedDate', 0),
            record_data.get('decompressAlgo')
        )
            
        conn = None
        try:
            conn = self._get_connection()
            with conn.cursor() as cursor:
                cursor.execute(upsert_sql, params)
                conn.commit()
        except DBError as e:
            print(f"数据库操作失败: {e}")
            if conn: conn.rollback()
        finally:
            if conn: conn.close()

    @abstractmethod
    def perform_download(self, stock_code: str, date: str, target_path: str):
        """子类必须实现的下载方法"""
        pass

    def _process_task(self, task: Dict, output_dir: str):
        """处理单个下载任务（线程安全版本）"""
        record = {
            'stockCode': task['stockCode'],
            'date': task['date'],
            'downloadStatus': 'PENDING'
        }
        
        target_path = None
        try:
            # 生成存储路径
            stock_dir, target_path = self.get_output_path(
                task['stockCode'],
                task['date'],
                output_dir
            )
            os.makedirs(stock_dir, exist_ok=True)
            
            # 检查是否需要跳过下载
            if self.should_skip_download(task['stockCode'], task['date'], target_path):
                record['downloadStatus'] = 'SKIPPED'
            else:
                # 执行下载
                self.perform_download(task['stockCode'], task['date'], target_path)
                
            # 计算文件属性
            props = self._calculate_file_properties(target_path)
            record.update({
                'downloadPath': target_path,
                'downloadStatus': props.get('downloadStatus', 'SUCCESS'),
                'datSize': props['size'],
                'md5': props['md5'],
                'datEntropy': props['entropy'],
                'parsedSize': props['parsedSize'],
                'parsedDate': props['parsedDate'],
                'decompressAlgo': props['decompressAlgo'],
            })
                
        except Exception as e:
            record['downloadStatus'] = f'ERROR: {str(e)}'
            # 清理无效文件
            if target_path and os.path.exists(target_path):
                try:
                    os.remove(target_path)
                except:
                    pass
        finally:
            self.save_record(record)

    def _worker(self, task_queue: queue.Queue, output_dir: str):
        """工作线程执行循环"""
        while True:
            task = task_queue.get()  # 阻塞式获取
            if task is None:  # 终止信号
                task_queue.task_done()
                break
            try:
                self._process_task(task, output_dir)
            finally:
                task_queue.task_done()

    def run(self):
        """主运行流程"""
        parser = argparse.ArgumentParser(description='数据下载工具')
        parser.add_argument('output_dir', help='输出目录路径')
        parser.add_argument('--max-line', type=int, help='最大处理记录数')
        parser.add_argument('--start-id', type=int, help='StockKLineDailies里的起始ID')
        parser.add_argument('--concurrency', type=int, default=1, 
                        help='并发线程数 (默认: 1)')
        args = parser.parse_args()
        if args.start_id == None:
            # 不传start_id默认从表里获取最新记录
            lastrecord = self._get_last_record()
            last = self._get_start_id(lastrecord)
            args.start_id = last['id']
            print(f"start_id {args.start_id}")
        
        # 初始化输出目录
        os.makedirs(args.output_dir, exist_ok=True)

        # 创建任务队列
        task_queue = queue.Queue(maxsize=args.concurrency * 2)
        
        # 下载计数器
        download_stats = {
            'total': 0,
            'success': 0,
            'skipped': 0,
            'failed': 0
        }
        
        # 生产者线程：填充任务队列
        def producer():
            count = 0
            for task in self.fetch_tasks(args.max_line, args.start_id):
                print(f"生产任务: {task['stockCode']} {task['date']}")
                task_queue.put(task)
                count += 1
                download_stats['total'] = count
            print(f"共生成{count}个任务")
        
        # 修改工作线程以更新计数器
        def _worker_with_stats(task_queue: queue.Queue, output_dir: str):
            """带统计功能的工作线程"""
            while True:
                task = task_queue.get()
                if task is None:
                    task_queue.task_done()
                    break
                try:
                    # 原处理逻辑
                    record = {
                        'stockCode': task['stockCode'],
                        'date': task['date'],
                        'downloadStatus': 'PENDING'
                    }
                    
                    target_path = None
                    try:
                        stock_dir, target_path = self.get_output_path(
                            task['stockCode'],
                            task['date'],
                            output_dir
                        )
                        os.makedirs(stock_dir, exist_ok=True)
                        
                        if self.should_skip_download(task['stockCode'], task['date'], target_path):
                            record['downloadStatus'] = 'SKIPPED'
                            download_stats['skipped'] += 1
                        else:
                            self.perform_download(task['stockCode'], task['date'], target_path)
                        
                        props = self._calculate_file_properties(target_path)
                        record.update({
                            'downloadPath': target_path,
                            'downloadStatus': props.get('downloadStatus', 'SUCCESS'),
                            'datSize': props['size'],
                            'md5': props['md5'],
                            'datEntropy': props['entropy'],
                            'parsedSize': props['parsedSize'],
                            'parsedDate': props['parsedDate'],
                            'decompressAlgo': props['decompressAlgo'],
                        })
                        
                        if record['downloadStatus'] == 'SUCCESS':
                            download_stats['success'] += 1
                            
                    except Exception as e:
                        record['downloadStatus'] = f'ERROR: {str(e)}'
                        download_stats['failed'] += 1
                        if target_path and os.path.exists(target_path):
                            try:
                                os.remove(target_path)
                            except:
                                pass
                    finally:
                        self.save_record(record)
                        
                finally:
                    task_queue.task_done()
        
        # 启动生产者线程
        producer_thread = threading.Thread(target=producer)
        producer_thread.start()
        
        # 创建工作线程
        workers = []
        for _ in range(args.concurrency):
            t = threading.Thread(
                target=_worker_with_stats,
                args=(task_queue, args.output_dir)
            )
            t.start()
            workers.append(t)
        
        # 等待生产者完成
        producer_thread.join()
        
        # 等待所有任务完成
        task_queue.join()
        
        # 停止工作线程
        for _ in workers:
            task_queue.put(None)
        for t in workers:
            t.join()

        # 发送通知
        self._send_notification(download_stats)
        print(f"所有任务处理完成. 统计: {download_stats}")

    def _send_notification(self, stats: dict):
        """发送通知消息"""
        from python_common.report.notify import notify
        task_name = self.task_name
        message = (
            f"下载任务完成\n"
            f"    总计: {stats['total']}\n"
            f"    成功: {stats['success']}\n"
            f"    跳过: {stats['skipped']}\n"
            f"    失败: {stats['failed']}"
        )
        print(f"发送通知:\n{message}")
        try:
            asyncio.run(notify(task_name, "<font color=\"green\">成功</font>", message))
        except Exception as e:
            print(f"发送通知失败: {e}")
            asyncio.run(notify(task_name, "<font color=\"green\">失败</font>", str(e)))


    def should_skip_download(self, stock_code: str, date: str, target_path: str) -> bool:
        """检查是否需要跳过下载"""
        # 检查文件是否存在
        if not os.path.exists(target_path):
            return False  # 文件不存在，需要下载
        
        try:
            # 获取文件真实大小
            file_size = os.path.getsize(target_path)
            
            # 解析文件，获取 parsedSize
            props = self._calculate_file_properties(target_path)
            parsed_size = props.get('parsedSize', -1)
            
            # 判断是否需要跳过
            if file_size == parsed_size:
                print(f"文件已存在且大小匹配，跳过下载: {target_path}")
                return True
        except Exception as e:
            print(f"检查文件大小和解析失败: {e}")
        
        return False  # 需要下载
