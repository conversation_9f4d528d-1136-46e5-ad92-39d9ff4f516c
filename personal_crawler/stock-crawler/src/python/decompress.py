"""
解压工具：从数据库读取已下载的 dat 文件，按算法解压后存储到指定目录
用法：python decompress.py --type=[fenshi|pankou] [输出目录]
"""
import os
import sys
import math
import zlib
import time
import lzma
import argparse
from typing import Dict, List
import MySQLdb
from MySQLdb import cursors, Error as DBError

class Decompressor:
    def __init__(self, db_config: Dict, table_name: str):
        self.db_config = db_config
        self.table_name = table_name
        self.batch_size = 500  # 每批处理500条记录

    def _get_connection(self):
        """获取数据库连接"""
        return MySQLdb.connect(**self.db_config)

    def fetch_records(self, last_id: int = 0) -> List[Dict]:
        """分批次获取需要解压的记录"""
        conn = None
        try:
            conn = self._get_connection()
            with conn.cursor(cursors.DictCursor) as cursor:
                query = f"""
                    SELECT id, stockCode, date, downloadPath, decompressAlgo
                    FROM {self.table_name}
                    WHERE downloadStatus = 'SUCCESS'
                      AND (decompressStatus IS NULL OR decompressStatus != 'SUCCESS')
                      AND id > %s
                    ORDER BY id ASC
                    LIMIT %s
                """
                cursor.execute(query, (last_id, self.batch_size))
                return cursor.fetchall()
        except DBError as e:
            print(f"数据库查询失败: {e}")
            return []
        finally:
            if conn: conn.close()

    def decompress_data(self, algo: str, data: bytes) -> bytes:
        """根据算法解压数据"""
        try:
            if algo == 'ZLIB':
                return zlib.decompress(data, wbits=zlib.MAX_WBITS)
            elif algo == 'LZMA_RAW':
                # 使用 Python 标准库的 lzma
                # 需显式指定 filters 参数（假设已知字典大小）
                return lzma.decompress(data, format=lzma.FORMAT_RAW, filters=[
                    {'id': lzma.FILTER_LZMA1, 'dict_size': 2**20}
                ])
            else:
                raise ValueError(f"不支持的压缩算法: {algo}")
        except Exception as e:
            raise RuntimeError(f"解压失败: {str(e)}")

    def _calculate_entropy(self, data: bytes) -> float:
        """计算字节数据的熵值"""
        freq = [0] * 256
        total = len(data)
        if total == 0:
            return 0.0
            
        for byte in data:
            freq[byte] += 1
            
        entropy = 0.0
        for count in freq:
            if count == 0:
                continue
            p = count / total
            entropy -= p * math.log2(p)
            
        return round(entropy, 4)


    def process_batch(self, output_dir: str, batch: List[Dict]):
        """处理一批记录"""
        conn = None
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            for record in batch:
                # 初始化解压状态
                decompress_status = "SUCCESS"
                decompress_path = ""
                decompress_size = 0
                decompress_entropy = 0.0

                try:
                    # 检查文件是否存在
                    dat_path = record['downloadPath']
                    if not os.path.exists(dat_path):
                        raise FileNotFoundError(f"文件不存在: {dat_path}")

                    # 读取文件内容
                    with open(dat_path, 'rb') as f:
                        raw_data = f.read()

                    # 根据算法设置偏移量
                    offset = 25 if record['decompressAlgo'] == 'ZLIB' else 35
                    if len(raw_data) < offset:
                        raise ValueError("文件长度不足，无法跳过头部")

                    # 解压数据
                    compressed_data = raw_data[offset:]
                    plain_data = self.decompress_data(record['decompressAlgo'], compressed_data)

                    # 写入解压文件
                    stock_code = record['stockCode']
                    date = record['date']
                    output_path = os.path.join(output_dir, stock_code, f"{stock_code}.{date}.plain.dat")
                    os.makedirs(os.path.dirname(output_path), exist_ok=True)

                    with open(output_path, 'wb') as f:
                        f.write(plain_data)

                    decompress_size = os.path.getsize(output_path)
                    decompress_path = output_path
                    decompress_entropy = self._calculate_entropy(plain_data)

                except Exception as e:
                    decompress_status = f"ERROR: {str(e)}"
                    print(f"处理记录失败 [id={record['id']}][{record['stockCode']}/{record['date']}]: {str(e)}")

                # 更新数据库
                update_sql = f"""
                    UPDATE {self.table_name}
                    SET decompressStatus = %s,
                        decompressPath = %s,
                        decompressSize = %s,
                        decompressEntropy = %s
                    WHERE id = %s
                """
                cursor.execute(update_sql, (
                    decompress_status,
                    decompress_path if decompress_status == "SUCCESS" else None,
                    decompress_size if decompress_status == "SUCCESS" else None,
                    decompress_entropy if decompress_status == "SUCCESS" else None,
                    record['id']
                ))

            conn.commit()
        except DBError as e:
            print(f"数据库更新失败: {e}")
            if conn: conn.rollback()
        finally:
            if conn: conn.close()

    def run(self, output_dir: str, start_id: int = 0):
        """主运行逻辑"""
        last_id = start_id
        finish_count = 0
        while True:
            batch = self.fetch_records(last_id)
            if not batch:
                print("所有记录处理完成")
                finish_count += 1
                if finish_count > 20:
                    break
                time.sleep(30)
                continue
            else:
                finish_count = 0

            print(f"正在处理批次: ID范围 {batch[0]['id']} ~ {batch[-1]['id']}")
            self.process_batch(output_dir, batch)
            last_id = batch[-1]['id']

if __name__ == "__main__":
    # 解析命令行参数
    parser = argparse.ArgumentParser()
    parser.add_argument("--type", choices=['fenshi', 'pankou'], required=True)
    parser.add_argument("--start-id", type=int, default=0, help='表主键id')
    parser.add_argument("output_dir", help="解压文件输出目录")
    args = parser.parse_args()

    # 数据库配置（与下载工具一致）
    db_config = {
        'host': os.getenv('db_host'),
        'port': int(os.getenv('db_port', '3306')),
        'user': os.getenv('db_user'),
        'passwd': os.getenv('db_pass'),
        'db': os.getenv('db_name'),
        'charset': 'utf8mb4'
    }

    # 确定目标表
    table_map = {
        'fenshi': 'FenshiDatRecords',
        'pankou': 'PankouDatRecords'
    }
    table_name = table_map[args.type]

    # 运行解压程序
    decompressor = Decompressor(db_config, table_name)
    decompressor.run(args.output_dir, args.start_id)
