import subprocess
from base_downloader import BaseDownloader

class FenshiDownloader(BaseDownloader):
    def __init__(self):
        super().__init__('FenshiDatRecords')
        self.date_filter_enabled = False  # 禁用日期过滤
        self.task_name = "下载分时数据"

    def parse_dat_file(self, file_path: str) -> dict:
        """解析分时数据dat文件"""
        result = {
            'parsedSize': 0,  # 解析出的文件总长度
            'parsedDate': None,  # 预留解析出的日期字段
            'decompressAlgo': None
        }
        
        try:
            with open(file_path, 'rb') as f:
                response = f.read()  # 读取整个文件内容
                
                # 检查压缩类型
                if len(response) >= 27 and response[25] == 0x78 and response[26] == 0x9c:
                    # ZLIB 压缩
                    # 解析长度字段（21-25字节）
                    length_field = response[21:25]
                    data_length = int.from_bytes(length_field, 'little')
                    # print(f"[ZLIB]正确解析数据长度(0x15 ~ 0x18)：{data_length} 字节 (HEX: {data_length:x})")
                    
                    # 解析日期字段（36-40字节）
                    date_field = response[36:40]
                    parsed_date = int.from_bytes(date_field, 'little')
                    # print(f"[ZLIB]解析日期字段(0x24 ~ 0x27)：{parsed_date}")
                    
                    # 计算总长度（基础头25字节 + 数据长度）
                    total_length = 25 + data_length
                    decompressAlgo = 'ZLIB'

                elif len(response) >= 29:
                    # LZMA_RAW 压缩
                    # 解析长度字段（25-29字节）
                    length_field = response[25:29]
                    data_length = int.from_bytes(length_field, 'little')
                    # print(f"[LZMA_RAW]正确解析数据长度(0x19 ~ 0x1c)：{data_length} 字节 (HEX: {data_length:x})")
                    
                    # 解析日期字段（21-25字节）
                    date_field = response[21:25]
                    parsed_date = int.from_bytes(date_field, 'little')
                    # print(f"[LZMA_RAW]解析日期字段(0x15 ~ 0x18)：{parsed_date}")
                    
                    # 计算总长度（基础头29字节 + 数据长度）
                    total_length = 29 + data_length
                    decompressAlgo = 'LZMA_RAW'
                else:
                    raise ValueError("文件长度不足，无法解析压缩类型")
                
                # 更新解析结果
                result['parsedSize'] = total_length
                result['parsedDate'] = parsed_date
                result['decompressAlgo'] = decompressAlgo
                if data_length == 0:
                    result['downloadStatus'] = "TCP_404"
                
        except Exception as e:
            print(f"解析DAT文件失败: {str(e)}")
            result['parsedSize'] = -1  # 用-1标记解析失败
            
        return result
    
    def perform_download(self, stock_code, date, target_path):
        """Fenshi data download implementation"""

        # 检查是否需要跳过下载
        if self.should_skip_download(stock_code, date, target_path):
            return

        result = subprocess.run(
            [
                'python3', 
                '/data/projects/personal_tool/crack-eastmoney-dat/fenshi/fetch_fenshi.py',
                stock_code,
                date,
                target_path
            ],
            check=True,
            capture_output=True,
            text=True
        )
        
        if "ERROR" in result.stdout:
            raise RuntimeError(f"External script error: {result.stdout}")

if __name__ == "__main__":
    try:
        FenshiDownloader().run()
    except subprocess.CalledProcessError as e:
        print(f"Subprocess failed: {e.stderr}")
        sys.exit(2)
    except Exception as e:
        print(f"Fatal error: {str(e)}")
        sys.exit(1)
