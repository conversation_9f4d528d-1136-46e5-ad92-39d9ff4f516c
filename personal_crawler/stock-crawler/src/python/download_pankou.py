import os
import sys
import requests
import urllib3
from base_downloader import BaseDownloader

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class PankouDownloader(BaseDownloader):
    def __init__(self):
        super().__init__('PankouDatRecords')
        self.date_filter_enabled = True  # 启用日期过滤
        self.task_name = "下载盘口数据"

    def _should_skip_task(self, task: dict) -> bool:
        """跳过2018-09-14之前的任务"""
        date_str = task['date']
        try:
            # 将日期字符串转换为整数（例如 '20180914' → 20180914）
            date_num = int(date_str)
            return date_num <= 20180914
        except:
            print(f"日期格式错误: {date_str}")
            return False  # 异常情况不跳过

    def perform_download(self, stock_code, date, target_path):
        """盘口数据下载实现"""
        # 检查是否需要跳过下载
        if self.should_skip_download(stock_code, date, target_path):
            return

        city = "SZ"
        if stock_code[0] == "6":
            city = "SH"
        url = f"https://applookback.eastmoney.com/free/pankou_detail_new/1/{date}/{city}{stock_code}.dat"
        headers = {
            "host": "applookback.eastmoney.com",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "em-md": "MTBCNDc1QTItMENCMC00ODkyLUJBMDAtRTQ0NEU5NzM5QTM2"
        }

        proxies = {
            'http': os.environ.get('http_proxy'),
            'https': os.environ.get('https_proxy')
        }
        # # 调试输出代理配置
        # print(f"代理配置检测:")
        # print(f"http_proxy={os.environ.get('http_proxy')}")
        # print(f"https_proxy={os.environ.get('https_proxy')}")
        # print(f"实际使用的代理设置: {proxies}")

        # 添加详细日志记录
        # try:
        #     import http.client as http_client
        #     http_client.HTTPConnection.debuglevel = 1
        #     import logging
        #     logging.basicConfig()
        #     logging.getLogger().setLevel(logging.DEBUG)
        #     requests_log = logging.getLogger("requests.packages.urllib3")
        #     requests_log.setLevel(logging.DEBUG)
        #     requests_log.propagate = True
        # except ImportError:
        #     pass

        response = requests.get(url, 
            headers=headers,
            proxies=proxies,
            verify=False,
            timeout=30,
            # 强制使用代理（即使本地地址）
            allow_redirects=True
        )
    
        response.raise_for_status()

        # 确保目录存在
        os.makedirs(os.path.dirname(target_path), exist_ok=True)
        
        with open(target_path, 'wb') as f:
            f.write(response.content)

    def parse_dat_file(self, file_path: str) -> dict:
        """解析盘口数据dat文件"""
        result = {
            'parsedSize': 0,
            'parsedDate': None,  # 预留解析出的日期字段
            'decompressAlgo': 'ZLIB'
        }
        
        try:
            with open(file_path, 'rb') as f:
                # 读取14-17字节（索引13到16）
                f.seek(13)
                size_bytes = f.read(4)

                # 调试日志：打印size_bytes的十六进制值和文件路径
                # hex_bytes = size_bytes.hex()  # 将字节转换为十六进制字符串
                # print(f"DEBUG: file_path={file_path}, size_bytes (hex)={hex_bytes}")
                
                # 验证有效数据长度
                if len(size_bytes) != 4:
                    raise ValueError("文件长度不足，无法解析size字段")
                
                # 小端序转换
                parsed_size = int.from_bytes(size_bytes, byteorder='little')

                # print(f"DEBUG: file_path={file_path}, parsed_size={parsed_size} total_size={parsed_size+25}")
                result['parsedSize'] = parsed_size + 25 # 加上25字节长度的header才是完整dat文件的size
                
        except Exception as e:
            print(f"解析DAT文件失败: {str(e)}")
            result['parsedSize'] = -1  # 用-1标记解析失败
            
        return result

if __name__ == "__main__":
    try:
        PankouDownloader().run()
    except Exception as e:
        print(f"Fatal error: {str(e)}")
        sys.exit(1)
