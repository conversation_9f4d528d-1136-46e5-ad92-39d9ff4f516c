import os
import pymysql
from clickhouse_driver import Client
from clickhouse_driver.errors import Error as ClickHouseError
import logging
from typing import Dict, List

# ===== 配置区域 =====
config = {
    "mysql": {
        'host': os.getenv('db_host'),
        'port': int(os.getenv('db_port', '3306')),
        'user': os.getenv('db_user'),
        'password': os.getenv('db_pass'),
        'database': os.getenv('db_name'),
    },
    "clickhouse": {
        'host': os.getenv('clickhouse_host'),
        'port': int(os.getenv('clickhouse_port', '8123')),
        'user': os.getenv('clickhouse_user'),
        'password': os.getenv('clickhouse_pass'),
        'database': os.getenv('clickhouse_name'),
    },
    # 需要迁移的表配置列表
    "tables": [
        {
            "source_table": "StockKLineDailies",          # MySQL源表名
            "target_table": "StockKLineDailies",          # ClickHouse目标表名
            "query": "SELECT * FROM StockKLineDailies",   # 查询语句
            "order_by": "id",                 # MergeTree排序键
            "batch_size": 50000,               # 每批处理的行数
            "id_field": "id"                   # 用于分页的ID字段
        },
        {
            "source_table": "Stocks",
            "target_table": "Stocks",
            "query": "SELECT * FROM Stocks",
            "order_by": "id",
            "batch_size": 50000,
            "id_field": "id"
        }
    ]
}

# 数据类型映射字典（可扩展）
TYPE_MAPPING = {
    "int": "Int32",
    "tinyint": "Int8",
    "tinyint(1)": "Int8",
    "boolean": "Int8",
    "smallint": "Int16",
    "mediumint": "Int32",
    "bigint": "Int64",
    "varchar": "String",
    "char": "String",
    "text": "String",
    "datetime": "DateTime",
    "timestamp": "DateTime",
    "date": "Date",
    "float": "Float32",
    "double": "Float64",
    "decimal": "Decimal(18, 4)"  # 按需调整精度
}

# ===== 工具函数 =====
def get_mysql_table_structure(conn, table_name: str) -> List[Dict]:
    """获取MySQL表结构"""
    with conn.cursor() as cursor:
        cursor.execute(f"DESCRIBE {table_name}")
        return [{"field": row[0], "type": row[1].lower().split("(")[0]} for row in cursor.fetchall()]

def map_to_ch_type(mysql_type: str) -> str:
    """映射MySQL类型到ClickHouse类型"""
    for key in TYPE_MAPPING:
        if key in mysql_type:
            return TYPE_MAPPING[key]
    return "String"  # 默认类型

def create_ch_table(client: Client, table_name: str, columns: List[Dict], order_by: str):
    """在ClickHouse创建表（先删除已存在的表）"""
    # 先删除已存在的表
    drop_sql = f"DROP TABLE IF EXISTS {table_name}"
    try:
        client.execute(drop_sql)
        logging.info(f"已删除旧表 {table_name}")
    except ClickHouseError as e:
        logging.warning(f"删除表 {table_name} 失败: {str(e)}") 

    """在ClickHouse创建表"""
    cols = []
    order_by_cols = [col.strip() for col in order_by.split(",")]
    
    for col in columns:
        ch_type = map_to_ch_type(col["type"])
        # 排序键列不能为Nullable，其他列可以为Nullable
        if col["field"] in order_by_cols:
            cols.append(f"{col['field']} {ch_type}")  # 非Nullable
        else:
            cols.append(f"{col['field']} Nullable({ch_type})")  # Nullable
    
    create_sql = f"""
    CREATE TABLE IF NOT EXISTS {table_name} (
        {', '.join(cols)}
    ) ENGINE = MergeTree()
    ORDER BY {order_by}
    SETTINGS allow_nullable_key = 1  -- 启用Nullable键支持
    """
    client.execute(create_sql)

def migrate_data(mysql_conn, ch_client: Client, table_cfg: Dict):
    """迁移数据（分批处理）"""
    source_table = table_cfg["source_table"]
    target_table = table_cfg["target_table"]
    base_query = table_cfg["query"]
    order_by = table_cfg["order_by"]
    batch_size = table_cfg["batch_size"]
    id_field = table_cfg["id_field"]
    
    try:
        # 获取MySQL表结构
        table_structure = get_mysql_table_structure(mysql_conn, source_table)

        # 在ClickHouse创建表
        ch_client.execute(f"USE {config['clickhouse']['database']}")
        create_ch_table(ch_client, target_table, table_structure, order_by)
        logging.info(f"表 {target_table} 创建成功")

        # 获取最小和最大ID用于分页
        with mysql_conn.cursor() as cursor:
            cursor.execute(f"SELECT MIN({id_field}), MAX({id_field}) FROM {source_table}")
            min_id, max_id = cursor.fetchone()
            logging.info(f"数据ID范围: {min_id} 到 {max_id}")

        # 分批处理数据
        current_min = min_id
        total_rows = 0
        
        while current_min <= max_id:
            # 构造分页查询
            query = f"""
            {base_query} 
            WHERE {id_field} >= {current_min} 
            ORDER BY {id_field} 
            LIMIT {batch_size}
            """
            
            with mysql_conn.cursor(pymysql.cursors.SSCursor) as cursor:
                cursor.execute(query)
                
                # 获取列元数据
                column_types = [desc[1] for desc in cursor.description]
                rows = cursor.fetchall()
                
                if not rows:
                    break
                
                data = []
                for row in rows:
                    processed_row = {}
                    for i, (col, col_type, val) in enumerate(zip(table_structure, column_types, row)):
                        # 特殊处理NULL值
                        if val is None:
                            # 对字符串类型字段，将NULL转为空字符串
                            if "varchar" in str(col_type).lower() or "char" in str(col_type).lower() or "text" in str(col_type).lower():
                                processed_row[col["field"]] = ""
                            # 对数值类型字段，将NULL转为0
                            elif "int" in str(col_type).lower() or "tinyint" in str(col_type).lower():
                                processed_row[col["field"]] = 0
                            # 对布尔类型字段，将NULL转为0
                            elif "tinyint(1)" in str(col_type).lower():
                                processed_row[col["field"]] = 0
                            # 其他类型保持NULL
                            else:
                                processed_row[col["field"]] = None
                        # 处理布尔值
                        elif "tinyint(1)" in str(col_type).lower():
                            processed_row[col["field"]] = 1 if val else 0
                        # 其他情况保持原值
                        else:
                            processed_row[col["field"]] = val
                    
                    data.append(processed_row)
                
                # 安全插入
                try:
                    ch_client.execute(
                        f"INSERT INTO {target_table} VALUES",
                        data,
                        types_check=True
                    )
                    inserted_count = len(data)
                    total_rows += inserted_count
                    current_min = data[-1][id_field] + 1  # 更新下一个批次的最小ID
                    logging.info(f"已插入 {inserted_count} 行到 {target_table} (总计: {total_rows}, 当前ID: {current_min-1})")
                except Exception as e:
                    logging.error(f"插入失败: {str(e)}")
                    logging.error(f"问题数据样本: {data[0] if data else '无数据'}")
                    raise

    except Exception as e:
        logging.error(f"数据迁移失败: {str(e)}")

# ===== 主程序 =====
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s: %(message)s")

    # 初始化连接
    try:
        logging.info(f"config.mysql: {config['mysql']}")
        logging.info(f"config.clickhouse: {config['clickhouse']}")
        mysql_conn = pymysql.connect(
            host=config["mysql"]["host"],
            port=config["mysql"]["port"],
            user=config["mysql"]["user"],
            password=config["mysql"]["password"],
            database=config["mysql"]["database"],
            charset="utf8mb4"
        )
        logging.info("MySQL 连接成功！")
        
        ch_client = Client(
            host=config["clickhouse"]["host"],
            port=config["clickhouse"]["port"],
            user=config["clickhouse"]["user"],
            password=config["clickhouse"]["password"],
            database=config["clickhouse"]["database"]
        )
        logging.info("ClickHouse 连接成功！")

    except pymysql.Error as e:
        logging.error(f"MySQL 连接失败: {e.args} (错误码: {e.args[0]})")
    except ClickHouseError as e:
        logging.error(f"ClickHouse 连接失败: {str(e)}")
    except Exception as e:
        logging.error(f"未知错误: {str(e)}")
        exit(1)

    # 处理每个表
    for table_cfg in config["tables"]:
        logging.info(f"开始处理表: {table_cfg['source_table']} => {table_cfg['target_table']}")
        migrate_data(mysql_conn, ch_client, table_cfg)

    # 关闭连接
    mysql_conn.close()
    ch_client.disconnect()
    logging.info("所有迁移任务完成")
