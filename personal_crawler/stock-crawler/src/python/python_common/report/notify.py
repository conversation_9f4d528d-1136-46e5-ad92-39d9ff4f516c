from ..request.client import http_request_with_retry
import logging

logger = logging.getLogger(__name__)

async def notify(name: str, status: str, detail: str) -> None:
    """
    Send notification to Feishu webhook
    
    Args:
        name: Task name
        status: Task status
        detail: Task details
    """
    url = 'https://www.feishu.cn/flow/api/trigger-webhook/2f00ca77f9a2d60b2cae852fc6e54ced'
    
    body = {
        'task_name': name,
        'task_status': status,
        'task_detail': detail
    }

    options = {
        'method': 'POST',
        'uri': url,
        'body': body,
        'json': True
    }

    try:
        response = http_request_with_retry(options)
        logger.info(f'Notification sent successfully: {response}')
    except Exception as error:
        logger.error(f'Failed to send notification: {error}')