import os
import gzip
import json
import time
import requests
from typing import Any, Dict, Optional

def make_request(logger: Any, options: Dict, retries: int = 3, delay: int = 1000) -> Any:
    """
    Make HTTP request with retry mechanism
    
    Args:
        logger: Logger instance
        options: Request options dictionary
        retries: Number of retries
        delay: Delay between retries in milliseconds
    """
    # Add proxy support from environment variables
    proxy = os.getenv('http_proxy') or os.getenv('HTTP_PROXY')
    if options.get('proxy'):
        # Ignore SSL issues when using proxy
        options['verify'] = False
        proxy = options['proxy']
        if logger:
            logger.debug(f"Using options.proxy: {proxy}")
    
    # Set proxy configuration
    proxies = {'http': proxy, 'https': proxy} if proxy else None
    
    # Set default headers
    headers = {
        **options.get('headers', {})
    }
    
    for attempt in range(1, retries + 1):
        try:
            response = requests.request(
                method=options.get('method', 'GET'),
                url=options['uri'],
                headers=headers,
                json=options.get('body') if options.get('json') else None,
                data=options.get('body') if not options.get('json') else None,
                proxies=proxies,
                verify=options.get('verify', True)
            )
            response.raise_for_status()
            
            content = response.content
            if options.get('encoding') is None and not options.get('json'):
                try:
                    content = gzip.decompress(content).decode('utf-8')
                    if options.get('parseJson'):
                        content = json.loads(content)
                except Exception:
                    pass
            
            return content if not options.get('json') else response.json()
            
        except Exception as error:
            proxy_error = ' (Proxy connection failed)' if 'Connection refused' in str(error) and options.get('proxy') else ''
            if logger:
                logger.warning(f"[Attempt {attempt}] Request failed for {options['uri']}: {str(error)}{proxy_error}")
            
            if attempt == retries:
                if logger:
                    logger.error(f"[Failed] Max retries reached for URL: {options['uri']}")
                raise
            
            # Wait before retry
            time.sleep(delay * attempt / 1000)

def http_request_with_retry(options: Dict, retry_count: int = 3) -> Any:
    """
    Wrapper function for making HTTP requests with retry
    """
    import logging
    logger = logging.getLogger(__name__)
    return make_request(logger, options, retry_count, 1000)