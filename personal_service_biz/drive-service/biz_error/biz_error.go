package biz_error

import (
	"fmt"
)

const (
	ERR_SUCCESS                = 200
	ERR_INTERNAL_ERROR         = 500
	ERR_INVALID_PARAMS         = 400
	ERR_FILE_NOT_FOUND         = 404
	ERR_FILE_ALREADY_EXISTS    = 409
	ERR_INSUFFICIENT_STORAGE   = 507
	ERR_UPLOAD_FAILED          = 422
	ERR_DOWNLOAD_FAILED        = 423
	ERR_PERMISSION_DENIED      = 403
	ERR_SHARE_NOT_FOUND        = 405
	ERR_SHARE_EXPIRED          = 410
	ERR_SHARE_PASSWORD_WRONG   = 401
	ERR_INIT_DB_CONN_FAILED    = 501
	ERR_UNSUPPORTED_FILE_TYPE  = 415
	ERR_FILE_READ_FAILED       = 424
)

var errorMessages = map[int]string{
	ERR_SUCCESS:                "Success",
	ERR_INTERNAL_ERROR:         "Internal server error",
	ERR_INVALID_PARAMS:         "Invalid parameters",
	ERR_FILE_NOT_FOUND:         "File not found",
	ERR_FILE_ALREADY_EXISTS:    "File already exists",
	ERR_INSUFFICIENT_STORAGE:   "Insufficient storage space",
	ERR_UPLOAD_FAILED:          "Upload failed",
	ERR_DOWNLOAD_FAILED:        "Download failed",
	ERR_PERMISSION_DENIED:      "Permission denied",
	ERR_SHARE_NOT_FOUND:        "Share not found",
	ERR_SHARE_EXPIRED:          "Share expired",
	ERR_SHARE_PASSWORD_WRONG:   "Share password wrong",
	ERR_INIT_DB_CONN_FAILED:    "Failed to initialize database connection",
	ERR_UNSUPPORTED_FILE_TYPE:  "Unsupported file type",
	ERR_FILE_READ_FAILED:       "Failed to read file",
}

type BizError struct {
	code int
	msg  string
}

func NewBizError(code int) *BizError {
	msg, exists := errorMessages[code]
	if !exists {
		msg = "Unknown error"
	}
	return &BizError{
		code: code,
		msg:  msg,
	}
}

func NewBizErrorWithMsg(code int, msg string) *BizError {
	return &BizError{
		code: code,
		msg:  msg,
	}
}

func NewBizErrorByError(err error) *BizError {
	return &BizError{
		code: ERR_INTERNAL_ERROR,
		msg:  err.Error(),
	}
}

func (e *BizError) Error() string {
	return fmt.Sprintf("BizError: code=%d, msg=%s", e.code, e.msg)
}

func (e *BizError) Code() int {
	return e.code
}

func (e *BizError) Msg() string {
	return e.msg
}
