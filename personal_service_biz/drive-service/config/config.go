package config

import (
	"context"
	"fmt"
	"os"
)

type Config struct {
	StorePath string
	DSN       string
}

var globalConfig Config

func GetDSNByHost(ipPort string) string {
	return fmt.Sprintf("driveuser:drivepass@tcp(%s)/db_drive?charset=utf8mb4&parseTime=True&loc=Local", ipPort)
}

func loadConfigs(ctx context.Context) error {
	env := os.Getenv("ENV")
	if env == "deploy" {
		// 生产环境配置
		globalConfig.StorePath = "/store_root"
		globalConfig.DSN = GetDSNByHost("drive-mysql:3306")
	} else {
		// 开发环境配置
		globalConfig.StorePath = "/data/docker-volumes/public_root/drive_files"
		globalConfig.DSN = GetDSNByHost("127.0.0.1:3312")
	}
	return nil
}

func InitConfig(ctx context.Context) error {
	return loadConfigs(ctx)
}

func GetStorePath() string {
	return globalConfig.StorePath
}

func GetDSN() string {
	return globalConfig.DSN
}
