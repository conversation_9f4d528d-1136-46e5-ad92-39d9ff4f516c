package main

import (
	"context"
	"drive-service/config"
	"drive-service/models"
	"drive-service/service"
	"thrift-common/server/drive"

	"github.com/sirupsen/logrus"
)

func main() {
	var err error
	ctx := context.Background()
	logger := logrus.New()
	err = config.InitConfig(ctx)
	if err != nil {
		logger.Errorf("loadConfigs failed: %+v", err)
		return
	}

	err = models.InitMysql(ctx)
	if err != nil {
		logger.Errorf("initMysql failed: %+v", err)
		return
	}

	handler := &service.DriveService{}
	logger.Info("Drive Service Started!")
	err = drive.Server(ctx, handler)
	if err != nil {
		logger.Errorf("Server error: %+v", err)
	}
}
