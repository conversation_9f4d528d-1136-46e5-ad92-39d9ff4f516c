package models

import (
	"context"
	"drive-service/biz_error"
	"time"

	"gorm.io/gorm"
)

type DriveFile struct {
	ID            int64      `gorm:"column:id"`
	Name          string     `gorm:"column:name"`
	Path          string     `gorm:"column:path"`
	ParentID      *int64     `gorm:"column:parent_id"`
	FileType      string     `gorm:"column:file_type"`
	MimeType      *string    `gorm:"column:mime_type"`
	Size          int64      `gorm:"column:size"`
	Hash          *string    `gorm:"column:hash"`
	StoragePath   *string    `gorm:"column:storage_path"`
	ThumbnailPath *string    `gorm:"column:thumbnail_path"`
	IsPublic      bool       `gorm:"column:is_public"`
	DownloadCount int64      `gorm:"column:download_count"`
	CreatedAt     time.Time  `gorm:"column:created_at"`
	UpdatedAt     time.Time  `gorm:"column:updated_at"`
	DeletedAt     *time.Time `gorm:"column:deleted_at"`
}

func (DriveFile) TableName() string {
	return "drive_files"
}

// 创建文件记录
func CreateDriveFile(ctx context.Context, file *DriveFile) *biz_error.BizError {
	now := time.Now()
	file.CreatedAt = now
	file.UpdatedAt = now
	
	result := GetDB().Create(file)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

// 根据ID获取文件
func GetDriveFileByID(ctx context.Context, id int64) (*DriveFile, *biz_error.BizError) {
	var file DriveFile
	result := GetDB().Where("id = ? AND deleted_at IS NULL", id).First(&file)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, biz_error.NewBizError(biz_error.ERR_FILE_NOT_FOUND)
		}
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	return &file, nil
}

// 根据路径获取文件
func GetDriveFileByPath(ctx context.Context, path string) (*DriveFile, *biz_error.BizError) {
	var file DriveFile
	result := GetDB().Where("path = ? AND deleted_at IS NULL", path).First(&file)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, biz_error.NewBizError(biz_error.ERR_FILE_NOT_FOUND)
		}
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	return &file, nil
}

// 获取文件列表
func GetDriveFileList(ctx context.Context, parentID *int64, offset, count int64, keyword string) ([]*DriveFile, int64, *biz_error.BizError) {
	var files []*DriveFile
	var total int64
	
	query := GetDB().Model(&DriveFile{}).Where("deleted_at IS NULL")
	
	// 父目录过滤
	if parentID == nil {
		query = query.Where("parent_id IS NULL")
	} else {
		query = query.Where("parent_id = ?", *parentID)
	}
	
	// 关键词搜索
	if keyword != "" {
		query = query.Where("name LIKE ?", "%"+keyword+"%")
	}
	
	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, biz_error.NewBizErrorByError(err)
	}
	
	// 获取分页数据
	if err := query.Order("file_type DESC, name ASC").Offset(int(offset)).Limit(int(count)).Find(&files).Error; err != nil {
		return nil, 0, biz_error.NewBizErrorByError(err)
	}
	
	return files, total, nil
}

// 更新文件
func UpdateDriveFile(ctx context.Context, file *DriveFile) *biz_error.BizError {
	file.UpdatedAt = time.Now()
	result := GetDB().Save(file)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

// 删除文件（软删除）
func DeleteDriveFile(ctx context.Context, id int64) *biz_error.BizError {
	now := time.Now()
	result := GetDB().Model(&DriveFile{}).Where("id = ?", id).Update("deleted_at", now)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected == 0 {
		return biz_error.NewBizError(biz_error.ERR_FILE_NOT_FOUND)
	}
	return nil
}

// 检查文件名是否在同一目录下已存在
func CheckFileNameExists(ctx context.Context, parentID *int64, name string, excludeID *int64) (bool, *biz_error.BizError) {
	var count int64
	query := GetDB().Model(&DriveFile{}).Where("name = ? AND deleted_at IS NULL", name)
	
	if parentID == nil {
		query = query.Where("parent_id IS NULL")
	} else {
		query = query.Where("parent_id = ?", *parentID)
	}
	
	if excludeID != nil {
		query = query.Where("id != ?", *excludeID)
	}
	
	if err := query.Count(&count).Error; err != nil {
		return false, biz_error.NewBizErrorByError(err)
	}
	
	return count > 0, nil
}

// 递归删除文件夹及其子文件
func DeleteDriveFileRecursive(ctx context.Context, id int64) *biz_error.BizError {
	// 先获取所有子文件
	var children []*DriveFile
	if err := GetDB().Where("parent_id = ? AND deleted_at IS NULL", id).Find(&children).Error; err != nil {
		return biz_error.NewBizErrorByError(err)
	}
	
	// 递归删除子文件
	for _, child := range children {
		if err := DeleteDriveFileRecursive(ctx, child.ID); err != nil {
			return err
		}
	}
	
	// 删除当前文件
	return DeleteDriveFile(ctx, id)
}

// 增加下载次数
func IncrementDownloadCount(ctx context.Context, id int64) *biz_error.BizError {
	result := GetDB().Model(&DriveFile{}).Where("id = ?", id).UpdateColumn("download_count", gorm.Expr("download_count + 1"))
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}
