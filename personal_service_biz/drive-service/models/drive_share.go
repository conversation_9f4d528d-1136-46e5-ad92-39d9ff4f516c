package models

import (
	"context"
	"drive-service/biz_error"
	"time"

	"gorm.io/gorm"
)

type DriveShare struct {
	ID            int64      `gorm:"column:id"`
	FileID        int64      `gorm:"column:file_id"`
	ShareCode     string     `gorm:"column:share_code"`
	Password      *string    `gorm:"column:password"`
	ExpireTime    *time.Time `gorm:"column:expire_time"`
	DownloadLimit int64      `gorm:"column:download_limit"`
	DownloadCount int64      `gorm:"column:download_count"`
	IsActive      bool       `gorm:"column:is_active"`
	CreatedAt     time.Time  `gorm:"column:created_at"`
	UpdatedAt     time.Time  `gorm:"column:updated_at"`
	DeletedAt     *time.Time `gorm:"column:deleted_at"`
}

func (DriveShare) TableName() string {
	return "drive_shares"
}

// 创建分享记录
func CreateDriveShare(ctx context.Context, share *DriveShare) *biz_error.BizError {
	now := time.Now()
	share.CreatedAt = now
	share.UpdatedAt = now
	share.IsActive = true
	
	result := GetDB().Create(share)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

// 根据分享码获取分享信息
func GetDriveShareByCode(ctx context.Context, shareCode string) (*DriveShare, *biz_error.BizError) {
	var share DriveShare
	result := GetDB().Where("share_code = ? AND deleted_at IS NULL", shareCode).First(&share)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, biz_error.NewBizError(biz_error.ERR_SHARE_NOT_FOUND)
		}
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	return &share, nil
}

// 检查分享是否有效
func (s *DriveShare) IsValid() bool {
	if !s.IsActive {
		return false
	}
	
	// 检查是否过期
	if s.ExpireTime != nil && time.Now().After(*s.ExpireTime) {
		return false
	}
	
	// 检查下载次数限制
	if s.DownloadLimit > 0 && s.DownloadCount >= s.DownloadLimit {
		return false
	}
	
	return true
}

// 增加下载次数
func IncrementShareDownloadCount(ctx context.Context, id int64) *biz_error.BizError {
	result := GetDB().Model(&DriveShare{}).Where("id = ?", id).UpdateColumn("download_count", gorm.Expr("download_count + 1"))
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

// 更新分享
func UpdateDriveShare(ctx context.Context, share *DriveShare) *biz_error.BizError {
	share.UpdatedAt = time.Now()
	result := GetDB().Save(share)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

// 删除分享（软删除）
func DeleteDriveShare(ctx context.Context, id int64) *biz_error.BizError {
	now := time.Now()
	result := GetDB().Model(&DriveShare{}).Where("id = ?", id).Update("deleted_at", now)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected == 0 {
		return biz_error.NewBizError(biz_error.ERR_SHARE_NOT_FOUND)
	}
	return nil
}

// 根据文件ID获取分享列表
func GetDriveSharesByFileID(ctx context.Context, fileID int64) ([]*DriveShare, *biz_error.BizError) {
	var shares []*DriveShare
	result := GetDB().Where("file_id = ? AND deleted_at IS NULL", fileID).Find(&shares)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	return shares, nil
}
