package models

import (
	"context"
	"drive-service/biz_error"
	"time"

	"gorm.io/gorm"
)

type DriveUploadTask struct {
	ID             int64     `gorm:"column:id"`
	FileName       string    `gorm:"column:file_name"`
	FileSize       int64     `gorm:"column:file_size"`
	ChunkSize      int64     `gorm:"column:chunk_size"`
	TotalChunks    int64     `gorm:"column:total_chunks"`
	UploadedChunks int64     `gorm:"column:uploaded_chunks"`
	UploadPath     string    `gorm:"column:upload_path"`
	TempDir        string    `gorm:"column:temp_dir"`
	Status         string    `gorm:"column:status"`
	ErrorMessage   *string   `gorm:"column:error_message"`
	CreatedAt      time.Time `gorm:"column:created_at"`
	UpdatedAt      time.Time `gorm:"column:updated_at"`
}

func (DriveUploadTask) TableName() string {
	return "drive_upload_tasks"
}

const (
	UploadStatusPending    = "pending"
	UploadStatusUploading  = "uploading"
	UploadStatusCompleted  = "completed"
	UploadStatusFailed     = "failed"
)

// 创建上传任务
func CreateDriveUploadTask(ctx context.Context, task *DriveUploadTask) *biz_error.BizError {
	now := time.Now()
	task.CreatedAt = now
	task.UpdatedAt = now
	task.Status = UploadStatusPending
	task.UploadedChunks = 0
	
	result := GetDB().Create(task)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

// 根据ID获取上传任务
func GetDriveUploadTaskByID(ctx context.Context, id int64) (*DriveUploadTask, *biz_error.BizError) {
	var task DriveUploadTask
	result := GetDB().Where("id = ?", id).First(&task)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			return nil, biz_error.NewBizError(biz_error.ERR_FILE_NOT_FOUND)
		}
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	return &task, nil
}

// 更新上传任务
func UpdateDriveUploadTask(ctx context.Context, task *DriveUploadTask) *biz_error.BizError {
	task.UpdatedAt = time.Now()
	result := GetDB().Save(task)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

// 更新上传进度
func UpdateUploadProgress(ctx context.Context, taskID int64, uploadedChunks int64) *biz_error.BizError {
	result := GetDB().Model(&DriveUploadTask{}).Where("id = ?", taskID).Updates(map[string]interface{}{
		"uploaded_chunks": uploadedChunks,
		"status":          UploadStatusUploading,
		"updated_at":      time.Now(),
	})
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

// 标记上传任务完成
func MarkUploadTaskCompleted(ctx context.Context, taskID int64) *biz_error.BizError {
	result := GetDB().Model(&DriveUploadTask{}).Where("id = ?", taskID).Updates(map[string]interface{}{
		"status":     UploadStatusCompleted,
		"updated_at": time.Now(),
	})
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

// 标记上传任务失败
func MarkUploadTaskFailed(ctx context.Context, taskID int64, errorMsg string) *biz_error.BizError {
	result := GetDB().Model(&DriveUploadTask{}).Where("id = ?", taskID).Updates(map[string]interface{}{
		"status":        UploadStatusFailed,
		"error_message": errorMsg,
		"updated_at":    time.Now(),
	})
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

// 删除上传任务
func DeleteDriveUploadTask(ctx context.Context, id int64) *biz_error.BizError {
	result := GetDB().Delete(&DriveUploadTask{}, id)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

// 清理过期的上传任务
func CleanupExpiredUploadTasks(ctx context.Context, expireHours int) *biz_error.BizError {
	expireTime := time.Now().Add(-time.Duration(expireHours) * time.Hour)
	result := GetDB().Where("created_at < ? AND status IN (?)", expireTime, []string{UploadStatusPending, UploadStatusFailed}).Delete(&DriveUploadTask{})
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}
