package models

import (
	"context"
	"drive-service/config"

	wErr "github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var db *gorm.DB

func GetDB() *gorm.DB {
	return db
}

func InitMysql(ctx context.Context) error {
	logger := logrus.New()
	var err error
	dsn := config.GetDSN()
	db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		logger.Errorf("InitMysql err[%+v]", err)
		return wErr.Wrap(err, "")
	}
	return nil
}
