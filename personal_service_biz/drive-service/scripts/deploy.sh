#!/bin/bash
cur_dir=$(dirname $(readlink -f $0))
root_dir=$(dirname ${cur_dir})
if [[ ! -f ${root_dir}/.version ]]; then
  echo "Wrong Root Directory"
  exit
fi

source ${root_dir}/config.ini
version_file=${root_dir}/.version
image_version=$(UpdateVersionPatch ${version_file})

image_name=drive-service:${image_version}
docker build -t ${image_name} -f ${root_dir}/image/Dockerfile ${root_dir}/
docker image ls -a | grep drive-service

path=/data/docker-volumes/${container_name}
if [[ -d ${path} ]]; then
  echo "Already Exist Path: ${path}"
else
  mkdir -p ${path}
fi

public_root=/data/docker-volumes/public_root
drive_root=${public_root}/drive_files

docker container rm -f ${container_name}
docker run -d  --restart unless-stopped --net admin-net \
  -e ENV='deploy' \
  -e LANG="C.UTF-8" \
  -e LANGUAGE="C.UTF-8" \
  -e LC_ALL="C.UTF-8" \
  --name=${container_name} \
  -v ${path}/runtime/drive-service-logs:/runtime/drive-service-logs \
  -v ${drive_root}:/store_root \
  ${image_name}
docker container ls -a

# deploy.sh使用root执行，git操作可能存在报错：fatal: detected dubious ownership in repository at
# git add .version
# git commit -m "update version"
# git push
