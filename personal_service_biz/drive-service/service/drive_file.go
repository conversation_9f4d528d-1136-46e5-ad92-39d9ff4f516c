package service

import (
	"context"
	"drive-service/biz_error"
	"drive-service/common"
	"drive-service/config"
	"drive-service/models"
	driveGen "thrift-common/gen-go/drive"
	"io/ioutil"
	"os"
	"path/filepath"

	"github.com/sirupsen/logrus"
)

// 获取文件列表
func GetFileList(ctx context.Context, parentID *int64, offset, count int64, keyword string) ([]*driveGen.DriveFile, int64, *biz_error.BizError) {
	logger := logrus.New()

	files, total, err := models.GetDriveFileList(ctx, parentID, offset, count, keyword)
	if err != nil {
		logger.Errorf("GetDriveFileList err: %+v", err)
		return nil, 0, err
	}

	// 转换为thrift结构
	result := make([]*driveGen.DriveFile, len(files))
	for i, file := range files {
		result[i] = &driveGen.DriveFile{
			ID:            file.ID,
			Name:          file.Name,
			Path:          file.Path,
			ParentID:      file.ParentID,
			FileType:      file.FileType,
			MimeType:      file.MimeType,
			Size:          file.Size,
			Hash:          file.Hash,
			StoragePath:   file.StoragePath,
			ThumbnailPath: file.ThumbnailPath,
			IsPublic:      file.IsPublic,
			DownloadCount: file.DownloadCount,
			CreatedAt:     common.FormatTime(file.CreatedAt),
			UpdatedAt:     common.FormatTime(file.UpdatedAt),
		}
	}

	return result, total, nil
}

// 创建文件夹
func CreateFolder(ctx context.Context, name string, parentID *int64) (*driveGen.DriveFile, *biz_error.BizError) {
	logger := logrus.New()

	// 验证文件夹名称
	if !common.IsValidFileName(name) {
		return nil, biz_error.NewBizError(biz_error.ERR_INVALID_PARAMS)
	}

	// 检查同名文件夹是否已存在
	exists, err := models.CheckFileNameExists(ctx, parentID, name, nil)
	if err != nil {
		logger.Errorf("CheckFileNameExists err: %+v", err)
		return nil, err
	}
	if exists {
		return nil, biz_error.NewBizError(biz_error.ERR_FILE_ALREADY_EXISTS)
	}

	// 构建路径
	var path string
	if parentID == nil {
		path = "/" + name
	} else {
		parentFile, err := models.GetDriveFileByID(ctx, *parentID)
		if err != nil {
			logger.Errorf("GetDriveFileByID err: %+v", err)
			return nil, err
		}
		if parentFile.FileType != "folder" {
			return nil, biz_error.NewBizError(biz_error.ERR_INVALID_PARAMS)
		}
		path = filepath.Join(parentFile.Path, name)
	}

	// 创建物理目录
	storagePath := common.BuildStoragePath(config.GetStorePath(), path)
	if err := common.EnsureDir(storagePath); err != nil {
		logger.Errorf("EnsureDir err: %+v", err)
		return nil, biz_error.NewBizErrorByError(err)
	}

	// 创建数据库记录
	file := &models.DriveFile{
		Name:        name,
		Path:        common.CleanPath(path),
		ParentID:    parentID,
		FileType:    "folder",
		Size:        0,
		StoragePath: common.Str2StrPtr(storagePath),
		IsPublic:    false,
	}

	if err := models.CreateDriveFile(ctx, file); err != nil {
		logger.Errorf("CreateDriveFile err: %+v", err)
		return nil, err
	}

	return &driveGen.DriveFile{
		ID:            file.ID,
		Name:          file.Name,
		Path:          file.Path,
		ParentID:      file.ParentID,
		FileType:      file.FileType,
		MimeType:      file.MimeType,
		Size:          file.Size,
		Hash:          file.Hash,
		StoragePath:   file.StoragePath,
		IsPublic:      file.IsPublic,
		DownloadCount: file.DownloadCount,
		CreatedAt:     common.FormatTime(file.CreatedAt),
		UpdatedAt:     common.FormatTime(file.UpdatedAt),
	}, nil
}

// 上传文件
func UploadFile(ctx context.Context, fileName string, fileSize int64, parentID *int64, fileData []byte) (*driveGen.DriveFile, *biz_error.BizError) {
	logger := logrus.New()

	// 验证文件名
	if !common.IsValidFileName(fileName) {
		return nil, biz_error.NewBizError(biz_error.ERR_INVALID_PARAMS)
	}

	// 检查同名文件是否已存在
	exists, err := models.CheckFileNameExists(ctx, parentID, fileName, nil)
	if err != nil {
		logger.Errorf("CheckFileNameExists err: %+v", err)
		return nil, err
	}
	if exists {
		return nil, biz_error.NewBizError(biz_error.ERR_FILE_ALREADY_EXISTS)
	}

	// 构建路径
	var path string
	if parentID == nil {
		path = "/" + fileName
	} else {
		parentFile, err := models.GetDriveFileByID(ctx, *parentID)
		if err != nil {
			logger.Errorf("GetDriveFileByID err: %+v", err)
			return nil, err
		}
		if parentFile.FileType != "folder" {
			return nil, biz_error.NewBizError(biz_error.ERR_INVALID_PARAMS)
		}
		path = filepath.Join(parentFile.Path, fileName)
	}

	// 保存文件到存储
	storagePath := common.BuildStoragePath(config.GetStorePath(), path)
	storageDir := filepath.Dir(storagePath)
	if err := common.EnsureDir(storageDir); err != nil {
		logger.Errorf("EnsureDir err: %+v", err)
		return nil, biz_error.NewBizErrorByError(err)
	}

	if err := ioutil.WriteFile(storagePath, fileData, 0644); err != nil {
		logger.Errorf("WriteFile err: %+v", err)
		return nil, biz_error.NewBizErrorByError(err)
	}

	// 计算文件哈希
	hash, hashErr := common.CalculateFileHash(storagePath)
	if hashErr != nil {
		logger.Errorf("CalculateFileHash err: %+v", hashErr)
		// 哈希计算失败不影响上传，继续处理
		hash = ""
	}

	// 获取MIME类型
	mimeType := common.GetMimeType(fileName)

	// 创建数据库记录
	file := &models.DriveFile{
		Name:        fileName,
		Path:        common.CleanPath(path),
		ParentID:    parentID,
		FileType:    "file",
		MimeType:    common.Str2StrPtr(mimeType),
		Size:        int64(len(fileData)),
		Hash:        common.Str2StrPtr(hash),
		StoragePath: common.Str2StrPtr(storagePath),
		IsPublic:    false,
	}

	if err := models.CreateDriveFile(ctx, file); err != nil {
		logger.Errorf("CreateDriveFile err: %+v", err)
		// 删除已保存的文件
		os.Remove(storagePath)
		return nil, err
	}

	// 尝试生成缩略图（异步，不影响文件上传结果）
	go func() {
		if IsThumbnailSupported(mimeType) {
			thumbnailPath, thumbnailErr := GenerateThumbnail(context.Background(), file.ID, DefaultThumbnailWidth, DefaultThumbnailHeight)
			if thumbnailErr == nil {
				file.ThumbnailPath = common.Str2StrPtr(thumbnailPath)
				models.UpdateDriveFile(context.Background(), file)
			}
		}
	}()

	return &driveGen.DriveFile{
		ID:            file.ID,
		Name:          file.Name,
		Path:          file.Path,
		ParentID:      file.ParentID,
		FileType:      file.FileType,
		MimeType:      file.MimeType,
		Size:          file.Size,
		Hash:          file.Hash,
		StoragePath:   file.StoragePath,
		ThumbnailPath: file.ThumbnailPath,
		IsPublic:      file.IsPublic,
		DownloadCount: file.DownloadCount,
		CreatedAt:     common.FormatTime(file.CreatedAt),
		UpdatedAt:     common.FormatTime(file.UpdatedAt),
	}, nil
}

// 下载文件
func DownloadFile(ctx context.Context, fileID int64) ([]byte, string, string, *biz_error.BizError) {
	logger := logrus.New()

	// 获取文件信息
	file, err := models.GetDriveFileByID(ctx, fileID)
	if err != nil {
		logger.Errorf("GetDriveFileByID err: %+v", err)
		return nil, "", "", err
	}

	if file.FileType != "file" {
		return nil, "", "", biz_error.NewBizError(biz_error.ERR_INVALID_PARAMS)
	}

	// 读取文件内容
	storagePath := common.StrPtr2Str(file.StoragePath)
	if storagePath == "" {
		storagePath = common.BuildStoragePath(config.GetStorePath(), file.Path)
	}

	fileData, readErr := ioutil.ReadFile(storagePath)
	if readErr != nil {
		logger.Errorf("ReadFile err: %+v", readErr)
		return nil, "", "", biz_error.NewBizError(biz_error.ERR_DOWNLOAD_FAILED)
	}

	// 增加下载次数
	if err := models.IncrementDownloadCount(ctx, fileID); err != nil {
		logger.Warnf("IncrementDownloadCount err: %+v", err)
		// 不影响下载流程
	}

	mimeType := common.StrPtr2Str(file.MimeType)
	if mimeType == "" {
		mimeType = common.GetMimeType(file.Name)
	}

	return fileData, file.Name, mimeType, nil
}

// 删除文件
func DeleteFile(ctx context.Context, fileID int64) *biz_error.BizError {
	logger := logrus.New()

	// 获取文件信息
	file, err := models.GetDriveFileByID(ctx, fileID)
	if err != nil {
		logger.Errorf("GetDriveFileByID err: %+v", err)
		return err
	}

	// 如果是文件夹，递归删除
	if file.FileType == "folder" {
		if err := models.DeleteDriveFileRecursive(ctx, fileID); err != nil {
			logger.Errorf("DeleteDriveFileRecursive err: %+v", err)
			return err
		}
	} else {
		// 删除物理文件
		storagePath := common.StrPtr2Str(file.StoragePath)
		if storagePath == "" {
			storagePath = common.BuildStoragePath(config.GetStorePath(), file.Path)
		}
		if common.FileExists(storagePath) {
			if err := os.Remove(storagePath); err != nil {
				logger.Warnf("Remove file err: %+v", err)
				// 不影响数据库删除
			}
		}

		// 删除数据库记录
		if err := models.DeleteDriveFile(ctx, fileID); err != nil {
			logger.Errorf("DeleteDriveFile err: %+v", err)
			return err
		}
	}

	return nil
}
