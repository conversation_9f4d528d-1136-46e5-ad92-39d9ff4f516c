package service

import (
	"context"
	"drive-service/biz_error"
	"drive-service/common"
	"drive-service/config"
	"drive-service/models"
	driveGen "thrift-common/gen-go/drive"
	"os"
	"path/filepath"

	"github.com/sirupsen/logrus"
)

// 重命名文件
func RenameFile(ctx context.Context, fileID int64, newName string) (*driveGen.DriveFile, *biz_error.BizError) {
	logger := logrus.New()

	// 验证新文件名
	if !common.IsValidFileName(newName) {
		return nil, biz_error.NewBizError(biz_error.ERR_INVALID_PARAMS)
	}

	// 获取文件信息
	file, err := models.GetDriveFileByID(ctx, fileID)
	if err != nil {
		logger.Errorf("GetDriveFileByID err: %+v", err)
		return nil, err
	}

	// 检查同名文件是否已存在
	exists, err := models.CheckFileNameExists(ctx, file.ParentID, newName, &fileID)
	if err != nil {
		logger.Errorf("CheckFileNameExists err: %+v", err)
		return nil, err
	}
	if exists {
		return nil, biz_error.NewBizError(biz_error.ERR_FILE_ALREADY_EXISTS)
	}

	// 构建新路径
	var newPath string
	if file.ParentID == nil {
		newPath = "/" + newName
	} else {
		parentFile, err := models.GetDriveFileByID(ctx, *file.ParentID)
		if err != nil {
			logger.Errorf("GetDriveFileByID err: %+v", err)
			return nil, err
		}
		newPath = filepath.Join(parentFile.Path, newName)
	}

	// 重命名物理文件/文件夹
	oldStoragePath := common.StrPtr2Str(file.StoragePath)
	if oldStoragePath == "" {
		oldStoragePath = common.BuildStoragePath(config.GetStorePath(), file.Path)
	}
	newStoragePath := common.BuildStoragePath(config.GetStorePath(), newPath)

	if common.FileExists(oldStoragePath) {
		if err := os.Rename(oldStoragePath, newStoragePath); err != nil {
			logger.Errorf("Rename file err: %+v", err)
			return nil, biz_error.NewBizErrorByError(err)
		}
	}

	// 更新数据库记录
	file.Name = newName
	file.Path = common.CleanPath(newPath)
	file.StoragePath = common.Str2StrPtr(newStoragePath)

	// 更新MIME类型（如果是文件）
	if file.FileType == "file" {
		mimeType := common.GetMimeType(newName)
		file.MimeType = common.Str2StrPtr(mimeType)
	}

	if err := models.UpdateDriveFile(ctx, file); err != nil {
		logger.Errorf("UpdateDriveFile err: %+v", err)
		// 尝试回滚文件重命名
		if common.FileExists(newStoragePath) {
			os.Rename(newStoragePath, oldStoragePath)
		}
		return nil, err
	}

	return &driveGen.DriveFile{
		ID:            file.ID,
		Name:          file.Name,
		Path:          file.Path,
		ParentID:      file.ParentID,
		FileType:      file.FileType,
		MimeType:      file.MimeType,
		Size:          file.Size,
		Hash:          file.Hash,
		StoragePath:   file.StoragePath,
		IsPublic:      file.IsPublic,
		DownloadCount: file.DownloadCount,
		CreatedAt:     common.FormatTime(file.CreatedAt),
		UpdatedAt:     common.FormatTime(file.UpdatedAt),
	}, nil
}

// 移动文件
func MoveFile(ctx context.Context, fileID int64, newParentID *int64) (*driveGen.DriveFile, *biz_error.BizError) {
	logger := logrus.New()

	// 获取文件信息
	file, err := models.GetDriveFileByID(ctx, fileID)
	if err != nil {
		logger.Errorf("GetDriveFileByID err: %+v", err)
		return nil, err
	}

	// 检查目标父目录
	if newParentID != nil {
		parentFile, err := models.GetDriveFileByID(ctx, *newParentID)
		if err != nil {
			logger.Errorf("GetDriveFileByID err: %+v", err)
			return nil, err
		}
		if parentFile.FileType != "folder" {
			return nil, biz_error.NewBizError(biz_error.ERR_INVALID_PARAMS)
		}
		
		// 防止将文件夹移动到自己的子目录中
		if file.FileType == "folder" && isSubDirectory(ctx, fileID, *newParentID) {
			return nil, biz_error.NewBizError(biz_error.ERR_INVALID_PARAMS)
		}
	}

	// 检查目标位置是否已有同名文件
	exists, err := models.CheckFileNameExists(ctx, newParentID, file.Name, &fileID)
	if err != nil {
		logger.Errorf("CheckFileNameExists err: %+v", err)
		return nil, err
	}
	if exists {
		return nil, biz_error.NewBizError(biz_error.ERR_FILE_ALREADY_EXISTS)
	}

	// 构建新路径
	var newPath string
	if newParentID == nil {
		newPath = "/" + file.Name
	} else {
		parentFile, err := models.GetDriveFileByID(ctx, *newParentID)
		if err != nil {
			logger.Errorf("GetDriveFileByID err: %+v", err)
			return nil, err
		}
		newPath = filepath.Join(parentFile.Path, file.Name)
	}

	// 移动物理文件/文件夹
	oldStoragePath := common.StrPtr2Str(file.StoragePath)
	if oldStoragePath == "" {
		oldStoragePath = common.BuildStoragePath(config.GetStorePath(), file.Path)
	}
	newStoragePath := common.BuildStoragePath(config.GetStorePath(), newPath)

	// 确保目标目录存在
	newStorageDir := filepath.Dir(newStoragePath)
	if err := common.EnsureDir(newStorageDir); err != nil {
		logger.Errorf("EnsureDir err: %+v", err)
		return nil, biz_error.NewBizErrorByError(err)
	}

	if common.FileExists(oldStoragePath) {
		if err := os.Rename(oldStoragePath, newStoragePath); err != nil {
			logger.Errorf("Rename file err: %+v", err)
			return nil, biz_error.NewBizErrorByError(err)
		}
	}

	// 更新数据库记录
	file.ParentID = newParentID
	file.Path = common.CleanPath(newPath)
	file.StoragePath = common.Str2StrPtr(newStoragePath)

	if err := models.UpdateDriveFile(ctx, file); err != nil {
		logger.Errorf("UpdateDriveFile err: %+v", err)
		// 尝试回滚文件移动
		if common.FileExists(newStoragePath) {
			os.Rename(newStoragePath, oldStoragePath)
		}
		return nil, err
	}

	return &driveGen.DriveFile{
		ID:            file.ID,
		Name:          file.Name,
		Path:          file.Path,
		ParentID:      file.ParentID,
		FileType:      file.FileType,
		MimeType:      file.MimeType,
		Size:          file.Size,
		Hash:          file.Hash,
		StoragePath:   file.StoragePath,
		IsPublic:      file.IsPublic,
		DownloadCount: file.DownloadCount,
		CreatedAt:     common.FormatTime(file.CreatedAt),
		UpdatedAt:     common.FormatTime(file.UpdatedAt),
	}, nil
}

// 检查是否为子目录（防止循环移动）
func isSubDirectory(ctx context.Context, parentID, childID int64) bool {
	if parentID == childID {
		return true
	}

	child, err := models.GetDriveFileByID(ctx, childID)
	if err != nil || child.ParentID == nil {
		return false
	}

	return isSubDirectory(ctx, parentID, *child.ParentID)
}
