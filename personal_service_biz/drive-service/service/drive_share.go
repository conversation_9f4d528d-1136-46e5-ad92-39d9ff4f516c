package service

import (
	"context"
	"drive-service/biz_error"
	"drive-service/common"
	"drive-service/config"
	"drive-service/models"
	driveGen "thrift-common/gen-go/drive"
	"io/ioutil"
	"syscall"
	"time"

	"github.com/sirupsen/logrus"
)

// 创建分享
func CreateShare(ctx context.Context, fileID int64, password, expireTime string, downloadLimit int64) (*driveGen.DriveShare, *biz_error.BizError) {
	logger := logrus.New()

	// 检查文件是否存在
	_, err := models.GetDriveFileByID(ctx, fileID)
	if err != nil {
		logger.Errorf("GetDriveFileByID err: %+v", err)
		return nil, err
	}

	// 生成分享码
	shareCode := common.GenerateShareCode()

	// 解析过期时间
	var expireTimePtr *time.Time
	if expireTime != "" {
		if t, err := time.Parse("2006-01-02 15:04:05", expireTime); err == nil {
			expireTimePtr = &t
		} else {
			return nil, biz_error.NewBizError(biz_error.ERR_INVALID_PARAMS)
		}
	}

	// 创建分享记录
	share := &models.DriveShare{
		FileID:        fileID,
		ShareCode:     shareCode,
		Password:      common.Str2StrPtr(password),
		ExpireTime:    expireTimePtr,
		DownloadLimit: downloadLimit,
	}

	if err := models.CreateDriveShare(ctx, share); err != nil {
		logger.Errorf("CreateDriveShare err: %+v", err)
		return nil, err
	}

	expireTimeStr := ""
	if share.ExpireTime != nil {
		expireTimeStr = common.FormatTime(*share.ExpireTime)
	}

	return &driveGen.DriveShare{
		ID:            share.ID,
		FileID:        share.FileID,
		ShareCode:     share.ShareCode,
		Password:      share.Password,
		ExpireTime:    common.Str2StrPtr(expireTimeStr),
		DownloadLimit: share.DownloadLimit,
		DownloadCount: share.DownloadCount,
		IsActive:      share.IsActive,
		CreatedAt:     common.FormatTime(share.CreatedAt),
	}, nil
}

// 获取分享信息
func GetShareInfo(ctx context.Context, shareCode, password string) (*driveGen.DriveShare, *driveGen.DriveFile, *biz_error.BizError) {
	logger := logrus.New()

	// 获取分享记录
	share, err := models.GetDriveShareByCode(ctx, shareCode)
	if err != nil {
		logger.Errorf("GetDriveShareByCode err: %+v", err)
		return nil, nil, err
	}

	// 检查分享是否有效
	if !share.IsValid() {
		if share.ExpireTime != nil && time.Now().After(*share.ExpireTime) {
			return nil, nil, biz_error.NewBizError(biz_error.ERR_SHARE_EXPIRED)
		}
		return nil, nil, biz_error.NewBizError(biz_error.ERR_SHARE_NOT_FOUND)
	}

	// 检查密码
	if share.Password != nil && common.StrPtr2Str(share.Password) != password {
		return nil, nil, biz_error.NewBizError(biz_error.ERR_SHARE_PASSWORD_WRONG)
	}

	// 获取文件信息
	file, err := models.GetDriveFileByID(ctx, share.FileID)
	if err != nil {
		logger.Errorf("GetDriveFileByID err: %+v", err)
		return nil, nil, err
	}

	expireTimeStr := ""
	if share.ExpireTime != nil {
		expireTimeStr = common.FormatTime(*share.ExpireTime)
	}

	shareResult := &driveGen.DriveShare{
		ID:            share.ID,
		FileID:        share.FileID,
		ShareCode:     share.ShareCode,
		Password:      share.Password,
		ExpireTime:    common.Str2StrPtr(expireTimeStr),
		DownloadLimit: share.DownloadLimit,
		DownloadCount: share.DownloadCount,
		IsActive:      share.IsActive,
		CreatedAt:     common.FormatTime(share.CreatedAt),
	}

	fileResult := &driveGen.DriveFile{
		ID:            file.ID,
		Name:          file.Name,
		Path:          file.Path,
		ParentID:      file.ParentID,
		FileType:      file.FileType,
		MimeType:      file.MimeType,
		Size:          file.Size,
		Hash:          file.Hash,
		StoragePath:   file.StoragePath,
		IsPublic:      file.IsPublic,
		DownloadCount: file.DownloadCount,
		CreatedAt:     common.FormatTime(file.CreatedAt),
		UpdatedAt:     common.FormatTime(file.UpdatedAt),
	}

	return shareResult, fileResult, nil
}

// 通过分享下载文件
func DownloadSharedFile(ctx context.Context, shareCode, password string) ([]byte, string, string, *biz_error.BizError) {
	logger := logrus.New()

	// 获取分享信息
	share, file, err := GetShareInfo(ctx, shareCode, password)
	if err != nil {
		return nil, "", "", err
	}

	// 只能下载文件，不能下载文件夹
	if file.FileType != "file" {
		return nil, "", "", biz_error.NewBizError(biz_error.ERR_INVALID_PARAMS)
	}

	// 读取文件内容
	storagePath := common.StrPtr2Str(file.StoragePath)
	if storagePath == "" {
		storagePath = common.BuildStoragePath(config.GetStorePath(), file.Path)
	}

	fileData, readErr := ioutil.ReadFile(storagePath)
	if readErr != nil {
		logger.Errorf("ReadFile err: %+v", readErr)
		return nil, "", "", biz_error.NewBizError(biz_error.ERR_DOWNLOAD_FAILED)
	}

	// 增加分享下载次数
	if err := models.IncrementShareDownloadCount(ctx, share.ID); err != nil {
		logger.Warnf("IncrementShareDownloadCount err: %+v", err)
		// 不影响下载流程
	}

	// 增加文件下载次数
	if err := models.IncrementDownloadCount(ctx, file.ID); err != nil {
		logger.Warnf("IncrementDownloadCount err: %+v", err)
		// 不影响下载流程
	}

	mimeType := common.StrPtr2Str(file.MimeType)
	if mimeType == "" {
		mimeType = common.GetMimeType(file.Name)
	}

	return fileData, file.Name, mimeType, nil
}

// 获取存储信息
func GetStorageInfo(ctx context.Context) (int64, int64, int64, float64, *biz_error.BizError) {
	logger := logrus.New()

	storagePath := config.GetStorePath()

	// 获取磁盘使用情况
	var stat syscall.Statfs_t
	if err := syscall.Statfs(storagePath, &stat); err != nil {
		logger.Errorf("Statfs err: %+v", err)
		return 0, 0, 0, 0, biz_error.NewBizErrorByError(err)
	}

	// 计算存储空间信息
	blockSize := int64(stat.Bsize)
	totalSpace := int64(stat.Blocks) * blockSize
	freeSpace := int64(stat.Bavail) * blockSize
	usedSpace := totalSpace - freeSpace

	// 计算使用百分比
	var usagePercent float64
	if totalSpace > 0 {
		usagePercent = float64(usedSpace) / float64(totalSpace) * 100
	}

	return totalSpace, usedSpace, freeSpace, usagePercent, nil
}
