package service

import (
	"context"
	"drive-service/biz_error"
	"drive-service/common"
	"drive-service/config"
	"drive-service/models"
	driveGen "thrift-common/gen-go/drive"
	driveClient "thrift-common/client/drive"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"

	"github.com/sirupsen/logrus"
)

// 获取Drive服务客户端
func GetDriveClient() (*driveGen.DriveClient, error) {
	return driveClient.GetDriveClient()
}

// 初始化分片上传
func InitChunkUpload(ctx context.Context, fileName string, fileSize int64, parentID *int64, chunkSize int64) (*driveGen.DriveUploadTask, *biz_error.BizError) {
	logger := logrus.New()

	// 验证文件名
	if !common.IsValidFileName(fileName) {
		return nil, biz_error.NewBizError(biz_error.ERR_INVALID_PARAMS)
	}

	// 检查同名文件是否已存在
	exists, err := models.CheckFileNameExists(ctx, parentID, fileName, nil)
	if err != nil {
		logger.Errorf("CheckFileNameExists err: %+v", err)
		return nil, err
	}
	if exists {
		return nil, biz_error.NewBizError(biz_error.ERR_FILE_ALREADY_EXISTS)
	}

	// 设置默认分片大小
	if chunkSize <= 0 {
		chunkSize = 1048576 // 1MB
	}

	// 计算总分片数
	totalChunks := (fileSize + chunkSize - 1) / chunkSize

	// 构建上传路径
	var uploadPath string
	if parentID == nil {
		uploadPath = "/" + fileName
	} else {
		parentFile, err := models.GetDriveFileByID(ctx, *parentID)
		if err != nil {
			logger.Errorf("GetDriveFileByID err: %+v", err)
			return nil, err
		}
		if parentFile.FileType != "folder" {
			return nil, biz_error.NewBizError(biz_error.ERR_INVALID_PARAMS)
		}
		uploadPath = filepath.Join(parentFile.Path, fileName)
	}

	// 创建临时目录
	tempDir := filepath.Join(config.GetStorePath(), ".temp", fmt.Sprintf("upload_%s", common.GenerateRandomString(8)))
	if err := common.EnsureDir(tempDir); err != nil {
		logger.Errorf("EnsureDir err: %+v", err)
		return nil, biz_error.NewBizErrorByError(err)
	}

	// 创建上传任务
	task := &models.DriveUploadTask{
		FileName:    fileName,
		FileSize:    fileSize,
		ChunkSize:   chunkSize,
		TotalChunks: totalChunks,
		UploadPath:  uploadPath,
		TempDir:     tempDir,
	}

	if err := models.CreateDriveUploadTask(ctx, task); err != nil {
		logger.Errorf("CreateDriveUploadTask err: %+v", err)
		// 清理临时目录
		os.RemoveAll(tempDir)
		return nil, err
	}

	return &driveGen.DriveUploadTask{
		ID:             task.ID,
		FileName:       task.FileName,
		FileSize:       task.FileSize,
		ChunkSize:      task.ChunkSize,
		TotalChunks:    task.TotalChunks,
		UploadedChunks: task.UploadedChunks,
		UploadPath:     task.UploadPath,
		TempDir:        task.TempDir,
		Status:         task.Status,
		ErrorMessage:   task.ErrorMessage,
		CreatedAt:      common.FormatTime(task.CreatedAt),
	}, nil
}

// 上传分片
func UploadChunk(ctx context.Context, taskID int64, chunkIndex int64, chunkData []byte) (*driveGen.DriveUploadTask, *biz_error.BizError) {
	logger := logrus.New()

	// 获取上传任务
	task, err := models.GetDriveUploadTaskByID(ctx, taskID)
	if err != nil {
		logger.Errorf("GetDriveUploadTaskByID err: %+v", err)
		return nil, err
	}

	// 检查任务状态
	if task.Status == models.UploadStatusCompleted {
		return nil, biz_error.NewBizError(biz_error.ERR_INVALID_PARAMS)
	}
	if task.Status == models.UploadStatusFailed {
		return nil, biz_error.NewBizError(biz_error.ERR_UPLOAD_FAILED)
	}

	// 验证分片索引
	if chunkIndex < 0 || chunkIndex >= task.TotalChunks {
		return nil, biz_error.NewBizError(biz_error.ERR_INVALID_PARAMS)
	}

	// 保存分片文件
	chunkFileName := fmt.Sprintf("chunk_%d", chunkIndex)
	chunkFilePath := filepath.Join(task.TempDir, chunkFileName)

	if err := ioutil.WriteFile(chunkFilePath, chunkData, 0644); err != nil {
		logger.Errorf("WriteFile err: %+v", err)
		// 标记任务失败
		models.MarkUploadTaskFailed(ctx, taskID, err.Error())
		return nil, biz_error.NewBizError(biz_error.ERR_UPLOAD_FAILED)
	}

	// 更新上传进度
	uploadedChunks := task.UploadedChunks + 1
	if err := models.UpdateUploadProgress(ctx, taskID, uploadedChunks); err != nil {
		logger.Errorf("UpdateUploadProgress err: %+v", err)
		return nil, err
	}

	// 计算上传进度
	uploadedBytes := uploadedChunks * task.ChunkSize
	if uploadedChunks == task.TotalChunks && task.FileSize%task.ChunkSize != 0 {
		// 最后一个分片可能不是完整的ChunkSize
		uploadedBytes = task.FileSize
	}
	progress := float64(uploadedBytes) / float64(task.FileSize) * 100

	// 发送进度回调
	progressReq := &driveGen.UploadProgressRequest{
		TaskID:        taskID,
		UploadedBytes: uploadedBytes,
		TotalBytes:    task.FileSize,
		Progress:      progress,
		Base:          nil,  // 传递原始请求的Base信息
	}
	
	if _, err := GetDriveClient().UploadProgress(ctx, progressReq); err != nil {
		logger.Warnf("UploadProgress callback failed: %+v", err)
		// 进度回调失败不影响上传流程
	}

	// 重新获取任务信息
	task, err = models.GetDriveUploadTaskByID(ctx, taskID)
	if err != nil {
		logger.Errorf("GetDriveUploadTaskByID err: %+v", err)
		return nil, err
	}

	return &driveGen.DriveUploadTask{
		ID:             task.ID,
		FileName:       task.FileName,
		FileSize:       task.FileSize,
		ChunkSize:      task.ChunkSize,
		TotalChunks:    task.TotalChunks,
		UploadedChunks: task.UploadedChunks,
		UploadPath:     task.UploadPath,
		TempDir:        task.TempDir,
		Status:         task.Status,
		ErrorMessage:   task.ErrorMessage,
		CreatedAt:      common.FormatTime(task.CreatedAt),
	}, nil
}

// 完成分片上传
func CompleteChunkUpload(ctx context.Context, taskID int64) (*driveGen.DriveFile, *biz_error.BizError) {
	logger := logrus.New()

	// 获取上传任务
	task, err := models.GetDriveUploadTaskByID(ctx, taskID)
	if err != nil {
		logger.Errorf("GetDriveUploadTaskByID err: %+v", err)
		return nil, err
	}

	// 检查是否所有分片都已上传
	if task.UploadedChunks != task.TotalChunks {
		return nil, biz_error.NewBizError(biz_error.ERR_UPLOAD_FAILED)
	}

	// 合并分片文件
	finalPath := common.BuildStoragePath(config.GetStorePath(), task.UploadPath)
	finalDir := filepath.Dir(finalPath)
	if err := common.EnsureDir(finalDir); err != nil {
		logger.Errorf("EnsureDir err: %+v", err)
		models.MarkUploadTaskFailed(ctx, taskID, err.Error())
		return nil, biz_error.NewBizErrorByError(err)
	}

	finalFile, createErr := os.Create(finalPath)
	if createErr != nil {
		logger.Errorf("Create file err: %+v", createErr)
		models.MarkUploadTaskFailed(ctx, taskID, createErr.Error())
		return nil, biz_error.NewBizErrorByError(createErr)
	}
	defer finalFile.Close()

	// 按顺序合并分片
	for i := int64(0); i < task.TotalChunks; i++ {
		chunkFileName := fmt.Sprintf("chunk_%d", i)
		chunkFilePath := filepath.Join(task.TempDir, chunkFileName)

		chunkData, err := ioutil.ReadFile(chunkFilePath)
		if err != nil {
			logger.Errorf("ReadFile chunk err: %+v", err)
			models.MarkUploadTaskFailed(ctx, taskID, err.Error())
			os.Remove(finalPath)
			return nil, biz_error.NewBizError(biz_error.ERR_UPLOAD_FAILED)
		}

		if _, err := finalFile.Write(chunkData); err != nil {
			logger.Errorf("Write chunk err: %+v", err)
			models.MarkUploadTaskFailed(ctx, taskID, err.Error())
			os.Remove(finalPath)
			return nil, biz_error.NewBizError(biz_error.ERR_UPLOAD_FAILED)
		}
	}

	// 计算文件哈希
	hash, hashErr := common.CalculateFileHash(finalPath)
	if hashErr != nil {
		logger.Errorf("CalculateFileHash err: %+v", hashErr)
		hash = ""
	}

	// 获取MIME类型
	mimeType := common.GetMimeType(task.FileName)

	// 解析父目录ID
	var parentID *int64
	if task.UploadPath != "/"+task.FileName {
		parentPath := filepath.Dir(task.UploadPath)
		if parentPath != "/" {
			parentFile, err := models.GetDriveFileByPath(ctx, parentPath)
			if err == nil {
				parentID = &parentFile.ID
			}
		}
	}

	// 创建文件记录
	file := &models.DriveFile{
		Name:        task.FileName,
		Path:        common.CleanPath(task.UploadPath),
		ParentID:    parentID,
		FileType:    "file",
		MimeType:    common.Str2StrPtr(mimeType),
		Size:        task.FileSize,
		Hash:        common.Str2StrPtr(hash),
		StoragePath: common.Str2StrPtr(finalPath),
		IsPublic:    false,
	}

	if err := models.CreateDriveFile(ctx, file); err != nil {
		logger.Errorf("CreateDriveFile err: %+v", err)
		models.MarkUploadTaskFailed(ctx, taskID, err.Error())
		os.Remove(finalPath)
		return nil, err
	}

	// 标记任务完成
	if err := models.MarkUploadTaskCompleted(ctx, taskID); err != nil {
		logger.Errorf("MarkUploadTaskCompleted err: %+v", err)
		// 不影响文件创建结果
	}

	// 清理临时文件
	go func() {
		os.RemoveAll(task.TempDir)
	}()

	// 尝试生成缩略图（异步，不影响文件上传结果）
	go func() {
		if IsThumbnailSupported(mimeType) {
			thumbnailPath, thumbnailErr := GenerateThumbnail(context.Background(), file.ID, DefaultThumbnailWidth, DefaultThumbnailHeight)
			if thumbnailErr == nil {
				file.ThumbnailPath = common.Str2StrPtr(thumbnailPath)
				models.UpdateDriveFile(context.Background(), file)
			}
		}
	}()

	return &driveGen.DriveFile{
		ID:            file.ID,
		Name:          file.Name,
		Path:          file.Path,
		ParentID:      file.ParentID,
		FileType:      file.FileType,
		MimeType:      file.MimeType,
		Size:          file.Size,
		Hash:          file.Hash,
		StoragePath:   file.StoragePath,
		ThumbnailPath: file.ThumbnailPath,
		IsPublic:      file.IsPublic,
		DownloadCount: file.DownloadCount,
		CreatedAt:     common.FormatTime(file.CreatedAt),
		UpdatedAt:     common.FormatTime(file.UpdatedAt),
	}, nil
}
