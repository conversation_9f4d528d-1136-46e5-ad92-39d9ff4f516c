package service

import (
	"context"
	"drive-service/common"
	driveGen "thrift-common/gen-go/drive"
	"thrift-common/gen-go/base"

	"github.com/sirupsen/logrus"
)

const (
	DefaultThumbnailWidth  = 200
	DefaultThumbnailHeight = 200
)

// 定义服务
type DriveService struct {
}

// 获取文件列表
func (s *DriveService) GetFileList(ctx context.Context, req *driveGen.GetFileListRequest) (*driveGen.GetFileListResponse, error) {
	logger := logrus.New()
	logger.Infof("GetFileList req: %+v", req)

	files, total, err := GetFileList(ctx, req.ParentID, req.Offset, req.Count, common.StrPtr2Str(req.Keyword))
	if err != nil {
		return &driveGen.GetFileListResponse{
			BaseResp: &base.BaseResponse{
				StatusCode:    int64(err.Code()),
				StatusMessage: err.Msg(),
			},
		}, nil
	}

	hasMore := req.Offset+req.Count < total

	return &driveGen.GetFileListResponse{
		FileList: files,
		Total:    total,
		HasMore:  hasMore,
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}

// 创建文件夹
func (s *DriveService) CreateFolder(ctx context.Context, req *driveGen.CreateFolderRequest) (*driveGen.CreateFolderResponse, error) {
	logger := logrus.New()
	logger.Infof("CreateFolder req: %+v", req)

	folder, err := CreateFolder(ctx, req.Name, req.ParentID)
	if err != nil {
		return &driveGen.CreateFolderResponse{
			BaseResp: &base.BaseResponse{
				StatusCode:    int64(err.Code()),
				StatusMessage: err.Msg(),
			},
		}, nil
	}

	return &driveGen.CreateFolderResponse{
		Folder: folder,
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}

// 上传文件
func (s *DriveService) UploadFile(ctx context.Context, req *driveGen.UploadFileRequest) (*driveGen.UploadFileResponse, error) {
	logger := logrus.New()
	logger.Infof("UploadFile req: fileName=%s, fileSize=%d", req.FileName, req.FileSize)

	file, err := UploadFile(ctx, req.FileName, req.FileSize, req.ParentID, req.FileData)
	if err != nil {
		return &driveGen.UploadFileResponse{
			BaseResp: &base.BaseResponse{
				StatusCode:    int64(err.Code()),
				StatusMessage: err.Msg(),
			},
		}, nil
	}

	return &driveGen.UploadFileResponse{
		File: file,
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}

// 下载文件
func (s *DriveService) DownloadFile(ctx context.Context, req *driveGen.DownloadFileRequest) (*driveGen.DownloadFileResponse, error) {
	logger := logrus.New()
	logger.Infof("DownloadFile req: %+v", req)

	fileData, fileName, mimeType, err := DownloadFile(ctx, req.FileID)
	if err != nil {
		return &driveGen.DownloadFileResponse{
			BaseResp: &base.BaseResponse{
				StatusCode:    int64(err.Code()),
				StatusMessage: err.Msg(),
			},
		}, nil
	}

	return &driveGen.DownloadFileResponse{
		FileData: fileData,
		FileName: fileName,
		MimeType: mimeType,
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}

// 删除文件
func (s *DriveService) DeleteFile(ctx context.Context, req *driveGen.DeleteFileRequest) (*driveGen.DeleteFileResponse, error) {
	logger := logrus.New()
	logger.Infof("DeleteFile req: %+v", req)

	err := DeleteFile(ctx, req.FileID)
	if err != nil {
		return &driveGen.DeleteFileResponse{
			BaseResp: &base.BaseResponse{
				StatusCode:    int64(err.Code()),
				StatusMessage: err.Msg(),
			},
		}, nil
	}

	return &driveGen.DeleteFileResponse{
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}

// 重命名文件
func (s *DriveService) RenameFile(ctx context.Context, req *driveGen.RenameFileRequest) (*driveGen.RenameFileResponse, error) {
	logger := logrus.New()
	logger.Infof("RenameFile req: %+v", req)

	file, err := RenameFile(ctx, req.FileID, req.NewName_)
	if err != nil {
		return &driveGen.RenameFileResponse{
			BaseResp: &base.BaseResponse{
				StatusCode:    int64(err.Code()),
				StatusMessage: err.Msg(),
			},
		}, nil
	}

	return &driveGen.RenameFileResponse{
		File: file,
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}

// 移动文件
func (s *DriveService) MoveFile(ctx context.Context, req *driveGen.MoveFileRequest) (*driveGen.MoveFileResponse, error) {
	logger := logrus.New()
	logger.Infof("MoveFile req: %+v", req)

	file, err := MoveFile(ctx, req.FileID, req.NewParentID_)
	if err != nil {
		return &driveGen.MoveFileResponse{
			BaseResp: &base.BaseResponse{
				StatusCode:    int64(err.Code()),
				StatusMessage: err.Msg(),
			},
		}, nil
	}

	return &driveGen.MoveFileResponse{
		File: file,
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}

// 获取存储信息
func (s *DriveService) GetStorageInfo(ctx context.Context, req *driveGen.GetStorageInfoRequest) (*driveGen.GetStorageInfoResponse, error) {
	logger := logrus.New()
	logger.Infof("GetStorageInfo req: %+v", req)

	totalSpace, usedSpace, freeSpace, usagePercent, err := GetStorageInfo(ctx)
	if err != nil {
		return &driveGen.GetStorageInfoResponse{
			BaseResp: &base.BaseResponse{
				StatusCode:    int64(err.Code()),
				StatusMessage: err.Msg(),
			},
		}, nil
	}

	return &driveGen.GetStorageInfoResponse{
		TotalSpace:    totalSpace,
		UsedSpace:     usedSpace,
		FreeSpace:     freeSpace,
		UsagePercent:  usagePercent,
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}

// 获取缩略图
func (s *DriveService) GetThumbnail(ctx context.Context, req *driveGen.GetThumbnailRequest) (*driveGen.GetThumbnailResponse, error) {
	logger := logrus.New()
	logger.Infof("GetThumbnail req: %+v", req)

	width := DefaultThumbnailWidth
	height := DefaultThumbnailHeight
	if req.Width != nil {
		width = int(*req.Width)
	}
	if req.Height != nil {
		height = int(*req.Height)
	}

	thumbnailData, mimeType, err := GetThumbnail(ctx, req.FileID, width, height)
	if err != nil {
		return &driveGen.GetThumbnailResponse{
			BaseResp: &base.BaseResponse{
				StatusCode:    int64(err.Code()),
				StatusMessage: err.Msg(),
			},
		}, nil
	}

	return &driveGen.GetThumbnailResponse{
		ThumbnailData: thumbnailData,
		MimeType:      mimeType,
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}

// 生成缩略图
func (s *DriveService) GenerateThumbnail(ctx context.Context, req *driveGen.GenerateThumbnailRequest) (*driveGen.GenerateThumbnailResponse, error) {
	logger := logrus.New()
	logger.Infof("GenerateThumbnail req: %+v", req)

	width := DefaultThumbnailWidth
	height := DefaultThumbnailHeight
	if req.Width != nil {
		width = int(*req.Width)
	}
	if req.Height != nil {
		height = int(*req.Height)
	}

	thumbnailPath, err := GenerateThumbnail(ctx, req.FileID, width, height)
	if err != nil {
		return &driveGen.GenerateThumbnailResponse{
			BaseResp: &base.BaseResponse{
				StatusCode:    int64(err.Code()),
				StatusMessage: err.Msg(),
			},
		}, nil
	}

	return &driveGen.GenerateThumbnailResponse{
		ThumbnailPath: thumbnailPath,
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}

// 初始化分片上传
func (s *DriveService) InitChunkUpload(ctx context.Context, req *driveGen.InitChunkUploadRequest) (*driveGen.InitChunkUploadResponse, error) {
	logger := logrus.New()
	logger.Infof("InitChunkUpload req: %+v", req)

	task, err := InitChunkUpload(ctx, req.FileName, req.FileSize, req.ParentID, req.ChunkSize)
	if err != nil {
		return &driveGen.InitChunkUploadResponse{
			BaseResp: &base.BaseResponse{
				StatusCode:    int64(err.Code()),
				StatusMessage: err.Msg(),
			},
		}, nil
	}

	return &driveGen.InitChunkUploadResponse{
		UploadTask: task,
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}

// 上传分片
func (s *DriveService) UploadChunk(ctx context.Context, req *driveGen.UploadChunkRequest) (*driveGen.UploadChunkResponse, error) {
	logger := logrus.New()
	logger.Infof("UploadChunk req: taskID=%d, chunkIndex=%d", req.TaskID, req.ChunkIndex)

	task, err := UploadChunk(ctx, req.TaskID, req.ChunkIndex, req.ChunkData)
	if err != nil {
		return &driveGen.UploadChunkResponse{
			BaseResp: &base.BaseResponse{
				StatusCode:    int64(err.Code()),
				StatusMessage: err.Msg(),
			},
		}, nil
	}

	return &driveGen.UploadChunkResponse{
		UploadTask: task,
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}

// 完成分片上传
func (s *DriveService) CompleteChunkUpload(ctx context.Context, req *driveGen.CompleteChunkUploadRequest) (*driveGen.CompleteChunkUploadResponse, error) {
	logger := logrus.New()
	logger.Infof("CompleteChunkUpload req: %+v", req)

	file, err := CompleteChunkUpload(ctx, req.TaskID)
	if err != nil {
		return &driveGen.CompleteChunkUploadResponse{
			BaseResp: &base.BaseResponse{
				StatusCode:    int64(err.Code()),
				StatusMessage: err.Msg(),
			},
		}, nil
	}

	return &driveGen.CompleteChunkUploadResponse{
		File: file,
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}

// 创建分享
func (s *DriveService) CreateShare(ctx context.Context, req *driveGen.CreateShareRequest) (*driveGen.CreateShareResponse, error) {
	logger := logrus.New()
	logger.Infof("CreateShare req: %+v", req)

	share, err := CreateShare(ctx, req.FileID, common.StrPtr2Str(req.Password), common.StrPtr2Str(req.ExpireTime), req.DownloadLimit)
	if err != nil {
		return &driveGen.CreateShareResponse{
			BaseResp: &base.BaseResponse{
				StatusCode:    int64(err.Code()),
				StatusMessage: err.Msg(),
			},
		}, nil
	}

	return &driveGen.CreateShareResponse{
		Share: share,
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}

// 获取分享信息
func (s *DriveService) GetShareInfo(ctx context.Context, req *driveGen.GetShareInfoRequest) (*driveGen.GetShareInfoResponse, error) {
	logger := logrus.New()
	logger.Infof("GetShareInfo req: %+v", req)

	share, file, err := GetShareInfo(ctx, req.ShareCode, common.StrPtr2Str(req.Password))
	if err != nil {
		return &driveGen.GetShareInfoResponse{
			BaseResp: &base.BaseResponse{
				StatusCode:    int64(err.Code()),
				StatusMessage: err.Msg(),
			},
		}, nil
	}

	return &driveGen.GetShareInfoResponse{
		Share: share,
		File:  file,
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}

// 通过分享下载文件
func (s *DriveService) DownloadSharedFile(ctx context.Context, req *driveGen.DownloadSharedFileRequest) (*driveGen.DownloadSharedFileResponse, error) {
	logger := logrus.New()
	logger.Infof("DownloadSharedFile req: %+v", req)

	fileData, fileName, mimeType, err := DownloadSharedFile(ctx, req.ShareCode, common.StrPtr2Str(req.Password))
	if err != nil {
		return &driveGen.DownloadSharedFileResponse{
			BaseResp: &base.BaseResponse{
				StatusCode:    int64(err.Code()),
				StatusMessage: err.Msg(),
			},
		}, nil
	}

	return &driveGen.DownloadSharedFileResponse{
		FileData: fileData,
		FileName: fileName,
		MimeType: mimeType,
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}

// 上传进度回调
func (s *DriveService) UploadProgress(ctx context.Context, req *driveGen.UploadProgressRequest) (*driveGen.UploadProgressResponse, error) {
	logger := logrus.New()
	logger.Infof("UploadProgress req: taskID=%d, uploadedBytes=%d, totalBytes=%d, progress=%.2f%%", 
		req.TaskID, req.UploadedBytes, req.TotalBytes, req.Progress)

	return &driveGen.UploadProgressResponse{
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}
