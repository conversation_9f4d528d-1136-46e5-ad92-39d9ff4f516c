package service

import (
	"context"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"strings"

	"github.com/nfnt/resize"
	"github.com/sirupsen/logrus"
	"drive-service/biz_error"
	"drive-service/common"
	"drive-service/config"
	"drive-service/models"
)

const (
	ThumbnailQuality = 80
)

// 判断文件是否支持缩略图生成
func IsThumbnailSupported(mimeType string) bool {
	supportedTypes := []string{
		"image/jpeg", "image/jpg", "image/png", "image/gif", "image/bmp", "image/webp",
		"video/mp4", "video/avi", "video/mov", "video/mkv", "video/webm", "video/flv",
	}
	
	for _, supportedType := range supportedTypes {
		if strings.HasPrefix(mimeType, supportedType) {
			return true
		}
	}
	return false
}

// 生成图片缩略图
func generateImageThumbnail(sourcePath, thumbnailPath string, width, height int) error {
	// 打开源图片文件
	file, err := os.Open(sourcePath)
	if err != nil {
		return err
	}
	defer file.Close()

	// 解码图片
	img, format, err := image.Decode(file)
	if err != nil {
		return err
	}

	// 调整图片大小
	thumbnail := resize.Resize(uint(width), uint(height), img, resize.Lanczos3)

	// 创建缩略图文件
	thumbnailFile, err := os.Create(thumbnailPath)
	if err != nil {
		return err
	}
	defer thumbnailFile.Close()

	// 根据原图格式保存缩略图
	switch format {
	case "jpeg", "jpg":
		return jpeg.Encode(thumbnailFile, thumbnail, &jpeg.Options{Quality: ThumbnailQuality})
	case "png":
		return png.Encode(thumbnailFile, thumbnail)
	default:
		// 默认保存为JPEG
		return jpeg.Encode(thumbnailFile, thumbnail, &jpeg.Options{Quality: ThumbnailQuality})
	}
}

// 生成视频缩略图
func generateVideoThumbnail(sourcePath, thumbnailPath string, width, height int) error {
	// 使用ffmpeg生成视频缩略图
	cmd := exec.Command("ffmpeg", 
		"-i", sourcePath,
		"-ss", "00:00:01.000",  // 从第1秒开始截取
		"-vframes", "1",        // 只截取1帧
		"-vf", fmt.Sprintf("scale=%d:%d", width, height),
		"-y",                   // 覆盖输出文件
		thumbnailPath,
	)
	
	return cmd.Run()
}

// 生成缩略图
func GenerateThumbnail(ctx context.Context, fileID int64, width, height int) (string, *biz_error.BizError) {
	logger := logrus.New()

	// 设置默认尺寸
	if width <= 0 {
		width = DefaultThumbnailWidth
	}
	if height <= 0 {
		height = DefaultThumbnailHeight
	}

	// 获取文件信息
	file, err := models.GetDriveFileByID(ctx, fileID)
	if err != nil {
		logger.Errorf("GetDriveFileByID err: %+v", err)
		return "", err
	}

	if file.FileType != "file" {
		return "", biz_error.NewBizError(biz_error.ERR_INVALID_PARAMS)
	}

	// 检查文件类型是否支持缩略图
	mimeType := common.StrPtr2Str(file.MimeType)
	if mimeType == "" {
		mimeType = common.GetMimeType(file.Name)
	}
	
	if !IsThumbnailSupported(mimeType) {
		return "", biz_error.NewBizError(biz_error.ERR_UNSUPPORTED_FILE_TYPE)
	}

	// 获取源文件路径
	sourcePath := common.StrPtr2Str(file.StoragePath)
	if sourcePath == "" {
		sourcePath = common.BuildStoragePath(config.GetStorePath(), file.Path)
	}

	// 检查源文件是否存在
	if !common.FileExists(sourcePath) {
		return "", biz_error.NewBizError(biz_error.ERR_FILE_NOT_FOUND)
	}

	// 构建缩略图路径
	thumbnailDir := filepath.Join(config.GetStorePath(), ".thumbnails")
	if err := common.EnsureDir(thumbnailDir); err != nil {
		logger.Errorf("EnsureDir err: %+v", err)
		return "", biz_error.NewBizErrorByError(err)
	}

	thumbnailFileName := fmt.Sprintf("%d_%dx%d.jpg", fileID, width, height)
	thumbnailPath := filepath.Join(thumbnailDir, thumbnailFileName)

	// 如果缩略图已存在，直接返回
	if common.FileExists(thumbnailPath) {
		return thumbnailPath, nil
	}

	// 根据文件类型生成缩略图
	var generateErr error
	if strings.HasPrefix(mimeType, "image/") {
		generateErr = generateImageThumbnail(sourcePath, thumbnailPath, width, height)
	} else if strings.HasPrefix(mimeType, "video/") {
		generateErr = generateVideoThumbnail(sourcePath, thumbnailPath, width, height)
	} else {
		return "", biz_error.NewBizError(biz_error.ERR_UNSUPPORTED_FILE_TYPE)
	}

	if generateErr != nil {
		logger.Errorf("Generate thumbnail err: %+v", generateErr)
		return "", biz_error.NewBizErrorByError(generateErr)
	}

	// 更新数据库记录
	file.ThumbnailPath = common.Str2StrPtr(thumbnailPath)
	if updateErr := models.UpdateDriveFile(ctx, file); updateErr != nil {
		logger.Errorf("UpdateDriveFile err: %+v", updateErr)
		// 不影响缩略图生成结果，只记录日志
	}

	return thumbnailPath, nil
}

// 获取缩略图
func GetThumbnail(ctx context.Context, fileID int64, width, height int) ([]byte, string, *biz_error.BizError) {
	logger := logrus.New()

	// 先尝试生成缩略图
	thumbnailPath, err := GenerateThumbnail(ctx, fileID, width, height)
	if err != nil {
		return nil, "", err
	}

	// 读取缩略图文件
	thumbnailData, readErr := ioutil.ReadFile(thumbnailPath)
	if readErr != nil {
		logger.Errorf("ReadFile err: %+v", readErr)
		return nil, "", biz_error.NewBizError(biz_error.ERR_FILE_READ_FAILED)
	}

	// 返回缩略图数据和MIME类型
	return thumbnailData, "image/jpeg", nil
}
