package background

import (
	"context"
	"time"

	"favorites-service/common"
	"favorites-service/models"
	"favorites-service/mq"
	"favorites-service/service"

	"github.com/sirupsen/logrus"
)

func ScanPendingTask(ctx context.Context) {
	// TODO 所有涉及DB业务操作的逻辑，应该判断localserver模式不直接操作本地DB，要获取cloudDB，最后再以数据同步的方式进行同步
	logger := logrus.New()
	retryTimes := 0
	for {
		records, err := models.SelectCollectRecordListByStatus(ctx, models.GetDB(), common.CollectRecordStatusPending, 50)
		if err != nil {
			logger.Errorf("retryTimes[%d] SelectCollectRecordListByStatus err[%+v]", retryTimes, err)
			retryTimes += 1
			if retryTimes > 5 {
				logger.Errorf("retryTimes[%d] reach max, exit loop...", retryTimes)
				break
			}
			time.Sleep(10 * time.Second)
			continue
		}
		if len(records) == 0 {
			break
		}

		for _, r := range records {
			mq.SendPullResourceTaskMsg(ctx, r, common.TaskSourceTypeBackground)
			time.Sleep(10 * time.Second)
		}
		break
	}
}

func convertAllExistShortUrl(ctx context.Context) {
	logger := logrus.New()
	records, e := models.SelectCollectRecordList(0, 10000)
	if e != nil {
		logger.Errorf("SelectCollectRecordList err[%+v]", e)
	}
	for _, r := range records {
		url, e := service.ConvertShortLink(ctx, r.Origin)
		if e != nil {
			logger.Errorf("ConvertShortLink err[%+v] r[%+v]", e, r)
		}
		r.Origin = url
		er := models.SaveCollectRecord(ctx, models.GetDB(), r)
		if er != nil {
			logger.Errorf("SaveCollectRecord err[%+v] r[%+v]", er, r)
		}
	}
}

func InitBackground(ctx context.Context) error {
	go func() {
		for {
			ScanPendingTask(ctx)
			// 6 hours一次
			time.Sleep(6 * time.Hour)
		}
	}()

	// go func() {
	// 	convertAllExistShortUrl(ctx)
	// }()
	return nil
}
