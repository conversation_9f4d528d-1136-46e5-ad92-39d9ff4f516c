package biz_error

import (
	"favorites-service/common"
	"fmt"

	wErr "github.com/pkg/errors"
	"github.com/sirupsen/logrus"
)

type BIZ_ERROR_CODE int

const (
	// 用户视角的error case
	//  - 1. 业务数据模块相关
	ERR_DUP_TASKID BIZ_ERROR_CODE = 10001
	// 业务模块定义的逻辑error
	ERR_WRONG_ROWS_AFFECTED = 80001

	// 服务级别通用error / 其他不对外暴露的error
	ERR_INTERNAL            = 90001
	ERR_COMMON              = 90002
	ERR_MISS_IMPLEMENT      = 90003 // 缺少逻辑分支
	ERR_INIT_DB_CONN_FAILED = 90004 // 初始化db链接错误
	// 服务级别规范错误
	ERR_NO_LOCALSERVER = 90011 // 仅支持localserver的逻辑，而实际环境为cloudserver模式
	// 数据库/表级别规范错误
	ERR_SYNC_TABLE_NO_EXIST   = 90021 // sync_meta_table参数指定表不存在
	ERR_SYNC_COLUMN_NO_EXIST  = 90022 // sync_meta_table参数指定表缺少对应的规范字段
	ERR_SYNC_UNSUPPORT_STATUS = 90023 // sync_status为不支持delayFetch的状态
)

type BizErrorExtra struct {
	TaskID       *string
	RowsAffected *int64
	Message      *string
}

type BizError struct {
	code     BIZ_ERROR_CODE
	message  string
	extra    *BizErrorExtra
	extraMap *map[string]string
}

var errMsgMap = map[BIZ_ERROR_CODE]string{
	ERR_DUP_TASKID:          "Duplicated taskID",
	ERR_WRONG_ROWS_AFFECTED: "error:ROWS_AFFECTED",

	ERR_INTERNAL:              "internal error",
	ERR_NO_LOCALSERVER:        "No Localserver DeployMode",
	ERR_INIT_DB_CONN_FAILED:   "Init Db Conn Failed",
	ERR_SYNC_UNSUPPORT_STATUS: "Unsupport sync_status",
}

func NewBizError(code BIZ_ERROR_CODE) *BizError {
	return &BizError{
		code:    code,
		message: errMsgMap[code],
		extra:   nil,
	}
}

func NewBizErrorWithExtra(code BIZ_ERROR_CODE, extra BizErrorExtra) *BizError {
	return &BizError{
		code:    code,
		message: errMsgMap[code],
		extra:   &extra,
	}
}

func NewBizErrorWithMap(code BIZ_ERROR_CODE, extraMap map[string]string) *BizError {
	return &BizError{
		code:     code,
		message:  errMsgMap[code],
		extraMap: &extraMap,
	}
}

func NewBizErrorWithMessage(msg string) *BizError {
	return &BizError{
		code:    ERR_COMMON,
		message: msg,
	}
}

func NewBizErrorByError(err error) *BizError {
	logger := logrus.New()
	// NOTE 这种写法无法打印stack。。。
	// logger.Error(wErr.Wrap(fmt.Errorf("%+v", err), ""))
	logger.Errorf("%+v", wErr.Wrap(fmt.Errorf("%+v", err), ""))
	return &BizError{
		code:    ERR_INTERNAL,
		message: errMsgMap[ERR_INTERNAL],
	}
}

func (e *BizError) Code() int {
	return int(e.code)
}

func (e *BizError) Msg() string {
	msg := e.message
	if e.extraMap != nil {
		for k, v := range *e.extraMap {
			msg = fmt.Sprintf("%s %s[%s]", msg, k, v)
		}
	}

	// 特殊处理的code
	if e.code == ERR_NO_LOCALSERVER {
		msg = fmt.Sprintf("%s(%s)", msg, common.GetInstallMode().GetString())
	}

	// 带extra信息
	if e.extra == nil {
		return msg
	}

	if e.extra.Message != nil {
		return *e.extra.Message
	}
	if e.extra.TaskID != nil {
		return fmt.Sprintf("%s [%s]", msg, *e.extra.TaskID)
	}

	return msg
}

func (e *BizError) IsCode(code BIZ_ERROR_CODE) bool {
	return code == e.code
}
