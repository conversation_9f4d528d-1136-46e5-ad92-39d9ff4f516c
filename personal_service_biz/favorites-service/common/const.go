package common

type CollectRecordStatusType int64

const (
	CollectRecordStatusPending      CollectRecordStatusType = 0
	CollectRecordStatusPullMeta     CollectRecordStatusType = 101
	CollectRecordStatusDownloadFile CollectRecordStatusType = 102
	CollectRecordStatusPartial      CollectRecordStatusType = 103
	CollectRecordStatusLasyDown     CollectRecordStatusType = 104
	CollectRecordStatusSuccess      CollectRecordStatusType = 105
	CollectRecordStatusError        CollectRecordStatusType = 500
)

type SyncStatusType int64

const (
	SyncStatusDefault        SyncStatusType = 0   // 初始状态/unkown/db表的默认值
	SyncStatusUndownload     SyncStatusType = 101 // 下载未完成
	SyncStatusUnsync         SyncStatusType = 102 // 下载完整，未转储
	SyncStatusSynced         SyncStatusType = 201 // 已转储到localserver
	SyncStatusUpdated        SyncStatusType = 202 // 转储后数据有更新(暂无场景)
	SyncStatusLazyDownFinish SyncStatusType = 203 // 直接来自801记录下载到localserver。
	SyncStatusCleard         SyncStatusType = 301 // 转储后cloudserver已清除
	SyncStatusIllegalUpdated SyncStatusType = 302 // cloudserver清除后有数据更新(非法逻辑，暂无场景)
	SyncStatusLazyPull       SyncStatusType = 801 // 延迟到localserver进行拉取(不走cloudserver=>localserver的流量)
	SyncStatusLogicalDeleted SyncStatusType = 901 // 业务上逻辑删除
	SyncStatusConfict        SyncStatusType = 902 // 数据冲突
	SyncStatusSyncDataFailed SyncStatusType = 911 // 转储进行时，文件数据同步失败
	SyncStatusOpenFileError  SyncStatusType = 912 // 转储进行时，打开文件失败（没权限等等）
	SyncStatusChecksumFailed SyncStatusType = 913 // 转储进行时，checksum校验不一致
	SyncStatusSyncNotFound   SyncStatusType = 914 // 转储进行时，rsync exit code=23，表示cloud也不存在文件。合理流程中应该属于801或者101
	SyncStatusSyncUnknown    SyncStatusType = 919 // 转储进行时，其他未知bad case。比如db CheckSum丢失
)

type TaskSourceType int64

const (
	TaskSourceTypeUser       TaskSourceType = 1 // 用户出发下载任务
	TaskSourceTypeBackground TaskSourceType = 2 // 定时任务
	TaskSourceTypeDelayFetch TaskSourceType = 3 // 来自delayFetch离线任务
)
