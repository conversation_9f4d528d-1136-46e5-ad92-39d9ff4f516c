package common

import (
	"fmt"
	"net/url"
	"os"
	"time"
)

// https://golangcode.com/how-to-check-if-a-string-is-a-url/
func IsValidUrl(toTest string) bool {
	_, err := url.ParseRequestURI(toTest)
	if err != nil {
		return false
	}

	u, err := url.Parse(toTest)
	if err != nil || u.Scheme == "" || u.Host == "" {
		return false
	}

	return true
}

func FormatTime(date time.Time) string {
	return date.Format("2006-01-02 15:04:05")
}

func StrPtr2Str(p *string) string {
	if p != nil {
		return *p
	}
	return "(nil)"
}

func CreateInt64Ptr(v int64) *int64 {
	return &v
}

func CreateStrPtr(v string) *string {
	return &v
}

func ConvertSize(b uint64) string {
	if b < 1024 {
		return ""
	} else if b < 1024*1024 {
		return fmt.Sprintf("(%.2fKB)", float64(b)/1024)
	} else if b < 1024*1024*1024 {
		return fmt.Sprintf("(%.2fMB)", float64(b)/(1024*1024))
	} else {
		return fmt.Sprintf("(%.2fGB)", float64(b)/(1024*1024*1024))
	}
}

type InstallMode struct {
	mode string
}

func (m *InstallMode) GetString() string {
	return m.mode
}

func (m *InstallMode) IsLocalMode() bool {
	if m.mode == "local_server" {
		return true
	}
	return false
}

func GetInstallMode() *InstallMode {
	return &InstallMode{
		mode: os.Getenv("TREETHEME_INSTALL_MODE"),
	}
}
