package config

import (
	"context"
	"fmt"
	"os"
)

type Config struct {
	StorePath string
	DSN       string
	AmqpUrl   string
}

var globalConfig Config

func GetDSNByHost(ipPort string) string {
	return fmt.Sprintf("favoritesuser:favoritespass@tcp(%s)/db_favorites?charset=utf8mb4&parseTime=True&loc=Local", ipPort)
}

func loadConfigs(ctx context.Context) error {
	env := os.Getenv("ENV")
	if env == "deploy" {
		// 加载config.deploy文件
		globalConfig.StorePath = "/store_root"
		globalConfig.DSN = GetDSNByHost("favorites-mysql:3306")
		globalConfig.AmqpUrl = "amqp://admin:adminuser@rabbit-mq:5672/"
	} else {
		// 开发环境加载config.dev文件
		// globalConfig.StorePath = "/data/docker-volumes/favorites-service/store_root"
		// TODO 这里要怎么跟bilibili-crawler的配置保持一致呢？但其实bilibili-crawler里面映射到/store_root的末尾还有/bilibili，是/data/docker-volumes/public_root/file_resources/bilibili
		globalConfig.StorePath = "/data/docker-volumes/public_root/file_resources"
		globalConfig.DSN = GetDSNByHost("127.0.0.1:3311")
		globalConfig.AmqpUrl = "amqp://admin:adminuser@127.0.0.1:5672/"
	}
	return nil
}

func InitConfig(ctx context.Context) error {
	return loadConfigs(ctx)
}

func GetDSN() string {
	return globalConfig.DSN
}

func GetStorePath() string {
	return globalConfig.StorePath
}

func GetAmqpUrl() string {
	return globalConfig.AmqpUrl
}
