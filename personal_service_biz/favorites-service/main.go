package main

import (
	"context"
	"favorites-service/background"
	"favorites-service/config"
	"favorites-service/models"
	"favorites-service/mq"
	"favorites-service/service"
	"thrift-common/server/favorites"

	"github.com/sirupsen/logrus"
)

func main() {
	var err error
	ctx := context.Background()
	logger := logrus.New()
	err = config.InitConfig(ctx)
	if err != nil {
		logger.Errorf("loadConfigs failed: %+v", err)
		return
	}

	err = models.InitMysql(ctx)
	if err != nil {
		logger.Errorf("initMysql failed: %+v", err)
		return
	}

	err = mq.InitRabbitMq(ctx, service.ReceivePullResourceStatusMsg)
	if err != nil {
		logger.Errorf("InitRabbitMq failed: %+v", err)
		return
	}

	err = background.InitBackground(ctx)
	if err != nil {
		logger.Errorf("InitBackground failed: %+v", err)
		return
	}

	// client := favorites.FavoritesClient{}
	// fmt.Println("vim-go: %+v\n", client)
	// wash_data.WashVideoRecord(ctx)
	// wash_data.WashCollectRecord()

	handler := &service.FavoritesService{}
	// handler.AddToFavorites(ctx, "1", "https://www.bilibili.com/video/BV1BJ411k7TJ", GlobalConfig.StorePath, "")
	logger.Info("Server Started!")
	err = favorites.Server(ctx, handler)
	if err != nil {
		logger.Errorf("Server error: %+v", err)
	}
}
