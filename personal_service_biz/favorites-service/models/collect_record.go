package models

import (
	"context"
	"favorites-service/biz_error"
	"favorites-service/common"
	"fmt"
	"time"

	wErr "github.com/pkg/errors"
	"gorm.io/gorm"
)

type CollectRecord struct {
	ID               int64      `gorm:"column:id"`
	CollectType      int64      `gorm:"column:collect_type"`
	Origin           string     `gorm:"column:origin"`
	SavePath         string     `gorm:"column:save_path"`
	Remark           *string    `gorm:"column:remark"`
	StatusCode       int64      `gorm:"column:status_code"`
	StatusMessage    string     `gorm:"column:status_message"`
	Duration         int64      `gorm:"column:duration"`
	DurationPullMeta *int64     `gorm:"column:duration_pull_meta"`
	DurationDetails  *string    `gorm:"column:duration_details"`
	Title            *string    `gorm:"column:title"`
	FilesNum         *int64     `gorm:"column:files_num"`
	FilesStatus      *string    `gorm:"column:files_status"`
	FilesMissingIds  *string    `gorm:"column:files_missing_ids"`
	TotalSize        *string    `gorm:"column:total_size"`
	Env              *string    `gorm:"column:env"`
	CreateTime       time.Time  `gorm:"column:created_at"`
	UpdateTime       time.Time  `gorm:"column:updated_at"`
	DeleteTime       *time.Time `gorm:"column:deleted_at"`
	// TreethemeMode   *string   `gorm:"treetheme_mode"`
}

func (CollectRecord) TableName() string {
	return "collect_record"
}

func UpdateCollectRecordListByIds(ctx context.Context, db *gorm.DB, status common.CollectRecordStatusType, ids []int64) *biz_error.BizError {
	result := db.Model(&CollectRecord{}).
		Where("id in ?", ids).
		Update("status_code", status)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

func SelectCollectRecordListByStatus(ctx context.Context, db *gorm.DB, status common.CollectRecordStatusType, limit int64) ([]*CollectRecord, *biz_error.BizError) {
	var records []*CollectRecord
	result := db.Where("status_code = ?", status).
		Limit(int(limit)).
		Find(&records)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected != int64(len(records)) {
		s := fmt.Sprintf("result.RowsAffected[%d] != len[%d]", result.RowsAffected, len(records))
		return nil, biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_ROWS_AFFECTED, biz_error.BizErrorExtra{
			Message: &s,
		})
	}
	return records, nil
}

func CreateCollectRecord(record *CollectRecord) *biz_error.BizError {
	now := time.Now()
	record.CreateTime = now
	record.UpdateTime = now
	result := GetDB().Create(record)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

func SaveCollectRecord(ctx context.Context, db *gorm.DB, record *CollectRecord) error {
	now := time.Now()
	record.UpdateTime = now
	result := db.Save(record)
	if result.Error != nil {
		return wErr.Wrap(result.Error, "")
	}
	return nil
}

func SelectCollectRecordByID(ctx context.Context, db *gorm.DB, id int64) (*CollectRecord, error) {
	var record CollectRecord
	result := db.Where(&CollectRecord{ID: id}).First(&record)
	if result.Error != nil {
		return nil, wErr.Wrap(result.Error, "")
	}
	if result.RowsAffected != 1 {
		return nil, wErr.Wrap(fmt.Errorf("Wrong RowsAffected[%d]", result.RowsAffected), "")
	}
	return &record, nil
}

func SelectCollectRecordList(offset, limit int64) ([]*CollectRecord, error) {
	var records []*CollectRecord
	result := GetDB().Limit(int(limit)).
		Offset(int(offset)).
		Order("created_at desc").
		Find(&records)
	if result.Error != nil {
		return nil, wErr.Wrap(result.Error, "")
	}
	if result.RowsAffected != int64(len(records)) {
		return nil, wErr.Wrap(fmt.Errorf("result.RowsAffected[%d] != len[%d]", result.RowsAffected, len(records)), "")
	}
	return records, nil
}

func CountCollectRecords() (int64, error) {
	var count int64
	result := GetDB().Model(&CollectRecord{}).Count(&count)
	if result.Error != nil {
		return 0, wErr.Wrap(result.Error, "")
	}
	return count, nil
}

func DeleteCollectRecord(id int64) error {
	result := GetDB().Delete(&CollectRecord{}, id)
	if result.Error != nil {
		return wErr.Wrap(result.Error, "")
	}
	return nil
}

func SearchCollectRecordList(keyword string, offset, limit int64) ([]*CollectRecord, error) {
	var records []*CollectRecord
	query := GetDB().Where("title LIKE ? OR origin LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	result := query.Limit(int(limit)).
		Offset(int(offset)).
		Order("created_at desc").
		Find(&records)
	if result.Error != nil {
		return nil, wErr.Wrap(result.Error, "")
	}
	return records, nil
}

func CountSearchCollectRecords(keyword string) (int64, error) {
	var count int64
	query := GetDB().Model(&CollectRecord{}).Where("title LIKE ? OR origin LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	result := query.Count(&count)
	if result.Error != nil {
		return 0, wErr.Wrap(result.Error, "")
	}
	return count, nil
}
