package models

import (
	"context"
	"favorites-service/biz_error"
	"favorites-service/config"

	wErr "github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

var db *gorm.DB

func GetDB() *gorm.DB {
	// TODO 所有涉及DB业务操作的逻辑，应该判断localserver模式不直接操作本地DB，要获取cloudDB，最后再以数据同步的方式进行同步
	// 但是一般情况下除了手动datasync，也不会有localserver去获取，否则会导致冲突。比如后台异步任务

	return db
}

func InitMysql(ctx context.Context) error {
	logger := logrus.New()
	var err error
	dsn := config.GetDSN()
	db, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		logger.Errorf("InitMysql err[%+v]", err)
		return wErr.Wrap(err, "")
	}
	return nil
}

/***
 * cloudserver端DB
 */
var cloudDB *gorm.DB

func GetCloudDB() *gorm.DB {
	return cloudDB
}

func InitCloudMysql(ctx context.Context, ipPort string) *biz_error.BizError {
	logger := logrus.New()
	var err error
	dsn := config.GetDSNByHost(ipPort)
	cloudDB, err = gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		logger.Errorf("InitCloudMysql err[%+v]", err)
		return biz_error.NewBizError(biz_error.ERR_INIT_DB_CONN_FAILED)
	}
	return nil
}

func CloseCloudMysql(ctx context.Context) *biz_error.BizError {
	sqlDB, err := cloudDB.DB()
	if err != nil {
		return biz_error.NewBizErrorByError(err)
	}
	err = sqlDB.Close()
	if err != nil {
		return biz_error.NewBizErrorByError(err)
	}
	cloudDB = nil
	return nil
}
