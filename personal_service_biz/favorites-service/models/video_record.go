package models

import (
	"context"
	"favorites-service/biz_error"
	"favorites-service/common"
	"fmt"
	"time"

	wErr "github.com/pkg/errors"
	"gorm.io/gorm"
)

type VideoRecord struct {
	ID              int64      `gorm:"column:id"`
	CollectRecordID int64      `gorm:"column:collect_record_id"`
	FileID          int64      `gorm:"column:file_id"`
	TotalFile       int64      `gorm:"column:total_file"`
	Title           string     `gorm:"column:title"`
	FileName        *string    `gorm:"column:filename"`
	Format          string     `gorm:"column:format"`
	FileType        *string    `gorm:"column:filetype"`
	Container       string     `gorm:"column:container"`
	Quality         string     `gorm:"column:quality"`
	Size            int64      `gorm:"column:size"`
	RealSize        *int64     `gorm:"column:realsize"`
	CheckSum        *string    `gorm:"column:checksum"`
	SyncStatus      int64      `gorm:"column:sync_status"`
	SyncUnitType    int64      `gorm:"column:sync_unit_type"`
	SyncPath        *string    `gorm:"column:sync_path"`
	CreatedAt       time.Time  `gorm:"column:created_at"`
	UpdatedAt       time.Time  `gorm:"column:updated_at"`
	DeletedAt       *time.Time `gorm:"column:deleted_at"`
}

func (VideoRecord) TableName() string {
	return "video_record"
}

func CreateVideoRecord(ctx context.Context, db *gorm.DB, record *VideoRecord) error {
	now := time.Now()
	record.CreatedAt = now
	record.UpdatedAt = now
	result := db.Create(record)
	if result.Error != nil {
		return wErr.Wrap(result.Error, "")
	}
	return nil
}

func SaveVideoRecord(ctx context.Context, db *gorm.DB, record *VideoRecord) *biz_error.BizError {
	now := time.Now()
	record.UpdatedAt = now
	result := db.Save(record)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

func SelectVideoRecordByID(ctx context.Context, db *gorm.DB, id int64) (*VideoRecord, *biz_error.BizError) {
	var record VideoRecord
	result := db.Where(&VideoRecord{ID: id}).First(&record)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected != 1 {
		return nil, biz_error.NewBizErrorByError(fmt.Errorf("Wrong RowsAffected[%d]", result.RowsAffected))
	}
	return &record, nil
}

func SelectVideoRecordListByStatus(ctx context.Context, db *gorm.DB, status []common.SyncStatusType, limit int64) ([]*VideoRecord, *biz_error.BizError) {
	var records []*VideoRecord
	result := db.Where("sync_status in ?", status).
		Limit(int(limit)).
		Find(&records)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected != int64(len(records)) {
		s := fmt.Sprintf("result.RowsAffected[%d] != len[%d]", result.RowsAffected, len(records))
		return nil, biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_ROWS_AFFECTED, biz_error.BizErrorExtra{
			Message: &s,
		})
	}
	return records, nil
}

func SelectVideoRecordListByCollectID(ctx context.Context, db *gorm.DB, collectID int64) ([]*VideoRecord, error) {
	var records []*VideoRecord
	result := db.Where("collect_record_id = ?", collectID).
		Find(&records)
	if result.Error != nil {
		return nil, wErr.Wrap(result.Error, "")
	}
	if result.RowsAffected != int64(len(records)) {
		return nil, wErr.Wrap(fmt.Errorf("result.RowsAffected[%d] != len[%d]", result.RowsAffected, len(records)), "")
	}
	return records, nil
}

// func SelectVideoRecordListByStatus(status common.VideoRecordStatusType, limit int64) ([]*VideoRecord, error) {
// 	var records []*VideoRecord
// 	result := GetDB().Where("status_code = ?", status).
// 		Limit(int(limit)).
// 		Find(&records)
// 	if result.Error != nil {
// 		return nil, wErr.Wrap(result.Error, "")
// 	}
// 	if result.RowsAffected != int64(len(records)) {
// 		return nil, wErr.Wrap(fmt.Errorf("result.RowsAffected[%d] != len[%d]", result.RowsAffected, len(records)), "")
// 	}
// 	return records, nil
// }
//
func SelectVideoRecordList(offset, limit int64) ([]*VideoRecord, error) {
	var records []*VideoRecord
	result := GetDB().Limit(int(limit)).
		Offset(int(offset)).
		Order("created_at desc").
		Find(&records)
	if result.Error != nil {
		return nil, wErr.Wrap(result.Error, "")
	}
	if result.RowsAffected != int64(len(records)) {
		return nil, wErr.Wrap(fmt.Errorf("result.RowsAffected[%d] != len[%d]", result.RowsAffected, len(records)), "")
	}
	return records, nil
}
