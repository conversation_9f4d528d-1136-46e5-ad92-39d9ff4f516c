package mq

import (
	"context"
	"encoding/json"
	"favorites-service/biz_error"
	"favorites-service/common"
	"favorites-service/models"
	"time"

	amqp "github.com/rabbitmq/amqp091-go"
	"github.com/sirupsen/logrus"
)

type PullResourceStartMsg struct {
	CollectRecordID int64
	Url             string
	SavePath        string
	TaskSource      int64
	IsMultiple      bool
}

type PullResourceStatusMsg struct {
	CollectRecordID int64
	ShouldContinue  bool
	StatusCode      int64
	StatusMessage   string
	Duration        int64
	TaskSource      int64
	DurationDetails *string
	Title           *string
	FilesStatus     *string
	FilesNum        *int64
	TotalSize       *int64
	FilesMissingIds *string
}

// 测试用
// func SendPullResourceStatusMsg() {
// 	logger := logrus.New()
//
// 	//发送数据
// 	msg := "hello 666"
// 	if err := mqChannel.Publish("", "favorites_pull_resource_status", false, false, amqp.Publishing{
// 		ContentType: "text/plain",
// 		Body:        []byte(msg),
// 	}); err != nil {
// 		logger.Errorf("Publish Err =", err)
// 		return
// 	}
// 	logger.Infof("Send msg ok, msg =", msg)
// }

// func SendPullResourceTaskMsg(ctx context.Context, recordID, collectType int64, origin, savePath string) error {
func SendPullResourceTaskMsg(ctx context.Context, record *models.CollectRecord, source common.TaskSourceType) *biz_error.BizError {
	logger := logrus.New()

	//发送数据
	msg := PullResourceStartMsg{
		CollectRecordID: record.ID,
		Url:             record.Origin,
		SavePath:        record.SavePath,
		TaskSource:      int64(source),
		IsMultiple:      true,
	}
	jsonMsg, err := json.Marshal(msg)
	if err != nil {
		return biz_error.NewBizErrorByError(err)
	}

	// 1xxx表示视频
	// 2xxx表示网页
	queue := ""
	if record.CollectType == 1001 {
		// b站视频
		queue = "favorites_pull_resource_bilibili_start"
	} else if record.CollectType == 2001 {
		// wx文章
		queue = "favorites_pull_resource_wx_start"
	}

	maxRetryTimes := 5
	for i := 0; ; i++ {
		logger.Infof("[%d]Start Publish, queue[%+v] record[%+v]", i, queue, record.ID)
		if err = mqChannel.Publish("", queue, false, false, amqp.Publishing{
			ContentType: "text/plain",
			Body:        []byte(jsonMsg),
		}); err != nil {
			if i >= maxRetryTimes {
				logger.Errorf("[%d]Publish failed, queue[%+v] record[%+v] err[%+v]", i, queue, record.ID, err)
				return biz_error.NewBizErrorByError(err)
			}
			logger.Warnf("mq.Publish retry %d times, err[%+v]", i, err)

			e := InitRabbitMq(ctx, ReceivePullResourceStatusMsgCallback)
			if e != nil {
				logger.Errorf("InitRabbitMq err[%+v]", e)
			} else {
				logger.Info("ReInitRabbitMq success")
			}
			time.Sleep(5 * time.Second)
		} else {
			logger.Infof("[%d]Publish success, queue[%+v] record[%+v]", i, queue, record.ID)
			break
		}
	}

	logger.Infof("Send msg ok, msg = %s", jsonMsg)
	return nil
}
