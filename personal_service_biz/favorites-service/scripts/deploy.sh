#!/bin/bash
cur_dir=$(dirname $(readlink -f $0))
root_dir=$(dirname ${cur_dir})
if [[ ! -f ${root_dir}/.version ]]; then
  echo "Wrong Root Directory"
  exit
fi

# 先拉取最新代码（避免每次.version冲突）
# git pull

source ${root_dir}/config.ini
version_file=${root_dir}/.version
image_version=$(UpdateVersionPatch ${version_file})

image_name=favorites-service:${image_version}
docker build -t ${image_name} -f ${root_dir}/image/Dockerfile ${root_dir}/
docker image ls -a | grep favorites-service

path=/data/docker-volumes/${container_name}
if [[ -d ${path} ]]; then
  echo "Already Exist Path: ${path}"
else
  mkdir -p ${path}
fi

public_root=/data/docker-volumes/public_root
bili_root=${public_root}/bilibili
store_root=/data/docker-volumes/public_root/file_resources

docker container rm -f ${container_name}
docker run -d  --restart unless-stopped --net admin-net \
  -e ENV='deploy' \
  -e TREETHEME_INSTALL_MODE="${TREETHEME_INSTALL_MODE}" \
  -e LANG="C.UTF-8" \
  -e LANGUAGE="C.UTF-8" \
  -e LC_ALL="C.UTF-8" \
  --name=${container_name} \
  -v ${path}/runtime/favorites-service-logs:/runtime/favorites-service-logs \
  -v ${public_root}:/public_root \
  -v ${store_root}:/store_root \
  ${image_name}
docker container ls -a

# deploy.sh使用root执行，git操作可能存在报错：fatal: detected dubious ownership in repository at
# git add .version
# git commit -m "update version"
# git push
