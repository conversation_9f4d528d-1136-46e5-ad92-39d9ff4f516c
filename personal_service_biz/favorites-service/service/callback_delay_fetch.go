package service

import (
	"context"
	"time"

	"favorites-service/biz_error"
	"favorites-service/common"
	"favorites-service/models"
	"favorites-service/mq"

	"github.com/sirupsen/logrus"
)

func checkIsDelayFetchFinish(ctx context.Context, collectID int64) (bool, int64, error) {
	record, err := models.SelectCollectRecordByID(ctx, models.GetCloudDB(), collectID)
	if err != nil {
		return false, int64(0), err
	}

	// 只要不是104/lazy down的状态，不管成功还是失败，都是更新完成。
	// 但其实也要经历101/pull meta => 102/download => 103/partial或者105/success的状态
	// 所以在101时，还是不允许当做finish
	if record.StatusCode == int64(common.CollectRecordStatusPartial) ||
		record.StatusCode == int64(common.CollectRecordStatusSuccess) {
		return true, record.StatusCode, nil
	}
	if record.StatusCode == int64(common.CollectRecordStatusError) {
		// 暂时没有处理手段，也先当做finish。否则会卡住
		return true, record.StatusCode, nil
	}
	return false, record.StatusCode, nil
}

func waitForDelayFetchFinish(ctx context.Context, collectID int64) bool {
	logger := logrus.New()
	for {
		time.Sleep(60 * time.Second)
		if isFinished, status, err := checkIsDelayFetchFinish(ctx, collectID); err != nil {
			// 可能客户端发起的请求已经终止，容器的cloud_db隧道中断了
			logger.Errorf("checkIsDelayFetchFinish err[%+v]", err)
			break
		} else if isFinished {
			logger.Infof("lazyDown finish! CollectID[%+v] status[%d]", collectID, status)
			break
		} else {
			logger.Infof("check not finish... CollectID[%+v] status[%d]", collectID, status)
		}
	}
	return true
}

type Handler func()

func stepCheckTunnel(ctx context.Context, ipPort string) (*biz_error.BizError, Handler) {
	logger := logrus.New()
	// Step1: 环境检测
	//   1. 必须是localserver的操作；
	//   2. 必须连通着cloudserver的db；
	if !common.GetInstallMode().IsLocalMode() {
		return biz_error.NewBizError(biz_error.ERR_NO_LOCALSERVER), nil
	}

	err := models.InitCloudMysql(ctx, ipPort)
	if err != nil {
		logger.Errorf("InitCloudMysql(ipPort=%s) err[%+v]", ipPort, err)
		return err, nil
	}
	logger.Infof("InitCloudMysql(ipPort=%s) Success.", ipPort)
	after := func() {
		err := models.CloseCloudMysql(ctx)
		if err != nil {
			logger.Errorf("CloseCloudMysql after err[%+v]", err)
		}
	}

	return nil, after
}

func delayFetchByVideoRecord(ctx context.Context, ipPort string) *biz_error.BizError {
	logger := logrus.New()
	err, after := stepCheckTunnel(ctx, ipPort)
	if err != nil {
		return err
	}
	defer after()

	// Step2: 先拉取lazy down的记录
	for {
		collects, err := models.SelectCollectRecordListByStatus(ctx, models.GetCloudDB(), common.CollectRecordStatusLasyDown, 9999999)
		if err != nil {
			return err
		}
		// 如果一次batch拉取完之后，外层循环继续进行到拉取为空结束
		if len(collects) == 0 {
			break
		}

		for _, c := range collects {
			// if *c.FilesNum > 1 {
			// 	// 先跳过超过1个视频的，避免长时间调试
			// 	continue
			// }
			err = mq.SendPullResourceTaskMsg(ctx, c, common.TaskSourceTypeDelayFetch)
			if err != nil {
				logger.Errorf("err[%+v]", err)
				// return "", err
			} else {
				logger.Infof("lazyDown start, c[%s:%s]", c.Origin, *c.Title)
			}

			// 每次允许一个collect进行
			// waitForDelayFetchFinish(c.ID)
		}

		// 最后一起等待finish（一批collect等待完了再拉取一批进行等待，所以放到内循环）
		// TODO 上面只处理了104lazy down的记录。但是waitForDelayFetchFinish检测的确实不存在104、101、500等等各种其他状态。所以如果存在其他状态的记录，会有无限等待
		for _, c := range collects {
			waitForDelayFetchFinish(ctx, c.ID)
		}
	}

	// Step3: 先拉取存在error的记录

	logger.Info("Close CloudMysql Connection...")

	// Step2: 在localserver触发拉取文件的流程，流程跟cloudserver正式服务的逻辑，需要有差异化、有复用逻辑。
	//    差异化就是绕开lazy down的这些判断逻辑。以及db的更新值。另外整个拉取的逻辑跟视频都是不完全一样的，不是一次完整的AddToFavorites业务。

	// Step4: 更新cloudserver的数据库表
	return nil
}

func CallbackDelayFetch(ctx context.Context, syncMetaTable, ipPort string) *biz_error.BizError {
	// 有登记的delay fetch的表
	if syncMetaTable == "video_record" {
		return delayFetchByVideoRecord(ctx, ipPort)
	} else {
		return biz_error.NewBizErrorWithMap(biz_error.ERR_SYNC_TABLE_NO_EXIST, map[string]string{"table": syncMetaTable})
	}
}
