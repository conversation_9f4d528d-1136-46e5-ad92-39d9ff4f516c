package service

import (
	"bytes"
	"context"
	"crypto/sha256"
	"favorites-service/biz_error"
	"favorites-service/common"
	"favorites-service/models"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"time"

	"github.com/sirupsen/logrus"
)

func waitForSyncDataFinish(ctx context.Context, collectID int64) bool {
	logger := logrus.New()
	for {
		time.Sleep(60 * time.Second)
		if isFinished, status, err := checkIsDelayFetchFinish(ctx, collectID); err != nil {
			// 可能客户端发起的请求已经终止，容器的cloud_db隧道中断了
			logger.Errorf("checkIsDelayFetchFinish err[%+v]", err)
			break
		} else if isFinished {
			logger.Infof("syncData finish! CollectID[%+v] status[%d]", collectID, status)
			break
		} else {
			logger.Infof("check not finish... CollectID[%+v] status[%d]", collectID, status)
		}
	}
	return true
}

// 先hardcode文件路径
// const PUBLIC_ROOT = "/data/docker-volumes/public_root/file_resources"
const PUBLIC_ROOT = "/public_root/file_resources/"

type FileInfo struct {
	CheckSum string
}

func stdPrint(name string, reader io.ReadCloser) {
	logger := logrus.New()
	output := []byte{}
	_, err := reader.Read(output)
	if err != nil {
		logger.Errorf("Command Read %s err[%+v]", name, err)
	} else {
		logger.Infof("Command %s[%s]", name, string(output))
	}
}

func syncDataByTunnel(ctx context.Context, v *models.VideoRecord, tunnel string) *biz_error.BizError {
	logger := logrus.New()
	path := filepath.Join(PUBLIC_ROOT, "bilibili/", *v.FileName)
	cmd := exec.Command("rsync", "-avz", "-e",
		fmt.Sprintf("ssh -o 'ControlPath=%s'", tunnel),
		fmt.Sprintf("root@localhost:\"%s\"", path),
		path,
		// fmt.Sprintf("\"%s\"", path),
	)
	var out bytes.Buffer
	var errout bytes.Buffer
	cmd.Stdout = &out
	cmd.Stderr = &errout

	err := cmd.Run()
	if err != nil && err.Error() != "exit status 23" {
		cmdStr := fmt.Sprintf("rsync -avz -e \"ssh -o 'ControlPath=%s'\" root@localhost:%s %s", tunnel, path, path)
		logger.Infof("cmdStr[%s]", cmdStr)
		logger.Infof("stdout[%+v]", out.String())
		logger.Infof("stderr[%+v]", errout.String())
		logger.Errorf("syncDataByTunnel err[%s][%+v]", *v.FileName, err)
		return biz_error.NewBizErrorByError(err)
	}
	logger.Infof("syncDataByTunnel success[%s]", *v.FileName)

	// stdout, err := cmd.StdoutPipe()
	// if err != nil {
	// 	logger.Errorf("Command StdoutPipe err[%+v]", err)
	// 	return biz_error.NewBizErrorByError(err)
	// }

	// stderr, err := cmd.StderrPipe()
	// if err != nil {
	// 	logger.Errorf("Command StderrPipe err[%+v]", err)
	// 	return biz_error.NewBizErrorByError(err)
	// }

	// // 如果是cmd.Run()，就不需要Wait，等到子进程结束再返回err或者nil
	// err = cmd.Start()
	// if err != nil {
	// 	logger.Errorf("Command err[%+v]", err)
	// 	return biz_error.NewBizErrorByError(err)
	// }

	// err = cmd.Wait()
	// if err != nil {
	// 	logger.Errorf("Command Wait err[%+v]", err)
	// 	stdPrint("stderr", stderr)
	// 	return biz_error.NewBizErrorByError(err)
	// }

	// stdPrint("stdout", stdout)
	// stdPrint("stderr", stderr)

	return nil
}

func checkStatus(ctx context.Context, v *models.VideoRecord) (*FileInfo, *biz_error.BizError) {
	logger := logrus.New()
	if v.CheckSum == nil {
		v.SyncStatus = int64(common.SyncStatusSyncUnknown)
		return nil, biz_error.NewBizErrorByError(fmt.Errorf("CheckSum丢失[%+v]", v))
	}

	// 获取文件路径
	path := filepath.Join(PUBLIC_ROOT, "bilibili/", *v.FileName)
	file, err := os.Open(path)
	if err != nil {
		if os.IsNotExist(err) {
			v.SyncStatus = int64(common.SyncStatusSyncNotFound)
		} else {
			v.SyncStatus = int64(common.SyncStatusOpenFileError)
		}
		return nil, biz_error.NewBizErrorByError(err)
	}
	defer file.Close()

	hash := sha256.New()
	if _, err := io.Copy(hash, file); err != nil {
		logger.Errorf("err[%+v]", err)
	}
	sum := fmt.Sprintf("%x", hash.Sum(nil))
	logger.Infof("sum: %+v", sum)

	if sum != *v.CheckSum {
		logger.Errorf("CheckSum failed![%s != %s]", sum, *v.CheckSum)
		// 校验失败
		v.SyncStatus = int64(common.SyncStatusChecksumFailed)
	} else {
		// 文件同步成功，且checksum校验成功
		v.SyncStatus = int64(common.SyncStatusSynced)
	}

	return &FileInfo{
		CheckSum: string(sum),
	}, nil
}

func syncDataByVideoRecord(ctx context.Context, ipPort, syncTunnel string) *biz_error.BizError {
	logger := logrus.New()
	logger.Infof("ipPort[%+v] syncTunnel[%+v]", ipPort, syncTunnel)
	err, after := stepCheckTunnel(ctx, ipPort)
	if err != nil {
		return err
	}
	defer after()

	// Step3:
	for {
		// Step1: 拉取cloudserver下载完成，但是未转储的记录
		videos, err := models.SelectVideoRecordListByStatus(ctx,
			models.GetCloudDB(),
			// 未转储的，转储失败的
			[]common.SyncStatusType{common.SyncStatusUnsync, common.SyncStatusSyncDataFailed},
			9999999)
		// video, err := models.SelectVideoRecordByID(ctx, models.GetCloudDB(), 618)
		// videos := []*models.VideoRecord{video}
		logger.Infof("videos[%+v]", videos)
		if err != nil {
			return err
		}

		// 如果一次batch拉取完之后，外层循环继续进行到拉取为空结束
		if len(videos) == 0 {
			break
		}

		for _, v := range videos {
			logger.Infof("syncDataByTunnel v[%+v]", v)
			e := syncDataByTunnel(ctx, v, syncTunnel)
			if e != nil {
				logger.Errorf("err[%+v]", err)
			} else {
				// 还是要通过checkStatus去检查视频是不是真的cloud、local都不存在，不要相信rsync命令的cloud不存在，因为可能lazy down拉去过到local
				fInfo, e := checkStatus(ctx, v)
				if e != nil {
					logger.Errorf("checkStatus err[%+v]", err)
				} else {
					logger.Infof("getFileInfo [%+v]", fInfo)
				}

				err = models.SaveVideoRecord(ctx, models.GetCloudDB(), v)
				if err != nil {
					logger.Errorf("SaveVideoRecord err[%+v]", err)
				}
			}
		}
		// 最后一起等待finish（一批videos等待完了再拉取一批进行等待，所以放到内循环）
		// for _, c := range videos {
		// 	waitForSyncDataFinish(ctx, c.ID)
		// }
	}

	videos, err := models.SelectVideoRecordListByStatus(ctx,
		models.GetCloudDB(),
		// cloudserver数据丢失的
		[]common.SyncStatusType{common.SyncStatusSyncNotFound},
		9999999)
	if err != nil {
		return err
	}

	notFoundCollectIds := []int64{}
	for _, v := range videos {
		notFoundCollectIds = append(notFoundCollectIds, v.CollectRecordID)
	}

	// 找出cloud_server找不到视频文件(sync_status=104)的video_record记录
	// 说明之前对应的collect_record被错误地当成success(105)了。把他们改成104(lazy down)重新来一边delay_fetch
	logger.Infof("update CollectRecord to 104(lazy down) [%+v]", notFoundCollectIds)
	err = models.UpdateCollectRecordListByIds(ctx, models.GetCloudDB(), common.CollectRecordStatusLasyDown, notFoundCollectIds)
	if err != nil {
		logger.Errorf("UpdateCollectRecordListByIds err[%+v]", err)
	}
	return nil
}

func CallbackSyncData(ctx context.Context, syncMetaTable, ipPort, syncTunnel string) *biz_error.BizError {
	// 有登记的delay fetch的表
	if syncMetaTable == "video_record" {
		return syncDataByVideoRecord(ctx, ipPort, syncTunnel)
	} else {
		return biz_error.NewBizErrorWithMap(biz_error.ERR_SYNC_TABLE_NO_EXIST, map[string]string{"table": syncMetaTable})
	}
}
