package service

import (
	"context"
	"favorites-service/common"
	"favorites-service/config"
	"fmt"

	"github.com/sirupsen/logrus"
	"golang.org/x/sys/unix"
)

type DiskStatus struct {
	All   uint64 `json:"all"`
	Used  uint64 `json:"used"`
	Free  uint64 `json:"free"`
	Avail uint64 `json:"avail"`
}

func GetDiskInfo(ctx context.Context, depth int64, root string) (string, string, int64, string, error) {
	logger := logrus.New()
	storePath := config.GetStorePath()
	logger.Infof("storePath[%+v]", storePath)

	disk := DiskStatus{}
	fs := unix.Statfs_t{}
	err := unix.Statfs(storePath, &fs)
	if err != nil {
		logger.Errorf("Statfs err[%+v]", err)
	} else {
		disk.All = fs.Blocks * uint64(fs.Bsize)
		disk.Avail = fs.Bavail * uint64(fs.Bsize)
		disk.Free = fs.Bfree * uint64(fs.Bsize)
		disk.Used = disk.All - disk.Free
		logger.Infof("diskStatus[%+v]", disk)
	}

	diskLabel := "cloud_server"
	diskUsage := fmt.Sprintf("%d%s/%d%s",
		disk.Used, common.ConvertSize(disk.Used),
		disk.All, common.ConvertSize(disk.All))
	fsTree := "dfdfdf"
	progress := int64(disk.Used * 100.0 / disk.All)

	logger.Infof("usage[%+v] progress[%+v]", diskUsage, progress)
	return diskLabel, diskUsage, progress, fsTree, nil
}
