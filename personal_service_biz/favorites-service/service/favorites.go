package service

import (
	"context"
	"fmt"
	"net/http"
	"os"

	"github.com/sirupsen/logrus"

	"favorites-service/biz_error"
	"favorites-service/common"
	"favorites-service/models"
	"favorites-service/mq"
	favoritesGen "thrift-common/gen-go/favorites"
)

// var client *biliGen.BilibiliClient

func init() {
	// 由于不会自动重连，暂时避免单例client
	// logger := logrus.New()
	// var err error
	// client, err = biliClient.GetBilibiliClient()
	// if err != nil {
	// 	logger.Errorf("err: %+v", err)
	// }
}

// type PullResourceReturn struct {
// 	status      int64
// 	msg         string
// 	duration    int64
// 	title       *string
// 	filesNum    *int64
// 	filesStatus *string
// }
//
// func pullResourceWxArticle(ctx context.Context, savePath string) (PullResourceReturn, error) {
// 	return PullResourceReturn{}, nil
// }
//
// func pullResourceBilibili(ctx context.Context, origin, savePath string) (PullResourceReturn, error) {
// 	logger := logrus.New()
//
// 	client, err := biliClient.GetBilibiliClient()
// 	if err != nil {
// 		return PullResourceReturn{status: 500, msg: "GetBilibiliClient error"}, err
// 	}
//
// 	req := &biliGen.PullVideoRequest{
// 		IsMultiple: true,
// 		URL:        origin,
// 		SavePath:   savePath,
// 	}
// 	logger.Infof("pullResourceBilibili req[%+v]", req)
// 	res, err := client.PullVideo(ctx, req)
// 	logger.Infof("res[%+v]", res)
// 	// 目前来说，err暂时没用起来，都通过res.StatusCode来返回各种错误码
// 	if err != nil {
// 		return PullResourceReturn{status: 500, msg: "PullVideo error"}, err
// 	}
// 	return PullResourceReturn{
// 		status:      res.BaseResp.StatusCode,
// 		msg:         res.BaseResp.StatusMessage,
// 		duration:    res.Duration,
// 		title:       res.Title,
// 		filesNum:    res.FilesNum,
// 		filesStatus: res.FilesStatus,
// 	}, nil
// 	// cmdline := fmt.Sprintf("you-get %s", origin)
// 	// cmd := exec.Command("you-get", origin, "-o", savePath)
// 	// logger.Info("== cmd: ", cmd)
// 	// err := cmd.Run()
// 	// logger.Info("== err: ", err)
// 	// if err != nil {
// 	// 	logger.Error(err)
// 	// }
// }
//
// func pullOriginResource(ctx context.Context, collectType int, origin, savePath string) {
//
// }
//
// func asyncPullResourceTask(ctx context.Context, record *models.CollectRecord, origin, savePath string) {
// 	logger := logrus.New()
// 	collectType := record.CollectType
// 	var err error
// 	var ret PullResourceReturn
// 	// 1xxx表示视频
// 	// 2xxx表示网页
// 	if collectType == 1001 {
// 		// b站视频
// 		ret, err = pullResourceBilibili(ctx, origin, savePath)
// 	} else if collectType == 2001 {
// 		// wx文章
// 	}
// 	if err != nil {
// 		logger.Errorf("ret[%+v] err[%+v]", ret, err)
// 	}
//
// 	record.StatusCode = ret.status
// 	record.StatusMessage = ret.msg
// 	record.Duration = ret.duration
// 	record.Title = ret.title
// 	record.FilesStatus = ret.filesStatus
// 	record.FilesNum = ret.filesNum
//
// 	models.SaveCollectRecord(record)
// }

func ConvertShortLink(ctx context.Context, origin string) (string, *biz_error.BizError) {
	logger := logrus.New()
	client := &http.Client{
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		},
	}

	req, err := http.NewRequest("GET", origin, nil)
	if err != nil {
		return origin, biz_error.NewBizErrorByError(err)
	}

	resp, err := client.Do(req)
	if err != nil {
		return origin, biz_error.NewBizErrorByError(err)
	}
	defer resp.Body.Close()

	if resp.StatusCode >= 300 && resp.StatusCode <= 399 {
		url, err := resp.Location()
		if err != nil {
			return origin, biz_error.NewBizErrorByError(err)
		}

		logger.Debugf("convertShortLink[%s => %+v]", origin, url)
		redirectUrl := fmt.Sprintf("%s://%s%s", url.Scheme, url.Host, url.Path)
		fmt.Printf("convertShortLink[%s => %s]", origin, redirectUrl)
		origin = redirectUrl
	}

	return origin, nil
}

func AddToFavorites(ctx context.Context, collectType int64, origin, title, savePath, remark string) (string, *biz_error.BizError) {
	logger := logrus.New()

	// Part1. 参数处理
	// 先简单过滤错误的url格式
	if !common.IsValidUrl(origin) {
		logger.Warnf("Wrong Url Format, origin[%s]", origin)
		return fmt.Sprintf("Wrong Url Format %s", origin), nil
	}
	if savePath == "" {
		savePath = "/"
	}

	// B站短链接转长链接（否则bilix不支持series拉取）
	origin, err := ConvertShortLink(ctx, origin)

	// Part2. 业务逻辑
	// 先创建表记录
	record := models.CollectRecord{
		CollectType:   collectType,
		Origin:        origin,
		SavePath:      savePath,
		Title:         &title,
		Remark:        nil,
		StatusCode:    int64(common.CollectRecordStatusPending), // 初始状态，待拉取
		StatusMessage: "pending",                                // 初始状态，待拉取
		Env:           nil,
	}
	logger.Infof("record[%+v]", record)

	if remark != "" {
		record.Remark = &remark
	}
	env := os.Getenv("ENV")
	if env != "" {
		record.Env = &env
	}
	// treetheme_mode := os.Getenv("TREETHEME_INSTALL_MODE")
	// if treetheme_mode != "" {
	// 	record.TreethemeMode = &treetheme_mode
	// }
	err = models.CreateCollectRecord(&record)
	if err != nil {
		logger.Errorf("err[%+v]", err)
		return "", err
	}
	logger.Infof("after record[%+v] title[%+v]", record, common.StrPtr2Str(record.Title))

	// 这里应该是异步任务拉取
	// go asyncPullResourceTask(ctx, &record, origin, savePath)
	// 改成通过mq的方式
	// err = mq.SendPullResourceTaskMsg(ctx, record.ID, collectType, origin, savePath)
	err = mq.SendPullResourceTaskMsg(ctx, &record, common.TaskSourceTypeUser)
	if err != nil {
		logger.Errorf("err[%+v]", err)
		return "", err
	}

	return "create task success", nil
}

func GetFavoritesPage(ctx context.Context, offset, count int64, keyword string) ([]*favoritesGen.FavoritesItem, int64, bool, error) {
	logger := logrus.New()

	var total int64
	var records []*models.CollectRecord
	var err error

	if keyword != "" {
		// 搜索模式
		total, err = models.CountSearchCollectRecords(keyword)
		if err != nil {
			logger.Errorf("CountSearchCollectRecords err[%+v]", err)
			return nil, 0, false, err
		}

		records, err = models.SearchCollectRecordList(keyword, offset, count)
		if err != nil {
			logger.Errorf("SearchCollectRecordList err[%+v]", err)
			return nil, 0, false, err
		}
	} else {
		// 普通列表模式
		total, err = models.CountCollectRecords()
		if err != nil {
			logger.Errorf("CountCollectRecords err[%+v]", err)
			return nil, 0, false, err
		}

		records, err = models.SelectCollectRecordList(offset, count)
		if err != nil {
			logger.Errorf("SelectCollectRecordList err[%+v]", err)
			return nil, 0, false, err
		}
	}

	// 计算是否还有更多数据
	hasMore := offset+count < total

	retList := make([]*favoritesGen.FavoritesItem, len(records))
	author := "66"
	for i, r := range records {
		retList[i] = &favoritesGen.FavoritesItem{
			ID:         r.ID,
			Origin:     r.Origin,
			Title:      r.Title,
			Author:     &author,
			State:      r.StatusMessage,
			CreateTime: common.FormatTime(r.CreateTime),
		}
	}
	return retList, total, hasMore, nil
}

func DeleteFavorite(ctx context.Context, favoriteId int64) *biz_error.BizError {
	logger := logrus.New()

	err := models.DeleteCollectRecord(favoriteId)
	if err != nil {
		logger.Errorf("DeleteCollectRecord err[%+v]", err)
		return biz_error.NewBizErrorByError(err)
	}

	logger.Infof("Successfully deleted favorite with ID: %d", favoriteId)
	return nil
}


