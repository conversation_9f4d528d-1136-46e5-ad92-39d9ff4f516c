package service

import (
	"context"
	"testing"

	. "github.com/smartystreets/goconvey/convey"

	"favorites-service/config"
	"favorites-service/models"
)

func init() {
	ctx := context.Background()
	config.InitConfig(ctx)
	models.InitMysql(ctx)
}

func TestGetFavoritesPagePagination(t *testing.T) {
	Convey("测试分页功能", t, func() {
		ctx := context.Background()

		Convey("测试基本分页功能", func() {
			// 测试第一页
			list, total, hasMore, err := GetFavoritesPage(ctx, 0, 5)
			So(err, ShouldBeNil)
			So(list, ShouldNotBeNil)
			So(total, ShouldBeGreaterThanOrEqualTo, 0)
			
			if total > 5 {
				So(hasMore, ShouldBeTrue)
				So(len(list), ShouldEqual, 5)
			} else {
				So(hasMore, ShouldBeFalse)
				So(len(list), ShouldEqual, total)
			}
		})

		Convey("测试第二页", func() {
			// 先获取总数
			_, total, _, err := GetFavoritesPage(ctx, 0, 5)
			So(err, ShouldBeNil)
			
			if total > 5 {
				// 测试第二页
				list, total2, hasMore, err := GetFavoritesPage(ctx, 5, 5)
				So(err, ShouldBeNil)
				So(total2, ShouldEqual, total) // 总数应该一致
				
				expectedCount := total - 5
				if expectedCount > 5 {
					expectedCount = 5
					So(hasMore, ShouldBeTrue)
				} else {
					So(hasMore, ShouldBeFalse)
				}
				So(len(list), ShouldEqual, expectedCount)
			}
		})

		Convey("测试边界情况", func() {
			// 测试offset超出范围
			list, total, hasMore, err := GetFavoritesPage(ctx, 99999, 10)
			So(err, ShouldBeNil)
			So(len(list), ShouldEqual, 0)
			So(hasMore, ShouldBeFalse)
			So(total, ShouldBeGreaterThanOrEqualTo, 0)
		})

		Convey("测试count为0", func() {
			list, total, hasMore, err := GetFavoritesPage(ctx, 0, 0)
			So(err, ShouldBeNil)
			So(len(list), ShouldEqual, 0)
			So(total, ShouldBeGreaterThanOrEqualTo, 0)
			So(hasMore, ShouldBeFalse)
		})

		Convey("测试大的count值", func() {
			list, total, hasMore, err := GetFavoritesPage(ctx, 0, 1000)
			So(err, ShouldBeNil)
			So(len(list), ShouldBeLessThanOrEqualTo, total)
			So(hasMore, ShouldBeFalse) // 获取了所有数据，应该没有更多
		})
	})
}

func TestCountCollectRecords(t *testing.T) {
	Convey("测试记录计数功能", t, func() {
		count, err := models.CountCollectRecords()
		So(err, ShouldBeNil)
		So(count, ShouldBeGreaterThanOrEqualTo, 0)
	})
}
