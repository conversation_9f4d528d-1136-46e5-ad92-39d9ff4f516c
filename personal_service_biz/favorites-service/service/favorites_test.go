package service

import (
	"context"
	"testing"

	. "github.com/smartystreets/goconvey/convey"

	"favorites-service/config"
)

func init() {
	ctx := context.Background()
	config.InitConfig(ctx)
}

func TestGetDiskInfo(t *testing.T) {
	Convey("should success", t, func() {
		// Mock(config.GetStorePath).Return("/data/docker-volumes/favorites-service/store_root").Build()
		ctx := context.Background()
		GetDiskInfo(ctx, 0, "")
	})
}

func TestConvertShortLink(t *testing.T) {
	Convey("should success", t, func() {
		ctx := context.Background()
		ConvertShortLink(ctx, "https://b23.tv/tpPfMwz")
	})
}
