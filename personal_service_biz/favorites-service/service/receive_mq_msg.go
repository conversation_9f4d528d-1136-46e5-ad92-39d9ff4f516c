package service

import (
	"context"
	"encoding/json"
	"favorites-service/common"
	"favorites-service/models"
	"favorites-service/mq"
	"fmt"
	"regexp"
	"strconv"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// [[{"id": "1/1", "url": "https://b23.tv/IiXHhQJ?p=1", "title": "既然所有生命都要死亡，那么活着的意义是什么？请看完！", "format": "", "container": "", "quality": "", "size": 0, "sync_status": "101", "sync_unit_type": "1"}]]
type MqMsgVideo struct {
	ID           string  `json:"id"`
	Title        string  `json:"title"`
	Format       string  `json:"format"`
	SyncStatus   *string `json:"sync_status"`
	Container    string  `json:"container"`
	Quality      string  `json:"quality"`
	Size         string  `json:"size"`
	Filename     string  `json:"filename"`
	Realsize     string  `json:"realsize"`
	FileType     *string `json:"filetype"`
	Sha256       *string `json:"sha256"`
	SyncUnitType *string `json:"sync_unit_type"`
}

func (v MqMsgVideo) getSyncUnitType() int64 {
	logger := logrus.New()
	if v.SyncUnitType == nil {
		return int64(0)
	}
	unitType, err := strconv.ParseInt(*v.SyncUnitType, 10, 64)
	if err != nil {
		logger.Errorf("getSize v[%+v] err[%+v]", v, err)
		return int64(0)
	}
	return unitType
}

func (v MqMsgVideo) getFilename() *string {
	if v.Filename == "" {
		return nil
	}
	return &v.Filename
}

func (v MqMsgVideo) getSize() int64 {
	logger := logrus.New()
	size, err := strconv.ParseInt(v.Size, 10, 64)
	if err != nil {
		logger.Errorf("getSize v[%+v] err[%+v]", v, err)
		return int64(0)
	}
	return size
}

func (v MqMsgVideo) getRealsize() *int64 {
	logger := logrus.New()
	if v.Realsize == "" {
		return nil
	}

	realsize, err := strconv.ParseInt(v.Realsize, 10, 64)
	if err != nil {
		logger.Errorf("getRealsize v[%+v] err[%+v]", v, err)
		return nil
	}

	return &realsize
}

func (v MqMsgVideo) getSyncStatus() *int64 {
	logger := logrus.New()
	if v.SyncStatus == nil {
		return nil
	}

	status, err := strconv.ParseInt(*v.SyncStatus, 10, 64)
	if err != nil {
		logger.Errorf("getSyncStatus err[%+v]", err)
		return nil
	}

	return &status
}

func (v MqMsgVideo) getParsedID() (int64, int64) {
	logger := logrus.New()
	re := regexp.MustCompile("^([0-9]+)/([0-9]+)$")
	match := re.FindStringSubmatch(v.ID)
	if len(match) == 0 {
		logger.Errorf("getParsedID not match, v[%+v]", v)
		return int64(0), int64(0)
	}

	id, err := strconv.ParseInt(match[1], 10, 64)
	if err != nil {
		logger.Errorf("getParsedID v[%+v] err[%+v]", v, err)
		return int64(0), int64(0)
	}

	total, err := strconv.ParseInt(match[2], 10, 64)
	if err != nil {
		logger.Errorf("getParsedID v[%+v] err[%+v]", v, err)
		return int64(0), int64(0)
	}

	return id, total
}

func videoSlice2Map(videos []*models.VideoRecord) map[int64]*models.VideoRecord {
	vMap := map[int64]*models.VideoRecord{}
	for _, v := range videos {
		vMap[v.FileID] = v
	}
	return vMap
}

type FilesStatus struct {
	MetaSuccessCount  *int               `json:"meta_success,omitempty"`
	MetaFailCount     *int               `json:"meta_fail,omitempty"`
	MetaErrDetail     *map[string]string `json:"meta_err_detail,omitempty"`
	FileSuccessCount  *int               `json:"file_success,omitempty"`
	FileFailCount     *int               `json:"file_fail,omitempty"`
	FileErrDetail     *map[string]string `json:"file_err_detail,omitempty"`
	CheckSuccessCount *int               `json:"check_success,omitempty"`
	CheckFailCount    *int               `json:"check_fail,omitempty"`
	CheckErrDetail    *map[string]string `json:"check_err_detail,omitempty"`
}

func (f FilesStatus) ToString() string {
	bytes, err := json.Marshal(f)
	if err != nil {
		panic(err)
	}
	return string(bytes)
}

func UpdateFilesStatus(ctx context.Context, collectRecord *models.CollectRecord, successCount, failCount int, errorMap map[string]string) *string {
	if collectRecord == nil {
		return nil
	}

	var status FilesStatus

	if collectRecord.FilesStatus != nil && *collectRecord.FilesStatus != "" {
		err := json.Unmarshal([]byte(*collectRecord.FilesStatus), &status)
		if err != nil {
			return common.CreateStrPtr(fmt.Sprintf("updateFilesStatus err[%+v]", err))
		}
	}

	// bytes, err := json.Marshal(errorMap)
	// if err != nil {
	// 	return common.CreateStrPtr(fmt.Sprintf("Marshal err[%+v]", err))
	// }
	// errDetail := string(bytes)

	if collectRecord.StatusCode == int64(common.CollectRecordStatusPullMeta) ||
		collectRecord.StatusCode == int64(common.CollectRecordStatusLasyDown) {
		// pull meta消息
		if successCount != 0 {
			status.MetaSuccessCount = &successCount
		}
		if failCount != 0 {
			status.MetaFailCount = &failCount
		}
		if len(errorMap) != 0 {
			status.MetaErrDetail = &errorMap
		}
	} else if collectRecord.StatusCode == int64(common.CollectRecordStatusDownloadFile) {
		// download files消息
		if successCount != 0 {
			status.FileSuccessCount = &successCount
		}
		if failCount != 0 {
			status.FileFailCount = &failCount
		}
		if len(errorMap) != 0 {
			status.FileErrDetail = &errorMap
		}
	} else if collectRecord.StatusCode == int64(common.CollectRecordStatusPartial) || collectRecord.StatusCode == int64(common.CollectRecordStatusSuccess) {
		// check消息
		if successCount != 0 {
			status.CheckSuccessCount = &successCount
		}
		if failCount != 0 {
			status.CheckFailCount = &failCount
		}
		if len(errorMap) != 0 {
			status.CheckErrDetail = &errorMap
		}
	} else {
		return common.CreateStrPtr(fmt.Sprintf("Wrong StatusCode[%+v]", collectRecord.StatusCode))
	}

	return common.CreateStrPtr(status.ToString())
}

func updateVideoRecord(ctx context.Context, db *gorm.DB, collectRecord *models.CollectRecord, fileStatus *string) *string {
	if collectRecord == nil {
		return nil
	}

	if fileStatus == nil {
		return collectRecord.FilesStatus
	}
	logger := logrus.New().WithField("collectID", collectRecord.ID)

	var mqVideos []MqMsgVideo
	if err := json.Unmarshal([]byte(*fileStatus), &mqVideos); err != nil {
		return common.CreateStrPtr(fmt.Sprintf("Unmarshal mqVideos err[%+v] fileStatus[%+v]", err, *fileStatus))
	}

	videos, err := models.SelectVideoRecordListByCollectID(ctx, db, collectRecord.ID)
	if err != nil {
		return common.CreateStrPtr(fmt.Sprintf("SelectVideoRecordListByCollectID err[%+v]", err))
	}

	successCount := 0
	errorMap := map[string]string{}
	videosMap := videoSlice2Map(videos)
	for _, mqV := range mqVideos {
		id, total := mqV.getParsedID()
		if id == int64(0) {
			errorMap[mqV.ID] = "illegal id"
			continue
		}

		realsize := mqV.getRealsize()
		syncStatus := mqV.getSyncStatus()

		if v, ok := videosMap[id]; ok {
			// 已经存在的video，进行更新
			v.RealSize = realsize
			v.FileName = mqV.getFilename()
			if mqV.Sha256 != nil {
				v.CheckSum = mqV.Sha256
			}
			if mqV.FileType != nil {
				v.FileType = mqV.FileType
			}
			if syncStatus != nil {
				v.SyncStatus = *syncStatus
			}
			err := models.SaveVideoRecord(ctx, db, v)
			if err != nil {
				logger.Errorf("SaveVideoRecord id[%d] err[%+v]", id, err)
				errorMap[string(mqV.ID)] = fmt.Sprintf("SaveVideoRecord id [%d] err[%+v]", id, err)
			} else {
				successCount += 1
			}
		} else {
			// 不存在的video，插入新纪录
			v := &models.VideoRecord{
				CollectRecordID: collectRecord.ID,
				FileID:          id,
				TotalFile:       total,
				Title:           mqV.Title,
				Format:          mqV.Format,
				Container:       mqV.Container,
				Quality:         mqV.Quality,
				Size:            mqV.getSize(),
				SyncUnitType:    mqV.getSyncUnitType(),
				FileName:        &mqV.Filename,
				RealSize:        realsize,
				CheckSum:        mqV.Sha256,
			}
			if syncStatus != nil {
				v.SyncStatus = *syncStatus
			}
			err := models.CreateVideoRecord(ctx, db, v)
			if err != nil {
				logger.Errorf("CreateVideoRecord id[%d] err[%+v]", id, err)
				errorMap[mqV.ID] = fmt.Sprintf("CreateVideoRecord id [%d] err[%+v]", id, err)
			} else {
				successCount += 1
			}
		}
	}

	return UpdateFilesStatus(ctx, collectRecord, successCount, len(errorMap), errorMap)
}

func ReceivePullResourceStatusMsg(body string) error {
	ctx := context.Background()
	logger := logrus.New()
	logger.Infof("ReceivePullResourceStatusMsg body %s", body)
	ret := mq.PullResourceStatusMsg{}
	if err := json.Unmarshal([]byte(body), &ret); err != nil {
		logger.Errorf("err[%+v]", err)
		return err
	}

	var db *gorm.DB
	if ret.TaskSource == int64(common.TaskSourceTypeUser) || ret.TaskSource == int64(common.TaskSourceTypeBackground) {
		db = models.GetDB()
	} else if ret.TaskSource == int64(common.TaskSourceTypeDelayFetch) {
		db = models.GetCloudDB()
		if db == nil {
			logger.Errorf("CloudDB have not initialed yet")
			return fmt.Errorf("CloudDB have not initialed yet")
		}
	} else {
		logger.Errorf("Wrong TaskSource, ret[%+v]", ret)
		return fmt.Errorf("Wrong TaskSource[%d]", ret.TaskSource)
	}

	record, err := models.SelectCollectRecordByID(ctx, db, ret.CollectRecordID)
	if err != nil {
		logger.Errorf("err[%+v]", err)
		return err
	}

	if record.DurationDetails == nil || *record.DurationDetails == "" {
		d := "{}"
		record.DurationDetails = &d
	}

	// durationDetails还是决定放在collect_record，因为跟收藏记录相关，而非实际视频记录
	if ret.DurationDetails != nil {
		dd := map[string]int{}
		if err := json.Unmarshal([]byte(*ret.DurationDetails), &dd); err != nil {
			logger.Errorf("err[%+v]", err)
		} else {
			detail := map[string]int{}
			if e := json.Unmarshal([]byte(*record.DurationDetails), &detail); e != nil {
				logger.Errorf("err[%+v]", e)
			} else {
				// 只更新分阶段赋值的耗时
				for k, v := range dd {
					detail[k] = v
				}
				b, _ := json.Marshal(detail)
				s := string(b)
				record.DurationDetails = &s
			}
		}
	}

	record.StatusCode = ret.StatusCode
	record.StatusMessage = ret.StatusMessage
	record.Duration = ret.Duration

	logger.Infof("record.Title[%+v] ret.Title[%+v]",
		common.StrPtr2Str(record.Title),
		common.StrPtr2Str(ret.Title))
	if ret.Title != nil && record.Title == nil {
		record.Title = ret.Title
	}
	// if ret.FilesStatus != nil {
	// 	record.FilesStatus = ret.FilesStatus
	// }
	if ret.FilesNum != nil {
		record.FilesNum = ret.FilesNum
	}
	if ret.TotalSize != nil {
		record.TotalSize = common.CreateStrPtr(common.ConvertSize(uint64(*ret.TotalSize)))
	}
	if ret.FilesMissingIds != nil {
		record.FilesMissingIds = ret.FilesMissingIds
	}

	record.FilesStatus = updateVideoRecord(ctx, db, record, ret.FilesStatus)
	err = models.SaveCollectRecord(ctx, db, record)
	if err != nil {
		logger.Errorf("SaveCollectRecord err[%+v]", err)
	}
	return nil
}
