package service

// import (
// 	"testing"
//
// 	. "github.com/smartystreets/goconvey/convey"
// )
//
// func TestMqMsgVideo(t *testing.T) {
// 	Convey("should success", t, func() {
// 		msg := MqMsgVideo{
// 			id: "2/4a",
// 		}
// 		id := msg.getParsedID()
// 		So(id, ShouldEqual, int64(0))
// 	})
//
// 	Convey("should success", t, func() {
// 		msg := MqMsgVideo{
// 			id: "a2/4",
// 		}
// 		id := msg.getParsedID()
// 		So(id, ShouldEqual, int64(0))
// 	})
//
// 	Convey("should success", t, func() {
// 		msg := MqMsgVideo{
// 			id: "2/4",
// 		}
// 		id := msg.getParsedID()
// 		So(id, ShouldEqual, int64(2))
// 	})
// }
