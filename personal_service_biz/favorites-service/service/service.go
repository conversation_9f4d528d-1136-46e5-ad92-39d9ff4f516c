package service

import (
	"context"
	"encoding/json"
	"strconv"
	"thrift-common/gen-go/base"
	favoritesGen "thrift-common/gen-go/favorites"
)

func Wrap(data interface{}, err error) (string, error) {
	if err != nil {
		return "", err
	}
	ret, err := json.<PERSON>(data)
	if err != nil {
		return "", err
	}
	return string(ret), nil
}

// 定义服务
type FavoritesService struct {
}

func (this *FavoritesService) CallbackDelayFetch(ctx context.Context, req *favoritesGen.CallbackDelayFetchRequest) (*favoritesGen.CallbackDelayFetchResponse, error) {
	code := 0
	msg := "success"
	err := CallbackDelayFetch(ctx, req.SyncMetaTable, req.CloudDbTunnel)
	if err != nil {
		code = err.Code()
		msg = err.Msg()
	}
	res := &favoritesGen.CallbackDelayFetchResponse{
		BaseResp: &base.BaseResponse{
			StatusCode:    int64(code),
			StatusMessage: msg,
		},
	}
	return res, nil
}

func (this *FavoritesService) CallbackSyncData(ctx context.Context, req *favoritesGen.CallbackSyncDataRequest) (*favoritesGen.CallbackSyncDataResponse, error) {
	code := 0
	msg := "success"
	err := CallbackSyncData(ctx, req.SyncMetaTable, req.CloudDbTunnel, req.SyncTunnel)
	if err != nil {
		code = err.Code()
		msg = err.Msg()
	}
	res := &favoritesGen.CallbackSyncDataResponse{
		BaseResp: &base.BaseResponse{
			StatusCode:    int64(code),
			StatusMessage: msg,
		},
	}
	return res, nil
}

func (this *FavoritesService) AddToFavorites(ctx context.Context, req *favoritesGen.AddToFavoritesRequest) (*favoritesGen.AddToFavoritesResponse, error) {
	code := 0
	msg := "success"

	iCollectType, _ := strconv.ParseInt(req.CollectType, 10, 64)
	_, err := AddToFavorites(ctx, iCollectType, req.Origin, req.Title, req.SavePath, req.Remark)
	if err != nil {
		code = err.Code()
		msg = err.Msg()
	}
	res := &favoritesGen.AddToFavoritesResponse{
		BaseResp: &base.BaseResponse{
			StatusCode:    int64(code),
			StatusMessage: msg,
		},
	}
	return res, nil
}

func (this *FavoritesService) GetFavoritesPage(ctx context.Context, req *favoritesGen.GetFavoritesPageRequest) (*favoritesGen.GetFavoritesPageResponse, error) {
	keyword := ""
	if req.Keyword != nil {
		keyword = *req.Keyword
	}
	list, total, hasMore, err := GetFavoritesPage(ctx, req.Offset, req.Count, keyword)
	if err != nil {
		return nil, err
	}
	res := &favoritesGen.GetFavoritesPageResponse{
		FavoritesList: list,
		Total:         total,
		HasMore:       hasMore,
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}
	return res, nil
}

func (this *FavoritesService) DeleteFavorite(ctx context.Context, req *favoritesGen.DeleteFavoriteRequest) (*favoritesGen.DeleteFavoriteResponse, error) {
	code := 0
	msg := "success"

	err := DeleteFavorite(ctx, req.FavoriteId)
	if err != nil {
		code = err.Code()
		msg = err.Msg()
	}

	res := &favoritesGen.DeleteFavoriteResponse{
		BaseResp: &base.BaseResponse{
			StatusCode:    int64(code),
			StatusMessage: msg,
		},
	}
	return res, nil
}

func (this *FavoritesService) GetDiskInfo(ctx context.Context, req *favoritesGen.GetDiskInfoRequest) (*favoritesGen.GetDiskInfoResponse, error) {
	label, usage, progress, tree, err := GetDiskInfo(ctx, req.Depth, req.Root)
	if err != nil {
		return nil, err
	}
	res := &favoritesGen.GetDiskInfoResponse{
		DiskLabel:    label,
		DiskUsage:    usage,
		DiskProgress: progress,
		FsTree:       tree,
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}
	return res, nil
}


