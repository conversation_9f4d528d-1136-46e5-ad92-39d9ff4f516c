package wash_data

import (
	"encoding/json"
	"favorites-service/common"
	"favorites-service/models"

	"github.com/sirupsen/logrus"
)

func WashCollectRecord() {
	logger := logrus.New()
	records, err := models.SelectCollectRecordList(0, 400)
	if err != nil {
		logger.Errorf("err[%+v]", err)
		panic(err)
	}

	for _, c := range records {
		videos, err := models.SelectVideoRecordListByCollectID(c.ID)
		if err != nil {
			logger.Errorf("err[%+v]", err)
			panic(err)
		}

		if len(videos) != int(*c.FilesNum) {
			logger.Warnf("[%d/%s] len[%d] != filenum[%d]", c.ID, *c.Title, len(videos), *c.FilesNum)
			continue
		}

		successCount := 0
		recordCount := 0
		totalSize := uint64(0)
		for _, v := range videos {
			if v.FileType != nil {
				successCount += 1
			}
			recordCount += 1
			totalSize += uint64(v.<PERSON>ze)
		}

		var filesStatus map[string]int
		if successCount == len(videos) {
			filesStatus = map[string]int{
				"meta_success":  successCount,
				"check_success": successCount,
			}
			c.TotalSize = common.CreateStrPtr(common.ConvertSize(totalSize))
		} else {
			filesStatus = map[string]int{
				"meta_success":  recordCount,
				"check_success": successCount,
			}
		}
		status, _ := json.Marshal(filesStatus)
		c.FilesStatus = common.CreateStrPtr(string(status))
		err = models.SaveCollectRecord(c)
		if err != nil {
			logger.Errorf("err[%+v]", err)
			panic(err)
		}
	}
}
