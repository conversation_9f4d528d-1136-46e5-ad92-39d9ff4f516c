package wash_data

import (
	"context"
	"encoding/json"
	"favorites-service/common"
	"favorites-service/models"
	"favorites-service/service"
	"regexp"
	"strconv"
	"strings"

	"github.com/sirupsen/logrus"
)

func updateDB(collect *models.CollectRecord, videos []*models.VideoRecord) {
	logger := logrus.New()
	allSuccess := true
	for _, v := range videos {
		err := models.CreateVideoRecord(v)
		if err != nil {
			logger.Errorf("CreateVideoRecord v[%+v] err[%+v]", v, err)
			allSuccess = false
		}
	}

	if allSuccess {
		successCount := len(videos)
		errorMap := map[string]string{}
		collect.FilesStatus = service.UpdateFilesStatus(collect, successCount, 0, errorMap)
		err := models.SaveCollectRecord(collect)
		if err != nil {
			logger.Errorf("SaveCollectRecord c[%+v] err[%+v]", collect, err)
			panic(err)
		}
		logger.Infof("wash success")
	}
}

func washFileStatus(ctx context.Context, collect *models.CollectRecord) {
	logger := logrus.New()
	files := []map[string]string{}
	err := json.Unmarshal([]byte(*collect.FilesStatus), &files)
	if err != nil {
		logger.Errorf("Unmarshal err[%+v]", err)
		panic(err)
	}

	// logger.Infof("  files[%d]", len(files))
	videos := []*models.VideoRecord{}
	totalSize := int64(0)
	for _, f := range files {
		fileIDStr := strings.Split(f["id"], "/")
		fileID, err := strconv.ParseInt(fileIDStr[0], 10, 64)
		if err != nil {
			logger.Errorf("ParseInt err[%+v]", err)
			panic(err)
		}
		total, err := strconv.ParseInt(fileIDStr[1], 10, 64)
		if err != nil {
			logger.Errorf("ParseInt err[%+v]", err)
			panic(err)
		}
		if int(total) != len(files) {
			panic("wrong total")
		}

		re := regexp.MustCompile("(\\d+)\\sbytes")
		match := re.FindStringSubmatch(f["size"])
		// logger.Infof("match[%+v]", match)
		size, err := strconv.ParseInt(match[1], 10, 64)
		if err != nil {
			logger.Errorf("ParseInt err[%+v]", err)
			panic(err)
		}

		var realSize *int64
		if _, ok := f["realsize"]; ok {
			rs, err := strconv.ParseInt(f["realsize"], 10, 64)
			if err != nil {
				logger.Warnf("ParseInt err[%+v]", err)
				// 此处因为之前有bug导致一些脏数据，这里做null修复先
				// panic(err)
			} else {
				realSize = &rs
			}
		}

		// logger.Infof("  filesID[%s] parseID[%d] [%s]", f["id"], fileID, f["title"])

		v := &models.VideoRecord{
			CollectRecordID: collect.ID,
			FileID:          fileID,
			TotalFile:       total,
			Title:           f["title"],
			FileName:        common.CreateStrPtr(f["filename"]),
			Format:          f["format"],
			Container:       f["container"],
			Quality:         f["quality"],
			Size:            size,
			RealSize:        realSize,
			CheckSum:        common.CreateStrPtr(f["sha256"]),
		}

		totalSize += size
		videos = append(videos, v)
		// logger.Infof("  v[%+v]", v)
	}
	if totalSize > 512*1024*1024 {
		// 超过512M就延迟pull
		for i, _ := range videos {
			videos[i].SyncStatus = int64(common.SyncStatusLazyPull)
		}
		logger.Infof("  videos(%d)[%+v]", len(videos), videos[0].Title)
		logger.Infof("  totalSize[%+v]", common.ConvertSize(uint64(totalSize)))
	} else {
		for i, _ := range videos {
			videos[i].SyncStatus = int64(common.SyncStatusUnsync)
		}
	}

	updateDB(collect, videos)
}

func WashCollectRecordFilesStatus(ctx context.Context) {
	logger := logrus.New()
	list, err := models.SelectCollectRecordList(0, 200)
	if err != nil {
		logger.Errorf("err[%+v]", err)
		panic(err)
	}

	uniqueMap := map[string]bool{}
	total := 0
	uniqueTotal := 0
	for i, d := range list {
		if d.CollectType != 1001 {
			continue
		}
		total += 1
		title := *d.Title
		if _, ok := uniqueMap[title]; ok {
			// 重复记录
			continue
		}
		uniqueMap[title] = true
		uniqueTotal += 1
		logger.Infof("\n[%d/%d/%d] [%d] [%+v]", i, total, uniqueTotal, d.ID, title)
		washFileStatus(ctx, d)
	}
}
