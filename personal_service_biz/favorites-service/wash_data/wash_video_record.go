package wash_data

import (
	"context"
	"favorites-service/models"
	"fmt"
	"io/fs"
	"io/ioutil"
	"os/exec"
	"strconv"
	"strings"

	"github.com/sirupsen/logrus"
)

func WashVideoRecord(ctx context.Context) {
	logger := logrus.New()
	records, err := models.SelectVideoRecordList(0, 400)
	if err != nil {
		logger.Errorf("err[%+v]", err)
		panic(err)
	}

	rootPath := "/data/docker-volumes/public_root/file_resources/bilibili/"
	files, err := ioutil.ReadDir(rootPath)
	if err != nil {
		logger.Errorf("err[%+v]", err)
		panic(err)
	}

	fileMap := map[string]fs.FileInfo{}
	for _, f := range files {
		if strings.Contains(f.Name(), ".cmt.xml") {
			continue
		}

		if f.IsDir() {
			logger.Infof("dir: %s", f.Name())
		} else {
			// logger.Infof("file: %s", f)
			fileMap[f.Name()] = f
		}
	}

	recordMap := map[string]bool{}
	for i, v := range records {
		if v.FileName == nil {
			continue
		}

		if v.FileType == nil {
			out, err := exec.Command("bash", "-c", fmt.Sprintf("file -b --mime-type '%s/%s'", rootPath, *v.FileName)).Output()
			if err != nil {
				panic(err)
			}
			fileType := strings.TrimSpace(string(out))
			logger.Infof("%s: %s\n", *v.FileName, fileType)
			v.FileType = &fileType
		}

		if v.RealSize == nil {
			out, err := exec.Command("bash", "-c", fmt.Sprintf("du -sb \"%s/%s\" | awk '{printf $1}'", rootPath, *v.FileName)).Output()
			if err != nil {
				panic(err)
			}
			realSize, err := strconv.ParseInt(strings.TrimSpace(string(out)), 10, 64)
			if err != nil {
				panic(err)
			}

			logger.Infof("%s: %d\n", *v.FileName, realSize)

			v.RealSize = &realSize
		}

		if v.CheckSum == nil {
			out2, err := exec.Command("bash", "-c", fmt.Sprintf("sha256sum '%s/%s' | awk '{printf $1}'", rootPath, *v.FileName)).Output()
			if err != nil {
				panic(err)
			}
			checkSum := strings.TrimSpace(string(out2))
			logger.Infof("%s: %s\n", *v.FileName, checkSum)
			v.CheckSum = &checkSum
		}

		recordMap[*v.FileName] = true
		models.SaveVideoRecord(v)

		if _, ok := fileMap[*v.FileName]; !ok {
			logger.Warnf("[%d] files not exist %+v", i, *v.FileName)
		}

		// file := filepath.Join("/data/docker-volumes/public_root/file_resources/bilibili/", *v.FileName)
		// _, err := os.Stat(file)
		// if err != nil {
		// 	logger.Warnf("[%d] not exist %+v", i, file)
		// }
	}

	for f, _ := range fileMap {
		if _, ok := recordMap[f]; !ok {
			logger.Warnf("db not exist %+v", f)
		}
	}
}
