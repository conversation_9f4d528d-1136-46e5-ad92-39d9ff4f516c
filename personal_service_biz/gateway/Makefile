
build:
	go build

run:
	ENV=dev go run main.go

# deploy要用root权限，但是build要用main_user权限（否则git status没权限报错）。所以不能用依赖关系
deploy:
	${CURDIR}/scripts/deploy.sh

TestFavoritesAdd:
	@./scripts/test.sh FavoritesAdd

TestFavoritesList:
	@./scripts/test.sh FavoritesList

TestFavoritesCallbackDelayFetch:
	@./scripts/test.sh FavoritesCallbackDelayFetch

TestTaskAdd:
	@./scripts/test.sh TaskAdd

TestTaskList:
	@./scripts/test.sh TaskList

TestTaskMark:
	@./scripts/test.sh TaskMark

TestTaskStart:
	@./scripts/test.sh TaskStart
