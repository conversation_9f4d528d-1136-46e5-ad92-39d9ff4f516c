package main

import (
	"context"
	"gateway/middlewares"
	"gateway/services"
	"thrift-common/server/gateway"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func main() {
	var err error
	ctx := context.Background()
	logger := logrus.New()

	// 启动thrift服务器
	gatewayHandler := &services.GatewayService{}
	go func() {
		logger.Info("Gateway Thrift Service Started!")
		err := gateway.Server(ctx, gatewayHandler)
		if err != nil {
			logger.Errorf("Gateway Thrift Server error: %+v", err)
		}
	}()

	// 启动HTTP服务器
	engine := gin.Default()
	err = middlewares.MountMiddlewares(engine)
	if err != nil {
		logger.Errorf("MountMiddlewares failed: %+v", err)
	}

	err = services.MountService(engine)
	if err != nil {
		logger.Errorf("MountService failed: %+v", err)
	}

	logger.Info("Gateway HTTP Service Started!")
	engine.Run(":16135") // listen and serve on 0.0.0.0:8080 (for windows "localhost:8080")
}
