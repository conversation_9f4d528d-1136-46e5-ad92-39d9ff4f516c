package middlewares

import (
	"fmt"
	"regexp"
	driveClient "thrift-common/client/drive"
	favoritesClient "thrift-common/client/favorites"
	notesClient "thrift-common/client/notes"
	taskClient "thrift-common/client/task"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func CheckPermissions(c *gin.Context) {
	logger := logrus.New()

	// 跳过WebSocket路由的认证检查
	if c.Request.URL.Path == "/api/drive/upload-progress" {
		return
	}

	bullet := c.<PERSON>Header("Bullet")
	logger.Infof("headers: %+v", c.Request.Header)
	// 浏览器modHeader用过
	// if bullet != "7b80cd58-9e3d-11ec-b113-525400043ced" {
	target := "36fdf066-9e42-11ec-b41e-525400043ced"
	if bullet != target {
		logger.Errorf("wrong header bullet[%+v] to target[%+v]", bullet, target)
		c.<PERSON><PERSON>(400, "fuck")
		c.AbortWithError(4003, fmt.Errorf("fuck"))
		return
	}

	// jsonData, err := ioutil.ReadAll(c.Request.Body)
	// if err != nil {
	// 	logger.Errorf("err: ", err)
	// 	c.JSON(400, "666")
	// 	c.AbortWithError(4003, fmt.Errorf("fuck"))
	// 	return
	// }

	// logger.Infof("== jsonData: %+v", string(jsonData))
}

func tryGetFavoritesClient(c *gin.Context, path string) (interface{}, error) {
	match, err := regexp.MatchString("/(inner_)?api/favorites", path)
	if err != nil {
		return nil, fmt.Errorf("MatchString err[%+v]", err)
	}

	if match {
		client, err := favoritesClient.GetFavoritesClient()
		if err != nil {
			return nil, fmt.Errorf("GetFavoritesClient err[%+v]", err)
		}
		return client, nil
	}
	return nil, nil
}

func tryGetTaskClient(c *gin.Context, path string) (interface{}, error) {
	match, err := regexp.MatchString("/api/task", path)
	if err != nil {
		return nil, fmt.Errorf("MatchString err[%+v]", err)
	}

	if match {
		client, err := taskClient.GetTaskClient()
		if err != nil {
			return nil, fmt.Errorf("GetTaskClient err[%+v]", err)
		}
		return client, nil
	}
	return nil, nil
}

func tryGetNotesClient(c *gin.Context, path string) (interface{}, error) {
	match, err := regexp.MatchString("/api/notes", path)
	if err != nil {
		return nil, fmt.Errorf("MatchString err[%+v]", err)
	}

	if match {
		client, err := notesClient.GetNotesClient()
		if err != nil {
			return nil, fmt.Errorf("GetNotesClient err[%+v]", err)
		}
		return client, nil
	}
	return nil, nil
}

func tryGetDriveClient(c *gin.Context, path string) (interface{}, error) {
	match, err := regexp.MatchString("/api/drive", path)
	if err != nil {
		return nil, fmt.Errorf("MatchString err[%+v]", err)
	}

	if match {
		client, err := driveClient.GetDriveClient()
		if err != nil {
			return nil, fmt.Errorf("GetDriveClient err[%+v]", err)
		}
		return client, nil
	}
	return nil, nil
}

type GetClientInterface func(c *gin.Context, path string) (interface{}, error)

func tryGetClient(c *gin.Context, tryGetClientFunc GetClientInterface) int {
	logger := logrus.New()
	path := c.Request.URL.Path
	logger.Infof("path[%s]", path)

	client, err := tryGetClientFunc(c, path)
	if err != nil {
		logger.Error(err)
	}
	if client != nil {
		c.Set("rpc_client", client)
		return 1
	}
	return 0
}

func GetClient(c *gin.Context) {
	count := 0
	count += tryGetClient(c, tryGetFavoritesClient)
	count += tryGetClient(c, tryGetTaskClient)
	count += tryGetClient(c, tryGetNotesClient)
	count += tryGetClient(c, tryGetDriveClient)

	if count != 1 {
		c.JSON(500, gin.H{
			"message": "GetClient Failed",
		})
	}
}

func MountMiddlewares(engine *gin.Engine) error {
	engine.Use(CheckPermissions)
	engine.Use(GetClient)
	return nil
}
