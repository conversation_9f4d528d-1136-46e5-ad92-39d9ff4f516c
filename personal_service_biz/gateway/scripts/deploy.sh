#!/bin/bash
cur_dir=$(dirname $(readlink -f $0))
root_dir=$(dirname ${cur_dir})
if [[ ! -f ${root_dir}/.version ]]; then
  echo "Wrong Root Directory"
  exit
fi

source ${root_dir}/config.ini
version_file=${root_dir}/.version
image_version=$(UpdateVersionPatch ${version_file})

image_name=gateway:${image_version}
docker build -t ${image_name} -f ${root_dir}/image/Dockerfile ${root_dir}/
docker image ls -a | grep gateway

path=/data/docker-volumes/${container_name}
if [[ -d ${path} ]]; then
  echo "Already Exist Path: ${path}"
else
  mkdir -p ${path}
fi

docker container rm -f ${container_name}
docker run -d --net admin-net -e ENV='deploy' \
  --name=${container_name} \
  -v ${path}/runtime/gateway-logs:/runtime/gateway-logs \
  ${image_name}
# docker container ls -a
docker container ls -f name=${container_name}
