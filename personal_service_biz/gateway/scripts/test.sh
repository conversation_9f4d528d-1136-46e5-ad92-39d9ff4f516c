#!/bin/bash
host="luzeshu.site"
api_prefix="https://${host}"
if [[ ${ENV} == dev ]]; then
    host="127.0.0.1:16135"
    # 注意区别是dev时用http
    api_prefix="http://${host}"
fi
unit=$1

black="\x1b[0;30m"
red="\x1b[0;31m"
green="\x1b[0;32m"
yellow="\x1b[0;33m"
blue="\x1b[0;34m"
purple="\x1b[0;35m"
cyan="\x1b[0;36m"
white="\x1b[0;37m"
bblack="\x1b[1;30m"
bred="\x1b[1;31m"
bgreen="\x1b[1;32m"
byellow="\x1b[1;33m"
bblue="\x1b[1;34m"
bpurple="\x1b[1;35m"
bcyan="\x1b[1;36m"
bwhite="\x1b[1;37m"
nocolor="\x1b[0m"

function printColor {
	local color=$1
	local title="$2"
	local info="$3"
    printf "${color}${title}: ${nocolor}${info}\n"
}

function printBRed {
	printColor ${bred} "$1" "$2"
}

function printBBlue {
	printColor ${bblue} "$1" "$2"
}

function printGreen {
	printColor ${green} "$1" "$2"
}

function TestFavoritesAdd {
    url="${api_prefix}/api/favorites/add"
    # origin="https://www.bilibili.com/bangumi/play/ss39865"
    origin="https://www.bilibili.com/video/BV1NM411C7ix"
    collect_type="1001"
    save_path="/economy/etc"
    postData='{"collect_type":"'${collect_type}'","origin":"'${origin}'","save_path":"'${save_path}'","remark":""}'

	printGreen "Url" "${url}"
	printGreen "  Param:origin" "${origin}"
	printGreen "  Param:collect_type" "${collect_type}"
	printGreen "  Param:save_path" "${save_path}"
	printGreen "  PostData" "${postData}"

    # curl -i -w '\n' -H "Content-Type: application/json" -X POST "http://127.0.0.1:16135/favorites/add" -d '{"test":"222"}'
    curl -L -i -w '\n' -H "Content-Type: application/json" \
        -H "Bullet: 36fdf066-9e42-11ec-b41e-525400043ced" \
        -X POST "${url}" \
        -d "${postData}"
}

function TestFavoritesList {
    url="${api_prefix}/api/favorites/list"

	printGreen "Url" "${url}"
    # curl -i -w '\n' -H "Content-Type: application/json" -X POST "http://127.0.0.1:16135/favorites/add" -d '{"test":"222"}'
    curl -L -i -w '\n' -H "Content-Type: application/json" \
        -H "Bullet: 36fdf066-9e42-11ec-b41e-525400043ced" \
        "${url}"
}

function TestFavoritesCallbackDelayFetch {
    url="${api_prefix}/api/favorites/callback/delay_fetch"
    postData='{"sync_meta_table":"video_record"}'

	printGreen "Url" "${url}"
    # curl -i -w '\n' -H "Content-Type: application/json" -X POST "http://127.0.0.1:16135/favorites/add" -d '{"test":"222"}'
    curl -L -i -w '\n' -H "Content-Type: application/json" \
        -H "Bullet: 36fdf066-9e42-11ec-b41e-525400043ced" \
        -X POST "${url}" \
        -d "${postData}"
}

function TestTaskAdd {
    url="${api_prefix}/api/task/add"
    title="测试任务"
    priority="0"
    parent="991104"
    postData='{"title":"'${title}'","priority":"'${priority}'","parent":"'${parent}'"}'

	printGreen "Url" "${url}"
	printGreen "  Param:title" "${title}"
	printGreen "  Param:parent" "${parent}"
	printGreen "  Param:priority" "${priority}"
	printGreen "  PostData" "${postData}"

    # curl -i -w '\n' -H "Content-Type: application/json" -X POST "http://127.0.0.1:16135/favorites/add" -d '{"test":"222"}'
    curl -L -i -w '\n' -H "Content-Type: application/json" \
        -H "Bullet: 36fdf066-9e42-11ec-b41e-525400043ced" \
        -X POST "${url}" \
        -d "${postData}"
}

function TestTaskMark {
    url="${api_prefix}/api/task/action"
    id_label="991104"
    content="666"
    postData='{"id_label":"'${id_label}'","action":"gains","content":"'${content}'"}'

	printGreen "Url" "${url}"
	printGreen "  Param:id_label" "${id_label}"
	printGreen "  Param:content" "${content}"
	printGreen "  PostData" "${postData}"

    curl -L -i -w '\n' -H "Content-Type: application/json" \
        -H "Bullet: 36fdf066-9e42-11ec-b41e-525400043ced" \
        -X POST "${url}" \
        -d "${postData}"
}

function TestTaskStart {
    url="${api_prefix}/api/task/action"
    id_label="991104"
    start_time=""
    start_time="2023-02-26 15:03:02"
    end_time="2023-02-26 15:03:09"
    postData='{"id_label":"'${id_label}'","action":"start","start_time":"'${start_time}'","end_time":"'${end_time}'"}'

	printGreen "Url" "${url}"
	printGreen "  Param:id_label" "${id_label}"
	printGreen "  Param:content" "${content}"
	printGreen "  PostData" "${postData}"

    curl -L -i -w '\n' -H "Content-Type: application/json" \
        -H "Bullet: 36fdf066-9e42-11ec-b41e-525400043ced" \
        -X POST "${url}" \
        -d "${postData}"
}

function TestTaskList {
    root=""
    url="${api_prefix}/api/task/list?root=${root}"
	printGreen "Url" "${url}"

    curl -L -i -w '\n' -H "Content-Type: application/json" \
        -H "Bullet: 36fdf066-9e42-11ec-b41e-525400043ced" \
        "${url}"
}

function main {
    printBRed "Unit" "${unit}"
    if [[ ${unit} == "FavoritesAdd" ]]; then
        TestFavoritesAdd
    elif [[ ${unit} == "FavoritesList" ]]; then
        TestFavoritesList
    elif [[ ${unit} == "FavoritesCallbackDelayFetch" ]]; then
        TestFavoritesCallbackDelayFetch
    elif [[ ${unit} == "TaskAdd" ]]; then
        TestTaskAdd
    elif [[ ${unit} == "TaskList" ]]; then
        TestTaskList
    elif [[ ${unit} == "TaskMark" ]]; then
        TestTaskMark
    elif [[ ${unit} == "TaskStart" ]]; then
        TestTaskStart
    else
        echo "Unknown UnitTest Name"
    fi
}

main
