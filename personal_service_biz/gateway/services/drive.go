package services

import (
	"context"
	"encoding/json"
	"io"
	"net/http"
	"strconv"
	"sync"
	driveGen "thrift-common/gen-go/drive"
	gatewayGen "thrift-common/gen-go/gateway"
	"thrift-common/gen-go/base"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

var (
	// WebSocket连接升级器
	upgrader = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true // 允许所有来源
		},
	}

	// 上传进度通知管理器
	progressManager = &ProgressManager{
		connections: make(map[int64][]*websocket.Conn),
	}
)

// 进度管理器
type ProgressManager struct {
	sync.RWMutex
	connections map[int64][]*websocket.Conn // taskID -> WebSocket连接列表
}

// 添加连接
func (pm *ProgressManager) AddConnection(taskID int64, conn *websocket.Conn) {
	pm.Lock()
	defer pm.Unlock()
	pm.connections[taskID] = append(pm.connections[taskID], conn)
}

// 移除连接
func (pm *ProgressManager) RemoveConnection(taskID int64, conn *websocket.Conn) {
	pm.Lock()
	defer pm.Unlock()
	conns := pm.connections[taskID]
	for i, c := range conns {
		if c == conn {
			pm.connections[taskID] = append(conns[:i], conns[i+1:]...)
			break
		}
	}
	if len(pm.connections[taskID]) == 0 {
		delete(pm.connections, taskID)
	}
}

// 广播进度
func (pm *ProgressManager) BroadcastProgress(taskID int64, progress float64, uploadedBytes, totalBytes int64) {
	pm.RLock()
	conns := pm.connections[taskID]
	pm.RUnlock()

	// 构建进度消息
	msg := map[string]interface{}{
		"task_id":        taskID,
		"progress":       progress,
		"uploaded_bytes": uploadedBytes,
		"total_bytes":    totalBytes,
	}
	msgBytes, _ := json.Marshal(msg)

	// 发送给所有连接
	for _, conn := range conns {
		conn.WriteMessage(websocket.TextMessage, msgBytes)
	}
}

// 获取文件列表
func GetFileList(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*driveGen.DriveClient)

	offset, e := strconv.ParseInt(c.DefaultQuery("offset", "0"), 10, 64)
	if e != nil {
		logger.Errorf("Wrong Params offset[%+v]", e)
		return
	}

	size, e := strconv.ParseInt(c.DefaultQuery("size", "10"), 10, 64)
	if e != nil {
		logger.Errorf("Wrong Params size[%+v]", e)
		return
	}

	// 限制每页最大数量
	if size > 100 {
		size = 100
	}
	if size <= 0 {
		size = 10
	}

	// 获取父目录ID参数
	var parentID *int64
	if parentIDStr := c.Query("parent_id"); parentIDStr != "" {
		if pid, err := strconv.ParseInt(parentIDStr, 10, 64); err == nil {
			parentID = &pid
		}
	}

	// 获取搜索关键词参数
	keyword := c.Query("keyword")

	logger.Infof("offset[%d] size[%d] parentID[%v] keyword[%s]", offset, size, parentID, keyword)

	req := &driveGen.GetFileListRequest{
		ParentID: parentID,
		Offset:   offset,
		Count:    size,
	}

	if keyword != "" {
		req.Keyword = &keyword
	}

	rsp, err := client.GetFileList(ctx, req)
	logger.Infof("rsp[%+v]", rsp)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"data":     rsp.FileList,
			"has_more": rsp.HasMore,
			"total":    rsp.Total,
		})
	}
}

// 创建文件夹
func CreateFolder(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*driveGen.DriveClient)

	var requestBody struct {
		Name     string `json:"name"`
		ParentID *int64 `json:"parent_id"`
	}

	if err := c.BindJSON(&requestBody); err != nil {
		logger.Errorf("BindJSON[%+v]", err)
		c.JSON(400, gin.H{
			"message": "invalid request body",
		})
		return
	}

	req := &driveGen.CreateFolderRequest{
		Name:     requestBody.Name,
		ParentID: requestBody.ParentID,
	}

	logger.Infof("req[%+v]", req)

	rsp, err := client.CreateFolder(ctx, req)
	logger.Infof("rsp[%+v]", rsp)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"message": base.StatusMessage,
			"data":    rsp.Folder,
		})
	}
}

// 上传文件
func UploadFile(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*driveGen.DriveClient)

	// 解析multipart form
	err := c.Request.ParseMultipartForm(32 << 20) // 32MB
	if err != nil {
		logger.Errorf("ParseMultipartForm err: %+v", err)
		c.JSON(400, gin.H{
			"message": "invalid form data",
		})
		return
	}

	// 获取文件
	file, header, err := c.Request.FormFile("file")
	if err != nil {
		logger.Errorf("FormFile err: %+v", err)
		c.JSON(400, gin.H{
			"message": "no file uploaded",
		})
		return
	}
	defer file.Close()

	// 读取文件内容
	fileData, err := io.ReadAll(file)
	if err != nil {
		logger.Errorf("ReadAll err: %+v", err)
		c.JSON(500, gin.H{
			"message": "failed to read file",
		})
		return
	}

	// 获取父目录ID
	var parentID *int64
	if parentIDStr := c.Request.FormValue("parent_id"); parentIDStr != "" {
		if pid, err := strconv.ParseInt(parentIDStr, 10, 64); err == nil {
			parentID = &pid
		}
	}

	req := &driveGen.UploadFileRequest{
		FileName: header.Filename,
		FileSize: int64(len(fileData)),
		ParentID: parentID,
		FileData: fileData,
	}

	logger.Infof("uploading file: %s, size: %d", header.Filename, len(fileData))

	rsp, err := client.UploadFile(ctx, req)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"message": base.StatusMessage,
			"data":    rsp.File,
		})
	}
}

// 初始化分片上传
func InitChunkUpload(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*driveGen.DriveClient)

	var requestBody struct {
		FileName  string `json:"file_name"`
		FileSize  int64  `json:"file_size"`
		ParentID  *int64 `json:"parent_id"`
		ChunkSize int64  `json:"chunk_size"`
	}

	if err := c.BindJSON(&requestBody); err != nil {
		logger.Errorf("BindJSON[%+v]", err)
		c.JSON(400, gin.H{
			"message": "invalid request body",
		})
		return
	}

	req := &driveGen.InitChunkUploadRequest{
		FileName:  requestBody.FileName,
		FileSize:  requestBody.FileSize,
		ParentID:  requestBody.ParentID,
		ChunkSize: requestBody.ChunkSize,
	}

	logger.Infof("initializing chunk upload: %s, size: %d", requestBody.FileName, requestBody.FileSize)

	rsp, err := client.InitChunkUpload(ctx, req)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"message": base.StatusMessage,
			"data":    rsp.UploadTask,
		})
	}
}

// 上传分片
func UploadChunk(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*driveGen.DriveClient)

	// 解析multipart form
	err := c.Request.ParseMultipartForm(32 << 20) // 32MB
	if err != nil {
		logger.Errorf("ParseMultipartForm err: %+v", err)
		c.JSON(400, gin.H{
			"message": "invalid form data",
		})
		return
	}

	// 获取参数
	taskIDStr := c.Request.FormValue("task_id")
	chunkIndexStr := c.Request.FormValue("chunk_index")

	taskID, err := strconv.ParseInt(taskIDStr, 10, 64)
	if err != nil {
		logger.Errorf("Invalid task ID: %+v", err)
		c.JSON(400, gin.H{
			"message": "invalid task id",
		})
		return
	}

	chunkIndex, err := strconv.ParseInt(chunkIndexStr, 10, 64)
	if err != nil {
		logger.Errorf("Invalid chunk index: %+v", err)
		c.JSON(400, gin.H{
			"message": "invalid chunk index",
		})
		return
	}

	// 获取分片数据
	file, _, err := c.Request.FormFile("chunk_data")
	if err != nil {
		logger.Errorf("FormFile err: %+v", err)
		c.JSON(400, gin.H{
			"message": "no chunk data uploaded",
		})
		return
	}
	defer file.Close()

	// 读取分片数据
	chunkData, err := io.ReadAll(file)
	if err != nil {
		logger.Errorf("ReadAll err: %+v", err)
		c.JSON(500, gin.H{
			"message": "failed to read chunk data",
		})
		return
	}

	req := &driveGen.UploadChunkRequest{
		TaskID:     taskID,
		ChunkIndex: chunkIndex,
		ChunkData:  chunkData,
	}

	logger.Infof("uploading chunk: task=%d, index=%d, size=%d", taskID, chunkIndex, len(chunkData))

	rsp, err := client.UploadChunk(ctx, req)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"message": base.StatusMessage,
			"data":    rsp.UploadTask,
		})
	}
}

// 完成分片上传
func CompleteChunkUpload(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*driveGen.DriveClient)

	var requestBody struct {
		TaskID string `json:"task_id"`
	}

	if err := c.BindJSON(&requestBody); err != nil {
		logger.Errorf("BindJSON[%+v]", err)
		c.JSON(400, gin.H{
			"message": "invalid request body",
		})
		return
	}

	taskID, err := strconv.ParseInt(requestBody.TaskID, 10, 64)
	if err != nil {
		logger.Errorf("Invalid task ID: %+v", err)
		c.JSON(400, gin.H{
			"message": "invalid task id",
		})
		return
	}

	req := &driveGen.CompleteChunkUploadRequest{
		TaskID: taskID,
	}

	logger.Infof("completing chunk upload: task=%d", taskID)

	rsp, err := client.CompleteChunkUpload(ctx, req)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"message": base.StatusMessage,
			"data":    rsp.File,
		})
	}
}

// 下载文件
func DownloadFile(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*driveGen.DriveClient)

	fileIDStr := c.Param("id")
	fileID, err := strconv.ParseInt(fileIDStr, 10, 64)
	if err != nil {
		logger.Errorf("Invalid file ID: %+v", err)
		c.JSON(400, gin.H{
			"message": "invalid file id",
		})
		return
	}

	req := &driveGen.DownloadFileRequest{
		FileID: fileID,
	}

	logger.Infof("downloading file: %d", fileID)

	rsp, err := client.DownloadFile(ctx, req)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		if base.StatusCode == 200 {
			// 设置响应头
			c.Header("Content-Disposition", "attachment; filename=\""+rsp.FileName+"\"")
			c.Header("Content-Type", rsp.MimeType)
			c.Header("Content-Length", strconv.Itoa(len(rsp.FileData)))
			
			// 返回文件数据
			c.Data(http.StatusOK, rsp.MimeType, rsp.FileData)
		} else {
			c.JSON(int(base.StatusCode), gin.H{
				"message": base.StatusMessage,
			})
		}
	}
}

// 删除文件
func DeleteFile(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*driveGen.DriveClient)

	fileIDStr := c.Param("id")
	fileID, err := strconv.ParseInt(fileIDStr, 10, 64)
	if err != nil {
		logger.Errorf("Invalid file ID: %+v", err)
		c.JSON(400, gin.H{
			"message": "invalid file id",
		})
		return
	}

	req := &driveGen.DeleteFileRequest{
		FileID: fileID,
	}

	logger.Infof("deleting file: %d", fileID)

	rsp, err := client.DeleteFile(ctx, req)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"message": base.StatusMessage,
		})
	}
}

// 重命名文件
func RenameFile(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*driveGen.DriveClient)

	fileIDStr := c.Param("id")
	fileID, err := strconv.ParseInt(fileIDStr, 10, 64)
	if err != nil {
		logger.Errorf("Invalid file ID: %+v", err)
		c.JSON(400, gin.H{
			"message": "invalid file id",
		})
		return
	}

	var requestBody struct {
		NewName string `json:"new_name"`
	}

	if err := c.BindJSON(&requestBody); err != nil {
		logger.Errorf("BindJSON[%+v]", err)
		c.JSON(400, gin.H{
			"message": "invalid request body",
		})
		return
	}

	req := &driveGen.RenameFileRequest{
		FileID:   fileID,
		NewName_: requestBody.NewName,
	}

	logger.Infof("renaming file: %d to %s", fileID, requestBody.NewName)

	rsp, err := client.RenameFile(ctx, req)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"message": base.StatusMessage,
			"data":    rsp.File,
		})
	}
}

// 移动文件
func MoveFile(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*driveGen.DriveClient)

	fileIDStr := c.Param("id")
	fileID, err := strconv.ParseInt(fileIDStr, 10, 64)
	if err != nil {
		logger.Errorf("Invalid file ID: %+v", err)
		c.JSON(400, gin.H{
			"message": "invalid file id",
		})
		return
	}

	var requestBody struct {
		NewParentID *int64 `json:"new_parent_id"`
	}

	if err := c.BindJSON(&requestBody); err != nil {
		logger.Errorf("BindJSON[%+v]", err)
		c.JSON(400, gin.H{
			"message": "invalid request body",
		})
		return
	}

	req := &driveGen.MoveFileRequest{
		FileID:       fileID,
		NewParentID_: requestBody.NewParentID,
	}

	logger.Infof("moving file: %d to parent %v", fileID, requestBody.NewParentID)

	rsp, err := client.MoveFile(ctx, req)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"message": base.StatusMessage,
			"data":    rsp.File,
		})
	}
}

// 获取存储信息
func GetStorageInfo(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*driveGen.DriveClient)

	req := &driveGen.GetStorageInfoRequest{}

	rsp, err := client.GetStorageInfo(ctx, req)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"total_space":    rsp.TotalSpace,
			"used_space":     rsp.UsedSpace,
			"free_space":     rsp.FreeSpace,
			"usage_percent":  rsp.UsagePercent,
		})
	}
}

// 创建分享
func CreateShare(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*driveGen.DriveClient)

	var requestBody struct {
		FileID        int64  `json:"file_id"`
		Password      string `json:"password"`
		ExpireTime    string `json:"expire_time"`
		DownloadLimit int64  `json:"download_limit"`
	}

	if err := c.BindJSON(&requestBody); err != nil {
		logger.Errorf("BindJSON[%+v]", err)
		c.JSON(400, gin.H{
			"message": "invalid request body",
		})
		return
	}

	req := &driveGen.CreateShareRequest{
		FileID:        requestBody.FileID,
		DownloadLimit: requestBody.DownloadLimit,
	}

	if requestBody.Password != "" {
		req.Password = &requestBody.Password
	}
	if requestBody.ExpireTime != "" {
		req.ExpireTime = &requestBody.ExpireTime
	}

	logger.Infof("creating share for file: %d", requestBody.FileID)

	rsp, err := client.CreateShare(ctx, req)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"message": base.StatusMessage,
			"data":    rsp.Share,
		})
	}
}

// 获取分享信息
func GetShareInfo(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*driveGen.DriveClient)

	shareCode := c.Param("code")
	password := c.Query("password")

	req := &driveGen.GetShareInfoRequest{
		ShareCode: shareCode,
	}

	if password != "" {
		req.Password = &password
	}

	logger.Infof("getting share info: %s", shareCode)

	rsp, err := client.GetShareInfo(ctx, req)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"message": base.StatusMessage,
			"share":   rsp.Share,
			"file":    rsp.File,
		})
	}
}

// 通过分享下载文件
func DownloadSharedFile(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*driveGen.DriveClient)

	shareCode := c.Param("code")
	password := c.Query("password")

	req := &driveGen.DownloadSharedFileRequest{
		ShareCode: shareCode,
	}

	if password != "" {
		req.Password = &password
	}

	logger.Infof("downloading shared file: %s", shareCode)

	rsp, err := client.DownloadSharedFile(ctx, req)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		if base.StatusCode == 200 {
			// 设置响应头
			c.Header("Content-Disposition", "attachment; filename=\""+rsp.FileName+"\"")
			c.Header("Content-Type", rsp.MimeType)
			c.Header("Content-Length", strconv.Itoa(len(rsp.FileData)))

			// 返回文件数据
			c.Data(http.StatusOK, rsp.MimeType, rsp.FileData)
		} else {
			c.JSON(int(base.StatusCode), gin.H{
				"message": base.StatusMessage,
			})
		}
	}
}

// Gateway服务实现
type GatewayService struct {
}

// 上传进度通知
func (s *GatewayService) UploadProgressNotify(ctx context.Context, req *gatewayGen.UploadProgressNotifyRequest) (*gatewayGen.UploadProgressNotifyResponse, error) {
	logger := logrus.New()
	logger.Infof("UploadProgressNotify req: taskID=%d, uploadedBytes=%d, totalBytes=%d, progress=%.2f%%",
		req.TaskID, req.UploadedBytes, req.TotalBytes, req.Progress)

	// 广播进度到WebSocket客户端
	progressManager.BroadcastProgress(req.TaskID, req.Progress, req.UploadedBytes, req.TotalBytes)

	return &gatewayGen.UploadProgressNotifyResponse{
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}

// 上传进度回调 (保留原有接口兼容性)
func (s *DriveService) UploadProgress(ctx context.Context, req *driveGen.UploadProgressRequest) (*driveGen.UploadProgressResponse, error) {
	logger := logrus.New()
	logger.Infof("UploadProgress req: taskID=%d, uploadedBytes=%d, totalBytes=%d, progress=%.2f%%", 
		req.TaskID, req.UploadedBytes, req.TotalBytes, req.Progress)

	// 广播进度到WebSocket客户端
	progressManager.BroadcastProgress(req.TaskID, req.Progress, req.UploadedBytes, req.TotalBytes)

	return &driveGen.UploadProgressResponse{
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}, nil
}

// WebSocket处理函数
func UploadProgress(c *gin.Context) {
	logger := logrus.New()

	// 获取任务ID
	taskIDStr := c.Query("task_id")
	taskID, err := strconv.ParseInt(taskIDStr, 10, 64)
	if err != nil {
		logger.Errorf("Invalid task ID: %+v", err)
		c.JSON(400, gin.H{
			"message": "invalid task id",
		})
		return
	}

	// 升级HTTP连接为WebSocket
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		logger.Errorf("WebSocket upgrade failed: %+v", err)
		return
	}
	defer conn.Close()

	// 添加到进度管理器
	progressManager.AddConnection(taskID, conn)
	defer progressManager.RemoveConnection(taskID, conn)

	// 保持连接直到客户端关闭
	for {
		_, _, err := conn.ReadMessage()
		if err != nil {
			break
		}
	}
}

// 挂载drive服务路由
func mountDrive(engine *gin.Engine) error {
	router := engine.Group("/api/drive")

	// 文件管理
	router.GET("/files", GetFileList)
	router.POST("/folders", CreateFolder)
	router.POST("/upload", UploadFile)
	router.GET("/files/:id/download", DownloadFile)
	router.DELETE("/files/:id", DeleteFile)
	router.PUT("/files/:id/rename", RenameFile)
	router.PUT("/files/:id/move", MoveFile)

	// 分片上传
	router.POST("/init-chunk-upload", InitChunkUpload)
	router.POST("/upload-chunk", UploadChunk)
	router.POST("/complete-chunk-upload", CompleteChunkUpload)

	// 存储信息
	router.GET("/storage/info", GetStorageInfo)

	// 文件分享
	router.POST("/shares", CreateShare)
	router.GET("/shares/:code", GetShareInfo)
	router.GET("/shares/:code/download", DownloadSharedFile)

	// 缩略图
	router.GET("/files/:id/thumbnail", GetThumbnail)
	router.POST("/files/:id/thumbnail", GenerateThumbnail)

	// 添加WebSocket路由
	router.GET("/upload-progress", UploadProgress)

	return nil
}

// 获取缩略图
func GetThumbnail(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	logger.Infof("GetThumbnail called for path: %s", c.Request.URL.Path)

	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	logger.Infof("RPC client obtained successfully")
	client := rpc_client.(*driveGen.DriveClient)

	// 获取文件ID
	fileIDStr := c.Param("id")
	fileID, err := strconv.ParseInt(fileIDStr, 10, 64)
	if err != nil {
		logger.Errorf("Invalid file ID: %+v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file ID"})
		return
	}

	// 获取缩略图尺寸参数
	var width, height *int32
	if widthStr := c.Query("width"); widthStr != "" {
		if w, err := strconv.ParseInt(widthStr, 10, 32); err == nil {
			w32 := int32(w)
			width = &w32
		}
	}
	if heightStr := c.Query("height"); heightStr != "" {
		if h, err := strconv.ParseInt(heightStr, 10, 32); err == nil {
			h32 := int32(h)
			height = &h32
		}
	}

	// 调用服务获取缩略图
	req := &driveGen.GetThumbnailRequest{
		FileID: fileID,
		Width:  width,
		Height: height,
	}

	resp, err := client.GetThumbnail(ctx, req)
	if err != nil {
		logger.Errorf("GetThumbnail RPC err: %+v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	if resp.BaseResp.StatusCode != 200 {
		logger.Errorf("GetThumbnail failed: %s", resp.BaseResp.StatusMessage)
		c.JSON(int(resp.BaseResp.StatusCode), gin.H{"error": resp.BaseResp.StatusMessage})
		return
	}

	// 返回缩略图数据
	c.Header("Content-Type", resp.MimeType)
	c.Header("Cache-Control", "public, max-age=3600") // 缓存1小时
	c.Data(http.StatusOK, resp.MimeType, resp.ThumbnailData)
}

// 生成缩略图
func GenerateThumbnail(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}
	client := rpc_client.(*driveGen.DriveClient)

	// 获取文件ID
	fileIDStr := c.Param("id")
	fileID, err := strconv.ParseInt(fileIDStr, 10, 64)
	if err != nil {
		logger.Errorf("Invalid file ID: %+v", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid file ID"})
		return
	}

	// 获取缩略图尺寸参数
	var width, height *int32
	if widthStr := c.Query("width"); widthStr != "" {
		if w, err := strconv.ParseInt(widthStr, 10, 32); err == nil {
			w32 := int32(w)
			width = &w32
		}
	}
	if heightStr := c.Query("height"); heightStr != "" {
		if h, err := strconv.ParseInt(heightStr, 10, 32); err == nil {
			h32 := int32(h)
			height = &h32
		}
	}

	// 调用服务生成缩略图
	req := &driveGen.GenerateThumbnailRequest{
		FileID: fileID,
		Width:  width,
		Height: height,
	}

	resp, err := client.GenerateThumbnail(ctx, req)
	if err != nil {
		logger.Errorf("GenerateThumbnail RPC err: %+v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Internal server error"})
		return
	}

	if resp.BaseResp.StatusCode != 200 {
		logger.Errorf("GenerateThumbnail failed: %s", resp.BaseResp.StatusMessage)
		c.JSON(int(resp.BaseResp.StatusCode), gin.H{"error": resp.BaseResp.StatusMessage})
		return
	}

	// 返回成功响应
	c.JSON(http.StatusOK, gin.H{
		"thumbnail_path": resp.ThumbnailPath,
		"message":        "Thumbnail generated successfully",
	})
}
