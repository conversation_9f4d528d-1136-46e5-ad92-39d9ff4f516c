package services

import (
	"context"
	"strconv"

	favoritesGen "thrift-common/gen-go/favorites"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// type PostApiFavoritesAddRequest struct {
// 	CollectType string `json:"collect_type" binding:"required"`
// 	Origin      string `json:"origin" binding:"required"`
// 	Title		string `json:"title"`
// 	SavePath    string `json:"save_path"`
// 	Remark      string `json:"remark"`
// }

// 由于下游重启之后，单例的client继续调用rpc会报write: broken pipe，所以暂时不用单例模式
// var client *favoritesGen.FavoritesClient

func AddToFavorites(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*favoritesGen.FavoritesClient)

	// var requestBody PostApiFavoritesAddRequest
	var req favoritesGen.AddToFavoritesRequest
	if err := c.BindJSON(&req); err != nil {
		logger.Errorf("BindJSON err[%+v]", err)
		return
	}
	logger.Infof("req[%+v]", req)

	// req := &favoritesGen.AddToFavoritesRequest{
	// 	CollectType: requestBody.CollectType,
	// 	Origin:      requestBody.Origin,
	// 	Title:		 requestBody.Title,
	// 	SavePath:    requestBody.SavePath,
	// 	Remark:      requestBody.Remark,
	// }
	rsp, err := client.AddToFavorites(ctx, &req)
	logger.Infof("rsp[%+v]", rsp)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"message": base.StatusMessage,
		})
	}
}

func GetFavoritesPage(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*favoritesGen.FavoritesClient)

	offset, e := strconv.ParseInt(c.DefaultQuery("offset", "0"), 10, 64)
	if e != nil {
		logger.Errorf("Wrong Params offset[%+v]", e)
		return
	}

	// 只支持size参数，简化参数处理
	size, e := strconv.ParseInt(c.DefaultQuery("size", "10"), 10, 64)
	if e != nil {
		logger.Errorf("Wrong Params size[%+v]", e)
		return
	}

	// 限制每页最大数量，防止查询过多数据
	if size > 100 {
		size = 100
	}
	if size <= 0 {
		size = 10
	}

	// 获取搜索关键词参数
	keyword := c.Query("keyword")

	logger.Infof("offset[%d] size[%d] keyword[%s]", offset, size, keyword)

	req := &favoritesGen.GetFavoritesPageRequest{
		Offset: offset,
		Count:  size,
	}

	// 如果有搜索关键词，添加到请求中
	if keyword != "" {
		req.Keyword = &keyword
	}
	rsp, err := client.GetFavoritesPage(ctx, req)
	logger.Infof("rsp[%+v]", rsp)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"data":     rsp.FavoritesList,
			"has_more": rsp.HasMore,
			"total":    rsp.Total,
		})
	}
}

func GetDiskInfo(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*favoritesGen.FavoritesClient)

	req := &favoritesGen.GetDiskInfoRequest{
		Depth: 0,
		Root:  "/",
	}
	rsp, err := client.GetDiskInfo(ctx, req)
	logger.Infof("rsp[%+v]", rsp)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"disk_label":    rsp.DiskLabel,
			"disk_usage":    rsp.DiskUsage,
			"disk_progress": rsp.DiskProgress,
			"fs_tree":       rsp.FsTree,
		})
	}
}

func CallbackDelayFetch(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*favoritesGen.FavoritesClient)

	var req favoritesGen.CallbackDelayFetchRequest
	if err := c.BindJSON(&req); err != nil {
		logger.Errorf("BindJSON[%+v]", err)
		return
	}
	logger.Infof("req[%+v]", req)

	rsp, err := client.CallbackDelayFetch(ctx, &req)
	commonResp(c, rsp.BaseResp, err, rsp)
}

func CallbackSyncData(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*favoritesGen.FavoritesClient)

	var req favoritesGen.CallbackSyncDataRequest
	if err := c.BindJSON(&req); err != nil {
		logger.Errorf("BindJSON[%+v]", err)
		return
	}
	logger.Infof("req[%+v]", req)

	rsp, err := client.CallbackSyncData(ctx, &req)
	commonResp(c, rsp.BaseResp, err, rsp)
}

func DeleteFavorite(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*favoritesGen.FavoritesClient)

	// 获取ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logger.Errorf("Invalid ID parameter: %v", err)
		c.JSON(400, gin.H{
			"message": "Invalid ID parameter",
		})
		return
	}

	req := &favoritesGen.DeleteFavoriteRequest{
		FavoriteId: id,
	}
	rsp, err := client.DeleteFavorite(ctx, req)
	logger.Infof("rsp[%+v]", rsp)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"message": base.StatusMessage,
		})
	}
}



func mountFavorites(engine *gin.Engine) error {
	// 在nginx已经限制了/api前缀
	router := engine.Group("/api/favorites")
	router.POST("/add", AddToFavorites)
	router.GET("/list", GetFavoritesPage)
	router.GET("/disk/info", GetDiskInfo)
	router.DELETE("/:id", DeleteFavorite)

	routerInner := engine.Group("/inner_api/favorites")
	routerInner.POST("/callback/delay_fetch", CallbackDelayFetch)
	routerInner.POST("/callback/sync_data", CallbackSyncData)
	return nil
}
