package services

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

func TestGetFavoritesPageParameterParsing(t *testing.T) {
	gin.SetMode(gin.TestMode)

	tests := []struct {
		name           string
		queryParams    string
		expectedOffset int64
		expectedCount  int64
	}{
		{
			name:           "使用size参数",
			queryParams:    "offset=0&size=20",
			expectedOffset: 0,
			expectedCount:  20,
		},

		{
			name:           "默认参数",
			queryParams:    "",
			expectedOffset: 0,
			expectedCount:  10,
		},
		{
			name:           "只有offset",
			queryParams:    "offset=20",
			expectedOffset: 20,
			expectedCount:  10,
		},
		{
			name:           "超过最大限制",
			queryParams:    "offset=0&size=150",
			expectedOffset: 0,
			expectedCount:  100, // 应该被限制为100
		},
		{
			name:           "负数或零值处理",
			queryParams:    "offset=0&size=0",
			expectedOffset: 0,
			expectedCount:  10, // 应该被设置为默认值10
		},
		{
			name:           "负数size",
			queryParams:    "offset=0&size=-5",
			expectedOffset: 0,
			expectedCount:  10, // 应该被设置为默认值10
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建一个模拟的HTTP请求
			req, _ := http.NewRequest("GET", "/api/favorites/list?"+tt.queryParams, nil)
			req.Header.Set("Bullet", "36fdf066-9e42-11ec-b41e-525400043ced")
			
			// 创建响应记录器
			w := httptest.NewRecorder()
			
			// 创建Gin上下文
			c, _ := gin.CreateTestContext(w)
			c.Request = req

			// 模拟参数解析逻辑（从GetFavoritesPage函数中提取）
			offset := int64(0)
			if offsetStr := c.Query("offset"); offsetStr != "" {
				if val, err := parseIntWithDefault(offsetStr, 0); err == nil {
					offset = val
				}
			}

			// 只支持size参数，简化参数处理
			size := int64(10) // 默认值
			if sizeStr := c.Query("size"); sizeStr != "" {
				if val, err := parseIntWithDefault(sizeStr, 10); err == nil {
					size = val
				}
			}

			// 限制每页最大数量，防止查询过多数据
			if size > 100 {
				size = 100
			}
			if size <= 0 {
				size = 10
			}

			// 验证结果
			assert.Equal(t, tt.expectedOffset, offset, "offset should match expected value")
			assert.Equal(t, tt.expectedCount, size, "size should match expected value")
		})
	}
}

// 辅助函数，用于解析整数参数
func parseIntWithDefault(s string, defaultVal int64) (int64, error) {
	if s == "" {
		return defaultVal, nil
	}
	// 简化的解析逻辑，实际应该使用strconv.ParseInt
	switch s {
	case "0":
		return 0, nil
	case "5":
		return 5, nil
	case "10":
		return 10, nil
	case "15":
		return 15, nil
	case "20":
		return 20, nil
	case "25":
		return 25, nil
	case "30":
		return 30, nil
	case "150":
		return 150, nil
	case "-5":
		return -5, nil
	default:
		return defaultVal, nil
	}
}

func TestPaginationLogic(t *testing.T) {
	tests := []struct {
		name        string
		offset      int64
		count       int64
		total       int64
		expectedHasMore bool
	}{
		{
			name:        "第一页，有更多数据",
			offset:      0,
			count:       10,
			total:       25,
			expectedHasMore: true,
		},
		{
			name:        "最后一页，没有更多数据",
			offset:      20,
			count:       10,
			total:       25,
			expectedHasMore: false,
		},
		{
			name:        "正好最后一页",
			offset:      10,
			count:       10,
			total:       20,
			expectedHasMore: false,
		},
		{
			name:        "超出范围",
			offset:      30,
			count:       10,
			total:       25,
			expectedHasMore: false,
		},
		{
			name:        "总数为0",
			offset:      0,
			count:       10,
			total:       0,
			expectedHasMore: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 计算是否还有更多数据的逻辑
			hasMore := tt.offset+tt.count < tt.total
			
			assert.Equal(t, tt.expectedHasMore, hasMore, "hasMore should match expected value")
		})
	}
}
