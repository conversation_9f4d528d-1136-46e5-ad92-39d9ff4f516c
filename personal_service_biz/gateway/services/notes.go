package services

import (
	"context"
	"strconv"

	notesGen "thrift-common/gen-go/notes"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func AddNote(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*notesGen.NotesClient)

	var req notesGen.AddNoteRequest
	if err := c.BindJSON(&req); err != nil {
		logger.Errorf("BindJSON err[%+v]", err)
		c.<PERSON>(400, gin.H{
			"message": "Invalid request body",
		})
		return
	}
	logger.Infof("req[%+v]", req)

	rsp, err := client.AddNote(ctx, &req)
	logger.Infof("rsp[%+v]", rsp)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.<PERSON>(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"message": base.StatusMessage,
			"note_id": rsp.NoteId,
		})
	}
}

func GetNotesPage(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*notesGen.NotesClient)

	offset, e := strconv.ParseInt(c.DefaultQuery("offset", "0"), 10, 64)
	if e != nil {
		logger.Errorf("Wrong Params offset[%+v]", e)
		return
	}

	// 只支持size参数，简化参数处理
	size, e := strconv.ParseInt(c.DefaultQuery("size", "10"), 10, 64)
	if e != nil {
		logger.Errorf("Wrong Params size[%+v]", e)
		return
	}

	// 限制每页最大数量，防止查询过多数据
	if size > 100 {
		size = 100
	}
	if size <= 0 {
		size = 10
	}

	// 获取搜索关键词参数
	keyword := c.Query("keyword")
	category := c.Query("category")

	logger.Infof("offset[%d] size[%d] keyword[%s] category[%s]", offset, size, keyword, category)

	req := &notesGen.GetNotesPageRequest{
		Offset: offset,
		Count:  size,
	}

	// 如果有搜索关键词，添加到请求中
	if keyword != "" {
		req.Keyword = &keyword
	}
	if category != "" {
		req.Category = &category
	}

	rsp, err := client.GetNotesPage(ctx, req)
	logger.Infof("rsp[%+v]", rsp)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"data":     rsp.NotesList,
			"has_more": rsp.HasMore,
			"total":    rsp.Total,
		})
	}
}

func UpdateNote(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*notesGen.NotesClient)

	// 获取ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logger.Errorf("Invalid ID parameter: %v", err)
		c.JSON(400, gin.H{
			"message": "Invalid ID parameter",
		})
		return
	}

	var reqBody notesGen.UpdateNoteRequest
	if err := c.BindJSON(&reqBody); err != nil {
		logger.Errorf("BindJSON err[%+v]", err)
		c.JSON(400, gin.H{
			"message": "Invalid request body",
		})
		return
	}

	req := &notesGen.UpdateNoteRequest{
		NoteId:   id,
		Title:    reqBody.Title,
		Content:  reqBody.Content,
		NoteType: reqBody.NoteType,
		Category: reqBody.Category,
		Tags:     reqBody.Tags,
	}

	rsp, err := client.UpdateNote(ctx, req)
	logger.Infof("rsp[%+v]", rsp)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"message": base.StatusMessage,
		})
	}
}

func DeleteNote(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*notesGen.NotesClient)

	// 获取ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logger.Errorf("Invalid ID parameter: %v", err)
		c.JSON(400, gin.H{
			"message": "Invalid ID parameter",
		})
		return
	}

	req := &notesGen.DeleteNoteRequest{
		NoteId: id,
	}
	rsp, err := client.DeleteNote(ctx, req)
	logger.Infof("rsp[%+v]", rsp)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"message": base.StatusMessage,
		})
	}
}

func GetNote(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*notesGen.NotesClient)

	// 获取ID参数
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		logger.Errorf("Invalid ID parameter: %v", err)
		c.JSON(400, gin.H{
			"message": "Invalid ID parameter",
		})
		return
	}

	req := &notesGen.GetNoteRequest{
		NoteId: id,
	}
	rsp, err := client.GetNote(ctx, req)
	logger.Infof("rsp[%+v]", rsp)
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		base := rsp.BaseResp
		c.JSON(int(base.StatusCode), gin.H{
			"data": rsp.Note,
		})
	}
}

func mountNotes(engine *gin.Engine) error {
	// 在nginx已经限制了/api前缀
	router := engine.Group("/api/notes")
	router.POST("/add", AddNote)
	router.GET("/list", GetNotesPage)
	router.PUT("/:id", UpdateNote)
	router.DELETE("/:id", DeleteNote)
	router.GET("/:id", GetNote)
	return nil
}
