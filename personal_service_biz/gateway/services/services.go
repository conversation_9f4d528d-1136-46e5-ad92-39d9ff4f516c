package services

import (
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func MountService(engine *gin.Engine) error {
	logger := logrus.New()
	engine.GET("/api/ping", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message": "pong",
		})
	})

	// 为兼容 ktask 客户端，添加直接的 /search 路由
	engine.GET("/search", SearchTask)

	var err error
	err = mountFavorites(engine)
	if err != nil {
		logger.Errorf("mountFavorites err[%+v]", err)
	}

	err = mountTask(engine)
	if err != nil {
		logger.Errorf("mountTask err[%+v]", err)
	}

	err = mountNotes(engine)
	if err != nil {
		logger.Errorf("mountNotes err[%+v]", err)
	}

	err = mountDrive(engine)
	if err != nil {
		logger.Errorf("mountDrive err[%+v]", err)
	}
	return nil
}
