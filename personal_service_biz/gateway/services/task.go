package services

import (
	"context"

	"thrift-common/gen-go/base"
	taskGen "thrift-common/gen-go/task"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

func commonResp(c *gin.Context, base *base.BaseResponse, err error, rsp interface{}) {
	logger := logrus.New()
	if err != nil {
		logger.Errorf("Rpc Call failed: %+v", err)
		c.JSON(500, gin.H{
			"message": "internal error",
		})
	} else {
		if base.StatusCode != 0 {
			c.JSON(200, gin.H{
				"message": base.StatusMessage,
				"code":    base.StatusCode,
			})
		} else {
			c.JSON(200, gin.H{
				"message": base.StatusMessage,
				"code":    base.StatusCode,
				"data":    rsp,
			})
		}
	}
}

func ListTask(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*taskGen.TaskClient)

	var req taskGen.ListTaskRequest
	if err := c.BindQuery(&req); err != nil {
		logger.Errorf("BindQuery err[%+v]", err)
		return
	}
	logger.Infof("req[%+v]", req)

	rsp, err := client.ListTask(ctx, &req)
	commonResp(c, rsp.BaseResp, err, rsp)
}

func GetLast(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*taskGen.TaskClient)

	var req taskGen.GetLastRequest
	if err := c.BindQuery(&req); err != nil {
		logger.Errorf("BindQuery err[%+v]", err)
		return
	}
	logger.Infof("req[%+v]", req)
	rsp, err := client.GetLast(ctx, &req)
	logger.Infof("rsp[%+v]", rsp)
	commonResp(c, rsp.BaseResp, err, rsp)
}

func PickTask(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*taskGen.TaskClient)

	var req taskGen.PickTaskRequest
	if err := c.BindJSON(&req); err != nil {
		logger.Errorf("BindJSON err[%+v]", err)
		return
	}
	logger.Infof("req[%+v]", req)
	rsp, err := client.PickTask(ctx, &req)
	logger.Infof("rsp[%+v]", rsp)
	commonResp(c, rsp.BaseResp, err, rsp)
}

func PickDel(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*taskGen.TaskClient)

	var req taskGen.PickDelRequest
	if err := c.BindJSON(&req); err != nil {
		logger.Errorf("BindJSON err[%+v]", err)
		return
	}
	logger.Infof("req[%+v]", req)
	rsp, err := client.PickDel(ctx, &req)
	logger.Infof("rsp[%+v]", rsp)
	commonResp(c, rsp.BaseResp, err, rsp)
}

func AddTask(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*taskGen.TaskClient)

	var req taskGen.AddTaskRequest
	if err := c.BindJSON(&req); err != nil {
		logger.Errorf("BindJSON err[%+v]", err)
		return
	}
	logger.Infof("req[%+v]", req)
	rsp, err := client.AddTask(ctx, &req)
	logger.Infof("rsp[%+v]", rsp)
	commonResp(c, rsp.BaseResp, err, rsp)
}

func EditTask(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*taskGen.TaskClient)

	var req taskGen.EditTaskRequest
	if err := c.BindJSON(&req); err != nil {
		logger.Errorf("BindJSON err[%+v]", err)
		return
	}
	logger.Infof("req[%+v]", req)
	rsp, err := client.EditTask(ctx, &req)
	logger.Infof("rsp[%+v]", rsp)
	commonResp(c, rsp.BaseResp, err, rsp)
}

func TaskAction(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*taskGen.TaskClient)

	var req taskGen.TaskActionRequest
	if err := c.BindJSON(&req); err != nil {
		logger.Errorf("BindJSON[%+v]", err)
		return
	}
	logger.Infof("req[%+v]", req)

	rsp, err := client.TaskAction(ctx, &req)
	logger.Infof("rsp[%+v]", rsp)
	commonResp(c, rsp.BaseResp, err, rsp)
}

func TaskMv(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*taskGen.TaskClient)

	var req taskGen.TaskMvRequest
	if err := c.BindJSON(&req); err != nil {
		logger.Errorf("BindJSON[%+v]", err)
		return
	}
	logger.Infof("req[%+v]", req)

	rsp, err := client.TaskMv(ctx, &req)
	logger.Infof("rsp[%+v]", rsp)
	commonResp(c, rsp.BaseResp, err, rsp)
}

func SearchTask(c *gin.Context) {
	ctx := context.Background()
	logger := logrus.New()
	rpc_client, ok := c.Get("rpc_client")
	if !ok {
		logger.Errorf("GetRpcClient err[%+v]", ok)
		return
	}
	client := rpc_client.(*taskGen.TaskClient)

	var req taskGen.SearchTaskRequest
	if err := c.BindQuery(&req); err != nil {
		logger.Errorf("BindQuery err[%+v]", err)
		return
	}
	logger.Infof("req[%+v]", req)

	rsp, err := client.SearchTask(ctx, &req)
	logger.Infof("rsp[%+v]", rsp)
	commonResp(c, rsp.BaseResp, err, rsp)
}

func mountTask(engine *gin.Engine) error {
	// 在nginx已经限制了/api前缀
	router := engine.Group("/api/task")

	router.POST("/add", AddTask)
	router.POST("/edit", EditTask)
	router.GET("/list", ListTask)
	router.GET("/last", GetLast)
	router.POST("/pick", PickTask)
	router.POST("/pickdel", PickDel)
	router.POST("/action", TaskAction)
	router.POST("/move", TaskMv)
	router.GET("/search", SearchTask)
	return nil
}
