# Notes Service - 笔记管理服务

Notes Service是一个用Go编写的笔记管理系统服务端，提供完整的笔记CRUD功能，支持分类和标签管理。

## 功能特性

- **笔记管理**：支持创建、读取、更新、删除笔记
- **分类支持**：支持按分类组织笔记
- **标签系统**：支持为笔记添加标签
- **搜索功能**：支持按标题和内容搜索笔记
- **分页查询**：支持分页获取笔记列表
- **Thrift RPC**：高性能的RPC服务接口
- **MySQL存储**：可靠的数据持久化

## 环境要求

- Go 1.17+
- MySQL 5.7+
- thrift 0.16.0+

## 安装部署

### 数据库配置
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE db_notes CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 创建用户
mysql -u root -p -e "CREATE USER 'notesuser'@'%' IDENTIFIED BY 'notespass';"
mysql -u root -p -e "GRANT ALL PRIVILEGES ON db_notes.* TO 'notesuser'@'%';"

# 创建表
mysql -u notesuser -p db_notes < scripts/create_table.sql
```

### 编译运行
```bash
# 编译
make build

# 运行
ENV=dev go run main.go

# 或者直接运行编译后的程序
./notes-service
```

### 部署
```bash
# 部署到生产环境
make deploy
```

## API接口

### 添加笔记
```bash
POST /api/notes/add
Content-Type: application/json

{
  "title": "笔记标题",
  "content": "笔记内容",
  "category": "分类名称",
  "tags": "标签1,标签2"
}
```

### 获取笔记列表
```bash
GET /api/notes/list?offset=0&size=10&keyword=搜索关键词&category=分类名称
```

### 获取单个笔记
```bash
GET /api/notes/{id}
```

### 更新笔记
```bash
PUT /api/notes/{id}
Content-Type: application/json

{
  "title": "更新的标题",
  "content": "更新的内容",
  "category": "新分类",
  "tags": "新标签"
}
```

### 删除笔记
```bash
DELETE /api/notes/{id}
```

## 服务架构

- **端口**: 18083 (Thrift RPC)
- **数据库**: MySQL (端口3315开发环境，3306生产环境)
- **通信协议**: Thrift Binary Protocol
- **日志**: 输出到 `/runtime/notes-service-logs/`

## 测试

```bash
# 运行单元测试
make test

# 运行API测试
./scripts/test_api.sh
```

## 配置说明

- **开发环境**: 连接本地MySQL (127.0.0.1:3315)
- **生产环境**: 连接Docker网络中的notes-mysql:3306
- **环境变量**: 通过ENV环境变量控制 (dev/deploy)

## 项目结构

```
notes-service/
├── main.go              # 主程序入口
├── config/              # 配置管理
├── models/              # 数据模型
├── service/             # 业务逻辑
├── biz_error/           # 错误处理
├── common/              # 公共工具
├── scripts/             # 部署脚本
├── image/               # Docker镜像
└── README.md           # 说明文档
```
