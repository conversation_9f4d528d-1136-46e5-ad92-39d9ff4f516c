package biz_error

import (
	"fmt"

	wErr "github.com/pkg/errors"
	"github.com/sirupsen/logrus"
)

type BIZ_ERROR_CODE int

const (
	// 用户视角的error case
	//  - 1. 业务数据模块相关
	ERR_NOTE_NOT_FOUND BIZ_ERROR_CODE = 10001
	ERR_INVALID_PARAMS                 = 10002
	// 业务模块定义的逻辑error
	ERR_WRONG_ROWS_AFFECTED = 80001

	// 服务级别通用error / 其他不对外暴露的error
	ERR_INTERNAL            = 90001
	ERR_COMMON              = 90002
	ERR_MISS_IMPLEMENT      = 90003 // 缺少逻辑分支
	ERR_INIT_DB_CONN_FAILED = 90004 // 初始化db链接错误
)

type BizErrorExtra struct {
	NoteID       *int64
	RowsAffected *int64
	Message      *string
}

type BizError struct {
	code     BIZ_ERROR_CODE
	message  string
	extra    *BizErrorExtra
	extraMap *map[string]string
}

var errMsgMap = map[BIZ_ERROR_CODE]string{
	ERR_NOTE_NOT_FOUND:      "Note not found",
	ERR_INVALID_PARAMS:      "Invalid parameters",
	ERR_WRONG_ROWS_AFFECTED: "error:ROWS_AFFECTED",

	ERR_INTERNAL:            "internal error",
	ERR_INIT_DB_CONN_FAILED: "Init Db Conn Failed",
}

func NewBizError(code BIZ_ERROR_CODE) *BizError {
	return &BizError{
		code:    code,
		message: errMsgMap[code],
		extra:   nil,
	}
}

func NewBizErrorWithExtra(code BIZ_ERROR_CODE, extra BizErrorExtra) *BizError {
	return &BizError{
		code:    code,
		message: errMsgMap[code],
		extra:   &extra,
	}
}

func NewBizErrorWithMap(code BIZ_ERROR_CODE, extraMap map[string]string) *BizError {
	return &BizError{
		code:     code,
		message:  errMsgMap[code],
		extraMap: &extraMap,
	}
}

func NewBizErrorWithMessage(msg string) *BizError {
	return &BizError{
		code:    ERR_COMMON,
		message: msg,
	}
}

func NewBizErrorByError(err error) *BizError {
	logger := logrus.New()
	logger.Errorf("%+v", wErr.Wrap(fmt.Errorf("%+v", err), ""))
	return &BizError{
		code:    ERR_INTERNAL,
		message: errMsgMap[ERR_INTERNAL],
	}
}

func (e *BizError) Code() int {
	return int(e.code)
}

func (e *BizError) Msg() string {
	msg := e.message
	if e.extraMap != nil {
		for k, v := range *e.extraMap {
			msg = fmt.Sprintf("%s %s[%s]", msg, k, v)
		}
	}

	// 带extra信息
	if e.extra == nil {
		return msg
	}

	if e.extra.Message != nil {
		return *e.extra.Message
	}
	if e.extra.NoteID != nil {
		return fmt.Sprintf("%s [%d]", msg, *e.extra.NoteID)
	}

	return msg
}

func (e *BizError) IsCode(code BIZ_ERROR_CODE) bool {
	return code == e.code
}
