package config

import (
	"context"
	"fmt"
	"os"
)

type Config struct {
	DSN string
}

var globalConfig Config

func GetDSNByHost(ipPort string) string {
	return fmt.Sprintf("notesuser:notespass@tcp(%s)/db_notes?charset=utf8mb4&parseTime=True&loc=Local", ipPort)
}

func loadConfigs(ctx context.Context) error {
	env := os.Getenv("ENV")
	if env == "deploy" {
		// 加载config.deploy文件
		globalConfig.DSN = GetDSNByHost("notes-mysql:3306")
	} else {
		// 开发环境加载config.dev文件
		globalConfig.DSN = GetDSNByHost("127.0.0.1:3315")
	}
	return nil
}

func InitConfig(ctx context.Context) error {
	return loadConfigs(ctx)
}

func GetDSN() string {
	return globalConfig.DSN
}
