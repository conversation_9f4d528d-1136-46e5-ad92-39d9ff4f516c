package main

import (
	"context"
	"notes-service/config"
	"notes-service/models"
	"notes-service/service"
	"thrift-common/server/notes"

	"github.com/sirupsen/logrus"
)

func main() {
	var err error
	ctx := context.Background()
	logger := logrus.New()
	err = config.InitConfig(ctx)
	if err != nil {
		logger.Errorf("loadConfigs failed: %+v", err)
		return
	}

	err = models.InitMysql(ctx)
	if err != nil {
		logger.Errorf("initMysql failed: %+v", err)
		return
	}

	handler := &service.NotesService{}
	logger.Info("Server Started!")
	err = notes.Server(ctx, handler)
	if err != nil {
		logger.Errorf("Server error: %+v", err)
	}
}
