package models

import (
	"context"
	"fmt"
	"notes-service/biz_error"
	"time"

	wErr "github.com/pkg/errors"
)

type Note struct {
	ID         int64      `gorm:"column:id"`
	Title      string     `gorm:"column:title"`
	Content    string     `gorm:"column:content"`
	NoteType   string     `gorm:"column:note_type"`
	Category   *string    `gorm:"column:category"`
	Tags       *string    `gorm:"column:tags"`
	CreateTime time.Time  `gorm:"column:created_at"`
	UpdateTime time.Time  `gorm:"column:updated_at"`
	DeleteTime *time.Time `gorm:"column:deleted_at"`
}

func (Note) TableName() string {
	return "notes"
}

func CreateNote(note *Note) *biz_error.BizError {
	now := time.Now()
	note.CreateTime = now
	note.UpdateTime = now
	result := GetDB().Create(note)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

func UpdateNote(ctx context.Context, noteID int64, updates map[string]interface{}) *biz_error.BizError {
	updates["updated_at"] = time.Now()
	result := GetDB().Model(&Note{}).Where("id = ?", noteID).Updates(updates)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected == 0 {
		return biz_error.NewBizError(biz_error.ERR_NOTE_NOT_FOUND)
	}
	return nil
}

func SelectNoteByID(ctx context.Context, id int64) (*Note, error) {
	var note Note
	result := GetDB().Where(&Note{ID: id}).First(&note)
	if result.Error != nil {
		return nil, wErr.Wrap(result.Error, "")
	}
	if result.RowsAffected != 1 {
		return nil, wErr.Wrap(fmt.Errorf("Wrong RowsAffected[%d]", result.RowsAffected), "")
	}
	return &note, nil
}

func SelectNotesList(offset, limit int64, category string) ([]*Note, error) {
	var notes []*Note
	query := GetDB()
	if category != "" {
		query = query.Where("category = ?", category)
	}
	result := query.Limit(int(limit)).
		Offset(int(offset)).
		Order("created_at desc").
		Find(&notes)
	if result.Error != nil {
		return nil, wErr.Wrap(result.Error, "")
	}
	return notes, nil
}

func CountNotes(category string) (int64, error) {
	var count int64
	query := GetDB().Model(&Note{})
	if category != "" {
		query = query.Where("category = ?", category)
	}
	result := query.Count(&count)
	if result.Error != nil {
		return 0, wErr.Wrap(result.Error, "")
	}
	return count, nil
}

func DeleteNote(id int64) error {
	result := GetDB().Delete(&Note{}, id)
	if result.Error != nil {
		return wErr.Wrap(result.Error, "")
	}
	return nil
}

func SearchNotesList(keyword string, offset, limit int64, category string) ([]*Note, error) {
	var notes []*Note
	query := GetDB().Where("title LIKE ? OR content LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	if category != "" {
		query = query.Where("category = ?", category)
	}
	result := query.Limit(int(limit)).
		Offset(int(offset)).
		Order("created_at desc").
		Find(&notes)
	if result.Error != nil {
		return nil, wErr.Wrap(result.Error, "")
	}
	return notes, nil
}

func CountSearchNotes(keyword string, category string) (int64, error) {
	var count int64
	query := GetDB().Model(&Note{}).Where("title LIKE ? OR content LIKE ?", "%"+keyword+"%", "%"+keyword+"%")
	if category != "" {
		query = query.Where("category = ?", category)
	}
	result := query.Count(&count)
	if result.Error != nil {
		return 0, wErr.Wrap(result.Error, "")
	}
	return count, nil
}
