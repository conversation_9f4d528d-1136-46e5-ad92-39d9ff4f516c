package models

import (
	"testing"
)

func TestCreateNote(t *testing.T) {
	note := Note{
		Title:   "Test Note",
		Content: "This is a test note content",
	}
	
	err := CreateNote(&note)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("CreateNote failed: %+v", err)
	}
	
	if note.ID == 0 {
		t.<PERSON><PERSON><PERSON>("Note ID should be set after creation")
	}
	
	t.Logf("Created note with ID: %d", note.ID)
}
