#!/bin/bash

echo "=== Notes Service 部署验证脚本 ==="

# 1. 检查数据库是否启动
echo "1. 检查notes-mysql数据库容器..."
if docker ps | grep -q "notes-mysql"; then
    echo "✓ notes-mysql 容器正在运行"
else
    echo "✗ notes-mysql 容器未运行，请先启动数据库"
    echo "  运行: cd admin/admin && ./onekey.sh"
    exit 1
fi

# 2. 检查数据库连接
echo "2. 检查数据库连接..."
if docker exec notes-mysql mysql -u notesuser -pnotespass -e "USE db_notes; SHOW TABLES;" > /dev/null 2>&1; then
    echo "✓ 数据库连接正常"
else
    echo "✗ 数据库连接失败"
    exit 1
fi

# 3. 检查notes表是否存在
echo "3. 检查notes表结构..."
if docker exec notes-mysql mysql -u notesuser -pnotespass -e "USE db_notes; DESCRIBE notes;" > /dev/null 2>&1; then
    echo "✓ notes表结构正常"
else
    echo "✗ notes表不存在或结构异常"
    exit 1
fi

# 4. 构建notes-service
echo "4. 构建notes-service..."
cd "$(dirname "$0")/.."
if go build; then
    echo "✓ notes-service 构建成功"
else
    echo "✗ notes-service 构建失败"
    exit 1
fi

# 5. 检查gateway构建
echo "5. 检查gateway集成..."
cd ../gateway
if go build; then
    echo "✓ gateway 构建成功"
else
    echo "✗ gateway 构建失败"
    exit 1
fi

echo ""
echo "=== 部署验证完成 ==="
echo "✓ 所有检查项都通过"
echo ""
echo "下一步操作："
echo "1. 启动notes-service: cd notes-service && ENV=dev go run main.go"
echo "2. 启动gateway: cd gateway && ENV=dev go run main.go"
echo "3. 测试API: ./scripts/test_api.sh"
echo ""
echo "或者使用Docker部署："
echo "1. cd notes-service && make deploy"
echo "2. cd gateway && make deploy"
