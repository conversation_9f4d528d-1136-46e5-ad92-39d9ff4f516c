#!/bin/bash

# Notes API测试脚本
BASE_URL="http://localhost:16135"
HEADER="Bullet: 36fdf066-9e42-11ec-b41e-525400043ced"

echo "=== Testing Notes API ==="

# 测试添加笔记
echo "1. Testing Add Note..."
ADD_RESPONSE=$(curl -s -X POST "$BASE_URL/api/notes/add" \
  -H "Content-Type: application/json" \
  -H "$HEADER" \
  -d '{
    "title": "Test Note",
    "content": "This is a test note content",
    "category": "test",
    "tags": "test,api"
  }')
echo "Add Note Response: $ADD_RESPONSE"

# 提取note_id
NOTE_ID=$(echo $ADD_RESPONSE | grep -o '"note_id":[0-9]*' | grep -o '[0-9]*')
echo "Created Note ID: $NOTE_ID"

# 测试获取笔记列表
echo -e "\n2. Testing Get Notes List..."
LIST_RESPONSE=$(curl -s -X GET "$BASE_URL/api/notes/list?size=10&offset=0" \
  -H "$HEADER")
echo "Notes List Response: $LIST_RESPONSE"

# 测试获取单个笔记
if [ ! -z "$NOTE_ID" ]; then
  echo -e "\n3. Testing Get Single Note..."
  GET_RESPONSE=$(curl -s -X GET "$BASE_URL/api/notes/$NOTE_ID" \
    -H "$HEADER")
  echo "Get Note Response: $GET_RESPONSE"

  # 测试更新笔记
  echo -e "\n4. Testing Update Note..."
  UPDATE_RESPONSE=$(curl -s -X PUT "$BASE_URL/api/notes/$NOTE_ID" \
    -H "Content-Type: application/json" \
    -H "$HEADER" \
    -d '{
      "title": "Updated Test Note",
      "content": "This is updated content"
    }')
  echo "Update Note Response: $UPDATE_RESPONSE"

  # 测试删除笔记
  echo -e "\n5. Testing Delete Note..."
  DELETE_RESPONSE=$(curl -s -X DELETE "$BASE_URL/api/notes/$NOTE_ID" \
    -H "$HEADER")
  echo "Delete Note Response: $DELETE_RESPONSE"
fi

echo -e "\n=== API Testing Complete ==="
