package service

import (
	"context"
	"notes-service/biz_error"
	"notes-service/common"
	"notes-service/models"
	notesGen "thrift-common/gen-go/notes"

	"github.com/sirupsen/logrus"
)

func AddNote(ctx context.Context, title, content string, category, tags *string) (int64, *biz_error.BizError) {
	return AddNoteWithType(ctx, title, content, "normal", category, tags)
}

func AddNoteWithType(ctx context.Context, title, content, noteType string, category, tags *string) (int64, *biz_error.BizError) {
	logger := logrus.New()

	// 参数验证
	if title == "" {
		return 0, biz_error.NewBizError(biz_error.ERR_INVALID_PARAMS)
	}

	// 验证笔记类型
	if noteType != "normal" && noteType != "timeline" {
		noteType = "normal" // 默认为普通笔记
	}

	// 创建笔记记录
	note := models.Note{
		Title:    title,
		Content:  content,
		NoteType: noteType,
	}

	if category != nil && *category != "" {
		note.Category = category
	}
	if tags != nil && *tags != "" {
		note.Tags = tags
	}

	logger.Infof("Creating note[%+v]", note)

	err := models.CreateNote(&note)
	if err != nil {
		logger.Errorf("CreateNote err[%+v]", err)
		return 0, err
	}

	logger.Infof("Successfully created note with ID: %d", note.ID)
	return note.ID, nil
}

func GetNotesPage(ctx context.Context, offset, count int64, keyword string, category string) ([]*notesGen.NoteItem, int64, bool, error) {
	logger := logrus.New()

	var total int64
	var notes []*models.Note
	var err error

	if keyword != "" {
		// 搜索模式
		total, err = models.CountSearchNotes(keyword, category)
		if err != nil {
			logger.Errorf("CountSearchNotes err[%+v]", err)
			return nil, 0, false, err
		}

		notes, err = models.SearchNotesList(keyword, offset, count, category)
		if err != nil {
			logger.Errorf("SearchNotesList err[%+v]", err)
			return nil, 0, false, err
		}
	} else {
		// 普通列表模式
		total, err = models.CountNotes(category)
		if err != nil {
			logger.Errorf("CountNotes err[%+v]", err)
			return nil, 0, false, err
		}

		notes, err = models.SelectNotesList(offset, count, category)
		if err != nil {
			logger.Errorf("SelectNotesList err[%+v]", err)
			return nil, 0, false, err
		}
	}

	// 计算是否还有更多数据
	hasMore := offset+count < total

	retList := make([]*notesGen.NoteItem, len(notes))
	for i, n := range notes {
		retList[i] = &notesGen.NoteItem{
			ID:         n.ID,
			Title:      n.Title,
			Content:    n.Content,
			NoteType:   n.NoteType,
			Category:   n.Category,
			Tags:       n.Tags,
			CreateTime: common.FormatTime(n.CreateTime),
			UpdateTime: common.FormatTime(n.UpdateTime),
		}
	}
	return retList, total, hasMore, nil
}

func UpdateNote(ctx context.Context, noteID int64, title, content, category, tags *string) *biz_error.BizError {
	return UpdateNoteWithType(ctx, noteID, title, content, nil, category, tags)
}

func UpdateNoteWithType(ctx context.Context, noteID int64, title, content, noteType, category, tags *string) *biz_error.BizError {
	logger := logrus.New()

	updates := make(map[string]interface{})
	if title != nil {
		updates["title"] = *title
	}
	if content != nil {
		updates["content"] = *content
	}
	if noteType != nil {
		// 验证笔记类型
		if *noteType == "normal" || *noteType == "timeline" {
			updates["note_type"] = *noteType
		}
	}
	if category != nil {
		updates["category"] = *category
	}
	if tags != nil {
		updates["tags"] = *tags
	}

	if len(updates) == 0 {
		return biz_error.NewBizError(biz_error.ERR_INVALID_PARAMS)
	}

	err := models.UpdateNote(ctx, noteID, updates)
	if err != nil {
		logger.Errorf("UpdateNote err[%+v]", err)
		return err
	}

	logger.Infof("Successfully updated note with ID: %d", noteID)
	return nil
}

func DeleteNote(ctx context.Context, noteID int64) *biz_error.BizError {
	logger := logrus.New()

	err := models.DeleteNote(noteID)
	if err != nil {
		logger.Errorf("DeleteNote err[%+v]", err)
		return biz_error.NewBizErrorByError(err)
	}

	logger.Infof("Successfully deleted note with ID: %d", noteID)
	return nil
}

func GetNote(ctx context.Context, noteID int64) (*notesGen.NoteItem, *biz_error.BizError) {
	logger := logrus.New()

	note, err := models.SelectNoteByID(ctx, noteID)
	if err != nil {
		logger.Errorf("SelectNoteByID err[%+v]", err)
		return nil, biz_error.NewBizErrorByError(err)
	}

	noteItem := &notesGen.NoteItem{
		ID:         note.ID,
		Title:      note.Title,
		Content:    note.Content,
		NoteType:   note.NoteType,
		Category:   note.Category,
		Tags:       note.Tags,
		CreateTime: common.FormatTime(note.CreateTime),
		UpdateTime: common.FormatTime(note.UpdateTime),
	}

	return noteItem, nil
}
