package service

import (
	"context"
	"encoding/json"
	"thrift-common/gen-go/base"
	notesGen "thrift-common/gen-go/notes"
)

func Wrap(data interface{}, err error) (string, error) {
	if err != nil {
		return "", err
	}
	ret, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	return string(ret), nil
}

// 定义服务
type NotesService struct {
}

func (this *NotesService) AddNote(ctx context.Context, req *notesGen.AddNoteRequest) (*notesGen.AddNoteResponse, error) {
	code := 0
	msg := "success"

	noteType := "normal"
	if req.NoteType != nil {
		noteType = *req.NoteType
	}

	noteID, err := AddNoteWithType(ctx, req.Title, req.Content, noteType, req.Category, req.Tags)
	if err != nil {
		code = err.Code()
		msg = err.Msg()
	}

	res := &notesGen.AddNoteResponse{
		NoteId: noteID,
		BaseResp: &base.BaseResponse{
			StatusCode:    int64(code),
			StatusMessage: msg,
		},
	}
	return res, nil
}

func (this *NotesService) GetNotesPage(ctx context.Context, req *notesGen.GetNotesPageRequest) (*notesGen.GetNotesPageResponse, error) {
	keyword := ""
	if req.Keyword != nil {
		keyword = *req.Keyword
	}
	category := ""
	if req.Category != nil {
		category = *req.Category
	}

	list, total, hasMore, err := GetNotesPage(ctx, req.Offset, req.Count, keyword, category)
	if err != nil {
		return nil, err
	}

	res := &notesGen.GetNotesPageResponse{
		NotesList: list,
		Total:     total,
		HasMore:   hasMore,
		BaseResp: &base.BaseResponse{
			StatusCode:    200,
			StatusMessage: "Success",
		},
	}
	return res, nil
}

func (this *NotesService) UpdateNote(ctx context.Context, req *notesGen.UpdateNoteRequest) (*notesGen.UpdateNoteResponse, error) {
	code := 0
	msg := "success"

	err := UpdateNoteWithType(ctx, req.NoteId, req.Title, req.Content, req.NoteType, req.Category, req.Tags)
	if err != nil {
		code = err.Code()
		msg = err.Msg()
	}

	res := &notesGen.UpdateNoteResponse{
		BaseResp: &base.BaseResponse{
			StatusCode:    int64(code),
			StatusMessage: msg,
		},
	}
	return res, nil
}

func (this *NotesService) DeleteNote(ctx context.Context, req *notesGen.DeleteNoteRequest) (*notesGen.DeleteNoteResponse, error) {
	code := 0
	msg := "success"

	err := DeleteNote(ctx, req.NoteId)
	if err != nil {
		code = err.Code()
		msg = err.Msg()
	}

	res := &notesGen.DeleteNoteResponse{
		BaseResp: &base.BaseResponse{
			StatusCode:    int64(code),
			StatusMessage: msg,
		},
	}
	return res, nil
}

func (this *NotesService) GetNote(ctx context.Context, req *notesGen.GetNoteRequest) (*notesGen.GetNoteResponse, error) {
	code := 0
	msg := "success"
	var noteItem notesGen.NoteItem

	note, err := GetNote(ctx, req.NoteId)
	if err != nil {
		code = err.Code()
		msg = err.Msg()
	} else {
		noteItem = *note
	}

	res := &notesGen.GetNoteResponse{
		Note: &noteItem,
		BaseResp: &base.BaseResponse{
			StatusCode:    int64(code),
			StatusMessage: msg,
		},
	}
	return res, nil
}
