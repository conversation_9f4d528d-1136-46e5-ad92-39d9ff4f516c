# KTask Service - 任务管理服务端

KTask Service是一个用Go编写的任务管理系统服务端，提供HTTP API和thrift RPC接口。

## 功能特性

- RESTful API：提供完整的任务管理HTTP接口
- Thrift RPC：高性能的RPC服务接口
- MySQL存储：可靠的数据持久化
- 层级任务：支持父子任务关系和树形结构
- 标记系统：支持多种类型的任务标记
- 时间追踪：记录任务操作历史和时间投入
- 搜索功能：支持任务和标记的关键字搜索

## 环境要求

- Go 1.19+
- MySQL 5.7+
- thrift 0.16.0+

## 安装部署

### 数据库配置
```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE ktask CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 配置连接信息
cp config.ini.example config.ini
# 编辑config.ini文件
```

### 编译运行
```bash
# 编译
make build

# 或者
go build -o task-service

# 运行
./task-service
```

### 配置文件
```ini
[mysql]
host = 127.0.0.1
port = 3306
user = root
password = your_password
database = ktask

[server]
port = 16135
```

## API接口

### 任务管理
- `POST /api/task/add` - 创建任务
- `POST /api/task/edit` - 编辑任务
- `POST /api/task/action` - 任务操作（开始/完成/标记等）
- `POST /api/task/move` - 移动任务
- `GET /api/task/list` - 获取任务列表
- `GET /api/task/search` - 搜索任务和标记

### 便捷功能
- `GET /api/task/last` - 获取最近任务
- `POST /api/task/pick` - 选择任务
- `POST /api/task/pickdel` - 取消选择

### 搜索API
```bash
# 搜索任务
GET /api/task/search?keyword=关键字&limit=20

# 搜索标记
GET /api/task/search?keyword=关键字&inMarks=true&markType=comment&limit=10
```

## 数据模型

### 任务表 (tasks)
```sql
CREATE TABLE tasks (
    id VARCHAR(255) PRIMARY KEY,
    parent VARCHAR(255),
    title VARCHAR(500),
    label VARCHAR(255),
    status VARCHAR(50),
    description TEXT,
    priority BIGINT,
    ddl TIMESTAMP,
    level BIGINT,
    createTime TIMESTAMP,
    updateTime TIMESTAMP
);
```

### 标记表 (marks)
```sql
CREATE TABLE marks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    taskID VARCHAR(255),
    type VARCHAR(50),
    content TEXT,
    createTime TIMESTAMP,
    updateTime TIMESTAMP
);
```

### 时间轴表 (timelines)
```sql
CREATE TABLE timelines (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    taskID VARCHAR(255),
    action VARCHAR(100),
    startTime TIMESTAMP,
    endTime TIMESTAMP,
    createTime TIMESTAMP
);
```

## 架构设计

### 目录结构
```
task-service/
├── main.go              # 服务入口
├── service/             # 业务逻辑层
│   ├── service.go       # 服务接口实现
│   ├── task.go          # 任务核心逻辑
│   ├── task_search_command.go  # 搜索功能
│   └── ...              # 其他业务模块
├── models/              # 数据模型层
│   ├── task.go          # 任务数据模型
│   ├── mark.go          # 标记数据模型
│   └── timelines.go     # 时间轴数据模型
├── config/              # 配置管理
├── biz_error/           # 业务错误定义
└── common/              # 公共工具
```

### 设计模式
- **分层架构**: 服务层、数据层、模型层清晰分离
- **依赖注入**: 通过接口解耦组件依赖
- **错误处理**: 统一的业务错误类型和处理
- **配置管理**: 环境配置和业务配置分离

### Thrift接口
```thrift
service task {
  AddTaskResponse AddTask(1: AddTaskRequest req),
  EditTaskResponse EditTask(1: EditTaskRequest req),
  ListTaskResponse ListTask(1: ListTaskRequest req),
  SearchTaskResponse SearchTask(1: SearchTaskRequest req),
  // ... 其他接口
}
```

## 业务逻辑

### 任务ID生成规则
- 根任务: "0"
- 子任务: 父任务ID + 两位数字序号 (例: "001", "0101")
- 最大支持99个子任务

### 任务状态流转
```
待启动 -> 进行中 -> 已完成
    |       |
    v       v
  阻塞中   挂起中
```

### 搜索实现
- **任务搜索**: 在title和description字段中使用LIKE查询
- **标记搜索**: 在content字段中搜索，支持按type过滤
- **排序**: 任务按updateTime DESC，标记按createTime DESC
- **限制**: 默认返回20条，最大100条

## 开发指南

### 本地开发
```bash
# 运行测试
go test ./...

# 代码格式化
go fmt ./...

# 静态检查
go vet ./...

# 启动开发服务
go run main.go
```

### 添加新接口
1. 在`idls/task.thrift`中定义接口
2. 重新生成thrift代码
3. 在`service/service.go`中实现接口
4. 在`service/`目录添加业务逻辑
5. 在`models/`中添加数据访问方法

### 数据库操作
```bash
# 初始化数据库
./scripts/test_init.sh

# 清理数据库  
./scripts/test_uninit.sh

# 运行测试
make test
```

## 性能优化

### 数据库索引
```sql
-- 任务查询优化
CREATE INDEX idx_tasks_parent ON tasks(parent);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_update_time ON tasks(updateTime);

-- 标记查询优化
CREATE INDEX idx_marks_task_id ON marks(taskID);
CREATE INDEX idx_marks_type ON marks(type);
CREATE INDEX idx_marks_content ON marks(content(100));

-- 时间轴查询优化
CREATE INDEX idx_timelines_task_id ON timelines(taskID);
CREATE INDEX idx_timelines_start_time ON timelines(startTime);
```

### 缓存策略
- 任务树结构缓存
- 最近任务列表缓存
- 搜索结果缓存

## 监控和日志

### 日志级别
- `DEBUG`: 详细调试信息
- `INFO`: 一般信息
- `WARN`: 警告信息
- `ERROR`: 错误信息

### 监控指标
- 请求响应时间
- 数据库连接数
- 任务操作频率
- 错误率统计

## 部署

### Docker部署
```bash
# 构建镜像
docker build -t ktask-service .

# 运行容器
docker run -p 16135:16135 ktask-service
```

### 生产配置
```bash
# 设置环境变量
export KTASK_ENV=production
export MYSQL_HOST=your-mysql-host
export MYSQL_PASSWORD=your-password

# 启动服务
./task-service
```

## 故障排除

### 常见问题
1. **数据库连接失败**: 检查配置文件和网络连通性
2. **thrift编译错误**: 确保thrift版本为0.16.0
3. **搜索功能异常**: 检查数据库索引是否创建

### 日志查看
```bash
# 查看服务日志
tail -f /var/log/ktask/service.log

# 查看数据库慢查询
mysql -e "SELECT * FROM information_schema.processlist;"
```

## 许可证

本项目采用MIT许可证。
