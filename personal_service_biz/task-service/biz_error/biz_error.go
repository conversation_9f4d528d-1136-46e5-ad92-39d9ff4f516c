package biz_error

import (
	"fmt"

	wErr "github.com/pkg/errors"
	"github.com/sirupsen/logrus"
)

type BIZ_ERROR_CODE int

const (
	// 用户视角的error case
	//  - 1. 业务数据模块相关
	ERR_DUP_TASKID                            BIZ_ERROR_CODE = 10001
	ERR_NOT_FOUND_PARENT                      BIZ_ERROR_CODE = 10002
	ERR_NOT_FOUND_TASK                        BIZ_ERROR_CODE = 10003
	ERR_NOT_PERMIT_NONLEAf                    BIZ_ERROR_CODE = 10004
	ERR_DIRTY_DATA                            BIZ_ERROR_CODE = 10005
	ERR_DIRTY_TASKID                          BIZ_ERROR_CODE = 10006
	ERR_DIRTY_PARENT_RESOURCE                 BIZ_ERROR_CODE = 10013
	ERR_NOT_PERMIT_FINISH_INPROGRESS          BIZ_ERROR_CODE = 10007
	ERR_NOT_PERMIT_REMOVE_INPROGRESS          BIZ_ERROR_CODE = 10011
	ERR_NOT_FOUND_INPROGRESS_TASK             BIZ_ERROR_CODE = 10008
	ERR_NOT_FOUND_SRC_TASK                    BIZ_ERROR_CODE = 10009
	ERR_REACH_MAX_SUB_TASK_ID                 BIZ_ERROR_CODE = 10010
	ERR_NOT_PERMIT_DELETE_TASK_WITH_RESOURCES BIZ_ERROR_CODE = 10012
	ERR_DUPLICATED_LABLE_NAME                 BIZ_ERROR_CODE = 10014
	ERR_CONFLICT_PICKUP_TASKID                BIZ_ERROR_CODE = 10015

	//  - 2. 请求参数相关
	ERR_WRONG_FORMAT                   BIZ_ERROR_CODE = 20001
	ERR_WRONG_FORMAT_TIME              BIZ_ERROR_CODE = 20006
	ERR_WRONG_FORMAT_DDL               BIZ_ERROR_CODE = 20013
	ERR_WRONG_FORMAT_TIMESPAN          BIZ_ERROR_CODE = 20010
	ERR_WRONG_FORMAT_POSITION          BIZ_ERROR_CODE = 20012
	ERR_WRONG_FORMAT_NUMBER            BIZ_ERROR_CODE = 20016
	ERR_MISS_ARG_POSITION              BIZ_ERROR_CODE = 20015
	ERR_MISS_ARG_TASK_LABEL            BIZ_ERROR_CODE = 20002
	ERR_MISS_ARG_NEW_LABEL_NAME        BIZ_ERROR_CODE = 20011
	ERR_WRONG_ACTION                   BIZ_ERROR_CODE = 20003
	ERR_MISS_ARG_CONTENT               BIZ_ERROR_CODE = 20004
	ERR_ILLEGAL_TIME                   BIZ_ERROR_CODE = 20005
	ERR_TIME_GT_NOW                    BIZ_ERROR_CODE = 20009
	ERR_MISS_ARG_PARENT_OR_DST_TASK    BIZ_ERROR_CODE = 20007
	ERR_ILLEGAL_ARG_PARENT_OR_DST_TASK BIZ_ERROR_CODE = 20014
	ERR_MISS_ARG_SRC_TASK              BIZ_ERROR_CODE = 20008

	// 业务模块定义的逻辑error
	ERR_WRONG_ROWS_AFFECTED = 80001

	// 其他不对外暴露的error
	ERR_INTERNAL       = 90001
	ERR_COMMON         = 90002
	ERR_MISS_IMPLEMENT = 90001 // 缺少逻辑分支
)

type BizErrorExtra struct {
	TaskID       *string
	RowsAffected *int64
	Message      *string
}

type BizError struct {
	code    BIZ_ERROR_CODE
	message string
	extra   *BizErrorExtra
}

var errMsgMap = map[BIZ_ERROR_CODE]string{
	ERR_DUP_TASKID:                            "Duplicated taskID",
	ERR_NOT_FOUND_TASK:                        "Not Found Task",
	ERR_NOT_FOUND_SRC_TASK:                    "Not Found SrcTask",
	ERR_NOT_FOUND_PARENT:                      "Not Found Parent",
	ERR_NOT_PERMIT_NONLEAf:                    "TaskAction Not Permit on NonLeaf Task",
	ERR_DIRTY_DATA:                            "Db Exist Dirty Data",
	ERR_DIRTY_TASKID:                          "Db Exist TaskID NOT Integer",
	ERR_DIRTY_PARENT_RESOURCE:                 "Db Exist Parent Task Owning Resource",
	ERR_NOT_PERMIT_FINISH_INPROGRESS:          "Not Permit Finish Inprogress Task",
	ERR_NOT_PERMIT_REMOVE_INPROGRESS:          "Not Permit Remove Inprogress Task",
	ERR_NOT_FOUND_INPROGRESS_TASK:             "Not Found Inprogress Task",
	ERR_REACH_MAX_SUB_TASK_ID:                 "Reach Max Sub Task ID",
	ERR_NOT_PERMIT_DELETE_TASK_WITH_RESOURCES: "Not Permit Delete Task With Resources",
	ERR_DUPLICATED_LABLE_NAME:                 "Duplicated Task Label",
	ERR_CONFLICT_PICKUP_TASKID:                "Conflict Pickup With Same TaskID",

	ERR_WRONG_FORMAT:                   "Wrong Format Arguments",
	ERR_WRONG_FORMAT_TIME:              "Wrong Format Time Arguments",
	ERR_WRONG_FORMAT_DDL:               "Wrong Format DDL Arguments",
	ERR_WRONG_FORMAT_TIMESPAN:          "Wrong Format TimeSpan Arguments",
	ERR_WRONG_FORMAT_POSITION:          "Wrong Format Position Arguments",
	ERR_WRONG_FORMAT_NUMBER:            "Wrong Format Number Arguments",
	ERR_MISS_ARG_POSITION:              "Miss Arguments Position",
	ERR_MISS_ARG_TASK_LABEL:            "Miss Argument Task ID Or Label",
	ERR_MISS_ARG_NEW_LABEL_NAME:        "Miss Argument Task New Label Name",
	ERR_WRONG_ACTION:                   "Wrong TaskAction",
	ERR_MISS_ARG_CONTENT:               "Miss Argument `Content`",
	ERR_ILLEGAL_TIME:                   "Illegal Time Argument",
	ERR_TIME_GT_NOW:                    "Illegal Time Greater than now",
	ERR_MISS_ARG_PARENT_OR_DST_TASK:    "Miss Argument `Parent` or `DstTaskID`",
	ERR_ILLEGAL_ARG_PARENT_OR_DST_TASK: "Illegal Argument `Parent` or `DstTaskID`",

	ERR_WRONG_ROWS_AFFECTED: "error:ROWS_AFFECTED",

	ERR_INTERNAL: "internal error",
}

func NewBizError(code BIZ_ERROR_CODE) *BizError {
	return &BizError{
		code:    code,
		message: errMsgMap[code],
		extra:   nil,
	}
}

func NewBizErrorWithExtra(code BIZ_ERROR_CODE, extra BizErrorExtra) *BizError {
	return &BizError{
		code:    code,
		message: errMsgMap[code],
		extra:   &extra,
	}
}

func NewBizErrorWithMessage(msg string) *BizError {
	return &BizError{
		code:    ERR_COMMON,
		message: msg,
	}
}

func NewBizErrorByError(err error) *BizError {
	logger := logrus.New()
	// NOTE 这种写法无法打印stack。。。
	// logger.Error(wErr.Wrap(fmt.Errorf("%+v", err), ""))
	logger.Errorf("%+v", wErr.Wrap(fmt.Errorf("%+v", err), ""))
	return &BizError{
		code:    ERR_INTERNAL,
		message: errMsgMap[ERR_INTERNAL],
	}
}

func (e *BizError) Code() int {
	return int(e.code)
}

func (e *BizError) Msg() string {
	if e.extra == nil {
		return e.message
	}

	if e.code == ERR_DUP_TASKID {
		return fmt.Sprintf("%s [%+v]", e.message, *e.extra.TaskID)
	}
	if e.extra.Message != nil {
		return *e.extra.Message
	}
	if e.extra.TaskID != nil {
		return fmt.Sprintf("%s [%s]", e.message, *e.extra.TaskID)
	}
	return e.message
}

func (e *BizError) IsCode(code BIZ_ERROR_CODE) bool {
	return code == e.code
}
