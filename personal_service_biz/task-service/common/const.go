package common

type TaskStatusType string

const (
	TaskStatusUnstarted  TaskStatusType = "待启动"
	TaskStatusInprogress TaskStatusType = "进行中"
	TaskStatusBlocking   TaskStatusType = "阻塞中"
	TaskStatusHoldon     TaskStatusType = "挂起中"
	TaskStatusFinish     TaskStatusType = "已完成"
	TaskStatusRemoved    TaskStatusType = "已删除"
	TaskStatusUnknown    TaskStatusType = "未知"
)

type RefType string

const (
	RefTypeTask2Task RefType = "task2task"
	RefTypeMark2Task RefType = "mark2task"
)
