package common

import (
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"
)

// https://golangcode.com/how-to-check-if-a-string-is-a-url/
func IsValidUrl(toTest string) bool {
	_, err := url.ParseRequestURI(toTest)
	if err != nil {
		return false
	}

	u, err := url.Parse(toTest)
	if err != nil || u.Scheme == "" || u.Host == "" {
		return false
	}

	return true
}

// NOTE 垃圾golang，下面只带分钟的的方式无法解析当天日期，会变成0000-01-01。只能用之前rust的方式处理了
//func ConvertFormatTime(timeStr string) (*time.Time, error) {
//	// 按照支持startTime格式转换
//	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", timeStr, time.Local)
//	if err == nil {
//		return &startTime, nil
//	}
//
//	startTime, err = time.ParseInLocation("2006-01-02 15:04", timeStr, time.Local)
//	if err == nil {
//		return &startTime, nil
//	}
//
//	startTime, err = time.ParseInLocation("15:04:05", timeStr, time.Local)
//	if err == nil {
//		return &startTime, nil
//	}
//
//	startTime, err = time.ParseInLocation("15:04", timeStr, time.Local)
//	if err == nil {
//		return &startTime, nil
//	}
//
//	return nil, fmt.Errorf("Wrong Format Time[%s]", timeStr, time.Local)
//}
func ConvertFormatTime(timeStr string) (*time.Time, error) {
	now := time.Now().Format("2006-01-02 15:04:05")

	// 按照支持startTime格式转换

	// Part1
	// 当成%Y-%m-%d %H:%M:%S的格式来拼接
	startTime, err := time.ParseInLocation("2006-01-02 15:04:05", timeStr, time.Local)
	if err == nil {
		return &startTime, nil
	}

	// 当成%m-%d %H:%M:%S的格式来拼接
	startTime, err = time.ParseInLocation("2006-01-02 15:04:05", now[0:5]+timeStr, time.Local)
	if err == nil {
		return &startTime, nil
	}

	// 当成%d %H:%M:%S的格式来拼接
	startTime, err = time.ParseInLocation("2006-01-02 15:04:05", now[0:8]+timeStr, time.Local)
	if err == nil {
		return &startTime, nil
	}

	// 当成%H:%M:%S的格式来拼接
	startTime, err = time.ParseInLocation("2006-01-02 15:04:05", now[0:11]+timeStr, time.Local)
	if err == nil {
		return &startTime, nil
	}

	// Part2
	// 当成%Y-%m-%d %H:%M的格式来拼接
	startTime, err = time.ParseInLocation("2006-01-02 15:04:05", timeStr+":00", time.Local)
	if err == nil {
		return &startTime, nil
	}

	// 当成%m-%d %H:%M的格式来拼接
	startTime, err = time.ParseInLocation("2006-01-02 15:04:05", now[0:5]+timeStr+":00", time.Local)
	if err == nil {
		return &startTime, nil
	}

	// 当成%d %H:%M的格式来拼接
	startTime, err = time.ParseInLocation("2006-01-02 15:04:05", now[0:8]+timeStr+":00", time.Local)
	if err == nil {
		return &startTime, nil
	}

	// 当成%H:%M的格式来拼接
	startTime, err = time.ParseInLocation("2006-01-02 15:04:05", now[0:11]+timeStr+":00", time.Local)
	if err == nil {
		return &startTime, nil
	}
	// Part3: 只有%H

	// Part4: 不带时间
	// 当成%Y-%m-%d的格式来拼接
	startTime, err = time.ParseInLocation("2006-01-02 15:04:05", timeStr+" 00:00:00", time.Local)
	if err == nil {
		return &startTime, nil
	}

	// 当成%m-%d的格式来拼接
	startTime, err = time.ParseInLocation("2006-01-02 15:04:05", now[0:5]+timeStr+" 00:00:00", time.Local)
	if err == nil {
		return &startTime, nil
	}

	return nil, fmt.Errorf("Wrong Format Time[%s]", timeStr)
}

func FormatTime(date *time.Time) string {
	if date == nil {
		return ""
	}
	return date.Format("2006-01-02 15:04:05")
}

func StrPtr2Str(p *string) string {
	if p != nil {
		return *p
	}
	return "(nil)"
}

func StrPtrIsEmpty(p *string) bool {
	if p == nil {
		return true
	}
	if *p == "" {
		return true
	}
	return false
}

func ConvertSize(b uint64) string {
	if b < 1024 {
		return ""
	} else if b < 1024*1024 {
		return fmt.Sprintf("(%.2fKB)", float64(b)/1024)
	} else if b < 1024*1024*1024 {
		return fmt.Sprintf("(%.2fMB)", float64(b)/(1024*1024))
	} else {
		return fmt.Sprintf("(%.2fGB)", float64(b)/(1024*1024*1024))
	}
}

func UniqStringSlice(slice []string) []string {
	uniqMap := map[string]bool{}
	for _, s := range slice {
		if !uniqMap[s] {
			uniqMap[s] = true
		}
	}

	retSlice := []string{}
	for s, _ := range uniqMap {
		retSlice = append(retSlice, s)
	}
	return retSlice
}

func UniqInt64Slice(slice []int64) []int64 {
	uniqMap := map[int64]bool{}
	for _, s := range slice {
		if !uniqMap[s] {
			uniqMap[s] = true
		}
	}

	retSlice := []int64{}
	for s, _ := range uniqMap {
		retSlice = append(retSlice, s)
	}
	return retSlice
}

func Int64Slice2StrSlice(s []int64) []string {
	ret := []string{}
	for _, i := range s {
		ret = append(ret, strconv.Itoa(int(i)))
	}
	return ret
}

func StrSlice2Int64Slice(s []string) []int64 {
	ret := []int64{}
	for _, i := range s {
		is, _ := strconv.ParseInt(i, 10, 64)
		ret = append(ret, is)
	}
	return ret
}

func ParseFilterTimeSpan(s string) ([]*time.Time, error) {
	// 作为filterTimeSpan时，只有startTime默认当成endTime=now
	timeSpan, err := ParseTimeSpan(s)
	if err != nil {
		return nil, err
	}
	if len(timeSpan) == 2 && timeSpan[1] == nil {
		now := time.Now()
		timeSpan[1] = &now
	}
	return timeSpan, nil
}

func ParseTimeSpan(s string) ([]*time.Time, error) {
	var err error
	span := strings.Split(s, "_")
	// 1个或者2个是允许的
	if len(span) > 2 || len(span) == 0 {
		return nil, fmt.Errorf("Wrong Format TimeSpan[%+v]", s)
	}
	retSpan := make([]*time.Time, 2, 2)
	if len(span) == 1 {
		// 只有startTime，则endTime为nil
		retSpan[1] = nil
		retSpan[0], err = ConvertFormatTime(span[0])
		return retSpan, err
	} // else 两个元素都有

	if span[1] == "" {
		// endTime为空字符串，表示截至now
		now := time.Now()
		retSpan[1] = &now
		retSpan[0], err = ConvertFormatTime(span[0])
		return retSpan, err
	}

	retSpan[0], err = ConvertFormatTime(span[0])
	if err != nil {
		return nil, err
	}
	retSpan[1], err = ConvertFormatTime(span[1])
	if err != nil {
		return nil, err
	}
	return retSpan, nil
}

func IsInTimeSpan(timePoint *time.Time, timeSpan []*time.Time) bool {
	if len(timeSpan) == 0 {
		return false
	}

	if len(timeSpan) == 1 {
		return timePoint.Unix() >= timeSpan[0].Unix()
	}

	return timePoint.Unix() <= timeSpan[1].Unix() && timePoint.Unix() >= timeSpan[0].Unix()
}
