package config

import (
	"context"
	"os"
)

type Config struct {
	DSN string
}

var globalConfig Config

func loadConfigs(ctx context.Context) error {
	env := os.Getenv("ENV")
	if env == "deploy" {
		// 加载config.deploy文件
		globalConfig.DSN = "taskuser:taskpass@tcp(task-mysql:3306)/db_task?charset=utf8mb4&parseTime=True&loc=Local"
	} else {
		// 开发环境加载config.dev文件
		// globalConfig.StorePath = "/data/docker-volumes/task-service/store_root"
		// TODO 这里要怎么跟bilibili-crawler的配置保持一致呢？但其实bilibili-crawler里面映射到/store_root的末尾还有/bilibili，是/data/docker-volumes/public_root/file_resources/bilibili
		globalConfig.DSN = "taskuser:taskpass@tcp(127.0.0.1:3314)/db_task?charset=utf8mb4&parseTime=True&loc=Local"
	}
	return nil
}

func InitConfig(ctx context.Context) error {
	return loadConfigs(ctx)
}

func GetDSN() string {
	return globalConfig.DSN
}
