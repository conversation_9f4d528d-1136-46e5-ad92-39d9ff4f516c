package main

import (
	"context"
	"task-service/config"
	"task-service/models"
	"task-service/service"
	"thrift-common/server/task"

	"github.com/sirupsen/logrus"
)

func main() {
	var err error
	ctx := context.Background()
	logger := logrus.New()
	err = config.InitConfig(ctx)
	if err != nil {
		logger.Errorf("loadConfigs failed: %+v", err)
		return
	}

	err = models.InitMysql(ctx, config.GetDSN())
	if err != nil {
		logger.Errorf("initMysql failed: %+v", err)
		return
	}

	handler := &service.TaskService{}
	logger.Info("Server Started!")
	err = task.Server(ctx, handler)
	if err != nil {
		logger.Errorf("Server error: %+v", err)
	}
}
