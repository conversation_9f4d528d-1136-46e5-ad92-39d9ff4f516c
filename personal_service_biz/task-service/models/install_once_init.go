package models

import (
	"context"
	"fmt"
	"task-service/biz_error"
	"task-service/common"
	"time"

	wErr "github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

func DeleteAll() {
	// 硬删除。否则Task因为外键索引无法删除。
	result := db.Where("1 = 1").Unscoped().Delete(&Timelines{})
	fmt.Printf("result : %+v\n", result)
	result = db.Where("1 = 1").Unscoped().Delete(&Mark{})
	fmt.Printf("result : %+v\n", result)
	result = db.Where("1 = 1").Delete(&Task{})
	fmt.Printf("result : %+v\n", result)
}

func InitTasks() {
	logger := logrus.New()
	presetStatus := string(common.TaskStatusUnstarted)
	presetTime, _ := time.Parse("2006-01-02 15:04:05", "2022-01-01 00:00:00")
	deletedAt := gorm.DeletedAt{}
	tasks := []*Task{
		{0, "1", "0", "bytedance", nil, nil, false, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{0, "5", "0", "personal", nil, nil, false, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		// 来自元模型
		// 是容器、框架、模板。也包括细化任务。总之就是所有自己想学习的东西的一个划分多级分类而已。
		{1, "99", "5", "学习元模型", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{2, "9901", "99", "经金财会理论", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "990101", "9901", "世界经济学(关红凌)", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "990102", "9901", "投资心得", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{2, "9902", "99", "经济政治生活", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "990202", "9902", "经济政治制度学习", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{4, "99020201", "990202", "地方债与城投", nil, nil, true, presetStatus, "地方政府发债机制与城投的历史了解", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "990203", "9902", "经济政治制度研究", nil, nil, true, presetStatus, "发展改革&经济规划&城市规划&政策研究", 2, 0, nil, presetTime, presetTime, deletedAt},
		{4, "99020301", "990203", "广东主要城市规划&产业研究&政策研究", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{5, "9902030101", "99020301", "深圳如何吸引那么多互联网企业", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{4, "99020302", "990203", "全国主要城市的GDP和财政收入来源、支柱产业", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{5, "9902030201", "99020302", "成都", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{5, "9902030202", "99020302", "大同旅游业", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{4, "99020303", "990203", "经济工作会议:学习&分析&研究", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{4, "99020304", "990203", "政府信息平台搜集", nil, nil, true, presetStatus, "整理一下都有哪些平台是政府发布第一手重要信息、文件的，并结合平时各种吹的自媒体的信息，看看都是哪些信息源头。以后自己关注。", 2, 0, nil, presetTime, presetTime, deletedAt},
		{4, "99020305", "990203", "权威数据平台搜集", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "990204", "9902", "宏观经济", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "990205", "9902", "世界经济", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "990206", "9902", "行业与产业链研究", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{4, "99020601", "990206", "C919相关产业链上下游调研", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{4, "99020602", "990206", "房地产行业研究（找找论文）", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{4, "99020603", "990206", "军工相关产业链和企业，对沙特出口的部分", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "990207", "9902", "地缘政治", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "990208", "9902", "热点时事", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "990209", "9902", "前沿动向", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{4, "99020901", "990209", "盈创建筑科技和3d建筑打印", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "990210", "9902", "社会规则", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{4, "99021001", "990210", "了解社会各行业利益输送规则", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},

		{2, "9903", "99", "自然科学", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "990301", "9903", "数学", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "990302", "9903", "物理", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{2, "9904", "99", "技术", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{2, "9905", "99", "社会科学", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{2, "9906", "99", "历史", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{2, "9907", "99", "地理人文", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{2, "9908", "99", "美学", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "990801", "9908", "艺术", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "990802", "9908", "建筑", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{4, "99080201", "990802", "欧美知名老建筑涉猎", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{2, "9909", "99", "医学", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "990901", "9909", "环孢素药理", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "990902", "9909", "护肝药药理", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "990903", "9909", "再障病理", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},

		{2, "9911", "99", "能力训练", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "991101", "9911", "社交", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "991102", "9911", "语言学习", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{4, "99110201", "991102", "英语口语训练", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{4, "99110202", "991102", "法语口语训练", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "991103", "9911", "表达与沟通训练", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
		{3, "991104", "9911", "形象管理", nil, nil, true, presetStatus, "", 2, 0, nil, presetTime, presetTime, deletedAt},
	}
	for _, t := range tasks {
		err := CreateTask(t)
		if err != nil && !err.IsCode(biz_error.ERR_DUP_TASKID) {
			logger.Warnf("InitData err[%+v]", err.Msg())
		}
	}
}

// 初始数据: 元模型预置任务
func InitData() error {
	// ctx := context.Background()

	// DeleteAll()
	InitTasks()

	// startTime, _ := common.ConvertFormatTime("2020-01-01 23:00")
	// now := time.Now()
	// CreateTimeline(context.Background(), &Timelines{
	// 	TaskID:     "991104",
	// 	StartTime:  *startTime,
	// 	EndTime:    nil,
	// 	CreateTime: now,
	// 	UpdateTime: now,
	// })
	return nil
}

func FixReferences() error {
	result := db.Where("1 = 1").Unscoped().Delete(&Reference{})

	ctx := context.Background()
	logger := logrus.New()
	var tasks []*Task
	result = db.Find(&tasks)
	if result.Error != nil {
		return wErr.Wrap(result.Error, "")
	}

	for _, t := range tasks {
		e := BuildTaskReference(ctx, t)
		if e != nil {
			logger.Warnf("InitData err[%+v]", e.Msg())
		}
	}

	// step3: marks 正向ref
	var marks []*Mark
	result = db.Find(&marks)
	if result.Error != nil {
		return wErr.Wrap(result.Error, "")
	}

	for _, m := range marks {
		e := BuildMarkReference(ctx, m)
		if e != nil {
			logger.Warnf("InitData err[%+v]", e.Msg())
		}
	}

	return nil
}

func InstallOnceInit(ctx context.Context) error {
	logger := logrus.New()
	err := InitData()
	if err != nil {
		logger.Errorf("InitData err[%+v]", err)
	}

	return FixReferences()
}
