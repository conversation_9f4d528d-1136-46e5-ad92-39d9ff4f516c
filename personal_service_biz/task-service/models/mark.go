package models

import (
	"context"
	"fmt"
	"task-service/biz_error"
	"time"

	"gorm.io/gorm"
)

type Mark struct {
	ID         int64          `gorm:"column:id"`
	TaskID     string         `gorm:"column:taskID"`
	Type       string         `gorm:"column:type"`
	DocLink    *string        `gorm:"column:docLink"`
	Content    string         `gorm:"column:content"`
	CreateTime time.Time      `gorm:"column:createTime"`
	UpdateTime time.Time      `gorm:"column:updateTime"`
	DeleteTime gorm.DeletedAt `gorm:"column:deleteTime"`
}

func (Mark) TableName() string {
	return "marks"
}

func CreateMark(ctx context.Context, mark *Mark) *biz_error.BizError {
	if result := db.Create(mark); result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	} else {
		return nil
	}
}

func SaveMark(ctx context.Context, mark *Mark) *biz_error.BizError {
	if result := db.Save(mark); result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	} else {
		return nil
	}
}

func SelectMarksByTaskID(ctx context.Context, taskID string) ([]*Mark, *biz_error.BizError) {
	var marks []*Mark
	result := db.Where("taskID = ?", taskID).
		Find(&marks)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	return marks, nil
}

func UpdateMarksTaskIDByTaskID(ctx context.Context, toTaskID string, byTaskID string) *biz_error.BizError {
	result := db.Model(&Mark{}).
		Where("taskID = ?", byTaskID).
		Update("taskID", toTaskID)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

func SelectMarkListByIDs(ctx context.Context, ids []int64) ([]*Mark, *biz_error.BizError) {
	var marks []*Mark
	result := db.Where("id in ?", ids).
		Find(&marks)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected != int64(len(marks)) {
		s := fmt.Sprintf("ids[%s] result.RowsAffected[%d] != len[%d]", ids, result.RowsAffected, len(marks))
		return nil, biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_ROWS_AFFECTED, biz_error.BizErrorExtra{
			Message: &s,
		})
	}
	return marks, nil
}

func SearchMarksByKeyword(ctx context.Context, keyword string, markType string, limit int) ([]*Mark, *biz_error.BizError) {
	var marks []*Mark
	query := db.Where("content LIKE ?", "%"+keyword+"%")
	if markType != "" {
		query = query.Where("type = ?", markType)
	}
	result := query.Limit(limit).
		Order("createTime DESC").
		Find(&marks)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	return marks, nil
}
