package models

import (
	"context"

	wErr "github.com/pkg/errors"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var db *gorm.DB

func GetDB() *gorm.DB {
	return db
}

func InitMysql(ctx context.Context, dsn string) error {
	logger := logrus.New()

	err := InitConnection(ctx, dsn)
	if err != nil {
		logger.Errorf("InitConnection err[%+v]", err)
		return err
	}

	InitTable(ctx)

	return InstallOnceInit(ctx)
	// return nil
}

func InitTable(ctx context.Context) {
	logger := logrus.New()
	err := db.AutoMigrate(&Task{}, &Timelines{})
	if err != nil {
		logger.Errorf("AutoMigrate failed: %+v", err)
	}
}

func InitConnection(ctx context.Context, dsn string) error {
	localDB, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		return wErr.Wrap(err, "")
	}
	db = localDB
	return nil
}
