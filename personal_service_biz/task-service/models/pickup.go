package models

import (
	"context"
	"task-service/biz_error"
	"time"

	"gorm.io/gorm"
)

type Pickup struct {
	ID         int64          `gorm:"column:id"`
	TaskID     string         `gorm:"column:taskID"`
	Position   int64          `gorm:"column:position"`
	CreateTime time.Time      `gorm:"column:createTime"`
	UpdateTime time.Time      `gorm:"column:updateTime"`
	DeleteTime gorm.DeletedAt `gorm:"column:deleteTime"`
}

func (Pickup) TableName() string {
	return "pickups"
}

// func CreatePickup(ctx context.Context, mark *Pickup) *biz_error.BizError {
// 	if result := db.Create(mark); result.Error != nil {
// 		return biz_error.NewBizErrorByError(result.Error)
// 	} else {
// 		return nil
// 	}
// }

func SelectPickupByPosition(ctx context.Context, position int64) (*Pickup, *biz_error.BizError) {
	var pickup Pickup
	result := db.Where("position = ?", position).First(&pickup)
	if result.Error != nil {
		if result.Error.Error() == "record not found" {
			return nil, nil
		}
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected != 1 {
		return nil, biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_ROWS_AFFECTED, biz_error.BizErrorExtra{
			RowsAffected: &result.RowsAffected,
		})
	}
	return &pickup, nil
}

func DeletePickup(ctx context.Context, pickup *Pickup) *biz_error.BizError {
	// 变化太频繁，而且价值不大，直接硬删除吧
	result := db.Where("id = ?", pickup.ID).Unscoped().Delete(&Pickup{})
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected != int64(1) {
		return biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_ROWS_AFFECTED, biz_error.BizErrorExtra{
			RowsAffected: &result.RowsAffected,
		})
	}
	return nil
}

func CreatePickup(pickup *Pickup) *biz_error.BizError {
	if result := db.Create(pickup); result.Error != nil {
		// var mysqlErr *mysql.MySQLError
		// if errors.As(result.Error, &mysqlErr) && mysqlErr.Number == 1062 {
		// 	return biz_error.NewBizErrorWithExtra(biz_error.ERR_DUP_TASKID, biz_error.BizErrorExtra{
		// 		PickupID: &pickup.ID,
		// 	})
		// }
		return biz_error.NewBizErrorByError(result.Error)
	} else {
		return nil
	}
}

func SavePickup(ctx context.Context, pickup *Pickup) *biz_error.BizError {
	pickup.UpdateTime = time.Now()
	if result := db.Save(pickup); result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	} else {
		return nil
	}
}

func SelectAllPickups(ctx context.Context) ([]*Pickup, *biz_error.BizError) {
	var pickups []*Pickup
	result := db.
		Order("position asc").
		Find(&pickups)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	return pickups, nil
}
