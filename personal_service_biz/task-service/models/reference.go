package models

import (
	"context"
	"errors"
	"regexp"
	"strconv"
	"task-service/biz_error"
	"task-service/common"
	"time"

	"github.com/go-sql-driver/mysql"
	"gorm.io/gorm"
)

type Reference struct {
	ID         int64          `gorm:"column:id"`
	RefType    string         `gorm:"column:refType"`
	SrcID      string         `gorm:"column:srcID"`
	DestID     string         `gorm:"column:destID"`
	CreateTime time.Time      `gorm:"column:createTime"`
	UpdateTime time.Time      `gorm:"column:updateTime"`
	DeleteTime gorm.DeletedAt `gorm:"column:deleteTime"`
}

func (Reference) TableName() string {
	return "references"
}

func AddReference(ctx context.Context, refType common.RefType, srcID, destID string) *biz_error.BizError {
	now := time.Now()
	result := db.Create(&Reference{
		RefType:    string(refType),
		SrcID:      srcID,
		DestID:     destID,
		CreateTime: now,
		UpdateTime: now,
	})
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

func DetectAndAddReference(ctx context.Context, refType common.RefType, srcID string, data string) *biz_error.BizError {
	re := regexp.MustCompile(`@task_(\d+)`)
	match := re.FindAllStringSubmatch(data, -1)
	if len(match) > 0 {
		for _, i := range match {
			refTaskID := i[1]

			err := AddReference(ctx, refType, srcID, refTaskID)
			if err != nil && !err.IsCode(biz_error.ERR_DUP_TASKID) {
				return err
			}
		}
	}
	return nil
}

func BuildTaskReference(ctx context.Context, task *Task) *biz_error.BizError {
	e := DetectAndAddReference(ctx, common.RefTypeTask2Task, task.ID, task.Title)
	if e != nil {
		return e
	}
	e = DetectAndAddReference(ctx, common.RefTypeTask2Task, task.ID, task.Description)
	if e != nil {
		return e
	}
	return nil
}

func BuildMarkReference(ctx context.Context, mark *Mark) *biz_error.BizError {
	e := DetectAndAddReference(ctx, common.RefTypeMark2Task, strconv.Itoa(int(mark.ID)), mark.Content)
	if e != nil {
		return e
	}
	return nil
}

func SelectRefsReverse(ctx context.Context, refType common.RefType, destID string) ([]string, *biz_error.BizError) {
	var refs []*Reference
	result := db.Where("refType = ? and destID = ?", string(refType), destID).
		Find(&refs)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}

	var byRefIDs []string
	for _, r := range refs {
		byRefIDs = append(byRefIDs, r.SrcID)
	}
	return byRefIDs, nil
}

func SelectRefsReverseAllType(ctx context.Context, destID string) ([]*Reference, *biz_error.BizError) {
	var refs []*Reference
	result := db.Where("destID = ?", destID).
		Find(&refs)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}

	return refs, nil
}

// func SelectRefsForward(ctx context.Content, refType common.RefType, srcID string) ([]string, *biz_error.BizError) {
// 	var refIDs []string
// 	result := db.Where("refType = ? and srcID = ?", string(refType), srcID).
// 		Find(&refIDs)
// 	if result.Error != nil {
// 		return nil, biz_error.NewBizErrorByError(result.Error)
// 	}
// 	return refIDs, nil
// }

func UpdateRefsReverse(ctx context.Context, oldID, newID string) *biz_error.BizError {
	result := db.Model(&Reference{}).
		Where("destID = ?", oldID).
		Update("destID", newID)
	if result.Error != nil {
		// 如果是重复冲突，可能是00节点缩并回父节点时的移动
		var mysqlErr *mysql.MySQLError
		if errors.As(result.Error, &mysqlErr) && mysqlErr.Number == 1062 {
			return nil
		}
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

func UpdateRefsForward(ctx context.Context, oldID, newID string) *biz_error.BizError {
	result := db.Model(&Reference{}).
		Where("refType = ? and srcID = ?", string(common.RefTypeTask2Task), oldID).
		Update("srcID", newID)
	if result.Error != nil {
		// 如果是重复冲突，可能是00节点缩并回父节点时的移动
		var mysqlErr *mysql.MySQLError
		if errors.As(result.Error, &mysqlErr) && mysqlErr.Number == 1062 {
			return nil
		}
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

func UpdateRefs(ctx context.Context, oldID, newID string) *biz_error.BizError {
	err := UpdateRefsReverse(ctx, oldID, newID)
	if err != nil {
		return err
	}

	err = UpdateRefsForward(ctx, oldID, newID)
	if err != nil {
		return err
	}

	return nil
}

// 一般只允许通过正向引用的关系去删除。如果自身被引用，不允许删除
func DeleteReferences(ctx context.Context, srcID string) *biz_error.BizError {
	// TODO 先用硬删除吧。否则deleteTime也会导致唯一索引冲突
	result := db.Where("srcID = ?", srcID).Unscoped().Delete(&Reference{})
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}
