package models

import (
	"encoding/json"
	"task-service/biz_error"
	"task-service/common"
)

type References struct {
	RefTasks    []string `json:"refTasks"`
	ByRefTasks  []string `json:"byRefTasks"`
	ByRefMarks  []int64  `json:"byRefMarks"`
	RefPartners []string `json:"refPartners"`
}

func NewReferences() *References {
	return &References{
		RefTasks:    []string{},
		ByRefTasks:  []string{},
		ByRefMarks:  []int64{},
		RefPartners: []string{},
	}
}

func NewReferencesFromStr(strRef *string) (*References, *biz_error.BizError) {
	if strRef == nil {
		return NewReferences(), nil
	}

	var ref References
	rerr := json.Unmarshal([]byte(*strRef), &ref)
	if rerr != nil {
		return nil, biz_error.NewBizErrorByError(rerr)
	}
	return &ref, nil
}

func (ref *References) ToStringPtr() *string {
	if len(ref.RefTasks) == 0 &&
		len(ref.RefPartners) == 0 &&
		len(ref.ByRefTasks) == 0 &&
		len(ref.ByRefMarks) == 0 {
		return nil
	}

	ref.RefTasks = common.UniqStringSlice(ref.RefTasks)
	ref.RefPartners = common.UniqStringSlice(ref.RefPartners)
	ref.ByRefTasks = common.UniqStringSlice(ref.ByRefTasks)
	ref.ByRefMarks = common.UniqInt64Slice(ref.ByRefMarks)

	b, _ := json.Marshal(ref)
	str := string(b)
	return &str
}
