package models

import (
	"context"
	"errors"
	"fmt"
	"task-service/biz_error"
	"time"

	"github.com/go-sql-driver/mysql"
	wErr "github.com/pkg/errors"
	"gorm.io/gorm"
)

// level之所以需要额外保存，因为不像JsonDB每次都拿全量数据构建。通过mysql可以直接select具体的task，所以无法构建level字段。虽然可以简单通过ID长度判断，但是始终不是长久之计
type Task struct {
	Level       int64          `gorm:"column:level"`
	ID          string         `gorm:"column:id"`
	Parent      string         `gorm:"column:parent"`
	Title       string         `gorm:"column:title"`
	Label       *string        `gorm:"column:label"`
	CategoryID  *int64         `gorm:"column:categoryID"`
	IsMeta      bool           `gorm:"column:isMeta"`
	Status      string         `gorm:"column:status"`
	Description string         `gorm:"column:description"`
	Priority    int64          `gorm:"column:priority"`
	Quadrant    int64          `gorm:"column:quadrant"`
	DDL         *time.Time     `gorm:"column:ddl"`
	CreateTime  time.Time      `gorm:"column:createTime"`
	UpdateTime  time.Time      `gorm:"column:updateTime"`
	DeleteTime  gorm.DeletedAt `gorm:"column:deleteTime"`
}

func (Task) TableName() string {
	return "tasks"
}

func CreateTask(task *Task) *biz_error.BizError {
	result := db.Create(task)
	if result.Error != nil {
		var mysqlErr *mysql.MySQLError
		// TODO 这里除了考虑重复键冲突，还要考虑是不是ID。比如Label冲突就应该报错。。
		if errors.As(result.Error, &mysqlErr) && mysqlErr.Number == 1062 {
			return biz_error.NewBizErrorWithExtra(biz_error.ERR_DUP_TASKID, biz_error.BizErrorExtra{
				TaskID: &task.ID,
			})
		}
		return biz_error.NewBizErrorByError(result.Error)
	}

	if result.RowsAffected != int64(1) {
		return biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_ROWS_AFFECTED, biz_error.BizErrorExtra{
			RowsAffected: &result.RowsAffected,
		})
	}
	return nil
}

func SaveTask(ctx context.Context, task *Task) *biz_error.BizError {
	task.UpdateTime = time.Now()
	result := db.Save(task)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	// if result.RowsAffected != int64(1) {
	// 	return biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_ROWS_AFFECTED, biz_error.BizErrorExtra{
	// 		RowsAffected: &result.RowsAffected,
	// 	})
	// }
	return nil
}

func DeleteTask(ctx context.Context, task *Task) *biz_error.BizError {
	// 既然是硬删除，check一下相关的timelines是否都已经被软删除了（否则不允许删除该task），直接硬删除清空。至于marks，留着报错不允许删除吧。
	err := HardDeleteTimelinesByTaskID(ctx, task.ID)
	if err != nil {
		return err
	}

	result := db.Where("id = ?", task.ID).Delete(&Task{})
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected != int64(1) {
		return biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_ROWS_AFFECTED, biz_error.BizErrorExtra{
			RowsAffected: &result.RowsAffected,
		})
	}
	return nil
}

func UpdateTaskByTaskID(ctx context.Context, task *Task, byTaskID string) *biz_error.BizError {
	if result := db.Model(&Task{}).
		Where("id = ?", byTaskID).
		Updates(task); result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	} else {
		return nil
	}
}

func UpdateTaskIDByTaskID(ctx context.Context, toTaskID string, byTaskID string) *biz_error.BizError {
	result := db.Model(&Task{}).
		Where("id = ?", byTaskID).
		Update("id", toTaskID)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

func UpdateTaskStatusByStatus(ctx context.Context, toStatus string, byStatus string) *biz_error.BizError {
	result := db.Model(&Task{}).
		Where("status = ?", byStatus).
		Update("status", toStatus)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

func UpdateTaskParentByParent(ctx context.Context, toParent string, byParent string) *biz_error.BizError {
	result := db.Model(&Task{}).
		Where("parent = ?", byParent).
		Update("parent", toParent)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

func SelectTaskByID(ctx context.Context, id string) (*Task, *biz_error.BizError) {
	var task Task
	// NOTE 这种Where写法当id不存在时，返回了第一个不符合where条件的记录。。
	// result := db.Where(&Task{ID: id}).First(&task)
	result := db.Where("id = ?", id).First(&task)
	if result.Error != nil {
		if result.Error.Error() == "record not found" {
			return nil, nil
		}
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected != 1 {
		return nil, biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_ROWS_AFFECTED, biz_error.BizErrorExtra{
			TaskID:       &id,
			RowsAffected: &result.RowsAffected,
		})
	}
	return &task, nil
}

func SelectTaskByLabel(ctx context.Context, label string) (*Task, *biz_error.BizError) {
	var task Task
	// NOTE 这种Where写法当id不存在时，返回了第一个不符合where条件的记录。。
	// result := db.Where(&Task{ID: id}).First(&task)
	result := db.Where("label = ?", label).First(&task)
	if result.Error != nil {
		if result.Error.Error() == "record not found" {
			return nil, nil
		}
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected != 1 {
		return nil, biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_ROWS_AFFECTED, biz_error.BizErrorExtra{
			TaskID:       &label,
			RowsAffected: &result.RowsAffected,
		})
	}
	return &task, nil
}

func SelectTaskListByParent(parentID string) ([]*Task, *biz_error.BizError) {
	var tasks []*Task
	result := db.Where("parent = ?", parentID).
		Order("id asc").
		Find(&tasks)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected != int64(len(tasks)) {
		s := fmt.Sprintf("parent[%s] result.RowsAffected[%d] != len[%d]", parentID, result.RowsAffected, len(tasks))
		return nil, biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_ROWS_AFFECTED, biz_error.BizErrorExtra{
			Message: &s,
		})
	}
	return tasks, nil
}

func SelectTaskListByIDs(ctx context.Context, ids []string) ([]*Task, *biz_error.BizError) {
	var tasks []*Task
	result := db.Where("id in ?", ids).
		Find(&tasks)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected != int64(len(tasks)) {
		s := fmt.Sprintf("ids[%s] result.RowsAffected[%d] != len[%d]", ids, result.RowsAffected, len(tasks))
		return nil, biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_ROWS_AFFECTED, biz_error.BizErrorExtra{
			Message: &s,
		})
	}
	return tasks, nil
}

func SelectTaskListByStatus(status string) ([]*Task, *biz_error.BizError) {
	var tasks []*Task
	result := db.Where("status = ?", status).
		Find(&tasks)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected != int64(len(tasks)) {
		s := fmt.Sprintf("status[%s] result.RowsAffected[%d] != len[%d]", status, result.RowsAffected, len(tasks))
		return nil, biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_ROWS_AFFECTED, biz_error.BizErrorExtra{
			Message: &s,
		})
	}
	return tasks, nil
}

func SelectTaskList(offset, limit int64) ([]*Task, error) {
	var tasks []*Task
	result := db.Limit(int(limit)).
		Offset(int(offset)).
		Order("created_at desc").
		Find(&tasks)
	if result.Error != nil {
		return nil, wErr.Wrap(result.Error, "")
	}
	if result.RowsAffected != int64(len(tasks)) {
		return nil, wErr.Wrap(fmt.Errorf("result.RowsAffected[%d] != len[%d]", result.RowsAffected, len(tasks)), "")
	}
	return tasks, nil
}

func SearchTasksByKeyword(ctx context.Context, keyword string, limit int) ([]*Task, *biz_error.BizError) {
	var tasks []*Task
	result := db.Where("title LIKE ? OR description LIKE ?", "%"+keyword+"%", "%"+keyword+"%").
		Limit(limit).
		Order("updateTime DESC").
		Find(&tasks)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	return tasks, nil
}
