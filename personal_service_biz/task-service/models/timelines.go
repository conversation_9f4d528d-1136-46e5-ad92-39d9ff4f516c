package models

import (
	"context"
	"task-service/biz_error"
	"time"

	"gorm.io/gorm"
)

type Timelines struct {
	ID          int64          `gorm:"column:id"`
	TaskID      string         `gorm:"column:taskID"`
	StartTime   time.Time      `gorm:"column:startTime"`
	EndTime     *time.Time     `gorm:"column:endTime"`
	CoverTaskID string         `gorm:"column:coverTaskID"`
	CreateTime  time.Time      `gorm:"column:createTime"`
	UpdateTime  time.Time      `gorm:"column:updateTime"`
	DeleteTime  gorm.DeletedAt `gorm:"column:deleteTime"`
}

func (Timelines) TableName() string {
	return "timelines"
}

func CreateTimeline(ctx context.Context, timelines *Timelines) *biz_error.BizError {
	if result := db.Create(timelines); result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	} else {
		return nil
	}
}

func BatchCreateTimelines(ctx context.Context, timelines []*Timelines) *biz_error.BizError {
	if result := db.Create(timelines); result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	} else {
		return nil
	}
}

func SaveTimeline(ctx context.Context, timeline *Timelines) *biz_error.BizError {
	if result := db.Save(timeline); result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	} else {
		return nil
	}
}

func SelectTimelinesListByStartTimeDesc(ctx context.Context, limit *int) ([]*Timelines, *biz_error.BizError) {
	var timelines []*Timelines
	query := db.Order("startTime DESC")
	if limit != nil {
		query = query.Limit(*limit)
	}
	result := query.Find(&timelines)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected != int64(len(timelines)) {
		return nil, biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_ROWS_AFFECTED, biz_error.BizErrorExtra{
			RowsAffected: &result.RowsAffected,
		})
	}
	return timelines, nil
}

func SelectTimelinesList(ctx context.Context, limit *int) ([]*Timelines, *biz_error.BizError) {
	var timelines []*Timelines
	// endTime也要参与排序，因为存在零宽度区间，startTime可能多记录相等
	// result := db.Order("startTime, endTime asc").
	// 但是endTime可能为NULL，要表示最大。使用-endTime DESC
	query := db.Order("startTime ASC, -endTime DESC")
	if limit != nil {
		query = query.Limit(*limit)
	}
	result := query.Find(&timelines)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected != int64(len(timelines)) {
		return nil, biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_ROWS_AFFECTED, biz_error.BizErrorExtra{
			RowsAffected: &result.RowsAffected,
		})
	}
	return timelines, nil
}

func UpdateTimelinesTaskIDByTaskID(ctx context.Context, toTaskID string, byTaskID string) *biz_error.BizError {
	result := db.Model(&Timelines{}).
		Where("taskID = ?", byTaskID).
		Update("taskID", toTaskID)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	return nil
}

func DeleteTimelinesByTaskID(ctx context.Context, timelines []*Timelines, taskID string) *biz_error.BizError {
	ids := []int64{}
	for _, t := range timelines {
		ids = append(ids, t.ID)
	}
	result := db.Model(&Timelines{}).Where("id in ?", ids).Update("coverTaskID", taskID)
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}

	result = db.Where("id in ?", ids).Delete(&Timelines{})
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected != int64(len(timelines)) {
		return biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_ROWS_AFFECTED, biz_error.BizErrorExtra{
			RowsAffected: &result.RowsAffected,
		})
	}
	return nil
}

func HardDeleteTimelinesByTaskID(ctx context.Context, taskID string) *biz_error.BizError {
	result := db.Where("taskID = ? and deleteTime is not null", taskID).
		Unscoped().Delete(&Timelines{})
	if result.Error != nil {
		return biz_error.NewBizErrorByError(result.Error)
	}

	return nil
}

func SelectTimelinesContains(ctx context.Context, time time.Time) ([]*Timelines, *biz_error.BizError) {
	var timelines []*Timelines
	// 这里边界点不要包含，交由上层逻辑处理各种边界case，否则这里的含义case将变得复杂。尤其是包含边界点后，会有很多零宽度的区间
	result := db.Where("(startTime < ?) and (endTime > ? or endTime is null)", time, time).
		Find(&timelines)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected != int64(len(timelines)) {
		return nil, biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_ROWS_AFFECTED, biz_error.BizErrorExtra{
			RowsAffected: &result.RowsAffected,
		})
	}
	return timelines, nil
}

func SelectTimelinesGreaterThan(ctx context.Context, time time.Time) ([]*Timelines, *biz_error.BizError) {
	var timelines []*Timelines
	// 这里加上endTime，确保不对零宽度的timeline进行处理。
	// result := db.Where("(startTime >= ?) and (endTime > ? or endTime is null)", time, time).

	// 这里endTime不能用>入参time，而应该用endTime > startTime，否则依旧可能零宽度。
	// result := db.Where("(startTime >= ?) and (endTime > startTime or endTime is null)", time).

	// 这里如果通过endTime > startTime来过滤零宽度，那么会导致边界点之外的其他零宽度区间，也被过滤了。
	// 而这里实际上需要的是：保留边界点的零宽度，同时后续的零宽度就不管了。所以其实一开始的条件是符合要求的：即不会获取到边界点的零宽度区间，也会涵盖后续的零宽度区间
	result := db.Where("(startTime >= ?) and (endTime > ? or endTime is null)", time, time).
		Find(&timelines)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	if result.RowsAffected != int64(len(timelines)) {
		return nil, biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_ROWS_AFFECTED, biz_error.BizErrorExtra{
			RowsAffected: &result.RowsAffected,
		})
	}
	return timelines, nil
}

func SelectTimelinesBetween(ctx context.Context, startTime, endTime time.Time) ([]*Timelines, *biz_error.BizError) {
	var timelines []*Timelines
	// 此处不同于SelectTimelinesGreaterThan里的情况，要考虑包含中间的零宽度区间，同时不处理边界的零宽度区间，需要让endTime != 入参startTime，且startTime != 入参endTime
	result := db.Where("startTime >= ? and endTime <= ? and startTime != ? and endTime != ?", startTime, endTime, endTime, startTime).
		Find(&timelines)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	return timelines, nil
}

func SelectTimelinesByTaskID(ctx context.Context, taskID string) ([]*Timelines, *biz_error.BizError) {
	var timelines []*Timelines
	result := db.Where("taskID = ?", taskID).
		Order("startTime ASC, -endTime DESC").
		Find(&timelines)
	if result.Error != nil {
		return nil, biz_error.NewBizErrorByError(result.Error)
	}
	return timelines, nil
}
