package mq

import (
	"context"
	"favorites-service/config"

	wErr "github.com/pkg/errors"
	amqp "github.com/rabbitmq/amqp091-go"
	"github.com/sirupsen/logrus"
)

type QueueListener func(string) error

var mqConn *amqp.Connection
var mqChannel *amqp.Channel

func initConn() error {
	logger := logrus.New()

	var err error
	mqConn, err = amqp.Dial(config.GetAmqpUrl())
	// defer mqConn.Close()
	if err != nil {
		logger.Errorf("InitRabbitMq error[%+v]", err)
		return wErr.Wrap(err, "")
	}
	return nil
}

func initChannel() error {
	logger := logrus.New()

	var err error
	mqChannel, err = mqConn.Channel()
	// defer ch.Close()
	if err != nil {
		logger.Errorf("conn.Channel error[%+v]", err)
		return wErr.Wrap(err, "")
	}
	return nil
}

func declareQueue(queueName string) error {
	logger := logrus.New()

	q, err := mqChannel.QueueDeclare(
		queueName, // name
		false,     // durable
		false,     // delete when unused
		false,     // exclusive
		false,     // no-wait
		nil,       // arguments
	)
	if err != nil {
		logger.Errorf("ch.QueueDeclare q[%+v] error[%+v]", q, err)
		return wErr.Wrap(err, "")
	}
	return nil
}

func bindQueueListener(queueName string, callback QueueListener) error {
	logger := logrus.New()

	go func() {
		// 获取接收消息的Delivery通道
		msgs, err := mqChannel.Consume(
			queueName, // queue
			"",        // consumer
			true,      // auto-ack
			false,     // exclusive
			false,     // no-local
			false,     // no-wait
			nil,       // args
		)
		if err != nil {
			logger.Errorf("Consume error[%+v]", err)
		}
		for d := range msgs {
			// logger.Infof("Received a message: %s", d.Body)
			innerErr := callback(string(d.Body))
			if innerErr != nil {
				logger.Error(innerErr)
			}
		}
		// waiting forever
		logger.Errorf("Oops")
	}()

	return nil
}

func InitRabbitMq(ctx context.Context) error {
	logger := logrus.New()

	if err := initConn(); err != nil {
		return err
	}

	if err := initChannel(); err != nil {
		return err
	}

	qList := []string{
		"favorites_pull_resource_bilibili_start",
		"favorites_pull_resource_wx_start",
		"favorites_pull_resource_status",
	}
	for _, queueName := range qList {
		if err := declareQueue(queueName); err != nil {
			return err
		}
	}

	if err := bindQueueListener(
		"favorites_pull_resource_status",
		ReceivePullResourceStatusMsg,
	); err != nil {
		return err
	}
	logger.Infof("InitRabbitMq Success")

	// SendPullResourceStatusMsg()
	// time.Sleep(3 * time.Second)
	// SendPullResourceStatusMsg()
	return nil
}
