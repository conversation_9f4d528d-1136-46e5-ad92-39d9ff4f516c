package mq

import (
	"encoding/json"
	"favorites-service/common"
	"favorites-service/models"

	wErr "github.com/pkg/errors"
	amqp "github.com/rabbitmq/amqp091-go"
	"github.com/sirupsen/logrus"
)

type PullResourceStartMsg struct {
	CollectRecordID int64
	Url             string
	SavePath        string
	IsMultiple      bool
}

type PullResourceStatusMsg struct {
	CollectRecordID int64
	StatusCode      int64
	StatusMessage   string
	Duration        int64
	DurationDetails *string
	Title           *string
	FilesStatus     *string
	FilesNum        *int64
	FilesMissingIds *string
}

func ReceivePullResourceStatusMsg(body string) error {
	logger := logrus.New()
	logger.Infof("ReceivePullResourceStatusMsg body %s", body)
	ret := PullResourceStatusMsg{}
	if err := json.Unmarshal([]byte(body), &ret); err != nil {
		logger.Errorf("err[%+v]", err)
		return err
	}
	record, err := models.SelectCollectRecordByID(ret.CollectRecordID)
	if err != nil {
		logger.Errorf("err[%+v]", err)
		return err
	}

	if record.DurationDetails == nil || *record.DurationDetails == "" {
		d := "{}"
		record.DurationDetails = &d
	}

	if ret.DurationDetails != nil {
		dd := map[string]int{}
		if err := json.Unmarshal([]byte(*ret.DurationDetails), &dd); err != nil {
			logger.Errorf("err[%+v]", err)
		} else {
			detail := map[string]int{}
			if e := json.Unmarshal([]byte(*record.DurationDetails), &detail); e != nil {
				logger.Errorf("err[%+v]", e)
			} else {
				// 只更新分阶段赋值的耗时
				for k, v := range dd {
					detail[k] = v
				}
				b, _ := json.Marshal(detail)
				s := string(b)
				record.DurationDetails = &s
			}
		}
	}

	record.StatusCode = ret.StatusCode
	record.StatusMessage = ret.StatusMessage
	record.Duration = ret.Duration

	logger.Infof("record.Title[%+v] ret.Title[%+v]",
		common.StrPtr2Str(record.Title),
		common.StrPtr2Str(ret.Title))
	if ret.Title != nil && record.Title == nil {
		record.Title = ret.Title
	}
	if ret.FilesStatus != nil {
		record.FilesStatus = ret.FilesStatus
	}
	if ret.FilesNum != nil {
		record.FilesNum = ret.FilesNum
	}
	if ret.FilesMissingIds != nil {
		record.FilesMissingIds = ret.FilesMissingIds
	}

	models.SaveCollectRecord(record)
	return nil
}

// 测试用
// func SendPullResourceStatusMsg() {
// 	logger := logrus.New()
//
// 	//发送数据
// 	msg := "hello 666"
// 	if err := mqChannel.Publish("", "favorites_pull_resource_status", false, false, amqp.Publishing{
// 		ContentType: "text/plain",
// 		Body:        []byte(msg),
// 	}); err != nil {
// 		logger.Errorf("Publish Err =", err)
// 		return
// 	}
// 	logger.Infof("Send msg ok, msg =", msg)
// }

func SendPullResourceTaskMsg(recordID, collectType int64, origin, savePath string) error {
	logger := logrus.New()

	//发送数据
	msg := PullResourceStartMsg{
		CollectRecordID: recordID,
		Url:             origin,
		SavePath:        savePath,
		IsMultiple:      true,
	}
	jsonMsg, err := json.Marshal(msg)
	if err != nil {
		return wErr.Wrap(err, "")
	}

	// 1xxx表示视频
	// 2xxx表示网页
	queue := ""
	if collectType == 1001 {
		// b站视频
		queue = "favorites_pull_resource_bilibili_start"
	} else if collectType == 2001 {
		// wx文章
		queue = "favorites_pull_resource_wx_start"
	}

	if err := mqChannel.Publish("", queue, false, false, amqp.Publishing{
		ContentType: "text/plain",
		Body:        []byte(jsonMsg),
	}); err != nil {
		return wErr.Wrap(err, "")
	}
	logger.Infof("Send msg ok, msg = %s", jsonMsg)
	return nil
}
