#!/bin/bash
container_name=testmock-mysql
db_root_pass=taskadminpass

sudo docker rm -f ${container_name}

sudo docker run -d --name ${container_name} \
    -e MYSQL_PASS=${db_root_pass} \
    -e ON_CREATE_DB="db_task" \
    -p 23456:3306 \
    tutum/mysql
# 参考【项目】favorites里面adb获取进程状态的自旋锁，取代盲目的sleep 20s等待
echo "waiting for mysqld start..."
check_tag="Version:.*port: 3306"
check_log=$(sudo docker logs ${container_name} 2>&1 | grep "${check_tag}")
while [[ ${check_log} == "" ]];do
  sleep 0.5
  check_log=$(sudo docker logs ${container_name} 2>&1 | grep "${check_tag}")
done
echo "mysqld start success"
