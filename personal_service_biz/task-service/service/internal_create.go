package service

import (
	"context"
	"task-service/biz_error"
	"task-service/common"
	"task-service/models"
	"time"
)

/*
 * 创建parent的叶子结点
 */
func CreateSubTask(ctx context.Context,
	parent *models.Task,
	forceID *string,
	title string,
	description *string,
	ddl *string,
	status string,
	priority int64) (*models.Task, *biz_error.BizError) {

	var err *biz_error.BizError

	// 生成taskID
	var taskID string
	if forceID != nil && *forceID != "" {
		taskID = *forceID
	} else {
		taskID, err = generateSubTaskID(ctx, parent.ID)
		if err != nil {
			return nil, err
		}
	}

	desc := ""
	if description != nil && *description != "" {
		desc = *description
	}

	var ddlTime *time.Time
	if ddl != nil && *ddl != "" {
		var e error
		ddlTime, e = common.ConvertFormatTime(*ddl)
		if e != nil {
			return nil, biz_error.NewBizError(biz_error.ERR_WRONG_FORMAT_DDL)
		}
	}

	// logger.Infof("req: %+v", req)
	now := time.Now()
	task := models.Task{
		ID:          taskID,
		Level:       parent.Level + 1,
		Parent:      parent.ID,
		Title:       title,
		CategoryID:  nil,
		IsMeta:      false,
		Status:      status,
		Description: desc,
		Priority:    priority,
		Quadrant:    0,
		DDL:         ddlTime,
		CreateTime:  now,
		UpdateTime:  now,
	}

	err = CreateTask(ctx, &task)
	if err != nil {
		return nil, err
	}
	return &task, nil
}
