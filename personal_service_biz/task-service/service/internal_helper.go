package service

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"strconv"
	"task-service/biz_error"
	"task-service/common"
	"task-service/models"
	taskGen "thrift-common/gen-go/task"

	"github.com/sirupsen/logrus"
)

// 生成删除域的taskID
func generateDeletedTaskID(ctx context.Context, taskID string) string {
	random := rand.Intn(99999999)
	// 09开头表示删除域的taskID，外加随机值，避免二次删除重复taskID依旧会主键冲突。随机值的空间范围大一些
	return fmt.Sprintf("09%08d%s", random, taskID)
}

// 生成最大继承子节点的任务ID
func generateElderSubTaskID(ctx context.Context, parent string) string {
	elderSubTaskID := fmt.Sprintf("%s00", parent)
	return elderSubTaskID
}

// 直接从0分配生成
func newSubTaskID(ctx context.Context, parent string, index int) (string, *biz_error.BizError) {
	if index > 99 {
		taskID := fmt.Sprintf("%s#%d", parent, index)
		return "", biz_error.NewBizErrorWithExtra(biz_error.ERR_REACH_MAX_SUB_TASK_ID, biz_error.BizErrorExtra{
			TaskID: &taskID,
		})
	}
	taskID := fmt.Sprintf("%s%02d", parent, index)
	return taskID, nil
}

// 从第1位开始往后扫描找空位
func generateSubTaskIDByScanFirst(ctx context.Context, parent string, tasks []*models.Task) (string, *biz_error.BizError) {
	logger := logrus.New()
	taskID := ""
	taskIDMap := map[string]bool{}
	for _, t := range tasks {
		taskIDMap[t.ID] = true
	}

	for i := 1; i <= 99; i++ {
		subID := fmt.Sprintf("%s%02d", parent, i)
		if !taskIDMap[subID] {
			taskID = subID
			break
		}
	}
	if taskID == "" {
		subID := fmt.Sprintf("%s%d", parent, 99)
		return "", biz_error.NewBizErrorWithExtra(biz_error.ERR_REACH_MAX_SUB_TASK_ID, biz_error.BizErrorExtra{
			TaskID: &subID,
		})
	}
	logger.Infof("generate %s => %s", parent, taskID)
	return taskID, nil
}

func checkTaskIDLegal(parent, dstTaskID string) bool {
	// 刚好差2位，或者level1的task只差了1位，刚好长度为2
	if len(dstTaskID)-len(parent) == 2 || len(parent) == 1 && len(dstTaskID) == 2 {
		return true
	}
	return false
}

// 在最后一个符合规范的task后加1位
func generateSubTaskIDByAppendLast(ctx context.Context, parent string, tasks []*models.Task) (string, *biz_error.BizError) {
	logger := logrus.New()
	// 默认没有子节点时，取parent+"01"
	taskID := fmt.Sprintf("%s01", parent)

	if len(tasks) == 0 {
		return taskID, nil
	}

	var lastTask *models.Task
	lastIdx := len(tasks) - 1
	for i := lastIdx; i >= 0; i-- {
		// NOTE 这里生成最大taskID的方式，有个问题就是。如果子节点的taskID不符合父节点+2位的规则，就会受到最后一个节点的干扰。
		// 因此从最后一个往回找，找到第一个符合加两位长度规则的
		iTask := tasks[i]
		if checkTaskIDLegal(parent, iTask.ID) {
			lastTask = iTask
			break
		}
	}

	if lastTask != nil {
		// 有子节点，取最大元素，加1
		maxSubID, err := strconv.Atoi(lastTask.ID)
		if err != nil {
			return "", biz_error.NewBizErrorWithExtra(biz_error.ERR_DIRTY_TASKID, biz_error.BizErrorExtra{
				TaskID: &lastTask.ID,
			})
		}
		if maxSubID%100 >= 99 {
			return "", biz_error.NewBizErrorWithExtra(biz_error.ERR_REACH_MAX_SUB_TASK_ID, biz_error.BizErrorExtra{
				TaskID: &lastTask.ID,
			})
		}
		taskID = strconv.Itoa(maxSubID + 1)
		logger.Infof("generate %s:%d => %s", parent, maxSubID, taskID)
	} // else 所有子tasks都不符合规范，就还是用parent+01的第一个值

	return taskID, nil
}

// 通过现有子task记录生成subTaskID
func generateSubTaskID(ctx context.Context, parent string) (string, *biz_error.BizError) {
	tasks, err := models.SelectTaskListByParent(parent)
	if err != nil {
		return "", err
	}

	// 默认没有子节点时，取parent+"01"
	taskID, err := generateSubTaskIDByAppendLast(ctx, parent, tasks)
	if err == nil {
		return taskID, nil
	}
	// else: 存在err
	if !err.IsCode(biz_error.ERR_REACH_MAX_SUB_TASK_ID) {
		return taskID, err
	}

	return generateSubTaskIDByScanFirst(ctx, parent, tasks)
}

func convertMarkModel2Gen(item *models.Mark) *taskGen.TaskMarkItem {
	if item == nil {
		return nil
	}
	return &taskGen.TaskMarkItem{
		ID:       strconv.FormatInt(item.ID, 10),
		Content:  item.Content,
		MarkType: item.Type,
		DocLink:  item.DocLink,
		Time:     common.FormatTime(&item.CreateTime),
	}
}

func convertModel2Gen(item *models.Task) *taskGen.TaskItem {
	if item == nil {
		return nil
	}
	task := &taskGen.TaskItem{
		ID:           item.ID,
		Level:        item.Level,
		Label:        item.Label,
		Title:        item.Title,
		Description:  item.Description,
		Parent:       item.Parent,
		Status:       item.Status,
		Priority:     item.Priority,
		DDL:          common.FormatTime(item.DDL),
		CreateTime:   common.FormatTime(&item.CreateTime),
		Subs:         []*taskGen.TaskItem{},
		TimeSpan:     []int64{math.MaxInt64, 0},
		TimeInvested: 0,
	}
	return task
}

func convertModelList2Gen(list []*models.Task) []*taskGen.TaskItem {
	ret := []*taskGen.TaskItem{}
	for _, t := range list {
		ret = append(ret, convertModel2Gen(t))
	}
	return ret
}

func convertTaskList2Map(list []*models.Task) map[string]*models.Task {
	retMap := map[string]*models.Task{}
	for _, t := range list {
		retMap[t.ID] = t
	}
	return retMap
}

func orderTasksByIds(tasks []*models.Task, ids []string) []*models.Task {
	retList := []*models.Task{}
	taskMap := convertTaskList2Map(tasks)
	for _, taskID := range ids {
		retList = append(retList, taskMap[taskID])
	}
	return retList
}

func convertMarkType(action string) string {
	if action == "comment" {
		return "评论备注"
	} else if action == "thought" {
		return "心情随想"
	} else if action == "diary" {
		return "流水记事"
	} else if action == "note" {
		return "学习笔记"
	} else if action == "gains" {
		return "感悟心得"
	} else {
		return ""
	}
}
