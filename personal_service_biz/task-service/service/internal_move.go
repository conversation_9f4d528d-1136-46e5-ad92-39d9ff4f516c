package service

import (
	"context"
	"fmt"
	"task-service/biz_error"
	"task-service/models"
	"time"

	"github.com/sirupsen/logrus"
)

func MoveTaskByCopy(ctx context.Context, srcTask *models.Task, dstTaskID string, dstLevel int64) *biz_error.BizError {
	logger := logrus.New()
	logger.Infof("MoveTaskByCopy %s => %s", srcTask.ID, dstTaskID)
	// 还要先创建子节点
	now := time.Now()
	// task应该原样从srcTask拷贝，需要更新的字段额外更新，否则后续很容易遗漏。比如现在增加references字段之后，不报错，但是遗漏导致特定case的bug。
	// task := models.Task{
	// 	ID:          dstTaskID,
	// 	Level:       dstLevel,
	// 	Parent:      srcTask.ID,
	// 	Title:       srcTask.Title,
	// 	CategoryID:  srcTask.CategoryID,
	// 	IsMeta:      srcTask.IsMeta,
	// 	Status:      srcTask.Status,
	// 	Description: srcTask.Description,
	// 	Priority:    srcTask.Priority,
	// 	Quadrant:    srcTask.Quadrant,
	// 	DDL:         srcTask.DDL,
	// 	CreateTime:  now,
	// 	UpdateTime:  now,
	// }
	task := *srcTask
	task.ID = dstTaskID
	task.Parent = srcTask.ID
	task.Level = dstLevel
	task.CreateTime = now
	task.UpdateTime = now
	// Label不能copy
	task.Label = nil
	err := models.CreateTask(&task)
	if err != nil {
		if !err.IsCode(biz_error.ERR_DUP_TASKID) {
			return err
		} // else 如果是已经存在的taskID，则继续后续任务
	}

	err = MoveTaskAtomic(ctx, srcTask.ID, dstTaskID)
	if err != nil {
		return err
	}

	// MoveTaskAtomic里面已经把srcTask的引用关系move到dstTaskID了。这里copy回来
	// => 甚至不用设计copy了。直接重新构建新的就ok了。
	return models.BuildTaskReference(ctx, srcTask)
}

// 覆盖移动。一般用于MoveTaskByCopy的反向操作。
// MoveTaskByCopy: 父节点 => 拷贝出00节点。创建了多一个task记录
// MoveTaskByCover: 00节点 => 父节点。合并后删除一个task记录
func MoveTaskByCover(ctx context.Context, elderChild, parent *models.Task) *biz_error.BizError {
	fmt.Printf("MoveTaskByCover elderChild[%+v] parentID[%+v]\n", elderChild.ID, parent.ID)
	err := MoveTaskAtomic(ctx, elderChild.ID, parent.ID)
	if err != nil {
		return err
	}

	// 还需要把现有elderChild的子tasks挂载上一级
	fmt.Printf("Step2 MoveTaskByCover elderChild[%+v] parentID[%+v]\n", elderChild.ID, parent.ID)

	// 这里不能简单地用原子性的MoveChildren的操作models挂载父节点。
	// 应该依赖于moveToParent，重新生成规范的subTaskID。否则后续还是会出现taskID冲突
	// 但是这里可以：
	// 1. 先借助把孙子都移动到爷爷。但是taskID没有重新生成
	// 2. 在硬删除了elderChild节点之后，再把刚刚挂到爷爷的所有孙子节点，再通过moveToParent挂一遍爷爷，重新生成taskID并且移动资源。
	//   => 之所以需要硬删除elderChild，是因为不把孙子先移走，就无法删除elderChild。而如果先移走孙子，那么孙子的00节点在生成新ID时，又跟elderChild冲突
	err = MoveChildren(ctx, elderChild.ID, parent.ID)
	if err != nil {
		return err
	}

	fmt.Printf("Step3 MoveTaskByCover elderChild[%+v] parentID[%+v]\n", elderChild.ID, parent.ID)
	// 最后硬删除elderChild节点。注意要走业务逻辑的HardDeleteTask，不能直接models层操作。做一下兜底校验，是否出现不允许删除的带资源情况
	err = StrongDeleteTask(ctx, elderChild)
	if err != nil {
		return err
	}

	// 最后一步，重整subTaskID
	grandChildren, err := findTaskChildren(ctx, parent)
	if err != nil {
		return err
	}

	for _, c := range grandChildren {
		taskID := c.ID
		if taskID[len(taskID)-2:] == "00" {
			// 如果是00节点。直接借助elderChild的ID
			dstTaskID := elderChild.ID
			_, err = moveToParent(ctx, c, dstTaskID, parent)
		} else {
			_, err = MoveTaskRecursively(ctx, c, parent, nil)
		}
		if err != nil {
			return err
		}
	}
	return nil
}

func moveBeforeStrongDelete(ctx context.Context, task *models.Task, dstTaskID string) (*models.Task, *biz_error.BizError) {
	srcTaskID := task.ID
	// 外部应该限制了带资源的task无法删除。这里作为兜底，避免bug导致资源错乱
	err := MoveTaskAtomic(ctx, srcTaskID, dstTaskID)
	if err != nil {
		return task, err
	}

	task.ID = dstTaskID
	err = models.UpdateTaskByTaskID(ctx, task, srcTaskID)
	return task, err
}

func moveTaskDirectly(ctx context.Context, srcTask *models.Task, dstTaskID string, parent *models.Task) (*models.Task, *biz_error.BizError) {
	// Step1
	newTask, err := MoveTaskBase(ctx, srcTask.ID, dstTaskID, parent)
	if err != nil {
		return nil, err
	}

	// Step2
	// NOTE 此处moveResource实际上依赖级联更新处理
	// 不过这里作为兜底。如果级联更新失效了，可以做兜底。另外这部分逻辑跟moveReferences封装起来了，这部分是需要的。
	err = MoveTaskAtomic(ctx, srcTask.ID, dstTaskID)
	if err != nil {
		return nil, err
	}

	// Step3
	err = MoveChildren(ctx, srcTask.ID, dstTaskID)
	if err != nil {
		return nil, err
	}

	return newTask, nil
}

func SinkParentResource(ctx context.Context, parent *models.Task) *biz_error.BizError {
	hasResource, err := checkResource(ctx, parent.ID)
	if err != nil {
		return err
	}

	if !hasResource {
		return nil
	}

	elderSubTaskID := generateElderSubTaskID(ctx, parent.ID)
	return MoveTaskByCopy(ctx, parent, elderSubTaskID, parent.Level+1)
}

func RiseElderChildResource(ctx context.Context, parent *models.Task, children []*models.Task) *biz_error.BizError {
	fmt.Printf("into RiseElderChildResource parentID[%+v]\n", parent.ID)
	if len(children) == 0 {
		// 如果一开始，父task没有resource，添加了subTask之后，是不会有00节点的，删除后，就没有了children
		return nil
	} else if len(children) > 1 {
		// 删除task之后，还有超过1个子节点，也不处理
		return nil
	} // else

	// 删除后只有一个task，得确认这个task是不是00节点，否则对应到len(children)==0的情况，加了两个task再删除一个，也会出现len == 1
	taskID := children[0].ID
	if taskID[len(taskID)-2:] != "00" {
		return nil
	} // else

	// 最后，命中了00节点的case，要来check一下内容是否完全覆盖。
	elderChild := children[0]
	// 下面这些字段做过变更，跟parent不一致，都不做上升合并处理
	if elderChild.Title != parent.Title ||
		elderChild.Description != parent.Description {
		return nil
	}

	// 如果parent，有了其他资源，是要被拒绝的，但属于异常情况。
	hasResource, err := checkResource(ctx, parent.ID)
	if err != nil {
		return err
	}

	if hasResource {
		return biz_error.NewBizErrorWithExtra(biz_error.ERR_DIRTY_PARENT_RESOURCE, biz_error.BizErrorExtra{
			TaskID: &parent.ID,
		})
	}

	fmt.Printf("final RiseElderChildResource parentID[%+v]\n", parent.ID)
	// 最终执行上升
	return MoveTaskByCover(ctx, elderChild, parent)
}

func MoveTaskRecursively(ctx context.Context, srcTask *models.Task, parent *models.Task, pDstTaskID *string) (*models.Task, *biz_error.BizError) {
	var err *biz_error.BizError
	var dstTaskID string

	if pDstTaskID != nil {
		dstTaskID = *pDstTaskID
	} else {
		dstTaskID, err = generateSubTaskID(ctx, parent.ID)
		if err != nil {
			return nil, err
		}
	}

	task, err := findTask(ctx, dstTaskID)
	if err != nil {
		return nil, err
	}
	if task != nil {
		return nil, biz_error.NewBizErrorWithExtra(biz_error.ERR_DUP_TASKID, biz_error.BizErrorExtra{
			TaskID: &dstTaskID,
		})
	}

	dstTask, err := moveToParent(ctx, srcTask, dstTaskID, parent)
	if err != nil {
		return nil, err
	}
	return dstTask, nil
}

func moveToParent(ctx context.Context, srcTask *models.Task, dstTaskID string, parent *models.Task) (*models.Task, *biz_error.BizError) {
	logger := logrus.New()
	logger.Infof("moveToParent %s => %s", srcTask.ID, dstTaskID)

	newTask, err := moveTaskDirectly(ctx, srcTask, dstTaskID, parent)
	if err != nil {
		return nil, err
	}

	// Step4
	// 查找子节点
	tasks, err := findTaskChildren(ctx, newTask)
	if err != nil {
		return nil, err
	}

	for i, t := range tasks {
		subTaskID, err := newSubTaskID(ctx, dstTaskID, i+1)
		if err != nil {
			return nil, err
		}

		_, err = moveToParent(ctx, t, subTaskID, newTask)
		if err != nil {
			return nil, err
		}
	}

	return newTask, nil
}
