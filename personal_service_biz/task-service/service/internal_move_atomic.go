package service

import (
	"context"
	"task-service/biz_error"
	"task-service/models"
)

// 单纯移动taskID。附带parent信息
func MoveTaskBase(ctx context.Context, srcTaskID, dstTaskID string, parent *models.Task) (*models.Task, *biz_error.BizError) {
	task, err := models.SelectTaskByID(ctx, srcTaskID)
	if err != nil {
		return nil, err
	}

	task.ID = dstTaskID
	task.Parent = parent.ID
	task.Level = parent.Level + 1
	err = models.UpdateTaskByTaskID(ctx, task, srcTaskID)
	if err != nil {
		return nil, err
	}
	return task, nil
}

func MoveChildren(ctx context.Context, srcTaskID, dstTaskID string) *biz_error.BizError {
	// 这里不能简单的操作models挂载父节点。应该依赖于add sub tasks时，重新生成规范的subTaskID。否则后续还是会出现taskID冲突
	err := models.UpdateTaskParentByParent(ctx, dstTaskID, srcTaskID)
	if err != nil {
		return err
	}
	return nil
}

func MoveTaskAtomic(ctx context.Context, srcTaskID, dstTaskID string) *biz_error.BizError {
	err := moveResource(ctx, srcTaskID, dstTaskID)
	if err != nil {
		return err
	}

	// 同步更新refenrences数据
	err = UpdateReferences(ctx, dstTaskID, srcTaskID)
	if err != nil {
		return err
	}
	return nil
}

func moveResource(ctx context.Context, srcTaskID, dstTaskID string) *biz_error.BizError {
	err := models.UpdateMarksTaskIDByTaskID(ctx, dstTaskID, srcTaskID)
	if err != nil {
		return err
	}

	err = models.UpdateTimelinesTaskIDByTaskID(ctx, dstTaskID, srcTaskID)
	if err != nil {
		return err
	}

	return nil
}
