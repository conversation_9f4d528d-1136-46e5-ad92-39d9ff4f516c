package service

import (
	"context"
	"task-service/biz_error"
	"task-service/models"
)

/**
 * 这里决定的是新增的task相关的reference信息更新：下沉的00节点task、或者直接变更taskID触发的
 *  => NOTE 所以这里更新的不是task自身节点，而是task所reference相关的那些task和mark
 */
func UpdateReferences(ctx context.Context, newTaskID, oldTaskID string) *biz_error.BizError {
	// Step1: 提取ByRefTasks，更新引用了当前task的相关tasks的title、description，以及references字段的refTasks
	byRefTasks, err := getByRefTasks(ctx, oldTaskID)
	if err != nil {
		return err
	}

	for _, t := range byRefTasks {
		e := updateByRefTask(ctx, t, oldTaskID, newTaskID)
		if e != nil {
			return e
		}
	}

	// Step2: 提取ByRefMarks，更新引用了当前task的相关marks的content，以及references字段的refTasks
	byRefMarks, err := getByRefMarks(ctx, oldTaskID)
	if err != nil {
		return err
	}
	for _, m := range byRefMarks {
		e := updateByRefMark(ctx, m, oldTaskID, newTaskID)
		if e != nil {
			return e
		}
	}

	// Step3: 更新
	err = models.UpdateRefs(ctx, oldTaskID, newTaskID)
	if err != nil {
		return err
	}

	return nil
}

// 新建task时检测添加
func CreateReference(ctx context.Context, task *models.Task) *biz_error.BizError {
	return models.BuildTaskReference(ctx, task)
}

func DeleteReferences(ctx context.Context, task *models.Task) *biz_error.BizError {
	return models.DeleteReferences(ctx, task.ID)
}
