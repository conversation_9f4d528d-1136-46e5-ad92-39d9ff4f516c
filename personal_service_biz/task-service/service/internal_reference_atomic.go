package service

import (
	"context"
	"fmt"
	"regexp"
	"task-service/biz_error"
	"task-service/common"
	"task-service/models"
)

func getByRefTasks(ctx context.Context, taskID string) ([]*models.Task, *biz_error.BizError) {
	byRefIDs, err := models.SelectRefsReverse(ctx, common.RefTypeTask2Task, taskID)
	if err != nil {
		return nil, err
	}

	byRefTasks, err := models.SelectTaskListByIDs(ctx, byRefIDs)
	if err != nil {
		return nil, err
	}
	return byRefTasks, nil
}

func getByRefMarks(ctx context.Context, taskID string) ([]*models.Mark, *biz_error.BizError) {
	byRefIDs, err := models.SelectRefsReverse(ctx, common.RefTypeMark2Task, taskID)
	if err != nil {
		return nil, err
	}

	byRefMarks, err := models.SelectMarkListByIDs(ctx, common.StrSlice2Int64Slice(byRefIDs))
	if err != nil {
		return nil, err
	}
	return byRefMarks, nil
}

func updateByRefTask(ctx context.Context, task *models.Task, oldTaskID, newTaskID string) *biz_error.BizError {
	re := regexp.MustCompile(fmt.Sprintf("@task_%s", oldTaskID))
	dstRef := fmt.Sprintf("@task_%s", newTaskID)

	task.Title = re.ReplaceAllString(task.Title, dstRef)
	task.Description = re.ReplaceAllString(task.Description, dstRef)

	e := models.SaveTask(ctx, task)
	if e != nil {
		return e
	}
	return nil
}

func updateByRefMark(ctx context.Context, mark *models.Mark, oldTaskID, newTaskID string) *biz_error.BizError {
	var err *biz_error.BizError
	re := regexp.MustCompile(fmt.Sprintf("@task_%s", oldTaskID))
	dstRef := fmt.Sprintf("@task_%s", newTaskID)

	mark.Content = re.ReplaceAllString(mark.Content, dstRef)
	if err != nil {
		return err
	}

	e := models.SaveMark(ctx, mark)
	if e != nil {
		return e
	}

	return nil
}
