package service

// import (
// 	"context"
// 	"os"
// 	"task-service/models"
// 	"testing"
//
// 	"github.com/sirupsen/logrus"
// 	"github.com/smartystreets/goconvey/convey"
// )
//
// func TestMain(m *testing.M) {
// 	ctx := context.Background()
// 	logger := logrus.New()
//
// 	dsn := "admin:taskadminpass@tcp(127.0.0.1:23456)/db_task?charset=utf8mb4&parseTime=True&loc=Local"
// 	err := models.InitMysql(ctx, dsn)
// 	if err != nil {
// 		logger.Errorf("initMysql failed: %+v", err)
// 		return
// 	}
//
// 	convey.SuppressConsoleStatistics()
// 	result := m.Run()
// 	convey.PrintConsoleStatistics()
// 	os.Exit(result)
// }
