package service

import (
	"context"
	"encoding/json"
	"thrift-common/gen-go/base"
	taskGen "thrift-common/gen-go/task"
)

func Wrap(data interface{}, err error) (string, error) {
	if err != nil {
		return "", err
	}
	ret, err := json.Marshal(data)
	if err != nil {
		return "", err
	}
	return string(ret), nil
}

// 定义服务
type TaskService struct {
}

func (this *TaskService) AddTask(ctx context.Context, req *taskGen.AddTaskRequest) (*taskGen.AddTaskResponse, error) {
	code := 0
	msg := "success"
	var timelines [][]string
	var marks []*taskGen.TaskMarkItem
	task, err := AddTask(ctx, req)
	if err != nil {
		code = err.Code()
		msg = err.Msg()
	}
	res := &taskGen.AddTaskResponse{
		TreeRoot:  task,
		Timelines: timelines,
		Marks:     marks,
		BaseResp: &base.BaseResponse{
			StatusCode:    int64(code),
			StatusMessage: msg,
		},
	}
	return res, nil
}

func (this *TaskService) EditTask(ctx context.Context, req *taskGen.EditTaskRequest) (*taskGen.EditTaskResponse, error) {
	code := 0
	msg := "success"
	var timelines [][]string
	var marks []*taskGen.TaskMarkItem
	task, err := EditTask(ctx, req)
	if err != nil {
		code = err.Code()
		msg = err.Msg()
	}
	res := &taskGen.EditTaskResponse{
		TreeRoot:  task,
		Timelines: timelines,
		Marks:     marks,
		BaseResp: &base.BaseResponse{
			StatusCode:    int64(code),
			StatusMessage: msg,
		},
	}
	return res, nil
}

func (this *TaskService) TaskAction(ctx context.Context, req *taskGen.TaskActionRequest) (*taskGen.TaskActionResponse, error) {
	code := 0
	msg := "success"
	var timelines [][]string
	var marks []*taskGen.TaskMarkItem

	task, err := TaskAction(ctx, req)
	if err != nil {
		code = err.Code()
		msg = err.Msg()
	} else {
		// 弱依赖
		timelines, err = GetTaskTimelines(ctx, task.ID)
		marks, err = GetTaskMarks(ctx, task.ID)
	}
	res := &taskGen.TaskActionResponse{
		TreeRoot:  task,
		Timelines: timelines,
		Marks:     marks,
		BaseResp: &base.BaseResponse{
			StatusCode:    int64(code),
			StatusMessage: msg,
		},
	}
	return res, nil
}

func (this *TaskService) TaskMv(ctx context.Context, req *taskGen.TaskMvRequest) (*taskGen.TaskMvResponse, error) {
	code := 0
	msg := "success"
	var timelines [][]string
	var marks []*taskGen.TaskMarkItem

	task, err := TaskMv(ctx, req)
	if err != nil {
		code = err.Code()
		msg = err.Msg()
	} else {
		// 弱依赖
		timelines, _ = GetTaskTimelines(ctx, task.ID)
		marks, _ = GetTaskMarks(ctx, task.ID)
	}
	res := &taskGen.TaskMvResponse{
		TreeRoot:  task,
		Timelines: timelines,
		Marks:     marks,
		BaseResp: &base.BaseResponse{
			StatusCode:    int64(code),
			StatusMessage: msg,
		},
	}
	return res, nil
}

func (this *TaskService) GetLast(ctx context.Context, req *taskGen.GetLastRequest) (*taskGen.GetLastResponse, error) {
	code := 0
	msg := "success"

	taskList, err := GetLastTasks(ctx, req)
	if err != nil {
		code = err.Code()
		msg = err.Msg()
	}

	res := &taskGen.GetLastResponse{
		Tasks: taskList,
		BaseResp: &base.BaseResponse{
			StatusCode:    int64(code),
			StatusMessage: msg,
		},
	}
	return res, nil
}

func (this *TaskService) PickTask(ctx context.Context, req *taskGen.PickTaskRequest) (*taskGen.PickTaskResponse, error) {
	code := 0
	msg := "success"

	taskList, err := PickupTask(ctx, req)
	if err != nil {
		code = err.Code()
		msg = err.Msg()
	}

	res := &taskGen.PickTaskResponse{
		Tasks: taskList,
		BaseResp: &base.BaseResponse{
			StatusCode:    int64(code),
			StatusMessage: msg,
		},
	}
	return res, nil
}

func (this *TaskService) PickDel(ctx context.Context, req *taskGen.PickDelRequest) (*taskGen.PickDelResponse, error) {
	code := 0
	msg := "success"

	taskList, err := PickupDel(ctx, req)
	if err != nil {
		code = err.Code()
		msg = err.Msg()
	}

	res := &taskGen.PickDelResponse{
		Tasks: taskList,
		BaseResp: &base.BaseResponse{
			StatusCode:    int64(code),
			StatusMessage: msg,
		},
	}
	return res, nil
}

func (this *TaskService) ListTask(ctx context.Context, req *taskGen.ListTaskRequest) (*taskGen.ListTaskResponse, error) {
	code := 0
	msg := "success"
	var timelines [][]string
	var marks []*taskGen.TaskMarkItem

	treeRoot, err := ListTask(ctx, req)
	if err != nil {
		code = err.Code()
		msg = err.Msg()
	} else {
		// 弱依赖
		timelines, err = GetTaskTimelines(ctx, treeRoot.ID)
		marks, err = GetTaskMarks(ctx, treeRoot.ID)
	}

	res := &taskGen.ListTaskResponse{
		TreeRoot:  treeRoot,
		Timelines: timelines,
		Marks:     marks,
		BaseResp: &base.BaseResponse{
			StatusCode:    int64(code),
			StatusMessage: msg,
		},
	}
	return res, nil
}

func (this *TaskService) GetTaskPage(ctx context.Context, req *taskGen.GetTaskPageRequest) (*taskGen.GetTaskPageResponse, error) {
	list, err := GetTaskPage(ctx, req.Offset, req.Count)
	if err != nil {
		return nil, err
	}
	res := &taskGen.GetTaskPageResponse{
		TaskList: list,
		Total:    int64(len(list)),
		HasMore:  false,
		BaseResp: &base.BaseResponse{
			StatusCode:    0,
			StatusMessage: "Success",
		},
	}
	return res, nil
}
func (this *TaskService) SearchTask(ctx context.Context, req *taskGen.SearchTaskRequest) (*taskGen.SearchTaskResponse, error) {
	code := 0
	msg := "success"

	// 提取搜索参数
	keyword := req.GetKeyword()
	inMarksStr := req.GetInMarks()
	markType := req.GetMarkType()
	limitStr := req.GetLimit()

	// 调用搜索逻辑
	taskList, err := GetSearchTasksFromParams(ctx, keyword, inMarksStr, markType, limitStr)
	if err != nil {
		code = err.Code()
		msg = err.Msg()
	}

	res := &taskGen.SearchTaskResponse{
		Tasks: taskList,
		BaseResp: &base.BaseResponse{
			StatusCode:    int64(code),
			StatusMessage: msg,
		},
	}
	return res, nil
}
