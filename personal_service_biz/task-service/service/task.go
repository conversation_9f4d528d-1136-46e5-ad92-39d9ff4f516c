package service

import (
	"context"
	"math"
	"strconv"
	"task-service/biz_error"
	"task-service/common"
	"task-service/models"
	"time"

	taskGen "thrift-common/gen-go/task"

	"github.com/sirupsen/logrus"
)

func init() {
}

func GetTaskMarks(ctx context.Context, taskID string) ([]*taskGen.TaskMarkItem, *biz_error.BizError) {
	marks, err := models.SelectMarksByTaskID(ctx, taskID)
	if err != nil {
		return nil, err
	}

	ret := []*taskGen.TaskMarkItem{}
	for _, m := range marks {
		ret = append(ret, convertMarkModel2Gen(m))
	}
	return ret, nil
}

func GetTaskTimelines(ctx context.Context, taskID string) ([][]string, *biz_error.BizError) {
	timelines, err := models.SelectTimelinesByTaskID(ctx, taskID)
	if err != nil {
		return nil, err
	}

	ret := [][]string{}
	for _, t := range timelines {
		ret = append(ret, formatTimeline(t))
	}
	return ret, nil
}

func GetTaskTimeCalcInfo(ctx context.Context, taskID string, filterTimeSpan []*time.Time) (int64, []int64, *biz_error.BizError) {
	timelines, err := models.SelectTimelinesByTaskID(ctx, taskID)
	if err != nil {
		return 0, nil, err
	}

	timeInvested := int64(0)
	timeSpan := []int64{math.MaxInt64, 0}

	for _, t := range timelines {
		start := t.StartTime
		end := time.Now()
		if t.EndTime != nil {
			end = *t.EndTime
		}

		if start.Unix() < timeSpan[0] {
			timeSpan[0] = start.Unix()
		}
		if end.Unix() > timeSpan[1] {
			timeSpan[1] = end.Unix()
		}

		timeInvested += calcTimeInvestedByFilterSpan(start, end, filterTimeSpan)
	}
	return timeInvested, timeSpan, nil
}

func GetLastTasks(ctx context.Context, req *taskGen.GetLastRequest) ([]*taskGen.TaskItem, *biz_error.BizError) {
	limit := 10
	if req.Number != nil && *req.Number != "" {
		l, e := strconv.Atoi(*req.Number)
		if e != nil {
			return nil, biz_error.NewBizError(biz_error.ERR_WRONG_FORMAT_NUMBER)
		}
		limit = l
	}
	tasks, err := findLastTasks(ctx, limit)
	if err != nil {
		return nil, err
	}
	return convertModelList2Gen(tasks), nil
}

func GetTaskTree(ctx context.Context, req *taskGen.ListTaskRequest) (*taskGen.TaskItem, *biz_error.BizError) {
	logger := logrus.New()
	logger.Infof("ListTask req[%+v]", req)
	// 默认根节点
	root := "0"
	if req.Root != nil && *req.Root != "" {
		root = *req.Root
		// 根节点可根据id或者label查找（方便info命令）
		parent, err := findTaskOrError(ctx, root, biz_error.ERR_NOT_FOUND_PARENT)
		if err != nil {
			return nil, err
		}
		tree, err := findTaskTree(ctx, parent)
		if err != nil {
			return nil, err
		}
		// 如果是根节点，展平下级任务
		return tree, nil

	} else if req.DefaultType == nil ||
		*req.DefaultType == "" ||
		*req.DefaultType == "root" {
		root = "0"
		// 默认root=0，构建虚假根节点
		parent := &models.Task{ID: "0", Level: -1}
		tree, err := findTaskTree(ctx, parent)
		if err != nil {
			return nil, err
			// logger.Errorf("findTaskTree err[%+v]", err)
			// return nil, "internal error"
		}
		return tree, nil

	} else if *req.DefaultType == "inprogress" {
		// 进行中任务，属于叶子节点，无须找子任务
		task, err := findInprogressLeafTask(ctx)
		if err != nil {
			return nil, err
			// logger.Errorf("findInprogressLeafTask err[%+v]", err)
			// return nil, "internal error"
		}

		tree := convertModel2Gen(task)
		return tree, nil
	} else {
		logger.Errorf("缺少逻辑分支")
		return nil, biz_error.NewBizError(biz_error.ERR_MISS_IMPLEMENT)
	}
}

func ListTask(ctx context.Context, req *taskGen.ListTaskRequest) (*taskGen.TaskItem, *biz_error.BizError) {
	logger := logrus.New()
	treeRoot, err := GetTaskTree(ctx, req)
	if err != nil {
		return nil, err
	}

	// 如果设置了filterTime，也默认开启showTime
	if req.ShowTime != nil && *req.ShowTime == "true" || req.FilterTime != nil && *req.FilterTime != "" {
		timeSpan := []*time.Time{}
		if req.FilterTime != nil && *req.FilterTime != "" {
			var e error
			timeSpan, e = common.ParseFilterTimeSpan(*req.FilterTime)
			if e != nil {
				logger.Errorf("ParseTimeSpan err[%+v]", e)
				return nil, biz_error.NewBizError(biz_error.ERR_WRONG_FORMAT_TIMESPAN)
			}
		}
		// 直接强依赖了。不然逻辑太复杂
		// 投入时间倍数关系
		investedTimeTimes := float64(1)
		if req.InvestedTimeTimes != nil && *req.InvestedTimeTimes != "" {
			floatRes, e := strconv.ParseFloat(*req.InvestedTimeTimes, 64)
			if e == nil {
				investedTimeTimes = floatRes
			}
		}
		err = WrapTaskTreeTime(ctx, treeRoot, timeSpan, investedTimeTimes)
		if err != nil {
			logger.Errorf("WrapTaskTreeTime err[%+v]", err)
			return nil, err
		}

		// // WrapTaskTreeTime成功了，才能filter
		// if req.FilterTime != nil && *req.FilterTime != "" {
		// 	timeSpan, err := common.ParseFilterTimeSpan(*req.FilterTime)
		// 	if err != nil {
		// 		logger.Errorf("ParseTimeSpan err[%+v]", err)
		// 		return nil, biz_error.NewBizError(biz_error.ERR_WRONG_FORMAT_TIMESPAN)
		// 	}

		// 	// 对于treeRoot自身，不做过滤。更可能来自info操作
		// 	e := FilterTaskTreeByTimeSpan(ctx, treeRoot, timeSpan)
		// 	if err != nil {
		// 		return nil, e
		// 	}
		// }
	}

	if req.Level != nil && *req.Level != "" {
		level, e := strconv.ParseInt(*req.Level, 10, 64)
		if e == nil {
			treeRoot = FilterTaskTreeByLevel(ctx, treeRoot, level)
		}
	}

	return treeRoot, nil
}

func FilterTaskTreeByLevel(ctx context.Context, tree *taskGen.TaskItem, level int64) *taskGen.TaskItem {
	if tree.Level >= level {
		tree.Subs = []*taskGen.TaskItem{}
		return tree
	}

	for _, c := range tree.Subs {
		FilterTaskTreeByLevel(ctx, c, level)
	}
	return tree
}

func WrapTaskTreeTime(ctx context.Context, tree *taskGen.TaskItem, filterTimeSpan []*time.Time, investedTimeTimes float64) (err *biz_error.BizError) {
	logger := logrus.New()
	if len(tree.Subs) == 0 {
		// 叶子节点，获取timelines计算
		tree.TimeInvested, tree.TimeSpan, err = GetTaskTimeCalcInfo(ctx, tree.ID, filterTimeSpan)
		tree.TimeInvested = int64(float64(tree.TimeInvested) * investedTimeTimes)
		if err != nil {
			return err
		}
	}

	// NOTE 非叶子节点，聚合子节点返回的数据。因此这里如果有树枝节点，带timelines数据，这里不考虑。并且这应该属于遗留的脏数据
	filterSubs := []*taskGen.TaskItem{}
	for _, t := range tree.Subs {
		err = WrapTaskTreeTime(ctx, t, filterTimeSpan, investedTimeTimes)
		if err != nil {
			return err
		}

		// 投入时间聚合方式：求和
		tree.TimeInvested += t.TimeInvested

		// 跨度聚合方式：左值、右值往更大边界拓宽
		if t.TimeSpan[0] < tree.TimeSpan[0] {
			// 左值更小赋值
			tree.TimeSpan[0] = t.TimeSpan[0]
		}
		if t.TimeSpan[1] > tree.TimeSpan[1] {
			// 右值更大赋值
			tree.TimeSpan[1] = t.TimeSpan[1]
		}

		createTime, e := common.ConvertFormatTime(t.CreateTime)
		if e != nil {
			// TODO 存在脏数据CreateTime
			logger.Errorf("ConvertFormatTime err[%+v]", e)
		}

		if len(filterTimeSpan) == 0 || t.TimeInvested != 0 ||
			common.IsInTimeSpan(createTime, filterTimeSpan) {
			// 要么没有过滤时间区间
			// 如果过滤时间区间，则保留有时间投入的部分
			filterSubs = append(filterSubs, t)
		}
	}
	tree.Subs = filterSubs
	return nil
}

func calcTimeInvestedByFilterSpan(start, end time.Time, filterTimeSpan []*time.Time) int64 {
	if len(filterTimeSpan) == 0 {
		return end.Unix() - start.Unix()
	}

	if end.Unix() <= filterTimeSpan[0].Unix() {
		return int64(0)
	}

	if start.Unix() >= filterTimeSpan[1].Unix() {
		return int64(0)
	}

	realInvested := end.Unix() - start.Unix()
	leftGap := filterTimeSpan[0].Unix() - start.Unix()
	if leftGap > 0 {
		realInvested = realInvested - leftGap
	}

	rightGap := end.Unix() - filterTimeSpan[1].Unix()
	if rightGap > 0 {
		realInvested = realInvested - rightGap
	}
	return realInvested
}

// func checkTimeInvestedMatch(ctx context.Context, task *taskGen.TaskItem, timeSpan []*time.Time) bool {
// 	// 没时间投入的任务
// 	if len(task.TimeSpan) == 0 {
// 		fmt.Printf("filter [%d] span=0 false\n", task.ID)
// 		return false
// 	}
//
// 	// 在区间左边
// 	if task.TimeSpan[1] <= timeSpan[0].Unix() {
// 		fmt.Printf("filter [%d] span left false\n", task.ID)
// 		return false
// 	}
//
// 	// 在区间右边
// 	if task.TimeSpan[0] >= timeSpan[1].Unix() {
// 		fmt.Printf("filter [%d] span right false\n", task.ID)
// 		return false
// 	}
//
// 	fmt.Printf("filter [%d] span true false\n", task.ID)
// 	return true
// }
//
// func FilterTaskTreeByTimeSpan(ctx context.Context, tree *taskGen.TaskItem, timeSpan []*time.Time) (err *biz_error.BizError) {
// 	if len(tree.Subs) == 0 {
// 		return nil
// 		// 叶子节点，判断自身的TimeInvested
// 	}
//
// 	filterSubs := []*taskGen.TaskItem{}
// 	for _, t := range tree.Subs {
// 		if checkTimeInvestedMatch(ctx, t, timeSpan) {
// 			// 命中则保留
// 			filterSubs = append(filterSubs, t)
// 			// 并且对其子树进行递归过滤
// 			e := FilterTaskTreeByTimeSpan(ctx, t, timeSpan)
// 			if e != nil {
// 				return e
// 			}
// 		} // else 不命中则过滤
// 	}
// 	tree.Subs = filterSubs
// 	return nil
// }

func GetTaskPage(ctx context.Context, offset, count int64) ([]*taskGen.TaskItem, error) {
	// logger := logrus.New()

	// records, err := models.SelectCollectRecordList(offset, count)
	// if err != nil {
	// 	logger.Errorf("err[%+v]", err)
	// 	return nil, err
	// }

	retList := make([]*taskGen.TaskItem, 0)
	// author := "66"
	// for i, r := range records {
	// 	retList[i] = &taskGen.TaskItem{
	// 		Origin: r.Origin,
	// 		Title: r.Title,
	// 		Author: &author,
	// 		State: r.StatusMessage,
	// 		CreateTime: common.FormatTime(r.CreatedAt),
	// 	}
	// }
	return retList, nil
}

func SearchTasks(ctx context.Context, keyword string, inMarks bool, markType string, limit int) ([]*taskGen.TaskItem, *biz_error.BizError) {
	var tasks []*models.Task
	var err *biz_error.BizError

	if inMarks {
		// 搜索标记内容
		marks, err := models.SearchMarksByKeyword(ctx, keyword, markType, limit)
		if err != nil {
			return nil, err
		}

		// 获取标记对应的任务ID
		taskIDs := make([]string, 0, len(marks))
		taskIDSet := make(map[string]bool)
		for _, mark := range marks {
			if !taskIDSet[mark.TaskID] {
				taskIDs = append(taskIDs, mark.TaskID)
				taskIDSet[mark.TaskID] = true
			}
		}

		if len(taskIDs) > 0 {
			tasks, err = models.SelectTaskListByIDs(ctx, taskIDs)
			if err != nil {
				return nil, err
			}
		}
	} else {
		// 搜索任务标题和描述
		tasks, err = models.SearchTasksByKeyword(ctx, keyword, limit)
		if err != nil {
			return nil, err
		}
	}

	return convertModelList2Gen(tasks), nil
}
