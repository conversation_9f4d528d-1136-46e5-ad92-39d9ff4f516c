package service

import (
	"context"
	"fmt"
	"task-service/biz_error"
	"task-service/common"
	"task-service/models"
	taskGen "thrift-common/gen-go/task"

	"github.com/sirupsen/logrus"
)

func TaskMark(ctx context.Context, req *taskGen.TaskActionRequest, task *models.Task) *biz_error.BizError {
	markType := convertMarkType(req.Action)
	if markType == "" {
		return biz_error.NewBizError(biz_error.ERR_WRONG_ACTION)
	}
	if req.Content == nil || *req.Content == "" {
		return biz_error.NewBizError(biz_error.ERR_MISS_ARG_CONTENT)
	}

	return createMark(ctx, task.ID, markType, *req.Content, nil, req.MarkTime)
}

func TaskFinish(ctx context.Context, req *taskGen.TaskActionRequest, task *models.Task) *biz_error.BizError {
	if task.Status == string(common.TaskStatusInprogress) {
		// 还是应该禁止finish当前任务。否则会出现status为已完成，endTime为null的情况，这样不方便找出实际在计时的任务。如果endTime也结束。那么就会出现时间的不连续
		return biz_error.NewBizErrorWithExtra(biz_error.ERR_NOT_PERMIT_FINISH_INPROGRESS, biz_error.BizErrorExtra{
			TaskID: &task.ID,
		})
	}

	return UpdateStatusByFinishCommand(ctx, task)
}

func TaskRemove(ctx context.Context, req *taskGen.TaskActionRequest, task *models.Task) *biz_error.BizError {
	if task.Status == string(common.TaskStatusInprogress) {
		// 还是应该禁止remove当前任务。否则会出现status为已删除，endTime为null的情况，这样不方便找出实际在计时的任务。如果endTime也结束。那么就会出现时间的不连续
		return biz_error.NewBizErrorWithExtra(biz_error.ERR_NOT_PERMIT_REMOVE_INPROGRESS, biz_error.BizErrorExtra{
			TaskID: &task.ID,
		})
	}

	if req.Force != nil && *req.Force == "true" {
		return StrongDeleteTask(ctx, task)
	} else {
		return WeakDeleteTask(ctx, task)
	}
}

func TaskLink(ctx context.Context, req *taskGen.TaskActionRequest, task *models.Task) *biz_error.BizError {
	if req.LabelName == nil {
		return biz_error.NewBizError(biz_error.ERR_MISS_ARG_NEW_LABEL_NAME)
	}

	if *req.LabelName == "" {
		req.LabelName = nil
	} else {
		// 检测冲突
		labelTask, err := findTaskByLabel(ctx, *req.LabelName)
		if err != nil {
			return err
		}

		if labelTask != nil {
			return biz_error.NewBizErrorWithExtra(biz_error.ERR_DUPLICATED_LABLE_NAME, biz_error.BizErrorExtra{
				TaskID: &labelTask.ID,
			})
		}
	}

	task.Label = req.LabelName
	return models.SaveTask(ctx, task)
}

func TaskAction(ctx context.Context, req *taskGen.TaskActionRequest) (*taskGen.TaskItem, *biz_error.BizError) {
	logger := logrus.New()
	var err *biz_error.BizError
	var task *models.Task

	// Step1: 先校验taskID的合法性
	if req.IDLabel != nil && *req.IDLabel != "" {
		task, err = findTaskOrError(ctx, *req.IDLabel, biz_error.ERR_NOT_FOUND_TASK)
		if err != nil {
			return nil, err
		}

		if req.MustLeaf == "true" {
			isLeaf, err := checkIsLeafTask(ctx, task.ID)
			if err != nil {
				return nil, err
			}

			// 非叶子结点
			if !isLeaf {
				s := fmt.Sprintf("TaskAction[%s] Not Permit on NonLeaf Task[%s]", req.Action, task.ID)
				return nil, biz_error.NewBizErrorWithExtra(biz_error.ERR_NOT_PERMIT_NONLEAf, biz_error.BizErrorExtra{
					Message: &s,
				})
			}
		}
	} else {
		// 需要指定id的action类型
		if req.Action == "start" || req.Action == "finish" || req.Action == "remove" || req.Action == "link" || req.Action == "regen" {
			// s := "Miss taskID or taskLabel"
			return nil, biz_error.NewBizError(biz_error.ERR_MISS_ARG_TASK_LABEL)
		} else {
			// 其他action允许不指定IDLabel，且没有指定。默认使用进行中的任务
			// 进行中任务，属于叶子节点，无须找子任务
			task, err = findInprogressLeafTask(ctx)
			if err != nil {
				return nil, err
				// logger.Errorf("findInprogressLeafTask err[%+v]", err)
				// return nil, "internal error"
			}
		}
	}
	logger.Infof("req[%+v]", req)

	if req.Action == "comment" ||
		req.Action == "thought" ||
		req.Action == "diary" ||
		req.Action == "note" ||
		req.Action == "gains" {
		err := TaskMark(ctx, req, task)
		return convertModel2Gen(task), err
	} else if req.Action == "start" {
		err := TaskStart(ctx, req, task)
		return convertModel2Gen(task), err
	} else if req.Action == "finish" {
		err := TaskFinish(ctx, req, task)
		return convertModel2Gen(task), err
	} else if req.Action == "remove" {
		err := TaskRemove(ctx, req, task)
		return convertModel2Gen(task), err
	} else if req.Action == "link" {
		err := TaskLink(ctx, req, task)
		return convertModel2Gen(task), err
	} else if req.Action == "regen" {
		taskTree, err := TaskRegen(ctx, req, task)
		return taskTree, err
	} else {
		if req.Action == "mark" {
			return nil, biz_error.NewBizErrorWithMessage("Wrong Action[mark], Do you means comment/thought/diary/note/gains?")
		}
		return nil, biz_error.NewBizError(biz_error.ERR_WRONG_ACTION)
	}
}
