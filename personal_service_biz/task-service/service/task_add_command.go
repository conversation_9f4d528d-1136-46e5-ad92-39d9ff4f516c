package service

import (
	"context"
	"fmt"
	"strconv"
	"task-service/biz_error"
	"task-service/common"
	taskGen "thrift-common/gen-go/task"
)

func AddTask(ctx context.Context, req *taskGen.AddTaskRequest) (*taskGen.TaskItem, *biz_error.BizError) {
	var err *biz_error.BizError
	// logger := logrus.New()
	parent, err := findTaskOrError(ctx, req.Parent, biz_error.ERR_NOT_FOUND_PARENT)
	if err != nil {
		return nil, err
	}

	// 默认P2
	priority := int64(2)
	if req.Priority != nil && *req.Priority != "" {
		var ierr error
		priority, ierr = strconv.ParseInt(*req.Priority, 10, 64)
		if ierr != nil {
			s := fmt.Sprintf("Wrong Format[priority=%s]", *req.Priority)
			return nil, biz_error.NewBizErrorWithExtra(biz_error.ERR_WRONG_FORMAT, biz_error.BizErrorExtra{
				Message: &s,
			})
		}
	}

	status := string(common.TaskStatusUnstarted)
	if req.ForceStatus != nil && *req.ForceStatus != "" {
		status = *req.ForceStatus
	}

	task, err := CreateSubTask(ctx, parent, req.ForceID, req.Title, req.Description, req.DDL, status, priority)
	if err != nil {
		return nil, err
	}

	err = SinkParentResource(ctx, parent)
	if err != nil {
		return nil, err
	}

	err = UpdateStatusByAddCommand(ctx, task)
	if err != nil {
		return nil, err
	}

	return convertModel2Gen(task), nil
}
