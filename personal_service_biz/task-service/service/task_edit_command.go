package service

import (
	"context"
	"task-service/biz_error"
	"task-service/common"
	"task-service/models"
	taskGen "thrift-common/gen-go/task"
)

func EditTask(ctx context.Context, req *taskGen.EditTaskRequest) (*taskGen.TaskItem, *biz_error.BizError) {
	if req.IDLabel == "" {
		return nil, biz_error.NewBizError(biz_error.ERR_MISS_ARG_TASK_LABEL)
	}

	task, err := findTaskOrError(ctx, req.IDLabel, biz_error.ERR_NOT_FOUND_TASK)
	if err != nil {
		return nil, err
	}

	if req.Title != nil && *req.Title != "" {
		task.Title = *req.Title
	}

	if req.Description != nil && *req.Description != "" {
		task.Description = *req.Description
	}

	if req.DDL != nil && *req.DDL != "" {
		ddlTime, e := common.ConvertFormatTime(*req.DDL)
		if e != nil {
			return nil, biz_error.NewBizError(biz_error.ERR_WRONG_FORMAT_DDL)
		}
		task.DDL = ddlTime
	}

	err = models.SaveTask(ctx, task)
	if err != nil {
		return nil, err
	}

	return convertModel2Gen(task), nil
}
