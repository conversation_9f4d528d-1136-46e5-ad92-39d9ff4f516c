package service

import (
	"context"
	"fmt"
	"task-service/biz_error"
	"task-service/common"
	"task-service/models"
	"time"
)

func checkResource(ctx context.Context, taskID string) (bool, *biz_error.BizError) {
	marks, err := models.SelectMarksByTaskID(ctx, taskID)
	if err != nil {
		return false, err
	}

	timelines, err := models.SelectTimelinesByTaskID(ctx, taskID)
	if err != nil {
		return false, err
	}

	refs, err := models.SelectRefsReverseAllType(ctx, taskID)
	if err != nil {
		return false, err
	}

	if len(marks) == 0 && len(timelines) == 0 && len(refs) == 0 {
		return false, nil
	}
	return true, nil
}

func createMark(ctx context.Context, taskID, markType, content string, docLink *string, markTime *string) *biz_error.BizError {
	now := time.Now()
	createTime := &now
	if markTime != nil && *markTime != "" {
		var err error
		createTime, err = common.ConvertFormatTime(*markTime)
		if err != nil {
			return biz_error.NewBizError(biz_error.ERR_WRONG_FORMAT_TIME)
		}
	}

	err := models.CreateMark(ctx, &models.Mark{
		TaskID:     taskID,
		Type:       markType,
		DocLink:    docLink,
		Content:    content,
		CreateTime: *createTime,
		UpdateTime: now,
	})
	return err
}

func CheckRiseElderChildResource(ctx context.Context, parentID string) *biz_error.BizError {
	fmt.Printf("CheckRiseElderChildResource parentID[%+v]\n", parentID)
	parent, err := findTaskOrError(ctx, parentID, biz_error.ERR_NOT_FOUND_PARENT)
	if err != nil {
		return err
	}

	children, err := findTaskChildren(ctx, parent)
	if err != nil {
		return err
	}

	err = RiseElderChildResource(ctx, parent, children)
	if err != nil {
		return err
	}

	return nil
	// NOTE 此处的递归操作，既影响每次操作的性能，又仅仅是一个兜底。因为不会出现递归式的这种task了。每个中间的task在剩下最后00节点的时候都会坍塌，把爷爷和孙子拼接到一起。
	// if len(parentID) > 2 && parentID[len(parentID)-2:] == "00" {
	// 	// 如果parent自身也是00节点，可以递归往上处理
	// 	return CheckRiseElderChildResource(ctx, parent.Parent)
	// } else {
	// 	return nil
	// }
}

// 强删除任务：db软删除
func StrongDeleteTask(ctx context.Context, task *models.Task) *biz_error.BizError {
	// 强删除(db可以是软删除可以是硬删除。这里的强删除区别于task改一下已删除的状态，继续返回在tree里面)
	// Step1: 需要校验，是否存在资源，timelines、marks、反向references等。如果存在，则不允许强删除。
	hasResource, err := checkResource(ctx, task.ID)
	if err != nil {
		return err
	}
	if hasResource {
		return biz_error.NewBizErrorWithExtra(biz_error.ERR_NOT_PERMIT_DELETE_TASK_WITH_RESOURCES, biz_error.BizErrorExtra{
			TaskID: &task.ID,
		})
	}

	// 删除正向引用
	err = DeleteReferences(ctx, task)
	if err != nil {
		return err
	}

	// 移动taskID
	removeTaskID := generateDeletedTaskID(ctx, task.ID)
	task, err = moveBeforeStrongDelete(ctx, task, removeTaskID)

	// 检验上升应该在DeleteTask之后。首先应该是弱依赖，其次依赖于删除后len(children) == 1的状态
	err = models.DeleteTask(ctx, task)
	if err != nil {
		return err
	}

	return CheckRiseElderChildResource(ctx, task.Parent)
}

// 弱删除task：只是修改状态
func WeakDeleteTask(ctx context.Context, task *models.Task) *biz_error.BizError {
	// 弱删除: 只更改状态，可以返回，但是标识状态
	task.Status = string(common.TaskStatusRemoved)
	err := models.SaveTask(ctx, task)
	if err != nil {
		return err
	}

	return UpdateParentTaskStatus(ctx, task)
}
