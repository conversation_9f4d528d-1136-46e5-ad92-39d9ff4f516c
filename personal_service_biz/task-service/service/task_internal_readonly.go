package service

import (
	"context"
	"fmt"
	"regexp"
	"sort"
	"strconv"
	"task-service/biz_error"
	"task-service/models"
	taskGen "thrift-common/gen-go/task"
)

/**
 * 该模块涉及方法全部只读
 */

func findTask(ctx context.Context, idOrLabel string) (*models.Task, *biz_error.BizError) {
	// 查找pickup任务
	r := regexp.MustCompile(`p(\d+)`)
	rs := r.FindStringSubmatch(idOrLabel)
	if len(rs) > 0 {
		// 符合规则
		position, _ := strconv.ParseInt(rs[1], 10, 64)
		task, err := findTaskByPickup(ctx, position)
		if err != nil || task != nil {
			return task, err
		}
	}

	// 查找last任务
	r = regexp.MustCompile(`l(\d+)`)
	rs = r.FindStringSubmatch(idOrLabel)
	if len(rs) > 0 {
		// 符合规则
		lastIdx, _ := strconv.Atoi(rs[1])
		task, err := findTaskByLast(ctx, lastIdx)
		if err != nil || task != nil {
			return task, err
		}
	}

	return findTaskByLabel(ctx, idOrLabel)
}

func findTaskByLast(ctx context.Context, lastIdx int) (*models.Task, *biz_error.BizError) {
	ids, err := findLastTaskIDs(ctx, lastIdx+1)
	if err != nil || len(ids) <= lastIdx {
		return nil, err
	}

	return findTaskByID(ctx, ids[lastIdx])
}

func findTaskByPickup(ctx context.Context, position int64) (*models.Task, *biz_error.BizError) {
	pickup, err := models.SelectPickupByPosition(ctx, position)
	if err != nil || pickup == nil {
		return nil, err
	}

	return findTaskByID(ctx, pickup.TaskID)
}

// 把label转成可能的组合
func splitTaskLabel(idOrLabel string) [][2]string {
	groups := [][2]string{}
	groups = append(groups, [2]string{idOrLabel})
	re := regexp.MustCompile(`^(.*?)([0-9]*)$`)
	match := re.FindStringSubmatch(idOrLabel)
	parentPart := match[1]
	subPart := match[2]
	for i := len(subPart) - 1; i >= 0; i-- {
		groups = append(groups, [2]string{
			parentPart + subPart[0:i],
			subPart[i:],
		})
	}
	fmt.Printf("label[%s] groups: %+v\n", idOrLabel, groups)

	return groups
}

func findTaskByLabel(ctx context.Context, idOrLabel string) (*models.Task, *biz_error.BizError) {
	// id尝试查找。之所以不通过正则匹配字母/数字做区分，是觉得label也可能有数字。
	// 但是如果要实现label拼接子任务序号的方式，就不得不做出规定了。
	// 规定：
	//   1. 子任务序号，一般是偶数个。特殊情况是根节点1位数到2位数。这种情况基本不用ln
	//   2. 或者不管偶数个，也支持奇数个，每位数字都拆除来尝试组合。
	//   3. ln之前都以同样规则方式查找label+子task逻辑，避免冲突。
	task, err := findTaskByID(ctx, idOrLabel)
	if err != nil || task != nil {
		return task, err
	}

	groups := splitTaskLabel(idOrLabel)
	for _, l := range groups {
		task, e := models.SelectTaskByLabel(ctx, l[0])
		if e != nil {
			return nil, e
		}
		if task != nil {
			if l[1] == "" {
				return task, nil
			} else {
				// 拼接子序列，再找id
				tryTaskId := task.ID + l[1]
				t, e := findTaskByID(ctx, tryTaskId)
				if e != nil {
					return nil, e
				}
				if t != nil {
					return t, nil
				}
			}
		}
	}
	return nil, nil
}

func findTaskByID(ctx context.Context, taskID string) (*models.Task, *biz_error.BizError) {
	return models.SelectTaskByID(ctx, taskID)
}

func findTaskOrError(ctx context.Context, idOrLabel string, errcode biz_error.BIZ_ERROR_CODE) (*models.Task, *biz_error.BizError) {
	task, err := findTask(ctx, idOrLabel)
	if err != nil {
		return nil, err
	}

	if task == nil {
		return nil, biz_error.NewBizErrorWithExtra(biz_error.ERR_NOT_FOUND_TASK, biz_error.BizErrorExtra{
			TaskID: &idOrLabel,
		})
	}
	return task, nil
}

func findTaskChildren(ctx context.Context, parent *models.Task) ([]*models.Task, *biz_error.BizError) {
	// 查找子节点
	tasks, err := models.SelectTaskListByParent(parent.ID)
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func findTaskTree(ctx context.Context, root *models.Task) (*taskGen.TaskItem, *biz_error.BizError) {
	taskView := convertModel2Gen(root)

	// 查找子节点
	tasks, err := findTaskChildren(ctx, root)
	if err != nil {
		return nil, err
	}
	for _, t := range tasks {
		child, e := findTaskTree(ctx, t)
		if e != nil {
			return nil, e
		}
		taskView.Subs = append(taskView.Subs, child)
	}

	return taskView, nil
}

func findTaskParent(ctx context.Context, task *models.Task) (*models.Task, *biz_error.BizError) {
	if task.Parent == "" {
		return nil, nil
	}

	return findTaskByID(ctx, task.Parent)
}

func findInprogressLeafTask(ctx context.Context) (*models.Task, *biz_error.BizError) {
	tasks, err := models.SelectTaskListByStatus("进行中")
	if err != nil {
		return nil, err
	}

	// 找出叶子节点
	sort.Slice(tasks, func(i, j int) bool {
		if tasks[i].Level < tasks[j].Level {
			return false
		}
		return true
	})
	if len(tasks) == 0 {
		return nil, biz_error.NewBizError(biz_error.ERR_NOT_FOUND_INPROGRESS_TASK)
	}
	return tasks[0], nil
}

func checkIsLeafTask(ctx context.Context, taskID string) (bool, *biz_error.BizError) {
	subs, err := models.SelectTaskListByParent(taskID)
	if err != nil {
		return false, err
	}

	if len(subs) > 0 {
		return false, nil
	}
	return true, nil
}

func findLastTaskIDs(ctx context.Context, limit int) ([]string, *biz_error.BizError) {
	timelines, err := models.SelectTimelinesListByStartTimeDesc(ctx, &limit)
	if err != nil {
		return nil, err
	}

	ids := []string{}
	for _, t := range timelines {
		ids = append(ids, t.TaskID)
	}
	return ids, nil
}

func findLastTasks(ctx context.Context, limit int) ([]*models.Task, *biz_error.BizError) {
	ids, err := findLastTaskIDs(ctx, limit)
	if err != nil {
		return nil, err
	}

	tasks, err := models.SelectTaskListByIDs(ctx, ids)
	if err != nil {
		return nil, err
	}
	tasks = orderTasksByIds(tasks, ids)
	return tasks, nil
}
