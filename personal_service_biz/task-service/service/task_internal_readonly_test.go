package service

import (
	"testing"

	. "github.com/smartystreets/goconvey/convey"
)

func TestSplitTaskLabel(t *testing.T) {
	Convey("should success", t, func() {
		res := splitTaskLabel("all_charcters")
		So(len(res), ShouldEqual, 1)
		So(res[0][0], ShouldEqual, "all_charcters")
		So(res[0][1], ShouldEqual, "")

		res = splitTaskLabel("all_charcters11")
		So(len(res), ShouldEqual, 3)
		So(res[0][0], ShouldEqual, "all_charcters11")
		So(res[0][1], ShouldEqual, "")
		So(res[1][0], ShouldEqual, "all_charcters1")
		So(res[1][1], ShouldEqual, "1")
		So(res[2][0], ShouldEqual, "all_charcters")
		So(res[2][1], ShouldEqual, "11")

		res = splitTaskLabel("all_charcters123")
		So(len(res), ShouldEqual, 4)
		So(res[0][0], ShouldEqual, "all_charcters123")
		So(res[0][1], ShouldEqual, "")
		So(res[1][0], ShouldEqual, "all_charcters12")
		So(res[1][1], ShouldEqual, "3")
		So(res[2][0], ShouldEqual, "all_charcters1")
		So(res[2][1], ShouldEqual, "23")
		So(res[3][0], ShouldEqual, "all_charcters")
		So(res[3][1], ShouldEqual, "123")

		res = splitTaskLabel("all_charcters123xxx")
		So(len(res), ShouldEqual, 1)
		So(res[0][0], ShouldEqual, "all_charcters123xxx")
		So(res[0][1], ShouldEqual, "")

		res = splitTaskLabel("1234all_charcters123xxx")
		So(len(res), ShouldEqual, 1)
		So(res[0][0], ShouldEqual, "1234all_charcters123xxx")
		So(res[0][1], ShouldEqual, "")
	})
}
