package service

import (
	"context"
	"fmt"
	"task-service/biz_error"
	"task-service/common"
	"task-service/models"
	"time"
)

func formatTimeline(timeline *models.Timelines) []string {
	return []string{
		common.FormatTime(&timeline.StartTime),
		common.FormatTime(timeline.EndTime),
	}
}

// func formatTimeline(timeline *models.Timelines) *biz_error.BizError {
// 	return fmt.Sprintf("%d:task=%s[%+v,%+v]",
// 		timeline.ID, timeline.TaskID,
// 		common.FormatTime(&timeline.StartTime),
// 		common.FormatTime(timeline.EndTime))
// }
//
// func formatTimelines(timelines []*models.Timelines) []string {
// 	formats := []string{}
// 	for _, t := range timelines {
// 		formats = append(formats, formatTimeline(t))
// 	}
// 	return formats
// }

func createTimeline(ctx context.Context, taskID string, startTime, endTime *time.Time) *biz_error.BizError {
	now := time.Now()
	timeline := &models.Timelines{
		TaskID:     taskID,
		StartTime:  *startTime,
		EndTime:    endTime,
		CreateTime: now,
		UpdateTime: now,
	}
	// 设置当前任务timelines
	return models.CreateTimeline(ctx, timeline)
	// if err != nil {
	// 	return err
	// 	logger.Errorf("CreateTimeline err[%+v]", err)
	// 	return "internal error"
	// }
	// return ""
}

func saveTimeline(ctx context.Context, timeline *models.Timelines) *biz_error.BizError {
	return models.SaveTimeline(ctx, timeline)
	// if err != nil {
	// 	logger.Errorf("SaveTimeline err[%+v]", err)
	// 	return "internal error"
	// }
	// return ""
}

// 抽象自cutoffTimelineByStart和参数cutoffTimelineByEnd两个方法的相同逻辑
func getTimelineForCutoff(ctx context.Context, timepoint time.Time) (*models.Timelines, *biz_error.BizError) {
	timelines, err := models.SelectTimelinesContains(ctx, timepoint)
	if err != nil {
		// logger.Errorf("SelectTimelinesContains err[%+v]", err)
		// return nil, "internal error"
		return nil, err
	}
	// 这个方法已经属于业务领域的逻辑了，所以考虑外部logic分支的划分，所以timepoint不存在导致len(timelines)为空的可能性
	if len(timelines) > 1 {
		// logger.Errorf("错误的逻辑可能性，可能数据出现问题。timepoint[%+v] timelines[%+v] len[%d]", timepoint, timelines, len(timelines))
		// return nil, "internal error"
		s := fmt.Sprintf("错误的逻辑可能性，可能数据出现问题。timepoint[%+v] timelines[%+v] len[%d]", timepoint, timelines, len(timelines))
		return nil, biz_error.NewBizErrorWithExtra(biz_error.ERR_DIRTY_DATA, biz_error.BizErrorExtra{
			Message: &s,
		})
	} else if len(timelines) == 0 {
		// 这里可能存在timepoint为边界点的情况
		return nil, nil
	}

	return timelines[0], nil
}

// 1. 通过timepoint，找出"左值<timepoint and 右值>timepoint"的timelines。
// 2. 把timeline截断到[startTime, timepoint]
// 3. 这个找出来的记录必须只有一条
func cutoffTimelineByEnd(ctx context.Context, timepoint time.Time) *biz_error.BizError {
	timeline, err := getTimelineForCutoff(ctx, timepoint)
	if err != nil {
		return err
	}

	if timeline == nil {
		// case2: timepoint落在区间边界的情况
		// 此种情况既不影响EndTime截断的操作，也不影响后续区间clear的操作。
		return nil
	}

	timeline.EndTime = &timepoint
	return models.SaveTimeline(ctx, timeline)
	// err := models.SaveTimeline(ctx, timeline)
	// if err != nil {
	// 	// logger.Errorf("SaveTimeline err[%+v]", err)
	// 	// return "internal error"
	// 	return err
	// }

	// return ""
}

func cutoffTimelineBetween(ctx context.Context, start, end time.Time) *biz_error.BizError {
	timelineStart, err := getTimelineForCutoff(ctx, start)
	if err != nil {
		return err
	}

	timelineEnd, err := getTimelineForCutoff(ctx, end)
	if err != nil {
		return err
	}

	if timelineStart == nil && timelineEnd == nil {
		limit := 1
		timelines, err := models.SelectTimelinesList(ctx, &limit)
		if err != nil {
			return err
			// logger.Errorf("SelectTimelinesList err[%+v]", err)
			// return "internal error"
		}
		if timelines[0].StartTime.Unix() > end.Unix() {
			// case4.1: startTime,endTime均小于最早记录
			s := fmt.Sprintf("导致不连续区间[%+v, %+v]", start, end)
			return biz_error.NewBizErrorWithExtra(biz_error.ERR_ILLEGAL_TIME, biz_error.BizErrorExtra{
				Message: &s,
			})
		} else {
			// case3.1: startTime和endTime都落在边界点时
			// case4.2: startTime比最早记录早，endTime落在边界点
			// 此种情况不影响后续区间clear操作。也不影响截断的情况
			return nil
		}

	} else if timelineStart == nil {
		// case2.1、case3.2: start落在边界点时
		// case4.2: startTime比最早记录早，endTime落在区间内
		// 此种情况不影响后续区间clear操作。也不影响右值EndTime截断的情况
		// 但是会退化成只需要进行左值截断。
		timelineEnd.StartTime = end
		return saveTimeline(ctx, timelineEnd)

	} else if timelineEnd == nil {
		// case2.2、case3.2: end落在边界点时
		// 此种情况不影响后续区间clear操作。也不影响左值StartTime截断的情况
		// 但是会退化成只需要进行右值截断。
		timelineStart.EndTime = &start
		return saveTimeline(ctx, timelineStart)

	} else if timelineStart.ID == timelineEnd.ID {
		// 同一条记录，满足dealLogic3Cases的case1
		err = createTimeline(ctx, timelineStart.TaskID, &end, timelineStart.EndTime)
		if err != nil {
			return err
		}
		timelineStart.EndTime = &start
		return saveTimeline(ctx, timelineStart)

	} else {
		// 不同记录，满足dealLogic3Cases的case3.3
		timelineStart.EndTime = &start
		timelineEnd.StartTime = end
		err = saveTimeline(ctx, timelineStart)
		if err != nil {
			return err
		}
		return saveTimeline(ctx, timelineEnd)
	}
}

func clearTimelinesFromStart(ctx context.Context, timepoint time.Time, taskID string) *biz_error.BizError {
	timelines, err := models.SelectTimelinesGreaterThan(ctx, timepoint)
	if err != nil {
		return err
		// logger.Errorf("SelectTimelinesGreaterThan err[%+v]", err)
		// return "internal error"
	}

	err = models.DeleteTimelinesByTaskID(ctx, timelines, taskID)
	if err != nil {
		return err
		// logger.Errorf("DeleteTimelines err[%+v]", err)
		// return "internal error"
	}

	return nil
}

func clearTimelinesBetween(ctx context.Context, start, end time.Time, taskID string) *biz_error.BizError {
	timelines, err := models.SelectTimelinesBetween(ctx, start, end)
	if err != nil {
		return err
		// logger.Errorf("SelectTimelinesBetween err[%+v]", err)
		// return "internal error"
	}

	err = models.DeleteTimelinesByTaskID(ctx, timelines, taskID)
	if err != nil {
		return err
		// logger.Errorf("DeleteTimelines err[%+v]", err)
		// return "internal error"
	}

	return nil
}

func checkTimelinesContinuous(timelines []*models.Timelines) (bool, string) {
	if len(timelines) == 0 {
		return true, "len=0"
	}
	count := len(timelines)
	for i := 0; i < count-1; i++ {
		if timelines[i].EndTime.Unix() != timelines[i+1].StartTime.Unix() {
			return false, fmt.Sprintf("endTime(%+v) != starTime(%+v)", timelines[i].EndTime, timelines[i+1].StartTime)
		}
	}
	if timelines[count-1].EndTime != nil {
		return false, "last endTime != null"
	}
	return true, ""
}
