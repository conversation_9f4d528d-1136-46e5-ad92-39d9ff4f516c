package service

import (
	"context"
	"task-service/biz_error"
	taskGen "thrift-common/gen-go/task"

	"github.com/sirupsen/logrus"
)

func TaskMv(ctx context.Context, req *taskGen.TaskMvRequest) (*taskGen.TaskItem, *biz_error.BizError) {
	logger := logrus.New()
	var err *biz_error.BizError

	// parent和dstTaskID不允许全部为空
	if (req.Parent == nil || *req.Parent == "") &&
		(req.DstTaskID == nil || *req.DstTaskID == "") {
		return nil, biz_error.NewBizError(biz_error.ERR_MISS_ARG_PARENT_OR_DST_TASK)
	}

	if req.SrcTask == "" {
		return nil, biz_error.NewBizError(biz_error.ERR_MISS_ARG_SRC_TASK)
	}

	srcTask, err := findTaskOrError(ctx, req.SrcTask, biz_error.ERR_NOT_FOUND_SRC_TASK)
	if err != nil {
		return nil, err
	}

	checkRiseParentID := ""

	parentID := srcTask.Parent
	// 没有parent参数，则默认用srcTask的parent
	if req.Parent != nil && *req.Parent != "" {
		parentID = *req.Parent

		// 如果移到其他parent，那么需要对旧的parent进行00节点上升检测
		checkRiseParentID = srcTask.Parent
	}

	var dstTaskID *string
	if req.DstTaskID != nil && *req.DstTaskID != "" {
		// 判断taskID合法性
		if !checkTaskIDLegal(parentID, *req.DstTaskID) {
			return nil, biz_error.NewBizError(biz_error.ERR_ILLEGAL_ARG_PARENT_OR_DST_TASK)
		}
		dstTaskID = req.DstTaskID
	}

	// TODO 整个树结构的资源都要递归迁移到新的任务编号。否则按照现有编号生成规则，后续肯定会有任务id冲突。
	parent, err := findTaskOrError(ctx, parentID, biz_error.ERR_NOT_FOUND_PARENT)
	if err != nil {
		return nil, err
	}

	err = SinkParentResource(ctx, parent)
	if err != nil {
		return nil, err
	}

	dstTask, err := MoveTaskRecursively(ctx, srcTask, parent, dstTaskID)
	if err != nil {
		return nil, err
	}

	// 这里是一个弱依赖的步骤
	if checkRiseParentID != "" {
		err = CheckRiseElderChildResource(ctx, checkRiseParentID)
		if err != nil {
			logger.Errorf("CheckRiseElderChildResource err[%+v]", err)
		}

		// 存在跨parent移动，先旧parent做status更新，再新task路径做更新
		err = UpdateParentTaskStatusByID(ctx, checkRiseParentID)
		if err != nil {
			logger.Errorf("UpdateParentTaskStatusByID new err[%+v]", err)
		}

		err = UpdateParentTaskStatusByID(ctx, dstTask.Parent)
		if err != nil {
			logger.Errorf("UpdateParentTaskStatusByID new err[%+v]", err)
		}
	}

	return findTaskTree(ctx, dstTask)
}
