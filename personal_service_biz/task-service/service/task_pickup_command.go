package service

import (
	"context"
	"strconv"
	"task-service/biz_error"
	"task-service/models"
	taskGen "thrift-common/gen-go/task"
	"time"
)

func removePickup(ctx context.Context, pickupList []*models.Pickup, taskID string) ([]*models.Pickup, *models.Pickup) {
	newPickupList := []*models.Pickup{}
	var exist *models.Pickup

	for _, p := range pickupList {
		if p.TaskID != taskID {
			newPickupList = append(newPickupList, p)
		} else {
			exist = p
		}
	}

	return newPickupList, exist
}

func insertPickup(ctx context.Context, pickupList []*models.Pickup, task *models.Task, position *int64) []*models.Pickup {
	now := time.Now()
	if len(pickupList) == 0 {
		// 直接第一个位置插入
		return []*models.Pickup{
			&models.Pickup{
				TaskID:     task.ID,
				Position:   0,
				CreateTime: now,
			},
		}
	}

	// 先删除taskID存在的记录。
	var exist *models.Pickup
	pickupList, exist = removePickup(ctx, pickupList, task.ID)
	if exist == nil {
		// 属于新增的task（之前不存在position）
		exist = &models.Pickup{
			TaskID:     task.ID,
			CreateTime: now,
		}
	}

	if position != nil && *position < int64(len(pickupList)) {
		pos := int(*position)
		// 有值且在中间，插在对应的位置上
		pickupList = append(pickupList[:pos+1], pickupList[pos:]...)
		exist.Position = int64(pos)
		pickupList[pos] = exist
	} else {
		// 没有指定position，加在最末尾
		exist.Position = int64(len(pickupList))
		pickupList = append(pickupList, exist)
	}

	return pickupList
}

func rePosition(ctx context.Context, pickupList []*models.Pickup) ([]*taskGen.TaskItem, *biz_error.BizError) {
	taskMap := map[string]*taskGen.TaskItem{}

	// 确认所有tasks是合法存在的task，中间没有被删除的task
	remainList := []*models.Pickup{}
	for _, p := range pickupList {
		// 如果pickup list需要展示树状，则改成findTaskTree
		t, e := findTaskByID(ctx, p.TaskID)
		if e != nil {
			return nil, e
		}
		if t == nil {
			// 被删掉的task，也从pickup中移除
			e = models.DeletePickup(ctx, p)
			if e != nil {
				return nil, e
			}
		} else {
			if _, ok := taskMap[t.ID]; ok {
				// NOTE 前面的position存在相同的重复task。应该属于异常情况的？如果是taskID冲突，说明前面处理不周到
				return nil, biz_error.NewBizError(biz_error.ERR_CONFLICT_PICKUP_TASKID)
			}

			taskMap[t.ID] = convertModel2Gen(t)
			remainList = append(remainList, p)
		}
	}

	finalList := []*taskGen.TaskItem{}
	// 重新order一下position
	for i, p := range remainList {
		// 这个判断会导致新插入的Pickup记录无法Save。干脆先全部Save一遍，反正pickup不会太多
		// if p.Position != int64(i) {
		p.Position = int64(i)
		err := models.SavePickup(ctx, p)
		if err != nil {
			return nil, err
		}
		// }

		finalList = append(finalList, taskMap[p.TaskID])
	}

	return finalList, nil
}

func PickupTask(ctx context.Context, req *taskGen.PickTaskRequest) ([]*taskGen.TaskItem, *biz_error.BizError) {
	var err *biz_error.BizError
	var position *int64
	if req.Position != nil && *req.Position != "" {
		pos, e := strconv.ParseInt(*req.Position, 10, 64)
		if e != nil {
			return nil, biz_error.NewBizError(biz_error.ERR_WRONG_FORMAT_POSITION)
		}
		position = &pos
	}

	pickupList, err := models.SelectAllPickups(ctx)
	if err != nil {
		return nil, err
	}

	// logger := logrus.New()
	if req.IDLabel != nil && *req.IDLabel != "" {
		// 插入pickup
		task, err := findTaskOrError(ctx, *req.IDLabel, biz_error.ERR_NOT_FOUND_TASK)
		if err != nil {
			return nil, err
		}

		pickupList = insertPickup(ctx, pickupList, task, position)
	}

	return rePosition(ctx, pickupList)
}

func delPickupByPosition(ctx context.Context, pickupList []*models.Pickup, position int64) ([]*models.Pickup, *biz_error.BizError) {
	newPickupList := []*models.Pickup{}
	for _, p := range pickupList {
		if p.Position == position {
			e := models.DeletePickup(ctx, p)
			if e != nil {
				return nil, e
			}
		} else {
			newPickupList = append(newPickupList, p)
		}
	}
	return newPickupList, nil
}

func PickupDel(ctx context.Context, req *taskGen.PickDelRequest) ([]*taskGen.TaskItem, *biz_error.BizError) {
	if req.Position == "" {
		return nil, biz_error.NewBizError(biz_error.ERR_MISS_ARG_POSITION)
	}

	pos, e := strconv.ParseInt(req.Position, 10, 64)
	if e != nil {
		return nil, biz_error.NewBizError(biz_error.ERR_WRONG_FORMAT_POSITION)
	}

	pickupList, err := models.SelectAllPickups(ctx)
	if err != nil {
		return nil, err
	}

	pickupList, err = delPickupByPosition(ctx, pickupList, pos)
	if err != nil {
		return nil, err
	}

	return rePosition(ctx, pickupList)
}
