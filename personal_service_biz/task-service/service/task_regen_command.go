package service

import (
	"context"
	"task-service/biz_error"
	"task-service/models"
	taskGen "thrift-common/gen-go/task"
)

func TaskRegen(ctx context.Context, req *taskGen.TaskActionRequest, task *models.Task) (*taskGen.TaskItem, *biz_error.BizError) {
	children, err := findTaskChildren(ctx, task)
	if err != nil {
		return nil, err
	}

	// 1表示存在，2表示已经移除掉
	existTask := map[string]int{}
	for _, c := range children {
		existTask[c.ID] = 1
	}

	// 按照顺序，依次重新映射子taskID
	i := 0
	genI := 0
	for i < len(children) {
		c := children[i]
		cID := c.ID
		subTaskID, err := newSubTaskID(ctx, task.ID, genI+1)
		if err != nil {
			return nil, err
		}
		genI += 1

		if subTaskID == cID {
			// 跳过
			i += 1
			continue
		}

		// 如果新生成的subTaskID存在，而且还未移除，先跳过这个subTaskID
		if existTask[subTaskID] == 1 {
			continue
		}

		_, err = moveToParent(ctx, c, subTaskID, task)
		if err != nil {
			return nil, err
		}
		existTask[cID] = 2
		i += 1
	}

	return findTaskTree(ctx, task)
}
