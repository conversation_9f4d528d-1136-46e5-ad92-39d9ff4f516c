package service

import (
	"context"
	"strconv"
	"task-service/biz_error"
	taskGen "thrift-common/gen-go/task"
)

func TaskSearch(ctx context.Context, keyword string, inMarks bool, markType string, limit int) ([]*taskGen.TaskItem, *biz_error.BizError) {
	if keyword == "" {
		return nil, biz_error.NewBizError(biz_error.ERR_MISS_ARG_CONTENT)
	}

	if limit <= 0 || limit > 100 {
		limit = 20
	}

	return SearchTasks(ctx, keyword, inMarks, markType, limit)
}

func GetSearchTasksFromParams(ctx context.Context, keyword string, inMarksStr string, markType string, limitStr string) ([]*taskGen.TaskItem, *biz_error.BizError) {
	limit := 20
	if limitStr != "" {
		l, e := strconv.Atoi(limitStr)
		if e != nil {
			return nil, biz_error.NewBizError(biz_error.ERR_WRONG_FORMAT_NUMBER)
		}
		limit = l
	}

	inMarks := false
	if inMarksStr == "true" {
		inMarks = true
	}

	return TaskSearch(ctx, keyword, inMarks, markType, limit)
}
