package service

import (
	"context"
	"fmt"
	"task-service/biz_error"
	"task-service/common"
	"task-service/models"
	taskGen "thrift-common/gen-go/task"
	"time"
)

/**
 * start命令逻辑分支非常复杂，独立模块进行梳理抽象。
 */

func getEndTime(endTime *string, now time.Time) (*time.Time, *biz_error.BizError) {
	if endTime == nil || *endTime == "" {
		return nil, nil
	}
	formatTime, err := common.ConvertFormatTime(*endTime)
	if err != nil {
		return nil, biz_error.NewBizErrorByError(err)
	}

	// startTime比当前时间还大，是不允许的
	if formatTime.Unix()-now.Unix() > 0 {
		return nil, biz_error.NewBizError(biz_error.ERR_TIME_GT_NOW)
	}

	return formatTime, nil
}

func getStartTime(startTimeOption *string, now time.Time) (*time.Time, *biz_error.BizError) {
	if startTimeOption == nil || *startTimeOption == "" {
		return &now, nil
	}
	formatTime, err := common.ConvertFormatTime(*startTimeOption)
	if err != nil {
		return nil, biz_error.NewBizErrorByError(err)
	}

	// startTime比当前时间还大，是不允许的
	if formatTime.Unix()-now.Unix() > 0 {
		return nil, biz_error.NewBizError(biz_error.ERR_TIME_GT_NOW)
	}
	return formatTime, nil
}

// 校验是否表记录为空
func isEmptyTimelineTable(ctx context.Context, now time.Time) (bool, *biz_error.BizError) {
	// models很多方法可以用来做现成判断。后续可以看看怎么合并掉其他逻辑分支
	timelines, err := models.SelectTimelinesContains(ctx, now)
	if err != nil {
		return false, err
	}
	if len(timelines) > 0 {
		return false, nil
	}
	return true, nil
}

// startTime、endTime均为空。且db有数据记录
func dealLogic1Cases(ctx context.Context, taskID string, now time.Time) *biz_error.BizError {
	// 直接把最新一条记录(符合预期应该是EndTime为NULL的记录)的EndTime截断为now
	return cutoffTimelineByEnd(ctx, now)
}

// startTime非空，endTime为空。且db有数据记录
func dealLogic2Cases(ctx context.Context, taskID string, startTime time.Time, now time.Time) *biz_error.BizError {
	// case1: startTime落在任何区间内部
	//   => 该区间的end截断到startTime
	//   => 该区间的后续区间全部清除

	// case2: startTime落在区间边界时
	//   => 落在区间的右值时，不应该影响该区间。
	//   => 落在区间的左值时，该区间及后续区间全部清除。
	//   => 还有一种落在零宽度区间，左右值相等，也不应该影响该区间。

	// case3: startTime比最早记录的左值还小
	//   => 清除全表记录。但是属于高危操作，应该有限定提醒

	// 综上几种case，其实可以合并处理，即:
	// NOTE: 这里提取的合并处理代码块，才是最终抽象成可复用的方法逻辑块的地方。通过一些参数差异化处理。
	// code1: 通过startTime，找出"左值<startTime and 右值>startTime"的timelines。
	//        这个满足了case1的第1点，用来做截断、保存操作。
	//        这个找出来的记录必须只有一条；
	//        => cutoffTimelineByEnd()
	//           :补充对len(timelines)==0的处理，覆盖case2落在边界的情况

	// code2: 通过startTime，找出"左值>=startTime"的所有timelines
	//	      这个满足了case1第2点，case2第2点，用来做清除操作。
	//        这里最好加多一个"右值>startTime"，来避免case2的第3点，即刚好落在startTime的零宽度区间，需不需要被清除。这里最好不变。
	//        这里同时也满足了case3的情况。因为不需要截断只需要clear

	// code1:
	err := cutoffTimelineByEnd(ctx, startTime)
	if err != nil {
		return err
	}

	// code2:
	return clearTimelinesFromStart(ctx, startTime, taskID)
}

// startTime、endTime均非空。且db有数据记录
func dealLogic3Cases(ctx context.Context, taskID string, startTime, endTime time.Time, now time.Time) *biz_error.BizError {
	// case1: 当[startTime, endTime]落在同一个区间的内部时
	//   => 对应区间截断成首尾两段记录保存，即覆盖一个、新增一个。最后还需要额外新增中间的区间。

	// case2：当[startTime, endTime]落在一个区间的内部边界时
	//   => 2.1 落在区间的左边界时，区间的左值start覆盖成endTime
	//   => 2.2 落在区间的右边界时，区间的右值end覆盖成startTime
	//   => 2.3 由于参数保证startTime,endTime有宽度，因此不考虑会落在零宽度区间内部的可能。
	//			NOTE: 但是这里会导致rust tool在migrate时，现有的零宽度的timelines数据都插入失败。但是考虑到这里价值很低，没必要专门针对这里做逻辑处理。

	// case3：当[startTime, endTime]落在超过一个区间范围（也可能刚好覆盖住一个）
	//   => 3.1 startTime、endTime都落在边界点时：
	//   =>   把完整覆盖住的区间，进行清除
	//   => 3.2 startTime、endTime有一个落在边界点时：
	//   =>   把完整覆盖住的区间，进行清除。
	//   =>   另外一个边界区间，根据case2来做处理
	//   => 3.3 startTime、endTime都落在不同区间内部时：
	//   =>   把完整覆盖住的区间，进行清除。
	//   =>   另外两个边界区间，根据case2来处理

	// case4：当startTime落在最早记录的start之前时
	//   => 4.1 endTime也比最早记录的start还早时：禁止这种情况，因为要保证现有记录的时间连续性
	//   => 4.2 endTime落在区间边界时：
	//   =>   把完整覆盖住的区间，进行清除
	//   => 4.3 endTime落在区间的内部时：
	//   =>   把完整覆盖住的区间，进行清除。
	//   =>   另外一个边界区间，根据case2来做处理

	// 综上几种case，合并处理代码块:
	// code1: 用前面的cutoffTimelineByEnd()其实也能覆盖此处的case1和case3.3的一半处理，即覆盖右值，截断到startTime的处理。
	//        另外再用类似的操作，提炼多一个方法cutoffTimelineByStart，用相反的条件判断来提取: "左值<endTime and 右值>endTime"的timelines，但是此部分的筛选条件是可复用的，此部分即models.SelectTimelinesContains进行复用提取。
	//        cutoffTimelineByStart同cutoffTimelineByEnd一样，也需要记录只有一条。
	//        但是考虑到两个逻辑的方法一模一样，为了避免重复代码的坑，把难理解的抽象留在里面，这里依旧保留两个形象好理解的方法。
	//
	//        但是还无法解决case1，只能处理case3.3。
	//        如果是case1，无法拆分成cutoffTimelineByStart和cutoffTimelineByEnd，这两个步骤独立进行。。因为任何一个步骤都导致，同一个timeline被损坏
	//        => 所以把SelectTimelinesContains和cutoff两个操作耦合到一个方法，是不合适的。看看适时拆分出来，应对变化。
	//        => 拆出来cutoffTimelineBetween来实现case1和case3.3
	//           :补充对len(timelines)==0的处理，覆盖case2，case3落在边界的情况

	// code2: 通过startTime，找出"左值>=startTime 且 右值<=endTime"的所有timelines
	//	      这个满足了case2、case3需要清除的情况
	//        这里最好加多一个"右值>startTime"，来避免case2的第3点，即刚好落在startTime的零宽度区间，需不需要被清除。这里最好不变。
	//        => clearTimelinesBetween
	//          :这里同时也满足了case4.2的情况。因为不需要截断只需要clear
	//          :而针对case4.3的情况，在cutoffTimelineBetween里面也cover住逻辑

	// 满足case2.3
	if endTime.Unix()-startTime.Unix() <= 0 {
		s := "错误的区间宽度:0"
		return biz_error.NewBizErrorWithExtra(biz_error.ERR_ILLEGAL_TIME, biz_error.BizErrorExtra{
			Message: &s,
		})
	}

	// code1
	err := cutoffTimelineBetween(ctx, startTime, endTime)
	if err != nil {
		return err
	}

	// code2
	return clearTimelinesBetween(ctx, startTime, endTime, taskID)
}

func TaskStart(ctx context.Context, req *taskGen.TaskActionRequest, task *models.Task) *biz_error.BizError {
	now := time.Now()
	// Step1: 前置获取通用参数 startTime, endTime
	startTime, err := getStartTime(req.StartTime, now)
	if err != nil {
		return err
	}
	// logger.Infof("req: %+v", req)
	endTime, err := getEndTime(req.EndTime, now)
	if err != nil {
		return err
	}

	// Step2: 前置获取通用参数 isEmptyTable
	isEmptyTable, err := isEmptyTimelineTable(ctx, now)
	if err != nil {
		return err
		// logger.Errorf("isEmptyTimelineTable err[%+v]", err)
		// return "internal error"
	}

	// Step3: 先前置检测一下当前数据的连续性
	allTimelines, err := models.SelectTimelinesList(ctx, nil)
	if err != nil {
		return err
		// logger.Errorf("SelectTimelinesList err[%+v]", err)
		// return "internal error"
	}
	isContinuous, msg := checkTimelinesContinuous(allTimelines)
	if !isContinuous {
		s := fmt.Sprintf("当前db timelines数据存在不连续性:%s", msg)
		return biz_error.NewBizErrorWithExtra(biz_error.ERR_DIRTY_DATA, biz_error.BizErrorExtra{
			Message: &s,
		})
	}

	taskID := task.ID

	if common.StrPtrIsEmpty(req.StartTime) && common.StrPtrIsEmpty(req.EndTime) {
		// 情况1: StartTime、EndTime均为空
		if !isEmptyTable {
			// 有数据：处理各种边界case
			// NOTE：这里的case很多，每种大case下又很多细微差别，这种就不要想着怎么复用、合并、抽象代码了，后面这种抽象会变得毫无可理解性。反而是最直白的分支、直接实现，把细微差别落实到各自分支代码去，更加直接。
			// => 所以这里不要想着去抽象、提取dealCases逻辑。直接按照daelLogicxxx去实现。
			err = dealLogic1Cases(ctx, taskID, now)
			if err != nil {
				return err
			}
		} // else 空数据: 直接新增

		err = createTimeline(ctx, taskID, &now, nil)
		if err != nil {
			return err
		}

	} else if !common.StrPtrIsEmpty(req.StartTime) && common.StrPtrIsEmpty(req.EndTime) {
		// 情况2: StartTime有值，EndTime为空
		if !isEmptyTable {
			// 有数据：处理各种边界case
			err = dealLogic2Cases(ctx, taskID, *startTime, now)
			if err != nil {
				return err
			}
		} // else 空数据: 直接新增

		err = createTimeline(ctx, taskID, startTime, nil)
		if err != nil {
			return err
		}

	} else if !common.StrPtrIsEmpty(req.StartTime) && !common.StrPtrIsEmpty(req.EndTime) {
		// 情况3: StartTime有值，EndTime也有值
		if isEmptyTable {
			// 空数据：为了保证db记录中，时间区间是从startTime开始之后不断延续至今的（时间连续性），及至少有一条最新记录的end为NULL，所以不允许这种情况。
			s := fmt.Sprintf("db表记录为空，禁止插入独立时间段区间[%s, %s]",
				common.FormatTime(startTime),
				common.FormatTime(endTime))
			return biz_error.NewBizErrorWithExtra(biz_error.ERR_ILLEGAL_TIME, biz_error.BizErrorExtra{
				Message: &s,
			})
		} else {
			// 有数据：处理各种边界case
			err = dealLogic3Cases(ctx, taskID, *startTime, *endTime, now)
			if err != nil {
				return err
			}
			err = createTimeline(ctx, taskID, startTime, endTime)
			if err != nil {
				return err
			}
		}
	} else {
		// 情况4：StartTime为空，EndTime有值
		s := fmt.Sprintf("Wrong Time Interval[%s, %s]",
			common.FormatTime(startTime),
			common.FormatTime(endTime))
		return biz_error.NewBizErrorWithExtra(biz_error.ERR_ILLEGAL_TIME, biz_error.BizErrorExtra{
			Message: &s,
		})
	}

	// 状态处理: 只有endTime为nil的情况，才需要变更任务状态
	if endTime == nil {
		return UpdateStatusByStartCommand(ctx, task)
	}

	return nil
}
