package service

//
// import (
// 	. "github.com/smartystreets/goconvey/convey"
// )
//
// func clearTimelines() error {
// 	err := models.GetDB().Exec("DELETE FROM timelines").Error
// 	if err != nil {
// 		return err
// 	}
// 	return nil
// }
//
// func mockInitialTimelines() error {
// 	ctx := context.Background()
// 	err := clearTimelines()
// 	if err != nil {
// 		return err
// 	}
//
// 	taskID := "991104"
// 	point1, _ := common.ConvertFormatTime("2022-11-01 10:00")
// 	point2, _ := common.ConvertFormatTime("2022-12-01 10:00")
// 	point3, _ := common.ConvertFormatTime("2023-01-01 10:00")
// 	point4, _ := common.ConvertFormatTime("2023-01-01 10:00")
// 	point5, _ := common.ConvertFormatTime("2023-02-01 10:00")
// 	point6, _ := common.ConvertFormatTime("2023-03-01 10:00")
// 	timelines := []*models.Timelines{
// 		&models.Timelines{ID: 1, TaskID: taskID, StartTime: *point1, EndTime: point2},
// 		&models.Timelines{ID: 2, TaskID: taskID, StartTime: *point2, EndTime: point3},
// 		&models.Timelines{ID: 3, TaskID: taskID, StartTime: *point3, EndTime: point4},
// 		&models.Timelines{ID: 4, TaskID: taskID, StartTime: *point4, EndTime: point5},
// 		&models.Timelines{ID: 5, TaskID: taskID, StartTime: *point5, EndTime: point6},
// 		&models.Timelines{ID: 6, TaskID: taskID, StartTime: *point6, EndTime: nil},
// 	}
// 	return models.BatchCreateTimelines(ctx, timelines)
// }
//
// func TestTaskStart(t *testing.T) {
// 	Convey("无初始数据组", t, func() {
// 		// 每个用例前都会执行的步骤
// 		clearTimelines()
//
// 		Convey("logic1: start/nil;end/nil", func() {
// 			ctx := context.Background()
// 			req := &taskGen.TaskActionRequest{}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 1)
// 			So(allTimelines[0].EndTime, ShouldBeNil)
// 		})
//
// 		Convey("logic2: start/not nil;end/nil", func() {
// 			ctx := context.Background()
// 			startTime := "2023-02-01 23:00"
// 			start, err := common.ConvertFormatTime(startTime)
// 			So(err, ShouldBeNil)
//
// 			req := &taskGen.TaskActionRequest{
// 				StartTime: &startTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 1)
// 			So(allTimelines[0].EndTime, ShouldBeNil)
// 			So(allTimelines[0].StartTime.Unix(), ShouldEqual, start.Unix())
// 		})
//
// 		Convey("logic3: start/not nil;end/not nil", func() {
// 			ctx := context.Background()
// 			startTime := "2023-02-01 23:00"
// 			endTime := "2023-02-02 23:00"
// 			req := &taskGen.TaskActionRequest{
// 				StartTime: &startTime,
// 				EndTime:   &endTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldNotEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 0)
// 		})
//
// 		Convey("logic4: start/nil;end/not nil", func() {
// 			ctx := context.Background()
// 			endTime := "2023-02-01 23:00"
// 			req := &taskGen.TaskActionRequest{
// 				EndTime: &endTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldNotEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 0)
// 		})
//
// 		Convey("test isEmptyTimelineTable/true", func() {
// 			ctx := context.Background()
// 			now := time.Now()
// 			result, err := isEmptyTimelineTable(ctx, now)
// 			So(err, ShouldBeNil)
// 			So(result, ShouldEqual, true)
// 		})
// 	})
//
// 	Convey("有初始数据组", t, func() {
// 		// 每个用例前都会执行的步骤
// 		mockInitialTimelines()
//
// 		Convey("logic1: start/nil;end/nil", func() {
// 			ctx := context.Background()
// 			req := &taskGen.TaskActionRequest{}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 7)
// 			So(allTimelines[6].EndTime, ShouldBeNil)
// 			So(checkTimelinesContinuous(allTimelines), ShouldEqual, true)
// 		})
//
// 		Convey("logic2/Case1: start/not nil;end/nil", func() {
// 			ctx := context.Background()
// 			startTime := "2022-12-02 23:00"
// 			start, err := common.ConvertFormatTime(startTime)
// 			So(err, ShouldBeNil)
//
// 			req := &taskGen.TaskActionRequest{
// 				StartTime: &startTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 3)
// 			So(allTimelines[2].StartTime.Unix(), ShouldEqual, start.Unix())
// 			So(allTimelines[2].EndTime, ShouldBeNil)
// 			So(checkTimelinesContinuous(allTimelines), ShouldEqual, true)
// 		})
//
// 		Convey("logic2/Case2: ", func() {
// 			ctx := context.Background()
// 			startTime := "2023-01-01 10:00"
// 			start, err := common.ConvertFormatTime(startTime)
// 			So(err, ShouldBeNil)
//
// 			req := &taskGen.TaskActionRequest{
// 				StartTime: &startTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 4)
// 			// 不影响零宽度区间
// 			So(allTimelines[2].StartTime.Unix(), ShouldEqual, start.Unix())
// 			So(allTimelines[3].StartTime.Unix(), ShouldEqual, start.Unix())
// 			So(allTimelines[3].EndTime, ShouldBeNil)
// 			So(checkTimelinesContinuous(allTimelines), ShouldEqual, true)
// 		})
//
// 		Convey("logic2/Case3: ", func() {
// 			ctx := context.Background()
// 			startTime := "2022-01-01 10:00"
// 			start, err := common.ConvertFormatTime(startTime)
// 			So(err, ShouldBeNil)
//
// 			req := &taskGen.TaskActionRequest{
// 				StartTime: &startTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 1)
// 			So(allTimelines[0].StartTime.Unix(), ShouldEqual, start.Unix())
// 			So(allTimelines[0].EndTime, ShouldBeNil)
// 		})
//
// 		Convey("logic3/Case1: start/not nil;end/not nil", func() {
// 			ctx := context.Background()
// 			startTime := "2023-02-02 23:00"
// 			start, err := common.ConvertFormatTime(startTime)
// 			So(err, ShouldBeNil)
// 			endTime := "2023-02-03 23:00"
// 			end, err := common.ConvertFormatTime(endTime)
// 			So(err, ShouldBeNil)
//
// 			req := &taskGen.TaskActionRequest{
// 				StartTime: &startTime,
// 				EndTime:   &endTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 8)
// 			So(allTimelines[4].EndTime.Unix(), ShouldEqual, start.Unix())
// 			So(allTimelines[5].EndTime.Unix(), ShouldEqual, end.Unix())
// 			So(checkTimelinesContinuous(allTimelines), ShouldEqual, true)
// 		})
//
// 		Convey("logic3/Case2.1: ", func() {
// 			ctx := context.Background()
// 			startTime := "2023-01-01 10:00"
// 			start, err := common.ConvertFormatTime(startTime)
// 			So(err, ShouldBeNil)
// 			endTime := "2023-01-03 23:00"
// 			end, err := common.ConvertFormatTime(endTime)
// 			So(err, ShouldBeNil)
//
// 			req := &taskGen.TaskActionRequest{
// 				StartTime: &startTime,
// 				EndTime:   &endTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 7)
// 			So(allTimelines[3].StartTime.Unix(), ShouldEqual, start.Unix())
// 			So(allTimelines[4].StartTime.Unix(), ShouldEqual, end.Unix())
// 			So(checkTimelinesContinuous(allTimelines), ShouldEqual, true)
// 		})
//
// 		Convey("logic3/Case2.2: ", func() {
// 			ctx := context.Background()
// 			startTime := "2022-12-29 10:00"
// 			start, err := common.ConvertFormatTime(startTime)
// 			So(err, ShouldBeNil)
// 			endTime := "2023-01-01 10:00"
// 			end, err := common.ConvertFormatTime(endTime)
// 			So(err, ShouldBeNil)
//
// 			req := &taskGen.TaskActionRequest{
// 				StartTime: &startTime,
// 				EndTime:   &endTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 7)
// 			So(allTimelines[2].StartTime.Unix(), ShouldEqual, start.Unix())
// 			So(allTimelines[3].StartTime.Unix(), ShouldEqual, end.Unix())
// 			So(checkTimelinesContinuous(allTimelines), ShouldEqual, true)
// 		})
//
// 		Convey("logic3/Case2.3: ", func() {
// 			ctx := context.Background()
// 			startTime := "2022-12-29 10:00"
// 			endTime := "2022-12-29 10:00"
//
// 			req := &taskGen.TaskActionRequest{
// 				StartTime: &startTime,
// 				EndTime:   &endTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldNotEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 6)
// 			So(checkTimelinesContinuous(allTimelines), ShouldEqual, true)
// 		})
//
// 		Convey("logic3/Case3.1: ", func() {
// 			ctx := context.Background()
// 			startTime := "2022-12-01 10:00"
// 			start, err := common.ConvertFormatTime(startTime)
// 			So(err, ShouldBeNil)
// 			endTime := "2023-02-01 10:00"
// 			end, err := common.ConvertFormatTime(endTime)
// 			So(err, ShouldBeNil)
//
// 			req := &taskGen.TaskActionRequest{
// 				StartTime: &startTime,
// 				EndTime:   &endTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 4)
// 			So(allTimelines[1].StartTime.Unix(), ShouldEqual, start.Unix())
// 			So(allTimelines[1].EndTime.Unix(), ShouldEqual, end.Unix())
// 			So(checkTimelinesContinuous(allTimelines), ShouldEqual, true)
// 		})
//
// 		Convey("logic3/Case3.2_Left: ", func() {
// 			ctx := context.Background()
// 			startTime := "2022-12-01 10:00"
// 			start, err := common.ConvertFormatTime(startTime)
// 			So(err, ShouldBeNil)
// 			endTime := "2023-01-05 10:00"
// 			end, err := common.ConvertFormatTime(endTime)
// 			So(err, ShouldBeNil)
// 			// for _, t := range allTimelines {
// 			// 	fmt.Printf("\nallTimelines: %+v %+v\n", t.StartTime, t.EndTime)
// 			// }
//
// 			req := &taskGen.TaskActionRequest{
// 				StartTime: &startTime,
// 				EndTime:   &endTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 5)
// 			So(allTimelines[1].StartTime.Unix(), ShouldEqual, start.Unix())
// 			So(allTimelines[1].EndTime.Unix(), ShouldEqual, end.Unix())
// 			So(checkTimelinesContinuous(allTimelines), ShouldEqual, true)
// 		})
//
// 		Convey("logic3/Case3.2_Right: ", func() {
// 			ctx := context.Background()
// 			startTime := "2022-12-05 10:00"
// 			start, err := common.ConvertFormatTime(startTime)
// 			So(err, ShouldBeNil)
// 			endTime := "2023-02-01 10:00"
// 			end, err := common.ConvertFormatTime(endTime)
// 			So(err, ShouldBeNil)
//
// 			req := &taskGen.TaskActionRequest{
// 				StartTime: &startTime,
// 				EndTime:   &endTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 5)
// 			So(allTimelines[2].StartTime.Unix(), ShouldEqual, start.Unix())
// 			So(allTimelines[2].EndTime.Unix(), ShouldEqual, end.Unix())
// 			So(checkTimelinesContinuous(allTimelines), ShouldEqual, true)
// 		})
//
// 		Convey("logic3/Case3.3: ", func() {
// 			ctx := context.Background()
// 			startTime := "2022-12-05 10:00"
// 			start, err := common.ConvertFormatTime(startTime)
// 			So(err, ShouldBeNil)
// 			endTime := "2023-02-05 10:00"
// 			end, err := common.ConvertFormatTime(endTime)
// 			So(err, ShouldBeNil)
//
// 			req := &taskGen.TaskActionRequest{
// 				StartTime: &startTime,
// 				EndTime:   &endTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 5)
// 			So(allTimelines[2].StartTime.Unix(), ShouldEqual, start.Unix())
// 			So(allTimelines[2].EndTime.Unix(), ShouldEqual, end.Unix())
// 			So(checkTimelinesContinuous(allTimelines), ShouldEqual, true)
// 		})
//
// 		Convey("logic3/Case4.1: ", func() {
// 			ctx := context.Background()
// 			startTime := "2022-10-05 10:00"
// 			endTime := "2022-10-06 10:00"
//
// 			req := &taskGen.TaskActionRequest{
// 				StartTime: &startTime,
// 				EndTime:   &endTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldNotEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 6)
// 			So(checkTimelinesContinuous(allTimelines), ShouldEqual, true)
// 		})
//
// 		Convey("logic3/Case4.2: ", func() {
// 			ctx := context.Background()
// 			startTime := "2022-10-05 10:00"
// 			start, err := common.ConvertFormatTime(startTime)
// 			So(err, ShouldBeNil)
// 			endTime := "2023-01-01 10:00"
// 			end, err := common.ConvertFormatTime(endTime)
// 			So(err, ShouldBeNil)
//
// 			req := &taskGen.TaskActionRequest{
// 				StartTime: &startTime,
// 				EndTime:   &endTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 5)
// 			So(allTimelines[0].StartTime.Unix(), ShouldEqual, start.Unix())
// 			So(allTimelines[0].EndTime.Unix(), ShouldEqual, end.Unix())
// 			So(checkTimelinesContinuous(allTimelines), ShouldEqual, true)
// 		})
//
// 		Convey("logic3/Case4.3: ", func() {
// 			ctx := context.Background()
// 			startTime := "2022-10-05 10:00"
// 			start, err := common.ConvertFormatTime(startTime)
// 			So(err, ShouldBeNil)
// 			endTime := "2023-01-02 10:00"
// 			end, err := common.ConvertFormatTime(endTime)
// 			So(err, ShouldBeNil)
//
// 			req := &taskGen.TaskActionRequest{
// 				StartTime: &startTime,
// 				EndTime:   &endTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 4)
// 			So(allTimelines[0].StartTime.Unix(), ShouldEqual, start.Unix())
// 			So(allTimelines[0].EndTime.Unix(), ShouldEqual, end.Unix())
// 			So(checkTimelinesContinuous(allTimelines), ShouldEqual, true)
// 		})
//
// 		Convey("logic4: start/nil;end/not nil", func() {
// 			ctx := context.Background()
// 			endTime := "2023-02-01 23:00"
// 			req := &taskGen.TaskActionRequest{
// 				EndTime: &endTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldNotEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 6)
// 		})
//
// 		Convey("param_constraint1: empty interval", func() {
// 			ctx := context.Background()
// 			startTime := "2023-02-01 23:00"
// 			endTime := "2023-02-01 23:00"
// 			req := &taskGen.TaskActionRequest{
// 				StartTime: &startTime,
// 				EndTime:   &endTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldNotEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 6)
// 			So(checkTimelinesContinuous(allTimelines), ShouldEqual, true)
// 		})
//
// 		Convey("param_constraint2: endtime < starttime", func() {
// 			ctx := context.Background()
// 			startTime := "2023-02-01 23:00"
// 			endTime := "2023-01-30 23:00"
// 			req := &taskGen.TaskActionRequest{
// 				StartTime: &startTime,
// 				EndTime:   &endTime,
// 			}
// 			task := &models.Task{
// 				ID: "991104",
// 			}
// 			errmsg := TaskStart(ctx, req, task)
// 			So(errmsg, ShouldNotEqual, "")
//
// 			allTimelines, err := models.SelectTimelinesList(ctx, nil)
// 			So(err, ShouldBeNil)
// 			So(len(allTimelines), ShouldEqual, 6)
// 			So(checkTimelinesContinuous(allTimelines), ShouldEqual, true)
// 		})
//
// 		Convey("test isEmptyTimelineTable/false", func() {
// 			ctx := context.Background()
// 			now := time.Now()
// 			result, err := isEmptyTimelineTable(ctx, now)
// 			So(err, ShouldBeNil)
// 			So(result, ShouldEqual, false)
// 		})
// 	})
// }
