package service

import (
	"context"
	"task-service/biz_error"
	"task-service/common"
	"task-service/models"
)

func convertInprogress2Blocking(ctx context.Context) *biz_error.BizError {
	// 如果进行中先执行了finish，那被切换后也不会变成阻塞中。
	err := models.UpdateTaskStatusByStatus(ctx, string(common.TaskStatusBlocking), string(common.TaskStatusInprogress))
	if err != nil {
		return err
	}
	return nil
}

func UpdateStatusByStartCommand(ctx context.Context, task *models.Task) *biz_error.BizError {
	// 需要把其他进行中任务设置为阻塞
	err := convertInprogress2Blocking(ctx)
	if err != nil {
		return err
	}

	// 设置当前任务状态为进行中。
	task.Status = string(common.TaskStatusInprogress)
	err = models.SaveTask(ctx, task)
	if err != nil {
		return err
	}

	return UpdateParentTaskStatus(ctx, task)
}

func UpdateStatusByFinishCommand(ctx context.Context, task *models.Task) *biz_error.BizError {
	task.Status = string(common.TaskStatusFinish)
	err := models.SaveTask(ctx, task)
	if err != nil {
		return err
	}

	return UpdateParentTaskStatus(ctx, task)
}

func UpdateStatusByAddCommand(ctx context.Context, task *models.Task) *biz_error.BizError {
	return UpdateParentTaskStatus(ctx, task)
}

// 通过子task来决策父任务的status的规则
func ruleGetParentStatus(ctx context.Context, children []*models.Task) common.TaskStatusType {
	// 记录是否全部已删除
	isRemoved := true
	// 记录是否全部已完成
	isFinished := true
	// 记录是否全部待启动
	isUnstarted := true
	// 是否全部待启动或者已完成
	isAllUnstartdOrFinished := true
	// 是否存在进行中
	hasInprogress := false
	// 是否存在阻塞中
	hasBlocking := false

	for _, c := range children {
		if common.TaskStatusType(c.Status) != common.TaskStatusRemoved {
			isRemoved = false
		} else {
			// 如果是已删除的任务，不影响后续其他规则逻辑，
			// 比如除了已删除的，其他的都是待启动或者已完成，那也要设置待启动或者已完成
			// 所以除非针对isRemoved=true的情况，否则continue
			continue
		}

		if common.TaskStatusType(c.Status) != common.TaskStatusUnstarted {
			isUnstarted = false
		}

		if common.TaskStatusType(c.Status) != common.TaskStatusFinish {
			isFinished = false
		}

		if common.TaskStatusType(c.Status) != common.TaskStatusUnstarted &&
			common.TaskStatusType(c.Status) != common.TaskStatusFinish {
			isAllUnstartdOrFinished = false
		}

		if common.TaskStatusType(c.Status) == common.TaskStatusInprogress {
			hasInprogress = true
		}

		if common.TaskStatusType(c.Status) == common.TaskStatusBlocking {
			hasBlocking = true
		}
	}

	// 由于初始值3个都是true，所以isRemoved的判断要放在最优先
	if isRemoved {
		// 这个条件满足，说明所有任务都是已删除。没有其他status的task
		return common.TaskStatusRemoved
	}
	if isUnstarted {
		// 表示除了已删除的task，全部都是待启动
		return common.TaskStatusUnstarted
	}
	if isFinished {
		// 表示除了已删除的task，全部都是已完成
		return common.TaskStatusFinish
	}

	// 按照优先级规则: 进行中>阻塞中>挂起中
	if hasInprogress {
		// 存在进行中的子任务，父任务也为进行中
		return common.TaskStatusInprogress
	}
	if hasBlocking {
		// 不存在进行中子任务时，存在阻塞中的子任务，父任务也为阻塞中
		return common.TaskStatusBlocking
	}
	// 最后判断，除了已删除的task，全部是待启动或者已完成两类
	if isAllUnstartdOrFinished {
		return common.TaskStatusUnstarted
	}
	return common.TaskStatusUnknown
}

func UpdateParentTaskStatus(ctx context.Context, task *models.Task) *biz_error.BizError {
	parent, err := findTaskParent(ctx, task)
	if err != nil {
		return err
	}

	if parent == nil {
		return nil
	}
	return UpdateTaskStatus(ctx, parent)
}

func UpdateParentTaskStatusByID(ctx context.Context, taskID string) *biz_error.BizError {
	task, err := findTaskByID(ctx, taskID)
	if err != nil {
		return err
	}
	return UpdateTaskStatus(ctx, task)
}

func UpdateTaskStatus(ctx context.Context, task *models.Task) *biz_error.BizError {
	// 用固定规则来递归更新父任务状态
	children, err := findTaskChildren(ctx, task)
	if len(children) == 0 {
		// 对于00节点上升的逻辑流程，会出现len(children) == 0
		return nil
	}
	status := ruleGetParentStatus(ctx, children)
	task.Status = string(status)

	err = models.SaveTask(ctx, task)
	if err != nil {
		return err
	}
	return UpdateParentTaskStatus(ctx, task)
}
