SHELL=/bin/bash

# 通过export传给子Makefile
export DB_ROOT_PASS=adminpass
export DB_HOST=127.0.0.1
export DB_PORT=2015
export DB_NAME=treeblog
export DB_USER=tree-blog
export DB_PASS=ce208f63-1f43-4bbe-a2ec-4764e7deb231

dv:
	npm run dev

prod:
	npm run prod

db%:
	make -C common_db/ $@

build:
	npm i

# deploy要用root权限，但是build要用main_user权限（否则git status没权限报错）。所以不能用依赖关系
deploy:
	${CURDIR}/scripts/deploy.sh

dbug:
	docker run -v /data/docker-volumes/blog-service/runtime/blog-service-logs:/runtime/blog-service-logs -it --entrypoint /bin/bash blog-service:0.0.21 -s 
