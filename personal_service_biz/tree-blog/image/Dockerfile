FROM node:22.11.0
WORKDIR .
EXPOSE 7799

COPY server server
COPY websites websites
COPY package.json package.json
COPY node_modules node_modules
# RUN npm install
RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN echo 'Asia/Shanghai' >/etc/timezone
# CMD [ "npm", "run", "prod" ]
# CMD [ "/bin/sh", "-c", "cd server && NODE_ENV=production NODE_CONFIG_DIR=./config node app.js >/runtime/blog-service-logs/stdout.log 2>/runtime/blog-service-logs/stderr.log" ]
CMD [ "/bin/sh", "-c", "NODE_ENV=production NODE_CONFIG_DIR=./server/config node server/app.js >/runtime/blog-service-logs/stdout.log 2>/runtime/blog-service-logs/stderr.log" ]
