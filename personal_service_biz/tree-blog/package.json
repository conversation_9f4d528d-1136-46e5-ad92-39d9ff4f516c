{"name": "tree-blog", "version": "1.0.0", "description": "Personal website of Mr<PERSON>.", "main": "", "scripts": {"dev": "NODE_ENV=development NODE_CONFIG_DIR=./server/config node server/app.js", "prod": "NODE_ENV=production NODE_CONFIG_DIR=./server/config pm2 start server/app.js --name tree-blog", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {}, "author": "luzeshu <<EMAIL>>", "license": "ISC", "dependencies": {"cheerio": "0.22.0", "config": "1.21.0", "highlight.js": "9.8.0", "koa": "1.2.0", "koa-bodyparser": "2.2.0", "koa-ejs": "3.0.0", "koa-log4": "1.0.1", "koa-router": "5.4.0", "koa-session": "3.4.0", "koa-static": "2.0.0", "lodash": "4.16.6", "markdown-it": "8.1.0", "mongoose": "4.6.4", "mongoose-delete": "0.3.4", "mysql": "2.13.0", "nodemailer": "4.0.1", "pm2": "4.2.3", "raw-body": "2.3.3", "request": "2.76.0", "sequelize": "3.24.1", "xml2js": "0.4.17", "xml2js-es6-promise": "1.1.1"}}