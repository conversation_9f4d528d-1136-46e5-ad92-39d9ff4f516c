
import request from 'superagent';
import {url} from '../../config';

function wrap(api) {
  return function () {
    return api.apply(this, arguments)
      .then((res) => {
        if (res.status != 200) {
          throw new Error('status: ' + res.status);
        }
        if (res.body.code != 0) {
          throw new Error('msg: ' + res.body.msg);
        }
        return res.body;
      })
      .catch((err) => {
        console.error(err);
        alert(err);
      });
  }
}

var mod = {};

mod.blogListApi = function(){
  return request(url + 'bloglist');
}

mod.blogPostApi = function(params) {
  return request.post(url + 'blog')
    .send(params);
}

mod.blogInfoApi = function (id) {
  return request(url + 'blog/' + id);
}

mod.blogUpdateApi = function(id, params) {
  return request.put(url + 'blog/' + id)
    .send(params);
}

mod.blogDelApi = function (id) {
  return request.del(url + 'blog/' + id);
};

mod.commentListApi = function (blogId) {
  return request(url + 'commentlist?blogId=' + blogId);
};

mod.commentDelApi = function (commentId) {
  return request.del(url + 'comment?id=' + commentId);
}

mod.unreadMsgApi = function () {
  return request(url + 'unreadmsg');
}

mod.commentSetMsgApi = function (commentId, status) {
  return request.put(url + 'comment/' + commentId)
    .send({status: status});
}

for(var api in mod) {
  mod[api] = wrap(mod[api]);
}
export default mod;
