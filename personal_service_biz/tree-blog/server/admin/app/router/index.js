
import VueRouter from 'vue-router';
import blogList from '../views/bloglist.vue';
import blogAdd from '../views/blogadd.vue';
import blogEdit from '../views/blogedit.vue';
import blogComments from '../views/blogcomments.vue';

const router = new VueRouter({
  mode: 'hash',
  base: '/hell',
  routes: [
    {path: '/', component: blogList},
    {path: '/blog/list', component: blogList},
    {path: '/blog/add', component: blogAdd},
    {path: '/blog/edit', component: blogEdit},
    {path: '/blog/comments', component: blogComments}
  ]
});
  
export default router;
