<template>
  <div class="wrapper">
    <app-header></app-header>
    <app-sidebar></app-sidebar>
    <div class="content-wrapper">
      <section class="content" style="overflow:scroll">
        <router-view></router-view>
      </section>
    </div>
  </div>
</template>

<script>
  import appHeader from './components/header.vue';
  import appSidebar from './components/sidebar.vue';
  export default {
    components: {
      appHeader,
      appSidebar
    }
  }
</script>

