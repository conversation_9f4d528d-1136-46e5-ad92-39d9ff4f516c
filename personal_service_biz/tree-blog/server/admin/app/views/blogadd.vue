<template>
  <form onSubmit="return false;">
    <div class="form-group">
      <label>标题</label>
      <input type="text" class="form-control" v-model="blogInfo.title"></input>
    </div>
    <div class="form-group">
      <label>url</label>
      <input type="text" class="form-control" v-model="blogInfo.url"></input>
    </div>
    <div class="form-group">
      <label>摘要</label>
      <textarea type="text" class="form-control" v-model="blogInfo.abstract"></textarea>
    </div>
    <textarea id="editor">
    </textarea>
    <button class="btn btn-success" @click="postBlog()">提交</button>
  </form>
</template>

<script>
  import api from '../apis';
  export default {
    data: function () {
      return {
        mde: null,
        blogInfo: {
          title: '',
          url: '',
          content: '',
          abstract: ''
        }
      };
    },

    mounted: function () {
      this.$nextTick(() => {
        this.mde = new SimpleMDE({element: document.getElementById("editor")});
      });
    },

    methods: {
      postBlog: function () {
        this.blogInfo.content = this.mde.value();
        return api.blogPostApi(this.blogInfo)
          .then((body) => {
            this.$router.replace('/blog/list');
          });
      }
    }
  };
</script>
