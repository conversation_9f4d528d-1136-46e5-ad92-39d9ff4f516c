<template>
  <div class="comments-list">
    <table class="table" style="border: 1px solid green">
      <thead>
        <tr>
          <th style="width: 30px;">评论者</th>
          <th style="width: 100px;">内容</th>
          <th style="width: 20px;">楼层</th>
        </tr>
      </thead>

      <tbody>
        <tr v-for="(comment, index) in comments" style="border: 1px solid green">
          <td>
            <a href="javascript:void(0)" ><img class="comment-head" :src="comment.head"></img></a>
            <div class="comment-user"><label><a :href="comment.link" >{{comment.user}}</a> : </label></div>
            <div v-if="comment.cnblogCommentId">来自cnblog</div>
            <div v-if="comment.cnodeCommentId">来自cnode</div>
          </td>

          <td>
            <div class="comment-time">{{ comment.timetext }}</div>
            <div class="clr"></div>
            <div class="comment-content">
              {{ comment.comment }}
            </div>
            <button v-if="comment.status=='UNREAD'" class="btn btn-warning comment-button" @click="setMsg(comment.id, 'READ')">标为已读</button>
            <button v-else class="btn btn-primary comment-button" @click="setMsg(comment.id, 'UNREAD')">标为未读</button>
            <button class="btn btn-danger comment-button" @click="delComment(comment.id)">删除</button>

            <div class="clr"></div>

            <div v-if="comment.reply.length" class="comment-reply-list">
              <div v-for="(reply, idx) in comment.reply" class="comment-wrapper">
                <a href="javascript:void(0)" ><img class="comment-head" :src="reply.head"></img></a>
                <div class="comment-user"><label><a :href="reply.link" >{{reply.user}}</a> : </label></div>
                <div class="comment-time">{{ reply.timetext }}</div>
                <div class="clr"></div>
                <div class="comment-content">
                  {{reply.comment}}
                </div>
                <button v-if="reply.status=='UNREAD'" class="btn btn-warning comment-button" @click="setMsg(reply.id, 'READ')">标为已读</button>
                <button v-else class="btn btn-primary comment-button" @click="setMsg(reply.id, 'UNREAD')">标为未读</button>
                <button class="btn btn-danger comment-button" @click="delComment(reply.id)">删除</button>
                <div class="clr"></div>
              </div>
            </div>
          </td>

          <td>
            <div class="comment-floor"><a href="javascript:void(0)">#{{index+1}}楼</a></div>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script>
  import api from '../apis';
  export default {
    data: function() {
      return {
        blogId: undefined,
        comments: []
      };
    },

    mounted: function() {
      this.$nextTick(() => {
        this.blogId = this.$route.query.id;
        this.getComments(this.blogId);
      });
    },
    
    methods: {
      getComments: function(id) {
        return api.commentListApi(id)
          .then((body) => {
          console.log('body: ', body);
            this.comments = body.msg;
          });
      },

      delComment: function(id) {
        return api.commentDelApi(id)
          .then((body) => {
            this.getComments(this.blogId);
          });
      },

      setMsg: function(id, status) {
        return api.commentSetMsgApi(id, status)
          .then((body) => {
            this.getComments(this.blogId);
          });
      }
    }

  };
</script>

<style>
@media (max-width: 500px) {
  .comment-wrapper {
    margin-right: 0px;
    margin-left: 0px;
  }
}
.comment-content * {
  font-family: Verdana,Geneva,Arial,Helvetica,sans-serif; 
}
.comment-wrapper {
  margin: 15px 10px 15px 10px;
  border-bottom: 1px dashed #d3d3d3;
  float: left;
  width: 80%;
}

.comment-head {
  margin: 10px;
  margin-right: 0px;
  height: 45px;
  width: 45px;
  float: left;
}

.comment-box {
  float: left;
  width: 100% - 90px;
}

.comment-user {
  float: left;
  margin: 10px 0 10px 20px;
}

.comment-time, .comment-floor {
  float: right;
  margin: 10px;
}
.comment-time {
  margin-left: 0px;
}

.comment-content {
  float: left;
  margin: 10px;
}

.comment-content img {
  max-width: 100%;
}

.reply-box {
  margin: 10px;
}

.reply-text {
  width: 100%;
}

.comment-reply-list {
  margin-left: 20px;
}

.comment-button {
  float: right;
  margin: 10px;
}

.comment-form {
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 10px 20px 20px 20px;
  margin: 0 10px 20px 10px;
}

.comment-input h4 {
  font-size: 20px;
  margin: 0 0 18px;
  text-transform: capitalize;
}

.comment-form label {
  margin: 0.4em;
}

.comment-text, .reply-box {
  min-height: 200px;
}

.comment-form textarea {
  width: 100%;
}
.comment-form input {
  width: 100%;
}
.comment-form button {
  float: right;
}

.comments-list-wrapper {
  margin-top: 20px;
}

.comments-list {

}

.comments-header {
  line-height: 1.7;
  font-size: 18px;
  font-weight: 700;
  border-bottom: 1px solid #d3d3d3;
  padding-bottom: 8px;
  margin-bottom: 20px;
}

.clr {
  clear: both;
}

</style>
