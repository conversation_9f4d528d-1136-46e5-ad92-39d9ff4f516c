<template>
  <form onSubmit="return false;">
    <div class="form-group">
      <label>标题</label>
      <input type="text" class="form-control" v-model="blogInfo.title"></input>
    </div>
    <div class="form-group">
      <label>url</label>
      <input type="text" class="form-control" v-model="blogInfo.url"></input>
    </div>
    <div class="form-group">
      <label>发表时间</label>
      <input type="text" class="form-control" v-model="blogInfo.postTime"></input>
    </div>
    <div class="form-group">
      <label>创建时间</label>
      <input type="text" class="form-control" v-model="blogInfo.createTime"></input>
    </div>
    <div class="form-group">
      <label>cnblog ID</label>
      <input type="text" class="form-control" v-model="blogInfo.cnblogId"></input>
    </div>
    <div class="form-group">
      <label>cnode ID</label>
      <input type="text" class="form-control" v-model="blogInfo.cnodeId"></input>
    </div>
    <div class="form-group">
      <label>摘要</label>
      <textarea type="text" style="min-height: 100px" class="form-control" v-model="blogInfo.abstract"></textarea>
    </div>
    <textarea id="editor">
    </textarea>
    <button class="btn btn-success" @click="updateBlog">提交</button>
  </form>
</template>

<script>
  import api from '../apis';
  export default {
    data: function () {
      return {
        mde: null,
        blogInfo: {
          id: null,
          url: '',
          postTime: null,
          createTime: null,
          title: '',
          content: '',
          abstract: '',
          cnodeId: '',
          cnblogId: ''
        }
      };
    },

    mounted: function () {
      this.$nextTick(() => {
        this.mde = new SimpleMDE({element: document.getElementById("editor")});
        this.getBlog();
      });
    },

    methods: {
      updateBlog: function () {
        this.blogInfo.content = this.mde.value();
        return api.blogUpdateApi(this.blogInfo.id, this.blogInfo)
          .then((body) => {
            this.$router.replace('/blog/list');
          });
      },

      getBlog: function () {
        this.blogInfo.id = this.$route.query.id;
        return api.blogInfoApi(this.blogInfo.id)
          .then((body) => {
            this.blogInfo = body.msg;
            this.mde.value(this.blogInfo.content);
          });
      }
    }
  };
</script>
