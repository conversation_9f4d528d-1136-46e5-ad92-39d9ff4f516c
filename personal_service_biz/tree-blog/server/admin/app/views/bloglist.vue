<template>
<div>
  <div style="float:left; padding-right:80px;">
    <div style="float: left">
      未读消息：{{unreadMsgNum}}
    </div>
    <div style="clear:both"></div>
    <div>
      <span v-for="(msg, index) in unreadMsg" style="padding: 5px">
        <router-link :to="'/blog/comments?id=' + msg.blogId">{{msg.blogId}}</router-link>
      </span>
    </div>
  </div>
  <div style="float:right">
    <router-link class="btn btn-success" style="float: right" to="/blog/add">添加</router-link>
  </div>
  <table class="table">
    <thead>
      <tr>
        <!-- 第一行th加上width就会乱 -->
        <th>页面路由</th>
        <th style="width:400px;">标题</th>
        <th style="width:60px">评论数</th>
        <th style="width:150px">创建时间</th>
        <th style="width:250px">操作</th>
      </tr>
    </thead>
    <tbody>
      <tr v-for="(blog, index) in blogList">
        <td style="text-align:left;">
          <a target="_blank" :href="blog.urlhref">{{blog.url}}</a>
        </td>
        <td style="text-align:left;">{{blog.title}}</td>
        <td>
          <router-link :to="blog.commenthref">{{blog.commentNum}}</router-link>
        </td>
        <td>{{blog.createTime}}</td>
        <td>
          <button v-if="blog.status=='UNPOST'" class="btn btn-warning" @click="postBlog(index)">发布</button>
          <button v-else class="btn btn-primary" @click="unpostBlog(index)">取消发布</button>
          <router-link class="btn btn-info" :to="'/blog/edit?id=' + blog.id">编辑</router-link>
          <button class="btn btn-danger" @click="delBlog(blog.id)">删除</button>
        </td>
      </tr>
    </tbody>
  </table>
</div>
</template>

<script>
  import api from '../apis';
  import config from '../../config';
  export default {
    data: function () {
      var blogList = [];
      return {
        unreadMsgNum: 0,
        unreadMsg: [],
        blogList: blogList
      }
    },

    created: function() {
      return this.getBlogList()
        .then(() => this.getUnreadMsg());
    },

    // watch: {
    //   $route: function (transition) {
    //     return this.getBlogList()
    //       .then(() => this.getUnreadMsg());
    //   }
    // },

    methods: {
      getBlogList: function () {
        return api.blogListApi()
          .then((body) => {
            this.blogList = body.msg;
            this.blogList.forEach((item) => {
              item.urlhref = config.blogurl + item.url;
              item.commenthref = "/blog/comments?id=" + item.id;
            });
          });
      },
      getUnreadMsg: function() {
        return api.unreadMsgApi()
          .then((body) => {
            this.unreadMsgNum = body.msg.count;
            this.unreadMsg = body.msg.rows;
          });
      },
      delBlog: function (id) {
        return api.blogDelApi(id)
          .then((body) => {
            this.getBlogList();
          });
      },
      postBlog: function (index) {
        return api.blogUpdateApi(this.blogList[index].id, {status: 'POST'})
          .then((body) => {
            this.blogList[index].status = 'POST';
            this.getBlogList();
          });

        function _padZero(num) {
          if (num < 10) {
            return '0' + num;
          }
          return '' + num;
        }
        
        function formatTime (time) {
          if (!(time instanceof Date))
            return ;
          let year = _padZero(time.getFullYear());
          let month = _padZero(time.getMonth() + 1);
          let date = _padZero(time.getDate());
          let hours = _padZero(time.getHours());
          let minutes = _padZero(time.getMinutes());
          let seconds = _padZero(time.getSeconds());
          return year + '-' + month + '-' + date + ' ' + hours + ':' + minutes + ':' + seconds;
        }
      },
      unpostBlog: function (index) {
        return api.blogUpdateApi(this.blogList[index].id, {status: 'UNPOST'})
          .then((body) => {
            this.blogList[index].status = 'UNPOST';
          });
      }
    }
  }
</script>

<style>
th,td {
  text-align: center;
}
</style>
