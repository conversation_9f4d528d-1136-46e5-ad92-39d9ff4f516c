<template>
  <aside class="main-sidebar">
    <section class="sidebar">
      <ul class="sidebar-menu">
        <li v-for="item in menu" class="treeview active">
          <router-link :to="item.route" tag="a">
            <i :class="'fa' + item.icon"></i>
            <span>{{item.desc}}</span>
          </router-link>
        </li>
      </ul>
    </section>
  </aside>
</template>

<script>
  export default {
    data: function () {
      var menu = [
        {desc: '博客管理', route: '/blog/list', icon: 'fa-list-ol'},
        {desc: '分类管理', route: '/category/list', icon: 'fa-dashboard'}
      ];
      return {
        menu: menu
      };
    }
  };
</script>
