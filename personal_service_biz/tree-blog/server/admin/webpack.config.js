var path = require('path');
var VueLoaderPlugin = require('vue-loader').VueLoaderPlugin;

var COMPRESS = true;
module.exports = {
  mode: 'development',
  entry: './app/entry.js',
  resolve: {
    alias: {vue: 'vue/dist/vue.js'}
  },
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: COMPRESS ? 'build.min.js' : 'build.js'
  },
  resolve: {
    extensions: ['.js', '.vue', '.css']
  },
  module: {
    rules: [
      {test: /\.vue$/, loader: 'vue-loader'},
      // {test: /\.js$/, loader: 'babel-loader', query: {presets: ['es2015']}, exclude: /node_modules/},
      {test: /\.css$/, loader: 'style-loader!css-loader'}
    ]
  },
  // ？？？注释掉时报的错误
  // vue: {
  //   loaders: {
  //     js: 'babel-loader'
  //   }
  // },
  // babel: {
  //   presets: ['es2015'],
  //   plugins: ['transform-runtime']
  // },
  optimization: {
    minimize: true
  },
  plugins: [
    new VueLoaderPlugin()
  ]
}
