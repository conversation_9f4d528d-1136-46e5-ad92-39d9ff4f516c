/**
 * @file server main file
 */
'use strict';

var path = require('path');
var fs = require('fs');
var https = require('https');
var http = require('http');

var koa = require('koa');
var bodyParser = require('koa-bodyparser'); 
var koaStatic = require('koa-static');
var render = require('koa-ejs');
var config = require('config');
// var log4js = require('log4js');
// var koaLog4 = require('koa-log4');
var session = require('koa-session');
var Promise = require('bluebird');
var co = require('co');

var routesArray = require('./routes');

// var hostWithPort = config.host;
// if (config.port != 443) {
//   hostWithPort += ':' + config.server.port.https;
// }

co(function *() {
try {
global.Promise = Promise;
// log4js.configure(config.log4js);
// global.logger = log4js.getLogger('normal');
// logger.setLevel(config.loglevel);
global.logger = console;
global.db = {};
yield require('./models')(global.db);

var app = koa();
app.on('error', function (err) {
  logger.info('error catch on koa: ');
  logger.error(err);
});

app.use(bodyParser());
app.use(session(config.session, app));

// app.use(koaLog4.koaLogger(global.logger));

/**
 * templating
 */
render(app, {
  //root: path.join(__dirname, 'views'),
  root: path.join(__dirname, config.template.path, 'views'),
  viewExt: 'ejs',
  layout: false,
  cache: false
});


/**
 * file serving
 */
// app.use(koaStatic(path.join(__dirname, 'public'), {hidden: true}));
// app.use(koaStatic(path.join(__dirname, 'public/imageservice')));
app.use(koaStatic(path.join(__dirname, '../websites'), {hidden: true}));
app.use(koaStatic(path.join(__dirname, '../websites/imageservice')));
app.use(koaStatic(path.join(__dirname, config.template.path, 'public')));


/**
 * routing and mounting
 */
routesArray.forEach((routes) => {
  app.use(routes);
});


//var heapdump = require('heapdump');
//heapdump.writeSnapshot(path.join(__dirname, 'public/111.heapsnapshot'));

/**
 * start server
 */
/*
if (config.server.port.https) {
//var options = {
//  key: fs.readFileSync(path.join(__dirname, 'cert/treeblog.key')),
//  cert: fs.readFileSync(path.join(__dirname, 'cert/luzeshu.com.pem'))
//};

  var options = {
    key: fs.readFileSync(path.join(__dirname, 'cert/letsencrypt-privkey.pem')),
    cert: fs.readFileSync(path.join(__dirname, 'cert/letsencrypt-fullchain.pem'))
  };
  
  https.createServer(options, app.callback())
    .listen(config.server.port.https, function () {
      logger.info('https server start, port: ', config.server.port.https);
    });
  
  // 如果启用了https，那么http服务则全部进行转发到https
  var app2 = koa();
  app2.use(function *() {
    var redirect = 'https://' + this.hostname;
    if (config.server.port.https != 443) {
      redirect += ':' + config.server.port.https;
    }
    redirect += this.url;
    logger.info('redirect to ['+this.method+'] '+redirect);
    this.redirect(redirect);
  });
  http.createServer(app2.callback())
    .listen(config.server.port.http, function () {
      logger.info('http server start, port: ', config.server.port.http);
    });
} else {
  // 如果没有https模式，则启用http
  // 比如缺少https证书时，获取证书前需要启动http模式验证域名
*/
  http.createServer(app.callback())
    .listen(config.server.port.http, config.server.bindHost, function () {
      logger.info('http server start, port: ', config.server.port.http);
    });
// }

} catch(err) {
  console.error(err);
}

// // 更新其他网站同步博客评论周期
// // 这些需要在yield require('models')() yield之后执行才可以
// var services = require('./services');
// services.initRenewal();
// services.startCommentsRenewal();

});

