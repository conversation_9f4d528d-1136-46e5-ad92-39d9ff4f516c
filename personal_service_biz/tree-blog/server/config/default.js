/**
 * @file config file
 */
'use strict';

module.exports = {
	server: {
	},
    // mysql: {
    //   host: '127.0.0.1',
    //   port: 2015,
    //   dbname: 'treeblog',
    //   username: 'tree-blog',
    //   password: 'ce208f63-1f43-4bbe-a2ec-4764e7deb231'
    // },
    mongo: {
      host: '127.0.0.1',
      port: 27017,
      dbname: 'blog'
    },
    session: {
      key: 'koa:sess',
      maxAge: 864000000,
      //overwrite: true,
      signed: false
    },
    loglevel: 'INFO',

    host: 'https://luzeshu.com',

    template: {
      //path: 'templates/1'
      //path: 'templates/Addiction/Documentation'
      //path: 'templates/my1'
      path: 'templates/tree'
    },

    mail: {
      host: '127.0.0.1',
      port: 2014,
      auth: {
        user: 'blog',
        pass: 'blogiscoder'
      },
      from: '卢泽树<<EMAIL>>'
    },
    defaultHeadpic: '/pictures/default.gif',

    app: {
      lastComment: 8
    }
}
