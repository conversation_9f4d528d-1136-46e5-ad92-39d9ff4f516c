/**
 * @file development config file
 */
'use strict';

module.exports = {
	server: {
      port: {
        https: 8899,
        http: 8898
      },
      bindHost: "localhost"
	},
    mysql: {
      host: '127.0.0.1',
      port: 3308,
      dbname: 'db_blog',
      username: 'bloguser',
      password: 'blogpass',
      log: '/var/log/tree-blog/mysql-dev.log'
    },
    // log4js: {
    //   appenders: [
    //     {type: 'console'},
    //     {
    //       type: 'file',
    //       filename: '/var/log/tree-blog/blog-dev.log',
    //       backup: 3,
    //       category: 'normal'
    //     }
    //   ]
    // },
    info: {
      webTitle: '大树的个人空间',
      author: {
        chName: '卢泽树',
        enName: 'luzeshu',
        intro: ''
      },
      recordation: '粤ICP备2021166477号',
      recordationUrl: 'https://beian.miit.gov.cn'
    }

}
