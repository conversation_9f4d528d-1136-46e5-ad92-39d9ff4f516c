/**
 * @file production config file
 */
'use strict';

module.exports = {
	server: {
      port: {
        https: 443,
        http: 7799
      },
      // 通过docker容器的话，不能只bind localhost
      bindHost: "0.0.0.0"
	},
    mysql: {
      host: 'blog-mysql',
      port: 3306,
      dbname: 'db_blog',
      username: 'bloguser',
      password: 'blogpass',
      // log: '/runtime/blog-service-logs/mysql.log'
    },
    // log4js: {
    //   appenders: [
    //     {type: 'debug'},
    //     {
    //       type: 'file',
    //       filename: '/runtime/blog-service-logs/blog.log',
    //       backup: 3,
    //       category: 'normal'
    //     }
    //   ]
    // },
    info: {
      webTitle: '大树的个人空间',
      author: {
        chName: '卢泽树',
        enName: 'luzeshu',
        intro: ''
      },
      recordation: '粤ICP备2021166477号',
      recordationUrl: 'https://beian.miit.gov.cn',
    }
}
