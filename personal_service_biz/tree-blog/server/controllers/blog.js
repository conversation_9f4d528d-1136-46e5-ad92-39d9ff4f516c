/**
 * @file page controllers file
 */
'use strict';

var _ = require('lodash');

var services = require('../services');
var formatTime = require('../lib').formatTime;

function transfer(blog) {
  blog = _.pick(blog, ['id', 'url', 'title', 'content', 'cnblogId', 'cnodeId', 'createTime', 'postTime', 'readNum', 'status', 'commentNum', 'abstract']);
  blog.createTime = formatTime(blog.createTime);
  blog.postTime = formatTime(blog.postTime);

  return blog;
}

exports.blogList = function *() {
  var result = yield services.blogList();
  
  for(var i = 0; i < result.length; ++i) {
    result[i] = transfer(result[i]);
    result[i].comments = yield services.commentList(result[i].id);
    result[i].commentNum = result[i].comments.length;
  }

  this.body = {
    code: 0,
    msg: result
  };
}

exports.blogPost = function *() {
  var result = yield services.blogPost(this.validators.body);
  this.body = {
    code: 0,
    msg: result
  };
  if (this.validators.body.cnblogId || this.validators.body.cnodeId) {
    services.startCommentsRenewal();
  }
}

exports.blogInfo = function *() {
  var result = yield services.blogInfo({id: this.validators.params.id});
  this.body = {
    code: 0, 
    msg: transfer(result)
  };
}

exports.blogUpdate = function *() {
  yield services.blogUpdate(this.validators.params.id, this.validators.body);
  this.body = {
    code: 0,
    msg: '成功'
  };
  if (this.validators.body.cnblogId || this.validators.body.cnodeId) {
    services.startCommentsRenewal();
  }
}

exports.blogDelete = function *() {
  yield services.blogDelete(this.validators.params.id);
  this.body = {
    code: 0,
    msg: '成功'
  };
}
