
var cheerio = require('cheerio');
var services = require('../services');

exports.commentPost = function *() {
  var result;
  var $ = cheerio.load(this.validators.body.comment);
  var ifScriptTag = $("script").length;
  if (ifScriptTag) {
    this.body = {
      code: 40301,
      msg: '非法标签'
    };
    return ;
  }
  if (this.validators.body.commentId) {
    comment = yield services.commentInfo(this.validators.body.commentId);
    if (!comment) {
      this.body = {
        code: 500002,
        msg: '错误的commentId'
      }
      return ;
    }
    result = yield services.commentReply(comment, this.validators.body);
  } else {
    result = yield services.commentPost(this.validators.body);
  }
  this.body = {
    code: 0,
    msg: result.id
  };
}

exports.commentList = function *() {
  comments = yield services.commentList(this.validators.query.blogId);
  this.body = {
    code: 0,
    msg: comments
  };
}

exports.commentDelete = function *() {
  result = yield services.commentDelete(this.validators.query.id);
  this.body = {
    code: 0,
    msg: result
  };
}

exports.commentUpdate = function *() {
  result = yield services.commentUpdate(this.validators.params.id, this.validators.body);
  this.body = {
    code: 0,
    msg: result
  };
}

exports.unreadMsg = function *() {
  result = yield services.unreadMsg();
  this.body = {
    code: 0,
    msg: result
  };
}
