/**
 * @file page controllers file
 */
'use strict';

var _ = require('lodash');
var hljs = require('highlight.js');
var md = require('markdown-it')({
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(lang, str).value;
      } catch (__) {}
    }

    return ''; // use external default escaping
  },
  html: true
});

var path = require('path');
var config = require('config');
var services = require('../services');
var formatTime = require('../lib').formatTime;

function monthToEn(month) {
  switch(month) {
    case 1: return 'January';
    case 2: return 'Febrary';
    case 3: return 'March';
    case 4: return 'April';
    case 5: return 'May';
    case 6: return 'June';
    case 7: return 'July';
    case 8: return 'August';
    case 9: return 'September';
    case 10: return 'October';
    case 11: return 'November';
    case 12: return 'December';
  }
}

function numToCh(num) {
  switch(num) {
    case 0: return '日';
    case 1: return '一';
    case 2: return '二';
    case 3: return '三';
    case 4: return '四';
    case 5: return '五';
    case 6: return '六';
    case 7: return '七';
    case 8: return '八';
    case 9: return '九';
    case 10: return '十';
    case 11: return '十一';
    case 12: return '十二';
  }
}

function monthToCh(month) {
  return numToCh(month) + '月';
}

function dayToCh(day) {
  return '星期' + numToCh(day);
}

function dayToEn(day) {
  switch(day) {
    case 7: return 'Sunday';
    case 0: return 'Sunday';
    case 1: return 'Monday';
    case 2: return 'Tuesday';
    case 3: return 'Wednesday';
    case 4: return 'Thursday';
    case 5: return 'Friday';
    case 6: return 'Saturday';
  }
}

function transfer(blog) {
  blog = _.pick(blog, ['id', 'url', 'title', 'createTime', 'postTime', 'readNum', 'status', 'commentNum', 'abstract', 'cnblogId', 'cnodeId']);

  if (blog.abstract.length > 200) {
    blog.abstract = blog.abstract.substring(0, 200) + '...';
  }
  blog.href = '/blog/' + blog.url;
//  blog.headpic = '/pictures/hacker.jpg';
  return blog;
}

function calcCommentNum(comments) {
  var len = comments.length;
  comments.forEach((item) => {
    if (item.reply) {
      len += item.reply.length; 
    }
  });
  return len;
}

function recentCommentsView() {
  // 深复制
  let recents = JSON.parse(JSON.stringify(recentComments));
  recents.forEach((com, idx) => {
    com.postTime = formatTime(new Date(com.postTime));
    com.blogUrl = '/blog/' + com.blogUrl + '#comment';
    com.blogTitle = (idx+1) + '.Re:' + com.blogTitle;
    com.user = '--' + com.user;
  });
  return recents;
}

exports.tech = function*() {
  //var blogs = yield blog.getAllBlogs();
  var blogs = yield services.blogList({status: 'POST'});
  for(var i = 0; i < blogs.length; ++i) {
    blogs[i] = transfer(blogs[i]);

    blogs[i].yeartext = blogs[i].postTime.getFullYear();
    blogs[i].monthtext = monthToEn(blogs[i].postTime.getMonth() + 1);
    blogs[i].datetext = blogs[i].postTime.getDate();

    blogs[i].daytext = dayToEn(blogs[i].postTime.getDay());
    blogs[i].author = 'Luzeshu';

    blogs[i].postTime = formatTime(blogs[i].postTime);

    let comments = yield services.commentList(blogs[i].id);
    blogs[i].commentNum = calcCommentNum(comments);
  }


  var locals = getPersonalInfo();
  _.extend(locals, {
    headMajor: '大树先生的个人网站',
    headMinor: '技术博客',
    blogs: blogs,
    type: 'blogHome'
  });
  locals.host = '"' + locals.host + '"';
  yield this.render('tech-wrapper', locals);
}
exports.home = function*() {
  this.redirect('/tech');
}

exports.admin = function*() {
  yield this.render('admin');
}

exports.blogDetailPage = function*() {
  var blogInfo;
  if (!this.session.user) {
    blogInfo = yield services.blogInfo({url: this.params.url, status: 'POST'});
  } else {
    blogInfo = yield services.blogInfo({url: this.params.url});
  }

  if (!blogInfo) {
    this.status = 404;
    return ;
  }

  var content = md.render(blogInfo.content);
  blogInfo = transfer(blogInfo);

  blogInfo.yeartext = blogInfo.postTime.getFullYear();
  blogInfo.monthtext = blogInfo.postTime.getMonth() + 1;
  blogInfo.daytext = dayToCh(blogInfo.postTime.getDay());
  blogInfo.datetext = blogInfo.postTime.getDate();
  blogInfo.fulldatetext = blogInfo.daytext + ', ' + blogInfo.yeartext + '年' +  blogInfo.monthtext + '月' + blogInfo.datetext + '日';

  blogInfo.postTime = formatTime(blogInfo.postTime);

  blogInfo.content = content;
  blogInfo.author = 'Luzeshu';

  blogInfo.comments = [];
  var comments = yield services.commentList(blogInfo.id);

  blogInfo.commentNum = comments.length;
  comments.forEach((item) => {
    blogInfo.commentNum += item.reply.length;
    item.link = item.link ? item.link : 'javascript:void(0)';
    item.timetext = formatTime(item.postTime)

    for(var i = 0; i < item.reply.length; ++i) {
      item.reply[i].timetext = formatTime(new Date(item.reply[i].postTime));
      item.reply[i].head = item.reply[i].headpicurl ? item.reply[i].headpicurl : config.defaultHeadpic;
      item.reply[i].link = item.reply[i].link ? item.reply[i].link : "javascript:void(0)";
    }
  });

  blogInfo.comments = comments;
  
  var locals = getPersonalInfo();

  _.extend(locals, {
    blogInfo: blogInfo,
    type: 'blogPage'
  });
  if (config.server.port.https != 443) {
    locals.host += ':' + config.server.port.https;
  }
  yield this.render('tech-wrapper', locals);
}

function calcDays() {
  var oneHour = 60*60*1000;
  var oneDay = 24*oneHour; // hours*minutes*seconds*milliseconds
  var firstDate = new Date('6/30/2015');
  var secondDate = new Date();

  var diffDays = Math.round(Math.abs((firstDate.getTime() - secondDate.getTime())/(oneDay)));
  var diffHours = Math.round(Math.abs((firstDate.getTime() - secondDate.getTime())/(oneHour)));
  return {
    days: diffDays,
    hours: diffHours
  };
}

function getPersonalInfo() {
  var diff = calcDays();
  var locals = {
    title: config.info.webTitle,
    author: config.info.author,
    host: config.host,
    recentComments: recentCommentsView(),
    days: diff.days,
    hours: diff.hours,
    recordation: config.info.recordation,
    recordationUrl: config.info.recordationUrl
  };
  if (config.server.port.https != 443) {
    locals.host += ':' + config.server.port.https;
  }
  return locals;
}


exports.login = function*() {
  yield this.render('login');
}
