curl -sL https://deb.nodesource.com/setup_12.x | sudo -E bash -
apt-get install -y nodejs

npm install pm2 -g

# 
# install mysqldb
#
apt-get install mariadb-server

DB_PASSWORD='ce208f63-1f43-4bbe-a2ec-4764e7deb231'

# mysql -uroot -h localhost -e "drop user 'tree-blog'@'localhost';"
mysql -uroot -h localhost -e "create user 'tree-blog'@'localhost' identified by '$DB_PASSWORD';"

mysql -uroot -hlocalhost -e "create database treeblog default character set utf8 default collate utf8_general_ci"
mysql -uroot -hlocalhost -e "grant all on treeblog.* to 'tree-blog'@'localhost'"

#
# allow user blog listen to 80 and 443
#
# setcap command
apt-get install libcap2-bin
setcap 'CAP_NET_BIND_SERVICE=+ep' `which node`


#
# install certbot
#
apt-get install -y certbot
apt-get install -y tcl

# 导入自动更新证书
# root 身份
cp ./renewcert /etc/cron.monthly/tree-blog
chmod 755 /etc/cron.monthly/tree-blog
