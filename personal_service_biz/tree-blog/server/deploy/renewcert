#!/usr/bin/expect

spawn certbot certonly --webroot -w /data/deploy/tree-blog/websites -d luzeshu.com  --register-unsafely-without-email
expect "appropriate number"
send "2\n"
expect "Congratulations! Your certificate and chain have been saved"
spawn cp /etc/letsencrypt/live/luzeshu.com/fullchain.pem /etc/nginx/cert/luzeshu.com/letsencrypt-fullchain.pem
spawn cp /etc/letsencrypt/live/luzeshu.com/privkey.pem /etc/nginx/cert/luzeshu.com/letsencrypt-privkey.pem
spawn nginx -s reload
expect eof  # 这里不添加expect eof，则nginx -s reload没有实际成功就退出中断了。
