#!/bin/bash

# root 身份
# 当证书未过期时，需要手动输入1、2选择是否强制更新证书。当过期时无需输入，兼容两种情况，都输入"2\n"
certbot certonly --webroot -w /data/deploy/tree-blog/websites -d luzeshu.com  --register-unsafely-without-email || true # ignore error
# cp /etc/letsencrypt/live/luzeshu.com/fullchain.pem /data/deploy/tree-blog/server/cert/letsencrypt-fullchain.pem
# cp /etc/letsencrypt/live/luzeshu.com/privkey.pem /data/deploy/tree-blog/server/cert/letsencrypt-privkey.pem
# chown blog:blog /data/deploy/tree-blog/server/cert/*
# # blog 身份
# sudo -u blog pm2 restart tree-blog

cp /etc/letsencrypt/live/luzeshu.com/fullchain.pem /etc/nginx/cert/luzeshu.com/letsencrypt-fullchain.pem
cp /etc/letsencrypt/live/luzeshu.com/privkey.pem /etc/nginx/cert/luzeshu.com/letsencrypt-privkey.pem
