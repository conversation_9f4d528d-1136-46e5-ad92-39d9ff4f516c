#!/bin/bash

# mysql -uroot -hlocalhost -e "use treeblog; alter table Comments add column status enum('READ', 'UNREAD') not null;"

# mysql -uroot -hlocalhost -e "use treeblog; alter table Comments add column cnodeCommentId char(36) null;"
# mysql -uroot -hlocalhost -e "use treeblog; alter table Comments add column cnblogCommentId char(36) null;"


mysql -uroot -hlocalhost -e "use treeblog; ALTER DATABASE treeblog CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci; ALTER TABLE Comments CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
