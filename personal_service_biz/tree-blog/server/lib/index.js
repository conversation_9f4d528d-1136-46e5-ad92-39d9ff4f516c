

function _padZero(num) {
  if (num < 10) {
    return '0' + num;
  }
  return '' + num;
}

exports.formatTime = function (time) {
  if (!(time instanceof Date))
    return ;
  let year = _padZero(time.getFullYear());
  let month = _padZero(time.getMonth() + 1);
  let date = _padZero(time.getDate());
  let hours = _padZero(time.getHours());
  let minutes = _padZero(time.getMinutes());
  let seconds = _padZero(time.getSeconds());
  return year + '-' + month + '-' + date + ' ' + hours + ':' + minutes + ':' + seconds;
}
