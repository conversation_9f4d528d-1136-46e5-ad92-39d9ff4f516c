
var thirdvalidator = require('validator');

const ErrorEnum = {
  MISSPARAM: 1,
  TYPEERROR: 2,
  LENGTHERROR: 3,
  NOTNULL: 4
};
function ValidatorError(type, key) {
  this._validatorerror = true;
  this._type = type;
  this._key = key;
  var msg = '';
  if (this._type == ErrorEnum.MISSPARAM) {
    msg = '缺少参数';
  } else if (this._type == ErrorEnum.TYPEERROR) {
    msg = '类型错误';
  } else if (this._type == ErrorEnum.NOTNULL) {
    msg = '不允许为空';
  } else {
    msg = '长度错误';
  }
  this.desc = msg + ' : ' + this._key;
}

function Validator(schema) {
  this._schema = schema;
  this._from = {
    query: [],
    body: [],
    params: []
  };
  this._compile();
}

Validator.prototype._compile = function () {
  if (typeof this._schema !== 'object') {
    throw new Error('schema必须是一个object, 而不' + typeof this._schema);
  }
  this._checkSchemaIllegalKeys();
  this._checkSchemaProperties();
  this._checkSchema
};

const legalKeys = [
  'properties', 'required'
];
Validator.prototype._checkSchemaIllegalKeys = function () {
  for(var key in this._schema) {
    if (legalKeys.indexOf(key) < 0) {
      throw new Error('schema存在不合法的key: ' + key);
    }
  }
};

var supportTypes = [
  'string', 'integer', 'datetime', 'bool', 'email', 'url'
];
Validator.prototype._checkSchemaProperties = function () {
  if (typeof this._schema.properties !== 'object') {
    throw new Error('schema.properties 必须存在且必须是object');
  }
  for(var col in this._schema.properties) {
    var from = this._schema.properties[col].from;
    if (!from) {
      throw new Error('schema.properties[' + col + ']必须存在`from`[value=(body,query,params)]属性');
    }
    if (['body', 'query', 'params'].indexOf(from) < 0) {
      throw new Error('schema.properties[' + col + '] from的值必须是body、query、params');
    }
    this._from[from].push(col);
    var type = this._schema.properties[col].type;
    if (!type) {
      throw new Error('schema.properties 缺少type');
    }
    if (supportTypes.indexOf(type) <  0) {
      throw new Error('不支持的type：' + type);
    }

    if (this._schema.properties[col].oneof
          && !Array.isArray(this._schema.properties[col].oneof)) {
      throw new Error('oneof 必须是数组');      
    }
  }
};

Validator.prototype.validate = function (target) {
  try {
    for(var key in this._schema) {
      this['validate' + key.charAt(0).toUpperCase() + key.slice(1)](target);
    }
  } catch(err) {
    if (err._validatorerror) {
      return err;
    }
    throw err;
  }
};

Validator.prototype.validateProperties = function (target) {
  for(var from in target) {
    for(var field in target[from]) {
      if (this._from[from].indexOf(field) < 0) {
        console.warn('目标参数存在schema没有的字段' + field + '，已被清除');
        delete target[from][field];
      }
    }
  }

  for(var field in this._schema.properties) {
    var from = this._schema.properties[field].from;
    this.validatePropertiesIsrequire(target[from], field);
    this.validatePropertiesType(target[from], field);
    //this.validatePropertiesAllowNull(target[from], field);
    this.validatePropertiesDeps(target[from], field);
    this.validatePropertiesOneof(target[from], field);
  }
};

Validator.prototype.validatePropertiesIsrequire = function (target, field) {
  if (this._schema.properties[field].isrequire
      && target[field] === undefined) {
    throw new ValidatorError(ErrorEnum.MISSPARAM, field);
  }
};

Validator.prototype.validatePropertiesType = function (target, field) {
  if (this._schema.properties[field].type == 'integer') {
    
  } else if (this._schema.properties[field].type == 'string') {
    if (!this._schema.properties[field].allowNull
        && target[field] === '') { // 如果是undefined是允许的，由isrequired判断合法性
      throw new ValidatorError(ErrorEnum.NOTNULL, field);
    }
  } else if (this._schema.properties[field].type == 'email') {
    if (target[field] && !thirdvalidator.isEmail(target[field])) {
      throw new ValidatorError(ErrorEnum.TYPEERROR, field);
    }
  } else if (this._schema.properties[field].type == 'url') {
    if (target[field] && !thirdvalidator.isURL(target[field], {
      require_protocol: true
    })) {
      throw new ValidatorError(ErrorEnum.TYPEERROR, field);
    }
  }
};

Validator.prototype.validatePropertiesAllowNull = function (target, field) {
  if (this._schema.properties[field].allowNull == false
      && this._schema.properties[field].type == 'string'
      && !target[field]) {
    throw new ValidatorError(ErrorEnum.NOTNULL, field);
  }
};

Validator.prototype.validatePropertiesDeps = function (target, field) {

};

Validator.prototype.validatePropertiesOneof = function (target, field) {

};

module.exports = {
  Validator: Validator
};
