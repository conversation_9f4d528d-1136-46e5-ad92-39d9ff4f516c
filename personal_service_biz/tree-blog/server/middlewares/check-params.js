
var Validator = require('../lib/schemaValidator').Validator;

module.exports = function (schema) {
  var validator = new Validator(schema);
  return function *(next) {
    var reqParams = {
      body: this.request.body,
      query: this.request.query,
      params: this.params
    };
    var error = validator.validate(reqParams);
    if (!error) {
      this.validators = reqParams;
      yield next;
    } else {
      this.body = {
        code: error._type,
        msg: error.desc
      };
    }
  };
}
