/**
 * blog model
 */
'use strict';

module.exports = function (sequelize, DataTypes) {
  return sequelize.define('blog', {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4
    },
    url: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    title: {
      type: DataTypes.STRING(255)
    },
    content: {
      type: DataTypes.TEXT
    },
    readNum: {
      type: DataTypes.INTEGER(11),
      allowNull: false,
      defaultValue: 0
    },
    status: {
      type: DataTypes.ENUM('POST', 'UNPOST'),
      defaultValue: 'UNPOST',
      allowNull: false
    },
    commentNum: {
      type: DataTypes.INTEGER(11)
    },
    abstract: {
      type: DataTypes.TEXT
    },
    cnodeId: {
      type: DataTypes.STRING(50)
    },
    cnblogId: {
      type: DataTypes.INTEGER(11)
    },
    postTime: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      allowNull: false
    }
  }, {
    createdAt: 'createTime',
    updatedAt: 'updateTime',
    deletedAt: 'deleteTime',
    timestamp: true,
    paranoid: true
  });
};
