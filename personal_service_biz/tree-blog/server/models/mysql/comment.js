'use strict';

module.exports = function (sequelize, DataTypes) {
  return sequelize.define('Comment', {
    id: {
      type: DataTypes.UUID,
      primaryKey: true,
      defaultValue: DataTypes.UUIDV4
    }, 
    cnodeCommentId: {
      type: DataTypes.UUID,
      defaultValue: null
    },
    cnblogCommentId: {
      type: DataTypes.UUID,
      defaultValue: null
    },
    blogId: {
      type: DataTypes.UUID,
      allowNull: false
    },
    comment: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    user: {
      type: DataTypes.STRING(50),
      allowNull: false
    },
    email: {
      type: DataTypes.STRING(255)
    },
    link: {
      type: DataTypes.STRING(1024),
      comment: '评论人的个人主页链接'
    },
    reply: {
      type: DataTypes.UUID,
      comment: '回复哪条comment的id，空表示无'
    },
    fromCnblog: {
      type: DataTypes.BOOLEAN
    },
    fromCnode: {
      type: DataTypes.BOOLEAN
    },
    headpicurl: {
      type: DataTypes.STRING(1024)
    },
    postTime: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
      allowNull: false
    },
    status: {
      type: DataTypes.ENUM('READ', 'UNREAD'),
      defaultValue: 'UNREAD',
      allowNull: false
    }
  }, {
    createdAt: 'createTime',
    updatedAt: 'updateTime',
    deletedAt: 'deleteTime',
    timestamp: true,
    paranoid: true
  });
}
