/**
 * mysql index file
 */
'use strict';

const fs = require('co-fs');
const Sequelize = require('sequelize');
const config = require('config');

module.exports = function *init(db) {
  let sequelize = new Sequelize(
    config.mysql.dbname, 
    config.mysql.username,
    config.mysql.password, 
    {
      dialect: 'mysql',
      dialectOptions: {
        charset: 'utf8mb4'
      },
      host: config.mysql.host,
      port: config.mysql.port,
      // 有bug了
      // logging: config.mysql.log
    }
  );

  let files = yield fs.readdir(__dirname);
  files.filter((file) => {
    if (file === 'index.js')
      return false;
    return true;
  });

  db.Blog = require('./blog.js')(sequelize, Sequelize);
  yield db.Blog.sync();
  db.Comment = require('./comment.js')(sequelize, Sequelize);
  yield db.Comment.sync();
  db.User = require('./user.js')(sequelize, Sequelize);
  yield db.User.sync()
    .then(() => {
      return db.User.create({
        user: 'bigtree',
        password: 'silverbullet'
      });
    });

  return db;
};
