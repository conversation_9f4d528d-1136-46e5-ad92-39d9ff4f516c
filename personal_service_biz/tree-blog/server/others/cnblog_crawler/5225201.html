
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>node源码详解（二 ）—— 运行机制 、整体流程 - 野路子程序员 - 博客园</title>
<link type="text/css" rel="stylesheet" href="/bundles/blog-common.css?v=XrHjCqi-oUg9M4EcevxEUXvgm_dLNu97ZVKqK6ra-aE1"/>
<link id="MainCss" type="text/css" rel="stylesheet" href="/skins/sea/bundle-sea.css?v=PPe3jt1rcXPno6kucw0iQU2MWF3DbrvubQHRM0FEMas1"/>
<link id="mobile-style" media="only screen and (max-width: 768px)" type="text/css" rel="stylesheet" href="/skins/sea/bundle-sea-mobile.css?v=dCMFeRnDl5aeD8pIbxbwMGs8e9wn75b-E2rGISFE2uk1"/>
<link title="RSS" type="application/rss+xml" rel="alternate" href="http://www.cnblogs.com/papertree/rss"/>
<link title="RSD" type="application/rsd+xml" rel="EditURI" href="http://www.cnblogs.com/papertree/rsd.xml"/>
<link type="application/wlwmanifest+xml" rel="wlwmanifest" href="http://www.cnblogs.com/papertree/wlwmanifest.xml"/>
<script src="//common.cnblogs.com/script/jquery.js" type="text/javascript"></script>  
<script type="text/javascript">var currentBlogApp = 'papertree', cb_enable_mathjax=false;var isLogined=false;</script>
<script src="/bundles/blog-common.js?v=ufkHqY7CLaQ-FdEJCMtH3vR-Ia3VNX59Tf2LnMKYKak1" type="text/javascript"></script>
</head>
<body>
<a name="top"></a>

<!--done-->
<div id="header">
	
<!--done-->
<div class="header">
	<div class="headerText">
		<a id="Header1_HeaderTitle" class="headermaintitle" href="http://www.cnblogs.com/papertree/">大树先生</a><br>
		
	</div>
</div>

</div>

<div id="mytopmenu" >
	
		<div id="mylinks"><a id="blog_nav_sitehome" class="menu" href="http://www.cnblogs.com/">博客园</a> &nbsp;
<a id="blog_nav_myhome" class="menu" href="http://www.cnblogs.com/papertree/">首页</a> &nbsp;
<a id="blog_nav_newpost" class="menu" rel="nofollow" href="https://i.cnblogs.com/EditPosts.aspx?opt=1">新随笔</a> &nbsp;
<a id="blog_nav_contact" class="menu" rel="nofollow" href="https://msg.cnblogs.com/send/%E9%87%8E%E8%B7%AF%E5%AD%90%E7%A8%8B%E5%BA%8F%E5%91%98">联系</a> &nbsp;
<a id="blog_nav_rss" class="menu" href="http://www.cnblogs.com/papertree/rss">订阅</a><a id="blog_nav_rss_image" href="http://www.cnblogs.com/papertree/rss"><img src="//www.cnblogs.com/images/xml.gif" alt="订阅" /></a>&nbsp;
<a id="blog_nav_admin" class="menu" rel="nofollow" href="https://i.cnblogs.com/">管理</a>
</div>
		<DIV id="mystats"><div id="blog_stats">
随笔-9&nbsp;
评论-14&nbsp;
文章-0&nbsp;
<!--trackbacks-0-->
</div></DIV>
	
</div>
<div id="centercontent" >
	
<div id="post_detail">
<div class = "post">
	<h1 class = "postTitle"><a id="cb_post_title_url" class="postTitle2" href="http://www.cnblogs.com/papertree/p/5225201.html">node源码详解（二 ）—— 运行机制 、整体流程</a></h1>
	<div id="cnblogs_post_body"><p>&nbsp;</p>
<p>声明：转载请保留声明头部并标明转载、并私信告知作者。原文：<a href="http://www.cnblogs.com/papertree/p/5225201.html" target="_blank">http://www.cnblogs.com/papertree/p/5225201.html</a></p>
<hr />
<h1>&nbsp;2.1 项目代码结构</h1>
<p>　　node 主要的部分有4个【下图最左列就是node项目源码（4.2.2）的根目录】：</p>
<p>&nbsp;1. 原生 js模块：node提供给 用户js 代码的类接口，平时用的require('fs')、require('http')调用的都是这部分的代码。【最左列的 lib文件夹，展开后是左二列】</p>
<p>&nbsp;2. node 源码：node程序的main函数入口；还有提供给lib模块的C++类接口。【最左列的 src 文件夹，展开后是第三列】</p>
<p>&nbsp;3. v8引擎：node用来解析、执行js代码的运行环境。【最左列的deps文件夹展开后是第四列，v8和libuv等依赖都放在这里】</p>
<p>&nbsp;4. libuv：事件循环库，提供最底层的 io操作接口（包括网络io操作的epoll_wait()、文件异步io的线程池管理）、事件循环逻辑。 【第四列的uv文件夹，展开后是第五列】</p>
<p>&nbsp;</p>
<p>记住这几个路径：</p>
<p>./lib</p>
<p>./src</p>
<p>./deps/uv</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160312114550819-1838732446.png" alt="" /></p>
<p style="text-align: center;">图2-1-1</p>
<p>&nbsp;</p>
<hr />
<p>&nbsp;</p>
<h1>2.2 运行流程</h1>
<p>　　下图4个红色序号分别对应着上篇博客提出的4个问题所在位置。后续博客分说。</p>
<p>　　接下来对一些关键地方进行说明：</p>
<p>1. 核心数据结构 default_loop_struct （结构体为struct uv_loop_s，后续祥讲）</p>
<p>　　这个数据结构是事件循环的核心。<span style="line-height: 1.5;">当node执行到&ldquo;加载js文件&rdquo;这个步骤（结合下图）时，</span>用户的js代码如果有io操作：</p>
<p>　　那么js代码通过调用 -》lib模块（2.1 中的原生js模块）-》C++模块（2.1中的源码部分） -》 libuv接口（2.1中deps/uv部分） -》最终的系统api，拿到系统返回的一个fd（文件描述符），和 js代码传进来的回调函数callback，封装成一个io观察者（一个uv__io_s类型的对象），保存到default_loop_struct；</p>
<p>&nbsp;</p>
<p>2. 进入事件循环</p>
<p>　　当处理完 js代码，如果有io操作，那么这时default_loop_struct是保存着对应的io观察者的。</p>
<p>　　处理完js代码，main函数继续往下调用libuv的事件循环入口uv_run()，node进程进入事件循环：</p>
<p>　　uv_run()的while循环做的就是一件事，判断default_loop_struct是否有存活的io观察者。</p>
<p>　　　　a. 如果没有io观察者，那么uv_run()退出，node进程退出。</p>
<p>　　　　b. 而如果有io观察者，那么uv_run()进入epoll_wait()，线程挂起等待，监听对应的io观察者是否有数据到来。有数据到来调用io观察者里保存着的callback（js代码），没有数据到来时一直在epoll_wait()进行等待。</p>
<p>　　这里解答了博客（一）的&ldquo;问题2&rdquo;的一部分：为什么console.log()的js代码导致node退出，而server.listen(80)导致线程挂起等待。</p>
<p>&nbsp;</p>
<p>3.&nbsp;<span style="line-height: 1.5;">这里一旦没搞清逻辑就有个疑问：</span></p>
<p>　　第2点说在uv_run()里面如果监听的io观察者有数据到来，那么调用对应的callback，执行js代码。如果没有数据到来，一直在epoll_wait()等待。那如果我js代码里面有新的 io操作想要交给epoll_wait()进行监听，而此刻监听着的io观察者又没有数据到来，线程一直在这里等待，那怎么办？</p>
<p>　　首先第1点讲到，执行js代码的时候，通过调用node提供的C++接口最终把io观察者都保存到default_loop_struct里面，js代码执行完之后，node继续运行才进入epoll_wait()等待。也就是说node在epoll_wait()的时候，js代码执行完毕了。而js代码的回调函数部分，本来的设定就是在epoll_wait()监听的io观察者被触发之后才会执行回调，在epoll_wait()进行等待的时候，不可能存在&ldquo;有新io操作要交给epoll_wait()去监听&rdquo;这样的js代码。</p>
<p>　　</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160312120153069-1142333435.png" alt="" /></p>
<p style="text-align: center;">图2-2-1</p></div><div id="MySignature"></div>
<div class="clear"></div>
<div id="blog_post_info_block">
<div id="BlogPostCategory"></div>
<div id="EntryTag"></div>
<div id="blog_post_info">
</div>
<div class="clear"></div>
<div id="post_next_prev"></div>
</div>


	<div class = "postDesc">posted on <span id="post-date">2016-03-12 14:01</span> <a href='http://www.cnblogs.com/papertree/'>野路子程序员</a> 阅读(<span id="post_view_count">...</span>) 评论(<span id="post_comment_count">...</span>)  <a href ="https://i.cnblogs.com/EditPosts.aspx?postid=5225201" rel="nofollow">编辑</a> <a href="#" onclick="AddToWz(5225201);return false;">收藏</a></div>
</div>
<script type="text/javascript">var allowComments=true,cb_blogId=242729,cb_entryId=5225201,cb_blogApp=currentBlogApp,cb_blogUserGuid='40a171c9-d74e-e511-b908-9dcfd8948a71',cb_entryCreatedDate='2016/3/12 14:01:00';loadViewCount(cb_entryId);</script>

</div><a name="!comments"></a><div id="blog-comments-placeholder"></div><script type="text/javascript">var commentManager = new blogCommentManager();commentManager.renderComments(0);</script>
<div id='comment_form' class='commentform'>
<a name='commentform'></a>
<div id='divCommentShow'></div>
<div id='comment_nav'><span id='span_refresh_tips'></span><a href='javascript:void(0);' onclick='return RefreshCommentList();' id='lnk_RefreshComments' runat='server' clientidmode='Static'>刷新评论</a><a href='#' onclick='return RefreshPage();'>刷新页面</a><a href='#top'>返回顶部</a></div>
<div id='comment_form_container'></div>
<div class='ad_text_commentbox' id='ad_text_under_commentbox'></div>
<div id='ad_t2'></div>
<div id='opt_under_post'></div>
<div id='ad_c1' class='c_ad_block'></div>
<div id='under_post_news'></div>
<div id='ad_c2' class='c_ad_block'></div>
<div id='under_post_kb'></div>
<div id='HistoryToday' class='c_ad_block'></div>
<script type='text/javascript'>
    fixPostBody();
    setTimeout(function () { incrementViewCount(cb_entryId); }, 50);
    deliverAdT2();
    deliverAdC1();
    deliverAdC2();    
    loadNewsAndKb();
    loadBlogSignature();
    LoadPostInfoBlock(cb_blogId, cb_entryId, cb_blogApp, cb_blogUserGuid);
    GetPrevNextPost(cb_entryId, cb_blogId, cb_entryCreatedDate);
    loadOptUnderPost();
    GetHistoryToday(cb_blogId, cb_blogApp, cb_entryCreatedDate);   
</script>
</div>


</div>
<div id="leftcontent">
	
		<DIV id="leftcontentcontainer">
			
<!--done-->
<div class="newsItem">
	<div id="blog-news"></div><script type="text/javascript">loadBlogNews();</script>
</div>

			<div id="blog-calendar" style="display:none"></div><script type="text/javascript">loadBlogDefaultCalendar();</script><br>
			<div id="blog-sidecolumn"></div><script type="text/javascript">loadBlogSideColumn();</script></DIV>
	
</div>

<!--done-->
<div class="footer">
	Powered by: <a href="http://www.cnblogs.com">博客园</a>	模板提供：<a href="http://blog.hjenglish.com">沪江博客</a>
	Copyright &copy;2016 野路子程序员
</div>



</body>
</html>
