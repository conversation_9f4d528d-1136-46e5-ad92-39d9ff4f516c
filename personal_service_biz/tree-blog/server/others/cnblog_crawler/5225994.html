
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>node源码详解（三）—— js代码在node中的位置，process、require、module、exports的由来 - 野路子程序员 - 博客园</title>
<link type="text/css" rel="stylesheet" href="/bundles/blog-common.css?v=XrHjCqi-oUg9M4EcevxEUXvgm_dLNu97ZVKqK6ra-aE1"/>
<link id="MainCss" type="text/css" rel="stylesheet" href="/skins/sea/bundle-sea.css?v=PPe3jt1rcXPno6kucw0iQU2MWF3DbrvubQHRM0FEMas1"/>
<link id="mobile-style" media="only screen and (max-width: 768px)" type="text/css" rel="stylesheet" href="/skins/sea/bundle-sea-mobile.css?v=dCMFeRnDl5aeD8pIbxbwMGs8e9wn75b-E2rGISFE2uk1"/>
<link title="RSS" type="application/rss+xml" rel="alternate" href="http://www.cnblogs.com/papertree/rss"/>
<link title="RSD" type="application/rsd+xml" rel="EditURI" href="http://www.cnblogs.com/papertree/rsd.xml"/>
<link type="application/wlwmanifest+xml" rel="wlwmanifest" href="http://www.cnblogs.com/papertree/wlwmanifest.xml"/>
<script src="//common.cnblogs.com/script/jquery.js" type="text/javascript"></script>  
<script type="text/javascript">var currentBlogApp = 'papertree', cb_enable_mathjax=false;var isLogined=false;</script>
<script src="/bundles/blog-common.js?v=ufkHqY7CLaQ-FdEJCMtH3vR-Ia3VNX59Tf2LnMKYKak1" type="text/javascript"></script>
</head>
<body>
<a name="top"></a>

<!--done-->
<div id="header">
	
<!--done-->
<div class="header">
	<div class="headerText">
		<a id="Header1_HeaderTitle" class="headermaintitle" href="http://www.cnblogs.com/papertree/">大树先生</a><br>
		
	</div>
</div>

</div>

<div id="mytopmenu" >
	
		<div id="mylinks"><a id="blog_nav_sitehome" class="menu" href="http://www.cnblogs.com/">博客园</a> &nbsp;
<a id="blog_nav_myhome" class="menu" href="http://www.cnblogs.com/papertree/">首页</a> &nbsp;
<a id="blog_nav_newpost" class="menu" rel="nofollow" href="https://i.cnblogs.com/EditPosts.aspx?opt=1">新随笔</a> &nbsp;
<a id="blog_nav_contact" class="menu" rel="nofollow" href="https://msg.cnblogs.com/send/%E9%87%8E%E8%B7%AF%E5%AD%90%E7%A8%8B%E5%BA%8F%E5%91%98">联系</a> &nbsp;
<a id="blog_nav_rss" class="menu" href="http://www.cnblogs.com/papertree/rss">订阅</a><a id="blog_nav_rss_image" href="http://www.cnblogs.com/papertree/rss"><img src="//www.cnblogs.com/images/xml.gif" alt="订阅" /></a>&nbsp;
<a id="blog_nav_admin" class="menu" rel="nofollow" href="https://i.cnblogs.com/">管理</a>
</div>
		<DIV id="mystats"><div id="blog_stats">
随笔-9&nbsp;
评论-14&nbsp;
文章-0&nbsp;
<!--trackbacks-0-->
</div></DIV>
	
</div>
<div id="centercontent" >
	
<div id="post_detail">
<div class = "post">
	<h1 class = "postTitle"><a id="cb_post_title_url" class="postTitle2" href="http://www.cnblogs.com/papertree/p/5225994.html">node源码详解（三）—— js代码在node中的位置，process、require、module、exports的由来</a></h1>
	<div id="cnblogs_post_body"><p>&nbsp;</p>
<p>声明：转载请保留声明头部并标明转载、并私信告知作者。原文：<a href="http://www.cnblogs.com/papertree/p/5225994.html" target="_blank">http://www.cnblogs.com/papertree/p/5225994.html</a></p>
<hr />
<p>　　我们用惯了process.nextTick()、require('xxx')、module.exports，但是它们哪里来呢？下文给出答案...&nbsp;</p>
<h1>3.1 node main函数到执行js文件的位置</h1>
<h2>3.1.1 入口&nbsp;</h2>
<p>　　上篇博客2.1中提到src目录存放的是node的C++源码部分，包括main函数。</p>
<p>　　src/node_main.cc 和 src/node.cc（注：.cc是linux下的C++文件，类似windows下的.cpp）这两个文件就是node的入口文件。</p>
<p>　　其中node_main.cc里面仅仅作为一个入口，调用node.cc 文件中的node::Start()。</p>
<p>　　<img src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160312155848757-1860080505.png" alt="" /></p>
<p>　　　　　　　　　　　　图3-1-1</p>
<p>&nbsp;</p>
<h2>3.1.2 node::Start()到加载js文件</h2>
<p>&nbsp;　　有兴趣可以看一下node::Start()函数做些什么（代码截图放上来了），我们关注的只是里面的StartNodeInstance()这一行。</p>
<p>　　<img src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160312160409991-1446668257.png" alt="" /></p>
<p>　　　　　　　　　　　　　　　　　　　　　　　　　　　　　图3-1-2</p>
<p>　　我们来看一下一连串的调用：</p>
<p>　　　　Start() -&gt; StartNodeInstance() -&gt; LoadEnviroment() -&gt; ExecuteString()</p>
<p>　　这四个函数都在node.cc文件里面，看到LoadEnviroment() 几行关键代码：</p>
<p>　　<img src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160312161229194-1478502695.png" alt="" /></p>
<p>　　　　　　　　　　　　　　　　　　　　　　　　　　图3-1-3</p>
<p>　　最终在LoadEnvrioment()里面加载node.js文件，调用ExecuteString()。</p>
<p>　　并且在ExecuteString()调用V8的 Script::Compile() 和 Script::Run()两个接口去解析执行js代码。</p>
<p>&nbsp;</p>
<h2>3.1.3 node.js文件和用户的app.js文件</h2>
<p>　　通过命令行&ldquo;node app.js&rdquo;启动，我们希望node执行的是app.js 文件，为什么LoadEnvironment()里面加载的是node.js文件呢？</p>
<p>　　上篇博客2.1 讲到node的lib文件夹存放原生js模块，而src文件夹里面全部是.cc文件（node C++源码） ，但src下面有一个node.js文件。这个文件的作用是什么呢？</p>
<p>&nbsp;</p>
<p>&nbsp;</p>
<hr />
<h1>&nbsp;3.2 node.js文件，包装app.js文件，require/module.exports</h1>
<p>&nbsp;　　先来看node.js文件的结构：</p>
<p>　　<img src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160312165136460-1359163524.png" alt="" /></p>
<p>　　　　　　　　　　　　　　　　　　　　　　图 3-2-1</p>
<h2>3.2.1 node.js 返回的是一个函数</h2>
<p>　　看到3.1.2 中，在LoadEnvironment()里面调用ExecuteString() 解析执行node.js文件，返回值是一个f_value。</p>
<p>　　而这个f_value通过V8的接口 Local&lt;Function&gt;::Cast转换成一个Local&lt;Function&gt;类型的变量f ，而Local&lt;Function&gt;类型是V8中表示一个js 函数的C++类型。</p>
<p>　　在LoadEnvironment()的最后一行通过 f-&gt;Call()，去执行node.js返回来的一个函数。</p>
<p>　　而从node.js文件中也可以看出确实返回的是一个匿名函数。</p>
<p>　　所以，我们的app.js并不在ExecuteString()里面执行，而是在f-&gt;Call()的时候被执行。</p>
<p>&nbsp;</p>
<h2>3.2.2 LoadEnvironment()之f-&gt;Call()与 node.js之startup()</h2>
<p>　　上面说到f-&gt;Call()的时候，是在V8里面解析执行了node.js返回来的匿名函数，来看一下该函数（图3-2-1）。</p>
<p>步骤1. &nbsp;匿名函数的第一个步骤是给startup()函数挂载一堆初始化函数，比如processNext()。</p>
<p>步骤2. &nbsp;startup的相关初始化函数都挂载完之后，匿名函数执行最后一步，调用startup()。</p>
<p>步骤3. &nbsp;看到startup()函数的定义，里面开始去执行这一堆挂载函数。processNext()里面所做的就是给process.nextTick赋值。<span style="color: #ff0000;">就是我们平时用的process.nextTick了</span>。</p>
<p><span style="line-height: 1.5;">　　　　　　注：匿名函数的参数process是C++模块传进来的对象（也就是f-&gt;Call()传进来的参数）。</span></p>
<p>步骤4. &nbsp;startup()函数在执行完匿名函数挂载的一堆初始化函数之后，继而执行Module.runMain()。</p>
<p>&nbsp;</p>
<h2>3.2.3 Module.runMain()包装app.js文件</h2>
<p>　　看到startup()函数在执行runMain()之前，对process.argv[1]里做了一下处理，这个argv就是node命令行启动的时候，main函数接收的参数。比如&ldquo;node app.js&rdquo;启动，argv[1]就保存着我们的js文件名了。</p>
<p>　　那么runMain()通过process.argv[1]去读取app.js文件，读成字符串，调用NativeModule.wrap()函数（图3-2-1），把我们的app.js代码包在里面。</p>
<p>　　<span style="color: #ff0000; background-color: #ffffff;">这里就可以说明app.js代码里面为何生来就有require、exports、module这几个变量可以用了</span>。其实是被放到一个 js函数里面了。</p>
<p>　　最后runMain()里面也是调用V8的Compile()、Run()等接口，去执行app.js代码。</p></div><div id="MySignature"></div>
<div class="clear"></div>
<div id="blog_post_info_block">
<div id="BlogPostCategory"></div>
<div id="EntryTag"></div>
<div id="blog_post_info">
</div>
<div class="clear"></div>
<div id="post_next_prev"></div>
</div>


	<div class = "postDesc">posted on <span id="post-date">2016-03-12 17:19</span> <a href='http://www.cnblogs.com/papertree/'>野路子程序员</a> 阅读(<span id="post_view_count">...</span>) 评论(<span id="post_comment_count">...</span>)  <a href ="https://i.cnblogs.com/EditPosts.aspx?postid=5225994" rel="nofollow">编辑</a> <a href="#" onclick="AddToWz(5225994);return false;">收藏</a></div>
</div>
<script type="text/javascript">var allowComments=true,cb_blogId=242729,cb_entryId=5225994,cb_blogApp=currentBlogApp,cb_blogUserGuid='40a171c9-d74e-e511-b908-9dcfd8948a71',cb_entryCreatedDate='2016/3/12 17:19:00';loadViewCount(cb_entryId);</script>

</div><a name="!comments"></a><div id="blog-comments-placeholder"></div><script type="text/javascript">var commentManager = new blogCommentManager();commentManager.renderComments(0);</script>
<div id='comment_form' class='commentform'>
<a name='commentform'></a>
<div id='divCommentShow'></div>
<div id='comment_nav'><span id='span_refresh_tips'></span><a href='javascript:void(0);' onclick='return RefreshCommentList();' id='lnk_RefreshComments' runat='server' clientidmode='Static'>刷新评论</a><a href='#' onclick='return RefreshPage();'>刷新页面</a><a href='#top'>返回顶部</a></div>
<div id='comment_form_container'></div>
<div class='ad_text_commentbox' id='ad_text_under_commentbox'></div>
<div id='ad_t2'></div>
<div id='opt_under_post'></div>
<div id='ad_c1' class='c_ad_block'></div>
<div id='under_post_news'></div>
<div id='ad_c2' class='c_ad_block'></div>
<div id='under_post_kb'></div>
<div id='HistoryToday' class='c_ad_block'></div>
<script type='text/javascript'>
    fixPostBody();
    setTimeout(function () { incrementViewCount(cb_entryId); }, 50);
    deliverAdT2();
    deliverAdC1();
    deliverAdC2();    
    loadNewsAndKb();
    loadBlogSignature();
    LoadPostInfoBlock(cb_blogId, cb_entryId, cb_blogApp, cb_blogUserGuid);
    GetPrevNextPost(cb_entryId, cb_blogId, cb_entryCreatedDate);
    loadOptUnderPost();
    GetHistoryToday(cb_blogId, cb_blogApp, cb_entryCreatedDate);   
</script>
</div>


</div>
<div id="leftcontent">
	
		<DIV id="leftcontentcontainer">
			
<!--done-->
<div class="newsItem">
	<div id="blog-news"></div><script type="text/javascript">loadBlogNews();</script>
</div>

			<div id="blog-calendar" style="display:none"></div><script type="text/javascript">loadBlogDefaultCalendar();</script><br>
			<div id="blog-sidecolumn"></div><script type="text/javascript">loadBlogSideColumn();</script></DIV>
	
</div>

<!--done-->
<div class="footer">
	Powered by: <a href="http://www.cnblogs.com">博客园</a>	模板提供：<a href="http://blog.hjenglish.com">沪江博客</a>
	Copyright &copy;2016 野路子程序员
</div>



</body>
</html>
