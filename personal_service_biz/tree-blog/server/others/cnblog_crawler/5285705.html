
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>node源码详解（四） —— js代码如何调用C++的函数 - 野路子程序员 - 博客园</title>
<link type="text/css" rel="stylesheet" href="/bundles/blog-common.css?v=XrHjCqi-oUg9M4EcevxEUXvgm_dLNu97ZVKqK6ra-aE1"/>
<link id="MainCss" type="text/css" rel="stylesheet" href="/skins/sea/bundle-sea.css?v=PPe3jt1rcXPno6kucw0iQU2MWF3DbrvubQHRM0FEMas1"/>
<link id="mobile-style" media="only screen and (max-width: 768px)" type="text/css" rel="stylesheet" href="/skins/sea/bundle-sea-mobile.css?v=dCMFeRnDl5aeD8pIbxbwMGs8e9wn75b-E2rGISFE2uk1"/>
<link title="RSS" type="application/rss+xml" rel="alternate" href="http://www.cnblogs.com/papertree/rss"/>
<link title="RSD" type="application/rsd+xml" rel="EditURI" href="http://www.cnblogs.com/papertree/rsd.xml"/>
<link type="application/wlwmanifest+xml" rel="wlwmanifest" href="http://www.cnblogs.com/papertree/wlwmanifest.xml"/>
<script src="//common.cnblogs.com/script/jquery.js" type="text/javascript"></script>  
<script type="text/javascript">var currentBlogApp = 'papertree', cb_enable_mathjax=false;var isLogined=false;</script>
<script src="/bundles/blog-common.js?v=ufkHqY7CLaQ-FdEJCMtH3vR-Ia3VNX59Tf2LnMKYKak1" type="text/javascript"></script>
</head>
<body>
<a name="top"></a>

<!--done-->
<div id="header">
	
<!--done-->
<div class="header">
	<div class="headerText">
		<a id="Header1_HeaderTitle" class="headermaintitle" href="http://www.cnblogs.com/papertree/">大树先生</a><br>
		
	</div>
</div>

</div>

<div id="mytopmenu" >
	
		<div id="mylinks"><a id="blog_nav_sitehome" class="menu" href="http://www.cnblogs.com/">博客园</a> &nbsp;
<a id="blog_nav_myhome" class="menu" href="http://www.cnblogs.com/papertree/">首页</a> &nbsp;
<a id="blog_nav_newpost" class="menu" rel="nofollow" href="https://i.cnblogs.com/EditPosts.aspx?opt=1">新随笔</a> &nbsp;
<a id="blog_nav_contact" class="menu" rel="nofollow" href="https://msg.cnblogs.com/send/%E9%87%8E%E8%B7%AF%E5%AD%90%E7%A8%8B%E5%BA%8F%E5%91%98">联系</a> &nbsp;
<a id="blog_nav_rss" class="menu" href="http://www.cnblogs.com/papertree/rss">订阅</a><a id="blog_nav_rss_image" href="http://www.cnblogs.com/papertree/rss"><img src="//www.cnblogs.com/images/xml.gif" alt="订阅" /></a>&nbsp;
<a id="blog_nav_admin" class="menu" rel="nofollow" href="https://i.cnblogs.com/">管理</a>
</div>
		<DIV id="mystats"><div id="blog_stats">
随笔-9&nbsp;
评论-14&nbsp;
文章-0&nbsp;
<!--trackbacks-0-->
</div></DIV>
	
</div>
<div id="centercontent" >
	
<div id="post_detail">
<div class = "post">
	<h1 class = "postTitle"><a id="cb_post_title_url" class="postTitle2" href="http://www.cnblogs.com/papertree/p/5285705.html">node源码详解（四） —— js代码如何调用C++的函数</a></h1>
	<div id="cnblogs_post_body"><p>声明：转载请保留声明头部并标明转载、并私信告知作者。原文：<a href="http://www.cnblogs.com/papertree/p/5285705.html" target="_blank">http://www.cnblogs.com/papertree/p/5285705.html</a></p>
<hr />
<p>　　上面讲到node调用Script::Compile()和Script::Run()解析执行app.js，并把io操作和callback保存到default_loop_struct，那么app.js里面js代码如何调用C++的函数呢？</p>
<p>　　在4.2节进行解释，先在4.1节来点知识预热。</p>
<h1>4.1&nbsp;V8运行js代码的基础知识 &mdash;&mdash; V8的上下文</h1>
<p>　　来看看google V8开发者文档的一点介绍：（地址：<a href="https://developers.google.com/v8/get_started" target="_blank">https://developers.google.com/v8/get_started</a>）</p>
<ul>
<li>A&nbsp;<em>context</em>&nbsp;is an execution environment that allows separate, unrelated, JavaScript code to run in a single instance of V8. You must explicitly specify the context in which you want any JavaScript code to be run.</li>
</ul>
<p><span style="line-height: 1.5;">　　大概意思就是context（上下文）是用来执行javascript代码的运行环境，而且运行javascript代码的时候必须指定一个context。</span></p>
<p><span style="line-height: 1.5;">　　从文档里面摘了一段hello world代码：</span></p>
<div class="cnblogs_code">
<pre><span style="color: #0000ff;">int</span> main(<span style="color: #0000ff;">int</span> argc, <span style="color: #0000ff;">char</span>*<span style="color: #000000;"> argv[]) {
  </span><span style="color: #008000;">//</span><span style="color: #008000;"> Initialize V8.</span>
<span style="color: #000000;">  V8::InitializeICU();
  V8::InitializeExternalStartupData(argv[</span><span style="color: #800080;">0</span><span style="color: #000000;">]);
  Platform</span>* platform =<span style="color: #000000;"> platform::CreateDefaultPlatform();
  V8::InitializePlatform(platform);
  V8::Initialize();

  </span><span style="color: #008000;">//</span><span style="color: #008000;"> Create a new Isolate and make it the current one.</span>
<span style="color: #000000;">  ArrayBufferAllocator allocator;
  Isolate::CreateParams create_params;
  create_params.array_buffer_allocator </span>= &amp;<span style="color: #000000;">allocator;
  Isolate</span>* isolate =<span style="color: #000000;"> Isolate::New(create_params);
  {
    Isolate::Scope isolate_scope(isolate);

    </span><span style="color: #008000;">//</span><span style="color: #008000;"> Create a stack-allocated handle scope.</span>
<span style="color: #000000;">    HandleScope handle_scope(isolate);

    </span><span style="color: #008000;">//</span><span style="color: #008000;"> Create a new context.</span>
    Local&lt;Context&gt; context =<span style="color: #000000;"> Context::New(isolate);

    </span><span style="color: #008000;">//</span><span style="color: #008000;"> Enter the context for compiling and running the hello world script.</span>
<span style="color: #000000;">    Context::Scope context_scope(context);

    </span><span style="color: #008000;">//</span><span style="color: #008000;"> Create a string containing the JavaScript source code.</span>
    Local&lt;String&gt; source =<span style="color: #000000;">
        String::NewFromUtf8(isolate, </span><span style="color: #800000;">"</span><span style="color: #800000;">'Hello' + ', World!'</span><span style="color: #800000;">"</span><span style="color: #000000;">,
                            NewStringType::kNormal).ToLocalChecked();

    </span><span style="color: #008000;">//</span><span style="color: #008000;"> Compile the source code.</span>
    Local&lt;Script&gt; script =<span style="color: #000000;"> Script::Compile(context, source).ToLocalChecked();

    </span><span style="color: #008000;">//</span><span style="color: #008000;"> Run the script to get the result.</span>
    Local&lt;Value&gt; result = script-&gt;<span style="color: #000000;">Run(context).ToLocalChecked();

    </span><span style="color: #008000;">//</span><span style="color: #008000;"> Convert the result to an UTF8 string and print it.</span>
<span style="color: #000000;">    String::Utf8Value utf8(result);
    printf(</span><span style="color: #800000;">"</span><span style="color: #800000;">%s\n</span><span style="color: #800000;">"</span>, *<span style="color: #000000;">utf8);
  }

  </span><span style="color: #008000;">//</span><span style="color: #008000;"> Dispose the isolate and tear down V8.</span>
  isolate-&gt;<span style="color: #000000;">Dispose();
  V8::Dispose();
  V8::ShutdownPlatform();
  </span><span style="color: #0000ff;">delete</span><span style="color: #000000;"> platform;
  </span><span style="color: #0000ff;">return</span> <span style="color: #800080;">0</span><span style="color: #000000;">;
}</span></pre>
</div>
<p>&nbsp;</p>
<p>　　你可能会发现，上面说了script-&gt;Run(context)&nbsp;一定要指定一个context。那么看回3.1.2 中的图3-1-3，node.cc里面的script-&gt;Run()并没有context参数。</p>
<p>　　跳到v8的源码，deps/v8/src/api.cc，就会发现这实际上是两个重载函数，无参Script::Run()会先从Script对象取得当前的context，再调用Script::Run(Local&lt;Context&gt; context)。</p>
<p>　　<img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160317225530506-268409836.png" alt="" />&nbsp;</p>
<p style="text-align: center;">图4-1-1</p>
<p>&nbsp;</p>
<hr />
<p>&nbsp;</p>
<h1>4.2 理解js代码如何调用C++函数 &mdash;&mdash; 运行时的上下文</h1>
<p>　　看个例子：</p>
<p>　　左边为node 原生lib模块网络socket操作部分的文件&nbsp;&mdash;&mdash; net.js，我们平时使用server.listen()时，最终调用到net.js里面，先通过new TCP()创建一个handle对象，再调用handle.listen()。而这个TCP和listen，均来自左边tcp_wrap.cc文件。</p>
<p>　　也就是说，通过net.js里面的handle.listen()调用了tcp_wrap.cc里面的TCPWrap::Listen()函数，并且传给handle.listen()的 js参数&mdash;&mdash; backlog，被包装到了C++的 FunctionCallbackInfo&lt;Value&gt;类对象args。</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160318100646881-1607348552.png" alt="" /></p>
<p style="text-align: center;">图4-2-1&nbsp;</p>
<p>&nbsp;</p>
<p>　　如果你第一感觉是js代码调用C++代码无法理解，那么一定是受到&ldquo;语法&rdquo;的干扰。</p>
<p>　　确实，从静态的角度来看，js和C++是两种语言，语法不互通，直接在js代码调用C++函数那是不可能的。</p>
<p>　　那么，从动态的角度（运行时）来看呢？别忘了，任何编程语言最终运行起来都不过是进程空间里的二进制代码和数据。</p>
<p>&nbsp;</p>
<p>&nbsp;　　<img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160318142859474-1685569848.png" alt="" /></p>
<p style="text-align: center;">图 4-2-2</p>
<h2>4.2.1 从js代码到context</h2>
<p>　　4.1 中已经讲了，Script::Compile()和Script::Run() 的时候必须为 js代码指定一个运行环境（context）。那么 js代码和context的关联是很自然的。</p>
<h2>4.2.2 设置C++函数到context</h2>
<p>　　那么，上图蓝色标号1-5这几个步骤，即在C++代码层面，把C++函数设置到context的细节和相应的V8 接口是什么呢？</p>
<p>&nbsp;</p>
<hr />
<h1>4.3 node的js模块调用C++模块的细节</h1>
<p>　　在node里面，在C++代码里面提供给运行时javascript代码使用的无非就是这几种：</p>
<p>　　1.&nbsp;一个对象（比如process），对象上设置属性（比如process.versions）、或者方法（比如process._kill）</p>
<p>　　2.&nbsp;函数对象（比如TCP），设置原型方法（比如TCP.prototype.listen）</p>
<h2>4.3.1 &nbsp;process对象 &mdash;&mdash; V8的Object类</h2>
<p>　　在3.2中讲到，main函数启动后会加载执行src/node.js文件，并且把process对象传给node.js文件，在里面设置process.nextTick()等方法。</p>
<p>　　那么来看看 C++如何创建一个给js使用的对象。</p>
<h3>******* 类型</h3>
<p>　　回去3.1.2节看一下&ldquo;图3-1-3&rdquo;。在LoadEnvironment() 里面执行 f-&gt;Call()调用node.js里的匿名函数时，传过去的process对象是通过env-&gt;process_object()获取的。</p>
<p>　　env-&gt;process_object()的实现如下：　</p>
<p><em id="__mceDel"><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160319164431896-1688026905.png" alt="" /></em></p>
<p style="text-align: center;">　图 4-3-1</p>
<p>　　这里是个宏，展开就是</p>
<div class="cnblogs_code">
<pre>inline v8::Local&lt;v8::Object&gt; Environment::process_object() <span style="color: #0000ff;">const</span><span style="color: #000000;"> {
    </span><span style="color: #0000ff;">return</span><span style="color: #000000;"> StrongPersistentToLocal(<span style="color: #ff0000;">process_object_</span>);
}</span></pre>
</div>
<p>&nbsp;</p>
<p>　　那么上面标红的process_object_ 成员，定义如下：</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160319165443115-893538279.png" alt="" /></p>
<p style="text-align: center;">图 4-3-2</p>
<p>　　这里也是一个宏，展开就是</p>
<div class="cnblogs_code">
<pre><span style="color: #0000ff;">class</span><span style="color: #000000;"> Environment {
    v8::Persistent</span>&lt;v8::Object&gt;<span style="color: #000000;"><span style="color: #ff0000;"> process_object_</span>;
}</span></pre>
</div>
<p>　　那么这里可以看到，C++里面提供给js代码的对象，就是一个v8::Object类型的对象。</p>
<h3>4.3.1.2 设置属性或方法</h3>
<p>　　那么，v8::Object类型的对象如何在C++里面设置属性呢？</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160319171507099-235401240.png" alt="" /></p>
<p style="text-align: center;">图4-3-3</p>
<p>　　这里可以看到，v8::Object类提供了Set()方法，来让你设置供js访问的属性或方法。</p>
<p>&nbsp;</p>
<h2>4.3.2 TCP类 &mdash;&mdash; v8的FunctionTemplate类</h2>
<p>　　那么第二种类型，就是设置prototype方法。在js里面，没有真正的类的概念，而是通过给函数对象TCP的prototype属性设置方法，使用的时候通过new TCP()去创建实例。</p>
<p>　　那么，v8如何设置原型方法？</p>
<h3>4.3.2.1 设置原型方法</h3>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160319174307553-810866233.png" alt="" /></p>
<p style="text-align: center;">图4-3-4</p>
<p style="text-align: left;">　　这里可以看到，通过创建一个v8::FunctionTemplate类型的对象 t，通过 t-&gt;PrototypeTemplate() 去获取函数对象的prototype，并进一步调用Set()去设置prototype上的方法。</p>
<p style="text-align: left;">　　最后再通过 t-&gt;GetFunction() 去获取一个该函数模版的方法。</p>
<p style="text-align: left;">　　</p>
<p style="text-align: left;"><strong><span style="font-size: 18pt;">注：关于 js文件process.binding('tcp_wrap')引入TCP函数对象的机制，在下一篇博客讲。</span></strong></p>
<p>&nbsp;</p></div><div id="MySignature"></div>
<div class="clear"></div>
<div id="blog_post_info_block">
<div id="BlogPostCategory"></div>
<div id="EntryTag"></div>
<div id="blog_post_info">
</div>
<div class="clear"></div>
<div id="post_next_prev"></div>
</div>


	<div class = "postDesc">posted on <span id="post-date">2016-03-19 17:59</span> <a href='http://www.cnblogs.com/papertree/'>野路子程序员</a> 阅读(<span id="post_view_count">...</span>) 评论(<span id="post_comment_count">...</span>)  <a href ="https://i.cnblogs.com/EditPosts.aspx?postid=5285705" rel="nofollow">编辑</a> <a href="#" onclick="AddToWz(5285705);return false;">收藏</a></div>
</div>
<script type="text/javascript">var allowComments=true,cb_blogId=242729,cb_entryId=5285705,cb_blogApp=currentBlogApp,cb_blogUserGuid='40a171c9-d74e-e511-b908-9dcfd8948a71',cb_entryCreatedDate='2016/3/19 17:59:00';loadViewCount(cb_entryId);</script>

</div><a name="!comments"></a><div id="blog-comments-placeholder"></div><script type="text/javascript">var commentManager = new blogCommentManager();commentManager.renderComments(0);</script>
<div id='comment_form' class='commentform'>
<a name='commentform'></a>
<div id='divCommentShow'></div>
<div id='comment_nav'><span id='span_refresh_tips'></span><a href='javascript:void(0);' onclick='return RefreshCommentList();' id='lnk_RefreshComments' runat='server' clientidmode='Static'>刷新评论</a><a href='#' onclick='return RefreshPage();'>刷新页面</a><a href='#top'>返回顶部</a></div>
<div id='comment_form_container'></div>
<div class='ad_text_commentbox' id='ad_text_under_commentbox'></div>
<div id='ad_t2'></div>
<div id='opt_under_post'></div>
<div id='ad_c1' class='c_ad_block'></div>
<div id='under_post_news'></div>
<div id='ad_c2' class='c_ad_block'></div>
<div id='under_post_kb'></div>
<div id='HistoryToday' class='c_ad_block'></div>
<script type='text/javascript'>
    fixPostBody();
    setTimeout(function () { incrementViewCount(cb_entryId); }, 50);
    deliverAdT2();
    deliverAdC1();
    deliverAdC2();    
    loadNewsAndKb();
    loadBlogSignature();
    LoadPostInfoBlock(cb_blogId, cb_entryId, cb_blogApp, cb_blogUserGuid);
    GetPrevNextPost(cb_entryId, cb_blogId, cb_entryCreatedDate);
    loadOptUnderPost();
    GetHistoryToday(cb_blogId, cb_blogApp, cb_entryCreatedDate);   
</script>
</div>


</div>
<div id="leftcontent">
	
		<DIV id="leftcontentcontainer">
			
<!--done-->
<div class="newsItem">
	<div id="blog-news"></div><script type="text/javascript">loadBlogNews();</script>
</div>

			<div id="blog-calendar" style="display:none"></div><script type="text/javascript">loadBlogDefaultCalendar();</script><br>
			<div id="blog-sidecolumn"></div><script type="text/javascript">loadBlogSideColumn();</script></DIV>
	
</div>

<!--done-->
<div class="footer">
	Powered by: <a href="http://www.cnblogs.com">博客园</a>	模板提供：<a href="http://blog.hjenglish.com">沪江博客</a>
	Copyright &copy;2016 野路子程序员
</div>



</body>
</html>
