
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>linux下使用shadowsocks 做代理 - 野路子程序员 - 博客园</title>
<link type="text/css" rel="stylesheet" href="/bundles/blog-common.css?v=XrHjCqi-oUg9M4EcevxEUXvgm_dLNu97ZVKqK6ra-aE1"/>
<link id="MainCss" type="text/css" rel="stylesheet" href="/skins/sea/bundle-sea.css?v=PPe3jt1rcXPno6kucw0iQU2MWF3DbrvubQHRM0FEMas1"/>
<link id="mobile-style" media="only screen and (max-width: 768px)" type="text/css" rel="stylesheet" href="/skins/sea/bundle-sea-mobile.css?v=dCMFeRnDl5aeD8pIbxbwMGs8e9wn75b-E2rGISFE2uk1"/>
<link title="RSS" type="application/rss+xml" rel="alternate" href="http://www.cnblogs.com/papertree/rss"/>
<link title="RSD" type="application/rsd+xml" rel="EditURI" href="http://www.cnblogs.com/papertree/rsd.xml"/>
<link type="application/wlwmanifest+xml" rel="wlwmanifest" href="http://www.cnblogs.com/papertree/wlwmanifest.xml"/>
<script src="//common.cnblogs.com/script/jquery.js" type="text/javascript"></script>  
<script type="text/javascript">var currentBlogApp = 'papertree', cb_enable_mathjax=false;var isLogined=false;</script>
<script src="/bundles/blog-common.js?v=ufkHqY7CLaQ-FdEJCMtH3vR-Ia3VNX59Tf2LnMKYKak1" type="text/javascript"></script>
</head>
<body>
<a name="top"></a>

<!--done-->
<div id="header">
	
<!--done-->
<div class="header">
	<div class="headerText">
		<a id="Header1_HeaderTitle" class="headermaintitle" href="http://www.cnblogs.com/papertree/">大树先生</a><br>
		
	</div>
</div>

</div>

<div id="mytopmenu" >
	
		<div id="mylinks"><a id="blog_nav_sitehome" class="menu" href="http://www.cnblogs.com/">博客园</a> &nbsp;
<a id="blog_nav_myhome" class="menu" href="http://www.cnblogs.com/papertree/">首页</a> &nbsp;
<a id="blog_nav_newpost" class="menu" rel="nofollow" href="https://i.cnblogs.com/EditPosts.aspx?opt=1">新随笔</a> &nbsp;
<a id="blog_nav_contact" class="menu" rel="nofollow" href="https://msg.cnblogs.com/send/%E9%87%8E%E8%B7%AF%E5%AD%90%E7%A8%8B%E5%BA%8F%E5%91%98">联系</a> &nbsp;
<a id="blog_nav_rss" class="menu" href="http://www.cnblogs.com/papertree/rss">订阅</a><a id="blog_nav_rss_image" href="http://www.cnblogs.com/papertree/rss"><img src="//www.cnblogs.com/images/xml.gif" alt="订阅" /></a>&nbsp;
<a id="blog_nav_admin" class="menu" rel="nofollow" href="https://i.cnblogs.com/">管理</a>
</div>
		<DIV id="mystats"><div id="blog_stats">
随笔-9&nbsp;
评论-14&nbsp;
文章-0&nbsp;
<!--trackbacks-0-->
</div></DIV>
	
</div>
<div id="centercontent" >
	
<div id="post_detail">
<div class = "post">
	<h1 class = "postTitle"><a id="cb_post_title_url" class="postTitle2" href="http://www.cnblogs.com/papertree/p/5294678.html">linux下使用shadowsocks 做代理</a></h1>
	<div id="cnblogs_post_body"><p>声明：转载请保留声明头部并标明转载、并私信告知作者。原文：<a href="http://www.cnblogs.com/papertree/p/5294678.html" target="_blank">http://www.cnblogs.com/papertree/p/5294678.html</a></p>
<hr />
<h1>1. 遇到的情况：</h1>
<p>　　在阿里云服务器上需要执行&ldquo;git clone https://chromium.googlesource.com/v8/v8.git&rdquo;，然后就访问不到googlesource.com了。。。</p>
<p>&nbsp;</p>
<h1>2. 工具 &mdash;&mdash; shadowsocks（包含ssserver/sslocal）：</h1>
<p>　　首先，需要一台能够访问到googlesource.com的服务器（<span style="color: #ff0000;">用来跑ssserver</span>），执行以下命令安装shadowsocks：</p>
<p>///////////////////////////////////////////////////////////////</p>
<p>　　<strong>为Debian / Ubuntu 安装shadowsocks服务端：<br /></strong>　　　　apt-get install python-pip<br />　　　　pip install shadowsocks<br /><strong>　　为CentOs 安装shadowsocks服务端;<br /></strong>　　　　yum install python-setuptools<br />　　　　easy_install pip<br />　　　　pip install shadowsocks</p>
<p>//////////////////////////////////////////////////////////////</p>
<p>　　然后，在自己需要访问googlesource.com的服务器同样安装shadowsocks，<span style="color: #ff0000;">本地代理需要跑sslocal</span>。</p>
<p>&nbsp;</p>
<h1>3. 配置</h1>
<p>1. 在跑ssserver的服务器上建配置文件 /etc/shadowsocks/config.json，然后启动执行ssserver。</p>
<p>config.json：</p>
<div class="cnblogs_code">
<pre><span style="color: #000000;">{
    </span>"server_port": "sslocal和sssver之间约定的端口"<span style="color: #000000;">,
    </span>"password": "sslocal和sssver之间约定的密码"<span style="color: #000000;">,
    </span>"method": "加密方式，"bf-cfb", "aes-256-cfb", "des-cfb", "rc4", etc."<span style="color: #000000;">,
    </span>"timeout": "超时时间"<span style="color: #000000;">
}</span></pre>
</div>
<p>2. 在跑sslocal的机器（也就是想访问googlesource的本机）上建配置文件 /etc/shadowsocks/config.json，然后执行sslocal。</p>
<p>config.json：</p>
<div class="cnblogs_code">
<pre><span style="color: #000000;">{
    </span>"server": "跑ssserver服务器的ip"<span style="color: #000000;">,
    </span>"server_port": "跑sssver的端口"<span style="color: #000000;">,
    </span>"local_address": "127.0.0.1"<span style="color: #000000;">,
    </span>"local_port": "本地https 代理的监听端口"
    "password": "与ssserver的password一致"<span style="color: #000000;">,
    </span>"method": "与ssserver的method一致"<span style="color: #000000;">,
    </span>"timeout": "超时时间"<span style="color: #000000;">
}</span></pre>
</div>
<p>3. 给git加上访问本地代理的配置：</p>
<p>　　执行&ldquo;git config --global http.proxy 'socks5://127.0.0.1:端口' &rdquo;，这里的端口是sslocal的config里面的local_port，</p>
<p>　　再执行&ldquo;git config --global https.proxy 'socks5://127.0.0.1:端口' &rdquo;。</p>
<p>&nbsp;</p>
<p>搞定。</p>
<p>&nbsp;</p>
<p>4. 最后执行&ldquo;git config --global --unset-all http.proxy&rdquo;和&ldquo;git config --global --unset-all https.proxy&rdquo;恢复设置。</p></div><div id="MySignature"></div>
<div class="clear"></div>
<div id="blog_post_info_block">
<div id="BlogPostCategory"></div>
<div id="EntryTag"></div>
<div id="blog_post_info">
</div>
<div class="clear"></div>
<div id="post_next_prev"></div>
</div>


	<div class = "postDesc">posted on <span id="post-date">2016-03-19 11:39</span> <a href='http://www.cnblogs.com/papertree/'>野路子程序员</a> 阅读(<span id="post_view_count">...</span>) 评论(<span id="post_comment_count">...</span>)  <a href ="https://i.cnblogs.com/EditPosts.aspx?postid=5294678" rel="nofollow">编辑</a> <a href="#" onclick="AddToWz(5294678);return false;">收藏</a></div>
</div>
<script type="text/javascript">var allowComments=true,cb_blogId=242729,cb_entryId=5294678,cb_blogApp=currentBlogApp,cb_blogUserGuid='40a171c9-d74e-e511-b908-9dcfd8948a71',cb_entryCreatedDate='2016/3/19 11:39:00';loadViewCount(cb_entryId);</script>

</div><a name="!comments"></a><div id="blog-comments-placeholder"></div><script type="text/javascript">var commentManager = new blogCommentManager();commentManager.renderComments(0);</script>
<div id='comment_form' class='commentform'>
<a name='commentform'></a>
<div id='divCommentShow'></div>
<div id='comment_nav'><span id='span_refresh_tips'></span><a href='javascript:void(0);' onclick='return RefreshCommentList();' id='lnk_RefreshComments' runat='server' clientidmode='Static'>刷新评论</a><a href='#' onclick='return RefreshPage();'>刷新页面</a><a href='#top'>返回顶部</a></div>
<div id='comment_form_container'></div>
<div class='ad_text_commentbox' id='ad_text_under_commentbox'></div>
<div id='ad_t2'></div>
<div id='opt_under_post'></div>
<div id='ad_c1' class='c_ad_block'></div>
<div id='under_post_news'></div>
<div id='ad_c2' class='c_ad_block'></div>
<div id='under_post_kb'></div>
<div id='HistoryToday' class='c_ad_block'></div>
<script type='text/javascript'>
    fixPostBody();
    setTimeout(function () { incrementViewCount(cb_entryId); }, 50);
    deliverAdT2();
    deliverAdC1();
    deliverAdC2();    
    loadNewsAndKb();
    loadBlogSignature();
    LoadPostInfoBlock(cb_blogId, cb_entryId, cb_blogApp, cb_blogUserGuid);
    GetPrevNextPost(cb_entryId, cb_blogId, cb_entryCreatedDate);
    loadOptUnderPost();
    GetHistoryToday(cb_blogId, cb_blogApp, cb_entryCreatedDate);   
</script>
</div>


</div>
<div id="leftcontent">
	
		<DIV id="leftcontentcontainer">
			
<!--done-->
<div class="newsItem">
	<div id="blog-news"></div><script type="text/javascript">loadBlogNews();</script>
</div>

			<div id="blog-calendar" style="display:none"></div><script type="text/javascript">loadBlogDefaultCalendar();</script><br>
			<div id="blog-sidecolumn"></div><script type="text/javascript">loadBlogSideColumn();</script></DIV>
	
</div>

<!--done-->
<div class="footer">
	Powered by: <a href="http://www.cnblogs.com">博客园</a>	模板提供：<a href="http://blog.hjenglish.com">沪江博客</a>
	Copyright &copy;2016 野路子程序员
</div>



</body>
</html>
