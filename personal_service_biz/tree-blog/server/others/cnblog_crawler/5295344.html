
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>node源码详解（五） —— 在main函数之前 —— js和C++的边界，process.binding - 野路子程序员 - 博客园</title>
<link type="text/css" rel="stylesheet" href="/bundles/blog-common.css?v=XrHjCqi-oUg9M4EcevxEUXvgm_dLNu97ZVKqK6ra-aE1"/>
<link id="MainCss" type="text/css" rel="stylesheet" href="/skins/sea/bundle-sea.css?v=PPe3jt1rcXPno6kucw0iQU2MWF3DbrvubQHRM0FEMas1"/>
<link id="mobile-style" media="only screen and (max-width: 768px)" type="text/css" rel="stylesheet" href="/skins/sea/bundle-sea-mobile.css?v=dCMFeRnDl5aeD8pIbxbwMGs8e9wn75b-E2rGISFE2uk1"/>
<link title="RSS" type="application/rss+xml" rel="alternate" href="http://www.cnblogs.com/papertree/rss"/>
<link title="RSD" type="application/rsd+xml" rel="EditURI" href="http://www.cnblogs.com/papertree/rsd.xml"/>
<link type="application/wlwmanifest+xml" rel="wlwmanifest" href="http://www.cnblogs.com/papertree/wlwmanifest.xml"/>
<script src="//common.cnblogs.com/script/jquery.js" type="text/javascript"></script>  
<script type="text/javascript">var currentBlogApp = 'papertree', cb_enable_mathjax=false;var isLogined=false;</script>
<script src="/bundles/blog-common.js?v=ufkHqY7CLaQ-FdEJCMtH3vR-Ia3VNX59Tf2LnMKYKak1" type="text/javascript"></script>
</head>
<body>
<a name="top"></a>

<!--done-->
<div id="header">
	
<!--done-->
<div class="header">
	<div class="headerText">
		<a id="Header1_HeaderTitle" class="headermaintitle" href="http://www.cnblogs.com/papertree/">大树先生</a><br>
		
	</div>
</div>

</div>

<div id="mytopmenu" >
	
		<div id="mylinks"><a id="blog_nav_sitehome" class="menu" href="http://www.cnblogs.com/">博客园</a> &nbsp;
<a id="blog_nav_myhome" class="menu" href="http://www.cnblogs.com/papertree/">首页</a> &nbsp;
<a id="blog_nav_newpost" class="menu" rel="nofollow" href="https://i.cnblogs.com/EditPosts.aspx?opt=1">新随笔</a> &nbsp;
<a id="blog_nav_contact" class="menu" rel="nofollow" href="https://msg.cnblogs.com/send/%E9%87%8E%E8%B7%AF%E5%AD%90%E7%A8%8B%E5%BA%8F%E5%91%98">联系</a> &nbsp;
<a id="blog_nav_rss" class="menu" href="http://www.cnblogs.com/papertree/rss">订阅</a><a id="blog_nav_rss_image" href="http://www.cnblogs.com/papertree/rss"><img src="//www.cnblogs.com/images/xml.gif" alt="订阅" /></a>&nbsp;
<a id="blog_nav_admin" class="menu" rel="nofollow" href="https://i.cnblogs.com/">管理</a>
</div>
		<DIV id="mystats"><div id="blog_stats">
随笔-9&nbsp;
评论-14&nbsp;
文章-0&nbsp;
<!--trackbacks-0-->
</div></DIV>
	
</div>
<div id="centercontent" >
	
<div id="post_detail">
<div class = "post">
	<h1 class = "postTitle"><a id="cb_post_title_url" class="postTitle2" href="http://www.cnblogs.com/papertree/p/5295344.html">node源码详解（五） —— 在main函数之前 —— js和C++的边界，process.binding</a></h1>
	<div id="cnblogs_post_body"><p>&nbsp;</p>
<p>声明：转载请保留声明头部并标明转载、并私信告知作者。原文：<a href="http://www.cnblogs.com/papertree/p/5295344.html" target="_blank">http://www.cnblogs.com/papertree/p/5295344.html</a></p>
<hr />
<p>　　在上一篇博客（详解四）讲了 C++通过v8的Object类和FunctionTemplate类，创建对象、方法，设置属性、原型方法等，提供给运行时的 js代码调用。</p>
<p>　　那么这些C++实现的process对象、TCP类是否都在程序启动的时候就创建到 js的执行环境（context）呢？</p>
<p>　　不全是。process对象是（见5.2节），但 TCP类等C++内建模块不是（见5.3节）。　　</p>
<h1>5.1 在main函数启动之前 &mdash;&mdash; C++内建模块的注册</h1>
<p>　　看一下C++内建模块的实现方法：</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160319205056224-1869068426.png" alt="" /></p>
<p style="text-align: center;">图5-1-1</p>
<p style="text-align: left;"><span style="line-height: 1.5;">　　我们看到最后一行的NODE_MODULE_CONTEXT_AWARE_BUILTIN，通过这个方式来导出一个C++内建模块，这行代码实现了什么？</span></p>
<h2 style="text-align: left;">5.1.1&nbsp;NODE_MODULE_CONTEXT_AWARE_BUILTIN 宏</h2>
<p>　　看下这个宏的实现，在src/node.h文件：</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160319212219912-2080485956.png" alt="" /></p>
<p style="text-align: center;">图5-1-2</p>
<h2>5.1.2 NODE_C_CTOR宏与__attribute__((constructor))声明 &mdash;&mdash; 在main函数前注册</h2>
<p>　　看到NODE_C_CTOR这个宏的作用时给传进来的函数加上这么一个声明，这是gcc的一个函数属性声明。</p>
<p>　　来自gcc文档的说明：（地址：<a href="https://gcc.gnu.org/onlnedocs/gcc/Common-Function-Attributes.html#Common-Function-Attributes" target="_blank">https://gcc.gnu.org/onlnedocs/gcc/Common-Function-Attributes.html#Common-Function-Attributes</a>）</p>
<p>　　　　［&nbsp;The&nbsp;<code>constructor</code>&nbsp;attribute causes the function to be called automatically before execution enters&nbsp;<code>main ()</code>. ］</p>
<p>　　　 &nbsp;［笔者译：constructor属性导致该函数在程序进入main函数之前被执行］　　</p>
<p>　　注：上面的 &ldquo;.CRT$XCU&rdquo;是微软的编译器实现类似功能的方法。</p>
<h2>5.1.3 node_module_register函数 与 node::node_module结构体的链表modlist_builtin</h2>
<p>　　通过图5-1-2，可以知道通过NODE_MODULE_CONTEXT_AWARE_BUILTIN( tcp_wrap, TCPWrap::Initialize )宏去注册一个C++内建模块时，过程如下：</p>
<p>　　1. 把 tcp_wrap（modname）和 Initilize函数（regfunc）封装成一个node_module类型的结构体 _module</p>
<p>　　2.&nbsp;调用node_module_register(&amp;_module) 函数进行注册。</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160319214959803-1539577164.png" alt="" /></p>
<p style="text-align: center;">图5-1-3</p>
<p style="text-align: left;">　　可以看到这个函数把传进来的m插入到内建模块的链表modlist_builtin。</p>
<p style="text-align: left;">&nbsp;</p>
<h2 style="text-align: left;">5.1.4 总结</h2>
<p style="text-align: left;">　　通过5.1节，我们知道tcp_wrap.cc里面实现的C++内建模块，在main函数启动之前把该内建模块的初始化函数 TCPWrap::Initialize()保存到一个全局的静态链表modlist_builtin。</p>
<p style="text-align: left;">　　那么这些内建模块的Initialize()什么时候执行呢？里面可是创建了TCP类对应的FunctionTemplate呢，在创建对应的FunctionTemplate对象之前，V8的context里头，js代码还是没得直接使用new TCP()呢。</p>
<hr />
<h1>5.2 process.binding &mdash;&mdash; C++内建模块的初始化与缓存</h1>
<p>　　既然C++内建模块只是保存初始化函数到链表，而不真正创建一个js可以使用的函数对象（TCP类）到v8的上下文（context），那么初始化这个步骤明显要交给 js代码来控制了。</p>
<p>　　通过process.binding('tcp_wrap') 来引入C++内建模块创建的对象，如果第一次binding这个内建模块，那么就会调用Initialize函数。</p>
<p>　　那么你可能会问，C++内建模块就需要 js里面调用process.binding()来引入，那process对象也是C++提供的v8::Object类型的对象，为什么可以直接使用process.binding()呢？</p>
<p>　　因为内建模块并不是你js代码一定会用到的，所以通过保存TCPWrap::Initialize()的方式，等到要用到时再通过process.binding()初始化。你要一开始就执行初始化函数，也是可以创建对应的FunctionTemplate对象到v8上下文的。</p>
<p>　　而process这个对象在main函数启动之后、执行node.js之前就创建了，那么js代码运行的上下文（context）里面就可以直接用了。具体代码见5.3节。</p>
<p>　　来看一下process.binding()的过程：</p>
<p>　　［注：C++设置给process对象的binding方法是下面的Binding()函数］</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160319222010646-1905307773.png" alt="" /></p>
<p style="text-align: center;">图5-2-1</p>
<p style="text-align: left;">　　1. Binding()函数通过要binding的模块名，调用get_builtin_module()，从5.1.3中提到的内建模块链表modlist_buildin去获取node::node_module结构体对象</p>
<p style="text-align: left;">　　2. node_module对象里面的nm_context_register_func字段保存着对应的初始化函数（即5.1.3中保存的TCPWrap::Initialize），图中第2320行可以看出binding的时候执行了初始化函数。</p>
<p style="text-align: left;">　　3. 最后把exports当成process.binding()的返回值。exports哪里被赋值呢？注意到nm_context_register_func其实是tcp_wrap.cc里面的TCPWrap::Initialize()函数，回去图5-1-1看看这个函数，就会发现哦原来就是target。</p>
<p>&nbsp;</p>
<hr />
<p>&nbsp;</p>
<h1>5.3 process对象的初始化</h1>
<p>　　在3.1.2节中讲到这么一个流程：</p>
<p>　　　　main() -&gt;&nbsp;Start() -&gt; StartNodeInstance() -&gt; LoadEnviroment()</p>
<p>　　在LoadEnvrionment()里面去执行node.js文件，并把process对象传进去。</p>
<h2>5.3.1 process初始化（js部分）</h2>
<p>　　在LoadEnvrionment()执行node.js文件，并传process进去的时候，process.nextTick()这些函数是在node.js里面初始化的。</p>
<h2>5.3.1 process初始化（C++部分）</h2>
<p>　　在main() -&gt; Start() -&gt; StartNodeInstance() 这里，执行LoadEnvironment()之前，先调用了CreateEnvironment()。</p>
<p>　　在CreateEnvironment()里面创建process对象，并且初始化C++部分的函数。看代码：</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160319224422365-200709044.png" alt="" /></p>
<p style="text-align: center;">图5-3-1</p>
<p>　　1. 创建v8::Object process_object 在 CreateEnvironment()里面。</p>
<p>　　2. 给process.binding等赋值在SetupProcessObject()里面。</p>
<p>&nbsp;</p>
<p style="text-align: left;">&nbsp;</p></div><div id="MySignature"></div>
<div class="clear"></div>
<div id="blog_post_info_block">
<div id="BlogPostCategory"></div>
<div id="EntryTag"></div>
<div id="blog_post_info">
</div>
<div class="clear"></div>
<div id="post_next_prev"></div>
</div>


	<div class = "postDesc">posted on <span id="post-date">2016-03-19 22:49</span> <a href='http://www.cnblogs.com/papertree/'>野路子程序员</a> 阅读(<span id="post_view_count">...</span>) 评论(<span id="post_comment_count">...</span>)  <a href ="https://i.cnblogs.com/EditPosts.aspx?postid=5295344" rel="nofollow">编辑</a> <a href="#" onclick="AddToWz(5295344);return false;">收藏</a></div>
</div>
<script type="text/javascript">var allowComments=true,cb_blogId=242729,cb_entryId=5295344,cb_blogApp=currentBlogApp,cb_blogUserGuid='40a171c9-d74e-e511-b908-9dcfd8948a71',cb_entryCreatedDate='2016/3/19 22:49:00';loadViewCount(cb_entryId);</script>

</div><a name="!comments"></a><div id="blog-comments-placeholder"></div><script type="text/javascript">var commentManager = new blogCommentManager();commentManager.renderComments(0);</script>
<div id='comment_form' class='commentform'>
<a name='commentform'></a>
<div id='divCommentShow'></div>
<div id='comment_nav'><span id='span_refresh_tips'></span><a href='javascript:void(0);' onclick='return RefreshCommentList();' id='lnk_RefreshComments' runat='server' clientidmode='Static'>刷新评论</a><a href='#' onclick='return RefreshPage();'>刷新页面</a><a href='#top'>返回顶部</a></div>
<div id='comment_form_container'></div>
<div class='ad_text_commentbox' id='ad_text_under_commentbox'></div>
<div id='ad_t2'></div>
<div id='opt_under_post'></div>
<div id='ad_c1' class='c_ad_block'></div>
<div id='under_post_news'></div>
<div id='ad_c2' class='c_ad_block'></div>
<div id='under_post_kb'></div>
<div id='HistoryToday' class='c_ad_block'></div>
<script type='text/javascript'>
    fixPostBody();
    setTimeout(function () { incrementViewCount(cb_entryId); }, 50);
    deliverAdT2();
    deliverAdC1();
    deliverAdC2();    
    loadNewsAndKb();
    loadBlogSignature();
    LoadPostInfoBlock(cb_blogId, cb_entryId, cb_blogApp, cb_blogUserGuid);
    GetPrevNextPost(cb_entryId, cb_blogId, cb_entryCreatedDate);
    loadOptUnderPost();
    GetHistoryToday(cb_blogId, cb_blogApp, cb_entryCreatedDate);   
</script>
</div>


</div>
<div id="leftcontent">
	
		<DIV id="leftcontentcontainer">
			
<!--done-->
<div class="newsItem">
	<div id="blog-news"></div><script type="text/javascript">loadBlogNews();</script>
</div>

			<div id="blog-calendar" style="display:none"></div><script type="text/javascript">loadBlogDefaultCalendar();</script><br>
			<div id="blog-sidecolumn"></div><script type="text/javascript">loadBlogSideColumn();</script></DIV>
	
</div>

<!--done-->
<div class="footer">
	Powered by: <a href="http://www.cnblogs.com">博客园</a>	模板提供：<a href="http://blog.hjenglish.com">沪江博客</a>
	Copyright &copy;2016 野路子程序员
</div>



</body>
</html>
