	<div id="cnblogs_post_body"><p>&nbsp;</p>
<p>声明：转载请保留声明头部并标明转载、并私信告知作者。原文：<a href="http://www.cnblogs.com/papertree/p/5328134.html" target="_blank">http://www.cnblogs.com/papertree/p/5328134.html</a></p>
<hr />
<p>　　今天把 bluebird（2.9.0版） 的源码看了，写成博客记录一下。</p>
<p>&nbsp;</p>
<h1>1. 带上几个问题看源码</h1>
<p><span style="line-height: 1.5;">1. promise链是如何实现的？</span></p>
<p>2. promise对象如何变成fulfill状态，并触发promise链条的后续函数？new Promise和Promise.resolve() 有何不同？</p>
<p>＊3. 为什么每执行.then就创建一个新的Promise对象，而不能使用第一个promise依次.then？</p>
<p>4. 如何对throw Error 和 reject进行加工</p>
<p>&nbsp;</p>
<p>　　分两条主线来讲解：</p>
<p>第2节：回调函数的设置、promise链条的保存</p>
<p>第3节：promise对象的解决（设置为rejected、fulfilled）、链条的迁移</p>
<hr />
<p>&nbsp;</p>
<h1>2. promise链如何实现 &mdash;&mdash; 注册阶段（.then）</h1>
<p>　　我们都知道设置一个promise链是通过promise对象的.then方法注册fulfill、reject 状态被激活时的回调函数。来看一下.then的代码：</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160329092917457-1526323842.png" alt="" /></p>
<p style="text-align: center;">图2-1</p>
<h2>2.1 promise保存链条的结构</h2>
<p>　　上图可以看到.then内部调用了._then，然后把我们传给.then()函数的didFulfill、didReject等回调函数通过_addCallbacks保存下来。这里注意到，不是通过 &ldquo; this._addCallbacks() &rdquo;，而是通过 &ldquo; target._addCallbacks() &rdquo;，而且上一行还判断了 &ldquo; target !== this &rdquo;的条件。那么target是什么呢？待会2.5节讲。</p>
<p>　　看到&nbsp;_addCallbacks的实现，promise对象以每5个参数为一组保存。当对一个promise对象调用一次.then(didFulfill, didReject)的时候，这组相关的参数保存在：</p>
<div class="cnblogs_code">
<pre><span style="color: #0000ff;">this</span>._fulfillmentHandler0;  // promise对象被置为fulfilled 时的回调函数<br />this._rejectionHandler0;　　// promise对象被置为rejected 时的回调函数<br />this._progressHandler0;<br />this._promise0;<br />this._receiver0;　　// 当 fulfill被调用时　　，传给函数的 this对象</pre>
</div>
<p style="text-align: center;">&nbsp;代码2-1</p>
<p>　　当在一个promise对象上超过一次调用.then(didFulfill, didReject) 时，大于1的部分以这种形式保存在promise对象上：</p>
<div class="cnblogs_code">
<pre><span style="color: #0000ff;">var</span> base; <span style="color: #008000;">//</span><span style="color: #008000;"> base表示每组参数的起点，每5个参数为一组保存</span>
<span style="color: #0000ff;">this</span>[base + 0<span style="color: #000000;">];
</span><span style="color: #0000ff;">this</span>[base + 1<span style="color: #000000;">];
</span><span style="color: #0000ff;">this</span>[base + 2<span style="color: #000000;">];
</span><span style="color: #0000ff;">this</span>[base + 3<span style="color: #000000;">];
</span><span style="color: #0000ff;">this</span>[base + 4];</pre>
</div>
<p style="text-align: center;">代码2-2&nbsp;</p>
<h2>2.2 链条的拓扑结构 &mdash;&mdash; 为何每个.then 都new一个新的Promise对象？</h2>
<p>　　很多说明文档会给出这样的示例代码：</p>
<div class="cnblogs_code">
<pre><span style="color: #008000;">//</span><span style="color: #008000;"> 来自 <a href="http://liubin.org/promises-book/#ch2-promise.then" target="_blank">http://liubin.org/promises-book/#ch2-promise.then</a></span><span style="color: #008000;">
//</span><span style="color: #008000;"> promise可以写成方法链的形式</span>
<span style="color: #000000;">
aPromise.then(</span><span style="color: #0000ff;">function</span><span style="color: #000000;"> taskA(value){
</span><span style="color: #008000;">//</span><span style="color: #008000;"> task A</span>
}).then(<span style="color: #0000ff;">function</span><span style="color: #000000;"> taskB(vaue){
</span><span style="color: #008000;">//</span><span style="color: #008000;"> task B</span>
}).<span style="color: #0000ff;">catch</span>(<span style="color: #0000ff;">function</span><span style="color: #000000;"> onRejected(error){
    console.log(error);
});</span></pre>
</div>
<p style="text-align: center;">&nbsp;代码2-3</p>
<p>　　这样的实现的任务块是这样一种拓扑结构：</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160329211236004-264006887.png" alt="" /></p>
<p style="text-align: center;">图2-2</p>
<p>　　而对于另一种拓扑结构的任务，有all 和 race方法：</p>
<p>　　<img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160329211726754-686731214.png" alt="" /></p>
<p style="text-align: center;">图2-3</p>
<p>&nbsp;</p>
<p>　　如果没有深究，咋一看可能以为上面的&ldquo;代码2-3&rdquo;中，依次.then都是在同一个aPromise对象上，<span style="line-height: 1.5;">而.then所注册的多个回调函数都保存在aPromise上。</span></p>
<p>　　事实上，看到上面图2-1中，Promise.prototype._then的代码里面，每次执行_then都会新建一个Promise对象，比如代码2-3实际上等效于这样：</p>
<div class="cnblogs_code">
<pre><span style="color: #0000ff;">var</span> bPromise = aPromise.then(<span style="color: #0000ff;">function</span><span style="color: #000000;"> taskA(value){
</span><span style="color: #008000;">//</span><span style="color: #008000;"> task A</span>
<span style="color: #000000;">});
</span><span style="color: #0000ff;">var</span> cPromise = bPromise.then(<span style="color: #0000ff;">function</span><span style="color: #000000;"> taskB(vaue){
</span><span style="color: #008000;">//</span><span style="color: #008000;"> task B</span>
}).<span style="color: #0000ff;">catch</span>(<span style="color: #0000ff;">function</span><span style="color: #000000;"> onRejected(error){
    console.log(error);
});</span></pre>
</div>
<p style="text-align: center;">代码2-4</p>
<p>　　aPromise、bPromise、cPromise分别是不同的对象。</p>
<p>　　那么为什么这么实现呢？想一下就会知道这样多种拓扑结构：</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160329232506629-1077215276.png" alt="" /></p>
<p style="text-align: center;">图2-4</p>
<p>　　当在同一个promise对象上多次执行.then时，跟代码2-3依次.then的情况并不一样，如下的示例代码：</p>
<div class="cnblogs_code">
<pre><span style="color: #0000ff;">var</span> bPromise = aPromise.then(<span style="color: #0000ff;">function</span><span style="color: #000000;"> taskA(value){<br />　　// task A
    </span><span style="color: #0000ff;">return</span> <span style="color: #0000ff;">new</span> Promise(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (resolve) {
        setTimeout(</span><span style="color: #0000ff;">function</span><span style="color: #000000;"> () {
            </span><span style="color: #0000ff;">return</span><span style="color: #000000;"> resolve();
        }, </span>5000<span style="color: #000000;">);
    });
});
</span><span style="color: #0000ff;">var</span> cPromise = aPromise.then(<span style="color: #0000ff;">function</span><span style="color: #000000;"> taskB(vaue){<br />　　// task B
　　console.log('task B');</span>
}); </pre>
</div>
<p style="text-align: center;">代码2-5</p>
<p>　　这里用aPromise.then两次，注册两个onFulfill函数（function taskA 和 function taskB）。当task A 里返回新建的promise对象处于pending状态时，task B的任务会先执行。</p>
<p><span style="line-height: 1.5;">&nbsp;</span>　　</p>
<p>　　那么这样的promise链条是相当灵活的，可以实现任何网状的依赖关系。那么通过这个发现，我想到利用它来做一件有趣的事情，可以求有向图最短路径的值，看2.3节。</p>
<h2><span style="color: #ff0000;">2.3 利用promise的拓扑特性做有趣的事 &mdash;&mdash; 有向图的最短路径另类求值</span></h2>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160329194504535-28924929.png" alt="" /></p>
<p style="text-align: center;">图2-5</p>
<p style="text-align: left;">　　如上这个有向图，要求0到3的最短路径，那么你可能第一想到的是Dijkstra算法、Floyd算法等等。</p>
<p style="text-align: left;">　　那么利用promise在2.2节中讲的特性，刚好可以用来求最短路径的值。但这里只是求值（玩玩），不能代替&ldquo;最短路径算法&rdquo;。上代码：</p>
<div class="cnblogs_code">
<pre><span style="color: #008080;"> 1</span> <span style="color: #0000ff;">var</span> Promise = require('bluebird'<span style="color: #000000;">);
</span><span style="color: #008080;"> 2</span> 
<span style="color: #008080;"> 3</span> <span style="color: #0000ff;">var</span> _base = 10<span style="color: #000000;">;　　// 等待时间基数
</span><span style="color: #008080;"> 4</span> 
<span style="color: #008080;"> 5</span> <span style="color: #0000ff;">var</span> dot0 = <span style="color: #0000ff;">new</span> Promise(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (resolve) {
</span><span style="color: #008080;"> 6</span>     <span style="color: #0000ff;">return</span> resolve('0'<span style="color: #000000;">);
</span><span style="color: #008080;"> 7</span> <span style="color: #000000;">});
</span><span style="color: #008080;"> 8</span> 
<span style="color: #008080;"> 9</span> <span style="color: #0000ff;">var</span> dot0_2 = dot0.then(<span style="color: #0000ff;">function</span><span style="color: #000000;"> () {
</span><span style="color: #008080;">10</span>     <span style="color: #0000ff;">return</span> <span style="color: #0000ff;">new</span> Promise(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (resolve) {
</span><span style="color: #008080;">11</span>         setTimeout(<span style="color: #0000ff;">function</span><span style="color: #000000;">() {
</span><span style="color: #008080;">12</span>             <span style="color: #0000ff;">return</span> resolve('0'<span style="color: #000000;">);
</span><span style="color: #008080;">13</span>         }, 5 *<span style="color: #000000;"> _base);
</span><span style="color: #008080;">14</span> <span style="color: #000000;">    });
</span><span style="color: #008080;">15</span> <span style="color: #000000;">});
</span><span style="color: #008080;">16</span> 
<span style="color: #008080;">17</span> <span style="color: #0000ff;">var</span> dot0_3 = dot0.then(<span style="color: #0000ff;">function</span><span style="color: #000000;"> () {
</span><span style="color: #008080;">18</span>     <span style="color: #0000ff;">return</span> <span style="color: #0000ff;">new</span> Promise(<span style="color: #0000ff;">function</span><span style="color: #000000;">(resolve) {
</span><span style="color: #008080;">19</span>         setTimeout(<span style="color: #0000ff;">function</span><span style="color: #000000;"> () {
</span><span style="color: #008080;">20</span>             <span style="color: #0000ff;">return</span> resolve('0'<span style="color: #000000;">);
</span><span style="color: #008080;">21</span>         }, 30 *<span style="color: #000000;"> _base);
</span><span style="color: #008080;">22</span> <span style="color: #000000;">    });
</span><span style="color: #008080;">23</span> <span style="color: #000000;">});
</span><span style="color: #008080;">24</span> 
<span style="color: #008080;">25</span> <span style="color: #0000ff;">var</span> dot2 =<span style="color: #000000;"> Promise.race([dot0_2]);
</span><span style="color: #008080;">26</span> 
<span style="color: #008080;">27</span> <span style="color: #0000ff;">var</span> dot2_1 = dot2.then(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (which) {
</span><span style="color: #008080;">28</span>     <span style="color: #0000ff;">return</span> <span style="color: #0000ff;">new</span> Promise(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (resolve) {
</span><span style="color: #008080;">29</span>         setTimeout(<span style="color: #0000ff;">function</span><span style="color: #000000;"> () {
</span><span style="color: #008080;">30</span>             <span style="color: #0000ff;">return</span> resolve(which + ' 2'<span style="color: #000000;">);
</span><span style="color: #008080;">31</span>         }, 15 *<span style="color: #000000;"> _base);
</span><span style="color: #008080;">32</span> <span style="color: #000000;">    });
</span><span style="color: #008080;">33</span> <span style="color: #000000;">});
</span><span style="color: #008080;">34</span> 
<span style="color: #008080;">35</span> <span style="color: #0000ff;">var</span> dot2_5 = dot2.then(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (which) {
</span><span style="color: #008080;">36</span>     <span style="color: #0000ff;">return</span> <span style="color: #0000ff;">new</span> Promise(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (resolve) {
</span><span style="color: #008080;">37</span>         setTimeout(<span style="color: #0000ff;">function</span><span style="color: #000000;"> () {
</span><span style="color: #008080;">38</span>             <span style="color: #0000ff;">return</span> resolve(which + ' 2'<span style="color: #000000;">);
</span><span style="color: #008080;">39</span>         }, 7 *<span style="color: #000000;"> _base);
</span><span style="color: #008080;">40</span> <span style="color: #000000;">    });
</span><span style="color: #008080;">41</span> <span style="color: #000000;">});
</span><span style="color: #008080;">42</span> 
<span style="color: #008080;">43</span> <span style="color: #0000ff;">var</span> dot5 =<span style="color: #000000;"> Promise.race([dot2_5]);
</span><span style="color: #008080;">44</span> 
<span style="color: #008080;">45</span> <span style="color: #0000ff;">var</span> dot5_3 = dot5.then(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (which) {
</span><span style="color: #008080;">46</span>     <span style="color: #0000ff;">return</span> <span style="color: #0000ff;">new</span> Promise(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (resolve) {
</span><span style="color: #008080;">47</span>         setTimeout(<span style="color: #0000ff;">function</span><span style="color: #000000;"> () {
</span><span style="color: #008080;">48</span>             <span style="color: #0000ff;">return</span> resolve(which + ' 5'<span style="color: #000000;">);
</span><span style="color: #008080;">49</span>         }, 10 *<span style="color: #000000;"> _base);
</span><span style="color: #008080;">50</span> <span style="color: #000000;">    });
</span><span style="color: #008080;">51</span> <span style="color: #000000;">});
</span><span style="color: #008080;">52</span> 
<span style="color: #008080;">53</span> <span style="color: #0000ff;">var</span> dot5_4 = dot5.then(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (which) {
</span><span style="color: #008080;">54</span>     <span style="color: #0000ff;">return</span> <span style="color: #0000ff;">new</span> Promise(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (resolve) {
</span><span style="color: #008080;">55</span>         setTimeout(<span style="color: #0000ff;">function</span><span style="color: #000000;"> () {
</span><span style="color: #008080;">56</span>             <span style="color: #0000ff;">return</span> resolve(which + ' 5'<span style="color: #000000;">);
</span><span style="color: #008080;">57</span>         }, 18 *<span style="color: #000000;"> _base);
</span><span style="color: #008080;">58</span> <span style="color: #000000;">    });
</span><span style="color: #008080;">59</span> <span style="color: #000000;">});
</span><span style="color: #008080;">60</span> 
<span style="color: #008080;">61</span> <span style="color: #0000ff;">var</span> dot1 =<span style="color: #000000;"> Promise.race([dot2_1]);
</span><span style="color: #008080;">62</span> 
<span style="color: #008080;">63</span> <span style="color: #0000ff;">var</span> dot1_4 = dot1.then(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (which) {
</span><span style="color: #008080;">64</span>     <span style="color: #0000ff;">return</span> <span style="color: #0000ff;">new</span> Promise(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (resolve) {
</span><span style="color: #008080;">65</span>         setTimeout(<span style="color: #0000ff;">function</span><span style="color: #000000;"> () {
</span><span style="color: #008080;">66</span>             <span style="color: #0000ff;">return</span> resolve(which + ' 1'<span style="color: #000000;">);
</span><span style="color: #008080;">67</span>         }, 8 *<span style="color: #000000;"> _base);
</span><span style="color: #008080;">68</span> <span style="color: #000000;">    });
</span><span style="color: #008080;">69</span> <span style="color: #000000;">});
</span><span style="color: #008080;">70</span> 
<span style="color: #008080;">71</span> <span style="color: #0000ff;">var</span> dot4 =<span style="color: #000000;"> Promise.race([dot1_4, dot5_4]);
</span><span style="color: #008080;">72</span> 
<span style="color: #008080;">73</span> <span style="color: #0000ff;">var</span> dot4_3 = dot4.then(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (which) {
</span><span style="color: #008080;">74</span>     <span style="color: #0000ff;">return</span> <span style="color: #0000ff;">new</span> Promise(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (resolve) {
</span><span style="color: #008080;">75</span>         setTimeout(<span style="color: #0000ff;">function</span><span style="color: #000000;"> () {
</span><span style="color: #008080;">76</span>             <span style="color: #0000ff;">return</span> resolve(which + ' 4'<span style="color: #000000;">);
</span><span style="color: #008080;">77</span>         }, 4 *<span style="color: #000000;"> _base);
</span><span style="color: #008080;">78</span> <span style="color: #000000;">    });
</span><span style="color: #008080;">79</span> <span style="color: #000000;">});
</span><span style="color: #008080;">80</span> 
<span style="color: #008080;">81</span> <span style="color: #0000ff;">var</span> dot3 =<span style="color: #000000;"> Promise.race([dot0_3, dot4_3, dot5_3])
</span><span style="color: #008080;">82</span>     .then(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (str) {
</span><span style="color: #008080;">83</span>         console.log('result: '<span style="color: #000000;">, str + ' 3');
</span><span style="color: #008080;">84</span>     });<br /><br />// 输出结果：<br />// 0 2 5 3</pre>
</div>
<p style="text-align: center;">代码2-6</p>
<p style="text-align: left;">　　如果我们把2-&gt;1边的权值改成4，即把第31行代码的15改成4，那么输出结果会是 ： 0 2 1 4 3</p>
<p style="text-align: left;">　　换种写法（结果一样）：</p>
<div class="cnblogs_code">
<pre><span style="color: #008080;"> 1</span> <span style="color: #0000ff;">var</span> Promise = require('bluebird'<span style="color: #000000;">);
</span><span style="color: #008080;"> 2</span> 
<span style="color: #008080;"> 3</span> <span style="color: #0000ff;">var</span> _base = 10<span style="color: #000000;">;
</span><span style="color: #008080;"> 4</span> <span style="color: #008000;">//</span><span style="color: #008000;"> key表示顶点，值表示出边</span>
<span style="color: #008080;"> 5</span> <span style="color: #0000ff;">var</span> digram =<span style="color: #000000;"> {
</span><span style="color: #008080;"> 6</span>     '0': { '2': 5, '3': 30<span style="color: #000000;"> },
</span><span style="color: #008080;"> 7</span>     '2': { '1': 15, '5': 7<span style="color: #000000;"> },
</span><span style="color: #008080;"> 8</span>     '5': { '3': 10, '4': 18<span style="color: #000000;"> },
</span><span style="color: #008080;"> 9</span>     '1': { '0': 2, '4': 8<span style="color: #000000;"> },
</span><span style="color: #008080;">10</span>     '4': { '3': 4<span style="color: #000000;"> },
</span><span style="color: #008080;">11</span>     '3'<span style="color: #000000;">: {}
</span><span style="color: #008080;">12</span> <span style="color: #000000;">};
</span><span style="color: #008080;">13</span> <span style="color: #0000ff;">var</span> order = ['0', '2', '5', '1', '4', '3'<span style="color: #000000;">];
</span><span style="color: #008080;">14</span> <span style="color: #0000ff;">var</span> startDot = '0'<span style="color: #000000;">;
</span><span style="color: #008080;">15</span> <span style="color: #0000ff;">var</span> endDot = '3'<span style="color: #000000;">;
</span><span style="color: #008080;">16</span> 
<span style="color: #008080;">17</span> <span style="color: #0000ff;">var</span> promiseMap =<span style="color: #000000;"> {};
</span><span style="color: #008080;">18</span> <span style="color: #0000ff;">function</span><span style="color: #000000;"> _buildMap() {
</span><span style="color: #008080;">19</span>     <span style="color: #0000ff;">for</span>(<span style="color: #0000ff;">var</span> dot <span style="color: #0000ff;">in</span><span style="color: #000000;"> digram)
</span><span style="color: #008080;">20</span>         promiseMap[dot] =<span style="color: #000000;"> {_promise: undefined, _in: [], _out: []};
</span><span style="color: #008080;">21</span>     <span style="color: #0000ff;">for</span>(<span style="color: #0000ff;">var</span> i = 0 ; i &lt; order.length; ++<span style="color: #000000;">i) {　　　　// <span style="color: #ff0000;">这里不能用 for(var dot in digram)，因为js对map的key会排序，这样取出来的dot顺序是0、1、2、3、4、5
</span></span><span style="color: #008080;">22</span>         <span style="color: #0000ff;">var</span> dot =<span style="color: #000000;"> order[i];
</span><span style="color: #008080;">23</span>         <span style="color: #0000ff;">if</span> (dot ==<span style="color: #000000;"> startDot) {
</span><span style="color: #008080;">24</span>             promiseMap[dot]._promise =<span style="color: #000000;"> Promise.resolve();
</span><span style="color: #008080;">25</span>         } <span style="color: #0000ff;">else</span> <span style="color: #0000ff;">if</span> (dot ==<span style="color: #000000;"> endDot) {
</span><span style="color: #008080;">26</span>             <span style="color: #0000ff;">var</span> localdot =<span style="color: #000000;"> dot;
</span><span style="color: #008080;">27</span>             promiseMap[dot]._promise =<span style="color: #000000;"> Promise.race(promiseMap[dot]._in)
</span><span style="color: #008080;">28</span>                 .then(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (str) {
</span><span style="color: #008080;">29</span>                     console.log('result: ', str + ' ' +<span style="color: #000000;"> localdot);
</span><span style="color: #008080;">30</span> <span style="color: #000000;">                });
</span><span style="color: #008080;">31</span>             <span style="color: #0000ff;">continue</span><span style="color: #000000;">;
</span><span style="color: #008080;">32</span>         } <span style="color: #0000ff;">else</span><span style="color: #000000;"> {
</span><span style="color: #008080;">33</span>         <span style="color: #0000ff;">debugger</span><span style="color: #000000;">;
</span><span style="color: #008080;">34</span>             promiseMap[dot]._promise =<span style="color: #000000;"> Promise.race(promiseMap[dot]._in);
</span><span style="color: #008080;">35</span> <span style="color: #000000;">        }
</span><span style="color: #008080;">36</span>         <span style="color: #0000ff;">for</span>(<span style="color: #0000ff;">var</span> edge <span style="color: #0000ff;">in</span><span style="color: #000000;"> digram[dot]) {
</span><span style="color: #008080;">37</span>             <span style="color: #0000ff;">var</span> edgePromise = 
<span style="color: #008080;">38</span>                 promiseMap[dot]._promise.then(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (which) {
</span><span style="color: #008080;">39</span>                     <span style="color: #0000ff;">var</span> self = <span style="color: #0000ff;">this</span><span style="color: #000000;">;
</span><span style="color: #008080;">40</span>                     <span style="color: #0000ff;">return</span> <span style="color: #0000ff;">new</span> Promise(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (resolve) {
</span><span style="color: #008080;">41</span>                         setTimeout(<span style="color: #0000ff;">function</span><span style="color: #000000;"> () {
</span><span style="color: #008080;">42</span>                             <span style="color: #0000ff;">return</span> resolve( (which ? which + ' ' : '') +<span style="color: #000000;"> self.dot);
</span><span style="color: #008080;">43</span>                         }, digram[self.dot][self.edge] *<span style="color: #000000;"> _base);　　　　// 这里不能直接访问外层dot、edge，因为异步函数被调用的时候值已经被改变，<span style="color: #ff0000;">也无法通过for循环里面保存tmpdot、tmpedge的办法，因为js没有块级作用域，es6新标准有块级作用域
</span></span><span style="color: #008080;">44</span> <span style="color: #000000;">                    });
</span><span style="color: #008080;">45</span> <span style="color: #000000;">                }.bind({dot: dot, edge: edge}));
</span><span style="color: #008080;">46</span> <span style="color: #000000;">            promiseMap[dot]._out.push(edgePromise);
</span><span style="color: #008080;">47</span> <span style="color: #000000;">            promiseMap[edge]._in.push(edgePromise);
</span><span style="color: #008080;">48</span> <span style="color: #000000;">        }
</span><span style="color: #008080;">49</span> <span style="color: #000000;">    }
</span><span style="color: #008080;">50</span> <span style="color: #000000;">}
</span><span style="color: #008080;">51</span> _buildMap();<br /><br />// 输出结果：<br />// 0 2 5 3</pre>
</div>
<p style="text-align: center;">代码2-7&nbsp;</p>
<p>&nbsp;</p>
<h2>2.4 .then链条的结构</h2>
<p>　　那么通过2.1、2.2节的理解，我们知道了，一个.then链条里面的结构并不是这样：</p>
<p style="text-align: center;"><img src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160331160535738-1825144401.png" alt="" />&nbsp;</p>
<p style="text-align: center;">图2-6</p>
<p>　　这是在同一个promise对象上多次.then的情况（代码2-5）。</p>
<p>　　而依次.then的链条（代码2-3 / 代码2-4）是这样的：</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160331160328988-96657080.png" alt="" /></p>
<p style="text-align: center;">图2-7</p>
<p>　　就是说如果这样的代码，不使用同一个promise对象，去.then两次，那么2.1中_addCallbacks的结构只会用到【this._promise0、】这一组，而不会有【this[base + index]】这些数据。</p>
<h2>2.5 Promise.prototype._target()&nbsp;</h2>
<p>　　2.1节留了一个疑问，在调用promise.then注册一个回调函数的时候，不是通过&ldquo; this._addCallbacks() &rdquo; 而是通过 &ldquo;target._addCallbacks() &rdquo;，那么这个target是什么？</p>
<p>　　通过上几节，了解了内部链条保存的细节，现在来看一下target。</p>
<p>　　看个示例代码：</p>
<p style="text-align: center;">&nbsp;<img src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160331165041691-1002630023.png" alt="" /></p>
<p style="text-align: center;">图2-8</p>
<p>　　那么通过app2.js，可以看到一般情况下，aPromise._target() 取到的target是this对象。通过target（aPromise）调用_addCallbacks时，bPromise是存在aPromise._promise0里面的。</p>
<p>　　通过app3.js，可以发现，当对aPromise使用一个pending状态的cPromise对象进行resolve时，aPromise._target()取到的target会变成cPromise，后续通过aPromise.then所创建的bPromise对象也都是通过target（cPromise）进行_addCallbacks的，这个时候aPromise._promise0就是undefined，而cPromise._promise0就是bPromise。</p>
<p>　　那么这里target的变动与promise链条的迁移如何实现呢？这里涉及到解决（settle）一个promise对象的细节，第3节会再讲到。</p>
<p>&nbsp;</p>
<hr />
<p>&nbsp;</p>
<h1>3. promise对象的resolve细节&nbsp;&mdash;&mdash; 解决阶段（.resolve）</h1>
<h2>3.1 resolve一个promise对象的几种情况</h2>
<p>　　看下示例代码：</p>
<div class="cnblogs_code">
<pre><span style="color: #0000ff;">var</span> Promise = require('bluebird'<span style="color: #000000;">);

</span><span style="color: #0000ff;">var</span> aPromise = <span style="color: #0000ff;">new</span> Promise(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (resolve) {
    </span><span style="color: #0000ff;">return</span><span style="color: #000000;"> resolve();　　// resolve的调用可能在任何异步回调函数里面
});

</span><span style="color: #0000ff;">var</span> bPromise = aPromise.then(<span style="color: #0000ff;">function</span><span style="color: #000000;"> () {
    </span><span style="color: #0000ff;">var</span> dPromise =<span style="color: #000000;"> Promise.resolve();
    </span><span style="color: #0000ff;">return</span><span style="color: #000000;"> dPromise;
});

</span><span style="color: #0000ff;">var</span> cPromise = bPromise.then(<span style="color: #0000ff;">function</span><span style="color: #000000;"> () {
    console.log(</span>'cPromise was resolved'<span style="color: #000000;">);
});</span></pre>
</div>
<p style="text-align: center;">代码3-1</p>
<p>1. 构造函数的回调函数里面，通过resolve()由我们手动触发解决，例如上面的 aPromise。resolve可能在任何异步回调函数里面被调用。</p>
<p>2. 通过Promise.resolve()创建一个已经被解决的promise对象</p>
<p>3. then函数注册的回调函数，会在上游promise对象被解决掉之后，由promise的机制触发后续promise对象被解决。比如aPromise被resolve之后，bPromise、cPromise 由Promise的机制进行解决。</p>
<p>　　这几种情况的细节在3.3节讲。</p>
<h2>3.2 Promise的队列管理对象 &mdash;&mdash; async</h2>
<p>　　async是Promise用来管理promise链中所有promise对象的settle 的一个<span style="color: #ff0000;">单例对象</span>，在async.js文件：</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160330120339410-1776100201.png" alt="" /></p>
<p style="text-align: center;">图3-1</p>
<p style="text-align: left;">　　async提供两个接口：</p>
<p style="text-align: left;">　　　　1.&nbsp;settlePromises：接收一个promise对象，针对3.1节中的情况1，调用async.settlePromises去把要settle的promise对象入队</p>
<p style="text-align: left;">　　　　2.&nbsp;invoke：接收一个回调函数的相关参数，针对3.1节中的情况2，把被settle的上游promise中保存的回调函数（2.1节中的参数组）通过async.invoke，把要执行的回调函数</p>
<p style="text-align: left;">&nbsp;</p>
<h2 style="text-align: left;">3.3 resolve一个promise链的细节</h2>
<p>　　针对3.1节讲的几种情况，进行详细说明。</p>
<h3>3.3.1 构造函数里的resolver</h3>
<p>&nbsp;　　来看代码：</p>
<p><img src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160401111041723-1783008318.png" alt="" /></p>
<p style="text-align: center;">图3-2</p>
<p>　　看右上角的示例代码，右下角是输出结果，为什么&ldquo;step 2&rdquo;不是在&ldquo;step 1&rdquo;之前呢？可以知道构造一个Promise对象时，传进去的函数是被同步执行的（如果是异步执行的，那么&ldquo;step 1&rdquo;必定在&ldquo;step 2&rdquo;之后），这也意味着，如果在该回调函数里面同步调用resolve()，那么该Promise对象被创建之后就已经是fulfilled状态了。【看step 2 的输出】。</p>
<p>　　可以从左边的源码看到，传给构造函数的回调函数是被同步执行的。</p>
<p>　　可以看出构造函数&ldquo;step 1&rdquo;被调用的时机。</p>
<h3>3.3.2 .then注册的回调函数被触发的机制 &mdash;&mdash; aPromise.then时，已是fulfilled状态</h3>
<p>　　那再来看上图3-2的示例代码中，通过aPromise.then()创建的bPromise对象。</p>
<p>　　我们知道aPromise 变成fulfilled之后，通过aPromise.then注册的bPromise也是会被settle的。而在aPromise.then的时候，aPromise本身已经是fulfilled状态的。那么通过&ldquo;step 3&rdquo;的输出、已经&ldquo;step 3&rdquo;和&ldquo;step 4&rdquo;的顺序，可以知道通过.then()创建的promise对象的onFulfilled函数是被异步执行的（不管.then的时候aPromise是否fulfilled），而且通过&ldquo;step 5&rdquo;的输出，我们可以猜到这个异步大致也是通过process.nextTick() 处理的。</p>
<p>　　我们来看看实现：</p>
<p>&nbsp;</p>
<h3>3.3.3&nbsp;.then注册的回调函数被触发的机制 &mdash;&mdash; aPromise.then时，处于pending状态</h3>
<p>　　3.3.2中讲了aPromise为已经fulfilled时，.then产生的后续promise对象在 async.invoke(target._settlePromiseAtPostResolution, target, callbackIndex)中通过process.nextTick进行settle。</p>
<p>　　那么如果&ldquo;图3-2&rdquo;的示例代码中，构造函数的resolver是这样的呢：</p>
<p>&nbsp;</p>
<p style="text-align: center;">代码3-2</p>
<p>　　那么aPromise.then产生bPromise时，aPromise还是pending状态，这时后续的bPromise对象的settle要等到aPromise被手动resolve()时再触发。</p>
<p>　　来看实现：</p>
<p>&nbsp;</p>
<h3>3.3.4&nbsp;.then注册的回调函数被触发的机制 &mdash;&mdash; bPromise.then时，bPromise本身就是.then产生的一个promise对象</h3>
<p>&nbsp;</p>
<p>&nbsp;</p>
<h3>3.3.5 .then链条的解决</h3>
<p>　　那么结合3.3.1 - 3.3.4，我们看这样一个promise链的解决时机是怎样的，示例代码：</p>
<div class="cnblogs_code">
<pre><span style="color: #0000ff;">var</span> aPromise = <span style="color: #0000ff;">new</span> Promise(<span style="color: #0000ff;">function</span><span style="color: #000000;"> (resolve) {
    </span><span style="color: #0000ff;">return</span><span style="color: #000000;"> resolve();
})
.then(</span><span style="color: #0000ff;">function</span> () {        <span style="color: #008000;">//</span><span style="color: #008000;"> 假设这里创建的是bPromise</span>
   <span style="color: #008000;">//</span><span style="color: #008000;"> task B </span>
<span style="color: #000000;">})
.then(</span><span style="color: #0000ff;">function</span> () {        <span style="color: #008000;">//</span><span style="color: #008000;"> 假设这里创建的是cPromise</span>
    <span style="color: #008000;">//</span><span style="color: #008000;"> task C</span>
});</pre>
</div>
<p>&nbsp;</p>
<p>　　解决顺序：</p>
<p>1. aPromise创建之时，同步执行了构造函数的回调函数，同步执行了resolve。这个是3.3.1节的情况。</p>
<p>2. bPromise在创建的时候，aPromise已经为fulfilled状态，这时通过async.invoke(target._settlePromiseAtPostResolution, target, callbackIndex)，把bPromise的settle任务放到process.nextTick。这个是3.3.2节的情况。</p>
<p>3. cPromise在创建的时候，注意这里cPromise不是通过aPromise.then产生的，而是bPromise.then产生的，那么这个时候bPromise还是pending状态的，所以cPromise的settle任务是3.3.4节里面的情况。</p>
<p>&nbsp;</p>
<h3>3.3.4 Promise.resolve()创建一个以解决的对象</h3>
<p>　　这种情况下类似于3.3.3中的，new Promise之后，在resolver里面同步resolve。</p>
<p>&nbsp;</p>
<h2>3.4 promise链的迁移</h2>
<p>　　回过来看2.1和2.5中提到的target的问题。看下Promise.prototype._target()的代码：</p>
<p>　　</p>
<p style="text-align: center;">图3-</p>
<p>　　promise对象内部的状态维护是通过一个 this._bitField属性，进行位运算去设置、判断状态的。看下相关代码：</p>
<p style="text-align: center;"><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160329093739519-732281573.png" alt="" />图2-2</p>
<p>&nbsp;</p>
<p>　　可以看到除了fulfilled、rejected等状态，还有isMigrated 和Following 状态，那么这个是什么呢？因为这两个状态主要效劳于内部实现，所以一般的使用文档里面可能只会提及fulfill、reject、pending这几个状态。</p>
<p>　　我们暂且来看看 this._target() 实现了什么：</p>
<p>&nbsp;<img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201603/804872-20160329094823082-686319077.png" alt="" /></p>
<p style="text-align: center;">图2-3</p>
<p>　　可以看到，如果promise对象是Following状态的话，就会一直取自身的followee，直到followee的源头。而如果不是Following状态，那么在上面._then函数里，可以忽略取target这个步骤，因为取到的target是this。</p>
<p>　　那么找一下promise什么时候被setFollowing，会发现就在3.3.1节中通过构造函数去resolveFromResolver的时候设置的，而且在特定的情况下发生：</p>
<p>　　　　当resolve的参数是一个处于pending状态的promise对象时，就会把该promise对象上的后续promise迁移过去。这时看回去2.5节中示例代码，明白为什么了把？</p></div>
