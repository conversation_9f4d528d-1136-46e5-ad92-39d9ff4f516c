
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>node源码详解（六） —— 从server.listen 到事件循环 - 野路子程序员 - 博客园</title>
<link type="text/css" rel="stylesheet" href="/bundles/blog-common.css?v=XrHjCqi-oUg9M4EcevxEUXvgm_dLNu97ZVKqK6ra-aE1"/>
<link id="MainCss" type="text/css" rel="stylesheet" href="/skins/sea/bundle-sea.css?v=PPe3jt1rcXPno6kucw0iQU2MWF3DbrvubQHRM0FEMas1"/>
<link id="mobile-style" media="only screen and (max-width: 768px)" type="text/css" rel="stylesheet" href="/skins/sea/bundle-sea-mobile.css?v=dCMFeRnDl5aeD8pIbxbwMGs8e9wn75b-E2rGISFE2uk1"/>
<link title="RSS" type="application/rss+xml" rel="alternate" href="http://www.cnblogs.com/papertree/rss"/>
<link title="RSD" type="application/rsd+xml" rel="EditURI" href="http://www.cnblogs.com/papertree/rsd.xml"/>
<link type="application/wlwmanifest+xml" rel="wlwmanifest" href="http://www.cnblogs.com/papertree/wlwmanifest.xml"/>
<script src="//common.cnblogs.com/script/jquery.js" type="text/javascript"></script>  
<script type="text/javascript">var currentBlogApp = 'papertree', cb_enable_mathjax=false;var isLogined=false;</script>
<script src="/bundles/blog-common.js?v=ufkHqY7CLaQ-FdEJCMtH3vR-Ia3VNX59Tf2LnMKYKak1" type="text/javascript"></script>
</head>
<body>
<a name="top"></a>

<!--done-->
<div id="header">
	
<!--done-->
<div class="header">
	<div class="headerText">
		<a id="Header1_HeaderTitle" class="headermaintitle" href="http://www.cnblogs.com/papertree/">大树先生</a><br>
		
	</div>
</div>

</div>

<div id="mytopmenu" >
	
		<div id="mylinks"><a id="blog_nav_sitehome" class="menu" href="http://www.cnblogs.com/">博客园</a> &nbsp;
<a id="blog_nav_myhome" class="menu" href="http://www.cnblogs.com/papertree/">首页</a> &nbsp;
<a id="blog_nav_newpost" class="menu" rel="nofollow" href="https://i.cnblogs.com/EditPosts.aspx?opt=1">新随笔</a> &nbsp;
<a id="blog_nav_contact" class="menu" rel="nofollow" href="https://msg.cnblogs.com/send/%E9%87%8E%E8%B7%AF%E5%AD%90%E7%A8%8B%E5%BA%8F%E5%91%98">联系</a> &nbsp;
<a id="blog_nav_rss" class="menu" href="http://www.cnblogs.com/papertree/rss">订阅</a><a id="blog_nav_rss_image" href="http://www.cnblogs.com/papertree/rss"><img src="//www.cnblogs.com/images/xml.gif" alt="订阅" /></a>&nbsp;
<a id="blog_nav_admin" class="menu" rel="nofollow" href="https://i.cnblogs.com/">管理</a>
</div>
		<DIV id="mystats"><div id="blog_stats">
随笔-9&nbsp;
评论-14&nbsp;
文章-0&nbsp;
<!--trackbacks-0-->
</div></DIV>
	
</div>
<div id="centercontent" >
	
<div id="post_detail">
<div class = "post">
	<h1 class = "postTitle"><a id="cb_post_title_url" class="postTitle2" href="http://www.cnblogs.com/papertree/p/5398008.html">node源码详解（六） —— 从server.listen 到事件循环</a></h1>
	<div id="cnblogs_post_body"><p>&nbsp;</p>
<p>声明：转载请保留声明头部并标明转载、并私信告知作者。原文：<a href="http://www.cnblogs.com/papertree/p/5398008.html" target="_blank">http://www.cnblogs.com/papertree/p/5398008.html</a></p>
<hr />
<p>　　我们在第3-5篇博客讲了js代码如何调用到C++接口的机制，其中暗含的require、process.binding这些过程。</p>
<p>　　这篇博客以server.listen(80)为例，讲这两点：</p>
<p>1. js代码深入、作用到libuv事件循环的过程【1.1节的问题2】</p>
<p>2. libuv事件循环本身的过程【1.1节的问题3】</p>
<h1>6.1 js到事件循环 &mdash;&mdash; 数据结构</h1>
<h2>6.1.1事件循环的核心数据结构 &mdash;&mdash; struct uv_loop_s default_loop_struct;</h2>
<p>　　还记得2.2节的流程图吗，js代码里面执行网络io操作，最终保存一个io观察者到default_loop_struct，在node进入事件循环的时候，再获取io观察者进行监听。</p>
<p>　　来看看struct uv_loop_s 的结构体定义：</p>
<p style="text-align: center;"><img src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160418133050085-851367609.png" alt="" /></p>
<p style="text-align: center;">图6-1-1</p>
<p>　　在这篇博客里主要关系的是watcher_queue、watchers、nwatchers、nfds这四个成员。</p>
<p>watcher_queue：io观察者链表，链表原理看6.4节。</p>
<p>watchers：是一个uv__io_t 类型的二级指针。这里维护的是一个io观察者映射表【实际是以fd为下标索引的数组】。</p>
<p>nwatchers：watchers数组的size，<span style="color: #ff0000;">因为是堆分配的动态数组，所以需要维护数组的长度</span>。</p>
<p>nfds：监听了多少个fd，不同于nwatchers，因为watchers里面很多元素是空的。</p>
<p>【注：c语言里面经常会有 &ldquo;typedef struct uv_loop_s uv_loop_t&rdquo;、&ldquo;typedef struct uv__io_s uv__io_t&rdquo;这种写法去给结构体类型起别名，这样的好处是用uv_loop_s去定义一个变量需要加上struct，而通过typedef的别名不用，比如：</p>
<div class="cnblogs_code">
<pre><span style="color: #0000ff;">struct</span><span style="color: #000000;"> uv_loop_s default_loop_struct;
uv_loop_t default_loop_struct;</span></pre>
</div>
<p>这两种写法是一样的。】</p>
<h2>6.1.2 io观察者结构体 &mdash;&mdash; struct uv__io_s</h2>
<p>　　6.1.1中看到，我们的网络io操作最终会封装成一个io观察者，保存到default_loop_struct的io观察者映射表&mdash;&mdash;watchers 里面。</p>
<p>　　来看一下封装的io观察者的定义：</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160416140834973-525791824.png" alt="" /></p>
<p style="text-align: center;">图6-1-2</p>
<p>　　可以看到一个io观察者封装了：</p>
<p>fd：文件描述符，操作系统对进程监听的网络端口、或者打开文件的一个标记</p>
<p>cb：回调函数，当相应的io观察者监听的事件被激活之后，被libuv事件循环调用的回调函数</p>
<p>events：交给libuv的事件循环（epoll_wait）进行监听的事件</p>
<h2>6.1.3 持有io观察者的结构体 &mdash;&mdash; 比如struct uv_tcp_s</h2>
<p>　　io观察者结构体（uv__io_s）&nbsp;是我们调用server.listen()之后，与libuv事件循环的交互数据。</p>
<p>　　事件循环数据结构default_loop_struct 维护uv__io_s的映射表&nbsp;&mdash;&mdash; watchers成员。</p>
<p>　　而用户的每一个io操作流程，最终也通过某个结构体来持有这个io观察者。比如当进行tcp的 io操作时，其对应的io观察者，由uv_tcp_s 结构体的 io_watcher成员持有：</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160416185539473-373831622.png" alt="" /></p>
<p style="text-align: center;">图6-1-3</p>
<p>&nbsp;</p>
<hr />
<p>&nbsp;</p>
<h1>6.2 js到事件循环 &mdash;&mdash; 流程</h1>
<p>　　6.1节讲了几个结构体和数据类型。这一节以这几行示例代码，介绍从js代码的io操作到保存io观察者的流程：</p>
<div class="cnblogs_code">
<pre><span style="color: #0000ff;">var</span> http = require(<span style="color: #800000;">'</span><span style="color: #800000;">http</span><span style="color: #800000;">'</span><span style="color: #000000;">);

function requestListener(req, res) {
    res.end(</span><span style="color: #800000;">'</span><span style="color: #800000;">hello world</span><span style="color: #800000;">'</span><span style="color: #000000;">);
}

</span><span style="color: #0000ff;">var</span> server =<span style="color: #000000;"> http.createServer(requestListener);
server.listen(</span><span style="color: #800080;">80</span>);</pre>
</div>
<p style="text-align: center;">代码6-2-1</p>
<p>　　其实这里http模块里面做的事情很简单，6-2-1示例代码等效于：</p>
<div class="cnblogs_code">
<pre><span style="color: #0000ff;">const</span> Server = require('_http_server'<span style="color: #000000;">).Server;

</span><span style="color: #0000ff;">function</span><span style="color: #000000;"> requestListener(req, res) {
    res.end(</span>'hello world'<span style="color: #000000;">);
}

</span><span style="color: #0000ff;">var</span> server = <span style="color: #0000ff;">new</span><span style="color: #000000;"> Server(requestListener);
server.listen(</span>80);</pre>
</div>
<p style="text-align: center;">代码6-2-2</p>
<p>　　面向用户的接口仅仅是一个requestListener回调函数、监听端口，那么调用server.listen(80)之后，经过多少个环节才形成一个io观察者？io观察者的回调函数被调用之后，又经过多少个环节才回调到用户的requestListener？</p>
<p>　　来看下有多少层：</p>
<h2>6.2.1 http层Server类&nbsp;&mdash;&mdash; lib/_http_server.js</h2>
<p>　　上述示例代码直接交互的是http Server类，看代码：</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160416165310613-478613021.png" alt="" /></p>
<p style="text-align: center;">图6-2-1</p>
<h3>A. 设置环节&nbsp;&mdash;&mdash; requestListener</h3>
<p>　　当用户new Server产生一个server对象时，server添加'request'事件监听器。</p>
<h3>B. 回调环节&nbsp;&mdash;&mdash; connectionListener</h3>
<p>　　可以看到http层的Server类继承了socket层（net.js）的Server类。并添加'connection'事件监听器，当有连接到来时，由socket层的Server类发射'connection'事件，http层connectionListener被调用，拿到来自socket层的一个socket对象，进行跟http协议相关的处理，把http请求相关的数据封装成req、res两个对象，emit 'request'事件，把req、res传给用户的requestListener回调函数。</p>
<h2>6.2.2 socket层Server类 &mdash;&mdash; lib/net.js</h2>
<p>　　net.Server是负责socket层的Server类，也是http.Server的基类：</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160416172425129-1413380283.png" alt="" /></p>
<p style="text-align: center;">图6-2-2</p>
<h3>A.&nbsp;listen环节&nbsp;&mdash;&mdash; 'connection'事件</h3>
<p>　　在执行listen操作时，socket层Server类给self._handle.onconnection赋上回调函数。self._handle是更下层的TCP类对象。</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160416173934770-1332459658.png" alt="" /></p>
<p style="text-align: center;">图6-2-3</p>
<h3>B. 回调环节&nbsp;&mdash;&mdash; onconnection函数</h3>
<p>　　当有连接到来时，底层回调了TCP类的onconnection函数（self._handle.onconnection），并传过来一个clientHandle，onconnection把clientHandle封装成socket对象，并发射'connection'事件，把socket传给上层的connectionListener监听器。</p>
<h2>6.2.3 node C++层TCP类 &mdash;&mdash; src/tcp_wrap.cc</h2>
<p>　　上面说到socket层的Server类与下层的交互是通过this._handle &mdash;&mdash; TCP类对象。【注意了TCP不是C++本身的类，而是C++用来表示js类的 FunctionTemplate】</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160416174953301-928513548.png" alt="" /></p>
<p style="text-align: center;">图6-2-4</p>
<h3>A. listen环节&nbsp;&mdash;&mdash; TCPWrap::OnConnection</h3>
<p>　　看到TCP这一层，执行listen时传给下层的回调函数是TCPWrap::OnConnection，而且可以看到与这一层交互的下一层就是libuv的接口了 &mdash;&mdash; uv_listen。</p>
<h3>B. 回调环节 &mdash;&mdash; onconnection</h3>
<p>　　上面讲到socket层Server类通过self._handle.onconnection = onconnection去设置回调函数。</p>
<p>　　这一层可以看到onconnection函数在TCPWrap::OnConnection里面通过tcp_wrap-&gt;MakeCallback去回调。</p>
<p>　　关于MakeCallback的实现在AsyncWrap类 &mdash;&mdash; TCPWrap的基类：</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160416180146988-947639953.png" alt="" /></p>
<p style="text-align: center;">图6-2-5</p>
<p>　　<span style="color: #ff0000;">这里有一行重要的代码 env() -&gt; tick_callback_function() -&gt; Call()。里面确保了当每次从C++陷入js领域、执行完js代码之后，会执行到诸如process.nextTick()设置的回调函数</span>。</p>
<p>　　通过2.2节我们可以知道，执行js代码只有两个时机：</p>
<p>1. 刚启动的时候执行app.js文件</p>
<p>2. 异步回调函数被触发（<span style="color: #ff0000;">注意回调函数有可能是被同步回调的</span>）</p>
<p>　　那么这里的AsyncWrap::MakeCallback()就是每次执行js异步回调函数时，从C++域陷入js域的位置。</p>
<h2>6.2.4 libuv层 uv_tcp_t结构体&nbsp;&mdash;&mdash; deps/uv/src/unix/tcp.c</h2>
<p>&nbsp;　　在app.js里面的server.listen(80)，通过http.Server -&gt; net.Server -&gt; TCPWrap，终于到达了libuv层。这一层，我们看到6.1节的数据结构的使用细节。关于io观察者如何被保存、如何被事件循环取出使用的细节，我们看6.3节。</p>
<p style="text-align: center;"><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160418115232507-250599094.png" alt="" />&nbsp;图6-2-6</p>
<p>　　看到uv_tcp_listen操作，通过调用uv__io_start 把自身的io_watcher（定义在6.1.2节）注册进tcp-&gt;loop（理解成6.1.1节里面的default_loop_struct &mdash;&mdash; 事件循环的数据结构）。</p>
<p>　　这里注意到，从上层传过来的cb（TCPWrap::OnConnection）保存在了tcp-&gt;connection_cb，而tcp-&gt;io_watcher.cb 保存的是 uv__server_io。</p>
<p>　　当有连接到来时，事件循环直接调用的cb是io_watcher里面的uv__server_io，里面先执行uv__accept等操作，再回调到stream-&gt;connection_cb。【注意到右边文件的stream-&gt;connection_cb实际上就是左边文件的tcp-&gt;connection_cb，uv_stream_t可以理解成uv_tcp_t的一个基类】</p>
<p>&nbsp;</p>
<hr />
<h1>6.3 事件循环与io观察者</h1>
<h2>6.3.1 io观察者的保存</h2>
<p>　　6.2.4节讲到libuv层封装了io观察者，通过uv__io_start，把io观察者保存到指定的事件循环数据结构 &mdash;&mdash; loop。来看看uv__io_start的细节：</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160418121143882-404656803.png" alt="" /></p>
<p style="text-align: center;">图6-3-1</p>
<p>　　这里的loop就是6.1.1节中的事件循环数据结构体，w就是6.1.2节中的io观察者结构体。</p>
<p>　　可以看到，添加一个io观察者需要两步操作：</p>
<p>1. 使用QUEUE_INSERT_TAIL 往loop-&gt;watcher_queue 添加io观察者，链表原理看6.4节。</p>
<p>2. 把io观察者保存在loop-&gt;watchers中 &mdash;&mdash; 以fd为索引的数组。loop-&gt;watchers实际上类似于映射表的功能，而不是观察者队列。</p>
<h2>6.3.2&nbsp;事件循环的核心 &mdash;&mdash;&nbsp;io观察者的取出与回调&nbsp;</h2>
<p>&nbsp;　　在2.2节的运行流程中知道事件循环最终调用了uv_run()进入了epoll_wait()等待，而uv_run的这个事件循环是调用了uv__io_poll()，那么来看看这个最终的循环：</p>
<p style="text-align: center;"><img src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160418135013366-1156737078.png" alt="" /></p>
<p style="text-align: center;">图6-3-2</p>
<p>　　通过2.2节的运行流程，我们知道在js代码里面添加一个io观察者（比如调用server.listen()）是先通过保存io观察者（uv__io_t 结构体）到uv_loop_t结构体的watcher_queue里面，而不是马上注册到epoll_wait()进行监听的。</p>
<p>　　当js代码执行完毕，进入C++域，再进入到uv__io_poll的时候，就需要这几个步骤：</p>
<p>1. 遍历 loop-&gt;watcher_queue，取出所有io观察者，这里取出的w就是图6-3-1中调用uv__io_start保存的io观察者 &mdash;&mdash; w。</p>
<p>2. 取出了w之后，调用epoll_ctl()，把w-&gt;fd（io观察者对应的fd）注册给系统的epoll机制，那么epoll_wait()时就监听对应的fd。</p>
<p>3. 当epoll_wait()返回了，拿出有事件到来的fd，这个时候loop-&gt;watchers 映射表就起到作用了，通过fd拿出对应的io观察者 &mdash;&mdash; w，调用w-&gt;cb()。</p>
<h2>6.3.3 setTimeout &mdash;&mdash; epoll_wait的timeout</h2>
<p>　　看到epoll_wait有个timeout参数，这里正是setTimeout的原理。试想一下，epoll_wait所监听的所有io观察者对应的fd都没有事件触发，而setTimeout所设置的timeout到达了，那么epoll_wait()也是需要返回，让setTimeout的回调函数能够得以运行的。</p>
<p>&nbsp;</p>
<hr />
<p>&nbsp;</p>
<h1>6.4 io观察者链表</h1>
<p>　　注意到4个点：</p>
<p>1. uv_loop_t 结构体的io观察者链表是void* [2]类型的watcher_queue来维护。</p>
<p>2. uv__io_t（io观察者） 结构体也拥有一个void* watcher_queue[2]。</p>
<p>3. 在uv__io_start里面，通过QUEUE_INSERT_TAIL宏，往loop-&gt;watcher_queue里面添加w-&gt;watcher_queue，而不是w（io观察者本身）。</p>
<p>4. 在uv__io_poll里面，通过QUEUE_HEAD宏，从loop-&gt;watcher_queue里面取出元素 q，这个q事实上只是w-&gt;watcher_queue字段，需要通过QUEUE_DATA宏，从q去取出w。</p>
<p>【<span style="color: #ff0000;">这跟c语言结构体的内存模型有关，可以通过一个成员的地址减去结构体内成员的偏移量，计算出结构体的在进程空间的内存地址。</span>这也是QUEUE_DATA宏所做的事。】</p>
<p>　　可以先来看看这几个宏的定义：</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160418152000726-234323139.png" alt="" /></p>
<p style="text-align: center;">图6-4-1</p>
<p>　　我们来看看下面这个图，第一个状态是uv_loop_t和两个uv__io_t里的watcher_queue成员执行了QUEUE_ININ之后的状态。</p>
<p>　　第二、三个状态是依次通过QUEUE_INSERT_TAIL宏往uv_loop_t的watcher_queue里面添加uv__io_t的watcher_queue之后的状态。</p>
<p style="text-align: center;"><img src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160419143446538-589831953.png" alt="" /></p>
<p style="text-align: center;">图6-4-2</p>
<p>&nbsp;</p></div><div id="MySignature"></div>
<div class="clear"></div>
<div id="blog_post_info_block">
<div id="BlogPostCategory"></div>
<div id="EntryTag"></div>
<div id="blog_post_info">
</div>
<div class="clear"></div>
<div id="post_next_prev"></div>
</div>


	<div class = "postDesc">posted on <span id="post-date">2016-04-18 15:28</span> <a href='http://www.cnblogs.com/papertree/'>野路子程序员</a> 阅读(<span id="post_view_count">...</span>) 评论(<span id="post_comment_count">...</span>)  <a href ="https://i.cnblogs.com/EditPosts.aspx?postid=5398008" rel="nofollow">编辑</a> <a href="#" onclick="AddToWz(5398008);return false;">收藏</a></div>
</div>
<script type="text/javascript">var allowComments=true,cb_blogId=242729,cb_entryId=5398008,cb_blogApp=currentBlogApp,cb_blogUserGuid='40a171c9-d74e-e511-b908-9dcfd8948a71',cb_entryCreatedDate='2016/4/18 15:28:00';loadViewCount(cb_entryId);</script>

</div><a name="!comments"></a><div id="blog-comments-placeholder"></div><script type="text/javascript">var commentManager = new blogCommentManager();commentManager.renderComments(0);</script>
<div id='comment_form' class='commentform'>
<a name='commentform'></a>
<div id='divCommentShow'></div>
<div id='comment_nav'><span id='span_refresh_tips'></span><a href='javascript:void(0);' onclick='return RefreshCommentList();' id='lnk_RefreshComments' runat='server' clientidmode='Static'>刷新评论</a><a href='#' onclick='return RefreshPage();'>刷新页面</a><a href='#top'>返回顶部</a></div>
<div id='comment_form_container'></div>
<div class='ad_text_commentbox' id='ad_text_under_commentbox'></div>
<div id='ad_t2'></div>
<div id='opt_under_post'></div>
<div id='ad_c1' class='c_ad_block'></div>
<div id='under_post_news'></div>
<div id='ad_c2' class='c_ad_block'></div>
<div id='under_post_kb'></div>
<div id='HistoryToday' class='c_ad_block'></div>
<script type='text/javascript'>
    fixPostBody();
    setTimeout(function () { incrementViewCount(cb_entryId); }, 50);
    deliverAdT2();
    deliverAdC1();
    deliverAdC2();    
    loadNewsAndKb();
    loadBlogSignature();
    LoadPostInfoBlock(cb_blogId, cb_entryId, cb_blogApp, cb_blogUserGuid);
    GetPrevNextPost(cb_entryId, cb_blogId, cb_entryCreatedDate);
    loadOptUnderPost();
    GetHistoryToday(cb_blogId, cb_blogApp, cb_entryCreatedDate);   
</script>
</div>


</div>
<div id="leftcontent">
	
		<DIV id="leftcontentcontainer">
			
<!--done-->
<div class="newsItem">
	<div id="blog-news"></div><script type="text/javascript">loadBlogNews();</script>
</div>

			<div id="blog-calendar" style="display:none"></div><script type="text/javascript">loadBlogDefaultCalendar();</script><br>
			<div id="blog-sidecolumn"></div><script type="text/javascript">loadBlogSideColumn();</script></DIV>
	
</div>

<!--done-->
<div class="footer">
	Powered by: <a href="http://www.cnblogs.com">博客园</a>	模板提供：<a href="http://blog.hjenglish.com">沪江博客</a>
	Copyright &copy;2016 野路子程序员
</div>



</body>
</html>
