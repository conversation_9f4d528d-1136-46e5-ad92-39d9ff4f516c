
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>node源码详解（七） —— 文件异步io、线程池【互斥锁、条件变量、管道、事件对象】 - 野路子程序员 - 博客园</title>
<link type="text/css" rel="stylesheet" href="/bundles/blog-common.css?v=XrHjCqi-oUg9M4EcevxEUXvgm_dLNu97ZVKqK6ra-aE1"/>
<link id="MainCss" type="text/css" rel="stylesheet" href="/skins/sea/bundle-sea.css?v=PPe3jt1rcXPno6kucw0iQU2MWF3DbrvubQHRM0FEMas1"/>
<link id="mobile-style" media="only screen and (max-width: 768px)" type="text/css" rel="stylesheet" href="/skins/sea/bundle-sea-mobile.css?v=dCMFeRnDl5aeD8pIbxbwMGs8e9wn75b-E2rGISFE2uk1"/>
<link title="RSS" type="application/rss+xml" rel="alternate" href="http://www.cnblogs.com/papertree/rss"/>
<link title="RSD" type="application/rsd+xml" rel="EditURI" href="http://www.cnblogs.com/papertree/rsd.xml"/>
<link type="application/wlwmanifest+xml" rel="wlwmanifest" href="http://www.cnblogs.com/papertree/wlwmanifest.xml"/>
<script src="//common.cnblogs.com/script/jquery.js" type="text/javascript"></script>  
<script type="text/javascript">var currentBlogApp = 'papertree', cb_enable_mathjax=false;var isLogined=false;</script>
<script src="/bundles/blog-common.js?v=ufkHqY7CLaQ-FdEJCMtH3vR-Ia3VNX59Tf2LnMKYKak1" type="text/javascript"></script>
</head>
<body>
<a name="top"></a>

<!--done-->
<div id="header">
	
<!--done-->
<div class="header">
	<div class="headerText">
		<a id="Header1_HeaderTitle" class="headermaintitle" href="http://www.cnblogs.com/papertree/">大树先生</a><br>
		
	</div>
</div>

</div>

<div id="mytopmenu" >
	
		<div id="mylinks"><a id="blog_nav_sitehome" class="menu" href="http://www.cnblogs.com/">博客园</a> &nbsp;
<a id="blog_nav_myhome" class="menu" href="http://www.cnblogs.com/papertree/">首页</a> &nbsp;
<a id="blog_nav_newpost" class="menu" rel="nofollow" href="https://i.cnblogs.com/EditPosts.aspx?opt=1">新随笔</a> &nbsp;
<a id="blog_nav_contact" class="menu" rel="nofollow" href="https://msg.cnblogs.com/send/%E9%87%8E%E8%B7%AF%E5%AD%90%E7%A8%8B%E5%BA%8F%E5%91%98">联系</a> &nbsp;
<a id="blog_nav_rss" class="menu" href="http://www.cnblogs.com/papertree/rss">订阅</a><a id="blog_nav_rss_image" href="http://www.cnblogs.com/papertree/rss"><img src="//www.cnblogs.com/images/xml.gif" alt="订阅" /></a>&nbsp;
<a id="blog_nav_admin" class="menu" rel="nofollow" href="https://i.cnblogs.com/">管理</a>
</div>
		<DIV id="mystats"><div id="blog_stats">
随笔-9&nbsp;
评论-14&nbsp;
文章-0&nbsp;
<!--trackbacks-0-->
</div></DIV>
	
</div>
<div id="centercontent" >
	
<div id="post_detail">
<div class = "post">
	<h1 class = "postTitle"><a id="cb_post_title_url" class="postTitle2" href="http://www.cnblogs.com/papertree/p/5405202.html">node源码详解（七） —— 文件异步io、线程池【互斥锁、条件变量、管道、事件对象】</a></h1>
	<div id="cnblogs_post_body"><p>&nbsp;</p>
<p>声明：转载请保留声明头部并标明转载、并私信告知作者。原文：<a href="http://www.cnblogs.com/papertree/p/5405202.html" target="_blank">http://www.cnblogs.com/papertree/p/5405202.html</a></p>
<hr />
<p>　　在上篇博客讲到，网络io通过封装io观察者（uv__io_t），添加到loop-&gt;watcher_queue队列。在2.2节中讲到文件异步io不同于网络io，文件异步io把请求操作交给线程池处理，所有线程池的异步io操作统一由一个io观察者来管理，等线程池处理完毕再通过该io观察者告知事件循环（epoll_wait）有异步io操作完成，需要在事件循环的线程执行回调函数。</p>
<p>　　这篇博客分以下几个部分讲解其中的细节：</p>
<p>1. 从文件异步io操作到封装请求交给线程池的过程</p>
<p>2. 线程池的原理、相关的系统支持【互斥锁、条件变量】</p>
<p>3. 线程池完成io操作后，告知主线程/事件循环的方式 &mdash;&mdash; 线程池统一的io观察者，及相关的系统支持【管道、事件对象】</p>
<p>4. 主线程epoll_wait收到线程池的通知后，回调到文件异步io操作的callback的过程</p>
<h1>7.1 文件异步io到线程池</h1>
<p>　　上一篇博客以server.listen(80)为例来讲解网络io，这一篇以fs.writeFile('xxx', function (err, data) {});为例来讲解文件异步io。</p>
<p>　　js代码到libuv的函数，经历了几个层次（6.2.1节-6.2.4节，&ldquo;原生js lib模块 -&gt; node C++模块 -&gt; libuv模块&rdquo;），这几个层次文件io和网络io是类似的，就忽略了。</p>
<p>　　这里只针对libuv的文件异步io如何封装成请求对象交给线程池。</p>
<h2>7.1.1 libuv的文件io请求对象 &mdash;&mdash; uv_fs_t</h2>
<p>　　看一下libuv的异步读文件代码，deps/uv/src/unix/fs.c：</p>
<p><img style="display: block; margin-left: auto; margin-right: auto;" src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160418204920804-1306490029.png" alt="" /></p>
<p style="text-align: center;">图7-1-1</p>
<p>　　可以看到一次异步文件读操作在libuv层被封装到一个uv_fs_t的结构体，req-&gt;cb是来自上层的回调函数（node C++层：src/node_file.cc 的After函数）。</p>
<p>　　异步io请求最后调用uv__work_submit，把异步io请求提交给线程池。这里有两个函数：</p>
<p>uv__fs_work：这个是文件io的处理函数，可以看到当cb为NULL的时候，即非异步模式，uv__fs_work在当前线程（事件循环所在线程）直接被调用。如果cb != NULL，即文件io为异步模式，此时把uv__fs_work和uv__fs_done提交给线程池。</p>
<p>uv__fs_done：这个是异步文件io结束后的回调函数。在uv__fs_done里面会回调上层C++模块的cb函数（即req-&gt;cb）。</p>
<p>　　<span style="color: #ff0000;">这里需要注意的是，异步模式下，把uv__fs_work、uv__fs_done当成参数调用uv__work_submit向线程池提交异步io请求，此时io操作的主体 &mdash;&mdash; uv__fs_work函数是在线程池里执行的。但是uv__fs_done必须在事件循环的线程里被回调，因为这个函数最终会回调到用户js代码的回调函数，而js代码里的所有代码必须在同个线程里面</span>。</p>
<h2>7.1.2 线程池的请求对象 &mdash;&mdash; struct uv__work</h2>
<p>　　来看看uv__work_submit做了什么：</p>
<p style="text-align: center;"><img src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160418211519273-1808841428.png" alt="" /></p>
<p style="text-align: center;">图7-1-2</p>
<p>　　uv__work_submit 把传进来的uv__fs_work、uv__fs_done封装到uv__work结构体里面，这个结构体表示一个线程操作的请求。通过post把请求提交给线程池，post的原理7.2节讲。</p>
<p>　　看到post函数里面的QUEUE_INSERT_TAIL，把该uv__work对象加进wq链表里面。<span style="color: #ff0000;">wq是一个全局静态变量。也就是说，进程空间里的所有线程共用同一个wq链表</span>。wq队列的使用在最下面的7.4.2节会用到。</p>
<p>　　至于通过void* [2]类型的成员变量w-&gt;wq去维护一个链表的机制，在6.4节里有介绍。</p>
<p>&nbsp;</p>
<hr />
<p>&nbsp;</p>
<h1>7.2 线程池的原理 &mdash;&mdash; 条件变量与互斥锁</h1>
<h2>7.2.1 条件变量与互斥锁基础</h2>
<p>1. 互斥锁 &mdash;&mdash; pthread_mutex_t mutex</p>
<p>　　系统通过pthread_mutex_t结构、及相关的pthread_mutex_lock()、pthread_mutex_unlock()来对共享资源的请求进行加锁、解锁。</p>
<p>2. 条件变量 &mdash;&mdash; pthread_cond_t condition</p>
<p>　　系统通过pthread_cond_t结构、及相关的pthread_cond_wait()、pthread_cond_signal()函数来实现线程间等待、通知的机制。</p>
<p>【注意：系统提供的条件变量机制必须结合互斥锁使用，也就是pthread_cond_wait(&amp;condition, &amp;mutex)需要传条件变量与一个互斥体结构，而且pthread_cond_wait之前必须获得互斥锁。其中原因简单来说就是条件变量本身也是需要加锁保护的资源。具体解释可以参考：<a href="http://stackoverflow.com/questions/6312342/pthread-cond-wait-and-mutex-requirement" target="_blank">http://stackoverflow.com/questions/6312342/pthread-cond-wait-and-mutex-requirement</a>】</p>
<h2>7.2.2 线程池原理</h2>
<p>　　来看看threadpool.c 文件的几个相关函数：</p>
<p style="text-align: center;"><img src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160418212528241-2045584301.png" alt="" /></p>
<p style="text-align: center;">图7-2-1</p>
<p>　　这里有四个环节：</p>
<p>1. 创建工作线程：</p>
<p>　　这里的init_once函数调用uv_thread_create创建了nthreads数量的工作线程，nthread默认为4。worker为工作线程的执行函数。</p>
<p>　　看到图7-1-2，有一行uv_once(&amp;once, init_once); 【uv_once对应的系统调用是pthread_once】。该行代码保证了init_once 有且仅被执行一次。在第一次调用uv__work_submit()时会执行一次init_once()。</p>
<p>2. 工作线程进入等待：</p>
<p>　　看到worker线程最终会陷入uv_cond_wait【对应的系统调用是pthread_cond_wait】进行等待，且idle_threads自增。</p>
<p>　　这里的&amp;cond、&amp;mutex分别是一个全局的静态条件变量、互斥体。</p>
<p>3. 提交任务到线程池：</p>
<p>　　看到post函数通过uv_cond_signal【对应的系统调用是pthread_cond_signal】向相应的条件变量&mdash;&mdash;cond发送信号，处在uv_cond_wait挂起等待的工作线程当中的某个被激活。</p>
<p>　　worker线程往下执行，从wq取出w（保存的过程见7.1节），执行w-&gt;work()（对应7.1节中的uv_fs_work）。</p>
<p>4. &nbsp;通知主线程的事件循环：</p>
<p>　　工作线程完成任务后，调用uv_async_send通知主线程某个统一的io观察者。这里的机制7.3节讲。</p>
<hr />
<h1>7.3 线程池统一的io观察者 &mdash;&mdash; 管道、事件对象</h1>
<h2>7.3.1 管道、事件对象</h2>
<p>　　管道、事件对象都是系统提供的机制，都可以用于线程间发送数据，所以这里可以用于线程间的通知。</p>
<p>1. 管道</p>
<p>　　管道的相关系统调用是pipe()、pipe2()。参考&nbsp;<a href="http://man7.org/linux/man-pages/man2/pipe.2.html" target="_blank">http://man7.org/linux/man-pages/man2/pipe.2.html</a></p>
<p>　　管道会创建两个fd，往fd[1]写数据，那么fd[0]就会收到数据。那么只需要把fd[0]添加到epoll_wait()所监听的io观察者队列里面，在工作线程需要通知的时候往fd[1]写数据，即能在主线程的epoll里面监听其他工作线程任务完成的通知。</p>
<p>2. 事件对象</p>
<p>　　事件对象的相关系统调用是eventfd()、eventfd2()。参考&nbsp;<a href="http://man7.org/linux/man-pages/man2/eventfd.2.html" target="_blank">http://man7.org/linux/man-pages/man2/eventfd.2.html</a></p>
<p>　　与管道不同的是eventfd()只会创建一个fd，事件对象的读写都通过这个fd。事件对象内部维护一个counter，往fd写一个8字节的整数，会往counter加，而读的时候会返回counter，如果counter为0，那么读操作会阻塞住（fd为阻塞模式）。而这个fd也是可以交由epoll机制进行监听的，那么也可以达到使用管道一样的目的。</p>
<p>&nbsp;3. 使用哪个？</p>
<p>　　这里libuv创建异步io观察者fd时，优先使用eventfd，如果系统不支持事件对象，就使用管道替代。看一下相关实现：</p>
<p style="text-align: center;"><img src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160419134447210-1520436942.png" alt="" /></p>
<p style="text-align: center;">图7-3-1</p>
<p>　　可以看到使用uv__eventfd2返回-1（errno = ENOSYS）时，uv__async_start里面使用管道替代了事件对象。而判断系统是否支持eventfd，是通过__NR_eventfd2宏去判断。</p>
<p>　　<span style="color: #ff0000;">这里需要注意的是：使用宏进行判断__NR_eventfd是否defined是在编译期，而uv__async_start的执行是在运行期，也就是说，如果你在不支持事件对象的系统编译之后，在支持事件对象的系统上运行，那么uv__eventfd2始终是返回-1的</span>。</p>
<h2>7.3.2 异步io观察者</h2>
<h3>7.3.2.1 数据结构 &mdash;&mdash; struct uv__async</h3>
<p>　　在6.1.3节讲了持有io观察者的结构体 uv_tcp_s，6.2.4节讲了网络io操作如何封装成uv_tcp_t结构体、并构造对应的io_watcher，6.3.1和6.4节讲了如何把io_watcher加进uv_loop_t default_loop_struct的watcher_queue队列里。</p>
<p>　　那么类似于网络io操作的io观察者（uv__io_t io_watcher）由uv_tcp_s结构体来持有，这里要讨论的异步io观察者也是由一个数据结构（struct uv__async）持有的io观察者。通过把持有的io观察者（io_watcher）加进loop-&gt;watcher_queue队列，来加进到epoll的观察者队列中。</p>
<p>　　看到6.1.1节中关于struct uv_loop_s default_loop_struct的截图，发现uv_loop_s里面有个成员 struct uv__async async_watcher。这个就是管理统一异步io观察者的数据结构，一个事件循环结构体（uv_loop_t）有且只有一个。类似于uv_tcp_s。</p>
<p>　　看一些uv__async的定义，也持有一个uv__io_t io_watcher，还有封装了一个cb：</p>
<p style="text-align: center;"><img src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160419145740070-1911467422.png" alt="" /></p>
<p style="text-align: center;">图7-3-2</p>
<h3>7.3.2.2 异步io观察者的保存与回调</h3>
<p>　　我们知道一个uv_tcp_t的io观察者，是在用户调用了网络io之后，才加进到loop-&gt;watcher_queue里面的。那么这个异步io观察者是在node启动时，通过一连串调用node::Start() -&gt; uv_default_loop() -&gt; uv_loop_init() -&gt; uv_async_init() -&gt; uv__async_start()，最终调用uv__io_start()，把loop-&gt;async_watcher所持有的io_watcher加进loop-&gt;watcher_queue的。uv__async_start()也是创建事件对象/管道的地方，在上图的7-3-1可以看到。</p>
<p>　　来看一下loop-&gt;async_watcher和loop-&gt;async_watcher.io_watcher封装的回调函数。</p>
<p style="text-align: center;"><img src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160419153254632-1363144008.png" alt="" /></p>
<p style="text-align: center;">图7-3-3</p>
<p>　　可以看到loop-&gt;async_watcher.io_watcher-&gt;cb 是uv__async_io；</p>
<p>　　loop-&gt;async_watcher.cb 是uv__async_event。</p>
<p>　　7.2.2节讲到worker线程完成w-&gt;work()之后，通过uv_async_send通知异步io观察者，uv_async_send的操作就是往事件对象/管道写东西，那么当io观察者收到数据，uv_run()里面的epoll_wait()返回该io_watcher的fd时，uv__async_io会先被回调，在uv__async_io里面会进而调用uv__async_event。看下代码：</p>
<p style="text-align: center;"><img src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160419154951226-115385412.png" alt="" /></p>
<p style="text-align: center;">图7-3-4</p>
<p>　　uv__aysnc_io里面取出的wa就是loop-&gt;async_watcher，所以wa-&gt;cb就是uv__async_event。</p>
<hr />
<p>&nbsp;</p>
<h1>7.4 线程池异步io之后的回调工作</h1>
<p>　　讲到uv__async_event这一步，我们回想一下此时应该执行什么处理：worker线程执行完了w-&gt;work()（其中w是提交线程池的请求结构体 uv__work），然后通知事件循环需要在主线程执行w-&gt;done()，而通知的这个过程就是通过 uv_async_send()往管道/事件对象写数据，激活epoll_wait()，根据返回的fd，由loop-&gt;watchers映射表拿到异步io观察者 &mdash;&mdash; loop-&gt;async_watcher.io_watcher，然后层层回调到uv__async_event，那么这个时候，我们是否要调用线程池完成了w-&gt;work()之后剩余的w-&gt;done()？</p>
<h2>7.4.1 uv__async_event() 到 uv__work_done()</h2>
<p>　　node里面多次使用void*[2]类型来维护一个链表，loop-&gt;async_handles也是。可以看到图6-1-1。那么async_handles保存什么链表呢？</p>
<p>　　看到图7-3-4，uv__async_event()就是从loop-&gt;async_handles链表里，取出struct uv_async_t结构类型的元素h，并调用回调函数h-&gt;async_cb()。</p>
<p>　　再看到图7-3-3，uv_async_init()里面，往loop-&gt;async_handles里面添加了struct uv_async_t* t。7.3.2.2节讲到的一系列调用流程有：uv_loop_init() -&gt; uv_async_init()，看下uv_loop_init()调用uv_async_init()的代码：</p>
<p style="text-align: center;"><img src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160419161202413-1150832996.png" alt="" /></p>
<p style="text-align: center;">图7-4-1</p>
<p>　　可以看到uv_loop_init()传给uv_async_init()的uv_async_t 是loop-&gt;wq_async，而async_cb是uv__work_done。</p>
<p>　　所以最终异步io观察者被激活之后，主线程回调到了uv__work_done()。uv__work_done在线程池模块（deps/uv/src/threadpool.c）里面。</p>
<h2>&nbsp;7.4.2 uv__work_done()</h2>
<p>　　看一下uv__work_done()的代码：</p>
<p style="text-align: center;"><img src="http://images2015.cnblogs.com/blog/804872/201604/804872-20160419163247757-134957613.png" alt="" /></p>
<p style="text-align: center;">图7-4-2</p>
<p>　　在7.1.2节就讲了post()提交请求时，往全局队列wq添加一个uv__work数据结构，那么最终uv__work_done()被调用的时候，从该wq取出所有w，执行w-&gt;done()，完成最终的回调。这里的w-&gt;done()就是7.1节中提到的fs__work_done()。</p>
<p>　　<span style="color: #000000;">注意了，这里的uv__work_done()是在主线程执行的，也就是你的js代码由始至终在同一个线程里面执行</span>。</p></div><div id="MySignature"></div>
<div class="clear"></div>
<div id="blog_post_info_block">
<div id="BlogPostCategory"></div>
<div id="EntryTag"></div>
<div id="blog_post_info">
</div>
<div class="clear"></div>
<div id="post_next_prev"></div>
</div>


	<div class = "postDesc">posted on <span id="post-date">2016-04-19 19:11</span> <a href='http://www.cnblogs.com/papertree/'>野路子程序员</a> 阅读(<span id="post_view_count">...</span>) 评论(<span id="post_comment_count">...</span>)  <a href ="https://i.cnblogs.com/EditPosts.aspx?postid=5405202" rel="nofollow">编辑</a> <a href="#" onclick="AddToWz(5405202);return false;">收藏</a></div>
</div>
<script type="text/javascript">var allowComments=true,cb_blogId=242729,cb_entryId=5405202,cb_blogApp=currentBlogApp,cb_blogUserGuid='40a171c9-d74e-e511-b908-9dcfd8948a71',cb_entryCreatedDate='2016/4/19 19:11:00';loadViewCount(cb_entryId);</script>

</div><a name="!comments"></a><div id="blog-comments-placeholder"></div><script type="text/javascript">var commentManager = new blogCommentManager();commentManager.renderComments(0);</script>
<div id='comment_form' class='commentform'>
<a name='commentform'></a>
<div id='divCommentShow'></div>
<div id='comment_nav'><span id='span_refresh_tips'></span><a href='javascript:void(0);' onclick='return RefreshCommentList();' id='lnk_RefreshComments' runat='server' clientidmode='Static'>刷新评论</a><a href='#' onclick='return RefreshPage();'>刷新页面</a><a href='#top'>返回顶部</a></div>
<div id='comment_form_container'></div>
<div class='ad_text_commentbox' id='ad_text_under_commentbox'></div>
<div id='ad_t2'></div>
<div id='opt_under_post'></div>
<div id='ad_c1' class='c_ad_block'></div>
<div id='under_post_news'></div>
<div id='ad_c2' class='c_ad_block'></div>
<div id='under_post_kb'></div>
<div id='HistoryToday' class='c_ad_block'></div>
<script type='text/javascript'>
    fixPostBody();
    setTimeout(function () { incrementViewCount(cb_entryId); }, 50);
    deliverAdT2();
    deliverAdC1();
    deliverAdC2();    
    loadNewsAndKb();
    loadBlogSignature();
    LoadPostInfoBlock(cb_blogId, cb_entryId, cb_blogApp, cb_blogUserGuid);
    GetPrevNextPost(cb_entryId, cb_blogId, cb_entryCreatedDate);
    loadOptUnderPost();
    GetHistoryToday(cb_blogId, cb_blogApp, cb_entryCreatedDate);   
</script>
</div>


</div>
<div id="leftcontent">
	
		<DIV id="leftcontentcontainer">
			
<!--done-->
<div class="newsItem">
	<div id="blog-news"></div><script type="text/javascript">loadBlogNews();</script>
</div>

			<div id="blog-calendar" style="display:none"></div><script type="text/javascript">loadBlogDefaultCalendar();</script><br>
			<div id="blog-sidecolumn"></div><script type="text/javascript">loadBlogSideColumn();</script></DIV>
	
</div>

<!--done-->
<div class="footer">
	Powered by: <a href="http://www.cnblogs.com">博客园</a>	模板提供：<a href="http://blog.hjenglish.com">沪江博客</a>
	Copyright &copy;2016 野路子程序员
</div>



</body>
</html>
