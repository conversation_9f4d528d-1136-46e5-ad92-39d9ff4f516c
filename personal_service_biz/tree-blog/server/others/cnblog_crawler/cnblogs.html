
<!DOCTYPE html>
<html lang="zh-cn">
<head>
<meta charset="utf-8"/>
<meta name="viewport" content="width=device-width, initial-scale=1" />
<title>野路子程序员 - 博客园</title>
<link type="text/css" rel="stylesheet" href="/bundles/blog-common.css?v=XrHjCqi-oUg9M4EcevxEUXvgm_dLNu97ZVKqK6ra-aE1"/>
<link id="MainCss" type="text/css" rel="stylesheet" href="/skins/sea/bundle-sea.css?v=PPe3jt1rcXPno6kucw0iQU2MWF3DbrvubQHRM0FEMas1"/>
<link id="mobile-style" media="only screen and (max-width: 768px)" type="text/css" rel="stylesheet" href="/skins/sea/bundle-sea-mobile.css?v=dCMFeRnDl5aeD8pIbxbwMGs8e9wn75b-E2rGISFE2uk1"/>
<link title="RSS" type="application/rss+xml" rel="alternate" href="http://www.cnblogs.com/papertree/rss"/>
<link title="RSD" type="application/rsd+xml" rel="EditURI" href="http://www.cnblogs.com/papertree/rsd.xml"/>
<link type="application/wlwmanifest+xml" rel="wlwmanifest" href="http://www.cnblogs.com/papertree/wlwmanifest.xml"/>
<script src="//common.cnblogs.com/script/jquery.js" type="text/javascript"></script>  
<script type="text/javascript">var currentBlogApp = 'papertree', cb_enable_mathjax=false;var isLogined=false;</script>
<script src="/bundles/blog-common.js?v=ufkHqY7CLaQ-FdEJCMtH3vR-Ia3VNX59Tf2LnMKYKak1" type="text/javascript"></script>
</head>
<body>
<a name="top"></a>

<!--done-->
<div id="header">
	
<!--done-->
<div class="header">
	<div class="headerText">
		<a id="Header1_HeaderTitle" class="headermaintitle" href="http://www.cnblogs.com/papertree/">大树先生</a><br>
		
	</div>
</div>

</div>

<div id="mytopmenu" >
	
		<div id="mylinks"><a id="blog_nav_sitehome" class="menu" href="http://www.cnblogs.com/">博客园</a> &nbsp;
<a id="blog_nav_myhome" class="menu" href="http://www.cnblogs.com/papertree/">首页</a> &nbsp;
<a id="blog_nav_newpost" class="menu" rel="nofollow" href="https://i.cnblogs.com/EditPosts.aspx?opt=1">新随笔</a> &nbsp;
<a id="blog_nav_contact" class="menu" rel="nofollow" href="https://msg.cnblogs.com/send/%E9%87%8E%E8%B7%AF%E5%AD%90%E7%A8%8B%E5%BA%8F%E5%91%98">联系</a> &nbsp;
<a id="blog_nav_rss" class="menu" href="http://www.cnblogs.com/papertree/rss">订阅</a><a id="blog_nav_rss_image" href="http://www.cnblogs.com/papertree/rss"><img src="//www.cnblogs.com/images/xml.gif" alt="订阅" /></a>&nbsp;
<a id="blog_nav_admin" class="menu" rel="nofollow" href="https://i.cnblogs.com/">管理</a>
</div>
		<DIV id="mystats"><div id="blog_stats">
随笔-9&nbsp;
评论-14&nbsp;
文章-0&nbsp;
<!--trackbacks-0-->
</div></DIV>
	
</div>
<div id="centercontent" >
	

<!--done-->


<div class="day">
	<div class = "dayTitle">
		<a id="homepage1_HomePageDays_DaysList_ctl00_ImageLink" Title="Day link" href="http://www.cnblogs.com/papertree/archive/2016/06/07.html"><img src="/skins/sea/images/title_post.gif" border=0 align=absmiddle></a>
		&nbsp;
		2016年6月7日		  
	</div>

	
			<div class = "postTitle">
				<a id="homepage1_HomePageDays_DaysList_ctl00_DayList_TitleUrl_0" class="postTitle2" href="http://www.cnblogs.com/papertree/p/5328134.html">promise/bluebird源码</a>
			</div>
			<div class="c_b_p_desc">摘要: 声明：转载请保留声明头部并标明转载、并私信告知作者。原文：http://www.cnblogs.com/papertree/p/5328134.html 今天把 bluebird（2.9.0版） 的源码看了，写成博客记录一下。 1. 带上几个问题看源码 1. promise链是如何实现的？ 2. p<a href="http://www.cnblogs.com/papertree/p/5328134.html" class="c_b_p_desc_readmore">阅读全文</a></div>
			<div class = "postDesc">posted @ 2016-06-07 17:58 野路子程序员 阅读(94) 评论(0)  <a href ="https://i.cnblogs.com/EditPosts.aspx?postid=5328134" rel="nofollow">编辑</a></div>
		
</div>

<div class="day">
	<div class = "dayTitle">
		<a id="homepage1_HomePageDays_DaysList_ctl01_ImageLink" Title="Day link" href="http://www.cnblogs.com/papertree/archive/2016/04/19.html"><img src="/skins/sea/images/title_post.gif" border=0 align=absmiddle></a>
		&nbsp;
		2016年4月19日		  
	</div>

	
			<div class = "postTitle">
				<a id="homepage1_HomePageDays_DaysList_ctl01_DayList_TitleUrl_0" class="postTitle2" href="http://www.cnblogs.com/papertree/p/5405202.html">node源码详解（七） —— 文件异步io、线程池【互斥锁、条件变量、管道、事件对象】</a>
			</div>
			<div class="c_b_p_desc">摘要: 声明：转载请保留声明头部并标明转载、并私信告知作者。原文：http://www.cnblogs.com/papertree/p/5405202.html 在上篇博客讲到，网络io通过封装io观察者（uv__io_t），添加到loop-&gt;watcher_queue队列。在2.2节中讲到文件异步io不同<a href="http://www.cnblogs.com/papertree/p/5405202.html" class="c_b_p_desc_readmore">阅读全文</a></div>
			<div class = "postDesc">posted @ 2016-04-19 19:11 野路子程序员 阅读(522) 评论(9)  <a href ="https://i.cnblogs.com/EditPosts.aspx?postid=5405202" rel="nofollow">编辑</a></div>
		
</div>

<div class="day">
	<div class = "dayTitle">
		<a id="homepage1_HomePageDays_DaysList_ctl02_ImageLink" Title="Day link" href="http://www.cnblogs.com/papertree/archive/2016/04/18.html"><img src="/skins/sea/images/title_post.gif" border=0 align=absmiddle></a>
		&nbsp;
		2016年4月18日		  
	</div>

	
			<div class = "postTitle">
				<a id="homepage1_HomePageDays_DaysList_ctl02_DayList_TitleUrl_0" class="postTitle2" href="http://www.cnblogs.com/papertree/p/5398008.html">node源码详解（六） —— 从server.listen 到事件循环</a>
			</div>
			<div class="c_b_p_desc">摘要: 声明：转载请保留声明头部并标明转载、并私信告知作者。原文：http://www.cnblogs.com/papertree/p/5398008.html 我们在第3-5篇博客讲了js代码如何调用到C++接口的机制，其中暗含的require、process.binding这些过程。 这篇博客以serv<a href="http://www.cnblogs.com/papertree/p/5398008.html" class="c_b_p_desc_readmore">阅读全文</a></div>
			<div class = "postDesc">posted @ 2016-04-18 15:28 野路子程序员 阅读(622) 评论(0)  <a href ="https://i.cnblogs.com/EditPosts.aspx?postid=5398008" rel="nofollow">编辑</a></div>
		
</div>

<div class="day">
	<div class = "dayTitle">
		<a id="homepage1_HomePageDays_DaysList_ctl03_ImageLink" Title="Day link" href="http://www.cnblogs.com/papertree/archive/2016/03/19.html"><img src="/skins/sea/images/title_post.gif" border=0 align=absmiddle></a>
		&nbsp;
		2016年3月19日		  
	</div>

	
			<div class = "postTitle">
				<a id="homepage1_HomePageDays_DaysList_ctl03_DayList_TitleUrl_0" class="postTitle2" href="http://www.cnblogs.com/papertree/p/5295344.html">node源码详解（五） —— 在main函数之前 —— js和C++的边界，process.binding</a>
			</div>
			<div class="c_b_p_desc">摘要: 声明：转载请保留声明头部并标明转载、并私信告知作者。原文：http://www.cnblogs.com/papertree/p/5295344.html 在上一篇博客（详解四）讲了 C++通过v8的Object类和FunctionTemplate类，创建对象、方法，设置属性、原型方法等，提供给运行时<a href="http://www.cnblogs.com/papertree/p/5295344.html" class="c_b_p_desc_readmore">阅读全文</a></div>
			<div class = "postDesc">posted @ 2016-03-19 22:49 野路子程序员 阅读(353) 评论(0)  <a href ="https://i.cnblogs.com/EditPosts.aspx?postid=5295344" rel="nofollow">编辑</a></div>
		
			<div class="postSeparator"></div>
		
			<div class = "postTitle">
				<a id="homepage1_HomePageDays_DaysList_ctl03_DayList_TitleUrl_1" class="postTitle2" href="http://www.cnblogs.com/papertree/p/5285705.html">node源码详解（四） —— js代码如何调用C++的函数</a>
			</div>
			<div class="c_b_p_desc">摘要: 声明：转载请保留声明头部并标明转载、并私信告知作者。原文：http://www.cnblogs.com/papertree/p/5285705.html 上面讲到node调用Script::Compile()和Script::Run()解析执行app.js，并把io操作和callback保存到def<a href="http://www.cnblogs.com/papertree/p/5285705.html" class="c_b_p_desc_readmore">阅读全文</a></div>
			<div class = "postDesc">posted @ 2016-03-19 17:59 野路子程序员 阅读(608) 评论(0)  <a href ="https://i.cnblogs.com/EditPosts.aspx?postid=5285705" rel="nofollow">编辑</a></div>
		
			<div class="postSeparator"></div>
		
			<div class = "postTitle">
				<a id="homepage1_HomePageDays_DaysList_ctl03_DayList_TitleUrl_2" class="postTitle2" href="http://www.cnblogs.com/papertree/p/5294678.html">linux下使用shadowsocks 做代理</a>
			</div>
			<div class="c_b_p_desc">摘要: 声明：转载请保留声明头部并标明转载、并私信告知作者。原文：http://www.cnblogs.com/papertree/p/5294678.html 1. 遇到的情况： 在阿里云服务器上需要执行“git clone https://chromium.googlesource.com/v8/v8.<a href="http://www.cnblogs.com/papertree/p/5294678.html" class="c_b_p_desc_readmore">阅读全文</a></div>
			<div class = "postDesc">posted @ 2016-03-19 11:39 野路子程序员 阅读(495) 评论(0)  <a href ="https://i.cnblogs.com/EditPosts.aspx?postid=5294678" rel="nofollow">编辑</a></div>
		
</div>

<div class="day">
	<div class = "dayTitle">
		<a id="homepage1_HomePageDays_DaysList_ctl04_ImageLink" Title="Day link" href="http://www.cnblogs.com/papertree/archive/2016/03/12.html"><img src="/skins/sea/images/title_post.gif" border=0 align=absmiddle></a>
		&nbsp;
		2016年3月12日		  
	</div>

	
			<div class = "postTitle">
				<a id="homepage1_HomePageDays_DaysList_ctl04_DayList_TitleUrl_0" class="postTitle2" href="http://www.cnblogs.com/papertree/p/5225994.html">node源码详解（三）—— js代码在node中的位置，process、require、module、exports的由来</a>
			</div>
			<div class="c_b_p_desc">摘要: 声明：转载请保留声明头部并标明转载、并私信告知作者。原文：http://www.cnblogs.com/papertree/p/5225994.html 我们用惯了process.nextTick()、require(&#39;xxx&#39;)、module.exports，但是它们哪里来呢？下文给出答案... <a href="http://www.cnblogs.com/papertree/p/5225994.html" class="c_b_p_desc_readmore">阅读全文</a></div>
			<div class = "postDesc">posted @ 2016-03-12 17:19 野路子程序员 阅读(654) 评论(3)  <a href ="https://i.cnblogs.com/EditPosts.aspx?postid=5225994" rel="nofollow">编辑</a></div>
		
			<div class="postSeparator"></div>
		
			<div class = "postTitle">
				<a id="homepage1_HomePageDays_DaysList_ctl04_DayList_TitleUrl_1" class="postTitle2" href="http://www.cnblogs.com/papertree/p/5225201.html">node源码详解（二 ）—— 运行机制 、整体流程</a>
			</div>
			<div class="c_b_p_desc">摘要: 声明：转载请保留声明头部并标明转载、并私信告知作者。原文：http://www.cnblogs.com/papertree/p/5225201.html 2.1 项目代码结构 node 主要的部分有4个【下图最左列就是node项目源码（4.2.2）的根目录】： 1. 原生 js模块：node提供给 <a href="http://www.cnblogs.com/papertree/p/5225201.html" class="c_b_p_desc_readmore">阅读全文</a></div>
			<div class = "postDesc">posted @ 2016-03-12 14:01 野路子程序员 阅读(1057) 评论(2)  <a href ="https://i.cnblogs.com/EditPosts.aspx?postid=5225201" rel="nofollow">编辑</a></div>
		
			<div class="postSeparator"></div>
		
			<div class = "postTitle">
				<a id="homepage1_HomePageDays_DaysList_ctl04_DayList_TitleUrl_2" class="postTitle2" href="http://www.cnblogs.com/papertree/p/5225009.html">node源码详解 （一）</a>
			</div>
			<div class="c_b_p_desc">摘要: 声明：转载请保留声明头部并标明转载、并私信告知作者。原文：http://www.cnblogs.com/papertree/p/5225009.html 1.1 好奇哪些问题？ 分析源码（以4.2.2版本的源码）之前，先带上几个问题： 1. node 如何执行js代码？在哪里？ 2. js代码的异步<a href="http://www.cnblogs.com/papertree/p/5225009.html" class="c_b_p_desc_readmore">阅读全文</a></div>
			<div class = "postDesc">posted @ 2016-03-12 13:56 野路子程序员 阅读(752) 评论(0)  <a href ="https://i.cnblogs.com/EditPosts.aspx?postid=5225009" rel="nofollow">编辑</a></div>
		
</div>
<div class="topicListFooter"></div>


</div>
<div id="leftcontent">
	
		<DIV id="leftcontentcontainer">
			
<!--done-->
<div class="newsItem">
	<div id="blog-news"></div><script type="text/javascript">loadBlogNews();</script>
</div>

			<div id="blog-calendar" style="display:none"></div><script type="text/javascript">loadBlogDefaultCalendar();</script><br>
			<div id="blog-sidecolumn"></div><script type="text/javascript">loadBlogSideColumn();</script></DIV>
	
</div>

<!--done-->
<div class="footer">
	Powered by: <a href="http://www.cnblogs.com">博客园</a>	模板提供：<a href="http://blog.hjenglish.com">沪江博客</a>
	Copyright &copy;2016 野路子程序员
</div>



</body>
</html>
