

var fs = require('fs');
var path = require('path');
var Promise = require('bluebird');
var request = Promise.promisify(require('request'));
var cheerio = require('cheerio');

var cnblogUrl = 'http://www.cnblogs.com/papertree/';

function crawlerCnblogs() {
  return request(cnblogUrl)
    .spread((content, body) => {
      body = body.replace(/\r\n/g, '\n');
      fs.writeFileSync(path.resolve(__dirname, 'cnblogs.html'), body);
      return body;
    });
}

function parseBlogs(body) {
  var $ = cheerio.load(body);
  var blogs = [];
  var postTitle = $('.postTitle');
  for(var i = 0; i < postTitle.length; ++i) {
    blogs.push(_parseTitle(postTitle[i]));
  }
  return blogs;

  function _parseTitle(titleDiv) {
    var info = {};
    // parse href and title
    if (titleDiv.children) {
      for(var i = 0; i < titleDiv.children.length; ++i) {
        if (titleDiv.children[i].name == 'a'
              && titleDiv.children[i].attribs) {
            info.href = titleDiv.children[i].attribs.href;  
            info.cnblogId = info.href.match(/.*\/(\d+)\.html/)[1];
            info.title = titleDiv.children[i].children[0].data;
            break;
          }
      }
    }

    // parse desc
    var descDiv = titleDiv.next.next;
    info.desc = descDiv.children[0].data;

    // parse postDesc
    var postDesc = descDiv.next.next.children[0];
    var desc = postDesc.data;
    var descPattern = /posted @ ([^\s]+) ([^\s]+).+阅读\((\d+)\) 评论\((\d+)\)/;
    var ret = desc.match(descPattern);
    info.postTime = ret[1] + ' ' + ret[2];
    info.readNum = ret[3];
    info.commentNum = ret[4];

    return info;
  }
}

function crawlerBlogsText(blogs) {
  return Promise.map(blogs, (blog) => {
    return request(blog.href)
      .spread((content, blogPage) => {
        blogPage = blogPage.replace(/\r\n/g, '\n');
        fs.writeFileSync(path.resolve(__dirname, blog.cnblogId + '.html'), blogPage);
      });
  }, {concurrency: 4}).then((result) => {
    return blogs;
  });
}

function saveBlogs(blogs) {
  
}

Promise.resolve()
  .then(crawlerCnblogs)
  .then(parseBlogs)
  .then(crawlerBlogsText)
  .then(saveBlogs)
  .catch((err) => {
    console.error(err);
  });
