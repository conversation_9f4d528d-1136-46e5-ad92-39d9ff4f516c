

var Promise = require('bluebird');
var path = require('path');
var co = require('co');
global.db = {};
process.chdir(path.join('../../'));

co(function *() {
  try {
  yield require('../../models')();
   
  return db.Comment.find({blogId: '5822c6570b8fe9385a6abc1d'})
    .then((result) => {
      console.log(result);
      return db.Comment.update({_id: '58442316919cf2349e3b6bea'}, {$set: {deleted: true}});
    });
  } catch(err) {
    console.log('error in co');
    console.error(err);
  };
});

