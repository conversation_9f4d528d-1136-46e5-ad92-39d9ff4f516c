
//
var path = require('path');
var _ = require('lodash');
var fs = require('fs');
var request = require('request');
var Promise = require('bluebird');
var cheerio = require('cheerio');
var save = require('./tomongo').save;
var toNestedReply = require('./tomongo').toNestedReply;
request = Promise.promisify(request);

var host = 'https://127.0.0.1';
var url = host + '/api/bloglist';
var cnblogUrl = 'https://www.cnblogs.com/papertree/p/';
//var cnblogCommentUrl = 'http://www.cnblogs.com/mvc/blog/GetComments.aspx?blogApp=papertree&pageIndex=0&postId=';
var cnblogCommentUrl = 'http://www.cnblogs.com/mvc/blog/GetComments.aspx?blogApp=papertree&pageIndex=0&anchorCommentId=3476141&_=1478760391151&postId=';


process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";
request(url)
  .then((res) => {
    var body = JSON.parse(res[1]);
    return Promise.map(body.msg, scrawlerComments, {concurrency: 1});
  })
  .catch((err) => {
    console.error(err);
  });


function scrawlerComments(data) {
  var url = cnblogUrl + data.cnblogId + '.html';

  url = cnblogCommentUrl + data.cnblogId;
  return request(url)
    .spread((content, body) => {
      body = JSON.parse(body);
      body = body.commentsHtml;
      var result = parseCommentDivs(body, data.cnblogId);
      console.log('----------------------------------');
      console.log(data.id, ' : ', data.url);
      console.log('----------------------------------');

      result.forEach((item) => {
        item.fromCnblog = true;
      });
      return save(result, data.id);
    });
}

function parseCommentDivs(body, id) {
  var $ = cheerio.load(body);
  var commentDivs = $('.feedbackItem');

  var filename = path.join(__dirname, 'cnblog_comments' + id + '.json');
  var comments = [];
  //for(var i = 0; i < commentDivs.length; ++i) {
  //  comments.push(_parseCommentDiv(commentDivs[i]));
  //}
  //fs.writeFileSync(filename, JSON.stringify(comments, null, 2));

  var comments = fs.readFileSync(filename, 'utf8');
  comments = JSON.parse(comments);
  if (!comments.length) {
    return comments;
  }

  comments = toNestedReply(comments);
  return comments;

  function _parseCommentDiv(div) {
    var retInfo = {};
    var dateSpan = $(div).find('.comment_date');
    retInfo.postTime = new Date(dateSpan.text());

    var authorAnchor = dateSpan.next();
    retInfo.link = authorAnchor[0].attribs.href;
    retInfo.user = authorAnchor[0].children[0].data;
    retInfo._user = retInfo.user;

    var commentBody = $(div).find('.blog_comment_body');

    var commentChildren = commentBody[0].children;
    if (commentChildren[0].type == 'tag') {
      retInfo._replyuser = commentChildren[1].data.replace(/\n/g, '');
      var commentDiv = commentChildren[2];
      if (!commentDiv) {
        var data = retInfo._replyuser.split(' ');
        retInfo._replyuser = data[0];
        retInfo.comment = data[1];
      } else {
        if (commentDiv.type == 'tag') {
          commentDiv = commentDiv.next;
        }
        retInfo.comment = commentDiv.data;
      }
    } else {
      retInfo.comment = commentChildren[0].data;
    }

    return retInfo;
  }
}

