[{"postTime": "2016-07-25T07:03:00.000Z", "link": "http://www.cnblogs.com/zjczoo/", "user": "早起的菜鸟", "comment": "楼主你好，我下载了node的源码，编译了，用clion怎么让它跑起来呢？我想看内部流程", "headpicurl": "https://luzeshu.com/avatar/a303645.png?id=18134639"}, {"postTime": "2016-07-26T14:02:00.000Z", "link": "http://www.cnblogs.com/papertree/", "user": "野路子程序员", "replyuser": "早起的菜鸟", "comment": "clion还没用过，我在linux上“./configure\"然后\"make\"，如果想生成debug模式就\"./configure BUILDTYPE=debug\"", "headpicurl": "https://luzeshu.com/pictures/hacker.jpg"}, {"postTime": "2016-07-28T06:49:00.000Z", "link": "http://www.cnblogs.com/zjczoo/", "user": "早起的菜鸟", "replyuser": "大树先生的面具", "comment": "哦哦，我研究出来了，再请问下，我打断点调试的时候，发现大部分变量都是引用，有什么办法可以查到这个指针引用的真实值，我这debug支持lldb输出的，也没找到合适的命令", "headpicurl": "https://luzeshu.com/avatar/a303645.png?id=18134639"}, {"postTime": "2016-07-29T15:00:00.000Z", "link": "http://www.cnblogs.com/papertree/", "user": "野路子程序员", "replyuser": "早起的菜鸟", "comment": "我调试用的gdb，可以直接执行语句查看", "headpicurl": "https://luzeshu.com/pictures/hacker.jpg"}, {"postTime": "2016-09-05T12:28:00.000Z", "link": "http://www.cnblogs.com/zjczoo/", "user": "早起的菜鸟", "replyuser": "野路子程序员", "comment": "执行哪条语句呢?", "headpicurl": "https://luzeshu.com/avatar/a303645.png?id=18134639"}, {"postTime": "2016-09-05T13:11:00.000Z", "link": "http://www.cnblogs.com/papertree/", "user": "野路子程序员", "replyuser": "早起的菜鸟", "comment": "gdb 不是可以直接print 查看变量吗", "headpicurl": "https://luzeshu.com/pictures/hacker.jpg"}, {"postTime": "2016-09-06T03:02:00.000Z", "link": "http://www.cnblogs.com/zjczoo/", "user": "早起的菜鸟", "replyuser": "野路子程序员", "comment": "<div id=\"comment_body_3504124\" class=\"blog_comment_body\"><a href=\"#3503839\" title=\"查看所回复的评论\" onclick=\"commentManager.renderComments(0,50,3503839);\">@</a>野路子程序员<br>比如<br><div class=\"cnblogs_Highlighter sh-gutter\"><div><div id=\"highlighter_584818\" class=\"syntaxhighlighter  cpp\"><div class=\"toolbar\"><span><a href=\"#\" class=\"toolbar_item command_help help\">?</a></span></div><table border=\"0\" cellpadding=\"0\" cellspacing=\"0\"><tbody><tr><td class=\"gutter\"><div class=\"line number1 index0 alt2\">1</div></td><td class=\"code\"><div class=\"container\"><div class=\"line number1 index0 alt2\">Local&lt;Value&gt; f_value = ExecuteString(env, MainSource(env), script_name);</div></div></td></tr></tbody></table></div></div></div><br><br>这里的f_value是一个对象,它的类型是v8::Local&lt;v8::Value&gt;,它有个val_成员,是个指针类型为V8::Value,地址是0x7ffc93deecc0；<br><img src=\"https://images2015.cnblogs.com/blog/303645/201609/303645-20160906105511426-*********.png\" alt=\"\" border=\"0\" \"=\"\"><br>然后我输出这个内存地址，得到的是这样的，<img src=\"https://images2015.cnblogs.com/blog/303645/201609/303645-20160906105802269-2091470646.png\" alt=\"\" border=\"0\" \"=\"\"><br>我认为的这里的f_value应该指向的是那个匿名函数，可是我不知道怎么确认是不是那个匿名函数？楼主大神，我可不可以加你的qq，向您请教下呢？</div>", "headpicurl": "https://luzeshu.com/avatar/a303645.png?id=18134639"}, {"postTime": "2016-09-08T14:37:00.000Z", "link": "http://www.cnblogs.com/papertree/", "user": "野路子程序员", "replyuser": "早起的菜鸟", "comment": "其实感觉有点难说清楚喔，那个f_value实际上是v8的一个对象，那么他里面的数据应该看v8的源码，对应的类是什么样的定义。还有那个“匿名函数”这个函数的概念是js语言的，在v8实现里面也是对应一个C++对象，如果要去确认一个C++对象它对应的是那个js函数的话，可能要看到v8关于“解析执行js代码”这一块的功能，感觉这个已经深入到另一个地方去了", "headpicurl": "https://luzeshu.com/pictures/hacker.jpg"}, {"postTime": "2016-09-10T13:01:00.000Z", "link": "http://www.cnblogs.com/zjczoo/", "user": "早起的菜鸟", "replyuser": "野路子程序员", "comment": "谢谢楼主大哥的耐心解答，你是我崇拜的偶像", "headpicurl": "https://luzeshu.com/avatar/a303645.png?id=18134639"}]