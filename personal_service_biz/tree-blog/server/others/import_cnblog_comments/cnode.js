

var path = require('path');
var _ = require('lodash');
var fs = require('fs');
var request = require('request');
var Promise = require('bluebird');
var cheerio = require('cheerio');
var save = require('./tomongo').save;
var toNestedReply = require('./tomongo').toNestedReply;
request = Promise.promisify(request);


var host = 'https://127.0.0.1';
var url = host + '/api/bloglist';

process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";
request(url)
  .then((res) => {
    var body = JSON.parse(res[1]);
    return Promise.map(body.msg, getCnodeComments, {concurrency: 1});
  });

function getCnodeComments(data) {
  if (!data.cnblogId) {
    return ;
  }
  delete data.content;
  delete data.abstract;

  var id = data.cnodeId;
  var url = 'https://cnodejs.org/api/v1/topic/' + id;
  return request(url)
    .then((res) => {
      try {
        var topic = JSON.parse(res[1]);
      } catch(err) {
        console.log(res[1]);
        return ;
      }
      if (!topic.success) {
        console.log('### ', data);
        console.log(topic);
        return ;
      }
      if (!topic.data.replies) {
        console.log('没有replies: ', topic.data.id);
        return ;
      }
      var replies = topic.data.replies;
      var comments = [];
      if (topic.data.id == '5716137fe84805cd5410ea21') {
        debugger;
      }
      for(var i = 0; i < replies.length; ++i) {
        let once = replies[i];
        let toPush = {
          _user: once.id,
          user: once.author.loginname,
          headpicurl: once.author.avatar_url,
          link: 'https://cnodejs.org/user/' + once.author.loginname,
          comment: once.content,
          postTime: once.create_at
        };
        if (toPush.user == 'bigtree9307') {
          toPush.headpicurl = 'https://luzeshu.com/pictures/hacker.jpg';
        }
        if (once.reply_id) {
          toPush._replyuser = _findFinalReplyId(replies, once.reply_id);
        }
        comments.push(toPush);
      }
      if (topic.data.id == '5716137fe84805cd5410ea21') {
        debugger;
      }
      
      comments = toNestedReply(comments);
      comments.forEach((item) => {
        item.fromCnode = true;
      });
      return save(comments, data.id);
    });
}

function _findFinalReplyId(replies, reply_id) {
  // to map
  var map = {};
  replies.forEach((reply) => {
    map[reply.id] = reply;
  });
  let final_id = reply_id;
  while(map[final_id].reply_id) {
    final_id = map[final_id].reply_id;
  }
  return final_id;
}
