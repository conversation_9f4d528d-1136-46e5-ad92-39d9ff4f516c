
var fs = require('fs');
var path = require('path');

var request = require('request');
var Promise = require('bluebird');
var cheerio = require('cheerio');

var files = fs.readdirSync(__dirname).filter(function (file) {
  if (file.substr(file.length - 5) == '.json') return true;
  return false;
});

var headmap = {
  'hust': '//pic.cnblogs.com/avatar/290046/20150716231810.png',
  'wangyonglong': '//pic.cnblogs.com/avatar/883520/20160118221431.png',
  '558342': '//pic.cnblogs.com/avatar/simple_avatar.gif',
  'zjczoo': '//pic.cnblogs.com/avatar/a303645.png?id=18134639',
  'papertree': "https://luzeshu.com/pictures/hacker.jpg"
};
for(var key in headmap) {
  headmap[key] = headmap[key].replace(/\/\/pic\.cnblogs.com/, 'https://luzeshu.com');
}

files.forEach((file) => {
  console.log(file);
  debugger;
  var comments = JSON.parse(fs.readFileSync(file, 'utf8'));
  for(var i = 0; i < comments.length; ++i) {
    let link = comments[i].link;
    let result = link.match(/com\/u\/(.+)\//);
    if (!result) {
      result = link.match(/com\/(.+)\//);
      if (!result) {
        console.log('warn link: ', link);
        continue;
      }
    }
    let uid = result[1];
    if (!headmap[uid]) {
      console.warn('不存在的uid' + uid);
    }
    comments[i].headpicurl = headmap[uid];
  }
  fs.writeFileSync(file, JSON.stringify(comments, null, 2));
});
