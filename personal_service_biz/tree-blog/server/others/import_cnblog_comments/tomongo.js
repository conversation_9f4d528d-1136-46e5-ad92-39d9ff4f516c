
var request = require('request');
var Promise = require('bluebird');
var _  = require('lodash');
request = Promise.promisify(request);

var host = 'https://127.0.0.1';

function _save(comments, id) {
  var commentUrl = host + '/comment';
  var options = {
    url: commentUrl,
    method: 'POST'
  };

  return Promise.map(comments, function (oneComment) {
    var data = _.extend({}, oneComment);
    data.blogId = id;
    delete data.reply;
    options.form = data;
    //console.log(options);
    return request(options)
      .then((result) => {
        var body = JSON.parse(result[1]);
        if (body.code) {
          console.log(data);
          console.log(body);
        }

        var commentId = body.msg;
        return Promise.each(oneComment.reply, (eachReply) => {
          var data = _.extend({}, eachReply);
          data.blogId = id;
          data.commentId = commentId;
          options.form = data;
          //console.log('--- reply ---', data);
          return request(options)
            .then((result) => {
              //console.log(result[1]);
              console.log(data);
              var body = JSON.parse(result[1]);
              if (body.code) {
                console.log(body);
              }
            });
        });
        //console.log(body);
      });
  }, {concurrency: 1});
}

function _toNestedReply(comments) {
  var ret = [];
  for(var i = 0; i < comments.length; ++i) {
    if (!comments[i]._replyuser) {
      comments[i].reply = [];
      ret.push(comments[i]);
    } else {
      for(var j = 0; j < ret.length; ++j) {
        //if (ret[j].user == comments[i].user
        if (ret[j]._user == comments[i]._replyuser) {
          ret[j].reply.push(comments[i]);    
          break;
        }
      }
    }
  }
  return ret;
}


exports.save = _save;
exports.toNestedReply = _toNestedReply;
