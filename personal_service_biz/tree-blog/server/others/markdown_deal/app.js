

var Promise = require('bluebird');
var path = require('path');
var co = require('co');
global.db = {};
process.chdir(path.join('../../'));

function fixMarkDown(blog) {
  var result = null;
  // 替换 __ 为 \_\_
  // 提示前后各十个字符
  console.log('before : ');
  result = blog.content.match(/(.{10}(__).{10})/g);
  console.log(result);
  blog.content = blog.content.replace(/__/g, '\\_\\_');
  console.log('\n\n\nafter : ');
  result = blog.content.match(/(.{10}(__).{10})/g);
  console.log('match __ : ', result);

  result = blog.content.match(/(.{10}(\\_\\_).{10})/g);
  console.log('\n\n match\\_\\_ : ', result);


  // 替换 _ 为 \_
  result = blog.content.match(/(.{10}([^\\]_).{10})/g);
  console.log('\n\n\n match_: ', result);
  // 使用$1，不在replace的时候把匹配到的^\\也一起替换了
  blog.content = blog.content.replace(/([^\\])(_)/g, '$1\\_');
  result = blog.content.match(/(.{10}([^\\]_).{10})/g);
  console.log('\n\n\n after match_: ', result);

  result = blog.content.match(/(.{10}(\\_).{10})/g);
  console.log('\n\n\n after match \\_: ', result);


  // 替换 * 为 \*
  result = blog.content.match(/.{10}\*.{10}/g);
  console.log('\n\n\n match * : ', result);
  blog.content = blog.content.replace(/\*/g, '\\*');
  console.log('\n\n\n after: ');
  result = blog.content.match(/.{10}\*.{10|/g);
  console.log(result);
  console.log('\n\n\n after match \\*: ');
  result = blog.content.match(/.{10}\\\*.{10|/g);
  console.log(result);


  // 处理code代码段的\*和\_
  function dealCode(ch) {
    var starReg = /(```[\s\S]+?)(\\\*)([\s\S]+?```)/g;
    var lodashReg = /(```[\s\S]+?)(\\_)([\s\S]+?```)/g;
    var reg = ch == '*' ? starReg : lodashReg;
    while(result = blog.content.match(reg)) {
      console.log('before: ', result);
      blog.content = blog.content.replace(reg, '$1' + ch + '$3');
      result = blog.content.match(reg);
      console.log('after: ', result);
    }
  }
  dealCode('*');
  dealCode('_');


  if (!blog.url) {
    blog.url = 'dd';
  }
  return blog.save();
}


co(function *() {
  try {
  yield require('../../models')();
    /*
  return db.Blog.findById("581ef8e44e6f6e0b27cccd73")
    .then((blog) => {
      //var result = blog.content.match(/```(.|\n)*?```/gm);
      //for(var i = 0; i < result.length; ++i) {
      //  console.log('------------------ ' + i + '----------------');
      //  console.log(result[i]);
      //}
      //blog.content.replace(/```(.|\n)*?```/gm, /
      //
      var result = blog.content;
      for(var i = 600; i < 700; ++i) {
        console.log(i, ' : ', result.charAt(i), ' : ', new Buffer(result.charAt(i)));
      }
      debugger;
    });
    */
    
  return db.Blog.find()
    .then((result) => {
      return Promise.resolve(result)
      .map((blog) => {
        return fixMarkDown(blog);
      }, {concurrency: 1})
      .catch((err) => {
        console.error(err);
      });
    });
  } catch(err) {
    console.log('error in co');
    console.error(err);
  };
});

