'use strict';

var co = require('co');
var _ = require('lodash');
var mongoModel = require('../../models/mongo');
var mysqlModel = require('../../models/mysql');
var mongoServices = require('../../services/mongo');
var mysqlServices = require('../../services/mysql');
var mongoDb = {};
var mysqlDb = {};

global.Promise = require('bluebird');

co(function*() {
  yield mongoModel(mongoDb);
  yield mysqlModel(mysqlDb);
})
.then(() => {
  global.db = mongoDb;
// find out all mongodb blogs
  return mongoServices.blogList()
    .tap((res) => {
      return;
      global.db = mysqlDb;
      // insert to mysql blogs
      return Promise.resolve(Object.keys(res))
        .each((idx) => {
          let blog = res[idx];
          return mysqlServices.blogPost(blog);
        })
    })
    //.then((res) => _.map(res, 'id'));
})

.then(() => {
 // find out all mongodb 
 global.db = mongoDb;

 return mongoServices.commentList()
  .then((res) => {
    global.db = mysqlDb;
    return Promise.resolve(Object.keys(res))
      .each((idx) => {
        var comment = res[idx];
        var reply = comment.reply;
        comment.reply = undefined;
        return mysqlServices.commentPost(comment)
        //return Promise.resolve(comment.id)
          .then((_id) => {
            return Promise.resolve(reply)
              .each((oneReply) => {
                return mysqlServices.commentReply(comment, oneReply);
              });
          });
      });
  });
})

.catch((err) => {
  console.error(err);
});
