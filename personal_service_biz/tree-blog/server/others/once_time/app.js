

var request = require('request');
var Promise = require('bluebird');
request = Promise.promisify(request);

var host = 'https://127.0.0.1';
var url = host + '/api/bloglist';

process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";
request(url)
  .then((res) => {
    var body = JSON.parse(res[1]);
    return Promise.map(body.msg, (data) => {
      //return dealId(data)
      //return cutoffDeclare(data)
      return dealTag(data)
        .then((result) => {
          var id = data.id;
          var options = {
            method: 'PUT',
            url: host + '/api/blog/' + id,
            form: result 
          };
          return request(options)
            .then((res) => {
              console.log(res[1]);
            });
        });
    }, {concurrency: 1});
  })
  .catch((err) => {
    console.error(err);
  });


/**
 * 2016/11/09
 * 修改原文的声明链接到自己的个人网站
 */
function dealId(data) {
  var result = data.content.match(/原文.*(http.*\/p\/(\d+)\.html)/);
  data.cnblogId = result[2];
  data.content = data.content.replace(/原文.*(http.*\/p\/\d+\.html)/, '原文：https://luzeshu.com/blog/' + data.url);
  return Promise.resolve(data);
}

/**
 * 2016/11/12 15:00
 * 取消博客正文的声明头部，声明头部由模板引擎统一处理
 */
function cutoffDeclare(data) {
  data.content = data.content.replace(/#*\s*声明.*\n+/, '', '');
  return Promise.resolve(data);
}

/**
 * 2016/11/12 20:03
 * 处理center标签等问题
 */
function dealTag(data) {
  if (data.cnblogId == 5328134) {
    var idx = data.content.indexOf('代码2-2');
    var str = data.content.substring(idx - 100, idx + 100);
    console.log(str);
    debugger;
  }
  return data;
}
