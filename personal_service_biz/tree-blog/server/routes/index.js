/**
 * @file routes index file
 */
'use strict';

var crypto = require('crypto');
var rawBody = require('raw-body');
var xml2js = require('xml2js-es6-promise');
var page = require('../controllers/page');
var blog = require('../controllers/blog');
var comment = require('../controllers/comment');
var user = require('../controllers/user');
var schemas = require('../schemas');
var checkParams = require('../middlewares/check-params');
var checkLogin = require('../middlewares/check-login');
var Router = require('koa-router');
var router = new Router();
var routerLogin = new Router();

//page routing
router.get('/', page.home);
router.get('/tech', page.tech);
router.get('/blog/:url', page.blogDetailPage);

router.post('/comment', checkParams(schemas.commentPostSchema), comment.commentPost);
//router.post('/reply', checkParams(schemas.commentReplySchema), comment.commentReply);
//
//router.get('/api/bloglist', blog.blogList);
//router.put('/api/blog/:id', checkParams(schemas.blogUpdateSchema), blog.blogUpdate);

router.get('/gate', page.login);
router.post('/gate', checkParams(schemas.loginSchema), user.login);

router.get('/wx', checkParams(schemas.wxSchema), function *(next) {
  var query = this.validators.query;
  var token = 'silverbullet';
  var list = [token, query.timestamp, query.nonce];
  list.sort();
  logger.info('list: ', list);
  var liststr = list[0] + list[1] + list[2];
  var sha1 = crypto.createHash('sha1');
  var hashCode = sha1.update(liststr, 'utf-8').digest('hex');
  logger.info('hashCode: ', hashCode);
  if (hashCode != query.signature) {
    this.body = {
      code: 5,
      msg: '校验失败'
    };
  } else {
    this.body = query.echostr;
  }
});

router.get('/wxauthcallback', function*(next) {
  logger.info('this:', this);
  this.body = '<!DOCTYPE><html>hello</html>';
});

router.post('/wx', function *(next) {
  var data = yield rawBody(this.req, {
    encoding: 'utf8'
  });
  var message = yield xml2js(data);
  logger.info('message: ', message);
  //this.set('Content-Type', 'text/xml');
  this.body = '<xml><ToUserName><![CDATA[oYuzestaEgWZkkeAes2JXEarDXMo]]></ToUserName><FromUserName><![CDATA[gh_f8fd2197add0]]></FromUserName><CreateTime>'+new Date().getTime()+'</CreateTime><MsgType><![CDATA[text]]></MsgType><Content><![CDATA[this is a test]]></Content></xml>';
});
router.get('/wxhello', function*(next) {
  logger.info('/wxhello', this.req);
  this.body = {code: 0};
});

// must checkLogin below
routerLogin.use(checkLogin);

// page routing
routerLogin.get('/hell', page.admin);
routerLogin.get('/hell/*', function*() {
  this.redirect('/hell');
});

// api routing
routerLogin.get('/api/bloglist', blog.blogList);
routerLogin.post('/api/blog', checkParams(schemas.blogPostSchema), blog.blogPost);
routerLogin.get('/api/blog/:id', checkParams(schemas.blogInfoSchema), blog.blogInfo);
routerLogin.put('/api/blog/:id', checkParams(schemas.blogUpdateSchema), blog.blogUpdate);
routerLogin.delete('/api/blog/:id', checkParams(schemas.blogDelSchema), blog.blogDelete);

routerLogin.get('/api/commentlist', checkParams(schemas.commentListSchema), comment.commentList);
routerLogin.delete('/api/comment', checkParams(schemas.commentDelSchema), comment.commentDelete);
routerLogin.put('/api/comment/:id', checkParams(schemas.commentUpdateSchema), comment.commentUpdate);

routerLogin.get('/api/unreadmsg', comment.unreadMsg);

module.exports = [router.routes(), routerLogin.routes()];
