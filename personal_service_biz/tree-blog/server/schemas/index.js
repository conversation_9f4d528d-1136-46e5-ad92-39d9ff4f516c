
exports.wxSchema = {
  properties: {
    signature: {
      from: 'query', 
      type: 'string',
      isrequire: true
    },
    timestamp: {
      from: 'query', 
      type: 'string',
      isrequire: true
    },
    nonce: {
      from: 'query', 
      type: 'string',
      isrequire: true
    },
    echostr: {
      from: 'query', 
      type: 'string',
      isrequire: true
    }
  }
};

exports.blogPostSchema = {
  properties: {
    title: {
      from: 'body',
      type: 'string',
      maxlength: 100,
      isrequire: true
    },
    url: {
      from: 'body',
      type: 'string',
      maxlength: 500,
      isrequire: true,
    },
    content: {
      from: 'body',
      type: 'string',
      isrequire: true
    },
    abstract: {
      from: 'body',
      type: 'string',
      isrequire: true
    }
  }
};

exports.blogDelSchema = {
  properties: {
    id: {
      from: 'params',
      type: 'integer',
      isrequire: true
    }
  }
};

exports.blogUpdateSchema = {
  properties: {
    id: {
      from: 'params',
      type: 'integer',
      isrequire: true
    },
    status: {
      from: 'body',
      type: 'string',
      oneof: ['POST', 'UNPOST']
    },
    content: {
      from: 'body',
      type: 'string'
    },
    title: {
      from: 'body',
      type: 'string',
      maxlength: 100
    },
    url: {
      from: 'body',
      type: 'string',
      maxlength: 500
    },
    abstract: {
      from: 'body',
      type: 'string'
    },
    postTime: {
      from: 'body',
      type: 'datetime'
    },
    createTime: {
      from: 'body',
      type: 'datetime'
    },
    cnblogId: {
      from: 'body',
      type: 'integer'
    },
    cnodeId: {
      from: 'body',
      type: 'string',
      allowNull: true
    }
  }
};

exports.blogInfoSchema = {
  properties: {
    id: {
      from: 'params',
      type: 'integer',
      isrequire: true
    }
  }
}

exports.loginSchema = {
  properties: {
    user: {
      from: 'body',
      type: 'string',
      isrequire: true
    },
    password: {
      from: 'body',
      type: 'string',
      isrequire: true
    }
  }
};

exports.commentDelSchema = {
  properties: {
    id: {
      from: 'query', 
      type: 'string',
      isrequire: true
    }
  }
};

exports.commentUpdateSchema = {
  properties: {
    id: {
      from: 'params',
      type: 'integer',
      isrequire: true
    },
    status: {
      from: 'body',
      type: 'string',
      oneof: ['READ', 'UNREAD']
    }
  }
};

exports.commentListSchema = {
  properties: {
    blogId: {
      from: 'query', 
      type: 'string',
      isrequire: true
    }
  }
};

exports.commentPostSchema = {
  properties: {
    blogId: {
      from: 'body',
      type: 'string',
      isrequire: true
    },
    commentId: {
      from: 'body',
      type: 'string',
      allowNull: true
    },
    comment: {
      from: 'body',
      type: 'string',
      isrequire: true
    },
    user: {
      from: 'body',
      type: 'string',
      maxstring: 100,
      isrequire: true
    },
    email: {
      from: 'body',
      type: 'email',
      allowNull: true
    },
    postTime: {
      from: 'body',
      type: 'datetime'
    },
    headpicurl: {
      from: 'body',
      type: 'url',
      allowNull: true
    },
    fromCnblog: {
      from: 'body',
      type: 'bool'
    },
    fromCnode: {
      from: 'body',
      type: 'bool'
    },
    link: {
      from: 'body',
      type: 'url',
      allowNull: true
    }
  }
};
