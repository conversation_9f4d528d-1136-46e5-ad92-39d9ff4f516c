/**
 * blog service file
 */

// 17-06-02 切换到mysql

exports.blogList = function (where) {
  return db.Blog.findAll({
    order: [['postTime', 'DESC']],
    where: where,
    raw: true
  });
}

exports.blogPost = function (data) {
  return db.Blog.create(data)
    .then((result) => {
      return {id: result.id};
    });
}

exports.blogInfo = function(where) {
  return db.Blog.findOne({
    where: where,
    raw: true
  });
}

exports.blogUpdate = function (id, params) {
  return db.Blog.update(params, {
    where: {id: id}
  });
}

exports.blogDelete = function (id) {
  return db.Blog.destroy({where: {id: id}});
}
