/**
 * comment service file
 */

var config = require('config');
var nodemailer = require('nodemailer');
var renewal = require('./commentrenewal');

global.recentComments = [];
exports.updateRecentComments = function () {
  return db.Comment.findAll({
    attributes: ['blogId', 'comment', 'user', 'postTime'],
    limit: 5,
    order: [['postTime', 'DESC']],
    raw: true
  })
  .then((res) => {
    global.recentComments = res.rows;
  });
}

exports.commentList = function (blogId) {
  //return db.Comment.find({$where: "this.blogId == '" + blogId + "'"})
  return db.Comment.findAll({
    where: {blogId: blogId},
    order: [['postTime', 'ASC']],
    raw: true
  })
  .then((all) => {
    var tmpCommentMap = {};
    var res = [];
    for(var i = 0; i < all.length; ++i) {
      all[i].head = all[i].headpicurl ? all[i].headpicurl : config.defaultHeadpic;
      if (!all[i].reply) {
        all[i].reply = [];
        res.push(all[i]);
        tmpCommentMap[all[i].id] = res.length - 1;
      } else {
        var idx = tmpCommentMap[all[i].reply];
        res[idx].reply.push(all[i]);
        tmpCommentMap[all[i].id] = idx;
      }
    }
    return res;
  });
}

exports.commentPost = function (data) {
  return db.Comment.create(data)
    .then((result) => {
      return {id: result.id};
    });
}

exports.commentInfo = function (id) {
  return db.Comment.find({
    where: {id: id},
    raw: true  
  });
}

exports.commentDelete = function (id) {
  renewal.stopCommentsRenewal();
  renewal.initRenewal()
    .then(() => renewal.startCommentsRenewal());
  return db.Comment.destroy({
    where: {
      $or: [{id: id}, {reply: id}]
    }
  });
}

exports.commentUpdate = function (id, params) {
  return db.Comment.update(params, {
    where: {id: id}
  });
}

exports.commentReply = function (comment, reply) {
  reply.reply = comment.id;
  reply.fromCnblog = comment.fromCnblog;
  reply.fromCnode = comment.fromCnode;
  reply.blogId = comment.blogId;
  reply.createTime = new Date();
  sendEmailAsync(comment, reply);
  return db.Comment.create(reply)
    .then((result) => {
      return {id: result.id};
    });
}

function sendEmailAsync(comment, reply) {
  var transporter = nodemailer.createTransport({
    host: config.mail.host,
    port: config.mail.port,
    auth: {
      user: config.mail.auth.user,
      pass: config.mail.auth.pass
    }
  });
  return db.Blog.findOne({
    attributes: ['title', 'url'],
    where: {id: comment.blogId},
    raw: true
  }).then((blogInfo) => {
      debugger;
      return db.Comment.findAll({
        where: {$or: [{id: comment.id}, {reply: comment.id}]},
        raw: true
      })
      .then((res) => {
        var mails = [];
        let data = '<p style="font-weight: bold">您在博客<a href="' + config.host + '/blog/' + blogInfo.url +  '#comment">《' + blogInfo.title + '》</a>中的评论收到了新的回复</p><hr />';
        data += '<p style="font-weight: bold">' + comment.user + '的回复: </p>';
        data += '<div style="margin: 20px">' + comment.comment + '</div>';
        data += '<hr /><div style="float: right">（声明：请勿回复至此邮箱）</div>';
        for(var i = 0; i < res.length; ++i) {
          if (res[i].email) {
            mails.push({
              from: config.mail.from,
              to: res[i].email,
              subject: '评论回复-卢泽树的个人网站',
              //text: comment.user + '的回复：\n' + comment.comment + '\n（声明：请勿回复至此邮箱）'
              //html: comment.user + '的回复：\n' + comment.comment + '\n（声明：请勿回复至此邮箱）'
              html: data
            });
          }
        }

        return Promise.resolve(mails)
          .each((mail) => {
            return sendOneMail(transporter, mail);
          });
        });
    })
    .catch((err) => {
      logger.error(err);
      logger.error('邮件投递失败！');
    });
}

function sendOneMail(transporter, mail) {
  return new Promise((resolve, reject) => {
    transporter.sendMail(mail, (error, info) => {
      if (error) {
        logger.error(error);
        logger.error('投递失败！');
        return resolve();
      } 
      logger.info('Message %s sent: %s', info.messageId, info.response);
      // 两种情况都应当视为成功，不应该阻断后续人的发送
      return resolve();
    });
  });
}

exports.unreadMsg = function () {
  return db.Comment.findAndCount({
    where: {status: 'UNREAD'},
    raw: true
  });
}
