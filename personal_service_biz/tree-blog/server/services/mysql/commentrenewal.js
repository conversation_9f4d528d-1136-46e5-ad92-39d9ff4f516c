'use strict';

var _ = require('lodash');
var request = require('request');
var parseString = require('xml2js').parseString;
var config = require('config');


global.recentComments = [];
// 在setInterval周期里面的函数，关闭logging输出。
function updateRecentComments() {
  return db.Comment.findAll({
    attributes: ['blogId', 'comment', 'user', 'postTime', 'cnodeCommentId', 'cnblogCommentId'],
    limit: config.app.lastComment,
    order: [['postTime', 'DESC']],
    raw: true,
    logging: false
  })
  .then((res) => {
    global.recentComments = res;
    let blogIds = _.map(res, 'blogId');

    return db.Blog.findAll({
      attributes: ['id', 'title', 'url'],
      where: {id: {$in: blogIds}},
      raw: true,
      logging: false
    });
  })
  .then((res) => {
    res = _.keyBy(res, 'id');
    recentComments.forEach((comm) => {
      comm.blogTitle = res[comm.blogId].title;
      comm.blogUrl = res[comm.blogId].url;
    });
  });
}


// 初始执行一次，获取所有inCnodeCommentIds、inCnblogCommentIds保存到全局变量。
global.inCnodeCommentIds = [];
global.inCnodeCommentMaps = {};
global.inCnblogCommentIds = [];
global.inCnblogCommentMaps = {};
global.cnodeBlogIds = [];
global.cnodeBlogMap = {};
global.cnblogBlogIds = [];
global.cnblogBlogMap = {};

function initRenewal() {
  return db.Comment.findAll({
    attributes: ['id', 'cnodeCommentId', 'cnblogCommentId', 'reply'],
    where: {
      $or: [{cnodeCommentId: {$ne: null}}, {cnblogCommentId: {$ne: null}}]
    },
    raw: true
  }) 
  .then((res) => {
    inCnodeCommentIds = [];
    inCnodeCommentMaps = {};
    inCnblogCommentIds = [];
    inCnblogCommentMaps = {};

    for(let i = 0; i < res.length; ++i) {
      if (res[i].cnodeCommentId) {
        let id = res[i].cnodeCommentId;
        inCnodeCommentIds.push(id);
        inCnodeCommentMaps[id] = res[i];
      }
      if (res[i].cnblogCommentId) {
        let id = res[i].cnblogCommentId;
        inCnblogCommentIds.push(id);
        inCnblogCommentMaps[id] = res[i];
      }
    }
  })
  .then(() => {
    return db.Blog.findAll({
      attributes: ['id', 'cnodeId', 'cnblogId'],
      where: {
        $or: [{cnodeId: {$ne: null}}, {cnblogId: {$ne: null}}]
      },
      raw: true
    })
    .then((res) => {
      cnodeBlogIds = [];
      cnodeBlogMap = {};
      cnblogBlogIds = [];
      cnblogBlogMap = {};

      for(let i = 0; i < res.length; ++i) {
        if (res[i].cnodeId) {
          cnodeBlogIds.push(res[i].cnodeId);
          cnodeBlogMap[res[i].cnodeId] = {id: res[i].id, title: res[i].title};
        }
        if (res[i].cnblogId) {
          cnblogBlogIds.push(res[i].cnblogId);
          cnblogBlogMap[res[i].cnblogId] = {id: res[i].id, title: res[i].title};
        }
      }
    });
  })
  .catch((err) => {
    logger.error('initRenewal 出错');
    logger.error(err);
  });
}

// 应用启动、更新或添加博客有其他网站同步id时，调用该函数
// 固定周期更新其他网站的博客评论
var timer;
function stopCommentsRenewal() {
  if (timer) {
    clearInterval(timer);
    timer = undefined;
  }
}
function startCommentsRenewal() {
  stopCommentsRenewal();

  Promise.resolve()
    .then(renewalCnode)
    .then(renewalCnblog)
    .then(updateRecentComments)
    .catch((err) => {
      logger.error('同步评论定时错误');
      logger.error(err);
    });

  timer = setInterval(function () {
    return Promise.resolve()
      .then(renewalCnode)
      .then(renewalCnblog)
      .then(updateRecentComments)
      .catch((err) => {
        logger.error('同步评论定时错误');
        logger.error(err);
      });
  }, 5000);
}

function renewalCnode() {
  return Promise.map(cnodeBlogIds, (bid) => {
    let cnodeUrl = 'https://cnodejs.org/api/v1/topic/' + bid;
    return requestPromise(cnodeUrl)
      .then((result) => {
        let body;
        try {
          body = JSON.parse(result.body);
        } catch(err) {
          //logger.error('statusCode: ', result.statusCode);
          //logger.error('解析cnode社区评论出错', cnodeUrl);
          //logger.error('内容如下：\n', result.body);
          //throw err;

          throw {custom: true};
        }
        let comments = body.data.replies;
        let commentsMap = _.keyBy(comments, 'id');
        let newCommentIds = _.difference(_.map(comments, 'id'), inCnodeCommentIds);
        let newCommentsFirst = [];
        let newFirstIds = [];
        let newCommentsSecondMap = {};
        let newSecondIds = [];
        newCommentIds.forEach((id) => {
          let obj = {
            cnodeCommentId: id,
            blogId: cnodeBlogMap[bid].id,
            fromCnode: true,
            comment: commentsMap[id].content,
            user: commentsMap[id].author.loginname,
            link: 'https://cnodejs.org/user/' + commentsMap[id].author.loginname,
            postTime: commentsMap[id].create_at,
            headpicurl: commentsMap[id].author.avatar_url
          };
          if (commentsMap[id].reply_id == null) {
            obj.reply = null;
            newCommentsFirst.push(obj);
            newFirstIds.push(id);
          } else {
            newCommentsSecondMap[id] = obj;
            newSecondIds.push(id);
          }
        });

        // 把没有reply的comment先添加进数据库
        return addComments(newCommentsFirst)
          .then(() => {
            let newCommentsSecond = [];
            // 有reply的comment要找到在本库中的comment id
            newSecondIds.forEach((id) => {
              // 循环找回复的comment id，一直到该comment不是新添加的，意味着该tmpid的comment存在数据库中
              let tmpid = id;
              while(newCommentsSecondMap[tmpid]) {
                tmpid = commentsMap[tmpid].reply_id;
              }
              let replyComment = inCnodeCommentMaps[tmpid];
              // 如果数据库里面的comment本身是reply其他comment的，
              // 那么reply既是最顶层comment的本数据库的id
              // 否则，使用该comment在本数据库的id
              if (replyComment.reply)
                newCommentsSecondMap[id].reply = replyComment.reply;
              else
                newCommentsSecondMap[id].reply = inCnodeCommentMaps[tmpid].id;
              newCommentsSecond.push(newCommentsSecondMap[id]);
            });
            return addComments(newCommentsSecond);
          });
      })
      .catch((err) => {
        if (!err.custom) {
          logger.error(err);
          logger.info('cnode topic 获取失败：%s : %s', bid, cnodeBlogMap[bid].title);
        }
      });
  }, 3);

  function addComments(newComments) {
    return db.Comment.bulkCreate(newComments)
      .then((result) => {
        inCnodeCommentIds = inCnodeCommentIds.concat(_.map(newComments, 'cnodeCommentId'));
        if (newComments.length) {
          logger.info('同步新评论：', newComments);
        }
        result.forEach((comment) => {
          inCnodeCommentMaps[comment.cnodeCommentId] = {
            id: comment.id,
            cnodeCommentId: comment.cnodeCommentId,
            cnblogCommentId: comment.cnblogCommentId,
            reply: comment.reply
          };
        });
      });
  }
}


function renewalCnblog() {
  return Promise.map(cnblogBlogIds, (bid) => {
    let cnblogUrl = 'http://wcf.open.cnblogs.com/blog/post/' + bid + '/comments/1/99999';
    return requestPromise(cnblogUrl)
      .then((result) => {
        return new Promise((resolve, reject) => {
          parseString(result.body, (err, res) => {
            if (err) {
              //logger.error('解析评论出错：', cnblogUrl);
              //return reject(err);
              return reject({custom: true});
            }
            return resolve(res);
          });
        });
      })
      .then((body) => {
        let comments = body.feed.entry;
        let commentsMap = _.keyBy(comments, 'id');
        let commentIds = [].concat.apply([], _.map(comments, 'id'));
        let newCommentIds = _.difference(commentIds, inCnblogCommentIds);
        let newCommentsFirst = [];
        let newFirstIds = [];
        let newCommentsSecondMap = {};
        let newSecondIds = [];

        return Promise.resolve()
          .then(() => {
            // 处理commentsMap里的content前面的<a>标签，把回复的id转成reply_id
            for(var id in commentsMap) {
              let match = /^\<a href="#(\d+)".*\<\/a\>/.exec(commentsMap[id].content[0]._)
              if (match) {
                commentsMap[id].content[0]._.substring(match[0].length);
                commentsMap[id].reply_id = match[1];
              } else {
                commentsMap[id].reply_id = null;
              }
            }
          })
          .then(() => {
            let userToIdMap = {};

            newCommentIds.forEach((id) => {
              let obj = {
                cnblogCommentId: id,
                blogId: cnblogBlogMap[bid].id,
                fromCnblog: true,
                comment: commentsMap[id].content[0]._,
                user: commentsMap[id].author[0].name[0],
                link: commentsMap[id].author[0].uri[0],
                postTime: commentsMap[id].published[0]
              };
              if (commentsMap[id].reply_id == null) {
                obj.reply = null;
                newCommentsFirst.push(obj);
                newFirstIds.push(id);
              } else {
                newCommentsSecondMap[id] = obj;
                newSecondIds.push(id);
              }

              let user = obj.user;
              if (!userToIdMap[user]) {
                userToIdMap[user] = [];
              }
              userToIdMap[user].push(id);
            });

            // 处理需要添加评论id里的用户avatar
            return Promise.map(Object.keys(userToIdMap), (user) => {
              let searchUrl = 'http://wcf.open.cnblogs.com/blog/bloggers/search?t=' + encodeURIComponent(user);
              return requestPromise(searchUrl)
                .then((searchRes) => {
                  return new Promise((resolve, reject) => {
                    parseString(searchRes.body, (err, res) => {
                      if (err) {
                        logger.error('解析用户avatar出错:', searchUrl);
                        return reject(err);
                      }
                      if (!res.feed.entry) {
                        logger.warn('无法搜索到用户', user);
                        logger.warn(searchUrl);
                        return resolve('');
                      } else {
                        return resolve(res.feed.entry[0].avatar[0]);
                      }
                    });
                  });                 
                })
                .then((avatar) => {
                  // 把avatar加进commentsMap的字段
                  userToIdMap[user].forEach((id) => {
                    commentsMap[id].avatar = avatar;
                  });
                });
            })
            .then(() => {
              // 把最终commentsMap的avatar信息添加到newCommentsSecondMap和newCommentsFirst
              for(var i = 0; i < newCommentsFirst.length; ++i) {
                let id = newCommentsFirst[i].cnblogCommentId;
                newCommentsFirst[i].headpicurl = commentsMap[id].avatar;
              }

              for(var id in newCommentsSecondMap) {
                newCommentsSecondMap[id].headpicurl = commentsMap[id].avatar;
              }
            });
          })
          .then(() => {
            // 把没有reply的comment先添加进数据库
            return addComments(newCommentsFirst)
              .then(() => {
                let newCommentsSecond = [];
                // 有reply的comment要找到在本库中的comment id
                newSecondIds.forEach((id) => {
                  // 循环找回复的comment id，一直到该comment不是新添加的，意味着该tmpid的comment存在数据库中
                  let tmpid = id;
                  while(newCommentsSecondMap[tmpid]) {
                    tmpid = commentsMap[tmpid].reply_id;
                  }
                  let replyComment = inCnblogCommentMaps[tmpid];
                  // 如果数据库里面的comment本身是reply其他comment的，
                  // 那么reply既是最顶层comment的本数据库的id
                  // 否则，使用该comment在本数据库的id
                  if (replyComment.reply)
                    newCommentsSecondMap[id].reply = replyComment.reply;
                  else
                    newCommentsSecondMap[id].reply = inCnblogCommentMaps[tmpid].id;
                  newCommentsSecond.push(newCommentsSecondMap[id]);
                });
                return addComments(newCommentsSecond);
              });
          });
      })
      .catch((err) => {
        if (!err.custom) {
          logger.error(err);
          logger.info('cnblog post 获取失败：%s : %s', bid, cnblogBlogMap[bid].title);
        }
      });
  });

  function addComments(newComments) {
    return db.Comment.bulkCreate(newComments)
      .then((result) => {
        inCnblogCommentIds = inCnblogCommentIds.concat(_.map(newComments, 'cnblogCommentId'));
        if (newComments.length) {
          logger.info('同步新评论：', newComments);
        }
        result.forEach((comment) => {
          inCnblogCommentMaps[comment.cnblogCommentId] = {
            id: comment.id,
            cnodeCommentId: comment.cnodeCommentId,
            cnblogCommentId: comment.cnblogCommentId,
            reply: comment.reply
          };
        });
      });
  }
}

function requestPromise(url) {
  return new Promise(function (resolve, reject) {
    request(url, function (err, res) {
      if (err) {
        return reject(err);
      }
      return resolve(res);
    });
  });
}

exports.startCommentsRenewal = startCommentsRenewal;
exports.stopCommentsRenewal = stopCommentsRenewal;
exports.updateRecentComments = updateRecentComments;
exports.initRenewal = initRenewal;
