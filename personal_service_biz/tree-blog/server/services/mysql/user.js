

// 一个小时内3次登录失败自动锁住
var failLogin = 0;
setTimeout(function () {
  failLogin = 0;
}, 3600000);

exports.login = function (loginInfo) {
  if (failLogin >= 3) {
    return {
      code: 500002,
      msg: '已被锁住'
    };
  }
  return db.User.findOne({$where: "this.user == '" + loginInfo.user + "'"})
    .then((userInfo) => {
      if (userInfo && userInfo.password === loginInfo.password) {
        return {
          code: 0, 
          msg: '登录成功'
        };
      }
      ++ failLogin;
      return {
        code: 50001,
        msg: '用户名或密码错误'
      };
    });
};
