body {
  background: #f3f3f3;
  color: #484848;
  padding: 0;
  font-family: playfair display,sans-serif;
  font-size: 14px;
  line-height: 24px;
  letter-spacing: .8px;
}

* {
  word-break: break-all; 
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: 700;
}

html, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, cite, code, del, dfn, em, font, img, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, figure {
  margin: 0;
  padding: 0;
}

.clr {
  clear: both;
}

.white {
  background: white;
}

.ct-wrapper {
  /*max-width: 1700px;*/
  padding: 0px 15px;
  margin: 0 auto;
}

.outer-wrapper {
  margin: 5px 0 45px;
  box-shadow: 0 0 40px -10px #000;
  position: relative;
}

.main-wrapper {
  padding-top: 45px;
}
.sidebar-wrapper {
  padding-top: 20px;
}

.main-wrapper {
  float: left;
  /*width: 80%;*/
  width: -webkit-calc(100% - 280px);
  width:    -moz-calc(100% - 280px);
  width:         calc(100% - 280px);
}

div#content {
  box-shadow: 20px 0 40px -40px #000;
}

.article {

}

.col {
  position: relative;
  min-height: 1px;
  padding-right: 15px;
  padding-left: 15px;
}

.blog-wrapper {
  margin: 0;
  margin-bottom: 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid #E4E4E4;
}

.date-box {
  width: 15%;
  font-family: Playfair Display;
  font-style: italic;
  color: #999;
  text-align: center;
  font-size: 60px;
  line-height: 24px;
  padding-top: 10px;
}

.left {
  float: left;
}

.right {
  float: right;
}

.date-box span {
  font-size: 18px;
  display: block;
  margin-bottom: 10px;
}

.date-box span:nth-child(2) {
  margin: 0;
  margin-top: 30px;
}

.date-box em {
  font-size: 30px;
}

.date-bar {
  display: none;
}

.blog-box {
  width: 85%;

}

.blog-image {
  width: 35%;
  float: left;
}

.img-responsive {
  display: block;
  max-width: 100%;
  height: auto;
}

.blog-container {
  margin-left: 38%;
}

.blog-container h2 {
  padding: 0;
  font-family: Playfair Display;
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin: 0 0 7px;
  text-transform: none;
  line-height: 1.7;
  position: relative; 
}

.blog-container p {
  font-weight: 400;
  font-size: 12px;
  letter-spacing: .5px;
  color: #555;
  font-family: playfair display;
}

.blog-container-meta {
  margin-bottom: 13px;
}

.blog-container span {
  margin-left: 10px;
  margin-right: 10px;
}

.sidebar-box {
  display: none;
  background: #fefefe;
}

.sidebar-box .image-wrapper {
  width: 50%;
}
  
.sidebar-wrapper {
  float: right;
  width: 280px;
  background: #fefefe;
}

.sidebar {
  padding: 0;
  margin: 0;
}

.widget {
  padding: 5px 20px;
  font-size: 13px;
  line-height: 23px;
  margin-bottom: 30px;
}

.menubar {
  font-size: 18px;
  font-color: #ff0000;
  margin: 0 auto;
  margin-bottom: 20px;
  width: 70px;
}

.widget-content {
  margin: 0;
  padding: 0;
}

.widget-title {
  font-size: 15px;
  font-weight: bold;
  margin: 2px;
}

.vt_sidebar_author_item {
  margin-bottom: 0px;
  text-align: center;
}

.image-wrapper {
  overflow: hidden;
  border-radius: 50%;
  width: 85%;
  margin: 0 auto;
  margin-bottom: 15px;
}

.head-image {
  width: 100%;
}

.vt_sidebar_author_item h4.author_name a {
  font-size: 18px;
  margin-bottom: 10px;
  display: block;
  color: #333;
}
.author_intro {
  margin-top: 30px;
}
.vt_sidebar_author_item .author_intro p {
  margin: 0;
}

.recent-wrapper {
  margin: 10px 5px 10px 5px;
  width: 100%;
  float: left;
}

.recent-user {
  float: right;
}

.recent-note {
  font-weight: bold;
}

.recent-time {
  float: right;
}

/*
 blog page css
*/
.blog-page-wrapper {
  padding: 0 20px;
  margin: 0;
  margin-bottom: 30px;
  border-bottom: 1px solid #E4E4E4;
}

.blog-title {
  font-size: 26px;
  line-height: normal;
  margin: 0;
  padding: 0 0 15px;
  text-decoration: none;
  text-transform: capitalize; 
}

.blog-meta-container {
  font-size: 12px;
  text-transform: capitalize;
  margin: 0;
  margin-bottom: 15px;
  padding-bottom: 10px;  
}

.blog-meta-container a {
  color: #000000;
}

.blog-meta-container span {
  margin-right: 15px;
}

.blog-meta-container span > i {
  margin-right: 4px;
}

.blog-meta-container {
  font-family: Verdana,Geneva,Arial,Helvetica,sans-serif; 
}
.blog-body * {
  font-family: Verdana,Geneva,Arial,Helvetica,sans-serif; 
}
.comment-content * {
  font-family: Verdana,Geneva,Arial,Helvetica,sans-serif; 
}
.blog-abstract {
  font-family: Verdana,Geneva,Arial,Helvetica,sans-serif; 
}

/*
.blog-body h1, h2, h3, h4, h5, h6, p {
  line-height: 1.7
}

.blog-body p {
  line-height: 2.0
}

.blog-declare {
  margin: 0;
  margin-top: 8px;
}
.blog-declare a {
  margin-right: 8px;
}
.blog-declare img {
  display: inline-block;
}
.blog-declare-line {
  margin: 5px 0 12px 0;
}
*/

.comments-list-wrapper {
  margin-top: 20px;
}

.comments-list {

}

.comments-header {
  line-height: 1.7;
  font-size: 18px;
  font-weight: 700;
  border-bottom: 1px solid #d3d3d3;
  padding-bottom: 8px;
  margin-bottom: 20px;
}

.comment-wrapper {
  margin: 15px 10px 15px 10px;
  border-bottom: 1px dashed #d3d3d3;
}

.comment-head {
  margin: 10px;
  margin-right: 0px;
  height: 45px;
  width: 45px;
  float: left;
}

.comment-box {
  float: left;
  width: 100% - 90px;
}

.comment-user {
  float: left;
  margin: 10px 0 10px 20px;
}

.comment-note {
  float: left;
  margin-top: 40px;
}

.comment-time, .comment-floor {
  float: right;
  margin: 10px;
}
.comment-time {
  margin-left: 0px;
}

.comment-content {
  float: left;
  margin: 10px;
}

.comment-content img {
  max-width: 100%;
}

.reply-box {
  margin: 10px;
}

.reply-text {
  width: 100%;
}

.comment-reply-list {
  margin-left: 20px;
}

.comment-reply {
  float: right;
  margin: 10px;
}

.comment-form {
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 10px 20px 20px 20px;
  margin: 0 10px 20px 10px;
}

.comment-input h4 {
  font-size: 20px;
  margin: 0 0 18px;
  text-transform: capitalize;
}

.comment-form label {
  margin: 0.4em;
}

.comment-text, .reply-box {
  min-height: 200px;
}

.comment-form textarea {
  width: 100%;
}
.comment-form input {
  width: 100%;
}
.comment-form button {
  float: right;
}
.comment-form-note {
  margin-top: 10px;
  float: left;
}


/*
 * adaptor
 */

@media (max-width: 1300px) {
  .ct-wrapper {
    padding: 0px 10px;
  }
  .main-wrapper {
    width: 100%;
  }
  .sidebar-wrapper {
    display: none;
  }
  .sidebar-box {
    display: block;
    margin: 0 auto;
    max-width: 320px;
  }
  .blog-page-wrapper {
    padding: 0;
  }
  .date-box {
    display: none
  }
  .date-bar {
    display: block;
    width: 100%;
    margin-bottom: 20px;
    font-size: 20px;
  }
  .blog-box {
    width: 100%;
  }
}

@media (max-width: 500px) {
  .blog-image {
    width: 100%;
    margin-bottom: 20px;
  }
  .img-responsive {
    margin: 0 auto;
  }
  .blog-container {
    float: left;
    margin-left: 0;
  }
  .comment-wrapper {
    margin-right: 0px;
    margin-left: 0px;
  }
}


pre {
  line-height: 1.7;
}

.info-must {
  color: #ff0000;
}

.unvisible {
  display: none;
}

.blog-body ol {
  margin: 0 0 10px 25px;
}










/* fix */
p {
  margin: 10px auto;
}
.blog-body {
  font-family: Verdana,Geneva,Arial,Helvetica,sans-serif;
  font-size 17px;
  word-wrap: break-word;
  line-height: 1.5;
}
hr {
    display: block;
    -webkit-margin-before: 0.5em;
    -webkit-margin-after: 0.5em;
    -webkit-margin-start: auto;
    -webkit-margin-end: auto;
    border-style: inset;
    border-width: 1px;
}
.blog-body h1 {
    font-size: 28px;
    font-weight: bold;
    line-height: 1.5;
    margin: 10px 0;
}
.blog-body h2 {
    font-size: 21px;
    font-weight: bold;
    line-height: 1.5;
    margin: 10px 0
}
.blog-body h3 {
    font-size: 16px;
    font-weight: bold;
    line-height: 1.5;
    margin: 10px 0
}
.blog-body h4 {
    font-size: 14px;
    font-weight: bold;
    margin: 10px 0
}
.blog-body h5 {
    font-size: 12px;
    font-weight: bold;
    margin: 10px 0
}
.blog-body h6 {
    font-size: 11px;
    font-weight: bold;
    margin: 10px 0
}
pre code {
  padding: 5px;
  display: block;
  font-family: "Courier New",sans-serif!important;
  font-size: 14px!important;
  line-height: 1.5!important;
}
.blog-body img {
  display: block;
  margin: 0 auto;
  max-width: 100%;
}
.blog-body blockquote {
  padding: 5px 0px 5px 15px;
  background-color: #fafafc;
}
.blog-body blockquote p {
  font-family: "Courier New",sans-serif!important;
  font-size: 14px!important;
  margin: 10px auto;
}
