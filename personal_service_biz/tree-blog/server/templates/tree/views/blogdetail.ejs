<div class="blog-page-wrapper">

<h1 class="blog-title"><%= blogInfo.title %></h1>
<div class="blog-meta-container">
  <span class="unvisible" id="blog-id"><%= blogInfo.id %></span>
  <span><i class="fa fa-user"></i><%= blogInfo.author %></span>
  <span><i class="fa fa-bookmark"></i><%= blogInfo.fulldatetext %></span>
  <a target="_self" href="<%= blogInfo.href %>#comment"><span><i class="fa fa-comments"></i><%= blogInfo.commentNum %></span></a>
</div>

<div class="blog-body">
  <!-- <hr class="blog-declare-line" /> -->
  <%- blogInfo.content %>
</div>

<div class="clr"></div>

<div class="comments-list-wrapper">
  <a name="comment"></a>
  <div class="comments-list">
    <div class="comments-header"><%= blogInfo.commentNum %> Comments: </div>
    <% if (blogInfo.commentNum) { %>
      <% for(var i = 0; i < blogInfo.comments.length; ++i) { %>
        <div class="comment-wrapper">
          <span class="unvisible comment-id-msg"><%= blogInfo.comments[i].id %></span>
          <a href=<%= blogInfo.comments[i].link %> ><img class="comment-head" src=<%= blogInfo.comments[i].head %> ></img></a>
          <div class="comment-user">
            <label><a href=<%= blogInfo.comments[i].link %> ><%= blogInfo.comments[i].user %></a> : </label>
            <% if(blogInfo.comments[i].cnodeCommentId) { %>
              <br />
              <label style="padding: 5px">(评论来自<a href="https://cnodejs.org/topic/<%= blogInfo.cnodeId %>" >cnode社区</a>)</label>
            <% } %>
            <% if(blogInfo.comments[i].cnblogCommentId) { %>
              <br />
              <label style="padding: 5px">(评论来自<a href="https://www.cnblogs.com/papertree/p/<%= blogInfo.cnblogId %>.html">博客园</a>)</label>
            <% } %>
          </div>
          <div class="comment-floor"><a href="javascript:void(0)">#<%= i+1 %>楼</a></div>
          <div class="comment-time"><%= blogInfo.comments[i].timetext %></div>
          <div class="clr"></div>
          <div class="comment-content">
            <p><%- blogInfo.comments[i].comment %></p>
          </div>
          <div class="clr"></div>
          <% if (blogInfo.comments[i].reply.length) { %>
            <div class="comment-reply-list">
              <% for(var j = 0; j < blogInfo.comments[i].reply.length; ++j) { %>
              <div class="comment-wrapper">
                <a href=<%= blogInfo.comments[i].reply[j].link %> ><img class="comment-head" src=<%= blogInfo.comments[i].reply[j].head %> ></img></a>
                <div class="comment-user">
                  <label><a href=<%= blogInfo.comments[i].reply[j].link %> ><%= blogInfo.comments[i].reply[j].user %></a> : </label>
                  <% if(blogInfo.comments[i].reply[j].cnodeCommentId) { %>
                    <br />
                    <label style="padding: 5px">(评论来自<a href="https://cnodejs.org/topic/<%= blogInfo.cnodeId %>" >cnode社区</a>)</label>
                  <% } %>
                  <% if(blogInfo.comments[i].reply[j].cnblogCommentId) { %>
                    <br />
                    <label style="padding: 5px">(评论来自<a href="https://www.cnblogs.com/papertree/p/<%= blogInfo.cnblogId %>.html">博客园</a>)</label>
                  <% } %>
                </div>
                <div class="comment-time"><%= blogInfo.comments[i].reply[j].timetext %></div>
                <div class="clr"></div>
                <div class="comment-content">
                  <p><%- blogInfo.comments[i].reply[j].comment %></p>
                </div>
                <div class="clr"></div>
              </div>
              <% } %>
            </div>
          <% } %>
          <button class="btn btn-default comment-reply reply-button">回复</button>
          <div class="clr"></div>
        </div>
      <% } %>
    <% } %>
  </div>

  <form method="POST" class="comment-form">
    <div class="comment-input form-group">
      <label><span class="info-must">*</span>发表评论：</label>
      <textarea type="text" class="form-control comment-text"></textarea>
    </div>
    <div class="form-group">
      <label><span class="info-must">*</span>您的姓名</label>
      <input type="text" class="form-control comment-username">
    </div>
    <div class="form-group">
      <label>头像链接(非必填)：</label>
      <input type="text" class="form-control comment-headpic">
    </div>
    <div class="form-group">
      <label>电子邮箱(非必填，仅博主可见，可用于接收评论回复)：</label>
      <input type="text" class="form-control comment-email">
    </div>
    <div class="form-group">
      <label>个人网址(非必填，作为您的跳转链接)：</label>
      <input type="text" class="form-control comment-link">
    </div>
    <button class="btn btn-default">提交</button>
    <div class="clr"></div>
    <div class="comment-form-note">
      声明：电子邮箱用于接收回复的通知邮件（来自&lt;<EMAIL>&gt;，请勿回复至此邮箱）。
      <br />
      163邮箱可正常接收，gmail邮箱注意查看垃圾邮件，qq邮箱由于ip频率限制可能无法成功投递。
    </div>
    <div class="clr"></div>
  </form>
</div>

</div>
