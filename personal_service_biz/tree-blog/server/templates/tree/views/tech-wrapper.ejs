<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <title><%= title %></title>
  <link href="/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <link href="/bootstrap-3.0.0/css/bootstrap.min.css" rel="stylesheet">
  <!-- <link href="//netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap.min.css" rel="stylesheet">  -->
  <link href="/css/font.css" rel="stylesheet">
  <link href="/highlight.js-9.8.0/styles/default.css" rel="stylesheet">
  <link href="/css/tree-tpl.css" rel="stylesheet">
</head>

<body>
  <div class="ct-wrapper">
    <div class="outer-wrapper white">
      <div class="sidebar-box">
         <div class="menubar"><a target="_self" href=<%- host -%> >/ 首页 /</a></div>
         <div class="vt_sidebar_author_item">
           <div class="image-wrapper">
             <a target="_self" href=<%- host -%>><img class="head-image" src="/pictures/hacker.jpg"></a>
           </div>
           <h4 class="author_name">
             <a target="_self" href=<%- host -%>><%= author.chName %></a>
           </h4>
           <% if( type=="blogHome") { %>
              <%- author.intro %>
           <% } %>
         </div>
      </div>

      <div class="main-wrapper">
        <div class="article col" id="content">
          <% if( type=="blogHome" ) { %>
            <%- include('tech.ejs') %>
          <% } else if( type=="blogPage" ) {%>
            <%- include('blogdetail.ejs') %>
          <% } else {%>
            <%- include('nofound.ejs') %>
          <% } %>
        </div>
      </div>

      <div class="sidebar-wrapper">
        <div class="sidebar col" id="sidebar">
          <div class="menubar"><a target="_self" href=<%- host -%> >/ 首页 /</a></div>

          <div class="widget">
            <div class="widget-content">
              <div class="vt_sidebar_author_item">
                <div class="image-wrapper">
                  <a target="_self" href=<%- host -%> ><img class="head-image" src="/pictures/hacker.jpg"></a>
                </div>
                <h4 class="author_name">
                  <a target="_self" href=<%- host -%>><%= author.chName %></a>
                </h4>
                  <!-- <%- author.intro %> -->
                  <div class="author_intro">
                    <p>有所求有所不求。人生就像无穷的棋盘，时胜时败，点到即止，却从不终止。没有终极的目标，只有角逐过程中胜败的乐趣。</p>
                  </div>
              </div>
            </div>
          </div>
          
          <div class="widget">
            <div class="widget-content">
              <hr />
              <p class="widget-title">最新评论</p>
              <% for(var i = 0; i < recentComments.length; ++i) { %>
                <div class="recent-wrapper">
                  <div class=recent-reply>
                    <a href=<%= recentComments[i].blogUrl %> ><%= recentComments[i].blogTitle %></a>
                  </div>
                  <%- recentComments[i].comment %>
                  <div class="clr"></div>
                  <div class="recent-user">
                    <%= recentComments[i].user %>
                    <% if (recentComments[i].cnblogCommentId) { %>
                      <span class="recent-note">(博客园)</span>
                    <% } %>
                    <% if (recentComments[i].cnodeCommentId) { %>
                      <span class="recent-note">(cnode社区)</span>
                    <% } %>
                  </div>
                  <div class="clr"></div>
                  <div class="recent-time">
                    <%= recentComments[i].postTime %>
                  </div>
                </div>
                <div class="clr"></div>
              <% } %>
            </div>
          </div>
        </div>
      </div>
      <div class="clr"></div>
      <div style="text-align:center;padding-bottom:20px"><a href=<%- recordationUrl -%> ><%- recordation -%></a></div>
    </div>
  </div>
</body>

<% if(type == "blogPage") {%>
  <script src="/jquery/jquery-3.1.1.min.js"></script> 
  <script src="/js/blog.js"></script>
<% } %>
</html>
