<!DOCTYPE html>
<html>
<head>
  <base target="_blank">
  <meta charset="utf-8">
  <meta content="width=device-width, initial-scale=1" name="viewport">
  <title>卢泽树的个人网站</title>
  <link href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
  <!-- <link href="bootstrap-3.0.0/css/bootstrap.min.css" rel="stylesheet"> -->
  <link href="//netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap.min.css" rel="stylesheet"> 
  <link href="/css/font.css" rel="stylesheet">
  <link href="/highlight.js-9.8.0/styles/default.css" rel="stylesheet">
  <link href="/css/tree-tpl.css" rel="stylesheet">
</head>

<body>
  <div class="ct-wrapper">
    <div class="outer-wrapper white">
      <div class="sidebar-box">
         <div class="vt_sidebar_author_item">
           <div class="image-wrapper">
             <a target="_self" href="https://luzeshu.com:8899"><img class="head-image" src="/pictures/hacker.jpg"></a>
           </div>
           <h4 class="author_name">
             <a target="_self" href="https://luzeshu.com:8899">大树居士</a>
           </h4>
           
         </div>
      </div>

      <div class="main-wrapper">
        <div class="article col" id="content">
          
            <div class="blog-page-wrapper">

<h1 class="blog-title">node源码详解（二 ）—— 运行机制 、整体流程</h1>
<div class="blog-meta-container">
  <span class="unvisible" id="blog-id">5822b4883a48ad366440e4f1</span>
  <span><i class="fa fa-user"></i>Luzeshu</span>
  <span><i class="fa fa-bookmark"></i>星期六, 2016年3月12日</span>
  <a target="_self" href="/blog/nodesource2#comment"><span><i class="fa fa-comments"></i>23</span></a>
</div>

<div class="blog-body">
  <!-- <hr class="blog-declare-line" /> -->
  <p>本作品采用<a href="http://creativecommons.net.cn/licenses/licenses_exp/">知识共享署名 4.0 国际许可协议</a>进行许可。转载联系作者并保留声明头部与原文链接<a href="https://luzeshu.com/blog/nodesource2">https://luzeshu.com/blog/nodesource2</a><br>
本博客同步在<a href="https://cnodejs.org/topic/56e3be21f5d830306e2f0fd3">https://cnodejs.org/topic/56e3be21f5d830306e2f0fd3</a><br>
本博客同步在<a href="http://www.cnblogs.com/papertree/p/5225201.html">http://www.cnblogs.com/papertree/p/5225201.html</a></p>
<hr>
<h1>2.1 项目代码结构</h1>
<p>node 主要的部分有4个【下图最左列就是node项目源码（v4.2.2）的根目录】：</p>
<ol>
<li>原生 js模块：node提供给 用户js 代码的类接口，平时用的require('fs')、require('http')调用的都是这部分的代码。【最左列的 lib文件夹，展开后是左二列】</li>
<li>node 源码：node程序的main函数入口；还有提供给lib模块的C++类接口。【最左列的 src 文件夹，展开后是第三列】</li>
<li>v8引擎：node用来解析、执行js代码的运行环境。【最左列的deps文件夹展开后是第四列，v8和libuv等依赖都放在这里】</li>
<li>libuv：事件循环库，提供最底层的 io操作接口（包括网络io操作的epoll_wait()、文件异步io的线程池管理）、事件循环逻辑。 【第四列的uv文件夹，展开后是第五列】</li>
</ol>
<p>记住这几个路径：</p>
<p>./lib
./src
./deps/uv</p>
<p><img src="//dn-cnode.qbox.me/FuFHJcpk82pfYMNaeVv5tgxv0lEW" alt="2-1-1.png"></p>
<center>图2-1-1</center>
<br />
<hr>
<h1>2.2 运行流程</h1>
<p>下图4个红色序号分别对应着上篇博客提出的4个问题所在位置。后续博客分说。</p>
<p>接下来对一些关键地方进行说明：</p>
<h2>1. 核心数据结构 default_loop_struct （结构体为struct uv_loop_s，后续祥讲）</h2>
<p>这个数据结构是事件循环的核心。当node执行到“加载js文件”这个步骤（结合下图）时，用户的js代码如果有io操作：
那么js代码通过调用 -》lib模块（2.1 中的原生js模块）-》C++模块（2.1中的源码部分） -》 libuv接口（2.1中deps/uv部分） -》最终的系统api，拿到系统返回的一个fd（文件描述符），和 js代码传进来的回调函数callback，封装成一个io观察者（一个uv__io_s类型的对象），保存到default_loop_struct；</p>
<br />
<h2>2. 进入事件循环</h2>
<p>当处理完 js代码，如果有io操作，那么这时default_loop_struct是保存着对应的io观察者的。</p>
<p>处理完js代码，main函数继续往下调用libuv的事件循环入口uv_run()，node进程进入事件循环：</p>
<p>uv_run()的while循环做的就是一件事，判断default_loop_struct是否有存活的io观察者。<br>
a. 如果没有io观察者，那么uv_run()退出，node进程退出。<br>
b. 而如果有io观察者，那么uv_run()进入epoll_wait()，线程挂起等待，监听对应的io观察者是否有数据到来。有数据到来调用io观察者里保存着的callback（js代码），没有数据到来时一直在epoll_wait()进行等待。</p>
<p>这里解答了博客（一）的“问题2”的一部分：为什么console.log()的js代码导致node退出，而server.listen(80)导致线程挂起等待。</p>
<br />
<h2>3. 这里一旦没搞清逻辑就有个疑问：</h2>
<p>第2点说在uv_run()里面如果监听的io观察者有数据到来，那么调用对应的callback，执行js代码。如果没有数据到来，一直在epoll_wait()等待。那如果我js代码里面有新的 io操作想要交给epoll_wait()进行监听，而此刻监听着的io观察者又没有数据到来，线程一直在这里等待，那怎么办？</p>
<p>首先第1点讲到，执行js代码的时候，通过调用node提供的C++接口最终把io观察者都保存到default_loop_struct里面，js代码执行完之后，node继续运行才进入epoll_wait()等待。也就是说node在epoll_wait()的时候，js代码执行完毕了。而js代码的回调函数部分，本来的设定就是在epoll_wait()监听的io观察者被触发之后才会执行回调，在epoll_wait()进行等待的时候，不可能存在“有新io操作要交给epoll_wait()去监听”这样的js代码。</p>
<p><img src="//dn-cnode.qbox.me/Fkd1LWFQaE9_4Qr727JhfiG6kdKF" alt="2-2-1.png"></p>
<center>图2-2-1</center>

</div>

<div class="clr"></div>

<div class="comments-list-wrapper">
  <a name="comment"></a>
  <div class="comments-list">
    <div class="comments-header">23 Comments: </div>
    
      
        <div class="comment-wrapper">
          <span class="unvisible comment-id-msg">5826923571308f7983ae92c2</span>
          <a href=https://cnodejs.org/user/yongningfu ><img class="comment-head" src=https://avatars.githubusercontent.com/u/9846613?v=3&amp;s=120 ></img></a>
          <div class="comment-user"><label><a href=https://cnodejs.org/user/yongningfu >yongningfu</a> : </label></div>
          <div class="comment-floor"><a href="javascript:void(0)">#1楼</a></div>
          <div class="comment-time">2016-03-13 10:57:58</div>
          <div class="clr"></div>
          <div class="comment-content">
            <p><div class="markdown-text"><p>赞</p>
</div></p>
          </div>
          <div class="clr"></div>
          
            <div class="comment-reply-list">
              
              <div class="comment-wrapper">
                <a href=https://cnodejs.org/user/bigtree9307 ><img class="comment-head" src=https://luzeshu.com/pictures/hacker.jpg ></img></a>
                <div class="comment-user"><label><a href=https://cnodejs.org/user/bigtree9307 >bigtree9307</a> : </label></div>
                <div class="comment-time">2016-03-13 21:12:14</div>
                <div class="clr"></div>
                <div class="comment-content">
                  <p><div class="markdown-text"><p><a href="/user/yongningfu">@yongningfu</a> 第一条评论T T</p>
</div></p>
                </div>
                <div class="clr"></div>
              </div>
              
              <div class="comment-wrapper">
                <a href=https://cnodejs.org/user/yongningfu ><img class="comment-head" src=https://avatars.githubusercontent.com/u/9846613?v=3&amp;s=120 ></img></a>
                <div class="comment-user"><label><a href=https://cnodejs.org/user/yongningfu >yongningfu</a> : </label></div>
                <div class="comment-time">2016-03-14 00:01:24</div>
                <div class="clr"></div>
                <div class="comment-content">
                  <p><div class="markdown-text"><p><a href="/user/bigtree9307">@bigtree9307</a> 特别赞那么将自己的见解分享的人 这也是我喜欢node社区的一个原因，现在在读nodeclub码源，过段时间把你的node源码分享来仔细的看一下</p>
</div></p>
                </div>
                <div class="clr"></div>
              </div>
              
            </div>
          
          <button class="btn btn-default comment-reply reply-button">回复</button>
          <div class="clr"></div>
        </div>
      
        <div class="comment-wrapper">
          <span class="unvisible comment-id-msg">5826923571308f7983ae92ce</span>
          <a href=https://cnodejs.org/user/bigtree9307 ><img class="comment-head" src=https://luzeshu.com/pictures/hacker.jpg ></img></a>
          <div class="comment-user"><label><a href=https://cnodejs.org/user/bigtree9307 >bigtree9307</a> : </label></div>
          <div class="comment-floor"><a href="javascript:void(0)">#2楼</a></div>
          <div class="comment-time">2016-03-20 10:14:16</div>
          <div class="clr"></div>
          <div class="comment-content">
            <p><div class="markdown-text"><p>编辑了markdown版本方便这边看~</p>
</div></p>
          </div>
          <div class="clr"></div>
          
          <button class="btn btn-default comment-reply reply-button">回复</button>
          <div class="clr"></div>
        </div>
      
        <div class="comment-wrapper">
          <span class="unvisible comment-id-msg">5826923571308f7983ae92cd</span>
          <a href=https://cnodejs.org/user/leiwei1991 ><img class="comment-head" src=https://avatars.githubusercontent.com/u/10721874?v=3&amp;s=120 ></img></a>
          <div class="comment-user"><label><a href=https://cnodejs.org/user/leiwei1991 >leiwei1991</a> : </label></div>
          <div class="comment-floor"><a href="javascript:void(0)">#3楼</a></div>
          <div class="comment-time">2016-03-29 16:06:56</div>
          <div class="clr"></div>
          <div class="comment-content">
            <p><div class="markdown-text"><p>看了好几遍了，今天有点豁然开朗的感觉</p>
</div></p>
          </div>
          <div class="clr"></div>
          
            <div class="comment-reply-list">
              
              <div class="comment-wrapper">
                <a href=https://cnodejs.org/user/bigtree9307 ><img class="comment-head" src=https://luzeshu.com/pictures/hacker.jpg ></img></a>
                <div class="comment-user"><label><a href=https://cnodejs.org/user/bigtree9307 >bigtree9307</a> : </label></div>
                <div class="comment-time">2016-06-21 10:24:01</div>
                <div class="clr"></div>
                <div class="comment-content">
                  <p><div class="markdown-text"><p><a href="/user/leiwei1991">@leiwei1991</a> ^_^</p>
</div></p>
                </div>
                <div class="clr"></div>
              </div>
              
            </div>
          
          <button class="btn btn-default comment-reply reply-button">回复</button>
          <div class="clr"></div>
        </div>
      
        <div class="comment-wrapper">
          <span class="unvisible comment-id-msg">5826923571308f7983ae92cc</span>
          <a href=https://cnodejs.org/user/huihongzhou ><img class="comment-head" src=https://avatars.githubusercontent.com/u/8851370?v=3&amp;s=120 ></img></a>
          <div class="comment-user"><label><a href=https://cnodejs.org/user/huihongzhou >huihongzhou</a> : </label></div>
          <div class="comment-floor"><a href="javascript:void(0)">#4楼</a></div>
          <div class="comment-time">2016-05-24 17:31:12</div>
          <div class="clr"></div>
          <div class="comment-content">
            <p><div class="markdown-text"><p>大神，收徒么？呜哇~呜哇~</p>
</div></p>
          </div>
          <div class="clr"></div>
          
            <div class="comment-reply-list">
              
              <div class="comment-wrapper">
                <a href=https://cnodejs.org/user/bigtree9307 ><img class="comment-head" src=https://luzeshu.com/pictures/hacker.jpg ></img></a>
                <div class="comment-user"><label><a href=https://cnodejs.org/user/bigtree9307 >bigtree9307</a> : </label></div>
                <div class="comment-time">2016-06-21 10:24:13</div>
                <div class="clr"></div>
                <div class="comment-content">
                  <p><div class="markdown-text"><p><a href="/user/huihongzhou">@huihongzhou</a> 不敢当哈</p>
</div></p>
                </div>
                <div class="clr"></div>
              </div>
              
            </div>
          
          <button class="btn btn-default comment-reply reply-button">回复</button>
          <div class="clr"></div>
        </div>
      
        <div class="comment-wrapper">
          <span class="unvisible comment-id-msg">5826923571308f7983ae92cb</span>
          <a href=https://cnodejs.org/user/tsyeyuanfeng ><img class="comment-head" src=https://avatars.githubusercontent.com/u/9998264?v=3&amp;s=120 ></img></a>
          <div class="comment-user"><label><a href=https://cnodejs.org/user/tsyeyuanfeng >tsyeyuanfeng</a> : </label></div>
          <div class="comment-floor"><a href="javascript:void(0)">#5楼</a></div>
          <div class="comment-time">2016-06-10 11:30:09</div>
          <div class="clr"></div>
          <div class="comment-content">
            <p><div class="markdown-text"><p>太赞了，讲得很清晰，很有条理，看完之后豁然开朗</p>
</div></p>
          </div>
          <div class="clr"></div>
          
            <div class="comment-reply-list">
              
              <div class="comment-wrapper">
                <a href=https://cnodejs.org/user/bigtree9307 ><img class="comment-head" src=https://luzeshu.com/pictures/hacker.jpg ></img></a>
                <div class="comment-user"><label><a href=https://cnodejs.org/user/bigtree9307 >bigtree9307</a> : </label></div>
                <div class="comment-time">2016-06-21 10:24:28</div>
                <div class="clr"></div>
                <div class="comment-content">
                  <p><div class="markdown-text"><p><a href="/user/tsyeyuanfeng">@tsyeyuanfeng</a> ^_^</p>
</div></p>
                </div>
                <div class="clr"></div>
              </div>
              
            </div>
          
          <button class="btn btn-default comment-reply reply-button">回复</button>
          <div class="clr"></div>
        </div>
      
        <div class="comment-wrapper">
          <span class="unvisible comment-id-msg">5826923571308f7983ae92ca</span>
          <a href=https://cnodejs.org/user/vincentgor ><img class="comment-head" src=https://avatars.githubusercontent.com/u/5249329?v=3&amp;s=120 ></img></a>
          <div class="comment-user"><label><a href=https://cnodejs.org/user/vincentgor >vincentgor</a> : </label></div>
          <div class="comment-floor"><a href="javascript:void(0)">#6楼</a></div>
          <div class="comment-time">2016-07-26 21:29:44</div>
          <div class="clr"></div>
          <div class="comment-content">
            <p><div class="markdown-text"><p>啥都憋说了，阅读帖子之前先膜拜大神。哈哈哈</p>
</div></p>
          </div>
          <div class="clr"></div>
          
            <div class="comment-reply-list">
              
              <div class="comment-wrapper">
                <a href=https://cnodejs.org/user/bigtree9307 ><img class="comment-head" src=https://luzeshu.com/pictures/hacker.jpg ></img></a>
                <div class="comment-user"><label><a href=https://cnodejs.org/user/bigtree9307 >bigtree9307</a> : </label></div>
                <div class="comment-time">2016-07-26 21:40:55</div>
                <div class="clr"></div>
                <div class="comment-content">
                  <p><div class="markdown-text"><p><a href="/user/vincentgor">@vincentgor</a> 离大神还差很远哈哈</p>
</div></p>
                </div>
                <div class="clr"></div>
              </div>
              
            </div>
          
          <button class="btn btn-default comment-reply reply-button">回复</button>
          <div class="clr"></div>
        </div>
      
        <div class="comment-wrapper">
          <span class="unvisible comment-id-msg">5826923571308f7983ae92c9</span>
          <a href=https://cnodejs.org/user/JasonBoy ><img class="comment-head" src=https://avatars.githubusercontent.com/u/2911620?v=3&amp;s=120 ></img></a>
          <div class="comment-user"><label><a href=https://cnodejs.org/user/JasonBoy >JasonBoy</a> : </label></div>
          <div class="comment-floor"><a href="javascript:void(0)">#7楼</a></div>
          <div class="comment-time">2016-07-27 20:45:16</div>
          <div class="clr"></div>
          <div class="comment-content">
            <p><div class="markdown-text"><p>赞</p>
</div></p>
          </div>
          <div class="clr"></div>
          
          <button class="btn btn-default comment-reply reply-button">回复</button>
          <div class="clr"></div>
        </div>
      
        <div class="comment-wrapper">
          <span class="unvisible comment-id-msg">5826923571308f7983ae92c8</span>
          <a href=https://cnodejs.org/user/hyj1991 ><img class="comment-head" src=https://avatars.githubusercontent.com/u/19908330?v=3&amp;s=120 ></img></a>
          <div class="comment-user"><label><a href=https://cnodejs.org/user/hyj1991 >hyj1991</a> : </label></div>
          <div class="comment-floor"><a href="javascript:void(0)">#8楼</a></div>
          <div class="comment-time">2016-07-28 09:27:36</div>
          <div class="clr"></div>
          <div class="comment-content">
            <p><div class="markdown-text"><p>mark一下
最近node的js的一些热门npm库源码看了不少
想花点时间研究下node的底层实现，我想问下4.x的源码和您写的还一致嘛…
因为项目使用的node版本就是4.x，所以想研究下这个版本的node源码实现</p>
</div></p>
          </div>
          <div class="clr"></div>
          
            <div class="comment-reply-list">
              
              <div class="comment-wrapper">
                <a href=https://cnodejs.org/user/bigtree9307 ><img class="comment-head" src=https://luzeshu.com/pictures/hacker.jpg ></img></a>
                <div class="comment-user"><label><a href=https://cnodejs.org/user/bigtree9307 >bigtree9307</a> : </label></div>
                <div class="comment-time">2016-07-28 23:14:45</div>
                <div class="clr"></div>
                <div class="comment-content">
                  <p><div class="markdown-text"><p><a href="/user/hyj1991">@hyj1991</a> 是基于node4.2.2版本的源码</p>
</div></p>
                </div>
                <div class="clr"></div>
              </div>
              
            </div>
          
          <button class="btn btn-default comment-reply reply-button">回复</button>
          <div class="clr"></div>
        </div>
      
        <div class="comment-wrapper">
          <span class="unvisible comment-id-msg">5826923571308f7983ae92c7</span>
          <a href=https://cnodejs.org/user/PEIC ><img class="comment-head" src=https://avatars.githubusercontent.com/u/7608219?v=3&amp;s=120 ></img></a>
          <div class="comment-user"><label><a href=https://cnodejs.org/user/PEIC >PEIC</a> : </label></div>
          <div class="comment-floor"><a href="javascript:void(0)">#9楼</a></div>
          <div class="comment-time">2016-07-28 15:18:08</div>
          <div class="clr"></div>
          <div class="comment-content">
            <p><div class="markdown-text"><p><a href="/user/PEIC">@PEIC</a></p>
</div></p>
          </div>
          <div class="clr"></div>
          
          <button class="btn btn-default comment-reply reply-button">回复</button>
          <div class="clr"></div>
        </div>
      
        <div class="comment-wrapper">
          <span class="unvisible comment-id-msg">5826923571308f7983ae92c6</span>
          <a href=https://cnodejs.org/user/hyj1991 ><img class="comment-head" src=https://avatars.githubusercontent.com/u/19908330?v=3&amp;s=120 ></img></a>
          <div class="comment-user"><label><a href=https://cnodejs.org/user/hyj1991 >hyj1991</a> : </label></div>
          <div class="comment-floor"><a href="javascript:void(0)">#10楼</a></div>
          <div class="comment-time">2016-07-29 14:59:11</div>
          <div class="clr"></div>
          <div class="comment-content">
            <p><div class="markdown-text"><p><a href="/user/bigtree9307">@bigtree9307</a> 好的，非常感谢哇，收藏了</p>
</div></p>
          </div>
          <div class="clr"></div>
          
          <button class="btn btn-default comment-reply reply-button">回复</button>
          <div class="clr"></div>
        </div>
      
        <div class="comment-wrapper">
          <span class="unvisible comment-id-msg">5826923571308f7983ae92c5</span>
          <a href=https://cnodejs.org/user/FujiBilly ><img class="comment-head" src=https://avatars.githubusercontent.com/u/16513668?v=3&amp;s=120 ></img></a>
          <div class="comment-user"><label><a href=https://cnodejs.org/user/FujiBilly >FujiBilly</a> : </label></div>
          <div class="comment-floor"><a href="javascript:void(0)">#11楼</a></div>
          <div class="comment-time">2016-07-31 16:42:25</div>
          <div class="clr"></div>
          <div class="comment-content">
            <p><div class="markdown-text"><p>关注下。。。</p>
</div></p>
          </div>
          <div class="clr"></div>
          
          <button class="btn btn-default comment-reply reply-button">回复</button>
          <div class="clr"></div>
        </div>
      
        <div class="comment-wrapper">
          <span class="unvisible comment-id-msg">5826923571308f7983ae92c4</span>
          <a href=https://cnodejs.org/user/songqinghehe ><img class="comment-head" src=https://avatars.githubusercontent.com/u/12146443?v=3&amp;s=120 ></img></a>
          <div class="comment-user"><label><a href=https://cnodejs.org/user/songqinghehe >songqinghehe</a> : </label></div>
          <div class="comment-floor"><a href="javascript:void(0)">#12楼</a></div>
          <div class="comment-time">2016-07-31 19:21:54</div>
          <div class="clr"></div>
          <div class="comment-content">
            <p><div class="markdown-text"><p>mark</p>
</div></p>
          </div>
          <div class="clr"></div>
          
          <button class="btn btn-default comment-reply reply-button">回复</button>
          <div class="clr"></div>
        </div>
      
        <div class="comment-wrapper">
          <span class="unvisible comment-id-msg">5826923171308f7983ae92b2</span>
          <a href=http://home.cnblogs.com/u/558342/ ><img class="comment-head" src=https://luzeshu.com/avatar/simple_avatar.gif ></img></a>
          <div class="comment-user"><label><a href=http://home.cnblogs.com/u/558342/ >蓝浩</a> : </label></div>
          <div class="comment-floor"><a href="javascript:void(0)">#13楼</a></div>
          <div class="comment-time">2016-09-11 11:47:00</div>
          <div class="clr"></div>
          <div class="comment-content">
            <p>想问一下main函数在哪个文件呢？</p>
          </div>
          <div class="clr"></div>
          
          <button class="btn btn-default comment-reply reply-button">回复</button>
          <div class="clr"></div>
        </div>
      
        <div class="comment-wrapper">
          <span class="unvisible comment-id-msg">5826923171308f7983ae92b3</span>
          <a href=http://www.cnblogs.com/papertree/ ><img class="comment-head" src=https://luzeshu.com/pictures/hacker.jpg ></img></a>
          <div class="comment-user"><label><a href=http://www.cnblogs.com/papertree/ >野路子程序员</a> : </label></div>
          <div class="comment-floor"><a href="javascript:void(0)">#14楼</a></div>
          <div class="comment-time">2016-09-19 16:52:00</div>
          <div class="clr"></div>
          <div class="comment-content">
            <p>可以全局搜一下呢，4.2.2版本的是在src/node_main.cc</p>
          </div>
          <div class="clr"></div>
          
          <button class="btn btn-default comment-reply reply-button">回复</button>
          <div class="clr"></div>
        </div>
      
        <div class="comment-wrapper">
          <span class="unvisible comment-id-msg">5826923571308f7983ae92c3</span>
          <a href=https://cnodejs.org/user/hyj1991 ><img class="comment-head" src=https://avatars.githubusercontent.com/u/19908330?v=3&amp;s=120 ></img></a>
          <div class="comment-user"><label><a href=https://cnodejs.org/user/hyj1991 >hyj1991</a> : </label></div>
          <div class="comment-floor"><a href="javascript:void(0)">#15楼</a></div>
          <div class="comment-time">2016-09-26 14:12:57</div>
          <div class="clr"></div>
          <div class="comment-content">
            <p><div class="markdown-text"><p>文末的图其实第一次看的一知半解的
后来按照你的6个教程仔细看了下对应的源代码实现，现在再来看这个图，画的非常清晰
再赞一个</p>
          </div>
          <div class="clr"></div>
          
          <button class="btn btn-default comment-reply reply-button">回复</button>
          <div class="clr"></div>
        </div>
      
        <div class="comment-wrapper">
          <span class="unvisible comment-id-msg">58c965979b325705b95c2713</span>
          <a href=javascript:void(0) ><img class="comment-head" src=/pictures/default.gif ></img></a>
          <div class="comment-user"><label><a href=javascript:void(0) >kkshaq</a> : </label></div>
          <div class="comment-floor"><a href="javascript:void(0)">#16楼</a></div>
          <div class="comment-time">2017-03-16 00:02:31</div>
          <div class="clr"></div>
          <div class="comment-content">
            <p><p>感谢！博主分析的很透彻！！</p></p>
          </div>
          <div class="clr"></div>
          
          <button class="btn btn-default comment-reply reply-button">回复</button>
          <div class="clr"></div>
        </div>
      
    
  </div>

  <form method="POST" class="comment-form">
    <div class="comment-input form-group">
      <label><span class="info-must">*</span>发表评论：</label>
      <textarea type="text" class="form-control comment-text"></textarea>
    </div>
    <div class="form-group">
      <label><span class="info-must">*</span>您的姓名</label>
      <input type="text" class="form-control comment-username">
    </div>
    <div class="form-group">
      <label>头像链接(非必填)：</label>
      <input type="text" class="form-control comment-headpic">
    </div>
    <div class="form-group">
      <label>电子邮箱(非必填，仅博主可见)：</label>
      <input type="text" class="form-control comment-email">
    </div>
    <div class="form-group">
      <label>个人网址(非必填，作为您的跳转链接)：</label>
      <input type="text" class="form-control comment-link">
    </div>
    <button class="btn btn-default">提交</button>
    <div class="clr"></div>
  </form>
</div>

</div>

          
        </div>
      </div>

      <div class="sidebar-wrapper">
        <div class="sidebar col" id="sidebar">
          <div class="widget">
            <div class="widget-content">
              <div class="vt_sidebar_author_item">
                <div class="image-wrapper">
                  <a target="_self" href="https://luzeshu.com:8899"><img class="head-image" src="/pictures/hacker.jpg"></a>
                </div>
                <h4 class="author_name">
                  <a target="_self" href="https://luzeshu.com:8899">大树居士</a>
                </h4>
                <p>
                  
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="clr"></div>
    </div>
  </div>
</body>


  <script src="/jquery/jquery-3.1.1.min.js"></script> 
  <script src="/js/blog.js"></script>

</html>

