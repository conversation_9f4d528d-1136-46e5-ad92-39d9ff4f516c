<!doctype html>
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:og="http://ogp.me/ns#"
      xmlns:fb="https://www.facebook.com/2008/fbml">
  <head>
  	<meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta charset="utf-8">
    <meta name="msvalidate.01" content="36A28D9109C077BA3E623651FC1656F4" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta property="fb:admins" content="********" />
    <meta property="fb:app_id" content="***************" /> 
    <meta property="og:title" content="HTML Snippets for Twitter Boostrap framework : Bootsnipp.com" />
    <meta property="og:type" content="website" />
    <meta property="twitter:account_id" content="*********" />
    <meta property="og:url" content="http://bootsnipp.com/snippets/2X0r" />
    <meta itemprop="name" content="Bootstrap Login &amp; Signup forms in panel Example">
    <meta itemprop="description" content="Bootstrap example of Login &amp; Signup forms in panel using HTML, Javascript, jQuery, and CSS. Snippet by calvinko">
    <meta name="description" content="Bootstrap example of Login &amp; Signup forms in panel using HTML, Javascript, jQuery, and CSS. Snippet by calvinko">
    <meta name="keywords" content="bootstrap, css, javascript, jquery, code, example, snippet,Login &amp; Signup forms in panel, login, forms, panel, registration, ">
    <meta name="twitter:card" content="summary">
    <meta name="twitter:site" content="@bootsnipp">
    <meta name="twitter:title" content="Login &amp; Signup forms in panel">
    <meta name="twitter:description" content="login, forms, panel, registration,  Bootstrap HTML CSS JS code snippet by calvinko">
    <meta name="twitter:creator" content="@bootsnipp">
    <meta name="twitter:image:src" content="http://bootsnipp.com/img/screenshots/2db464d97cbad6d7bf3e97766d4c032c76830995.jpeg ">
      <meta property="og:image" content="http://bootsnipp.com/img/logo.jpg" />
    <meta property="og:site_name" content="Bootsnipp.com" />
      <meta property="og:description" content="Awesome Bootstrap HTML CSS JS Snippet on Bootsnipp.com." />
      <title>Bootstrap Snippet Login &amp; Signup forms in panel using HTML  Bootstrap   | Bootsnipp.com</title>
    <link rel="apple-touch-icon-precomposed" href="apple-touch-icon-precomposed.png">
    <link rel="apple-touch-icon-precomposed" href="apple-touch-icon-72x72-precomposed.png">
    <link rel="apple-touch-icon-precomposed" href="apple-touch-icon-114x114-precomposed.png">
    <link rel="apple-touch-icon-precomposed" href="apple-touch-icon-144x144-precomposed.png">
    <link rel="alternate" type="application/rss+xml" title="Latest snippets from Bootsnipp.com" href="http://bootsnipp.com/feed.rss" />

    <link rel="stylesheet" href="//netdna.bootstrapcdn.com/bootstrap/3.3.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="//netdna.bootstrapcdn.com/font-awesome/3.2.1/css/font-awesome.min.css">
    <link rel="stylesheet" href="http://bootsnipp.com/dist/bootsnipp.min.css?ver=7d23ff901039aef6293954d33d23c066">
        <!-- HTML5 shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!--[if lt IE 9]>
    	<script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.6.2/html5shiv.js"></script>
    	<script src="//cdnjs.cloudflare.com/ajax/libs/respond.js/1.2.0/respond.js"></script>
    <![endif]-->
<script>
  (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
  (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
  m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
  })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

  ga('create', 'UA-55581850-1', 'auto', {'allowLinker': true});
  ga('require', 'linker');
  ga('linker:autoLink', ['danstools.com','unixtimestamp.com','url-encode-decode.com','cssfontstack.com','hexcolortool.com','htaccessredirect.net','jspretty.com','jsmini.com','jsobfuscate.com','md5hashgenerator.com','regextester.com','cleancss.com','favicon-generator.org','website-performance.org','permissions-calculator.org','conversoes.org','convertissez.fr','convertitore.net','elconvertidor.com','files-conversion.com','henkan-muryo.com','konvertirung.org','konvertor.org','tahwil.net','zhuan-huan.com','bootsnipp.com'] );
  ga('send', 'pageview');

        window.onload = function() {
        $('body').append('<script type="text/javascript" src="https://srv.buysellads.com/ads/CVADLKQJ.json?callback=drop_ad" async><\/script>');
        $('body').append("<style>.bsa-apiads {\
  line-height: 1.5;\
  display: inline-block;\
  float: left;\
  font-size: 12px;\
  background-color: #5A8DB6;\
  border: solid 1px #337799;\
  box-shadow: inset 0 1px hsla(0, 0%, 100%, .1);\
  box-sizing: border-box;\
  background-image: url('http://www.danstools.com/devops/img/devoops_pattern_b10.png');\
  margin: 1em 1em 0 2em;\
  border-radius: 4px;\
  text-align: center;\
  padding: .25em;\
}\
 \
.bsa-apiads a:before {\
  margin-right: 4px;\
  padding: 2px 6px;\
  border-radius: 3px;\
  background-color: #58B668;\
  color: #fff;\
  content: 'Ad';\
}\
 \
  .bsa-apiads a {\
    color: #fff;\
  }\
 \
  .bsa-apiads a:hover {\
    color: inherit;\
  }</style>");
        setTimeout(function() {
          var ad = document.querySelector(".bsap_ac1f95c148ce6148393fd34e69a52240");
          var promos = document.querySelectorAll(".gfabz");
	  var isblock = 0;
          for (var i = 0; i < promos.length; i++) {
                  var promo = promos[i];
          if (isblock ==1 || (ad && ad.innerHTML.replace(/\s/g, "").length == 0)) {
	    isblock = 1;
//            ad.style.cssText = 'display:block !important';
//          promo.style.cssText = 'display:none !important';
            promo.innerHTML = '<a href=\"https://photodune.net/?ref=skyhighpn\"><img src=\"/img/pd2.jpg\" /></a><p>';
	    promo.style.display= 'inline-block';
	    promo.style.visibility= 'visible';
            promo.style.maxWidth= '';
          } else if (promo) {
            promo.innerHTML = '<center>\
<div id="gfabz2" style="background-color:#FCFCFC;width: 728px;height:90px;padding:10px;border:1px solid #cccccc;text-align:left;">\
<h5 style="margin-top:0px;margin-bottom:2px">\
<i class="fa fa-area-chart"></i> <a href="https://photodune.net/?ref=skyhighpn" >Royalty Free Stock Photos</a></h5>\
Find the perfect royalty free image\
</div>\
</center>';
//            var promotwo = document.querySelector("#toppromo2");
            promo.childNodes[0].childNodes[0].style.width= '';
          }
	  }
        }, 1000);
      };
   
</script>
    <script type="text/javascript">
    var fb_param = {};
    fb_param.pixel_id = '6007046190250';
    fb_param.value = '0.00';
    (function(){
      var fpw = document.createElement('script');
      fpw.async = true;
      fpw.src = '//connect.facebook.net/en_US/fp.js';
      var ref = document.getElementsByTagName('script')[0];
      ref.parentNode.insertBefore(fpw, ref);
    })();
    </script>
    <noscript><img height="1" width="1" alt="" style="display:none" src="https://www.facebook.com/offsite_event.php?id=6007046190250&amp;value=0" /></noscript>
  </head>
  <body>
<!-- BuySellAds Ad Code -->
<script type="text/javascript">
(function(){
  var bsa = document.createElement('script');
     bsa.type = 'text/javascript';
     bsa.async = true;
     bsa.src = '//s3.buysellads.com/ac/bsa.js';
  (document.getElementsByTagName('head')[0]||document.getElementsByTagName('body')[0]).appendChild(bsa);
})();
</script>
<!-- End BuySellAds Ad Code -->

    <nav class="navbar navbar-fixed-top navbar-bootsnipp animate" role="navigation" style="z-index: 9999999">
  <div class="container">
    <!-- Brand and toggle get grouped for better mobile display -->
    <div class="navbar-header">
      <button type="button" class="navbar-toggle" data-toggle="collapse" data-target="#bs-example-navbar-collapse-2">
        <span class="sr-only">Toggle navigation</span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
        <span class="icon-bar"></span>
      </button>
      <div class="animbrand">
        <a class="navbar-brand animate" href="http://bootsnipp.com">Bootsnipp</a>
      </div>
    </div>
<script src="/js/BSAcpc.js" async></script>
<div class="bsa-apiads hidden-sm hidden-xs"></div>
    <!-- Collect the nav links, forms, and other content for toggling -->
    <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-2">
      <ul class="nav navbar-nav navbar-right">
        <li class="visible-xs">
          <form action="http://bootsnipp.com/search" method="GET" role="search">
            <div class="input-group">
              <input type="text" class="form-control" name="q" placeholder="Search for snippets">
              <span class="input-group-btn">
                <button class="btn btn-primary" type="submit"><span class="glyphicon glyphicon-search"></span></button>
                <button class="btn btn-danger" type="reset"><span class="glyphicon glyphicon-remove"></span></button>
              </span>
            </div>
          </form>
        </li>
<!--        <li class=""><a href="http://bootsnipp.com/about" class="animate">About</a></li>-->
        <li>
          <a href="#" class="dropdown-toggle " data-toggle="dropdown">Tools <span class="caret"></span></a>
          <ul class="dropdown-menu" role="menu">
<!--            <li class=""><a href="http://bootsnipp.com/blog" class="animate">Blog <span class="pull-right glyphicon glyphicon-pencil"></span></a></li>
            <li class=""><a href="http://bootsnipp.com/resources" class="animate">List of resources <span class="pull-right glyphicon glyphicon-align-justify"></span></a></li>
            <li><a href="http://getbootstrap.com" target="_blank" class="animate">Download Bootstrap <span class="pull-right glyphicon glyphicon-cloud-download"></span></a></li>
            <li class="dropdown-header">Bootstrap Templates</li>
            <li class=""><a href="http://bootsnipp.com/templates" class="animate">Browse Templates <span class="pull-right glyphicon glyphicon-shopping-cart"></span></a></li>
            <li class="dropdown-header">Builders</li>
-->
            <li class=""><a href="http://bootsnipp.com/forms" class="">Form Builder <span class="pull-right glyphicon glyphicon-tasks"></span></a></li>
            <li class=""><a href="http://bootsnipp.com/buttons" class="">Button Builder <span class="pull-right glyphicon glyphicon-edit"></span></a></li>
            <li class=""><a href="http://bootsnipp.com/iconsearch" class="">Icon Search <span class="pull-right glyphicon glyphicon-search"></span></a></li>
            <li class="dropdown-header">Dan's Tools</li>
            <li class=""><a href="http://www.cleancss.com/diff-compare-merge/" class="">Diff / Merge <span class="pull-right glyphicon glyphicon-transfer"></span></a></li>
            <li class=""><a href="http://www.hexcolortool.com/" class="">Color Picker <span class="pull-right glyphicon glyphicon-pencil"></span></a></li>
            <li class=""><a href="http://www.danstools.com/keyword-tool/" class="">Keyword Tool <span class="pull-right glyphicon glyphicon-list-alt"></span></a></li>
            <li class=""><a href="http://www.cssfontstack.com/Web-Fonts/" class="">Web Fonts <span class="pull-right glyphicon glyphicon-font"></span></a></li>
            <li class=""><a href="http://www.htaccessredirect.net/" class="">.htaccess Generator <span class="pull-right glyphicon glyphicon-console"></span></a></li>
            <li class=""><a href="http://www.favicon-generator.org/" class="">Favicon Generator <span class="pull-right glyphicon glyphicon-picture"></span></a></li>
            <li class=""><a href="http://www.website-performance.org/" class="">Site Speed Test <span class="pull-right glyphicon glyphicon-dashboard"></span></a></li>

          </ul>
        </li>
        <li class="dropdown">
          <a href="http://bootsnipp.com/snippets" class="dropdown-toggle " data-toggle="dropdown">Snippets <span class="caret"></span></a>
          <ul class="dropdown-menu" role="menu">
            <li class="active"><a href="http://bootsnipp.com/snippets/featured" class="">Featured <span class="pull-right glyphicon glyphicon-star"></span></a></li>
            <li class=""><a href="http://bootsnipp.com/tags" class="">Tags  <span class="pull-right glyphicon glyphicon-tags"></span></a></li>
            <li class="dropdown-header">By Bootstrap Version</li>
                          <li><a href="http://bootsnipp.com/tags/4.0.0" class="">4.0.0</a></li>
                          <li><a href="http://bootsnipp.com/tags/3.3.0" class="">3.3.0</a></li>
                          <li><a href="http://bootsnipp.com/tags/3.2.0" class="">3.2.0</a></li>
                          <li><a href="http://bootsnipp.com/tags/3.1.0" class="">3.1.0</a></li>
                          <li><a href="http://bootsnipp.com/tags/3.0.3" class="">3.0.3</a></li>
                          <li><a href="http://bootsnipp.com/tags/3.0.1" class="">3.0.1</a></li>
                          <li><a href="http://bootsnipp.com/tags/3.0.0" class="">3.0.0</a></li>
                          <li><a href="http://bootsnipp.com/tags/2.3.2" class="">2.3.2</a></li>
                      </ul>
        </li>
        
                  <li class=""><a href="http://bootsnipp.com/register" class="">Register</a></li>
          <li id="nav-login-btn" class=""><a href="http://bootsnipp.com/login" class="">Login</a></li>
        
        <li class="hidden-xs"><a href="#toggle-search" class=""><span class="glyphicon glyphicon-search"></span></a></li>
      </ul>
    </div>
  </div>
  <div class="bootsnipp-search ">
    <div class="container">
      <form action="http://bootsnipp.com/search" method="GET" role="search">
        <div class="input-group">
          <input type="text" class="form-control" name="q" placeholder="Search for snippets and hit enter">
          <span class="input-group-btn">
            <button class="btn btn-danger" type="reset"><span class="glyphicon glyphicon-remove"></span></button>
          </span>
        </div>
      </form>
    </div>
  </div>
</nav>
           <div class="container" style="margin-top:10px;">
        <div class="row" itemscope="http://schema.org/Thing">
	 <div class="col-sm-12 col-md-4">
                <div class="title-bar" style="margin-bottom:0px;"> 
                    <a href="http://bootsnipp.com/calvinko" title="Bootstrap snippets by calvinko" class="avatar-sm-container pull-left"><img src="https://secure.gravatar.com/avatar/6c34f4f35fccb0daf4cdb803d4f4d8d6?s=100&r=g&d=mm" class="img-rounded user-avatar-sm"></a>

                    <h4 itemprop="name">&quot;Login &amp; Signup forms in panel&quot;<br>
                    <small>Bootstrap 3.1.0 Snippet by <a href="http://bootsnipp.com/calvinko" title="Bootstrap snippets by calvinko" itemscope itemtype="http://schema.org/Person" itemprop="name">calvinko</a></small>
                    </h4>
                    <div class="clearfix"></div>
                </div> 
                <div id="tags" itemprop="description" style="padding-top:0px; float:left;">

                    <a href="http://bootsnipp.com/tags/3.1.0"><span class="label label-primary">3.1.0</span></a>
		                                                <a href="http://bootsnipp.com/tags/login" title="login"><span class="label label-primary">login</span></a>
                                            <a href="http://bootsnipp.com/tags/forms" title="forms"><span class="label label-primary">forms</span></a>
                                            <a href="http://bootsnipp.com/tags/panel" title="panel"><span class="label label-primary">panel</span></a>
                                            <a href="http://bootsnipp.com/tags/registration" title="registration"><span class="label label-primary">registration</span></a>
                                                                <a href="http://bootsnipp.com/tags/panel" title="panel"><span class="label label-primary">panel</span></a>
                                            <a href="http://bootsnipp.com/tags/forms" title="forms"><span class="label label-primary">forms</span></a>
                                            <a href="http://bootsnipp.com/tags/login" title="login"><span class="label label-primary">login</span></a>
                                            <a href="http://bootsnipp.com/tags/signup" title="signup"><span class="label label-primary">signup</span></a>
                                    </div> 

	 </div>
	 <div class="col-sm-12 col-md-8">
                <div style="margin-top:10px">
<center>
	<!-- bootsnipp-leaderboard-2 -->
<!--	<ins class="adsbygoogle"
	     style="display:inline-block;width:728px;height:90px"
	     data-ad-client="ca-pub-8815422507798180"
	     data-ad-slot="6745801727"></ins>
	<script>
	(adsbygoogle = window.adsbygoogle || []).push({});
	</script>
-->
<!-- BuySellAds Zone Code -->
<div id="bsap_1305164" class="bsarocks bsap_ac1f95c148ce6148393fd34e69a52240"></div>
<!-- End BuySellAds Zone Code -->

<div class="hidden-xs hidden-sm hidden-md gfabz" style="max-width:300px;vertical-align:top;display:none;visibility:hidden;margin-left:10px;height:90px"></div>
</center>
</div>

	 </div>
	</div>
	<div class="row" style="margin-top: 10px;margin-bottom:10px;">
                   <div class="col-md-8">
                        <div class="btn-group">
                            <button type="button" id="show-preview" class="active btn btn-info">Preview</button>
                            <button type="button" id="show-html" class="btn btn-info">HTML</button>
                                                                                </div>

                        <div class="btn-group">
                          <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                            <span class="glyphicon glyphicon-cog"></span>
                          </button>
                          <ul class="dropdown-menu pull-right" role="menu">
                                                        <li><a href="http://bootsnipp.com/fullscreen/2X0r" target="_blank"><span class="glyphicon glyphicon-fullscreen"></span> View Full Screen</a></li>
                                                      </ul>
                        </div>

                        <a href="http://bootsnipp.com/fork/2X0r" id="reboot" class="btn btn-default tip" title="Copy this snippet"><span class="glyphicon glyphicon-retweet"></span> Fork this</a>
			<div id="action-bar" style="display: inline-block">
			    <a href="#" class="btn btn-default disabled"><i class="icon-eye-open"></i> 408.3K</a>
			    <a href="#comments" class="btn btn-default tip" title="View comments"><i class="icon-comment"></i></a>
			    				<a href="http://bootsnipp.com/favorites/snippet/2X0r?url=snippets%2Ffeatured%2Flogin-amp-signup-forms-in-panel" class="btn btn-default tip" title="Please log in to favorite this snippet"><span class="glyphicon glyphicon-thumbs-up"></span> 270 Fav</a>
			    			</div>
                    </div>
		    <div class="col-md-4">
			    <div class="pull-right social-sharing hidden-xs" style="padding: 0px">
				<a href="#" class="btn btn-primary"
				  onclick="window.open(
				      'https://www.facebook.com/sharer/sharer.php?u='+encodeURIComponent('http://bootsnipp.com/snippets/featured/login-amp-signup-forms-in-panel') +'&t=' + encodeURIComponent('Login &amp; Signup forms in panel #Bootstrap #snippet'), 
				      'facebook-share-dialog', 
				      'width=626,height=436,top='+((screen.height - 436) / 2)+',left='+((screen.width - 626)/2 )); 
				    return false;">
				    <i class="icon-facebook"></i>
				</a>
				
				<a href="#" class="btn btn-info"
				  onclick="window.open(
				      'https://twitter.com/share?url='+encodeURIComponent('http://bootsnipp.com/snippets/featured/login-amp-signup-forms-in-panel')+'&text='+encodeURIComponent('I shared a cool #snippet on @bootsnipp  :') + '&count=none/', 
				      'twitter-share-dialog', 
				      'width=626,height=436,top='+((screen.height - 436) / 2)+',left='+((screen.width - 626)/2 )); 
				    return false;">
				  <i class="icon-twitter"></i>
				</a>
			    </div>

		    </div>

                <div class="row visible-xs">
                    <div class="col-md-12">
                        <p>
                        <div class="btn-group btn-group-justified">
                            <a href="#" class="btn btn-primary"
                              onclick="window.open(
                                  'https://www.facebook.com/sharer/sharer.php?u='+encodeURIComponent('http://bootsnipp.com/snippets/featured/login-amp-signup-forms-in-panel') +'&t=' + encodeURIComponent('Login &amp; Signup forms in panel #Bootstrap #snippet'), 
                                  'facebook-share-dialog', 
                                  'width=626,height=436,top='+((screen.height - 436) / 2)+',left='+((screen.width - 626)/2 )); 
                                return false;">
                                <i class="icon-facebook"></i> Post to Facebook
                            </a>
                            
                            <a href="#" class="btn btn-info"
                              onclick="window.open(
                                  'https://twitter.com/share?url='+encodeURIComponent('http://bootsnipp.com/snippets/featured/login-amp-signup-forms-in-panel')+'&text='+encodeURIComponent('I shared a cool #snippet on @bootsnipp  :') + '&count=none/', 
                                  'twitter-share-dialog', 
                                  'width=626,height=436,top='+((screen.height - 436) / 2)+',left='+((screen.width - 626)/2 )); 
                                return false;">
                              <i class="icon-twitter"></i> Tweet this
                            </a>
                        </div>
                        </p>
                    </div>
                    
                </div>

            </div>
         
            

        </div>
        
    </div>

    <div id="playground-container" style="overflow: hidden">
        <div id="preview-container"><iframe id="snippet-preview" class="preview-iframe" src="http://bootsnipp-env.elasticbeanstalk.com/iframe/2X0r"></iframe></div>
        <div class="container">
            <div class="row" itemscope="http://schema.org/Code" >
                <div class="col-lg-12" itemprop="programmingLanguage" content="html/css/js">
                    <div id="editor-html" class="playground-editor" itemprop="sampleType">    <div class="container">    
        <div id="loginbox" style="margin-top:50px;" class="mainbox col-md-6 col-md-offset-3 col-sm-8 col-sm-offset-2">                    
            <div class="panel panel-info" >
                    <div class="panel-heading">
                        <div class="panel-title">Sign In</div>
                        <div style="float:right; font-size: 80%; position: relative; top:-10px"><a href="#">Forgot password?</a></div>
                    </div>     

                    <div style="padding-top:30px" class="panel-body" >

                        <div style="display:none" id="login-alert" class="alert alert-danger col-sm-12"></div>
                            
                        <form id="loginform" class="form-horizontal" role="form">
                                    
                            <div style="margin-bottom: 25px" class="input-group">
                                        <span class="input-group-addon"><i class="glyphicon glyphicon-user"></i></span>
                                        <input id="login-username" type="text" class="form-control" name="username" value="" placeholder="username or email">                                        
                                    </div>
                                
                            <div style="margin-bottom: 25px" class="input-group">
                                        <span class="input-group-addon"><i class="glyphicon glyphicon-lock"></i></span>
                                        <input id="login-password" type="password" class="form-control" name="password" placeholder="password">
                                    </div>
                                    

                                
                            <div class="input-group">
                                      <div class="checkbox">
                                        <label>
                                          <input id="login-remember" type="checkbox" name="remember" value="1"> Remember me
                                        </label>
                                      </div>
                                    </div>


                                <div style="margin-top:10px" class="form-group">
                                    <!-- Button -->

                                    <div class="col-sm-12 controls">
                                      <a id="btn-login" href="#" class="btn btn-success">Login  </a>
                                      <a id="btn-fblogin" href="#" class="btn btn-primary">Login with Facebook</a>

                                    </div>
                                </div>


                                <div class="form-group">
                                    <div class="col-md-12 control">
                                        <div style="border-top: 1px solid#888; padding-top:15px; font-size:85%" >
                                            Don&#039;t have an account! 
                                        <a href="#" onClick="$(&#039;#loginbox&#039;).hide(); $(&#039;#signupbox&#039;).show()">
                                            Sign Up Here
                                        </a>
                                        </div>
                                    </div>
                                </div>    
                            </form>     



                        </div>                     
                    </div>  
        </div>
        <div id="signupbox" style="display:none; margin-top:50px" class="mainbox col-md-6 col-md-offset-3 col-sm-8 col-sm-offset-2">
                    <div class="panel panel-info">
                        <div class="panel-heading">
                            <div class="panel-title">Sign Up</div>
                            <div style="float:right; font-size: 85%; position: relative; top:-10px"><a id="signinlink" href="#" onclick="$(&#039;#signupbox&#039;).hide(); $(&#039;#loginbox&#039;).show()">Sign In</a></div>
                        </div>  
                        <div class="panel-body" >
                            <form id="signupform" class="form-horizontal" role="form">
                                
                                <div id="signupalert" style="display:none" class="alert alert-danger">
                                    <p>Error:</p>
                                    <span></span>
                                </div>
                                    
                                
                                  
                                <div class="form-group">
                                    <label for="email" class="col-md-3 control-label">Email</label>
                                    <div class="col-md-9">
                                        <input type="text" class="form-control" name="email" placeholder="Email Address">
                                    </div>
                                </div>
                                    
                                <div class="form-group">
                                    <label for="firstname" class="col-md-3 control-label">First Name</label>
                                    <div class="col-md-9">
                                        <input type="text" class="form-control" name="firstname" placeholder="First Name">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="lastname" class="col-md-3 control-label">Last Name</label>
                                    <div class="col-md-9">
                                        <input type="text" class="form-control" name="lastname" placeholder="Last Name">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label for="password" class="col-md-3 control-label">Password</label>
                                    <div class="col-md-9">
                                        <input type="password" class="form-control" name="passwd" placeholder="Password">
                                    </div>
                                </div>
                                    
                                <div class="form-group">
                                    <label for="icode" class="col-md-3 control-label">Invitation Code</label>
                                    <div class="col-md-9">
                                        <input type="text" class="form-control" name="icode" placeholder="">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <!-- Button -->                                        
                                    <div class="col-md-offset-3 col-md-9">
                                        <button id="btn-signup" type="button" class="btn btn-info"><i class="icon-hand-right"></i> &amp;nbsp Sign Up</button>
                                        <span style="margin-left:8px;">or</span>  
                                    </div>
                                </div>
                                
                                <div style="border-top: 1px solid #999; padding-top:20px"  class="form-group">
                                    
                                    <div class="col-md-offset-3 col-md-9">
                                        <button id="btn-fbsignup" type="button" class="btn btn-primary"><i class="icon-facebook"></i> &nbsp; Sign Up with Facebook</button>
                                    </div>                                           
                                        
                                </div>
                                
                                
                                
                            </form>
                         </div>
                    </div>

               
               
                
         </div> 
    </div>
    </div>
                    <div id="editor-css" class="playground-editor" itemprop="sampleType"></div>
                    <div id="editor-js" class="playground-editor" itemprop="sampleType"></div>
                </div>
            </div>
        </div>
    </div>



    <div class="container">
                <div class="row">
            <div class="col-lg-12">
                <h2>Similar snippets: <small><a href="http://bootsnipp.com/similar/2X0r">See <!--392--> More</a></small></h2>
            </div>
        </div>
        <div class="row">
                            <div class="col-sm-12 col-xs-12 col-md-3 col-lg-3">
    <div class="thumbnail bootsnipp-thumb">
	    <div>
	    	<p class="pull-right view-counts hidden-md">
	    		<a href="http://bootsnipp.com/snippets/r86RR" class="tip" data-toggle="tooltip" title="Viewed">581 <i class="icon-eye-open"></i></a> 

	    		<a href="http://bootsnipp.com/snippets/r86RR" class="tip" data-toggle="tooltip" title="Favorited">0 <i class="icon-thumbs-up"></i></a> 
<!--	    		<a href="http://bootsnipp.com/tags/3.3.0"><span class="label label-info tip" data-toggle="tooltip" title="Bootstrap version">3.3.0</span></a>-->
	    	</p>
	    	<p class="lead snipp-title">
	    		<a href="http://bootsnipp.com/snippets/r86RR">sign up and login forms responsive</a>
	    	</p>
	    </div>
                        <div style="padding-left:10px;height:200px;position:relative"><iframe class="new-preview" rel="nofollow" style="width: 1px; height: 1px; z-index:1;" frameborder="0" scrolling="no" src="http://d2d3qesrx8xj6s.cloudfront.net/iframe/r86RR"></iframe>
<a href="http://bootsnipp.com/snippets/r86RR" style="height:100%;width:100%">
<div style="position:absolute;top:0;left:0;width:100%;height:100%">
</div>
</a>
</div>
                        <!--<div style="padding-left:10px;"><iframe class="snippet-preview" frameborder="0" scrolling="no" allowTransparency="true" src="http://d2d3qesrx8xj6s.cloudfront.net/siframe/r86RR" data-url="http://bootsnipp.com/snippets/r86RR"></iframe></div>-->
    	<div class="caption">
<!--        	<p><a href="http://bootsnipp.com/snippets/r86RR" class="btn btn-primary btn-lg btn-block">View</a></p>-->
    	</div>
    </div>
</div>
                            <div class="col-sm-12 col-xs-12 col-md-3 col-lg-3">
    <div class="thumbnail bootsnipp-thumb">
	    <div>
	    	<p class="pull-right view-counts hidden-md">
	    		<a href="http://bootsnipp.com/snippets/33d2n" class="tip" data-toggle="tooltip" title="Viewed">4.7K <i class="icon-eye-open"></i></a> 

	    		<a href="http://bootsnipp.com/snippets/33d2n" class="tip" data-toggle="tooltip" title="Favorited">0 <i class="icon-thumbs-up"></i></a> 
<!--	    		<a href="http://bootsnipp.com/tags/3.3.0"><span class="label label-info tip" data-toggle="tooltip" title="Bootstrap version">3.3.0</span></a>-->
	    	</p>
	    	<p class="lead snipp-title">
	    		<a href="http://bootsnipp.com/snippets/33d2n">SignUp &amp;&amp; Login </a>
	    	</p>
	    </div>
                        <div style="padding-left:10px;height:200px;position:relative"><iframe class="new-preview" rel="nofollow" style="width: 1px; height: 1px; z-index:1;" frameborder="0" scrolling="no" src="http://d2d3qesrx8xj6s.cloudfront.net/iframe/33d2n"></iframe>
<a href="http://bootsnipp.com/snippets/33d2n" style="height:100%;width:100%">
<div style="position:absolute;top:0;left:0;width:100%;height:100%">
</div>
</a>
</div>
                        <!--<div style="padding-left:10px;"><iframe class="snippet-preview" frameborder="0" scrolling="no" allowTransparency="true" src="http://d2d3qesrx8xj6s.cloudfront.net/siframe/33d2n" data-url="http://bootsnipp.com/snippets/33d2n"></iframe></div>-->
    	<div class="caption">
<!--        	<p><a href="http://bootsnipp.com/snippets/33d2n" class="btn btn-primary btn-lg btn-block">View</a></p>-->
    	</div>
    </div>
</div>
                            <div class="col-sm-12 col-xs-12 col-md-3 col-lg-3">
    <div class="thumbnail bootsnipp-thumb">
	    <div>
	    	<p class="pull-right view-counts hidden-md">
	    		<a href="http://bootsnipp.com/snippets/bpxxA" class="tip" data-toggle="tooltip" title="Viewed">1.2K <i class="icon-eye-open"></i></a> 

	    		<a href="http://bootsnipp.com/snippets/bpxxA" class="tip" data-toggle="tooltip" title="Favorited">1 <i class="icon-thumbs-up"></i></a> 
<!--	    		<a href="http://bootsnipp.com/tags/3.2.0"><span class="label label-info tip" data-toggle="tooltip" title="Bootstrap version">3.2.0</span></a>-->
	    	</p>
	    	<p class="lead snipp-title">
	    		<a href="http://bootsnipp.com/snippets/bpxxA">logIn and signUp form</a>
	    	</p>
	    </div>
                        <div style="padding-left:10px;height:200px;position:relative"><iframe class="new-preview" rel="nofollow" style="width: 1px; height: 1px; z-index:1;" frameborder="0" scrolling="no" src="http://d2d3qesrx8xj6s.cloudfront.net/iframe/bpxxA"></iframe>
<a href="http://bootsnipp.com/snippets/bpxxA" style="height:100%;width:100%">
<div style="position:absolute;top:0;left:0;width:100%;height:100%">
</div>
</a>
</div>
                        <!--<div style="padding-left:10px;"><iframe class="snippet-preview" frameborder="0" scrolling="no" allowTransparency="true" src="http://d2d3qesrx8xj6s.cloudfront.net/siframe/bpxxA" data-url="http://bootsnipp.com/snippets/bpxxA"></iframe></div>-->
    	<div class="caption">
<!--        	<p><a href="http://bootsnipp.com/snippets/bpxxA" class="btn btn-primary btn-lg btn-block">View</a></p>-->
    	</div>
    </div>
</div>
                            <div class="col-sm-12 col-xs-12 col-md-3 col-lg-3">
    <div class="thumbnail bootsnipp-thumb">
	    <div>
	    	<p class="pull-right view-counts hidden-md">
	    		<a href="http://bootsnipp.com/snippets/rgzOz" class="tip" data-toggle="tooltip" title="Viewed">1.7K <i class="icon-eye-open"></i></a> 

	    		<a href="http://bootsnipp.com/snippets/rgzOz" class="tip" data-toggle="tooltip" title="Favorited">0 <i class="icon-thumbs-up"></i></a> 
<!--	    		<a href="http://bootsnipp.com/tags/3.2.0"><span class="label label-info tip" data-toggle="tooltip" title="Bootstrap version">3.2.0</span></a>-->
	    	</p>
	    	<p class="lead snipp-title">
	    		<a href="http://bootsnipp.com/snippets/rgzOz">dropdown login toggle signup</a>
	    	</p>
	    </div>
                        <div style="padding-left:10px;height:200px;position:relative"><iframe class="new-preview" rel="nofollow" style="width: 1px; height: 1px; z-index:1;" frameborder="0" scrolling="no" src="http://d2d3qesrx8xj6s.cloudfront.net/iframe/rgzOz"></iframe>
<a href="http://bootsnipp.com/snippets/rgzOz" style="height:100%;width:100%">
<div style="position:absolute;top:0;left:0;width:100%;height:100%">
</div>
</a>
</div>
                        <!--<div style="padding-left:10px;"><iframe class="snippet-preview" frameborder="0" scrolling="no" allowTransparency="true" src="http://d2d3qesrx8xj6s.cloudfront.net/siframe/rgzOz" data-url="http://bootsnipp.com/snippets/rgzOz"></iframe></div>-->
    	<div class="caption">
<!--        	<p><a href="http://bootsnipp.com/snippets/rgzOz" class="btn btn-primary btn-lg btn-block">View</a></p>-->
    	</div>
    </div>
</div>
                    </div>
        
    <div class="row">
      <div class="col-md-12">
        <div style="margin-top:10px">
<center>
	<!-- bootsnipp-leaderboard-2 -->
	<ins class="adsbygoogle"
	     style="display:inline-block;width:728px;height:90px"
	     data-ad-client="ca-pub-8815422507798180"
	     data-ad-slot="6745801727"></ins>
	<script>
	(adsbygoogle = window.adsbygoogle || []).push({});
	</script>

<div class="hidden-xs hidden-sm hidden-md gfabz" style="max-width:300px;vertical-align:top;display:inline-block;margin-left:10px;height:90px;"></div>
</center>
</div>

      </div>
    </div>
<hr />
        <div class="row">
            <div class="col-md-8">
                <h2 id="comments">Comments: </h2>
             <div id="disqus_thread"></div>
                <script type="text/javascript">
                    /* * * CONFIGURATION VARIABLES: EDIT BEFORE PASTING INTO YOUR WEBPAGE * * */
                    var disqus_shortname = 'bootsnipp'; // required: replace example with your forum shortname
                    var disqus_identifier = '2X0r';
                    /* * * DON'T EDIT BELOW THIS LINE * * */
                    (function() {
                        var dsq = document.createElement('script'); dsq.type = 'text/javascript'; dsq.async = true;
                        dsq.src = 'http://' + disqus_shortname + '.disqus.com/embed.js';
                        (document.getElementsByTagName('head')[0] || document.getElementsByTagName('body')[0]).appendChild(dsq);
                    })();
                </script>
                <noscript>Please enable JavaScript to view the <a href="http://disqus.com/?ref_noscript">comments powered by Disqus.</a></noscript>
                <a href="http://disqus.com" class="dsq-brlink">comments powered by <span class="logo-disqus">Disqus</span></a>

<!--<div class="fb-comments" data-href="http://bootsnipp.com/snippets/featured/login-amp-signup-forms-in-panel" data-numposts="10"></div>-->
            </div>
	    <div class="col-md-4" style="padding-top: 60px">
<script async src="//pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
<!-- body box -->
<ins class="adsbygoogle"
     style="display:inline-block;width:336px;height:280px"
     data-ad-client="ca-pub-8815422507798180"
     data-ad-slot="3792335328"></ins>
<script>
(adsbygoogle = window.adsbygoogle || []).push({});
</script>
	    </div>
        </div>
    </div>
    <footer class="bs-footer" role="contentinfo">
  <div class="container">
    <div class="bs-social">
      <ul class="bs-social-buttons">
        <li class="facebook-button">
          <div id="fb-root"></div>
          <div id="js-facebook-share" class="fb-like" data-href="http://bootsnipp.com" data-width="130" data-layout="button_count" data-action="like" data-show-faces="false" data-share="true"></div>        
        </li>
        <li class="follow-btn">
          <a id="js-twitter-follow" href="https://twitter.com/bootsnipp" class="twitter-follow-button" data-show-count="false">Follow @bootsnipp</a>
        </li>
        <li class="tweet-btn">
          <a id="js-twitter-tweet" href="https://twitter.com/share" class="twitter-share-button" data-url="http://bootsnipp.com" data-text="RT Design elements and code snippets for #twbootstrap HTML/CSS/JS framework" data-via="bootsnipp" data-related="bootsnipp">Tweet</a>
        </li>
      </ul>
    </div>
    <p>Bootsnipp.com &copy; 2015 <a href="http://www.danstools.com" target="_blank">Dan's Tools</a> | <a href="http://bootsnipp.com/privacy" target="_blank">Site Privacy policy</a> | <a href="https://www.buysellads.com/buy/detail/270577" rel="nofollow">Advertise</a> | Featured snippets are <a href="http://bootsnipp.com/license">MIT license.</a> </p>
  </div>
<style>
._fancybar{margin-top:50px !important;z-index: 5}
</style>
<script async type="text/javascript" src="//cdn.fancybar.net/ac/fancybar.js?zoneid=1502&serve=C6ADVKE&placement=danstools" id="_fancybar_js"></script>

</footer>

    <script src="//code.jquery.com/jquery-1.11.0.min.js"></script>
    <script src="//netdna.bootstrapcdn.com/bootstrap/3.3.2/js/bootstrap.min.js"></script>
    <script src="http://bootsnipp.com/dist/scripts.min.js"></script>
    <script async src="//pagead2.googlesyndication.com/pagead/js/adsbygoogle.js"></script>
    <script type="text/javascript">
$(function(){
        $('iframe.new-preview').each(function()
        {
                previewportWidth = $(this).parent().innerWidth()-15;
                factor = previewportWidth/1200;
console.log(factor);
                $(this).css({
    'height': '800px',
    'width': '1200px',
    '-ms-zoom': factor,
    '-ms-transform': 'scale('+factor+')',
    'transform': 'scale('+factor+')',
    '-moz-transform': 'scale('+factor+')',
    '-moz-transform-origin': '0 0',
    '-o-transform': 'scale('+factor+')',
    '-o-transform-origin': '0 0',
    '-webkit-transform': 'scale('+factor+')',
    '-webkit-transform-origin': '0 0'
                });
                $(this).parent().css('height',$(this).parent().innerWidth()*.75);
        });
$('iframe.snippet-preview').each(function()
        {
                previewportWidth = $(this).parent().parent().innerWidth()-30;
                $(this).zoomer({ width: previewportWidth, height: $(this).parent().innerWidth()*.75, zoom: .5, message : '' ,messageURL : $(this).attr('data-url') })
                $(this).zoomer('refresh');
        });
});
</script>
<script src="//cdnjs.cloudflare.com/ajax/libs/ace/1.2.5/ace.js"></script>
<script type="text/javascript">
(function($) { 
    window.addEventListener('message', receiveMessage, false);
    function receiveMessage(evt)
    {
        if (evt.origin !== 'http://bootsnipp.com') return;
        
        if ((evt.data != undefined) && (evt.data) && (typeof evt.data === "number"))
            if(evt.data >= 500) {
//                $('#playground-container').css('height', evt.data+30+'px');    
            }
    }

    var version = '3.1.0';
    var htmleditor = ace.edit("editor-html");
    var jseditor = ace.edit("editor-js");
    var csseditor = ace.edit("editor-css");

    $('#theme_chooser').change(function(){
        whichCSS = $(this).val();
        document.getElementById('snippet-preview').contentWindow.changeCSS(whichCSS);
    });

    function setEditorOptions(editor, type){
        editor.setTheme("ace/theme/clouds");
        editor.setHighlightActiveLine(false);
//        editor.setReadOnly(true);
        editor.getSession().setMode("ace/mode/"+type);
    };

    setEditorOptions(htmleditor,'html');
    setEditorOptions(jseditor,'javascript');
    setEditorOptions(csseditor,'css');

    function markActive(el)
    {
       $(el).siblings().removeClass('active');
       $(el).addClass('active'); 
    }

    $('#show-html').click(function(e){
        e.preventDefault();
        $('#editor-html').show().siblings().hide();
        markActive(this);
        $('#preview-container').hide();
        htmleditor.resize();
    });

    $('#show-js').click(function(e){
        e.preventDefault();
        $('#editor-js').show().siblings().hide();
        $('#preview-container').hide();
        markActive(this);
        jseditor.resize();
    });

    $('#show-css').click(function(e){
        e.preventDefault();
        $('#editor-css').show().siblings().hide();
        $('#preview-container').hide();
        markActive(this);
        csseditor.resize();
    });

    $('#show-preview').click(function(e){
        e.preventDefault();
 
        markActive(this);
 
        $('.playground-editor').hide();
 
        var html = buildSource(htmleditor, jseditor, csseditor);
        var iframe = document.createElement('iframe');
        
        iframe.src = 'about:blank';
        iframe.frameBorder="0";
        iframe.height = 496;
        iframe.className = 'preview-iframe';
        
        $('.preview-iframe').remove();
        $('div#preview-container').append(iframe);
        
        iframe.contentWindow.document.open('text/html', 'replace');
        iframe.contentWindow.document.write(html);
        iframe.contentWindow.document.close();
 
        $('#preview-container').show();
    });

    var cssurls = {};
    var jsurls = {};

        cssurls['4.0.0'] = '//cdn.rawgit.com/twbs/bootstrap/v4-dev/dist/css/bootstrap.css';
    jsurls['4.0.0'] = '//cdn.rawgit.com/twbs/bootstrap/v4-dev/dist/js/bootstrap.js';
        cssurls['3.3.0'] = '//maxcdn.bootstrapcdn.com/bootstrap/3.3.0/css/bootstrap.min.css';
    jsurls['3.3.0'] = '//maxcdn.bootstrapcdn.com/bootstrap/3.3.0/js/bootstrap.min.js';
        cssurls['3.2.0'] = '//netdna.bootstrapcdn.com/bootstrap/3.2.0/css/bootstrap.min.css';
    jsurls['3.2.0'] = '//netdna.bootstrapcdn.com/bootstrap/3.2.0/js/bootstrap.min.js';
        cssurls['3.1.0'] = '//netdna.bootstrapcdn.com/bootstrap/3.1.0/css/bootstrap.min.css';
    jsurls['3.1.0'] = '//netdna.bootstrapcdn.com/bootstrap/3.1.0/js/bootstrap.min.js';
        cssurls['3.0.3'] = '//netdna.bootstrapcdn.com/bootstrap/3.0.3/css/bootstrap.min.css';
    jsurls['3.0.3'] = '//netdna.bootstrapcdn.com/bootstrap/3.0.3/js/bootstrap.min.js';
        cssurls['3.0.1'] = '//netdna.bootstrapcdn.com/bootstrap/3.0.1/css/bootstrap.min.css';
    jsurls['3.0.1'] = '//netdna.bootstrapcdn.com/bootstrap/3.0.1/js/bootstrap.min.js';
        cssurls['3.0.0'] = '//netdna.bootstrapcdn.com/bootstrap/3.0.0/css/bootstrap.min.css';
    jsurls['3.0.0'] = '//netdna.bootstrapcdn.com/bootstrap/3.0.0/js/bootstrap.min.js';
        cssurls['2.3.2'] = '//netdna.bootstrapcdn.com/twitter-bootstrap/2.3.2/css/bootstrap-combined.min.css';
    jsurls['2.3.2'] = '//netdna.bootstrapcdn.com/twitter-bootstrap/2.3.2/js/bootstrap.min.js';
        

    function buildSource(htmleditor, jseditor, csseditor)
    {   
        var code = {};
        code.html = htmleditor.getValue();
        code.css = csseditor.getValue();
        code.js = jseditor.getValue();
        code.bootstrapcss = cssurls[version];
        code.bootstrapjs = jsurls[version];

        var template = "<!doctype html>\n\
                        <html>\n\
                            <head>\n\
                                <meta charset='utf-8'>\n\
                                <meta name='viewport' content='width=device-width, initial-scale=1'>\n\
                                <title>Snippet - Bootsnipp.com</title>\n\
                                <link href='|bootstrapcss|' rel='stylesheet'>\n\
                                <style>|css|\x3C/style>\n\
                                \x3Cscript type='text/javascript' src='//code.jquery.com/jquery-1.10.2.min.js'>\x3C/script>\n\
                                \x3Cscript type='text/javascript' src='|bootstrapjs|'>\x3C/script>\n\
                                \x3Cscript type='text/javascript'>|js|\x3C/script>\n\
                            </head>\n\
                            <body>\n\
                            |html|\n\
                            </body>\n\
                        </html>";

        content = template.replace(/\|(\w+)\|/g, function(match, str)
        {
            if(str in code) return code[str];
            return '';
        });   
                        
        return content;
    }

    
})(jQuery);
</script>
  </body>
</html>
