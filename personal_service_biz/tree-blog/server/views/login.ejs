<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <link rel="stylesheet" href="//netdna.bootstrapcdn.com/bootstrap/3.3.2/css/bootstrap.min.css">
</head>

<body>
<div class="container">
  <div class="panel panel-info" style="max-width: 300px">
    <div class="panel-heading">
      <div class="panel-title">Sign In</div>
      <div style="float:right; font-size: 80%; position: relative; top:-10px"><a href="#">Forgot password?</a></div>
    </div>     
  
    <div style="padding-top:30px" class="panel-body" >
      <div style="display:none" id="login-alert" class="alert alert-danger col-sm-12"></div>
      <form id="loginform" class="form-horizontal" role="form">
        <div style="margin-bottom: 25px" class="input-group">
          <span class="input-group-addon"><i class="glyphicon glyphicon-user"></i></span>
          <input id="login-username" type="text" class="form-control" name="username" value="" placeholder="username">
        </div>
        <div style="margin-bottom: 25px" class="input-group">
          <span class="input-group-addon"><i class="glyphicon glyphicon-lock"></i></span>
          <input id="login-password" type="password" class="form-control" name="password" placeholder="password">
        </div>
        <div style="margin-top:10px" class="form-group">
          <!-- Button -->
          <div class="col-sm-12 controls">
            <a id="btn-login" class="btn btn-success">Login</a>
          </div>
        </div>
      </form>     
    </div>                     
  </div>  
</div>
</body>

<script src="/jquery/jquery-3.1.1.min.js"></script>
<script type="text/javascript">
  $("#btn-login").click(function () {
    $.ajax({
      type: "POST",
      url: "/login",
      data: {
        user: $("#login-username").val(),
        password: $("#login-password").val()
      }
    })
    .done(function (result) {
      if (result.code == 0) {
        window.location.replace('/admin');
      } else {
        alert(JSON.stringify(result.msg));
      }
    })
    .fail(function () {
      alert('fail');
    });
  });
</script>
</html>
