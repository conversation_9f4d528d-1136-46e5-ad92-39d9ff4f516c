
var host = "https://luzeshu.com:7777";

function init() {

$(".comment-form").submit(function (e) {
  var commentForm = $(e.target);
  var commentText = $(commentForm).find(".comment-text");
  var commentUser = $(commentForm).find(".comment-user");
  var commentEmail = $(commentForm).find(".comment-email");
  var commentLink = $(commentForm).find(".comment-link");
  var wrapper = commentForm;
  wrapper = wrapper.parent() ? wrapper.parent() : wrapper;
  wrapper = wrapper.parent() ? wrapper.parent() : wrapper;
  var commentId = wrapper.find("span.unvisible");
  commentId = commentId ? commentId.text() : undefined;

  var data = {
    blogId: $("#blog-id").text(),
    comment: commentText.val(),
    user: commentUser.val(),
    email: commentEmail.val(),
    link: commentLink.val()
  };
  if (commentId) {
    data.commentId = commentId;
  }
  
  if (!data.comment || !data.user) {
    alert('评论和姓名为必填项');
    return false;
  }
  $.ajax({
    type: 'POST',
    url: host + '/comment',
    data: data
  })
  .done(function (result) {
    if (result.code == 0) {
      alert('评论成功');
    } else {
      alert(result.msg);
    }
  })
  .fail(function () {
    alert('faile');
  });
  return false;
});

$(".reply-cancel").click(function (e) {
  var replyBox = $(e.target).parent().parent();
  var wrapper = replyBox.parent();
  replyBox.remove();
  var button = $(wrapper).find("button.reply-button");
  button.css("display", "block");
  return false;
});

}
init();

var commentBox = `
  <div class='reply-box'>
  <form method="POST" class="comment-form">
    <div class="comment-input form-group">
      <label><span class="info-must">*</span>发表评论：</label>
      <textarea type="text" class="form-control comment-text"></textarea>
    </div>
    <div class="form-group">
      <label><span class="info-must">*</span>您的姓名</label>
      <input type="text" class="form-control comment-username">
    </div>
    <div class="form-group">
      <label>电子邮箱：</label>
      <input type="text" class="form-control comment-email">
    </div>
    <div class="form-group">
      <label>个人网址：</label>
      <input type="text" class="form-control comment-link">
    </div>
    <button class="btn btn-default">提交</button>
    <button class="btn btn-default reply-cancel" style="margin-right: 10px">取消</button>
    <div class="clr"></div>
  </form>
  </div>
`;

$(".reply-button").click(function (e) {
  var wrapper = $(e.target).parent();
  var commentId = $(wrapper).find("span.unvisible").text();
  var button = $(wrapper).find("button.reply-button");
  button.before(commentBox);
  init();
  button.css("display", "none");

  console.log(wrapper.find(".comment-user a"));
  var username = wrapper.find(".comment-user a")[0].innerText;
  var commentText = wrapper.find(".comment-text");
  commentText.val('回复 ' + username + ' : \n');
  //var buttonPost = $(wrapper).find("button.reply-post");
  //buttonPost.css("display", "block");
  //var buttonCancel = $(wrapper).find("button.reply-cancel");
  //buttonCancel.css("display", "block");
});

//$(".reply-post").click(function (e) {
//  var wrapper = $(e.target).parent();
//  var commentId = $(wrapper).find("span.unvisible").text();
//  var button = $(wrapper).find("button.reply-button");
//  var buttonPost = $(wrapper).find("button.reply-post");
//  var buttonCancel = $(wrapper).find("button.reply-cancel");
//
//  console.log($(wrapper).find("textarea.reply-text").val());
//  var data = {
//    commentId: commentId,
//    content: $(wrapper).find("textarea.reply-text").val()
//  };
//  if (!data.content) {
//    alert('回复内容不允许为空');
//    return ;
//  }
//  $.ajax({
//    type: 'POST',
//    url: host + '/reply',
//    data: data
//  })
//  .done(function (result) {
//    if (result.code == 0) {
//      buttonPost.css("display", "none");
//      buttonCancel.css("display", "none");
//      button.css("display", "block");
//      location.reload();
//    } else {
//      alert('fail');
//    }
//  });
//});

