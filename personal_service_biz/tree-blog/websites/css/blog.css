a {
  text-decoration: none
}

@media (min-width: 1588px) {
  .bodybox {
    padding-left: 180px;
  }
}

.bodybox {
  margin: 0;
}

.site-header {
  background-color: #DA9917;
  color: #FFFFFF;
}

.sidebar {
  position: fixed  ;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  height: 100%;
  font-size: 16px;
}

.imagebox {
  width: 25%;
}

@media (max-width: 767px) {
  .imagebox {
    display: none;
  }
}

.imagebox .side-img {
  position: absolute;
  width: 25%;
  height: 100%;
  top: 0;
  bottom: 0;
}

.pagebox {
  height: 100%;
  width: 60%;
  margin-left: 25%
}

@media (min-width: 1588px) {
  .pagebox {
    padding-left: 45px;
  }
}

@media (max-width: 767px) {
  .pagebox {
    margin-left: 0;
    width: 100%;
  }
}

.pagebox .pagenavbar {
  padding-left: 20px;
  border-bottom: 2px solid #d9d9d9;
  font-size: 12px;
  list-style: none;
}

.pagenavbar>ul {
  list-style: none;
  display: table;
  text-align: center;
  line-height: 20px;
  margin: 0;
  padding: 0;
}

.pagenavbar>ul .selected {
  margin-bottom: -2px;
  border-bottom: 2px solid #555555;
}

.pagenavbar>ul .selected a {
  color: #555555;
}

.pagenavbar li {
  float: left;
}

.pagenavbar>ul a {
  padding: 15px;
  color: #999999;
  display: inline-block;
}

.blog-container {

}

.blog-list {
  margin-top: 35px;
  padding-left: 35px;
}

.blog-list, ul {
  list-style: none;
}

.blog-list>li {
  border-bottom: 1px dashed #d9d9d9;
  margin: 0 0 17px;
  padding-bottom: 17px;
}

.blog-list .hasimg .cover-img {
  width: 100px;
  height: 100px;
  float: right;
}

.blog-list .hasimg>div {
  padding-right: 110px;
}

.blog-list .blog-header {
  font-size: 12px;
  margin: 8px 0;
}

.blog-list .blog-title {
  margin-bottom: 10px;
  font-size: 18px;
  font-weight: bold;
  line-height: 1.5;
}

.blog-list .blog-footer {
  font-size: 12px;
  font-family: "lucida grande", "lucida sans unicode";
}
