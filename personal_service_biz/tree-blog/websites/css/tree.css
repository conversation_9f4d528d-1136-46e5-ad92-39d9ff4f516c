* {
  -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
      box-sizing: border-box;
}

.grid-wrap {

}

.grid-container {
  margin: auto;
  overflow: hidden;
  max-width: 1300px;
  height: auto;
}

.grid-left, .grid-middle, .grid-right {
  float: left;
  display: block;
}

@media (min-width: 979px) {
  .grid-right {
    width: 20%;
  }
  .grid-middle {
    width: 60%;
  }
  .grid-left {
    width: 20%;
  }
}

@media (min-width: 767px) and (max-width: 979px) {
  .grid-right {
    width: 0;
  }
  .grid-middle {
    width: 65%;
  }
  .grid-left {
    width: 35%;
  }
}

@media (max-width: 767px) {
  .grid-right {
    width: 0;
  }
  .grid-middle {
    width: 100%;
  }
  .grid-left {
    display: none;
    width: 0;
  }
}

.grid-left {
}

.img-head {
  width: 200px;
}

.grid-right {
  text-align:center;
  overflow:hidden;
}

.img-head {
  font-family: "Cursive", "Comic Sans", "adelle-n4", "adelle", serif;
}

.date-box {
  float: left;
}

.date-box {
  //position: absolute;
  position: relative;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  left: -100px;
  border: 3px solid #01DF74;
  background: #ffffff;
  //background: url(../pictures/icon7.png) 59px no-repeat;
}

.date {
  position: relative;
  top: 10px;
  left: 5px
}

.year {
  //position: absolute;
  left: -100px;
  width: 200px;
  height: 100px;
  background: url(../pictures/icon9.png) 56px no-repeat;
}

.empty-box {
  position: relative;
  height: 100px;
  margin-top: 20px;
}

.blog-box {
  position: relative;
  padding: 0 0 0 70px;
}

.blog-abstract {
  word-wrap: break-word;
}

.blog-title {
  border: 1px ;
}

.verticalline {
  margin-left: 35px;
  border-left: solid #01DF74 2px;
}
