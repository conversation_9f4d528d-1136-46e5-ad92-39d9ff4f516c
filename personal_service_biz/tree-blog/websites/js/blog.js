function init(){$(".comment-form").submit(function(t){var e,n=$(t.target),a=$(n).find(".comment-text"),l=$(n).find(".comment-username"),o=$(n).find(".comment-email"),m=$(n).find(".comment-link"),r=$(n).find(".comment-headpic");n.parent().hasClass("reply-box")&&(e=n.parent().parent().find(".comment-id-msg").text());var i=o.val(),c=m.val(),s=r.val(),p={blogId:$("#blog-id").text(),comment:"<p>"+a.val()+"</p>",user:l.val()};return i&&(p.email=i),c&&(p.link=c),s&&(p.headpicurl=s),e&&(p.commentId=e),p.comment&&p.user?($.ajax({type:"POST",url:"/comment",data:p}).done(function(t){0==t.code?(alert("评论成功"),location.reload()):alert(t.msg)}).fail(function(){alert("faile")}),!1):(alert("评论和姓名为必填项"),!1)}),$(".reply-cancel").click(function(t){var e=$(t.target).parent().parent(),n=e.parent();e.remove();var a=$(n).find("button.reply-button");return a.css("display","block"),!1})}init();var commentBox='  <div class="reply-box">  <form method="POST" class="comment-form">    <div class="comment-input form-group">      <label><span class="info-must">*</span>发表评论：</label>      <textarea type="text" class="form-control comment-text"></textarea>    </div>    <div class="form-group">      <label><span class="info-must">*</span>您的姓名</label>      <input type="text" class="form-control comment-username">    </div>    <div class="form-group">      <label>头像链接(非必填)：</label>      <input type="text" class="form-control comment-headpic">    </div>    <div class="form-group">      <label>电子邮箱(非必填，仅博主可见)：</label>      <input type="text" class="form-control comment-email">    </div>    <div class="form-group">      <label>个人网址(非必填，作为您的跳转链接)：</label>      <input type="text" class="form-control comment-link">    </div>    <button class="btn btn-default">提交</button>    <button class="btn btn-default reply-cancel" style="margin-right: 10px">取消</button>    <div class="clr"></div>  </form>  </div>';$(".reply-button").click(function(t){var e=$(t.target).parent(),n=($(e).find("span.unvisible").text(),$(e).find("button.reply-button"));n.before(commentBox),init(),n.css("display","none");e.find(".comment-user a")[0].innerText,e.find(".comment-text")});