FROM debian:bullseye
COPY sources.list /etc/apt/sources.list

RUN cat /etc/resolv.conf && apt-get update && apt-get install -y socat python3 wget

ENV SOCAT_LISTEN_PORT=8080
ENV SOCAT_TARGET_HOST=host.docker.internal
ENV SOCAT_TARGET_PORT=29999

ENV SOCAT_LISTEN_PORT2=29998
ENV SOCAT_TARGET_HOST2=host.docker.internal
ENV SOCAT_TARGET_PORT2=29998

CMD ["sh", "-c", "socat TCP-LISTEN:${SOCAT_LISTEN_PORT},fork TCP:${SOCAT_TARGET_HOST}:${SOCAT_TARGET_PORT} & socat TCP-LISTEN:${SOCAT_LISTEN_PORT2},fork TCP:${SOCAT_TARGET_HOST2}:${SOCAT_TARGET_PORT2} & wait"]

# FROM python:3.10
# CMD [ "python3", "-m", "http.server", "8080" ]
