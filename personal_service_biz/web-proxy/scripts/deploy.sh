#!/bin/bash
cur_dir=$(dirname $(readlink -f $0))
root_dir=$(dirname ${cur_dir})
if [[ ! -f ${root_dir}/.version ]]; then
  echo "Wrong Root Directory"
  exit
fi

# 先拉取最新代码（避免每次.version冲突）
# git pull

container_name=web-proxy
version_file=${root_dir}/.version
image_version=$(UpdateVersionPatch ${version_file})

image_name=web-proxy:${image_version}
docker build --no-cache -t ${image_name} -f ${root_dir}/image/Dockerfile ${root_dir}/image/
docker image ls -a | grep web-proxy

# path=/data/docker-volumes/${container_name}
# if [[ -d ${path} ]]; then
#   echo "Already Exist Path: ${path}"
# else
#   mkdir -p ${path}
# fi

host_ip=$(ip addr show docker0 | grep 'inet ' | awk '{print $2}' | cut -d'/' -f1)

# Set socat parameters based on TREETHEME_INSTALL_MODE
if [[ "${TREETHEME_INSTALL_MODE}" == "local_server" ]]; then
    SOCAT_LISTEN_PORT=19999
    SOCAT_TARGET_HOST=nginx-admin
    SOCAT_TARGET_PORT=80
    PORT_MAPPING="-p 19999:19999"

    SOCAT_LISTEN_PORT2=19998
    SOCAT_TARGET_HOST2=nginx-admin
    SOCAT_TARGET_PORT2=443
    PORT_MAPPING2="-p 19998:19998"
elif [[ "${TREETHEME_INSTALL_MODE}" == "cloud_server" ]]; then
    SOCAT_LISTEN_PORT=29999
    SOCAT_TARGET_HOST=host.docker.internal
    SOCAT_TARGET_PORT=29999
    PORT_MAPPING="-p 80:29999"

    SOCAT_LISTEN_PORT2=29998
    SOCAT_TARGET_HOST2=host.docker.internal
    SOCAT_TARGET_PORT2=29998
    PORT_MAPPING2="-p 443:29998"
else
    # Use default values from Dockerfile
    exit
fi

docker container rm -f ${container_name}
docker run -d --net admin-net \
    --add-host=host.docker.internal:${host_ip} \
    -e ENV='deploy' \
    -e SOCAT_LISTEN_PORT=${SOCAT_LISTEN_PORT} \
    -e SOCAT_TARGET_HOST=${SOCAT_TARGET_HOST} \
    -e SOCAT_TARGET_PORT=${SOCAT_TARGET_PORT} \
    -e SOCAT_LISTEN_PORT2=${SOCAT_LISTEN_PORT2} \
    -e SOCAT_TARGET_HOST2=${SOCAT_TARGET_HOST2} \
    -e SOCAT_TARGET_PORT2=${SOCAT_TARGET_PORT2} \
    -e TREETHEME_INSTALL_MODE="${TREETHEME_INSTALL_MODE}" \
    -e LANG="C.UTF-8" \
    -e LANGUAGE="C.UTF-8" \
    -e LC_ALL="C.UTF-8" \
    ${PORT_MAPPING} \
    ${PORT_MAPPING2} \
    --name=${container_name} \
    ${image_name}
docker container ls -a

# deploy.sh使用root执行，git操作可能存在报错：fatal: detected dubious ownership in repository at
# git add .version
# git commit -m "update version"
# git push
