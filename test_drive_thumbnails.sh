#!/bin/bash

# 云盘缩略图和预览功能完整测试脚本
# 包括后端部署、前端构建和功能验证

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "此脚本需要root权限运行"
        log_info "请使用: sudo $0"
        exit 1
    fi
}

# 检查项目目录
check_project_dir() {
    if [[ ! -d "/data/projects/personal" ]]; then
        log_error "项目目录不存在: /data/projects/personal"
        exit 1
    fi
    cd /data/projects/personal
    log_success "项目目录检查通过"
}

# 1. 重新生成Thrift代码
generate_thrift() {
    log_info "重新生成Thrift代码..."
    cd /data/projects/personal/personal_common/thrift-common
    
    if make gen; then
        log_success "Thrift代码生成成功"
    else
        log_error "Thrift代码生成失败"
        exit 1
    fi
}

# 2. 构建drive-service
build_drive_service() {
    log_info "构建drive-service..."
    cd /data/projects/personal/personal_service_biz/drive-service
    
    # 清理之前的构建
    rm -f drive-service
    
    # 更新依赖
    if go mod tidy; then
        log_success "Go依赖更新成功"
    else
        log_warning "Go依赖更新失败，继续构建..."
    fi
    
    # 构建服务
    if make build; then
        log_success "drive-service构建成功"
    else
        log_error "drive-service构建失败"
        exit 1
    fi
}

# 3. 部署drive-service
deploy_drive_service() {
    log_info "部署drive-service..."
    cd /data/projects/personal/personal_service_biz/drive-service
    
    if make deploy; then
        log_success "drive-service部署成功"
    else
        log_error "drive-service部署失败"
        exit 1
    fi
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 5
}

# 4. 部署gateway
deploy_gateway() {
    log_info "部署gateway..."
    cd /data/projects/personal/personal_service_biz/gateway
    
    if make build && make deploy; then
        log_success "gateway部署成功"
    else
        log_error "gateway部署失败"
        exit 1
    fi
    
    # 等待服务启动
    log_info "等待gateway启动..."
    sleep 5
}

# 5. 检查服务状态
check_services() {
    log_info "检查服务状态..."
    
    # 检查Docker容器
    if docker ps | grep -q "drive-service"; then
        log_success "drive-service容器运行正常"
    else
        log_warning "drive-service容器未找到"
    fi
    
    if docker ps | grep -q "gateway"; then
        log_success "gateway容器运行正常"
    else
        log_warning "gateway容器未找到"
    fi
    
    # 检查服务端口
    if netstat -tlnp | grep -q ":8080"; then
        log_success "API服务端口8080正常监听"
    else
        log_warning "API服务端口8080未监听"
    fi
}

# 6. 运行后端API测试
test_backend_api() {
    log_info "运行后端API测试..."
    cd /data/projects/personal/personal_clients/thinking-react-native
    
    # 检查Node.js环境
    if ! command -v node &> /dev/null; then
        log_error "Node.js未安装"
        exit 1
    fi
    
    # 安装测试依赖
    if ! npm list axios &> /dev/null; then
        log_info "安装axios依赖..."
        npm install axios
    fi
    
    # 运行集成测试
    if node integration_test.js; then
        log_success "后端API测试通过"
    else
        log_error "后端API测试失败"
        return 1
    fi
}

# 7. 构建React Native应用
build_react_native() {
    log_info "构建React Native应用..."
    cd /data/projects/personal/personal_clients/thinking-react-native
    
    # 检查依赖
    if [[ ! -d "node_modules" ]]; then
        log_info "安装npm依赖..."
        npm install
    fi
    
    # 检查新添加的依赖
    local missing_deps=()
    
    if ! npm list react-native-image-viewing &> /dev/null; then
        missing_deps+=("react-native-image-viewing")
    fi
    
    if ! npm list react-native-video &> /dev/null; then
        missing_deps+=("react-native-video")
    fi
    
    if ! npm list react-native-fast-image &> /dev/null; then
        missing_deps+=("react-native-fast-image")
    fi
    
    if [[ ${#missing_deps[@]} -gt 0 ]]; then
        log_info "安装缺失的依赖: ${missing_deps[*]}"
        npm install "${missing_deps[@]}" --legacy-peer-deps
    fi
    
    # 检查TypeScript编译
    log_info "检查TypeScript编译..."
    if npx tsc --noEmit --skipLibCheck; then
        log_success "TypeScript编译检查通过"
    else
        log_warning "TypeScript编译有警告，但继续执行"
    fi
}

# 8. 启动React Native开发服务器
start_metro() {
    log_info "启动Metro开发服务器..."
    cd /data/projects/personal/personal_clients/thinking-react-native
    
    # 杀掉可能存在的Metro进程
    pkill -f "react-native\|metro" || true
    
    # 启动Metro服务器
    log_info "在后台启动Metro服务器..."
    nohup npm start > metro.log 2>&1 &
    METRO_PID=$!
    
    # 等待Metro启动
    log_info "等待Metro服务器启动..."
    sleep 10
    
    # 检查Metro是否启动成功
    if ps -p $METRO_PID > /dev/null; then
        log_success "Metro服务器启动成功 (PID: $METRO_PID)"
        echo $METRO_PID > metro.pid
    else
        log_error "Metro服务器启动失败"
        return 1
    fi
}

# 9. 检查Android设备连接
check_android_device() {
    log_info "检查Android设备连接..."
    
    if ! command -v adb &> /dev/null; then
        log_error "ADB未安装或不在PATH中"
        return 1
    fi
    
    local devices=$(adb devices | grep -v "List of devices" | grep "device$" | wc -l)
    
    if [[ $devices -eq 0 ]]; then
        log_warning "没有检测到Android设备"
        log_info "请确保:"
        log_info "1. Android设备已连接并开启USB调试"
        log_info "2. 或者使用无线调试: adb connect <device_ip>:5555"
        return 1
    else
        log_success "检测到 $devices 个Android设备"
        adb devices
    fi
}

# 10. 生成测试报告
generate_test_report() {
    log_info "生成测试报告..."
    
    local report_file="/data/projects/personal/drive_thumbnail_test_report.md"
    
    cat > "$report_file" << EOF
# 云盘缩略图和预览功能测试报告

## 测试时间
$(date '+%Y-%m-%d %H:%M:%S')

## 测试环境
- 操作系统: $(uname -a)
- Docker版本: $(docker --version)
- Node.js版本: $(node --version)
- Go版本: $(go version)

## 功能特性
### ✅ 已实现功能
1. **后端缩略图生成服务**
   - 支持图片缩略图生成 (JPEG, PNG, GIF, BMP, WebP)
   - 支持视频缩略图生成 (MP4, AVI, MOV, MKV, WebM, FLV)
   - 异步生成，不影响文件上传性能
   - 缩略图缓存机制

2. **前端预览组件**
   - ImageViewer: 图片查看器，支持缩放、滑动
   - VideoPlayer: 视频播放器，支持播放控制
   - 缩略图网格显示
   - 文件类型自动识别

3. **API接口**
   - GET /api/drive/files/{id}/thumbnail - 获取缩略图
   - POST /api/drive/files/{id}/thumbnail - 生成缩略图
   - 支持自定义缩略图尺寸

4. **用户体验优化**
   - 文件列表中显示缩略图
   - 点击图片/视频直接预览
   - 手势操作支持
   - 加载状态和错误处理

## 测试结果
EOF

    if [[ -f "/data/projects/personal/personal_clients/thinking-react-native/metro.log" ]]; then
        echo "### Metro服务器日志" >> "$report_file"
        echo '```' >> "$report_file"
        tail -20 "/data/projects/personal/personal_clients/thinking-react-native/metro.log" >> "$report_file"
        echo '```' >> "$report_file"
    fi

    log_success "测试报告已生成: $report_file"
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    
    # 停止Metro服务器
    if [[ -f "/data/projects/personal/personal_clients/thinking-react-native/metro.pid" ]]; then
        local metro_pid=$(cat "/data/projects/personal/personal_clients/thinking-react-native/metro.pid")
        if ps -p $metro_pid > /dev/null; then
            kill $metro_pid
            log_info "Metro服务器已停止"
        fi
        rm -f "/data/projects/personal/personal_clients/thinking-react-native/metro.pid"
    fi
}

# 主函数
main() {
    log_info "开始云盘缩略图和预览功能完整测试"
    log_info "========================================"
    
    # 设置清理陷阱
    trap cleanup EXIT
    
    # 执行测试步骤
    check_root
    check_project_dir
    
    log_info "步骤1: 生成Thrift代码"
    generate_thrift
    
    log_info "步骤2: 构建drive-service"
    build_drive_service
    
    log_info "步骤3: 部署drive-service"
    deploy_drive_service
    
    log_info "步骤4: 部署gateway"
    deploy_gateway
    
    log_info "步骤5: 检查服务状态"
    check_services
    
    log_info "步骤6: 运行后端API测试"
    if test_backend_api; then
        log_success "后端测试通过"
    else
        log_warning "后端测试失败，但继续前端测试"
    fi
    
    log_info "步骤7: 构建React Native应用"
    build_react_native
    
    log_info "步骤8: 启动Metro开发服务器"
    if start_metro; then
        log_success "Metro服务器启动成功"
    else
        log_error "Metro服务器启动失败"
        exit 1
    fi
    
    log_info "步骤9: 检查Android设备"
    if check_android_device; then
        log_success "Android设备检查通过"
        log_info "现在可以运行: npx react-native run-android"
    else
        log_warning "Android设备检查失败，请手动连接设备"
    fi
    
    log_info "步骤10: 生成测试报告"
    generate_test_report
    
    log_success "========================================"
    log_success "云盘缩略图和预览功能测试完成！"
    log_info ""
    log_info "下一步操作:"
    log_info "1. 连接Android设备或启动模拟器"
    log_info "2. 运行: npx react-native run-android"
    log_info "3. 在应用中测试云盘功能"
    log_info "4. 查看测试报告: /data/projects/personal/drive_thumbnail_test_report.md"
    log_info ""
    log_info "Metro服务器正在后台运行，日志文件: metro.log"
    log_info "要停止Metro服务器，请运行此脚本的清理函数或手动杀掉进程"
}

# 运行主函数
main "$@"
