#!/bin/bash

# AI文档完整性验证脚本
# 用于检查AI Agent上下文文档是否完整且格式正确

echo "🔍 验证AI Agent上下文文档..."

# 检查文档是否存在
echo "📋 检查文档文件..."
if [[ -f "README-for-AI.md" ]]; then
    echo "✅ README-for-AI.md 存在"
else
    echo "❌ README-for-AI.md 不存在"
    exit 1
fi

if [[ -f "AI-CONTEXT-QUICK-REF.md" ]]; then
    echo "✅ AI-CONTEXT-QUICK-REF.md 存在"
else
    echo "❌ AI-CONTEXT-QUICK-REF.md 不存在"
    exit 1
fi

# 检查关键章节是否存在
echo ""
echo "📖 检查README-for-AI.md关键章节..."
required_sections=(
    "关键约束与常见错误"
    "微服务架构"
    "数据库与存储"
    "客户端开发"
    "常见问题与解决方案"
    "开发规范"
    "调试与故障排除"
)

for section in "${required_sections[@]}"; do
    if grep -q "$section" README-for-AI.md; then
        echo "✅ 包含章节: $section"
    else
        echo "❌ 缺少章节: $section"
    fi
done

# 检查关键配置信息
echo ""
echo "⚙️ 检查关键配置信息..."
key_configs=(
    "sudo -i"
    "https://local.luzeshu.cn"
    "admin-net"
    "docker-volumes"
    "bullet: ************************************"
)

for config in "${key_configs[@]}"; do
    if grep -q "$config" README-for-AI.md; then
        echo "✅ 包含配置: $config"
    else
        echo "❌ 缺少配置: $config"
    fi
done

# 检查服务端口配置
echo ""
echo "🔌 检查服务端口配置..."
services=(
    "gateway:16135"
    "favorites-service:18081"
    "task-service:18082"
    "notes-service:18083"
    "drive-service:18084"
)

for service in "${services[@]}"; do
    service_name=$(echo $service | cut -d: -f1)
    port=$(echo $service | cut -d: -f2)
    if grep -q "$port" README-for-AI.md; then
        echo "✅ 包含服务端口: $service"
    else
        echo "❌ 缺少服务端口: $service"
    fi
done

# 检查快速参考文档
echo ""
echo "🚀 检查AI-CONTEXT-QUICK-REF.md..."
quick_ref_sections=(
    "关键约束"
    "架构速览"
    "常用命令"
    "客户端开发"
    "故障排除"
)

for section in "${quick_ref_sections[@]}"; do
    if grep -q "$section" AI-CONTEXT-QUICK-REF.md; then
        echo "✅ 快速参考包含: $section"
    else
        echo "❌ 快速参考缺少: $section"
    fi
done

# 统计文档行数
echo ""
echo "📊 文档统计信息..."
readme_lines=$(wc -l < README-for-AI.md)
quickref_lines=$(wc -l < AI-CONTEXT-QUICK-REF.md)
echo "📋 README-for-AI.md: $readme_lines 行"
echo "🚀 AI-CONTEXT-QUICK-REF.md: $quickref_lines 行"

# 检查文档链接
echo ""
echo "🔗 检查文档交叉引用..."
if grep -q "README-for-AI.md" AI-CONTEXT-QUICK-REF.md; then
    echo "✅ 快速参考正确链接到完整文档"
else
    echo "❌ 快速参考缺少到完整文档的链接"
fi

if grep -q "AI-CONTEXT-QUICK-REF.md" README.md; then
    echo "✅ 主README正确引用快速参考"
else
    echo "❌ 主README缺少快速参考链接"
fi

echo ""
echo "✨ 文档验证完成！"
echo "💡 使用建议："
echo "   - 新AI Agent: 先读 README-for-AI.md"
echo "   - 日常开发: 用 AI-CONTEXT-QUICK-REF.md"
echo "   - 遇到问题: 查故障排除章节"
