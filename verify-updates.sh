#!/bin/bash

# 验证AI文档更新脚本
echo "🔍 验证AI文档更新..."

echo "📋 检查ADB调试配置..."
if grep -q "ssh -R.*19900.*手机ip.*调试端口" README-for-AI.md && grep -q "ssh -R.*19900.*手机ip.*调试端口" AI-CONTEXT-QUICK-REF.md; then
    echo "✅ ADB SSH隧道配置已添加"
else
    echo "❌ ADB SSH隧道配置缺失"
fi

if grep -q "adb connect 127.0.0.1:19900" README-for-AI.md && grep -q "adb connect 127.0.0.1:19900" AI-CONTEXT-QUICK-REF.md; then
    echo "✅ ADB连接命令已添加"
else
    echo "❌ ADB连接命令缺失"
fi

echo ""
echo "🌐 检查网络代理配置..."
if grep -q "oproxy_ks" README-for-AI.md && grep -q "oproxy_ks" AI-CONTEXT-QUICK-REF.md; then
    echo "✅ oproxy_ks代理配置已添加"
else
    echo "❌ oproxy_ks代理配置缺失"
fi

if grep -q "sudo -i.*oproxy_ks" README-for-AI.md && grep -q "sudo -i.*oproxy_ks" AI-CONTEXT-QUICK-REF.md; then
    echo "✅ 代理配置命令已添加"
else
    echo "❌ 代理配置命令缺失"
fi

echo ""
echo "📊 文档统计..."
readme_lines=$(wc -l < README-for-AI.md)
quickref_lines=$(wc -l < AI-CONTEXT-QUICK-REF.md)
echo "📋 README-for-AI.md: $readme_lines 行"
echo "🚀 AI-CONTEXT-QUICK-REF.md: $quickref_lines 行"

echo ""
echo "✨ 更新验证完成！"
echo "🎯 新增功能："
echo "   - ADB跨网络调试配置"
echo "   - 网络代理环境配置"
echo "   - 完善的故障排除步骤"
